/**
 * Debug script to identify the LangGraph ReactAgent tool binding issue
 * Specifically targeting the "Cannot set properties of undefined (setting 'name')" error
 */

// Import only what we need to avoid TS issues
import { speakingTools } from './src/agent/tools/avatar/speaking.js';

async function debugToolIssue() {
    console.log('🔍 Starting LangGraph ReactAgent tool debugging...');

    try {
        // Step 1: Check speaking tools directly
        console.log('Step 1: Checking speaking tools...');
        
        const tools = speakingTools;

        // Step 2: Simple tool debugging without external dependencies
        console.log('Step 2: Running simple tool debugging...');
        const debugReport = {
            totalTools: Array.isArray(tools) ? tools.length : 0,
            validTools: [],
            undefinedTools: [],
            nullTools: [],
            invalidTools: [],
            missingProperties: []
        };
        
        if (!Array.isArray(tools)) {
            console.log('❌ Tools is not an array:', typeof tools);
            return;
        }
        
        for (let i = 0; i < tools.length; i++) {
            const tool = tools[i];
            
            if (tool === undefined) {
                debugReport.undefinedTools.push({ index: i, issue: 'Tool is undefined' });
            } else if (tool === null) {
                debugReport.nullTools.push({ index: i, issue: 'Tool is null' });
            } else if (typeof tool !== 'object') {
                debugReport.invalidTools.push({ index: i, issue: `Tool is not an object (type: ${typeof tool})` });
            } else {
                const missingProps = [];
                if (!tool.name) missingProps.push('name');
                if (!tool.description) missingProps.push('description');
                if (!tool.schema) missingProps.push('schema');
                if (!tool.invoke && !tool.func) missingProps.push('invoke/func');
                
                if (missingProps.length > 0) {
                    debugReport.missingProperties.push({
                        index: i,
                        toolName: tool.name || `tool_${i}`,
                        missingProperties: missingProps
                    });
                } else {
                    debugReport.validTools.push({
                        index: i,
                        name: tool.name,
                        hasDescription: !!tool.description,
                        hasSchema: !!tool.schema,
                        hasInvoke: !!(tool.invoke || tool.func)
                    });
                }
            }
        }
        
        console.log('\n=== TOOL DEBUG REPORT ===');
        console.log('Total tools:', debugReport.totalTools);
        console.log('Valid tools:', debugReport.validTools.length);
        console.log('Undefined tools:', debugReport.undefinedTools.length);
        console.log('Null tools:', debugReport.nullTools.length);
        console.log('Invalid tools:', debugReport.invalidTools.length);
        console.log('Missing properties:', debugReport.missingProperties.length);
        
        if (debugReport.undefinedTools.length > 0) {
            console.log('\n🚨 UNDEFINED TOOLS (CRITICAL):');
            debugReport.undefinedTools.forEach(ut => {
                console.log(`  - Index ${ut.index}: ${ut.issue}`);
            });
        }
        
        if (debugReport.invalidTools.length > 0) {
            console.log('\n⚠️ INVALID TOOLS:');
            debugReport.invalidTools.forEach(it => {
                console.log(`  - Index ${it.index} (${it.toolName || 'unnamed'}): ${it.issues?.join(', ') || it.issue}`);
            });
        }
        
        if (debugReport.missingProperties.length > 0) {
            console.log('\n⚠️ TOOLS WITH MISSING PROPERTIES:');
            debugReport.missingProperties.forEach(mp => {
                console.log(`  - Index ${mp.index} (${mp.toolName}): Missing ${mp.missingProperties.join(', ')}`);
            });
        }

        console.log('\n📋 RECOMMENDATIONS:');
        debugReport.recommendations.forEach(rec => {
            console.log(`  ${rec}`);
        });

        // Step 3: Manual validation check
        console.log('Step 3: Manual validation...');
        const validatedTools = [];
        
        for (const tool of tools) {
            if (tool && 
                typeof tool === 'object' && 
                tool.name && 
                typeof tool.name === 'string' &&
                tool.description &&
                tool.schema &&
                (tool.invoke || tool.func) &&
                typeof (tool.invoke || tool.func) === 'function') {
                validatedTools.push(tool);
            }
        }
        
        console.log('\n=== MANUAL VALIDATION RESULT ===');
        console.log(`Original tools: ${tools.length}`);
        console.log(`Validated tools: ${validatedTools.length}`);
        console.log(`Filtered out: ${tools.length - validatedTools.length}`);

        // Step 4: Inspect individual tools
        console.log('\n=== INDIVIDUAL TOOL INSPECTION ===');
        for (let i = 0; i < Math.min(tools.length, 10); i++) {
            const tool = tools[i];
            console.log(`\nTool ${i}:`, {
                type: typeof tool,
                isNull: tool === null,
                isUndefined: tool === undefined,
                name: tool?.name,
                hasDescription: !!tool?.description,
                hasSchema: !!tool?.schema,
                hasInvoke: !!tool?.invoke,
                invokeType: typeof tool?.invoke,
                keys: tool ? Object.keys(tool) : 'N/A'
            });
        }

    } catch (error) {
        console.log('\n❌ DEBUG SCRIPT ERROR:');
        console.log(error.message);
        console.log(error.stack);
    }
}

debugToolIssue().catch(console.error);