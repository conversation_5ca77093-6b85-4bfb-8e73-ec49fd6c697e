name: 🏗️ Build Architecture Documentation

on:
  push:
    branches: [ main, langchain ]
    paths:
      - 'docs/arch/**'
      - 'src/**'
      - 'app/**'
      - '.github/workflows/architecture-docs.yml'
  pull_request:
    branches: [ main, langchain ]
    paths:
      - 'docs/arch/**'

  # Allow manual triggering
  workflow_dispatch:

jobs:
  build-architecture:
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          
      - name: 🔧 Install dependencies
        run: |
          cd docs/arch
          npm install
          npm run install-cli
          
      - name: 🎨 Build architecture diagrams
        run: |
          cd docs/arch
          ./build-architecture.sh
          
      - name: 📊 Generate architecture report
        run: |
          cd docs/arch
          echo "# 🏗️ Architecture Build Report" > ../../architecture-report.md
          echo "" >> ../../architecture-report.md
          echo "## 📈 Generated Diagrams" >> ../../architecture-report.md
          echo "" >> ../../architecture-report.md
          echo "### 🖼️ Images Generated:" >> ../../architecture-report.md
          ls -la ./images/ >> ../../architecture-report.md
          echo "" >> ../../architecture-report.md
          echo "### 📁 Interactive Files Generated:" >> ../../architecture-report.md
          ls -la ./generated/ >> ../../architecture-report.md
          
      - name: 📋 Upload architecture report
        uses: actions/upload-artifact@v3
        with:
          name: architecture-report
          path: architecture-report.md
          
      - name: 🗂️ Upload generated diagrams
        uses: actions/upload-artifact@v3
        with:
          name: architecture-diagrams
          path: docs/arch/
          
      - name: 🚀 Deploy to GitHub Pages (if main branch)
        if: github.ref == 'refs/heads/main'
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./docs/arch/generated
          destination_dir: architecture
          
      - name: 💬 Comment PR with architecture preview
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            
            // Read architecture report
            const report = fs.readFileSync('architecture-report.md', 'utf8');
            
            // Create comment body
            const body = `
            ## 🏗️ Architecture Documentation Updated
            
            Your changes have triggered an architecture documentation rebuild!
            
            ${report}
            
            📥 **Download the generated diagrams from the artifacts above** to review the updates.
            
            ### 🎯 Quick Actions:
            - View interactive diagrams: [Architecture Preview](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})
            - Download images: Check the "architecture-diagrams" artifact
            - Review accuracy: Compare with your code changes
            
            _This comment was automatically generated by the Architecture Documentation workflow._
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: body
            });