/**
 * Input Coordination Manager Tests
 * Tests for the event-driven multimodal input coordination system
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
    InputCoordinationManager,
    InputEventType,
    normalizeInput,
    validateMultimodalInput,
    processMultimodal,
    MULTIMODAL_LIMITS
} from '@/media/modality/index.js';

describe('Input Coordination Manager', () => {
    let inputManager;

    beforeEach(() => {
        inputManager = new InputCoordinationManager({
            bufferSize: 5,
            qualitySettings: { audioQuality: 0.8 }
        });
    });

    afterEach(() => {
        if (inputManager) {
            inputManager.dispose();
        }
    });

    describe('InputCoordinationManager', () => {
        test('should initialize correctly', () => {
            expect(inputManager).toBeInstanceOf(InputCoordinationManager);
            expect(inputManager.getBufferStatus()).toBeDefined();
        });

        test('should manage event listeners', () => {
            const mockListener = jest.fn();

            inputManager.addEventListener(InputEventType.AUDIO_DATA, mockListener);

            // Verify listener was added
            expect(() => {
                inputManager.removeEventListener(InputEventType.AUDIO_DATA, mockListener);
            }).not.toThrow();
        });

        test('should start and stop coordination', () => {
            expect(() => inputManager.start()).not.toThrow();
            expect(() => inputManager.stop()).not.toThrow();
        });

        test('should process audio input', async () => {
            const mockListener = jest.fn();
            inputManager.addEventListener(InputEventType.AUDIO_DATA, mockListener);

            inputManager.start();

            const audioData = new Float32Array([0.1, 0.2, 0.3]);
            const format = { sampleRate: 44100, channels: 2, bitDepth: 16 };

            await inputManager.processAudioInput(audioData, format);

            expect(mockListener).toHaveBeenCalledWith(expect.objectContaining({
                type: InputEventType.AUDIO_DATA,
                data: audioData,
                format
            }));
        });

        test('should process video input', async () => {
            const mockListener = jest.fn();
            inputManager.addEventListener(InputEventType.VIDEO_FRAME, mockListener);

            inputManager.start();

            const frameData = 'data:image/jpeg;base64,test';
            const format = { width: 640, height: 480, format: 'jpeg', quality: 0.8 };

            await inputManager.processVideoInput(frameData, format);

            expect(mockListener).toHaveBeenCalledWith(expect.objectContaining({
                type: InputEventType.VIDEO_FRAME,
                data: frameData,
                format
            }));
        });

        test('should process text input', async () => {
            const mockListener = jest.fn();
            inputManager.addEventListener(InputEventType.TEXT_INPUT, mockListener);

            inputManager.start();

            await inputManager.processTextInput('Hello world', 'en');

            expect(mockListener).toHaveBeenCalledWith(expect.objectContaining({
                type: InputEventType.TEXT_INPUT,
                text: 'Hello world',
                language: 'en'
            }));
        });

        test('should create multimodal input from buffered data', async () => {
            inputManager.start();

            // Add some test data
            await inputManager.processAudioInput(new Float32Array([0.1, 0.2]), {
                sampleRate: 44100, channels: 1, bitDepth: 16
            });
            await inputManager.processVideoInput('frame1', {
                width: 320, height: 240, format: 'jpeg', quality: 0.8
            });
            await inputManager.processTextInput('test text');

            const multimodalInput = await inputManager.createMultimodalInput();

            expect(multimodalInput).toEqual(expect.objectContaining({
                type: InputEventType.MULTIMODAL_INPUT,
                audio: expect.any(Float32Array),
                video: expect.any(Array),
                text: expect.any(String)
            }));
        });

        test('should handle buffer size limits', async () => {
            inputManager.start();

            // Add more items than buffer size
            for (let i = 0; i < 10; i++) {
                await inputManager.processAudioInput(new Float32Array([i]), {
                    sampleRate: 44100, channels: 1, bitDepth: 16
                });
            }

            const status = inputManager.getBufferStatus();
            expect(status[InputEventType.AUDIO_DATA]).toBeLessThanOrEqual(5); // Buffer size limit
        });

        test('should update quality settings', () => {
            const newSettings = { videoQuality: 0.9, audioSampleRate: 48000 };

            expect(() => inputManager.updateQualitySettings(newSettings)).not.toThrow();
        });

        test('should combine audio chunks correctly', async () => {
            inputManager.start();

            const chunk1 = new Float32Array([0.1, 0.2]);
            const chunk2 = new Float32Array([0.3, 0.4]);

            await inputManager.processAudioInput(chunk1, {
                sampleRate: 44100, channels: 1, bitDepth: 16
            });
            await inputManager.processAudioInput(chunk2, {
                sampleRate: 44100, channels: 1, bitDepth: 16
            });

            const multimodalInput = await inputManager.createMultimodalInput();

            expect(multimodalInput.audio.length).toBe(4); // Combined length
        });
    });

    describe('Legacy Function Support', () => {
        test('normalizeInput should process different input types', () => {
            // Test string input
            const textResult = normalizeInput('Hello world');
            expect(textResult.text).toBe('Hello world');
            expect(textResult.audio).toBeNull();

            // Test Float32Array input
            const audioData = new Float32Array([0.1, 0.2]);
            const audioResult = normalizeInput(audioData);
            expect(audioResult.audio).toBe(audioData);
            expect(audioResult.text).toBeNull();
        });

        test('validateMultimodalInput should validate correctly', () => {
            const validInput = {
                text: 'Hello',
                audio: new Float32Array([0.1, 0.2]),
                video: ['frame1', 'frame2']
            };

            const result = validateMultimodalInput(validInput);

            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });

        test('processMultimodal should process normalized input', () => {
            const normalizedInput = {
                text: 'test',
                audio: new Float32Array([0.1, 0.2]),
                video: ['frame1'],
                metadata: { timestamp: Date.now() }
            };

            const result = processMultimodal(normalizedInput);

            expect(result).toEqual(expect.objectContaining({
                text: 'test',
                audioData: expect.any(Float32Array),
                videoFrames: expect.any(Array),
                metadata: expect.any(Object)
            }));
        });

        test('MULTIMODAL_LIMITS should be defined', () => {
            expect(MULTIMODAL_LIMITS).toEqual(expect.objectContaining({
                MAX_VIDEO_FRAME_SIZE_KB: expect.any(Number),
                RECOMMENDED_FRAME_RATE: expect.any(Number),
                AUDIO_FORMAT: expect.any(Object),
                VIDEO_FORMAT: expect.any(Object)
            }));
        });
    });

    describe('Event System', () => {
        test('should handle event listener errors gracefully', async () => {
            const errorListener = jest.fn(() => {
                throw new Error('Test error');
            });

            inputManager.addEventListener(InputEventType.AUDIO_DATA, errorListener);
            inputManager.start();

            // Should not throw even if listener throws
            await expect(inputManager.processAudioInput(new Float32Array([0.1]), {
                sampleRate: 44100, channels: 1, bitDepth: 16
            })).resolves.not.toThrow();
        });

        test('should emit events in correct order', async () => {
            const events = [];
            const listener = (event) => events.push(event.type);

            inputManager.addEventListener(InputEventType.INPUT_STARTED, listener);
            inputManager.addEventListener(InputEventType.AUDIO_DATA, listener);
            inputManager.addEventListener(InputEventType.INPUT_STOPPED, listener);

            inputManager.start();
            await inputManager.processAudioInput(new Float32Array([0.1]), {
                sampleRate: 44100, channels: 1, bitDepth: 16
            });
            inputManager.stop();

            expect(events).toEqual([
                InputEventType.INPUT_STARTED,
                InputEventType.AUDIO_DATA,
                InputEventType.INPUT_STOPPED
            ]);
        });
    });

    describe('Performance Tests', () => {
        test('should handle high-frequency input processing', async () => {
            inputManager.start();

            const startTime = Date.now();

            // Process 100 audio chunks rapidly
            const promises = [];
            for (let i = 0; i < 100; i++) {
                promises.push(inputManager.processAudioInput(new Float32Array([i / 100]), {
                    sampleRate: 44100, channels: 1, bitDepth: 16
                }));
            }

            await Promise.all(promises);

            const duration = Date.now() - startTime;
            expect(duration).toBeLessThan(1000); // Should handle 100 inputs within 1 second
        });
    });
});