/**
 * Audio Analysis Module Tests
 * Tests for the refactored audio processing functionality
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  extractAudioAnalysis,
  handleAudioData,
  createAudioProcessingConfig,
  DEFAULT_AUDIO_PROCESSING_OPTIONS
} from '@/media/modality/audio';

describe('AudioAnalysis', () => {
  describe('extractAudioAnalysis', () => {
    it('should analyze PCM16 audio data correctly', () => {
      // Create mock PCM16 data with some audio signal
      const audioBuffer = new ArrayBuffer(1024 * 2); // 1024 samples, 2 bytes each
      const samples = new Int16Array(audioBuffer);

      // Add some audio signal
      for (let i = 0; i < samples.length; i++) {
        samples[i] = Math.sin(i * 0.1) * 5000; // Sine wave with amplitude
      }

      const result = extractAudioAnalysis(audioBuffer, 'pcm16');

      expect(result).toMatchObject({
        volume: expect.any(Number),
        hasSignificantAudio: expect.any(Boolean),
        quality: expect.stringMatching(/good|silent|error/),
        sentiment: expect.stringMatching(/neutral|positive|negative/),
        vadState: expect.stringMatching(/speaking|silent/),
        timestamp: expect.any(Number)
      });

      expect(result.volume).toBeGreaterThan(0);
      expect(result.hasSignificantAudio).toBe(true);
      expect(result.quality).toBe('good');
    });

    it('should analyze Float32 audio data correctly', () => {
      // Create mock Float32 data with some audio signal
      const audioData = new Float32Array(1024);

      // Add some audio signal
      for (let i = 0; i < audioData.length; i++) {
        audioData[i] = Math.sin(i * 0.1) * 0.5; // Sine wave
      }

      const result = extractAudioAnalysis(audioData, 'float32');

      expect(result.volume).toBeGreaterThan(0);
      expect(result.hasSignificantAudio).toBe(true);
      expect(result.quality).toBe('good');
      expect(result.vadState).toBe('speaking');
    });

    it('should detect silent audio correctly', () => {
      // Create silent audio data
      const audioBuffer = new ArrayBuffer(1024 * 2);
      const samples = new Int16Array(audioBuffer);
      // samples are already initialized to 0

      const result = extractAudioAnalysis(audioBuffer, 'pcm16');

      expect(result.volume).toBe(0);
      expect(result.hasSignificantAudio).toBe(false);
      expect(result.quality).toBe('silent');
      expect(result.vadState).toBe('silent');
    });

    it('should handle unsupported formats gracefully', () => {
      const result = extractAudioAnalysis('invalid data', 'unsupported');

      expect(result.quality).toBe('error');
      expect(result.hasSignificantAudio).toBe(false);
      expect(result.volume).toBe(0);
    });

    it('should use custom options', () => {
      const audioBuffer = new ArrayBuffer(1024 * 2);
      const samples = new Int16Array(audioBuffer);

      // Add minimal audio signal
      for (let i = 0; i < 10; i++) {
        samples[i] = 500; // Small signal
      }

      const customOptions = {
        volumeThreshold: 0.001,
        significantAudioThreshold: 0.001,
        maxSampleThreshold: 100
      };

      const result = extractAudioAnalysis(audioBuffer, 'pcm16', customOptions);

      expect(result.hasSignificantAudio).toBe(true);
    });
  });

  describe('handleAudioData', () => {
    let mockWebSocketSender;
    let mockContextUpdater;
    let mockLogger;

    beforeEach(() => {
      mockWebSocketSender = vi.fn().mockResolvedValue(true);
      mockContextUpdater = vi.fn();
      mockLogger = {
        debug: vi.fn(),
        error: vi.fn(),
        info: vi.fn()
      };
    });

    it('should handle audio data and call all handlers', async () => {
      const audioBuffer = new ArrayBuffer(1024 * 2);
      const samples = new Int16Array(audioBuffer);

      // Add significant audio signal
      for (let i = 0; i < samples.length; i++) {
        samples[i] = Math.sin(i * 0.1) * 10000;
      }

      const result = await handleAudioData(audioBuffer, 'pcm16', {
        sendToWebSocket: mockWebSocketSender,
        updateContext: mockContextUpdater,
        logger: mockLogger
      });

      expect(mockWebSocketSender).toHaveBeenCalledWith(audioBuffer, 'pcm16');
      expect(mockContextUpdater).toHaveBeenCalledWith('audio', expect.objectContaining({
        hasAudio: true,
        audioFormat: 'pcm16',
        volume: expect.any(Number)
      }));
      expect(result.hasSignificantAudio).toBe(true);
    });

    it('should not update context for silent audio', async () => {
      const audioBuffer = new ArrayBuffer(1024 * 2);
      // Silent audio (all zeros)

      await handleAudioData(audioBuffer, 'pcm16', {
        sendToWebSocket: mockWebSocketSender,
        updateContext: mockContextUpdater,
        logger: mockLogger
      });

      expect(mockWebSocketSender).toHaveBeenCalled();
      expect(mockContextUpdater).not.toHaveBeenCalled(); // Should not be called for silent audio
    });

    it('should handle errors gracefully', async () => {
      const mockFailingWebSocket = vi.fn().mockRejectedValue(new Error('WebSocket error'));

      const result = await handleAudioData(new ArrayBuffer(100), 'pcm16', {
        sendToWebSocket: mockFailingWebSocket,
        updateContext: mockContextUpdater,
        logger: mockLogger
      });

      expect(result).toBeNull();
      expect(mockLogger.debug).toHaveBeenCalled();
    });
  });

  describe('createAudioProcessingConfig', () => {
    it('should create proper configuration object', () => {
      const mockWebSocketSender = vi.fn();
      const mockContextUpdater = vi.fn();
      const mockAudioHandler = vi.fn();

      const config = createAudioProcessingConfig({
        webSocketSender: mockWebSocketSender,
        contextUpdater: mockContextUpdater,
        audioHandler: mockAudioHandler
      });

      expect(config).toHaveProperty('onAudioData');
      expect(typeof config.onAudioData).toBe('function');
    });

    it('should handle audio data through configuration', async () => {
      const mockWebSocketSender = vi.fn().mockResolvedValue(true);
      const mockContextUpdater = vi.fn();
      const mockAudioHandler = vi.fn();

      const config = createAudioProcessingConfig({
        webSocketSender: mockWebSocketSender,
        contextUpdater: mockContextUpdater,
        audioHandler: mockAudioHandler
      });

      const audioBuffer = new ArrayBuffer(1024 * 2);
      const samples = new Int16Array(audioBuffer);

      // Add audio signal
      for (let i = 0; i < samples.length; i++) {
        samples[i] = Math.sin(i * 0.1) * 5000;
      }

      const result = await config.onAudioData(audioBuffer, 'pcm16');

      expect(mockWebSocketSender).toHaveBeenCalled();
      expect(mockAudioHandler).toHaveBeenCalled();
      expect(result.hasSignificantAudio).toBe(true);
    });
  });

  describe('DEFAULT_AUDIO_PROCESSING_OPTIONS', () => {
    it('should have expected default values', () => {
      expect(DEFAULT_AUDIO_PROCESSING_OPTIONS).toEqual({
        volumeThreshold: 0.01,
        significantAudioThreshold: 0.01,
        maxSampleThreshold: 1000,
        enableSentimentAnalysis: true
      });
    });
  });
});