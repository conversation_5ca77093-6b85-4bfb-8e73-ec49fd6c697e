/**
 * Video Processing Module Tests
 * Tests for the modernized video processing system including VideoProcessor
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
    VideoProcessor,
    VideoFormat,
    detectVideoFormat,
    validateVideoInput,
    DEFAULT_VIDEO_CONFIG
} from '@/media/modality/video.js';

// Mock WebGL and MediaInfo for testing
global.window = {
    document: {
        createElement: vi.fn(() => ({
            getContext: vi.fn(() => null),
            width: 0,
            height: 0,
            toDataURL: vi.fn(() => 'data:image/jpeg;base64,test')
        }))
    }
};

global.MediaInfo = vi.fn(() => Promise.resolve({
    analyzeData: vi.fn(() => Promise.resolve({ format: 'MP4' }))
}));

describe('Video Processing Module', () => {
    let videoProcessor;

    beforeEach(() => {
        videoProcessor = new VideoProcessor();
    });

    afterEach(() => {
        if (videoProcessor) {
            videoProcessor.dispose();
        }
    });

    describe('VideoProcessor', () => {
        test('should initialize correctly', () => {
            expect(videoProcessor).toBeInstanceOf(VideoProcessor);
        });

        test('should handle WebGL initialization gracefully', () => {
            // Should not throw even if WebGL is not available
            expect(() => new VideoProcessor()).not.toThrow();
        });

        test('should process video frame with fallback to Canvas 2D', async () => {
            const mockVideo = {
                videoWidth: 640,
                videoHeight: 480,
                width: 640,
                height: 480
            };

            const result = await videoProcessor.processFrame(mockVideo);

            // Should handle gracefully even with mocked canvas
            expect(typeof result).toBe('string');
        });

        test('should extract metadata from file', async () => {
            const mockFile = new File(['mock video data'], 'test.mp4', { type: 'video/mp4' });

            const result = await videoProcessor.extractMetadata(mockFile);

            expect(result).toEqual(expect.objectContaining({
                success: expect.any(Boolean)
            }));
        });

        test('should process multiple frames', async () => {
            const mockVideo = {
                videoWidth: 320,
                videoHeight: 240,
                width: 320,
                height: 240
            };

            const result = await videoProcessor.processFrames(mockVideo, 3, {
                interval: 10 // Fast interval for testing
            });

            expect(result).toEqual(expect.objectContaining({
                success: expect.any(Boolean),
                metadata: expect.objectContaining({
                    totalFrames: 3,
                    processingTimeMs: expect.any(Number)
                })
            }));
        });

        test('should handle processing errors gracefully', async () => {
            const result = await videoProcessor.processFrame(null);

            expect(result).toBeNull();
        });
    });

    describe('Video Format Detection', () => {
        test('should detect MP4 format from file extension', () => {
            const format = detectVideoFormat('video.mp4');
            expect(format).toBe(VideoFormat.MP4);
        });

        test('should detect WEBM format from MIME type', () => {
            const mockFile = { type: 'video/webm', name: 'video.webm' };
            const format = detectVideoFormat(mockFile);
            expect(format).toBe(VideoFormat.WEBM);
        });

        test('should detect MOV format', () => {
            const format = detectVideoFormat('movie.mov');
            expect(format).toBe(VideoFormat.MOV);
        });

        test('should detect MKV format', () => {
            const format = detectVideoFormat('video.mkv');
            expect(format).toBe(VideoFormat.MKV);
        });

        test('should return UNKNOWN for unsupported formats', () => {
            const format = detectVideoFormat('video.xyz');
            expect(format).toBe(VideoFormat.UNKNOWN);
        });

        test('should handle enhanced format detection', () => {
            const mockFile = { type: 'video/quicktime', name: 'movie.qt' };
            const format = detectVideoFormat(mockFile);
            expect(format).toBe(VideoFormat.MOV);
        });
    });

    describe('Video Input Validation', () => {
        test('should validate File input', () => {
            const mockFile = new File(['data'], 'video.mp4', { type: 'video/mp4' });
            const result = validateVideoInput(mockFile);

            expect(result.isValid).toBe(true);
            expect(result.format).toBe(VideoFormat.MP4);
            expect(result.metadata.size).toBeDefined();
        });

        test('should validate string URL input', () => {
            const result = validateVideoInput('https://example.com/video.mp4');

            expect(result.isValid).toBe(true);
            expect(result.format).toBe(VideoFormat.MP4);
        });

        test('should reject null input', () => {
            const result = validateVideoInput(null);

            expect(result.isValid).toBe(false);
            expect(result.error).toContain('null or undefined');
        });

        test('should reject unsupported input types', () => {
            const result = validateVideoInput(123);

            expect(result.isValid).toBe(false);
            expect(result.error).toContain('Unsupported');
        });

        test('should reject empty string input', () => {
            const result = validateVideoInput('');

            expect(result.isValid).toBe(false);
            expect(result.error).toContain('Empty');
        });
    });

    describe('Video Configuration', () => {
        test('should use default video config', () => {
            expect(DEFAULT_VIDEO_CONFIG).toEqual(expect.objectContaining({
                maxFrames: expect.any(Number),
                frameRate: expect.any(Number),
                quality: expect.any(Number),
                maxFileSize: expect.any(Number),
                format: expect.any(String)
            }));
        });

        test('should apply custom video config', async () => {
            const customConfig = {
                quality: 0.9,
                format: 'png',
                maxFrames: 10
            };

            const mockVideo = { videoWidth: 640, videoHeight: 480 };
            const result = await videoProcessor.processFrame(mockVideo, customConfig);

            // Should handle custom config without errors
            expect(typeof result).toBe('string');
        });
    });

    describe('Video Effects Processing', () => {
        test('should handle effects processing', async () => {
            const mockVideo = { videoWidth: 640, videoHeight: 480 };
            const effects = ['blur', 'contrast', 'brightness'];

            const result = await videoProcessor.processFrame(mockVideo, { effects });

            // Should process without errors even with mocked WebGL
            expect(typeof result).toBe('string');
        });
    });

    describe('Performance Tests', () => {
        test('should process frames efficiently', async () => {
            const mockVideo = { videoWidth: 320, videoHeight: 240 };

            const startTime = Date.now();
            await videoProcessor.processFrame(mockVideo);
            const duration = Date.now() - startTime;

            expect(duration).toBeLessThan(100); // Should be fast with mocked canvas
        });

        test('should handle multiple concurrent frame processing', async () => {
            const mockVideo = { videoWidth: 320, videoHeight: 240 };

            const promises = Array(5).fill().map(() =>
                videoProcessor.processFrame(mockVideo)
            );

            const results = await Promise.all(promises);

            expect(results).toHaveLength(5);
            results.forEach(result => {
                expect(typeof result).toBe('string');
            });
        });
    });

    describe('Memory Management', () => {
        test('should dispose resources properly', () => {
            const processor = new VideoProcessor();

            expect(() => processor.dispose()).not.toThrow();
        });
    });
});