/**
 * Audio API Tests
 * Tests for src/media/api/audioAPI.ts
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock the audio processing functions
vi.mock('../../../src/media/modality/audio.js', () => ({
  convertFloat32ToWav: vi.fn(() => new ArrayBuffer(1024)),
  base64ToBlob: vi.fn(() => new Blob(['test'], { type: 'audio/wav' }))
}));

// Mock the logger
vi.mock('../../../src/utils/logger.js', () => ({
  createLogger: vi.fn(() => ({
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn()
  }))
}));

describe('Audio API Module', () => {
  let audioAPI;

  beforeEach(async () => {
    // Clear all mocks
    vi.clearAllMocks();

    // Dynamically import the module to ensure fresh state
    audioAPI = await import('../../../src/media/api/audioAPI.ts');
  });

  afterEach(() => {
    vi.resetModules();
  });

  describe('Module Structure', () => {
    it('should export expected functions', () => {
      expect(typeof audioAPI.sendAudioToAPI).toBe('function');
      expect(typeof audioAPI.getAudioAPIStatus).toBe('function');
    });

    it('should have proper imports', () => {
      // Test that imports work without throwing
      expect(() => {
        const { convertFloat32ToWav, base64ToBlob } = require('../../../src/media/modality/audio.js');
      }).not.toThrow();
    });
  });

  describe('sendAudioToAPI', () => {
    it('should handle Float32Array input', async () => {
      const mockAudioData = new Float32Array([0.1, 0.2, 0.3]);
      const options = {
        endpoint: 'http://test-api.com/audio',
        apiKey: 'test-key',
        format: 'wav'
      };

      // Mock fetch
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true, transcription: 'test result' })
      });

      const result = await audioAPI.sendAudioToAPI(mockAudioData, options);

      expect(result).toBeDefined();
      expect(global.fetch).toHaveBeenCalledWith(
        options.endpoint,
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Authorization': `Bearer ${options.apiKey}`,
            'Content-Type': 'application/json'
          })
        })
      );
    });

    it('should handle API errors gracefully', async () => {
      const mockAudioData = new Float32Array([0.1, 0.2]);
      const options = {
        endpoint: 'http://test-api.com/audio',
        apiKey: 'test-key'
      };

      // Mock fetch to reject
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));

      await expect(audioAPI.sendAudioToAPI(mockAudioData, options))
        .rejects.toThrow('Network error');
    });

    it('should handle HTTP error responses', async () => {
      const mockAudioData = new Float32Array([0.1, 0.2]);
      const options = {
        endpoint: 'http://test-api.com/audio',
        apiKey: 'test-key'
      };

      // Mock fetch to return error response
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        text: () => Promise.resolve('Invalid audio format')
      });

      await expect(audioAPI.sendAudioToAPI(mockAudioData, options))
        .rejects.toThrow();
    });
  });

  describe('getAudioAPIStatus', () => {
    it('should check API availability', async () => {
      const endpoint = 'http://test-api.com/status';

      // Mock successful status check
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ status: 'healthy', version: '1.0.0' })
      });

      const status = await audioAPI.getAudioAPIStatus(endpoint);

      expect(status).toBeDefined();
      expect(status.status).toBe('healthy');
      expect(global.fetch).toHaveBeenCalledWith(endpoint);
    });

    it('should handle unavailable API', async () => {
      const endpoint = 'http://test-api.com/status';

      // Mock network error
      global.fetch = vi.fn().mockRejectedValue(new Error('Connection refused'));

      const status = await audioAPI.getAudioAPIStatus(endpoint);

      expect(status.status).toBe('error');
      expect(status.error).toContain('Connection refused');
    });
  });

  describe('Integration with Media Layer', () => {
    it('should properly convert audio formats', async () => {
      const { convertFloat32ToWav } = await import('../../../src/media/modality/audio.js');

      const mockAudioData = new Float32Array([0.1, -0.2, 0.3]);

      // Call the function to ensure it works
      expect(() => convertFloat32ToWav(mockAudioData)).not.toThrow();
      expect(convertFloat32ToWav).toHaveBeenCalledWith(mockAudioData);
    });

    it('should handle base64 conversion', async () => {
      const { base64ToBlob } = await import('../../../src/media/modality/audio.js');

      const base64Data = 'dGVzdCBhdWRpbyBkYXRh'; // "test audio data" in base64

      expect(() => base64ToBlob(base64Data, 'audio/wav')).not.toThrow();
      expect(base64ToBlob).toHaveBeenCalledWith(base64Data, 'audio/wav');
    });
  });

  describe('Error Handling', () => {
    it('should validate input parameters', async () => {
      // Test with invalid audio data
      await expect(audioAPI.sendAudioToAPI(null, { endpoint: 'test' }))
        .rejects.toThrow();

      await expect(audioAPI.sendAudioToAPI(new Float32Array([]), {}))
        .rejects.toThrow();
    });

    it('should handle missing configuration', async () => {
      const mockAudioData = new Float32Array([0.1, 0.2]);

      // Test with missing endpoint
      await expect(audioAPI.sendAudioToAPI(mockAudioData, {}))
        .rejects.toThrow();
    });
  });
});
