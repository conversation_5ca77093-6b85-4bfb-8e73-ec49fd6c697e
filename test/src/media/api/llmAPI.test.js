/**
 * LLM API Tests
 * Tests for src/media/api/llmAPI.ts
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock dependencies
vi.mock('../../../src/utils/logger.js', () => ({
  createLogger: vi.fn(() => ({
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn()
  }))
}));

vi.mock('../../../src/utils/portManager.js', () => ({
  getDownloadServerUrl: vi.fn(() => 'http://localhost:3001')
}));

vi.mock('../../../src/agent/models/aliyun/AliyunConfig.js', () => ({
  ALIYUN_AUDIO_CONFIG: {
    format: 'pcm',
    sample_rate: 16000,
    sample_width: 16
  }
}));

describe('LLM API Module', () => {
  let llmAPI;

  beforeEach(async () => {
    vi.clearAllMocks();

    // Mock fetch globally
    global.fetch = vi.fn();

    llmAPI = await import('../../../src/media/api/llmAPI.ts');
  });

  afterEach(() => {
    vi.resetModules();
    vi.restoreAllMocks();
  });

  describe('Module Structure', () => {
    it('should export llmAPI object', () => {
      expect(llmAPI.llmAPI).toBeDefined();
      expect(typeof llmAPI.llmAPI).toBe('object');
    });

    it('should have required methods', () => {
      expect(typeof llmAPI.llmAPI.sendMessage).toBe('function');
      expect(typeof llmAPI.llmAPI.sendMultimodalMessage).toBe('function');
      expect(typeof llmAPI.llmAPI.streamMessage).toBe('function');
    });
  });

  describe('sendMessage', () => {
    it('should send text messages to LLM API', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: { content: 'Test response' }
          }]
        })
      };

      global.fetch.mockResolvedValue(mockResponse);

      const result = await llmAPI.llmAPI.sendMessage([
        { role: 'user', content: 'Hello' }
      ]);

      expect(global.fetch).toHaveBeenCalledWith(
        'http://localhost:3001/llm',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: expect.stringContaining('Hello')
        })
      );

      expect(result).toBeDefined();
    });

    it('should handle API errors', async () => {
      global.fetch.mockRejectedValue(new Error('Network error'));

      await expect(llmAPI.llmAPI.sendMessage([
        { role: 'user', content: 'Hello' }
      ])).rejects.toThrow('Network error');
    });

    it('should handle HTTP errors', async () => {
      const mockResponse = {
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      };

      global.fetch.mockResolvedValue(mockResponse);

      await expect(llmAPI.llmAPI.sendMessage([
        { role: 'user', content: 'Hello' }
      ])).rejects.toThrow();
    });
  });

  describe('sendMultimodalMessage', () => {
    it('should send multimodal messages with audio', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: { content: 'Processed audio message' }
          }]
        })
      };

      global.fetch.mockResolvedValue(mockResponse);

      const audioData = new Float32Array([0.1, 0.2, 0.3]);
      const result = await llmAPI.llmAPI.sendMultimodalMessage(
        [{ role: 'user', content: 'Process this audio' }],
        { audio: audioData }
      );

      expect(global.fetch).toHaveBeenCalledWith(
        'http://localhost:3001/llm',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        })
      );

      expect(result).toBeDefined();
    });

    it('should send multimodal messages with video', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: { content: 'Processed video message' }
          }]
        })
      };

      global.fetch.mockResolvedValue(mockResponse);

      const videoBlob = new Blob(['video data'], { type: 'video/mp4' });
      const result = await llmAPI.llmAPI.sendMultimodalMessage(
        [{ role: 'user', content: 'Process this video' }],
        { video: videoBlob }
      );

      expect(global.fetch).toHaveBeenCalled();
      expect(result).toBeDefined();
    });

    it('should handle missing media data', async () => {
      await expect(llmAPI.llmAPI.sendMultimodalMessage(
        [{ role: 'user', content: 'Hello' }],
        {}
      )).rejects.toThrow();
    });
  });

  describe('streamMessage', () => {
    it('should handle streaming responses', async () => {
      // Mock ReadableStream
      const mockStream = new ReadableStream({
        start(controller) {
          controller.enqueue(new TextEncoder().encode('data: {"content": "streaming"}\n\n'));
          controller.close();
        }
      });

      const mockResponse = {
        ok: true,
        body: mockStream,
        headers: new Map([['content-type', 'text/event-stream']])
      };

      global.fetch.mockResolvedValue(mockResponse);

      const messages = [{ role: 'user', content: 'Stream this' }];
      const onChunk = vi.fn();

      await llmAPI.llmAPI.streamMessage(messages, onChunk);

      expect(global.fetch).toHaveBeenCalledWith(
        'http://localhost:3001/llm',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream'
          }
        })
      );

      // Note: Testing streaming is complex due to ReadableStream implementation
      // This test verifies the setup but full streaming testing would require more sophisticated mocking
    });

    it('should handle streaming errors', async () => {
      global.fetch.mockRejectedValue(new Error('Streaming error'));

      const onChunk = vi.fn();
      await expect(llmAPI.llmAPI.streamMessage(
        [{ role: 'user', content: 'Stream this' }],
        onChunk
      )).rejects.toThrow('Streaming error');
    });
  });

  describe('Configuration', () => {
    it('should use correct server URL', () => {
      const { getDownloadServerUrl } = require('../../../src/utils/portManager.js');
      expect(getDownloadServerUrl).toHaveBeenCalled();
    });

    it('should have proper audio configuration', () => {
      const { ALIYUN_AUDIO_CONFIG } = require('../../../src/agent/models/aliyun/AliyunConfig.js');
      expect(ALIYUN_AUDIO_CONFIG).toBeDefined();
      expect(ALIYUN_AUDIO_CONFIG.format).toBe('pcm');
      expect(ALIYUN_AUDIO_CONFIG.sample_rate).toBe(16000);
    });
  });

  describe('Security', () => {
    it('should proxy requests through server', () => {
      // Verify that all requests go to localhost:3001/llm
      // This ensures API keys stay server-side
      expect(true).toBe(true); // Placeholder - actual implementation depends on llmAPI structure
    });

    it('should not expose API keys in client code', () => {
      // This test would verify no hardcoded API keys exist
      const moduleString = llmAPI.toString();
      expect(moduleString).not.toMatch(/sk-[a-zA-Z0-9]+/); // Common API key pattern
      expect(moduleString).not.toMatch(/Bearer [a-zA-Z0-9]+/); // Bearer token pattern
    });
  });

  describe('Error Handling', () => {
    it('should validate message format', async () => {
      await expect(llmAPI.llmAPI.sendMessage(null)).rejects.toThrow();
      await expect(llmAPI.llmAPI.sendMessage([])).rejects.toThrow();
      await expect(llmAPI.llmAPI.sendMessage('invalid')).rejects.toThrow();
    });

    it('should handle network timeouts', async () => {
      // Mock a timeout
      global.fetch.mockImplementation(() =>
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout')), 100)
        )
      );

      await expect(llmAPI.llmAPI.sendMessage([
        { role: 'user', content: 'Hello' }
      ])).rejects.toThrow('Timeout');
    });
  });
});
