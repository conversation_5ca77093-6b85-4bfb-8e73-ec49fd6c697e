/**
 * Comprehensive Test Helper Utilities
 * Provides common testing functions, mock creation, and test data generation
 */

import { vi } from 'vitest';
// Mock imports that might not exist in test environment
// import { LangGraphAgentService } from '@/agent/core.js';
// import { StandardizedAgentWorkflow } from '@/agent/workflow/agent.js';

/**
 * Test Agent Factory
 * Creates pre-configured agent instances for testing
 */
export class TestAgentFactory {
    static async createTestAgent(options = {}) {
        const defaultOptions = {
            temperature: 0.7,
            maxTokens: 1024,
            maxIterations: 5,
            enableMemory: true,
            maxRetries: 1,
            enableStructuredOutput: true,
            enableAdvancedStreaming: true,
            services: {
                ttsService: createMockTTSService(),
                audioPlayer: createMockAudioPlayer(),
                animationController: createMockAnimationController(),
                animationRegistry: createMockAnimationRegistry()
            }
        };

        // Return mock agent service for testing without real dependencies
        return {
            ...defaultOptions,
            ...options,
            _initialized: true,
            initialize: vi.fn().mockResolvedValue(true),
            dispose: vi.fn(),
            generateResponse: vi.fn().mockResolvedValue({ success: true, response: 'Mock response' }),
            getModel: vi.fn().mockReturnValue(null),
            updateDualBrainContext: vi.fn(),
            getMemoryManager: vi.fn().mockReturnValue({})
        };
    }

    static async createTestWorkflow(options = {}) {
        const defaultOptions = {
            temperature: 0.7,
            maxTokens: 1024,
            maxIterations: 5,
            enableMemory: true,
            maxRetries: 1,
            services: {
                ttsService: createMockTTSService(),
                audioPlayer: createMockAudioPlayer()
            }
        };

        // Return mock workflow for testing without real dependencies
        return {
            ...defaultOptions,
            ...options,
            _initialized: true,
            initialize: vi.fn().mockResolvedValue(true),
            execute: vi.fn().mockResolvedValue({ success: true }),
            dispose: vi.fn()
        };
    }
}

/**
 * Mock Service Factories
 */

export function createMockTTSService(options = {}) {
    return {
        speak: vi.fn().mockImplementation(async (text, opts = {}) => {
            // Simulate TTS processing delay
            await new Promise(resolve => setTimeout(resolve, options.delay || 10));

            return {
                success: true,
                audioData: new ArrayBuffer(1024),
                duration: text.length * 50, // Mock duration
                text: text
            };
        }),
        isAvailable: vi.fn().mockReturnValue(true),
        getVoices: vi.fn().mockReturnValue(['voice1', 'voice2']),
        setVoice: vi.fn(),
        constructor: { name: 'MockTTSService' }
    };
}

export function createMockAudioPlayer(options = {}) {
    return {
        playChunk: vi.fn().mockImplementation(async (audioData, opts = {}) => {
            await new Promise(resolve => setTimeout(resolve, options.delay || 5));
            return { success: true, played: true };
        }),
        stop: vi.fn(),
        pause: vi.fn(),
        resume: vi.fn(),
        isPlaying: vi.fn().mockReturnValue(false)
    };
}

export function createMockAnimationController(options = {}) {
    return {
        triggerAnimation: vi.fn().mockImplementation(async (animationId) => {
            await new Promise(resolve => setTimeout(resolve, options.delay || 5));
            return {
                success: true,
                animationId,
                triggered: true,
                duration: 2000
            };
        }),
        stopAnimation: vi.fn(),
        getCurrentAnimation: vi.fn().mockReturnValue(null),
        isAnimationPlaying: vi.fn().mockReturnValue(false)
    };
}

export function createMockAnimationRegistry() {
    return {
        getById: vi.fn().mockImplementation((id) => {
            const mockAnimations = {
                'dance_silly': { id: 'dance_silly', name: 'Silly Dance', category: 'dance' },
                'happy_idle': { id: 'happy_idle', name: 'Happy Idle', category: 'emotion' },
                'talking': { id: 'talking', name: 'Talking', category: 'communication' },
                'greeting': { id: 'greeting', name: 'Greeting', category: 'communication' }
            };
            return mockAnimations[id] || null;
        }),
        getAllAnimations: vi.fn().mockReturnValue({
            'dance_silly': { id: 'dance_silly', name: 'Silly Dance', category: 'dance' },
            'happy_idle': { id: 'happy_idle', name: 'Happy Idle', category: 'emotion' },
            'talking': { id: 'talking', name: 'Talking', category: 'communication' },
            'greeting': { id: 'greeting', name: 'Greeting', category: 'communication' }
        }),
        getByCategory: vi.fn().mockImplementation((category) => {
            const categorized = {
                'dance': [{ id: 'dance_silly', name: 'Silly Dance', category: 'dance' }],
                'emotion': [{ id: 'happy_idle', name: 'Happy Idle', category: 'emotion' }],
                'communication': [
                    { id: 'talking', name: 'Talking', category: 'communication' },
                    { id: 'greeting', name: 'Greeting', category: 'communication' }
                ]
            };
            return categorized[category] || [];
        })
    };
}

/**
 * Mock LLM Response Factories
 */

export function createMockLLMResponse(content, options = {}) {
    return {
        choices: [{
            message: {
                role: 'assistant',
                content: content,
                tool_calls: options.toolCalls || []
            },
            finish_reason: options.finishReason || 'stop',
            index: 0
        }],
        usage: {
            prompt_tokens: options.promptTokens || 100,
            completion_tokens: options.completionTokens || content.length,
            total_tokens: (options.promptTokens || 100) + content.length
        },
        model: options.model || 'test-model',
        id: options.id || `test-${Date.now()}`,
        created: Date.now()
    };
}

export function createMockToolCall(toolName, args = {}, id = null) {
    return {
        id: id || `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: 'function',
        function: {
            name: toolName,
            arguments: JSON.stringify(args)
        }
    };
}

export function createMockStreamingResponse(chunks, options = {}) {
    return {
        async *[Symbol.asyncIterator]() {
            for (let i = 0; i < chunks.length; i++) {
                await new Promise(resolve => setTimeout(resolve, options.delay || 10));

                yield {
                    choices: [{
                        delta: {
                            content: chunks[i],
                            role: i === 0 ? 'assistant' : undefined
                        },
                        index: 0,
                        finish_reason: i === chunks.length - 1 ? 'stop' : null
                    }]
                };
            }
        }
    };
}

/**
 * Test Input Factories
 */

export function createTextInput(text, options = {}) {
    return {
        type: 'text',
        content: text,
        timestamp: options.timestamp || Date.now(),
        ...options
    };
}

export function createMultimodalInput(text, audioData = null, options = {}) {
    return {
        text: text,
        audio: audioData || new ArrayBuffer(1024),
        mediaType: options.mediaType || 'audio/wav',
        contextualInformation: options.contextualInformation || '',
        asrMetadata: options.asrMetadata || {
            confidence: 0.95,
            detectedLanguage: 'en',
            detectedEmotion: 'neutral'
        },
        timestamp: options.timestamp || Date.now(),
        ...options
    };
}

export function createConversationHistory(turns = []) {
    return turns.map((turn, index) => ({
        role: index % 2 === 0 ? 'user' : 'assistant',
        content: turn,
        timestamp: Date.now() - (turns.length - index) * 1000
    }));
}

/**
 * Assertion Helpers
 */

export function expectValidAgentResponse(response) {
    expect(response).toBeDefined();
    expect(typeof response).toBe('object');
    expect(response.responseText).toBeDefined();
    expect(typeof response.responseText).toBe('string');
    expect(typeof response.hasTools).toBe('boolean');
    expect(typeof response.hasAnimation).toBe('boolean');
    expect(typeof response.isStructured).toBe('boolean');
}

export function expectValidWorkflowState(state) {
    expect(state).toBeDefined();
    expect(state.messages).toBeDefined();
    expect(Array.isArray(state.messages)).toBe(true);
    expect(state.context).toBeDefined();
    expect(typeof state.context).toBe('object');
    expect(state.metadata).toBeDefined();
    expect(typeof state.metadata).toBe('object');
}

export function expectValidToolResult(result) {
    expect(result).toBeDefined();
    expect(typeof result.success).toBe('boolean');

    if (result.success) {
        expect(result.error).toBeUndefined();
    } else {
        expect(result.error).toBeDefined();
        expect(typeof result.error).toBe('string');
    }
}

export function expectValidAnimationResult(result) {
    expectValidToolResult(result);

    if (result.success) {
        expect(result.animationId).toBeDefined();
        expect(typeof result.animationId).toBe('string');
    }
}

export function expectValidTTSResult(result) {
    expectValidToolResult(result);

    if (result.success) {
        expect(result.text).toBeDefined();
        expect(typeof result.text).toBe('string');
    }
}

/**
 * Performance Testing Helpers
 */

export class PerformanceTracker {
    constructor() {
        this.measurements = new Map();
    }

    start(name) {
        this.measurements.set(name, {
            startTime: performance.now(),
            startMemory: process.memoryUsage()
        });
    }

    end(name) {
        const measurement = this.measurements.get(name);
        if (!measurement) {
            throw new Error(`No measurement started for: ${name}`);
        }

        const endTime = performance.now();
        const endMemory = process.memoryUsage();

        const result = {
            duration: endTime - measurement.startTime,
            memoryDelta: {
                rss: endMemory.rss - measurement.startMemory.rss,
                heapUsed: endMemory.heapUsed - measurement.startMemory.heapUsed,
                heapTotal: endMemory.heapTotal - measurement.startMemory.heapTotal
            }
        };

        this.measurements.delete(name);
        return result;
    }

    async measureAsync(name, asyncFn) {
        this.start(name);
        try {
            const result = await asyncFn();
            const measurement = this.end(name);
            return { result, measurement };
        } catch (error) {
            this.measurements.delete(name);
            throw error;
        }
    }
}

/**
 * Async Testing Utilities
 */

export function waitFor(condition, options = {}) {
    const timeout = options.timeout || 5000;
    const interval = options.interval || 50;
    const startTime = Date.now();

    return new Promise((resolve, reject) => {
        const check = async () => {
            try {
                const result = await condition();
                if (result) {
                    resolve(result);
                    return;
                }
            } catch (error) {
                // Condition check failed, continue waiting
            }

            if (Date.now() - startTime > timeout) {
                reject(new Error(`Condition not met within ${timeout}ms`));
                return;
            }

            setTimeout(check, interval);
        };

        check();
    });
}

export function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Mock API Responses
 */

export function mockFetchVllmApi(responses = {}) {
    return vi.fn().mockImplementation(async (endpoint, options = {}) => {
        const key = `${options.method || 'GET'} ${endpoint}`;
        const response = responses[key] || responses[endpoint] || { ok: true, json: async () => ({}) };

        if (typeof response === 'function') {
            return response(endpoint, options);
        }

        return {
            ok: response.ok !== false,
            status: response.status || 200,
            statusText: response.statusText || 'OK',
            json: async () => response.data || response,
            text: async () => JSON.stringify(response.data || response)
        };
    });
}

/**
 * Test Cleanup Utilities
 */

export function createTestCleanup() {
    const cleanupTasks = [];

    return {
        add: (task) => cleanupTasks.push(task),
        cleanup: async () => {
            for (const task of cleanupTasks.reverse()) {
                try {
                    await task();
                } catch (error) {
                    console.warn('Cleanup task failed:', error);
                }
            }
            cleanupTasks.length = 0;
        }
    };
}

/**
 * Mock Agent Service Factory
 * Creates comprehensive mock agent service for dual brain testing
 */
export function createMockAgentService(options = {}) {
    const defaultOptions = {
        enableDualBrain: true,
        realtimeCapable: true,
        models: {},
        ...options
    };

    const mockService = {
        // Basic service properties
        _initialized: true,
        testMode: true,
        isDualBrainMode: vi.fn().mockReturnValue(defaultOptions.enableDualBrain),
        
        // Model management
        models: new Map(),
        getModel: vi.fn().mockImplementation((modelName) => {
            if (defaultOptions.models[modelName]) {
                return defaultOptions.models[modelName];
            }
            
            // Return default mock model
            return {
                constructor: { name: `Mock${modelName}Model` },
                apiMode: modelName === 'system1' ? 'websocket' : 'http',
                invoke: vi.fn().mockResolvedValue({ 
                    content: JSON.stringify({
                        shouldAct: true,
                        confidence: 0.8,
                        reason: 'mock_decision',
                        urgency: 'medium'
                    })
                }),
                isConnected: vi.fn().mockReturnValue(true),
                isSessionReady: vi.fn().mockReturnValue(true),
                updateSystemPrompt: vi.fn().mockResolvedValue(true),
                setCharacterContext: vi.fn().mockResolvedValue(true)
            };
        }),
        
        // Context management
        updateDualBrainContext: vi.fn(),
        getMemoryContext: vi.fn().mockReturnValue({
            userPreferences: 'test preferences',
            conversationStyle: 'analytical'
        }),
        
        // Memory management
        getMemoryManager: vi.fn().mockReturnValue({
            get: vi.fn(),
            set: vi.fn(),
            clear: vi.fn()
        }),
        
        // Service lifecycle
        initialize: vi.fn().mockResolvedValue(true),
        dispose: vi.fn().mockResolvedValue(true),
        
        // Response generation
        generateResponse: vi.fn().mockResolvedValue({
            success: true,
            response: 'Mock agent response',
            confidence: 0.9
        }),
        
        // Event handling
        on: vi.fn(),
        off: vi.fn(),
        emit: vi.fn(),
        
        // Configuration
        options: defaultOptions
    };
    
    return mockService;
}

// Export factory instances for convenience
export const createTestAgent = TestAgentFactory.createTestAgent;
export const createTestWorkflow = TestAgentFactory.createTestWorkflow; 