/**
 * Services Memory Integration Tests - December 2024
 * 
 * Comprehensive test suite for services integration with LangGraph memory:
 * - CharacterAnalysisService memory integration
 * - AgentCoordinator VAD context management
 * - DualBrainCoordinator memory replacement
 * - End-to-end service interaction with memory
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { LangGraphMemoryManager } from '../../../src/agent/memory/index.js';
import { CharacterAnalysisService } from '../../../app/viewer/services/CharacterAnalysisService.js';
import { DualBrainCoordinator } from '../../../src/agent/arch/dualbrain/DualBrainCoordinator.js';
import { createLogger } from '../../../src/utils/logger.js';

const logger = createLogger('ServicesMemoryIntegrationTest');

describe('Services Memory Integration - December 2024', () => {
  let memoryManager;
  let characterService;
  let mockAgentService;
  let dualBrainCoordinator;

  beforeEach(async () => {
    // Initialize memory manager
    memoryManager = new LangGraphMemoryManager({
      enablePersistence: false,
      enableLogging: false
    });

    // Mock agent service
    mockAgentService = {
      getMemoryManager: vi.fn().mockReturnValue(memoryManager),
      updateDualBrainContext: vi.fn(),
      getDualBrainCoordinator: vi.fn(),
      getModel: vi.fn().mockReturnValue({
        invoke: vi.fn().mockResolvedValue({
          content: JSON.stringify({
            overview: 'Test character analysis',
            personality: 'Test personality',
            speakingStyle: 'Test style'
          })
        })
      })
    };

    // Initialize services
    characterService = new CharacterAnalysisService({
      memoryManager,
      agentService: mockAgentService,
      userId: 'test_user_services',
      dualBrainCoordinator: null
    });

    dualBrainCoordinator = new DualBrainCoordinator(mockAgentService, {
      memoryManager,
      userId: 'test_user_services',
      enableProactiveDecisions: true,
      system2AnalysisInterval: 5000
    });

    mockAgentService.getDualBrainCoordinator.mockReturnValue(dualBrainCoordinator);
  });

  afterEach(async () => {
    // Cleanup
    if (memoryManager) {
      await memoryManager.clearMemories('test_user_services');
    }
    if (dualBrainCoordinator) {
      dualBrainCoordinator.dispose();
    }
  });

  describe('CharacterAnalysisService Memory Integration', () => {
    it('should integrate memory manager from constructor options', () => {
      expect(characterService.memoryManager).toBe(memoryManager);
      expect(characterService.userId).toBe('test_user_services');
    });

    it('should store and retrieve character analysis through memory', async () => {
      const characterData = {
        name: 'Luffy',
        anime: 'One Piece',
        description: 'Rubber pirate captain'
      };

      // First analysis - should store in memory
      const analysis1 = await characterService.analyzeCharacter(characterData);
      expect(analysis1).toBeDefined();

      // Verify stored in memory
      const memoryResults = await memoryManager.searchMemories('test_user_services', {
        context: 'character',
        filter: { characterId: 'luffy_one_piece' }
      });

      expect(memoryResults).toHaveLength(1);
      expect(memoryResults[0].name).toBe('Luffy');
      expect(memoryResults[0].anime).toBe('One Piece');

      // Second analysis - should use cached version
      const systemSpy = vi.spyOn(characterService, 'invokeSystem2');
      const analysis2 = await characterService.analyzeCharacter(characterData);

      expect(analysis2).toEqual(analysis1);
      expect(systemSpy).not.toHaveBeenCalled(); // Should use cached
    });

    it('should handle character analysis with missing memory manager gracefully', async () => {
      // Create service without memory manager
      const serviceWithoutMemory = new CharacterAnalysisService({
        memoryManager: null,
        userId: 'test_user_services'
      });

      const characterData = {
        name: 'Goku',
        anime: 'Dragon Ball',
        description: 'Saiyan warrior'
      };

      // Mock System 2 response
      vi.spyOn(serviceWithoutMemory, 'invokeSystem2').mockResolvedValue(
        JSON.stringify({
          overview: 'Powerful Saiyan warrior',
          personality: 'Pure hearted and strong'
        })
      );

      // Should still work with local cache
      const analysis = await serviceWithoutMemory.analyzeCharacter(characterData);
      expect(analysis).toBeDefined();
      expect(analysis.overview).toBe('Powerful Saiyan warrior');
    });

    it('should integrate with multiple character personalities in memory', async () => {
      const characters = [
        { name: 'Naruto', anime: 'Naruto', description: 'Ninja with fox spirit' },
        { name: 'Ichigo', anime: 'Bleach', description: 'Soul reaper student' },
        { name: 'Edward', anime: 'Fullmetal Alchemist', description: 'Alchemist prodigy' }
      ];

      // Analyze multiple characters
      const analyses = [];
      for (const char of characters) {
        const analysis = await characterService.analyzeCharacter(char);
        analyses.push(analysis);
      }

      expect(analyses).toHaveLength(3);

      // Verify all stored in memory
      const allCharacterMemories = await memoryManager.searchMemories('test_user_services', {
        context: 'character'
      });

      expect(allCharacterMemories).toHaveLength(3);

      const storedNames = allCharacterMemories.map(m => m.name).sort();
      expect(storedNames).toEqual(['Edward', 'Ichigo', 'Naruto']);
    });
  });

  describe('DualBrainCoordinator Memory Integration', () => {
    it('should replace contextBuffer with memory storage', async () => {
      expect(dualBrainCoordinator.state.contextBuffer).toBeUndefined();
      expect(dualBrainCoordinator.memoryManager).toBe(memoryManager);
      expect(dualBrainCoordinator.userId).toBe('test_user_services');
    });

    it('should store context updates in memory', async () => {
      const contextData = {
        audioActivity: true,
        vadSignal: 'speaking',
        timestamp: Date.now()
      };

      // Update context
      await dualBrainCoordinator.updateContext('environmental', contextData);

      // Verify stored in memory
      const contextMemories = await memoryManager.searchMemories('test_user_services', {
        context: 'dualbrain_context',
        limit: 5
      });

      expect(contextMemories).toHaveLength(1);
      expect(contextMemories[0].type).toBe('environmental');
      expect(contextMemories[0].data.audioActivity).toBe(true);
      expect(contextMemories[0].source).toBe('dualbrain_coordinator');
    });

    it('should retrieve user memory profiles from memory system', async () => {
      // Store user preferences in memory
      await memoryManager.addMemories('test_user_services', {
        content: 'Prefers anime characters',
        type: 'preference'
      }, 'preferences');

      await memoryManager.addMemories('test_user_services', {
        content: 'Recent conversation about One Piece',
        type: 'interaction'
      }, 'conversation');

      // Get user memory profiles
      const profiles = await dualBrainCoordinator._getUserMemoryProfiles();

      expect(profiles).toBeDefined();
      expect(profiles.userPreferences).toContain('Prefers anime characters');
      expect(profiles.interactionHistory).toContain('Recent conversation about One Piece');
    });

    it('should retrieve avatar profiles from character memory', async () => {
      // Store character analysis in memory
      await memoryManager.addMemories('test_user_services', {
        content: 'Enthusiastic and determined personality',
        type: 'character_analysis',
        metadata: { type: 'personality' }
      }, 'character');

      await memoryManager.addMemories('test_user_services', {
        content: 'Direct and energetic communication',
        type: 'character_analysis',
        metadata: { type: 'responseStyle' }
      }, 'character');

      // Get avatar profiles
      const profiles = await dualBrainCoordinator._getAvatarProfiles();

      expect(profiles).toBeDefined();
      expect(profiles.personality).toContain('Enthusiastic and determined');
      expect(profiles.responseStyle).toContain('Direct and energetic');
    });

    it('should handle periodic analysis with memory-based context', async () => {
      // Store context in memory
      await memoryManager.addMemories('test_user_services', {
        type: 'environmental',
        data: { audioActivity: false },
        timestamp: Date.now()
      }, 'dualbrain_context');

      await memoryManager.addMemories('test_user_services', {
        type: 'conversation',
        data: { userMessage: 'Hello' },
        timestamp: Date.now()
      }, 'dualbrain_context');

      // Mock System 2 response
      const mockSystem2Response = {
        shouldAct: true,
        confidence: 0.8,
        reason: 'User initiated conversation',
        urgency: 'medium'
      };

      vi.spyOn(dualBrainCoordinator, '_invokeSystem2').mockResolvedValue({
        content: JSON.stringify(mockSystem2Response)
      });

      // Generate proactive decision (replaces legacy _performPeriodicAnalysis)
      await dualBrainCoordinator.generateProactiveDecision({ test: 'memory-integration' });

      // Verify System 2 was called with memory context
      expect(dualBrainCoordinator._invokeSystem2).toHaveBeenCalled();
    });
  });

  describe('End-to-End Service Integration', () => {
    it('should provide seamless integration across services', async () => {
      // 1. Character analysis with memory storage
      const characterData = {
        name: 'Tanjiro',
        anime: 'Demon Slayer',
        description: 'Kind-hearted demon slayer'
      };

      const analysis = await characterService.analyzeCharacter(characterData);
      expect(analysis).toBeDefined();

      // 2. DualBrain should access character data through memory
      const avatarProfiles = await dualBrainCoordinator._getAvatarProfiles();
      expect(avatarProfiles).toBeDefined();

      // 3. Context updates should persist across service interactions
      await dualBrainCoordinator.updateContext('conversation', {
        characterAnalysis: analysis,
        interactionType: 'character_roleplay'
      });

      // 4. Verify integration through memory search
      const allMemories = await memoryManager.getAllMemories('test_user_services');

      const characterMemories = allMemories.filter(m => m.context === 'character');
      const contextMemories = allMemories.filter(m => m.context === 'dualbrain_context');

      expect(characterMemories).toHaveLength(1);
      expect(contextMemories).toHaveLength(1);
      expect(characterMemories[0].name).toBe('Tanjiro');
    });

    it('should handle service interactions with memory failures gracefully', async () => {
      // Mock memory manager to fail
      const failingMemoryManager = {
        addMemories: vi.fn().mockRejectedValue(new Error('Storage failed')),
        searchMemories: vi.fn().mockResolvedValue([]),
        getAllMemories: vi.fn().mockResolvedValue([]),
        clearMemories: vi.fn().mockResolvedValue(true)
      };

      // Create services with failing memory
      const characterServiceWithFailingMemory = new CharacterAnalysisService({
        memoryManager: failingMemoryManager,
        userId: 'test_user_services'
      });

      const coordinatorWithFailingMemory = new DualBrainCoordinator(mockAgentService, {
        memoryManager: failingMemoryManager,
        userId: 'test_user_services'
      });

      // Mock System 2 response
      vi.spyOn(characterServiceWithFailingMemory, 'invokeSystem2').mockResolvedValue(
        JSON.stringify({ overview: 'Test analysis' })
      );

      // Services should continue working despite memory failures
      const characterData = { name: 'Test', anime: 'Test' };
      const analysis = await characterServiceWithFailingMemory.analyzeCharacter(characterData);
      expect(analysis).toBeDefined();

      await coordinatorWithFailingMemory.updateContext('test', { data: 'test' });
      const profiles = await coordinatorWithFailingMemory._getUserMemoryProfiles();
      expect(profiles).toBeDefined();

      coordinatorWithFailingMemory.dispose();
    });

    it('should maintain performance across integrated services', async () => {
      const startTime = Date.now();

      // Simulate realistic service usage
      const tasks = [];

      // Multiple character analyses
      for (let i = 0; i < 5; i++) {
        tasks.push(characterService.analyzeCharacter({
          name: `Character${i}`,
          anime: `Anime${i}`,
          description: `Description ${i}`
        }));
      }

      // Multiple context updates
      for (let i = 0; i < 10; i++) {
        tasks.push(dualBrainCoordinator.updateContext('test', {
          data: `context${i}`,
          timestamp: Date.now()
        }));
      }

      // Execute all tasks
      await Promise.all(tasks);

      const totalTime = Date.now() - startTime;

      // Verify all data was stored
      const allMemories = await memoryManager.getAllMemories('test_user_services');
      expect(allMemories.length).toBeGreaterThanOrEqual(15); // 5 characters + 10 contexts

      // Performance should be reasonable
      expect(totalTime).toBeLessThan(5000); // Within 5 seconds

      logger.info(`Service integration performance test completed in ${totalTime}ms`);
    });
  });

  describe('Memory Consistency and Data Integrity', () => {
    it('should maintain data consistency across service updates', async () => {
      const characterData = {
        name: 'Consistency Test',
        anime: 'Test Anime',
        description: 'Test character for consistency'
      };

      // Initial analysis
      const analysis1 = await characterService.analyzeCharacter(characterData);

      // Update context referencing the character
      await dualBrainCoordinator.updateContext('character_interaction', {
        characterName: characterData.name,
        interactionType: 'analysis_complete'
      });

      // Retrieve character again (should be from cache)
      const analysis2 = await characterService.analyzeCharacter(characterData);

      // Both analyses should be identical
      expect(analysis1).toEqual(analysis2);

      // Memory should show consistent data
      const characterMemories = await memoryManager.searchMemories('test_user_services', {
        context: 'character'
      });
      const contextMemories = await memoryManager.searchMemories('test_user_services', {
        context: 'dualbrain_context'
      });

      expect(characterMemories).toHaveLength(1);
      expect(contextMemories).toHaveLength(1);
      expect(contextMemories[0].data.characterName).toBe(characterData.name);
    });

    it('should handle concurrent service operations safely', async () => {
      const concurrentTasks = [];

      // Concurrent character analyses
      for (let i = 0; i < 3; i++) {
        concurrentTasks.push(
          characterService.analyzeCharacter({
            name: `Concurrent${i}`,
            anime: 'Concurrent Test',
            description: `Character ${i}`
          })
        );
      }

      // Concurrent context updates
      for (let i = 0; i < 3; i++) {
        concurrentTasks.push(
          dualBrainCoordinator.updateContext('concurrent', {
            id: i,
            timestamp: Date.now()
          })
        );
      }

      // Execute concurrently
      const results = await Promise.all(concurrentTasks);

      // Verify all operations completed
      expect(results).toHaveLength(6);
      results.slice(0, 3).forEach(analysis => {
        expect(analysis).toBeDefined();
      });

      // Verify data integrity in memory
      const allMemories = await memoryManager.getAllMemories('test_user_services');
      expect(allMemories.length).toBeGreaterThanOrEqual(6);
    });
  });
});