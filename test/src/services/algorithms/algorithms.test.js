/**
 * Algorithm Services Tests
 * Tests for the moved algorithm services
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock the server dependencies
vi.mock('@/server/algorithm', () => ({
  AlgorithmService: class AlgorithmService {
    constructor(name) {
      this.name = name;
      this.gradioClient = null;
    }
    
    async ensureGradioClient() {
      return true;
    }
    
    async processRequest(request) {
      return { success: true, data: {} };
    }
  }
}));

vi.mock('@/server/config', () => ({
  config: {
    gradio: {
      baseUrl: 'http://localhost:7860'
    }
  }
}));

vi.mock('@/server/constants', () => ({
  ENDPOINTS: {
    TEXT_TO_IMAGE: '/text_to_image',
    DOWNLOAD_TEXT_MESH: '/download_text_mesh',
    IMAGE_TO_MESH: '/image_to_mesh',
    DOWNLOAD_IMAGE_MESH: '/download_image_mesh'
  }
}));

vi.mock('@/server/api', () => ({
  GradioClient: {
    getInstance: vi.fn(() => ({
      init: vi.fn(() => Promise.resolve(true)),
      client: {
        predict: vi.fn(() => Promise.resolve({
          success: true,
          data: ['result1', 'result2', 'result3']
        }))
      }
    }))
  }
}));

describe('Algorithm Services', () => {
  describe('AnyTo3DService', () => {
    let AnyTo3DService, anyTo3DService;

    beforeEach(async () => {
      vi.clearAllMocks();
      ({ AnyTo3DService, anyTo3DService } = await import('@/services/algorithms/anyto3d.js'));
    });

    it('should initialize properly', () => {
      expect(anyTo3DService).toBeInstanceOf(AnyTo3DService);
      expect(anyTo3DService.name).toBe('anyto3d');
    });

    it('should handle text-to-3D requests', async () => {
      const request = {
        source: 'text',
        input: 'A red car',
        seed: 42,
        usePredict: true
      };

      const result = await anyTo3DService.anyTo3D(request, (progress) => {
        expect(progress).toBeGreaterThanOrEqual(0);
        expect(progress).toBeLessThanOrEqual(100);
      });

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('meshResult');
      expect(result.data).toHaveProperty('imageResult');
    });

    it('should handle image-to-3D requests', async () => {
      const request = {
        source: 'image',
        input: 'base64image',
        seed: 42
      };

      const result = await anyTo3DService.anyTo3D(request);
      expect(result.success).toBe(true);
    });

    it('should handle tripo-doll requests specially', async () => {
      const mockTripoDollClient = {
        init: vi.fn(() => Promise.resolve(true)),
        client: {
          predict: vi.fn(() => Promise.resolve({
            data: ['doll_image', 'model_glb', 'display_model', 'rigging_glb']
          }))
        }
      };

      const { GradioClient } = vi.mocked(await import('@/server/api'));
      GradioClient.getInstance.mockReturnValue(mockTripoDollClient);

      const request = {
        source: 'tripo-doll',
        input: 'base64image',
        gender: 'boy'
      };

      const result = await anyTo3DService.anyTo3D(request);
      
      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('metadata');
      expect(result.data.metadata.type).toBe('tripo-doll');
      expect(result.data.metadata.gender).toBe('boy');
    });

    it('should handle errors gracefully', async () => {
      const request = null; // Invalid request

      const result = await anyTo3DService.anyTo3D(request);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Request cannot be null');
    });
  });

  describe('TextTo3DService', () => {
    let TextTo3DService, textTo3DService;

    beforeEach(async () => {
      vi.clearAllMocks();
      ({ TextTo3DService, textTo3DService } = await import('@/services/algorithms/textto3d.js'));
    });

    it('should initialize properly', () => {
      expect(textTo3DService).toBeInstanceOf(TextTo3DService);
      expect(textTo3DService.name).toBe('textto3d');
    });

    it('should handle text-to-3D requests', async () => {
      const mockClient = {
        init: vi.fn(() => Promise.resolve(true)),
        client: {
          predict: vi.fn()
            .mockResolvedValueOnce({ data: { session_id: 'test-session' } }) // start_session
            .mockResolvedValueOnce({ data: ['model3d', 'glb1', 'glb2'] }) // process_text_input
        }
      };

      const { GradioClient } = vi.mocked(await import('@/server/api'));
      GradioClient.getInstance.mockReturnValue(mockClient);

      const request = {
        textPrompt: 'A futuristic robot'
      };

      const result = await textTo3DService.textTo3D(request, (progress) => {
        expect(progress).toBeGreaterThanOrEqual(0);
        expect(progress).toBeLessThanOrEqual(100);
      });

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('model3d');
      expect(result.data).toHaveProperty('downloadGlb1');
      expect(result.data).toHaveProperty('downloadGlb2');
      expect(result.data.sessionId).toBe('test-session');
    });

    it('should reuse existing session', async () => {
      const mockClient = {
        init: vi.fn(() => Promise.resolve(true)),
        client: {
          predict: vi.fn()
            .mockResolvedValueOnce({ data: { session_id: 'existing-session' } }) // start_session
            .mockResolvedValueOnce({ data: ['model1', 'glb1', 'glb2'] }) // first request
            .mockResolvedValueOnce({ data: ['model2', 'glb3', 'glb4'] }) // second request
        }
      };

      const { GradioClient } = vi.mocked(await import('@/server/api'));
      GradioClient.getInstance.mockReturnValue(mockClient);

      // First request should start session
      const request1 = { textPrompt: 'First prompt' };
      await textTo3DService.textTo3D(request1);

      // Second request should reuse session
      const request2 = { textPrompt: 'Second prompt' };
      const result2 = await textTo3DService.textTo3D(request2);

      expect(result2.success).toBe(true);
      expect(result2.data.sessionId).toBe('existing-session');
      
      // start_session should only be called once
      expect(mockClient.client.predict).toHaveBeenCalledWith('/start_session', {});
      expect(mockClient.client.predict).toHaveBeenCalledTimes(3); // 1 start_session + 2 process_text_input
    });

    it('should handle session reset', async () => {
      textTo3DService.resetSession();
      expect(textTo3DService.getSessionId()).toBeNull();
    });

    it('should handle disposal', () => {
      textTo3DService.dispose();
      expect(textTo3DService.getSessionId()).toBeNull();
    });

    it('should handle errors gracefully', async () => {
      const request = { textPrompt: '' }; // Empty prompt

      const result = await textTo3DService.textTo3D(request);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Text prompt is required');
    });
  });
});