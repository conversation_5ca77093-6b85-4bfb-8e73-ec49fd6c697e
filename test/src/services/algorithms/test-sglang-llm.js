/**
 * Test script for SGLang LLM service
 * Tests basic functionality, streaming, and error handling
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { unifiedConfig as config } from '@/config/index.ts';

// Test configuration
const TEST_CONFIG = {
    timeout: 30000, // 30 seconds timeout for LLM requests
    defaultModel: 'Xinyuan-LLM-14B-0428',
    testPrompts: {
        simple: "Hello! Please respond with a brief greeting.",
        counting: "Please count from 1 to 5, each number on a new line.",
        conversation: "What is 2 + 2?",
        chinese: "你好，请用中文回答。"
    }
};

describe('SGLang LLM Service Tests', () => {
    let sglangLLMService;

    beforeAll(async () => {
        // Dynamically import the SGLang service
        const { sglangLLMService: service } = await import('../../src/server/algorithms/sglang-llm.js');
        sglangLLMService = service;

        // Verify configuration
        console.log('SGLang Test Configuration:');
        console.log('- Provider:', config.llm.provider);
        console.log('- Endpoint:', config.endpoints.sglang);
        console.log('- Model:', config.llm.sglang.model);
        console.log('- Test Model:', TEST_CONFIG.defaultModel);
    });

    describe('Configuration Tests', () => {
        it('should have correct provider configuration', () => {
            expect(config.llm.provider).toBe('sglang');
        });

        it('should have valid SGLang endpoint', () => {
            expect(config.endpoints.sglang).toBeDefined();
            expect(config.endpoints.sglang).toMatch(/^https?:\/\//);
        });

        it('should have correct model configuration', () => {
            expect(config.llm.sglang.model).toBe(TEST_CONFIG.defaultModel);
        });
    });

    describe('Basic Text Generation', () => {
        it('should generate a simple text response', async () => {
            const testRequest = {
                prompt: TEST_CONFIG.testPrompts.simple,
                model: TEST_CONFIG.defaultModel,
                temperature: 0.7,
                stream: false,
                language: 'English'
            };

            const response = await sglangLLMService.generateText(testRequest);

            expect(response.success).toBe(true);
            expect(response.data).toBeDefined();
            expect(response.data.text).toBeDefined();
            expect(typeof response.data.text).toBe('string');
            expect(response.data.text.length).toBeGreaterThan(0);
            expect(response.data.model).toBe(TEST_CONFIG.defaultModel);
            expect(response.data.usage).toBeDefined();
            expect(response.data.usage.totalTokens).toBeGreaterThan(0);
        }, TEST_CONFIG.timeout);

        it('should handle math questions correctly', async () => {
            const testRequest = {
                prompt: TEST_CONFIG.testPrompts.conversation,
                model: TEST_CONFIG.defaultModel,
                temperature: 0.1, // Lower temperature for more consistent math results
                stream: false,
                language: 'English'
            };

            const response = await sglangLLMService.generateText(testRequest);

            expect(response.success).toBe(true);
            expect(response.data.text).toBeDefined();
            expect(response.data.text).toMatch(/4/); // Should contain the answer "4"
        }, TEST_CONFIG.timeout);

        it('should handle Chinese input', async () => {
            const testRequest = {
                prompt: TEST_CONFIG.testPrompts.chinese,
                model: TEST_CONFIG.defaultModel,
                temperature: 0.7,
                stream: false,
                language: 'Chinese'
            };

            const response = await sglangLLMService.generateText(testRequest);

            expect(response.success).toBe(true);
            expect(response.data.text).toBeDefined();
            expect(response.data.text.length).toBeGreaterThan(0);
        }, TEST_CONFIG.timeout);
    });

    describe('Streaming Tests', () => {
        it('should handle streaming responses', async () => {
            const testRequest = {
                prompt: TEST_CONFIG.testPrompts.counting,
                model: TEST_CONFIG.defaultModel,
                temperature: 0.7,
                stream: true,
                language: 'English'
            };

            let streamedChunks = [];
            let fullStreamResponse = '';

            const response = await sglangLLMService.generateText(
                testRequest,
                (chunk) => {
                    streamedChunks.push(chunk);
                    fullStreamResponse += chunk;
                },
                (fullResponse) => {
                    expect(fullResponse).toBe(fullStreamResponse);
                }
            );

            expect(response.success).toBe(true);
            expect(streamedChunks.length).toBeGreaterThan(0);
            expect(fullStreamResponse.length).toBeGreaterThan(0);

            // Should contain numbers 1-5
            for (let i = 1; i <= 5; i++) {
                expect(fullStreamResponse).toMatch(new RegExp(i.toString()));
            }
        }, TEST_CONFIG.timeout);

        it('should handle streaming with proper callback execution', async () => {
            const testRequest = {
                prompt: TEST_CONFIG.testPrompts.simple,
                model: TEST_CONFIG.defaultModel,
                temperature: 0.7,
                stream: true,
                language: 'English'
            };

            let progressCallbackCalled = false;
            let completionCallbackCalled = false;

            const response = await sglangLLMService.generateText(
                testRequest,
                (chunk) => {
                    progressCallbackCalled = true;
                    expect(typeof chunk).toBe('string');
                },
                (fullResponse) => {
                    completionCallbackCalled = true;
                    expect(typeof fullResponse).toBe('string');
                    expect(fullResponse.length).toBeGreaterThan(0);
                }
            );

            expect(response.success).toBe(true);
            expect(progressCallbackCalled).toBe(true);
            expect(completionCallbackCalled).toBe(true);
        }, TEST_CONFIG.timeout);
    });

    describe('Error Handling', () => {
        it('should handle empty prompt gracefully', async () => {
            const testRequest = {
                prompt: "",
                model: TEST_CONFIG.defaultModel,
                temperature: 0.7,
                stream: false,
                language: 'English'
            };

            const response = await sglangLLMService.generateText(testRequest);

            // Should either succeed with a response or fail gracefully
            if (!response.success) {
                expect(response.error).toBeDefined();
                expect(typeof response.error).toBe('string');
            }
        }, TEST_CONFIG.timeout);

        it('should handle invalid model gracefully', async () => {
            const testRequest = {
                prompt: TEST_CONFIG.testPrompts.simple,
                model: 'invalid-model-name',
                temperature: 0.7,
                stream: false,
                language: 'English'
            };

            const response = await sglangLLMService.generateText(testRequest);

            // Should either succeed (if service handles model fallback) or fail gracefully
            if (!response.success) {
                expect(response.error).toBeDefined();
                expect(typeof response.error).toBe('string');
            }
        }, TEST_CONFIG.timeout);
    });

    describe('Performance Tests', () => {
        it('should respond within reasonable time limits', async () => {
            const startTime = Date.now();

            const testRequest = {
                prompt: TEST_CONFIG.testPrompts.simple,
                model: TEST_CONFIG.defaultModel,
                temperature: 0.7,
                stream: false,
                language: 'English'
            };

            const response = await sglangLLMService.generateText(testRequest);
            const responseTime = Date.now() - startTime;

            expect(response.success).toBe(true);
            expect(responseTime).toBeLessThan(TEST_CONFIG.timeout);

            console.log(`Response time: ${responseTime}ms`);
        }, TEST_CONFIG.timeout);

        it('should handle multiple concurrent requests', async () => {
            const promises = [];
            const numRequests = 3;

            for (let i = 0; i < numRequests; i++) {
                const testRequest = {
                    prompt: `Request ${i + 1}: ${TEST_CONFIG.testPrompts.simple}`,
                    model: TEST_CONFIG.defaultModel,
                    temperature: 0.7,
                    stream: false,
                    language: 'English'
                };

                promises.push(sglangLLMService.generateText(testRequest));
            }

            const responses = await Promise.all(promises);

            responses.forEach((response, index) => {
                expect(response.success).toBe(true);
                expect(response.data.text).toBeDefined();
                console.log(`Request ${index + 1} completed successfully`);
            });
        }, TEST_CONFIG.timeout * 2);
    });
});
