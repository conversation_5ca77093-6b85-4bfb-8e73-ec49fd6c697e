/**
 * Unified LLM Service Tests
 * Tests for the consolidated LLM service architecture
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { UnifiedLLMService } from '@/services/llm/service.js';
import { LLMServiceError } from '@/services/llm/index.js';

// Mock the auth service
vi.mock('@/services/llm/authentication.js', () => ({
  authService: {
    getApiKey: vi.fn(() => 'sk-test-key'),
    createProviderConfig: vi.fn(() => ({
      endpoint: 'https://test.api.com/chat/completions',
      timeout: 30000,
      apiKey: 'sk-test-key',
      retryConfig: {
        maxRetries: 3,
        initialDelay: 1000,
        maxDelay: 10000,
        backoffFactor: 2
      }
    })),
    getAuthHeaders: vi.fn(() => ({
      'Authorization': 'Bearer sk-test-key',
      'Content-Type': 'application/json'
    }))
  }
}));

// Mock node-fetch
vi.mock('node-fetch', () => ({
  default: vi.fn()
}));

describe('UnifiedLLMService', () => {
  let service;
  let mockFetch;

  beforeEach(async () => {
    service = UnifiedLLMService.getInstance();
    mockFetch = vi.mocked((await import('node-fetch')).default);
    vi.clearAllMocks();
  });

  describe('Service Initialization', () => {
    it('should be a singleton', () => {
      const instance1 = UnifiedLLMService.getInstance();
      const instance2 = UnifiedLLMService.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('should register default Aliyun provider', () => {
      const metrics = service.getMetrics();
      expect(metrics).toHaveProperty('aliyun');
    });
  });

  describe('Request Validation', () => {
    it('should validate required parameters', async () => {
      const invalidRequest = {
        // Missing required fields
      };

      await expect(service.invoke(invalidRequest)).rejects.toThrow(LLMServiceError);
    });

    it('should validate provider existence', async () => {
      const invalidRequest = {
        provider: 'non-existent',
        model: 'test-model',
        messages: [{ role: 'user', content: 'test' }]
      };

      await expect(service.invoke(invalidRequest)).rejects.toThrow('Provider not found');
    });

    it('should validate message format', async () => {
      const invalidRequest = {
        provider: 'aliyun',
        model: 'qwen-turbo',
        messages: [] // Empty messages
      };

      await expect(service.invoke(invalidRequest)).rejects.toThrow('Empty messages array');
    });
  });

  describe('Successful API Calls', () => {
    it('should handle successful Aliyun API response', async () => {
      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({
          choices: [{
            message: {
              content: 'Test response',
              audio: null
            }
          }],
          usage: {
            prompt_tokens: 10,
            completion_tokens: 5,
            total_tokens: 15
          }
        })
      };

      mockFetch.mockResolvedValue(mockResponse);

      const request = {
        provider: 'aliyun',
        model: 'qwen-turbo',
        messages: [{ role: 'user', content: 'Hello' }]
      };

      const response = await service.invoke(request);

      expect(response).toMatchObject({
        content: 'Test response',
        audio: null,
        usage: {
          prompt_tokens: 10,
          completion_tokens: 5,
          total_tokens: 15
        },
        metadata: expect.objectContaining({
          model: 'qwen-turbo',
          provider: 'aliyun',
          requestId: expect.any(String),
          timestamp: expect.any(String)
        })
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors properly', async () => {
      const mockResponse = {
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        text: vi.fn().mockResolvedValue('Invalid request format')
      };

      mockFetch.mockResolvedValue(mockResponse);

      const request = {
        provider: 'aliyun',
        model: 'qwen-turbo',
        messages: [{ role: 'user', content: 'Hello' }]
      };

      await expect(service.invoke(request)).rejects.toMatchObject({
        message: expect.stringContaining('Aliyun API error'),
        code: 'API_ERROR',
        statusCode: 400
      });
    });

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'));

      const request = {
        provider: 'aliyun',
        model: 'qwen-turbo',
        messages: [{ role: 'user', content: 'Hello' }]
      };

      await expect(service.invoke(request)).rejects.toMatchObject({
        message: expect.stringContaining('Failed to invoke Aliyun API'),
        code: 'NETWORK_ERROR',
        statusCode: 502
      });
    });
  });

  describe('Metrics Tracking', () => {
    it('should track request metrics', async () => {
      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({
          choices: [{ message: { content: 'test' } }],
          usage: {}
        })
      };

      mockFetch.mockResolvedValue(mockResponse);

      const request = {
        provider: 'aliyun',
        model: 'qwen-turbo',
        messages: [{ role: 'user', content: 'Hello' }]
      };

      const initialMetrics = service.getMetrics('aliyun');
      const initialRequestCount = initialMetrics.aliyun?.requestCount || 0;

      await service.invoke(request);

      const updatedMetrics = service.getMetrics('aliyun');
      expect(updatedMetrics.aliyun.requestCount).toBe(initialRequestCount + 1);
    });

    it('should track error metrics', async () => {
      mockFetch.mockRejectedValue(new Error('Test error'));

      const request = {
        provider: 'aliyun',
        model: 'qwen-turbo',
        messages: [{ role: 'user', content: 'Hello' }]
      };

      const initialMetrics = service.getMetrics('aliyun');
      const initialErrorCount = initialMetrics.aliyun?.errorCount || 0;

      try {
        await service.invoke(request);
      } catch (error) {
        // Expected to throw
      }

      const updatedMetrics = service.getMetrics('aliyun');
      expect(updatedMetrics.aliyun.errorCount).toBe(initialErrorCount + 1);
    });
  });

  describe('Health Checks', () => {
    it('should perform health check for specific provider', async () => {
      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({
          choices: [{ message: { content: 'pong' } }]
        })
      };

      mockFetch.mockResolvedValue(mockResponse);

      const health = await service.healthCheck('aliyun');
      expect(health).toHaveProperty('aliyun', true);
    });

    it('should handle health check failures', async () => {
      mockFetch.mockRejectedValue(new Error('Health check failed'));

      const health = await service.healthCheck('aliyun');
      expect(health).toHaveProperty('aliyun', false);
    });
  });
});