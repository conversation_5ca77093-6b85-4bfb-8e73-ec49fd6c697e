/**
 * LLM Authentication Service Tests
 * Tests for the consolidated authentication service
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { LLMAuthenticationService } from '@/services/llm/authentication.js';
import { LLMServiceError } from '@/services/llm/index.js';

// Mock environment variables
vi.mock('@/config/env.js', () => ({
  getEnvVar: vi.fn((key, defaultValue, required) => {
    const mockEnvVars = {
      'DASHSCOPE_API_KEY': 'sk-test-dashscope-key',
      'OPENAI_API_KEY': 'sk-test-openai-key'
    };
    return mockEnvVars[key] || defaultValue;
  })
}));

describe('LLMAuthenticationService', () => {
  let service;

  beforeEach(() => {
    service = LLMAuthenticationService.getInstance();
    service.clearCache(); // Clear cache between tests
    vi.clearAllMocks();
  });

  describe('Service Initialization', () => {
    it('should be a singleton', () => {
      const instance1 = LLMAuthenticationService.getInstance();
      const instance2 = LLMAuthenticationService.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('API Key Management', () => {
    it('should retrieve Aliyun API key', () => {
      const apiKey = service.getApiKey('aliyun');
      expect(apiKey).toBe('sk-test-dashscope-key');
    });

    it('should retrieve OpenAI API key', () => {
      const apiKey = service.getApiKey('openai');
      expect(apiKey).toBe('sk-test-openai-key');
    });

    it('should throw error for unsupported provider', () => {
      expect(() => service.getApiKey('unsupported')).toThrow(LLMServiceError);
      expect(() => service.getApiKey('unsupported')).toThrow('Unsupported provider');
    });

    it('should cache API keys', async () => {
      const { getEnvVar } = vi.mocked(await import('@/config/env.js'));
      
      // First call
      const key1 = service.getApiKey('aliyun');
      
      // Second call should use cache
      const key2 = service.getApiKey('aliyun');
      
      expect(key1).toBe(key2);
      expect(getEnvVar).toHaveBeenCalledTimes(4); // Called once for each env var in Aliyun lookup
    });
  });

  describe('API Key Validation', () => {
    it('should validate Aliyun API key format', () => {
      expect(service.validateApiKey('aliyun', 'sk-test-dashscope-key')).toBe(true);
      expect(service.validateApiKey('aliyun', 'invalid-key')).toBe(false);
      expect(service.validateApiKey('aliyun', '')).toBe(false);
    });

    it('should validate OpenAI API key format', () => {
      expect(service.validateApiKey('openai', 'sk-' + 'x'.repeat(45))).toBe(true);
      expect(service.validateApiKey('openai', 'sk-short')).toBe(false);
      expect(service.validateApiKey('openai', 'invalid')).toBe(false);
    });

    it('should handle generic validation for unknown providers', () => {
      expect(service.validateApiKey('unknown', 'some-key')).toBe(true);
      expect(service.validateApiKey('unknown', '')).toBe(false);
    });
  });

  describe('Provider Configuration', () => {
    it('should create Aliyun provider configuration', () => {
      const config = service.createProviderConfig('aliyun');
      
      expect(config).toMatchObject({
        endpoint: 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions',
        timeout: 30000,
        apiKey: 'sk-test-dashscope-key',
        retryConfig: {
          maxRetries: 3,
          initialDelay: 1000,
          maxDelay: 10000,
          backoffFactor: 2
        }
      });
    });

    it('should create OpenAI provider configuration', () => {
      const config = service.createProviderConfig('openai');
      
      expect(config).toMatchObject({
        endpoint: 'https://api.openai.com/v1/chat/completions',
        timeout: 30000,
        apiKey: 'sk-test-openai-key',
        retryConfig: {
          maxRetries: 3,
          initialDelay: 1000,
          maxDelay: 10000,
          backoffFactor: 2
        }
      });
    });

    it('should accept configuration overrides', () => {
      const config = service.createProviderConfig('aliyun', {
        timeout: 60000,
        endpoint: 'https://custom.endpoint.com'
      });
      
      expect(config.timeout).toBe(60000);
      expect(config.endpoint).toBe('https://custom.endpoint.com');
      expect(config.apiKey).toBe('sk-test-dashscope-key');
    });
  });

  describe('Authentication Headers', () => {
    it('should generate Aliyun auth headers', () => {
      const headers = service.getAuthHeaders('aliyun');
      
      expect(headers).toEqual({
        'Authorization': 'Bearer sk-test-dashscope-key',
        'Content-Type': 'application/json'
      });
    });

    it('should generate OpenAI auth headers', () => {
      const headers = service.getAuthHeaders('openai');
      
      expect(headers).toEqual({
        'Authorization': 'Bearer sk-test-openai-key',
        'Content-Type': 'application/json'
      });
    });
  });

  describe('Cache Management', () => {
    it('should clear cache for specific provider', async () => {
      const { getEnvVar } = vi.mocked(await import('@/config/env.js'));
      
      // Populate cache
      service.getApiKey('aliyun');
      getEnvVar.mockClear();
      
      // Clear cache for aliyun
      service.clearCache('aliyun');
      
      // Next call should hit environment again
      service.getApiKey('aliyun');
      expect(getEnvVar).toHaveBeenCalled();
    });

    it('should clear all cache', async () => {
      const { getEnvVar } = vi.mocked(await import('@/config/env.js'));
      
      // Populate cache
      service.getApiKey('aliyun');
      service.getApiKey('openai');
      getEnvVar.mockClear();
      
      // Clear all cache
      service.clearCache();
      
      // Next calls should hit environment again
      service.getApiKey('aliyun');
      service.getApiKey('openai');
      expect(getEnvVar).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle missing API keys', async () => {
      const { getEnvVar } = vi.mocked(await import('@/config/env.js'));
      getEnvVar.mockReturnValue(''); // Empty API key
      
      expect(() => service.getApiKey('aliyun')).toThrow(LLMServiceError);
      expect(() => service.getApiKey('aliyun')).toThrow('API key not configured');
    });

    it('should handle invalid API key format', async () => {
      const { getEnvVar } = vi.mocked(await import('@/config/env.js'));
      getEnvVar.mockReturnValue('invalid-key-format');
      
      expect(() => service.createProviderConfig('aliyun')).toThrow(LLMServiceError);
      expect(() => service.createProviderConfig('aliyun')).toThrow('Invalid API key format');
    });
  });
});