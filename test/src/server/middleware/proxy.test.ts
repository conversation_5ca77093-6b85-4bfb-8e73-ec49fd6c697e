import { describe, it, expect, beforeAll, afterAll, vi } from 'vitest';
import express from 'express';
import request from 'supertest';
import { aliyunChatProxy, attachAliyunRealtimeProxy } from '../../../../src/server/middleware/proxy';
import http from 'http';
const WebSocketServer = require('ws').Server;
import * as ws from 'ws';

// Set the API key before anything else so the proxy sees it (needs to be at least 10 chars)
process.env.VITE_DASHSCOPE_API_KEY = process.env.VITE_DASHSCOPE_API_KEY || 'test-api-key-1234567890';

let Response;

vi.mock('node-fetch', () => ({ default: vi.fn() }));
import fetch from 'node-fetch';

beforeAll(async () => {
    ({ Response } = await vi.importActual('node-fetch'));
});

describe('Aliyun HTTP Proxy Endpoint', () => {
    let app;
    beforeAll(() => {
        process.env.VITE_DASHSCOPE_API_KEY = 'test-api-key-1234567890';
        app = express();
        app.use(express.json());
        app.post('/api/aliyun-chat', aliyunChatProxy);
        app.get('/api/aliyun-chat', (req, res) => res.status(405).json({ error: 'Method not allowed' }));
    });

    afterAll(() => {
        delete process.env.VITE_DASHSCOPE_API_KEY;
    });

    it('proxies POST /api/aliyun-chat and streams response', async () => {
        // @ts-ignore
        (fetch as any).mockResolvedValueOnce(new Response(JSON.stringify({ id: 'mock', choices: [{ delta: { content: 'Hello' } }] }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' },
        }));

        const payload = {
            model: 'qwen-omni-turbo',
            messages: [{ role: 'user', content: 'hi' }],
            stream: true
        };
        // @ts-ignore
        const res = await request(app).post('/api/aliyun-chat').send(payload).expect(200);
        // Parse streamed response from res.text
        const data = JSON.parse(res.text);
        expect(data).toHaveProperty('id', 'mock');
        expect(data.choices[0].delta.content).toBe('Hello');
    });

    it('returns 405 for non-POST', async () => {
        // @ts-ignore
        const res = await request(app).get('/api/aliyun-chat');
        expect(res.status).toBe(405);
    });

    it('returns 500 if API key missing', async () => {
        delete process.env.VITE_DASHSCOPE_API_KEY;
        const payload = { model: 'qwen-omni-turbo', messages: [] };
        // @ts-ignore
        const res = await request(app).post('/api/aliyun-chat').send(payload);
        expect(res.status).toBe(500);
        process.env.VITE_DASHSCOPE_API_KEY = 'test-api-key-1234567890';
    });
});

describe('Aliyun WebSocket Proxy', () => {
    let server, port, wsClient, mockBackend, mockBackendPort;
    beforeAll(async () => {
        process.env.VITE_DASHSCOPE_API_KEY = 'test-api-key-1234567890';
        // Start mock backend WebSocket server
        mockBackend = new WebSocketServer({ port: 0 });
        await new Promise(resolve => mockBackend.on('listening', resolve));
        mockBackendPort = mockBackend.address().port;
        mockBackend.on('connection', ws => {
            ws.on('message', msg => {
                // Echo or respond with a test message
                ws.send(JSON.stringify({ type: 'mock_backend_response', data: 'pong' }));
            });
        });
        // TODO: For a true integration test, patch attachAliyunRealtimeProxy to use mock backendUrl
        // For now, just call as normal (proxy will try to connect to real backend unless patched)
        const expressApp = express();
        server = http.createServer(expressApp);
        attachAliyunRealtimeProxy(server, '/ws');
        await new Promise<void>((resolve) => {
            server.listen(0, () => {
                port = server.address().port;
                resolve();
            });
        });
    });
    afterAll(async () => {
        if (wsClient) wsClient.close();
        if (server) await new Promise<void>((resolve) => server.close(resolve));
        if (mockBackend) await new Promise<void>((resolve) => mockBackend.close(resolve));
    });
    it('should accept a WebSocket connection and relay messages', async () => {
        console.log('Starting WebSocket proxy test');
        await new Promise((resolve, reject) => {
            wsClient = new ws.WebSocket(`ws://localhost:${port}/ws?model=qwen-omni-turbo-realtime`);
            wsClient.on('open', () => {
                console.log('WebSocket client connected');
                // Send a minimal valid message (session.update)
                const msg = JSON.stringify({
                    event_id: 'event_test',
                    type: 'session.update',
                    session: { modalities: ['text'] }
                });
                wsClient.send(msg);
            });
            wsClient.on('message', (data) => {
                console.log('WebSocket client received message:', data.toString());
                resolve(undefined);
            });
            wsClient.on('error', (err) => {
                console.error('WebSocket client error:', err);
                reject(err);
            });
            wsClient.on('close', (code, reason) => {
                console.log('WebSocket client closed:', code, reason.toString());
            });
        });
    }, 30000); // Increase timeout to 30s
}); 