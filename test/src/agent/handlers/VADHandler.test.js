/**
 * Comprehensive Test Suite for VADHandler
 * Tests all functionality including callback management, preservation,
 * environmental analysis, and workflow integration
 */

import { VADHandler } from '../../../src/agent/handlers/VADHandler.js';
import { createLogger } from '../../../src/utils/logger.js';

describe('VADHandler - Unified VAD Management System', () => {
    let vadHandler;
    let mockConnectionManager;
    let mockLogger;
    let mockAgentService;
    
    beforeEach(() => {
        mockConnectionManager = {
            isConnected: jest.fn(() => true),
            getConnectionState: jest.fn(() => 'connected')
        };
        
        mockLogger = {
            info: jest.fn(),
            debug: jest.fn(),
            warn: jest.fn(),
            error: jest.fn()
        };
        
        mockAgentService = {
            generateResponse: jest.fn(() => Promise.resolve('Mock response'))
        };
        
        vadHandler = new VADHandler(mockConnectionManager, mockLogger);
    });
    
    afterEach(() => {
        if (vadHandler) {
            vadHandler.dispose();
        }
    });
    
    describe('Single Registration System', () => {
        test('should register callbacks for all event types', () => {
            const speechStartedCallback = jest.fn();
            const speechStoppedCallback = jest.fn();
            
            const id1 = vadHandler.registerCallback('speechStarted', speechStartedCallback, { name: 'test1' });
            const id2 = vadHandler.registerCallback('speechStopped', speechStoppedCallback, { name: 'test2' });
            
            expect(id1).toBeTruthy();
            expect(id2).toBeTruthy();
            expect(vadHandler.callbacks.speechStarted.has(speechStartedCallback)).toBe(true);
            expect(vadHandler.callbacks.speechStopped.has(speechStoppedCallback)).toBe(true);
        });
        
        test('should prevent duplicate callback registration', () => {
            const callback = jest.fn();
            
            const id1 = vadHandler.registerCallback('speechStarted', callback);
            const id2 = vadHandler.registerCallback('speechStarted', callback);
            
            expect(id1).toBeTruthy();
            expect(id2).toBeNull();
            expect(vadHandler.callbacks.speechStarted.size).toBe(1);
        });
        
        test('should enforce callback limits', () => {
            vadHandler.configure({ maxCallbacks: 2 });
            
            vadHandler.registerCallback('speechStarted', jest.fn());
            vadHandler.registerCallback('speechStarted', jest.fn());
            
            expect(() => {
                vadHandler.registerCallback('speechStarted', jest.fn());
            }).toThrow('Maximum VAD callbacks reached for speechStarted');
        });
        
        test('should validate callback function types', () => {
            expect(() => {
                vadHandler.registerCallback('speechStarted', 'not a function');
            }).toThrow('VAD callback must be a function');
        });
        
        test('should validate event types', () => {
            expect(() => {
                vadHandler.registerCallback('invalidEventType', jest.fn());
            }).toThrow('Invalid VAD event type: invalidEventType');
        });
        
        test('should unregister callbacks by function reference', () => {
            const callback = jest.fn();
            vadHandler.registerCallback('speechStarted', callback);
            
            const result = vadHandler.unregisterCallback('speechStarted', callback);
            
            expect(result).toBe(true);
            expect(vadHandler.callbacks.speechStarted.has(callback)).toBe(false);
        });
        
        test('should unregister callbacks by registration ID', () => {
            const callback = jest.fn();
            const id = vadHandler.registerCallback('speechStarted', callback);
            
            const result = vadHandler.unregisterCallback('speechStarted', id);
            
            expect(result).toBe(true);
            expect(vadHandler.callbacks.speechStarted.has(callback)).toBe(false);
        });
        
        test('should clear all callbacks for specific event type', () => {
            vadHandler.registerCallback('speechStarted', jest.fn());
            vadHandler.registerCallback('speechStarted', jest.fn());
            vadHandler.registerCallback('speechStopped', jest.fn());
            
            vadHandler.clearAllCallbacks('speechStarted');
            
            expect(vadHandler.callbacks.speechStarted.size).toBe(0);
            expect(vadHandler.callbacks.speechStopped.size).toBe(1);
        });
        
        test('should clear all callbacks across all event types', () => {
            vadHandler.registerCallback('speechStarted', jest.fn());
            vadHandler.registerCallback('speechStopped', jest.fn());
            vadHandler.registerCallback('transcriptReceived', jest.fn());
            
            vadHandler.clearAllCallbacks();
            
            expect(vadHandler.callbacks.speechStarted.size).toBe(0);
            expect(vadHandler.callbacks.speechStopped.size).toBe(0);
            expect(vadHandler.callbacks.transcriptReceived.size).toBe(0);
        });
    });
    
    describe('Bulletproof Callback Preservation', () => {
        test('should preserve callbacks successfully', () => {
            const callback1 = jest.fn();
            const callback2 = jest.fn();
            
            vadHandler.registerCallback('speechStarted', callback1, { name: 'callback1' });
            vadHandler.registerCallback('speechStopped', callback2, { name: 'callback2' });
            
            const result = vadHandler.preserveCallbacks();
            
            expect(result).toBe(true);
            expect(vadHandler.preservedCallbacks).toBeTruthy();
            expect(vadHandler.preservedCallbacks.callbacks.speechStarted).toHaveLength(1);
            expect(vadHandler.preservedCallbacks.callbacks.speechStopped).toHaveLength(1);
        });
        
        test('should restore preserved callbacks successfully', () => {
            const callback1 = jest.fn();
            const callback2 = jest.fn();
            
            vadHandler.registerCallback('speechStarted', callback1, { name: 'callback1' });
            vadHandler.registerCallback('speechStopped', callback2, { name: 'callback2' });
            
            vadHandler.preserveCallbacks();
            vadHandler.clearAllCallbacks();
            
            expect(vadHandler.callbacks.speechStarted.size).toBe(0);
            expect(vadHandler.callbacks.speechStopped.size).toBe(0);
            
            const result = vadHandler.restoreCallbacks();
            
            expect(result).toBe(true);
            expect(vadHandler.callbacks.speechStarted.has(callback1)).toBe(true);
            expect(vadHandler.callbacks.speechStopped.has(callback2)).toBe(true);
        });
        
        test('should handle restoration without preserved callbacks', () => {
            const result = vadHandler.restoreCallbacks();
            
            expect(result).toBe(false);
            expect(mockLogger.warn).toHaveBeenCalledWith(
                expect.stringContaining('No preserved callbacks to restore')
            );
        });
        
        test('should validate callbacks during restoration', () => {
            const validCallback = jest.fn();
            const invalidCallback = null;
            
            // Manually create preservation data with invalid callback
            vadHandler.preservedCallbacks = {
                callbacks: {
                    speechStarted: [
                        { original: validCallback, validated: true },
                        { original: invalidCallback, validated: false }
                    ]
                },
                metadata: new Map(),
                preservedAt: Date.now()
            };
            
            vadHandler.restoreCallbacks();
            
            // Only the valid callback should be restored
            expect(vadHandler.callbacks.speechStarted.size).toBe(1);
            expect(vadHandler.callbacks.speechStarted.has(validCallback)).toBe(true);
        });
    });
    
    describe('VAD Event Processing', () => {
        test('should handle speech started event with environmental analysis', () => {
            const callback = jest.fn();
            vadHandler.registerCallback('speechStarted', callback);
            
            const event = {
                confidence: 0.9,
                threshold: 0.3,
                timestamp: Date.now()
            };
            
            const result = vadHandler.handleSpeechStarted(event);
            
            expect(result).toBeTruthy();
            expect(result.audioQuality).toBeTruthy();
            expect(result.speakerProximity).toBeTruthy();
            expect(result.acousticEnvironment).toBeTruthy();
            expect(callback).toHaveBeenCalledWith(result, event);
        });
        
        test('should handle speech stopped event with workflow triggering', () => {
            vadHandler.setAgentService(mockAgentService);
            
            const callback = jest.fn();
            vadHandler.registerCallback('speechStopped', callback);
            
            // Set up speech started first
            vadHandler.handleSpeechStarted({ confidence: 0.9 });
            
            const event = {
                confidence: 0.8,
                transcript: 'Hello world',
                timestamp: Date.now()
            };
            
            const result = vadHandler.handleSpeechStopped(event);
            
            expect(result).toBeTruthy();
            expect(result.speechQuality).toBeTruthy();
            expect(result.readinessForResponse).toBeTruthy();
            expect(callback).toHaveBeenCalledWith(result, event);
            
            // Check workflow was triggered
            setTimeout(() => {
                expect(mockAgentService.generateResponse).toHaveBeenCalled();
            }, 10);
        });
        
        test('should execute multiple callbacks for same event', () => {
            const callback1 = jest.fn();
            const callback2 = jest.fn();
            
            vadHandler.registerCallback('speechStarted', callback1);
            vadHandler.registerCallback('speechStarted', callback2);
            
            const event = { confidence: 0.8 };
            vadHandler.handleSpeechStarted(event);
            
            expect(callback1).toHaveBeenCalled();
            expect(callback2).toHaveBeenCalled();
        });
        
        test('should handle callback errors gracefully', () => {
            const errorCallback = jest.fn(() => { throw new Error('Callback error'); });
            const normalCallback = jest.fn();
            
            vadHandler.registerCallback('speechStarted', errorCallback);
            vadHandler.registerCallback('speechStarted', normalCallback);
            
            const event = { confidence: 0.8 };
            
            expect(() => {
                vadHandler.handleSpeechStarted(event);
            }).not.toThrow();
            
            expect(normalCallback).toHaveBeenCalled();
            expect(mockLogger.error).toHaveBeenCalled();
        });
    });
    
    describe('Environmental Analysis', () => {
        test('should analyze audio quality correctly', () => {
            const vadData = {
                confidence: 0.9,
                threshold: 0.2,
                eventType: 'speechStarted'
            };
            
            const analysis = vadHandler.analyzeAudioEnvironment(vadData);
            
            expect(analysis.audioQuality).toBeTruthy();
            expect(analysis.audioQuality.rating).toBe('excellent');
            expect(analysis.audioQuality.score).toBeGreaterThan(80);
        });
        
        test('should estimate speaker proximity', () => {
            const vadData = {
                confidence: 0.8,
                eventType: 'speechStarted'
            };
            
            const analysis = vadHandler.analyzeAudioEnvironment(vadData);
            
            expect(analysis.speakerProximity).toBeTruthy();
            expect(['very_close', 'close', 'moderate', 'distant']).toContain(analysis.speakerProximity.estimation);
        });
        
        test('should classify acoustic environment', () => {
            const vadData = {
                confidence: 0.9,
                threshold: 0.1,
                eventType: 'speechStarted'
            };
            
            const analysis = vadHandler.analyzeAudioEnvironment(vadData);
            
            expect(analysis.acousticEnvironment).toBeTruthy();
            expect(analysis.acousticEnvironment.type).toBeTruthy();
            expect(typeof analysis.acousticEnvironment.confidence).toBe('number');
        });
        
        test('should analyze engagement level based on history', () => {
            // Add some history first
            vadHandler.environmentalHistory = [
                { audioQuality: 'excellent', engagementLevel: 'highly_engaged' },
                { audioQuality: 'good', engagementLevel: 'actively_engaged' },
                { audioQuality: 'excellent', engagementLevel: 'highly_engaged' }
            ];
            
            const vadData = {
                confidence: 0.9,
                eventType: 'speechStarted'
            };
            
            const analysis = vadHandler.analyzeAudioEnvironment(vadData);
            
            expect(analysis.engagementLevel).toBeTruthy();
            expect(['highly_engaged', 'actively_engaged', 'moderately_engaged', 'passively_engaged', 'low_engagement'])
                .toContain(analysis.engagementLevel.level);
        });
        
        test('should generate DualBrain context', () => {
            const vadData = {
                confidence: 0.9,
                eventType: 'speechStarted'
            };
            
            const analysis = vadHandler.analyzeAudioEnvironment(vadData);
            
            expect(analysis.dualBrainContext).toBeTruthy();
            expect(['activate_system_two', 'continue_system_one']).toContain(
                analysis.dualBrainContext.systemTwoRecommendation
            );
            expect(['deliberative', 'intuitive']).toContain(
                analysis.dualBrainContext.processingMode
            );
        });
        
        test('should maintain environmental history', () => {
            vadHandler.handleSpeechStarted({ confidence: 0.8 });
            vadHandler.handleSpeechStarted({ confidence: 0.7 });
            vadHandler.handleSpeechStarted({ confidence: 0.9 });
            
            expect(vadHandler.environmentalHistory.length).toBe(3);
            expect(vadHandler.environmentalHistory[0].timestamp).toBeTruthy();
        });
        
        test('should limit environmental history length', () => {
            vadHandler.configure({ maxHistoryLength: 3 });
            
            for (let i = 0; i < 5; i++) {
                vadHandler.handleSpeechStarted({ confidence: 0.8 });
            }
            
            expect(vadHandler.environmentalHistory.length).toBe(3);
        });
    });
    
    describe('Workflow Integration', () => {
        beforeEach(() => {
            vadHandler.setAgentService(mockAgentService);
        });
        
        test('should trigger workflow with contextual data', () => {
            const transcript = 'Test transcript';
            const context = {
                processingRecommendations: { priority: 'high' },
                dualBrainContext: { systemTwoRecommendation: 'activate_system_two' }
            };
            
            const result = vadHandler.triggerWorkflow(transcript, context);
            
            expect(result).toBe(true);
            expect(mockAgentService.generateResponse).toHaveBeenCalledWith(
                transcript,
                expect.objectContaining({
                    vadTriggered: true,
                    environmentalContext: context
                })
            );
        });
        
        test('should handle workflow without agent service', () => {
            vadHandler.agentService = null;
            
            const result = vadHandler.triggerWorkflow('test', {});
            
            expect(result).toBe(false);
            expect(mockLogger.warn).toHaveBeenCalledWith(
                expect.stringContaining('Cannot trigger workflow - no agent service available')
            );
        });
        
        test('should integrate workflow triggering in speech stopped event', async () => {
            const event = {
                confidence: 0.9,
                transcript: 'Hello world'
            };
            
            vadHandler.handleSpeechStarted({ confidence: 0.8 });
            vadHandler.handleSpeechStopped(event);
            
            // Wait for async workflow trigger
            await new Promise(resolve => setTimeout(resolve, 10));
            
            expect(mockAgentService.generateResponse).toHaveBeenCalled();
        });
    });
    
    describe('Configuration and Status', () => {
        test('should configure successfully', () => {
            const config = {
                maxCallbacks: 20,
                analysisDepth: 'basic',
                environmentalIntelligence: false
            };
            
            const result = vadHandler.configure(config);
            
            expect(result).toBe(true);
            expect(vadHandler.config.maxCallbacks).toBe(20);
            expect(vadHandler.config.analysisDepth).toBe('basic');
            expect(vadHandler.config.environmentalIntelligence).toBe(false);
        });
        
        test('should check configuration status', () => {
            expect(vadHandler.isConfigured()).toBe(true);
            
            vadHandler.callbacks = null;
            expect(vadHandler.isConfigured()).toBe(false);
        });
        
        test('should provide comprehensive status', () => {
            vadHandler.registerCallback('speechStarted', jest.fn());
            vadHandler.registerCallback('speechStopped', jest.fn());
            
            const status = vadHandler.getStatus();
            
            expect(status).toHaveProperty('configuration');
            expect(status).toHaveProperty('callbacks');
            expect(status).toHaveProperty('environment');
            expect(status).toHaveProperty('workflow');
            expect(status).toHaveProperty('performance');
            expect(status).toHaveProperty('errors');
            
            expect(status.callbacks.total).toBe(2);
            expect(status.configuration.isConfigured).toBe(true);
        });
        
        test('should provide debug information', () => {
            vadHandler.registerCallback('speechStarted', jest.fn(), { name: 'test-callback' });
            
            const debugInfo = vadHandler.getDebugInfo();
            
            expect(debugInfo).toHaveProperty('callbacks');
            expect(debugInfo).toHaveProperty('environmentalHistory');
            expect(debugInfo).toHaveProperty('currentState');
            
            expect(debugInfo.callbacks).toHaveLength(1);
            expect(debugInfo.callbacks[0].metadata.name).toBe('test-callback');
        });
    });
    
    describe('Error Handling and Recovery', () => {
        test('should handle preservation errors gracefully', () => {
            // Force an error by corrupting internal state
            vadHandler.callbacks = null;
            
            const result = vadHandler.preserveCallbacks();
            
            expect(result).toBe(false);
            expect(vadHandler.errorCount).toBeGreaterThan(0);
        });
        
        test('should track error metrics', () => {
            const errorCallback = jest.fn(() => { throw new Error('Test error'); });
            vadHandler.registerCallback('speechStarted', errorCallback);
            
            vadHandler.handleSpeechStarted({ confidence: 0.8 });
            
            expect(vadHandler.errorCount).toBeGreaterThan(0);
            expect(vadHandler.lastError).toBeTruthy();
        });
        
        test('should attempt error recovery when enabled', () => {
            vadHandler.configure({ errorRecovery: true });
            
            // Force error during speech processing
            vadHandler.callbacks.speechStarted = new Set([() => { throw new Error('Test error'); }]);
            
            expect(() => {
                vadHandler.handleSpeechStarted({ confidence: 0.8 });
            }).toThrow();
            
            expect(vadHandler.recoveryAttempts).toBeGreaterThan(0);
        });
        
        test('should provide fallback analysis on error', () => {
            // Mock environmental analysis to throw error
            vadHandler._assessAudioQuality = jest.fn(() => { throw new Error('Analysis error'); });
            
            const result = vadHandler.analyzeAudioEnvironment({ eventType: 'speechStarted' });
            
            expect(result).toBeTruthy();
            expect(result.audioQuality.rating).toBe('unknown');
        });
    });
    
    describe('Performance and Metrics', () => {
        test('should track callback registration metrics', () => {
            vadHandler.registerCallback('speechStarted', jest.fn());
            vadHandler.registerCallback('speechStopped', jest.fn());
            
            expect(vadHandler.metrics.callbacksRegistered).toBe(2);
        });
        
        test('should track speech event processing metrics', () => {
            vadHandler.handleSpeechStarted({ confidence: 0.8 });
            vadHandler.handleSpeechStopped({ confidence: 0.8 });
            
            expect(vadHandler.metrics.speechEventsProcessed).toBe(2);
        });
        
        test('should track environmental analysis metrics', () => {
            vadHandler.analyzeAudioEnvironment({ eventType: 'speechStarted' });
            vadHandler.analyzeAudioEnvironment({ eventType: 'speechStarted' });
            
            expect(vadHandler.metrics.environmentalAnalysesPerformed).toBe(2);
        });
        
        test('should track workflow trigger metrics', () => {
            vadHandler.setAgentService(mockAgentService);
            
            vadHandler.triggerWorkflow('test', { readinessForResponse: { ready: true } });
            vadHandler.triggerWorkflow('test2', { readinessForResponse: { ready: true } });
            
            expect(vadHandler.metrics.workflowTriggersExecuted).toBe(2);
        });
    });
    
    describe('Reset and Dispose', () => {
        test('should reset all state correctly', () => {
            vadHandler.registerCallback('speechStarted', jest.fn());
            vadHandler.environmentalHistory = [{ test: 'data' }];
            vadHandler.errorCount = 5;
            
            vadHandler.reset();
            
            expect(vadHandler.callbacks.speechStarted.size).toBe(0);
            expect(vadHandler.environmentalHistory).toHaveLength(0);
            expect(vadHandler.errorCount).toBe(0);
            expect(vadHandler.metrics.callbacksRegistered).toBe(0);
        });
        
        test('should dispose of resources properly', () => {
            vadHandler.registerCallback('speechStarted', jest.fn());
            vadHandler.setAgentService(mockAgentService);
            
            vadHandler.dispose();
            
            expect(vadHandler.callbacks.speechStarted.size).toBe(0);
            expect(vadHandler.connectionManager).toBeNull();
            expect(vadHandler.agentService).toBeNull();
        });
    });
    
    describe('Edge Cases and Robustness', () => {
        test('should handle empty VAD data gracefully', () => {
            const result = vadHandler.analyzeAudioEnvironment({});
            
            expect(result).toBeTruthy();
            expect(result.audioQuality).toBeTruthy();
        });
        
        test('should handle missing confidence values', () => {
            const event = { eventType: 'speechStarted' };
            
            expect(() => {
                vadHandler.handleSpeechStarted(event);
            }).not.toThrow();
        });
        
        test('should handle rapid successive events', () => {
            const callback = jest.fn();
            vadHandler.registerCallback('speechStarted', callback);
            
            // Fire multiple events rapidly
            for (let i = 0; i < 10; i++) {
                vadHandler.handleSpeechStarted({ confidence: 0.8 });
            }
            
            expect(callback).toHaveBeenCalledTimes(10);
            expect(vadHandler.environmentalHistory.length).toBe(10);
        });
        
        test('should handle very long environmental history', () => {
            vadHandler.configure({ maxHistoryLength: 5 });
            
            // Add more entries than the limit
            for (let i = 0; i < 10; i++) {
                vadHandler.handleSpeechStarted({ confidence: 0.8 });
            }
            
            expect(vadHandler.environmentalHistory.length).toBe(5);
        });
    });
});

// Integration tests for VADHandler with other systems
describe('VADHandler Integration Tests', () => {
    let vadHandler;
    let mockConnectionManager;
    let mockAgentService;
    
    beforeEach(() => {
        mockConnectionManager = {
            isConnected: jest.fn(() => true),
            on: jest.fn(),
            off: jest.fn()
        };
        
        mockAgentService = {
            generateResponse: jest.fn(() => Promise.resolve({ result: 'success' }))
        };
        
        vadHandler = new VADHandler(mockConnectionManager);
        vadHandler.setAgentService(mockAgentService);
    });
    
    test('should handle complete VAD workflow cycle', async () => {
        const speechStartedCallback = jest.fn();
        const speechStoppedCallback = jest.fn();
        
        vadHandler.registerCallback('speechStarted', speechStartedCallback);
        vadHandler.registerCallback('speechStopped', speechStoppedCallback);
        
        // Simulate complete cycle
        const startEvent = { confidence: 0.9, timestamp: Date.now() };
        const stopEvent = { confidence: 0.8, transcript: 'Hello world', timestamp: Date.now() + 2000 };
        
        const startResult = vadHandler.handleSpeechStarted(startEvent);
        expect(speechStartedCallback).toHaveBeenCalled();
        expect(startResult).toBeTruthy();
        
        // Wait a bit to simulate speech duration
        await new Promise(resolve => setTimeout(resolve, 10));
        
        const stopResult = vadHandler.handleSpeechStopped(stopEvent);
        expect(speechStoppedCallback).toHaveBeenCalled();
        expect(stopResult).toBeTruthy();
        
        // Check workflow was triggered
        await new Promise(resolve => setTimeout(resolve, 10));
        expect(mockAgentService.generateResponse).toHaveBeenCalled();
    });
    
    test('should handle connection loss and recovery scenario', () => {
        const callback = jest.fn();
        vadHandler.registerCallback('speechStarted', callback);
        
        // Preserve callbacks before connection loss
        vadHandler.preserveCallbacks();
        
        // Simulate connection loss - clear callbacks
        vadHandler.clearAllCallbacks();
        expect(vadHandler.callbacks.speechStarted.size).toBe(0);
        
        // Simulate reconnection - restore callbacks
        vadHandler.restoreCallbacks();
        expect(vadHandler.callbacks.speechStarted.has(callback)).toBe(true);
        
        // Test that restored callbacks work
        vadHandler.handleSpeechStarted({ confidence: 0.8 });
        expect(callback).toHaveBeenCalled();
    });
});