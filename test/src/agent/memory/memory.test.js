/**
 * Comprehensive LangGraph Memory Implementation Test
 * Tests both basic LangGraph components and full memory manager functionality
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { LangGraphMemoryManager } from '@/agent/memory/index.js';
import { MemorySaver, InMemoryStore } from '@langchain/langgraph';
import { v4 as uuidv4 } from 'uuid';

describe('LangGraph Memory System', () => {
    describe('Basic LangGraph Components', () => {
        it('should create MemorySaver successfully', () => {
            const checkpointer = new MemorySaver();
            expect(checkpointer).toBeDefined();
        });

        it('should create InMemoryStore successfully', () => {
            const store = new InMemoryStore();
            expect(store).toBeDefined();
        });

        it('should perform basic store operations', async () => {
            const store = new InMemoryStore();
            const namespace = ['test-user', 'memories'];
            const memoryId = uuidv4();
            const testData = {
                content: 'This is a test memory',
                timestamp: Date.now(),
                context: 'test'
            };

            // Put data
            await store.put(namespace, memoryId, testData);

            // Get data
            const retrieved = await store.get(namespace, memoryId);
            expect(retrieved?.value?.content).toBe('This is a test memory');

            // Search data
            const searchResults = await store.search(namespace);
            expect(searchResults.length).toBe(1);

            // Delete data
            await store.delete(namespace, memoryId);

            // Verify deletion
            const afterDelete = await store.search(namespace);
            expect(afterDelete.length).toBe(0);
        });
    });

    describe('LangGraph Memory Manager', () => {
        let memoryManager;
        const userId = 'test-user-123';

        beforeEach(() => {
            memoryManager = new LangGraphMemoryManager();
        });

        afterEach(() => {
            if (memoryManager) {
                memoryManager.dispose();
            }
        });

        it('should initialize memory manager successfully', () => {
            expect(memoryManager).toBeDefined();
            expect(memoryManager.getCheckpointer()).toBeDefined();
            expect(memoryManager.getStore()).toBeDefined();
        });

        it('should add and retrieve memories', async () => {
            const testMemories = [
                'User prefers short responses',
                'User is interested in AI and technology',
                'User speaks English'
            ];

            // Add memories
            const memoryIds = await memoryManager.addMemories(userId, testMemories, 'preferences');
            expect(memoryIds).toHaveLength(3);
            expect(memoryIds.every(id => typeof id === 'string')).toBe(true);

            // Search memories
            const searchResults = await memoryManager.searchMemories(userId, { context: 'preferences' });
            expect(searchResults).toHaveLength(3);
            expect(searchResults.map(m => m.content)).toEqual(expect.arrayContaining(testMemories));
        });

        it('should get memory statistics', async () => {
            const testMemories = ['Test memory 1', 'Test memory 2'];
            await memoryManager.addMemories(userId, testMemories, 'preferences');

            const stats = await memoryManager.getMemoryStats(userId);
            expect(stats.userId).toBe(userId);
            expect(stats.totalMemories).toBe(2);
            expect(stats.byContext.preferences).toBe(2);
            expect(stats.oldestMemory).toBeDefined();
            expect(stats.newestMemory).toBeDefined();
        });

        it('should format memories for LLM', async () => {
            const testMemories = ['User likes cats', 'User dislikes rain'];
            await memoryManager.addMemories(userId, testMemories, 'preferences');

            const searchResults = await memoryManager.searchMemories(userId, { context: 'preferences' });
            const formatted = memoryManager.formatMemoriesForLLM(searchResults);

            expect(formatted).toContain('User likes cats');
            expect(formatted).toContain('User dislikes rain');
            expect(formatted).toContain('[Context: preferences]');
        });

        it('should clear memories', async () => {
            const testMemories = ['Temporary memory 1', 'Temporary memory 2'];
            await memoryManager.addMemories(userId, testMemories, 'temporary');

            // Verify memories exist
            let searchResults = await memoryManager.searchMemories(userId, { context: 'temporary' });
            expect(searchResults).toHaveLength(2);

            // Clear memories
            const cleared = await memoryManager.clearMemories(userId, 'temporary');
            expect(cleared).toBe(true);

            // Verify memories are cleared
            searchResults = await memoryManager.searchMemories(userId, { context: 'temporary' });
            expect(searchResults).toHaveLength(0);
        });

        it('should get memory context with conversation history and user memories', async () => {
            const sessionId = 'test-session-123';
            const userId = 'test-user-456';

            // Add some memories first
            await memoryManager.addMemories(userId, ['User likes pizza', 'User speaks English'], 'preferences');

            // Get memory context
            const context = await memoryManager.getMemoryContext(sessionId, userId);

            expect(context).toBeDefined();
            expect(context.memory_type).toBe('langgraph');
            expect(context.conversation_history).toBeInstanceOf(Array);
            expect(context.user_memories).toBeInstanceOf(Array);
            expect(context.memory_stats).toBeDefined();
            expect(context.memory_stats.userId).toBe(userId);
            expect(context.memory_stats.sessionId).toBe(sessionId);
            expect(context.user_memories.length).toBe(2);
        });

        it('should add conversation turns', async () => {
            const sessionId = 'test-session-789';
            const userId = 'test-user-101';
            const userMessage = 'Hello, how are you?';
            const aiResponse = 'I am doing well, thank you!';

            const result = await memoryManager.addConversationTurn(sessionId, userId, userMessage, aiResponse);
            expect(result).toBe(true);

            // Verify the conversation was stored as memories
            const searchResults = await memoryManager.searchMemories(userId, { context: 'conversation' });
            expect(searchResults.length).toBeGreaterThan(0);

            const userMemory = searchResults.find(m => m.content.includes('User said:'));
            const aiMemory = searchResults.find(m => m.content.includes('Assistant replied:'));

            expect(userMemory).toBeDefined();
            expect(aiMemory).toBeDefined();
            expect(userMemory.content).toContain(userMessage);
            expect(aiMemory.content).toContain(aiResponse);
        });
    });
});
