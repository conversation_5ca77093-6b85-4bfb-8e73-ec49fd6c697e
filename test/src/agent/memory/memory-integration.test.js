/**
 * Enhanced Memory Integration Tests - December 2024
 * 
 * Comprehensive test suite for LangGraph memory integration covering:
 * - Memory manager initialization
 * - Memory storage and retrieval
 * - Cross-thread persistence
 * - Character analysis memory integration
 * - DualBrain context memory
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { LangGraphMemoryManager } from '../../../../src/agent/memory/index.js';
import { CharacterAnalysisService } from '../../../../app/viewer/services/CharacterAnalysisService.js';
import { createLogger } from '../../../../src/utils/logger.js';

const logger = createLogger('MemoryIntegrationTest');

describe('Enhanced Memory Integration - December 2024', () => {
  let memoryManager;
  let characterService;

  beforeEach(async () => {
    // Initialize fresh memory manager for each test
    memoryManager = new LangGraphMemoryManager({
      enablePersistence: false, // Use in-memory for tests
      enableLogging: false
    });

    characterService = new CharacterAnalysisService({
      memoryManager,
      userId: 'test_user_123',
      cacheExpiry: 1000 // Short expiry for testing
    });
  });

  afterEach(async () => {
    // Cleanup after each test
    if (memoryManager) {
      await memoryManager.clearMemories('test_user_123');
    }
  });

  describe('Memory Manager Core Functionality', () => {
    it('should initialize memory manager with correct configuration', async () => {
      expect(memoryManager).toBeDefined();
      expect(memoryManager.getStore()).toBeDefined();
      expect(memoryManager.getCheckpointer()).toBeDefined();
      expect(memoryManager.initialized).toBe(true);
    });

    it('should store and retrieve memories correctly', async () => {
      const userId = 'test_user_123';
      const testMemory = {
        content: 'User prefers dark theme',
        type: 'preference',
        context: 'ui_settings'
      };

      // Store memory
      const memoryIds = await memoryManager.addMemories(userId, testMemory, 'preferences');
      expect(memoryIds).toHaveLength(1);
      expect(memoryIds[0]).toMatch(/^[a-f0-9-]{36}$/); // UUID format

      // Retrieve memory
      const retrievedMemories = await memoryManager.searchMemories(userId, {
        context: 'preferences',
        limit: 5
      });

      expect(retrievedMemories).toHaveLength(1);
      expect(retrievedMemories[0].content).toBe(testMemory.content);
      expect(retrievedMemories[0].userId).toBe(userId);
    });

    it('should handle multiple memories and search filtering', async () => {
      const userId = 'test_user_123';
      const memories = [
        { content: 'Prefers anime characters', type: 'preference' },
        { content: 'Speaks English and Japanese', type: 'language' },
        { content: 'Last conversation about Naruto', type: 'conversation' }
      ];

      // Store multiple memories
      await memoryManager.addMemories(userId, memories[0], 'preferences');
      await memoryManager.addMemories(userId, memories[1], 'preferences');
      await memoryManager.addMemories(userId, memories[2], 'conversation');

      // Search by context
      const prefMemories = await memoryManager.searchMemories(userId, {
        context: 'preferences'
      });
      expect(prefMemories).toHaveLength(2);

      const convMemories = await memoryManager.searchMemories(userId, {
        context: 'conversation'
      });
      expect(convMemories).toHaveLength(1);
      expect(convMemories[0].content).toBe(memories[2].content);
    });

    it('should handle memory updates correctly', async () => {
      const userId = 'test_user_123';
      const originalMemory = {
        content: 'User likes action anime',
        type: 'preference'
      };

      // Store original memory
      const memoryIds = await memoryManager.addMemories(userId, originalMemory, 'preferences');
      const memoryId = memoryIds[0];

      // Update memory
      const updatedData = {
        content: 'User likes action and romance anime',
        type: 'preference',
        updated: true
      };

      const updateSuccess = await memoryManager.updateMemory(userId, memoryId, updatedData, 'preferences');
      expect(updateSuccess).toBe(true);

      // Verify update
      const retrievedMemories = await memoryManager.searchMemories(userId, {
        context: 'preferences'
      });

      expect(retrievedMemories).toHaveLength(1);
      expect(retrievedMemories[0].content).toBe(updatedData.content);
      expect(retrievedMemories[0].updated).toBe(true);
    });
  });

  describe('Character Analysis Memory Integration', () => {
    it('should store character analysis in memory', async () => {
      const characterData = {
        name: 'Naruto Uzumaki',
        anime: 'Naruto',
        description: 'Energetic ninja with dreams of becoming Hokage'
      };

      // Mock System 2 analysis
      vi.spyOn(characterService, 'invokeSystem2').mockResolvedValue(JSON.stringify({
        overview: 'Energetic and determined ninja',
        personality: 'Optimistic, never gives up',
        speakingStyle: 'Enthusiastic and direct',
        relationships: 'Strong bonds with teammates',
        guidelines: 'Embody determination and friendship'
      }));

      // Analyze character
      const analysis = await characterService.analyzeCharacter(characterData);

      expect(analysis).toBeDefined();
      expect(analysis.overview).toContain('ninja');

      // Verify storage in memory
      const characterMemories = await memoryManager.searchMemories('test_user_123', {
        context: 'character',
        filter: { characterId: 'naruto_uzumaki_naruto' }
      });

      expect(characterMemories).toHaveLength(1);
      expect(characterMemories[0].name).toBe(characterData.name);
      expect(characterMemories[0].anime).toBe(characterData.anime);
      expect(characterMemories[0].type).toBe('character_analysis');
    });

    it('should retrieve cached character analysis from memory', async () => {
      const characterData = {
        name: 'Sasuke Uchiha',
        anime: 'Naruto',
        description: 'Cool and talented ninja'
      };

      // Store pre-existing analysis in memory
      const existingAnalysis = {
        overview: 'Cool and talented shinobi',
        personality: 'Reserved and focused',
        speakingStyle: 'Minimal and precise'
      };

      await memoryManager.addMemories(
        'test_user_123',
        {
          content: existingAnalysis,
          characterId: 'sasuke_uchiha_naruto',
          type: 'character_analysis',
          name: characterData.name,
          anime: characterData.anime,
          metadata: {
            type: 'character_analysis',
            characterId: 'sasuke_uchiha_naruto'
          }
        },
        'character'
      );

      // Mock System 2 to ensure it's not called
      const system2Spy = vi.spyOn(characterService, 'invokeSystem2');

      // Analyze character (should use cached)
      const analysis = await characterService.analyzeCharacter(characterData);

      expect(analysis).toEqual(existingAnalysis);
      expect(system2Spy).not.toHaveBeenCalled(); // Should use cached version
    });
  });

  describe('DualBrain Context Memory Integration', () => {
    it('should store context data in dualbrain_context namespace', async () => {
      const userId = 'test_user_123';
      const contextData = {
        type: 'environmental',
        data: {
          audioActivity: true,
          vadSignal: 'speaking',
          interruptionRisk: 'high'
        },
        timestamp: Date.now(),
        source: 'dualbrain_coordinator'
      };

      // Store context
      await memoryManager.addMemories(userId, contextData, 'dualbrain_context');

      // Retrieve context
      const contextMemories = await memoryManager.searchMemories(userId, {
        context: 'dualbrain_context',
        limit: 5
      });

      expect(contextMemories).toHaveLength(1);
      expect(contextMemories[0].type).toBe('environmental');
      expect(contextMemories[0].data.audioActivity).toBe(true);
      expect(contextMemories[0].source).toBe('dualbrain_coordinator');
    });

    it('should handle multiple context types in dualbrain memory', async () => {
      const userId = 'test_user_123';
      const contexts = [
        {
          type: 'environmental',
          data: { audioActivity: true },
          source: 'agent_coordinator'
        },
        {
          type: 'conversation',
          data: { userMessage: 'Hello' },
          source: 'dualbrain_coordinator'
        },
        {
          type: 'visual',
          data: { engagement: 0.8 },
          source: 'mediapipe_provider'
        }
      ];

      // Store multiple contexts
      for (const context of contexts) {
        await memoryManager.addMemories(userId, context, 'dualbrain_context');
      }

      // Retrieve all contexts
      const allContexts = await memoryManager.searchMemories(userId, {
        context: 'dualbrain_context'
      });

      expect(allContexts).toHaveLength(3);

      // Verify each context type
      const environmentalContext = allContexts.find(c => c.type === 'environmental');
      const conversationContext = allContexts.find(c => c.type === 'conversation');
      const visualContext = allContexts.find(c => c.type === 'visual');

      expect(environmentalContext).toBeDefined();
      expect(conversationContext).toBeDefined();
      expect(visualContext).toBeDefined();

      expect(environmentalContext.data.audioActivity).toBe(true);
      expect(conversationContext.data.userMessage).toBe('Hello');
      expect(visualContext.data.engagement).toBe(0.8);
    });
  });

  describe('Memory Performance and Error Handling', () => {
    it('should handle memory storage failures gracefully', async () => {
      // Create a memory manager that will fail
      const failingMemoryManager = {
        addMemories: vi.fn().mockRejectedValue(new Error('Storage failed')),
        searchMemories: vi.fn().mockResolvedValue([])
      };

      const characterServiceWithFailingMemory = new CharacterAnalysisService({
        memoryManager: failingMemoryManager,
        userId: 'test_user_123'
      });

      const characterData = {
        name: 'Test Character',
        anime: 'Test Anime'
      };

      // Mock System 2 analysis
      vi.spyOn(characterServiceWithFailingMemory, 'invokeSystem2').mockResolvedValue(JSON.stringify({
        overview: 'Test analysis'
      }));

      // Should not throw even if memory storage fails
      const analysis = await characterServiceWithFailingMemory.analyzeCharacter(characterData);
      expect(analysis).toBeDefined();
      expect(analysis.overview).toBe('Test analysis');
    });

    it('should handle memory retrieval failures gracefully', async () => {
      const userId = 'test_user_123';

      // Mock memory manager to fail on search
      vi.spyOn(memoryManager, 'searchMemories').mockRejectedValueOnce(new Error('Search failed'));

      // Should not throw and should return empty array
      const memories = await memoryManager.searchMemories(userId, { context: 'test' });
      expect(memories).toEqual([]);
    });

    it('should maintain performance with large memory datasets', async () => {
      const userId = 'test_user_123';
      const startTime = Date.now();

      // Store many memories
      const promises = [];
      for (let i = 0; i < 100; i++) {
        promises.push(
          memoryManager.addMemories(userId, {
            content: `Memory content ${i}`,
            index: i,
            type: 'performance_test'
          }, 'performance')
        );
      }

      await Promise.all(promises);

      // Search through large dataset
      const searchStart = Date.now();
      const results = await memoryManager.searchMemories(userId, {
        context: 'performance',
        limit: 10
      });
      const searchTime = Date.now() - searchStart;

      expect(results).toHaveLength(10);
      expect(searchTime).toBeLessThan(1000); // Should complete within 1 second

      const totalTime = Date.now() - startTime;
      logger.info(`Performance test completed in ${totalTime}ms (search: ${searchTime}ms)`);
    });
  });

  describe('Memory Statistics and Management', () => {
    it('should provide accurate memory statistics', async () => {
      const userId = 'test_user_123';

      // Add various types of memories
      await memoryManager.addMemories(userId, { content: 'Pref 1' }, 'preferences');
      await memoryManager.addMemories(userId, { content: 'Pref 2' }, 'preferences');
      await memoryManager.addMemories(userId, { content: 'Conv 1' }, 'conversation');
      await memoryManager.addMemories(userId, { content: 'Char 1' }, 'character');

      const stats = await memoryManager.getMemoryStats(userId);

      expect(stats).toBeDefined();
      expect(stats.totalMemories).toBe(4);
      expect(stats.contextBreakdown).toBeDefined();
      expect(stats.contextBreakdown.preferences).toBe(2);
      expect(stats.contextBreakdown.conversation).toBe(1);
      expect(stats.contextBreakdown.character).toBe(1);
    });

    it('should handle memory cleanup operations', async () => {
      const userId = 'test_user_123';

      // Add test memories
      await memoryManager.addMemories(userId, { content: 'Test 1' }, 'test');
      await memoryManager.addMemories(userId, { content: 'Test 2' }, 'test');

      // Verify memories exist
      let memories = await memoryManager.searchMemories(userId, { context: 'test' });
      expect(memories).toHaveLength(2);

      // Clear memories
      const clearResult = await memoryManager.clearMemories(userId, 'test');
      expect(clearResult).toBe(true);

      // Verify memories are cleared
      memories = await memoryManager.searchMemories(userId, { context: 'test' });
      expect(memories).toHaveLength(0);
    });
  });
});