/**
 * Contextual Bridge Tests
 * 
 * Tests for the ContextualBridge class including:
 * - Context management and updates
 * - Context aging and staleness
 * - Change significance calculation
 * - Callback handling
 * - Context analysis utilities
 */

import { vi, expect, describe, test, beforeEach, afterEach } from 'vitest';
import { ContextualBridge, ContextAnalysisUtils } from '../../../../../src/agent/models/base/ContextualBridge.js';

describe('ContextualBridge', () => {
    let bridge;

    beforeEach(() => {
        bridge = new ContextualBridge({
            maxContextAge: 5000,
            contextUpdateThreshold: 0.2,
            maxContextHistory: 10
        });
    });

    afterEach(() => {
        bridge.cleanup();
    });

    describe('Initialization', () => {
        test('should initialize with default configuration', () => {
            const defaultBridge = new ContextualBridge();
            
            expect(defaultBridge.config.maxContextAge).toBe(10000);
            expect(defaultBridge.config.contextUpdateThreshold).toBe(0.1);
            expect(defaultBridge.config.maxContextHistory).toBe(20);
        });

        test('should initialize with custom configuration', () => {
            expect(bridge.config.maxContextAge).toBe(5000);
            expect(bridge.config.contextUpdateThreshold).toBe(0.2);
            expect(bridge.config.maxContextHistory).toBe(10);
        });

        test('should initialize empty context storage', () => {
            expect(bridge.context.audio).toBeNull();
            expect(bridge.context.visual).toBeNull();
            expect(bridge.context.updateHistory).toEqual([]);
        });
    });

    describe('Context Updates', () => {
        test('should update context with timestamp', () => {
            const contextData = { volume: 0.5, quality: 'good' };
            
            bridge.updateContext('audio', contextData);
            
            expect(bridge.context.audio.volume).toBe(0.5);
            expect(bridge.context.audio.quality).toBe('good');
            expect(bridge.context.audio.timestamp).toBeDefined();
            expect(bridge.context.audio.type).toBe('audio');
            expect(bridge.context.lastUpdate).toBeDefined();
        });

        test('should track update history', () => {
            bridge.updateContext('audio', { volume: 0.3 });
            bridge.updateContext('visual', { engagement: 0.8 });
            bridge.updateContext('audio', { volume: 0.7 });

            expect(bridge.context.updateHistory).toHaveLength(3);
            expect(bridge.context.updateHistory[0].type).toBe('audio');
            expect(bridge.context.updateHistory[1].type).toBe('visual');
            expect(bridge.context.updateHistory[2].type).toBe('audio');
        });

        test('should trim update history when exceeding limit', () => {
            // Add more updates than the limit allows
            for (let i = 0; i < 60; i++) {
                bridge.updateContext('audio', { volume: i / 100 });
            }

            expect(bridge.context.updateHistory.length).toBe(50);
        });

        test('should calculate change significance', () => {
            bridge.updateContext('audio', { volume: 0.1 });
            bridge.updateContext('audio', { volume: 0.9 }); // Significant change

            const history = bridge.context.updateHistory;
            const lastUpdate = history[history.length - 1];
            expect(lastUpdate.changeSignificance).toBeGreaterThan(0.2);
        });
    });

    describe('Context Enrichment', () => {
        test('should provide enriched context with freshness indicators', () => {
            bridge.updateContext('audio', { volume: 0.5 });
            bridge.updateContext('visual', { engagement: 0.7 });

            const enriched = bridge.getEnrichedContext();

            expect(enriched.audio.volume).toBe(0.5);
            expect(enriched.audio.isFresh).toBe(true);
            expect(enriched.audio.age).toBeLessThan(100);
            expect(enriched.audio.staleness).toBeLessThan(0.1);

            expect(enriched.visual.engagement).toBe(0.7);
            expect(enriched.visual.isFresh).toBe(true);
        });

        test('should mark old context as stale', async () => {
            bridge.updateContext('audio', { volume: 0.5 });
            
            // Wait longer than maxContextAge
            await new Promise(resolve => setTimeout(resolve, 100));

            const enriched = bridge.getEnrichedContext();
            expect(enriched.audio.age).toBeGreaterThan(0);
            expect(enriched.audio.staleness).toBeGreaterThan(0);
        });

        test('should provide context summary', () => {
            bridge.updateContext('audio', { volume: 0.5 });
            bridge.updateContext('visual', { engagement: 0.7 });

            const enriched = bridge.getEnrichedContext();

            expect(enriched.summary.lastUpdate).toBeDefined();
            expect(enriched.summary.hasRecentContext).toBe(true);
            expect(enriched.summary.contextTypes).toContain('audio');
            expect(enriched.summary.contextTypes).toContain('visual');
            expect(enriched.summary.recentUpdates).toHaveLength(2);
        });
    });

    describe('Callback Management', () => {
        test('should register and trigger context update callbacks', () => {
            const callback = vi.fn();
            bridge.onContextUpdate(callback);

            const contextData = { volume: 0.5 };
            bridge.updateContext('audio', contextData);

            expect(callback).toHaveBeenCalledWith(
                'audio',
                expect.objectContaining(contextData),
                expect.any(Object)
            );
        });

        test('should register and trigger significant change callbacks', () => {
            const callback = vi.fn();
            bridge.onSignificantChange(callback);

            // Establish baseline
            bridge.updateContext('audio', { volume: 0.1 });
            callback.mockClear();

            // Make significant change
            bridge.updateContext('audio', { volume: 0.9 });

            expect(callback).toHaveBeenCalled();
        });

        test('should not trigger significant change for small changes', () => {
            const callback = vi.fn();
            bridge.onSignificantChange(callback);

            bridge.updateContext('audio', { volume: 0.5 });
            callback.mockClear();

            // Small change below threshold
            bridge.updateContext('audio', { volume: 0.51 });

            expect(callback).not.toHaveBeenCalled();
        });

        test('should remove callbacks', () => {
            const callback1 = vi.fn();
            const callback2 = vi.fn();

            bridge.onContextUpdate(callback1);
            bridge.onContextUpdate(callback2);
            bridge.removeCallback(callback1);

            bridge.updateContext('audio', { volume: 0.5 });

            expect(callback1).not.toHaveBeenCalled();
            expect(callback2).toHaveBeenCalled();
        });

        test('should handle callback errors gracefully', () => {
            const errorCallback = vi.fn(() => {
                throw new Error('Callback error');
            });
            const normalCallback = vi.fn();

            bridge.onContextUpdate(errorCallback);
            bridge.onContextUpdate(normalCallback);

            expect(() => {
                bridge.updateContext('audio', { volume: 0.5 });
            }).not.toThrow();

            expect(normalCallback).toHaveBeenCalled();
        });
    });

    describe('Context Cleanup', () => {
        test('should cleanup stale context data', () => {
            const shortAgeBridge = new ContextualBridge({ 
                maxContextAge: 50 
            });

            shortAgeBridge.updateContext('audio', { volume: 0.5 });
            
            setTimeout(() => {
                shortAgeBridge.cleanup();
                expect(shortAgeBridge.context.audio).toBeNull();
            }, 150);
        });

        test('should not cleanup fresh context data', () => {
            bridge.updateContext('audio', { volume: 0.5 });
            bridge.cleanup();

            expect(bridge.context.audio).not.toBeNull();
        });
    });
});

describe('ContextAnalysisUtils', () => {
    describe('Audio Context Analysis', () => {
        test('should analyze active audio context', () => {
            const now = Date.now();
            const analysis = ContextAnalysisUtils.analyzeAudioContext(
                {},
                now - 1000 // 1 second ago
            );

            expect(analysis.activityLevel).toBe('high');
            expect(analysis.quality).toBe('active');
            expect(analysis.volume).toBe(0.7);
            expect(analysis.sentiment).toBe('engaged');
        });

        test('should analyze recent audio context', () => {
            const now = Date.now();
            const analysis = ContextAnalysisUtils.analyzeAudioContext(
                {},
                now - 5000 // 5 seconds ago
            );

            expect(analysis.activityLevel).toBe('medium');
            expect(analysis.quality).toBe('recent');
            expect(analysis.volume).toBe(0.3);
        });

        test('should analyze idle audio context', () => {
            const now = Date.now();
            const analysis = ContextAnalysisUtils.analyzeAudioContext(
                {},
                now - 15000 // 15 seconds ago
            );

            expect(analysis.activityLevel).toBe('low');
            expect(analysis.quality).toBe('idle');
            expect(analysis.volume).toBe(0);
        });

        test('should handle null lastAudioTime', () => {
            const analysis = ContextAnalysisUtils.analyzeAudioContext({}, null);

            expect(analysis.activityLevel).toBe('low');
            expect(analysis.quality).toBe('idle');
            expect(analysis.lastActivity).toBeNull();
        });
    });

    describe('Conversation Context Analysis', () => {
        test('should analyze empty transcript history', () => {
            const analysis = ContextAnalysisUtils.analyzeConversationContext([]);

            expect(analysis.topicFlow).toBe('stable');
            expect(analysis.userInterest).toBe(0.5);
            expect(analysis.responsePattern).toBe('normal');
            expect(analysis.transcriptCount).toBe(0);
        });

        test('should analyze recent conversation', () => {
            const transcripts = [
                { text: 'Hello', timestamp: Date.now() - 1000 },
                { text: 'How are you?', timestamp: Date.now() - 500 }
            ];

            const analysis = ContextAnalysisUtils.analyzeConversationContext(transcripts);

            expect(analysis.topicFlow).toBe('active');
            expect(analysis.responsePattern).toBe('responsive');
            expect(analysis.transcriptCount).toBe(2);
        });

        test('should detect delayed response pattern', () => {
            const transcripts = [
                { text: 'Hello', timestamp: Date.now() - 10000 }
            ];

            const analysis = ContextAnalysisUtils.analyzeConversationContext(transcripts);

            expect(analysis.responsePattern).toBe('delayed');
        });
    });

    describe('User Interest Calculation', () => {
        test('should calculate user interest from transcripts', () => {
            const transcripts = [
                { text: 'This is a short message', timestamp: Date.now() - 1000 },
                { text: 'This is a much longer message with more content and detail', timestamp: Date.now() - 500 }
            ];

            const interest = ContextAnalysisUtils.calculateUserInterest(transcripts);

            expect(interest).toBeGreaterThan(0.5);
            expect(interest).toBeLessThanOrEqual(1);
        });

        test('should return default interest for empty transcripts', () => {
            const interest = ContextAnalysisUtils.calculateUserInterest([]);
            expect(interest).toBe(0.5);
        });
    });

    describe('Trend Calculation', () => {
        test('should calculate positive trend', () => {
            const values = [0.2, 0.4, 0.6, 0.8];
            const trend = ContextAnalysisUtils.calculateTrend(values);

            expect(trend).toBeGreaterThan(0);
        });

        test('should calculate negative trend', () => {
            const values = [0.8, 0.6, 0.4, 0.2];
            const trend = ContextAnalysisUtils.calculateTrend(values);

            expect(trend).toBeLessThan(0);
        });

        test('should return zero for insufficient data', () => {
            const trend = ContextAnalysisUtils.calculateTrend([0.5]);
            expect(trend).toBe(0);
        });
    });

    describe('Context Trends Calculation', () => {
        test('should calculate trends from context history', () => {
            const contextHistory = [
                { conversation: { userInterest: 0.3 }, audio: { activityLevel: 'low' } },
                { conversation: { userInterest: 0.5 }, audio: { activityLevel: 'medium' } },
                { conversation: { userInterest: 0.7 }, audio: { activityLevel: 'high' } }
            ];

            const trends = ContextAnalysisUtils.calculateContextTrends(contextHistory);

            expect(trends.engagement).toBe('increasing');
            expect(trends.activity).toBe('increasing');
            expect(trends.trend).toBeGreaterThan(0);
        });

        test('should return stable trends for insufficient data', () => {
            const contextHistory = [
                { conversation: { userInterest: 0.5 }, audio: { activityLevel: 'medium' } }
            ];

            const trends = ContextAnalysisUtils.calculateContextTrends(contextHistory);

            expect(trends.engagement).toBe('stable');
            expect(trends.activity).toBe('stable');
            expect(trends.trend).toBe('stable');
        });

        test('should detect decreasing trends', () => {
            const contextHistory = [
                { conversation: { userInterest: 0.8 }, audio: { activityLevel: 'high' } },
                { conversation: { userInterest: 0.5 }, audio: { activityLevel: 'medium' } },
                { conversation: { userInterest: 0.2 }, audio: { activityLevel: 'low' } }
            ];

            const trends = ContextAnalysisUtils.calculateContextTrends(contextHistory);

            expect(trends.engagement).toBe('decreasing');
            expect(trends.activity).toBe('decreasing');
            expect(trends.trend).toBeLessThan(0);
        });
    });
});