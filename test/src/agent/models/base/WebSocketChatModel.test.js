/**
 * Comprehensive Unit Tests for WebSocketChatModel
 * Tests the WebSocket-specific functionality built on top of BaseChatModel
 * 
 * Coverage Areas:
 * - WebSocket connection management
 * - Session coordination and state management
 * - Audio streaming capabilities
 * - Provider-specific message routing
 * - Connection recovery and retry logic
 * - Performance monitoring for WebSocket operations
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { WebSocketChatModel } from '@/agent/models/base/WebSocketChatModel.js';
import { StreamingState } from '@/agent/models/base/BaseChatModel.js';

// Mock WebSocket for testing
class MockWebSocket {
    constructor(url, options) {
        this.url = url;
        this.options = options;
        this.readyState = 0; // CONNECTING
        this.listeners = {};
        
        // Simulate connection after short delay
        setTimeout(() => {
            this.readyState = 1; // OPEN
            if (this.onopen) this.onopen({ type: 'open' });
        }, 10);
    }

    addEventListener(event, handler) {
        if (!this.listeners[event]) this.listeners[event] = [];
        this.listeners[event].push(handler);
    }

    removeEventListener(event, handler) {
        if (this.listeners[event]) {
            const index = this.listeners[event].indexOf(handler);
            if (index > -1) this.listeners[event].splice(index, 1);
        }
    }

    send(data) {
        if (this.readyState !== 1) {
            throw new Error('WebSocket is not open');
        }
        // Simulate successful send
        return true;
    }

    close(code, reason) {
        this.readyState = 3; // CLOSED
        if (this.onclose) {
            this.onclose({ 
                type: 'close', 
                code: code || 1000, 
                reason: reason || 'Normal closure',
                wasClean: true 
            });
        }
    }
}

// Test implementation of WebSocketChatModel
class TestWebSocketModel extends WebSocketChatModel {
    constructor(options = {}) {
        super({
            ...options,
            provider: 'test-websocket',
            apiMode: 'websocket'
        });
    }

    buildWebSocketUrl() {
        return `ws://test.example.com/ws?api_key=${this.apiKey}&model=${this.model}`;
    }

    getConnectionOptions() {
        return {
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'User-Agent': 'test-websocket-client/1.0'
            }
        };
    }

    processProviderMessage(messageData) {
        // Simple echo processing for testing
        return {
            processed: true,
            type: messageData.type || 'unknown',
            data: messageData.data
        };
    }

    async initializeSession(sessionConfig = {}) {
        // Mock session initialization
        await new Promise(resolve => setTimeout(resolve, 50));
        this._setState(StreamingState.SESSION_READY);
        this.sessionStabilized = true;
        return true;
    }

    async _sendAudioToProvider(audioData, format) {
        if (!this.isSessionReady()) return false;
        
        // Mock sending audio
        const message = {
            type: 'audio_data',
            data: audioData,
            format: format
        };
        
        if (this.socket && this.socket.readyState === 1) {
            this.socket.send(JSON.stringify(message));
            this.stats.audioChunksSent++;
            return true;
        }
        return false;
    }

    // Override WebSocket class for testing
    async _getWebSocketClass() {
        return MockWebSocket;
    }
}

describe('WebSocketChatModel', () => {
    let model;

    beforeEach(() => {
        global.WebSocket = MockWebSocket;
        
        model = new TestWebSocketModel({
            apiKey: 'test-ws-key',
            model: 'test-ws-model',
            timeout: 5000
        });
    });

    afterEach(() => {
        vi.clearAllMocks();
        if (model.socket) {
            model.socket.close();
        }
    });

    describe('WebSocket Connection Management', () => {
        it('should build WebSocket URL correctly', () => {
            const url = model.buildWebSocketUrl();
            expect(url).toBe('ws://test.example.com/ws?api_key=test-ws-key&model=test-ws-model');
        });

        it('should provide connection options', () => {
            const options = model.getConnectionOptions();
            expect(options).toMatchObject({
                headers: {
                    'Authorization': 'Bearer test-ws-key',
                    'User-Agent': 'test-websocket-client/1.0'
                }
            });
        });

        it('should establish WebSocket connection', async () => {
            const connected = await model.connect();
            
            expect(connected).toBe(true);
            expect(model.state).toBe(StreamingState.SESSION_READY);
            expect(model.socket).toBeDefined();
            expect(model.socket.url).toContain('test.example.com');
        });

        it('should handle connection errors gracefully', async () => {
            // Override to simulate connection failure
            model._getWebSocketClass = async () => {
                return class FailingWebSocket {
                    constructor() {
                        setTimeout(() => {
                            throw new Error('Connection failed');
                        }, 10);
                    }
                };
            };

            const connected = await model.connect();
            expect(connected).toBe(false);
            expect(model.state).toBe(StreamingState.ERROR);
        });

        it('should prevent multiple concurrent connections', async () => {
            const promise1 = model.connect();
            const promise2 = model.connect();
            
            const [result1, result2] = await Promise.all([promise1, promise2]);
            
            expect(result1).toBe(true);
            expect(result2).toBe(false); // Should be prevented
            expect(model.stats.connectionsAttempted).toBe(1);
        });
    });

    describe('Session Management', () => {
        beforeEach(async () => {
            await model.connect();
        });

        it('should initialize session after connection', () => {
            expect(model.isSessionReady()).toBe(true);
            expect(model.sessionStabilized).toBe(true);
            expect(model.state).toBe(StreamingState.SESSION_READY);
        });

        it('should handle session timeout', async () => {
            // Create a model that won't stabilize
            const slowModel = new TestWebSocketModel({ apiKey: 'slow-key' });
            slowModel.initializeSession = () => new Promise(() => {}); // Never resolves
            
            const sessionReady = await slowModel.waitForRealtimeReady(100); // Short timeout
            expect(sessionReady).toBe(false);
        });

        it('should provide session readiness check', () => {
            expect(model.isSessionReady()).toBe(true);
            
            model._setState(StreamingState.CONNECTED); // Not session ready
            expect(model.isSessionReady()).toBe(false);
            
            model.sessionStabilized = false;
            expect(model.isSessionReady()).toBe(false);
        });
    });

    describe('Message Processing', () => {
        beforeEach(async () => {
            await model.connect();
        });

        it('should process incoming messages', () => {
            const testMessage = {
                type: 'test_event',
                data: { content: 'Hello from server' }
            };

            const result = model.processProviderMessage(testMessage);
            
            expect(result).toMatchObject({
                processed: true,
                type: 'test_event',
                data: { content: 'Hello from server' }
            });
        });

        it('should handle WebSocket message events', () => {
            const messageHandler = vi.fn();
            model.on('message', messageHandler);

            // Simulate incoming WebSocket message
            const mockEvent = {
                data: JSON.stringify({
                    type: 'server_message',
                    content: 'Hello client'
                })
            };

            model._handleMessage(mockEvent);
            
            expect(messageHandler).toHaveBeenCalled();
            expect(model.stats.messagesReceived).toBe(1);
        });

        it('should handle binary message events', async () => {
            const messageHandler = vi.fn();
            model.on('message', messageHandler);

            // Create a Blob with JSON data
            const jsonData = JSON.stringify({ type: 'binary_message', data: 'test' });
            const blob = new Blob([jsonData], { type: 'application/json' });

            const mockEvent = { data: blob };
            
            // Simulate processing binary message
            model._handleMessage(mockEvent);
            
            // Wait for blob processing
            await new Promise(resolve => setTimeout(resolve, 10));
            
            expect(model.stats.messagesReceived).toBe(1);
        });
    });

    describe('Audio Streaming', () => {
        beforeEach(async () => {
            await model.connect();
        });

        it('should send audio data successfully', async () => {
            const audioData = 'base64-encoded-audio-data';
            const result = await model.sendAudio(audioData, 'base64');
            
            expect(result).toBe(true);
            expect(model.stats.audioChunksSent).toBe(1);
        });

        it('should reject audio when session not ready', async () => {
            model._setState(StreamingState.CONNECTED); // Not session ready
            
            const result = await model.sendAudio('audio-data');
            expect(result).toBe(false);
        });

        it('should handle rate limiting for audio', async () => {
            model.config.rateLimiting = {
                enabled: true,
                maxChunksPerSecond: 2,
                minIntervalMs: 500
            };

            // Send multiple audio chunks rapidly
            const results = await Promise.all([
                model.sendAudio('chunk1'),
                model.sendAudio('chunk2'),
                model.sendAudio('chunk3')
            ]);

            expect(results[0]).toBe(true);
            expect(results[1]).toBe(true); 
            expect(results[2]).toBe(true);
            expect(model.audioQueue.length).toBeGreaterThanOrEqual(0);
        });

        it('should process audio queue with rate limiting', async () => {
            model.config.rateLimiting.enabled = true;
            model.config.rateLimiting.minIntervalMs = 100;

            // Add items to queue
            model.audioQueue.push(
                { audioData: 'chunk1', format: 'base64', timestamp: Date.now() },
                { audioData: 'chunk2', format: 'base64', timestamp: Date.now() }
            );

            await model._processAudioQueue();
            
            expect(model.audioQueue.length).toBe(0);
            expect(model.stats.audioChunksSent).toBeGreaterThanOrEqual(2);
        });
    });

    describe('Connection Recovery', () => {
        beforeEach(async () => {
            await model.connect();
        });

        it('should handle connection close events', () => {
            const closeHandler = vi.fn();
            model.on('close', closeHandler);

            // Simulate connection close
            model._handleClose({
                code: 1000,
                reason: 'Normal closure',
                wasClean: true
            });

            expect(model.state).toBe(StreamingState.DISCONNECTED);
            expect(closeHandler).toHaveBeenCalled();
        });

        it('should attempt reconnection on unexpected close', async () => {
            const reconnectSpy = vi.spyOn(model, '_attemptReconnect');
            
            // Simulate unexpected close (not normal closure)
            model._handleClose({
                code: 1006,
                reason: 'Connection lost',
                wasClean: false
            });

            expect(reconnectSpy).toHaveBeenCalled();
        });

        it('should not reconnect on authentication failures', () => {
            const reconnectSpy = vi.spyOn(model, '_attemptReconnect');
            
            // Simulate authentication failure
            model._handleClose({
                code: 4001,
                reason: 'Authentication failed',
                wasClean: false
            });

            expect(reconnectSpy).not.toHaveBeenCalled();
        });

        it('should respect max reconnection attempts', () => {
            model.config.maxReconnectAttempts = 2;
            model.reconnectAttempts = 3;

            const shouldReconnect = model._shouldAutoReconnect(1006);
            expect(shouldReconnect).toBe(false);
        });
    });

    describe('Performance Monitoring', () => {
        beforeEach(async () => {
            await model.connect();
        });

        it('should track WebSocket-specific metrics', () => {
            model.stats.connectionsAttempted = 3;
            model.stats.connectionsSuccessful = 2;
            model.stats.messagesReceived = 10;
            model.stats.audioChunksSent = 5;

            const metrics = model.getMetrics();

            expect(metrics.streaming).toMatchObject({
                connectionsAttempted: 3,
                connectionsSuccessful: 2,
                messagesReceived: 10,
                audioChunksSent: 5,
                currentState: StreamingState.SESSION_READY,
                successRate: (2/3) * 100
            });
        });

        it('should handle connection timeouts', () => {
            model.connectionTimeout = setTimeout(() => {}, 1000);
            
            model._handleConnectionTimeout();
            
            expect(model.state).toBe(StreamingState.ERROR);
            expect(model.connectionTimeout).toBe(null);
        });

        it('should clean up timers and resources', async () => {
            model.connectionTimeout = setTimeout(() => {}, 1000);
            model.audioQueue = [{ audioData: 'test' }];
            
            await model.disconnect();
            
            expect(model.connectionTimeout).toBe(null);
            expect(model.audioQueue).toHaveLength(0);
            expect(model.state).toBe(StreamingState.DISCONNECTED);
        });
    });

    describe('Event System', () => {
        it('should add and remove event listeners', () => {
            const testHandler = vi.fn();
            
            model.on('testEvent', testHandler);
            expect(model.eventHandlers.testEvent).toContain(testHandler);
            
            model.off('testEvent', testHandler);
            expect(model.eventHandlers.testEvent).not.toContain(testHandler);
        });

        it('should handle unknown event types gracefully', () => {
            const consoleSpy = vi.spyOn(model.logger, 'warn');
            
            model.on('unknownEvent', () => {});
            
            expect(consoleSpy).toHaveBeenCalledWith(
                expect.stringContaining('Unknown event type')
            );
        });

        it('should emit state change events', () => {
            const stateHandler = vi.fn();
            model.on('stateChange', stateHandler);

            model._setState(StreamingState.CONNECTING);
            
            expect(stateHandler).toHaveBeenCalledWith(
                StreamingState.CONNECTING,
                StreamingState.SESSION_READY
            );
        });
    });

    describe('Cleanup and Disconnect', () => {
        beforeEach(async () => {
            await model.connect();
        });

        it('should disconnect gracefully', async () => {
            expect(model.isConnected()).toBe(true);
            
            await model.disconnect();
            
            expect(model.socket).toBe(null);
            expect(model.state).toBe(StreamingState.DISCONNECTED);
            expect(model.sessionStabilized).toBe(false);
        });

        it('should handle disconnect when already disconnected', async () => {
            await model.disconnect(); // First disconnect
            
            // Should not throw on second disconnect
            await expect(model.disconnect()).resolves.not.toThrow();
        });

        it('should cleanup all resources', async () => {
            model.audioQueue = [{ audioData: 'test' }];
            model.connectionTimeout = setTimeout(() => {}, 1000);
            
            await model.cleanup();
            
            expect(model.audioQueue).toHaveLength(0);
            expect(model.connectionTimeout).toBe(null);
        });
    });

    describe('Browser vs Node.js Environment', () => {
        it('should detect browser environment correctly', () => {
            // Mock browser environment
            global.window = { WebSocket: MockWebSocket };
            
            const strategy = model._determineConnectionStrategy();
            expect(['browser', 'browser-direct']).toContain(strategy);
            
            delete global.window;
        });

        it('should detect Node.js environment correctly', () => {
            // Ensure no window object
            delete global.window;
            
            const strategy = model._determineConnectionStrategy();
            expect(strategy).toBe('node');
        });

        it('should handle missing WebSocket in Node.js gracefully', async () => {
            model._getWebSocketClass = async () => {
                throw new Error('WebSocket not available. Install ws package for Node.js environments.');
            };

            await expect(model.connect()).rejects.toThrow('WebSocket not available');
        });
    });
});