/**
 * Dual Brain Chat Model Tests
 * 
 * Tests for the DualBrainChatModel base class including:
 * - Contextual bridge integration
 * - Continuous context analysis
 * - Transcript management
 * - Context enrichment
 */

import { vi, expect, describe, test, beforeEach, afterEach } from 'vitest';
import { DualBrainChatModel } from '../../../../../src/agent/models/base/DualBrainChatModel.js';
import { ContextualBridge } from '../../../../../src/agent/models/base/ContextualBridge.js';

describe('DualBrainChatModel', () => {
    let model;
    let mockOptions;

    beforeEach(() => {
        mockOptions = {
            apiKey: 'test-key',
            model: 'test-model',
            timeout: 500,
            contextualBridge: {
                maxContextAge: 5000,
                contextUpdateThreshold: 0.1
            }
        };
        
        model = new DualBrainChatModel(mockOptions);
    });

    afterEach(async () => {
        if (model) {
            await model.cleanup();
        }
    });

    describe('Initialization', () => {
        test('should initialize with dual brain capabilities', () => {
            expect(model).toBeDefined();
            expect(model.contextualBridge).toBeInstanceOf(ContextualBridge);
            expect(model.contextBuffer).toBeDefined();
            expect(model.recentTranscripts).toEqual([]);
            expect(model.contextHistory).toEqual([]);
        });

        test('should initialize context buffer with default values', () => {
            expect(model.contextBuffer.audio).toEqual({
                volume: 0,
                quality: 'idle',
                sentiment: 'neutral',
                lastActivity: null
            });
            expect(model.contextBuffer.visual).toEqual({
                engagement: 0.5,
                attention: 0.5,
                posture: 'neutral'
            });
            expect(model.contextBuffer.conversation).toEqual({
                topicFlow: 'stable',
                userInterest: 0.5,
                responsePattern: 'normal'
            });
        });
    });

    describe('Context Update Callback', () => {
        test('should set context update callback and start continuous analysis', () => {
            const callback = vi.fn();
            model.setContextUpdateCallback(callback);

            expect(model.contextUpdateCallback).toBe(callback);
            expect(model.contextAnalysisTimer).toBeDefined();
        });

        test('should stop existing timer when setting new callback', () => {
            const callback1 = vi.fn();
            const callback2 = vi.fn();

            model.setContextUpdateCallback(callback1);
            const firstTimer = model.contextAnalysisTimer;

            model.setContextUpdateCallback(callback2);
            const secondTimer = model.contextAnalysisTimer;

            expect(firstTimer).not.toBe(secondTimer);
            expect(model.contextUpdateCallback).toBe(callback2);
        });
    });

    describe('Continuous Context Analysis', () => {
        test('should update context buffer during continuous analysis', async () => {
            const callback = vi.fn();
            model.setContextUpdateCallback(callback);

            // Wait for at least one context update
            await new Promise(resolve => setTimeout(resolve, 1100));

            expect(callback).toHaveBeenCalled();
            expect(model.contextHistory).toHaveLength(1);
        });

        test('should maintain context history with maximum length', async () => {
            model.maxContextHistory = 3;
            const callback = vi.fn();
            model.setContextUpdateCallback(callback);

            // Wait for multiple context updates
            await new Promise(resolve => setTimeout(resolve, 3500));

            expect(model.contextHistory.length).toBeLessThanOrEqual(3);
        });
    });

    describe('Transcript Management', () => {
        test('should add transcript with metadata', () => {
            const transcript = 'Hello, how are you?';
            const metadata = { source: 'user', confidence: 0.9 };

            model.addTranscript(transcript, metadata);

            expect(model.recentTranscripts).toHaveLength(1);
            expect(model.recentTranscripts[0].text).toBe(transcript);
            expect(model.recentTranscripts[0].source).toBe('user');
            expect(model.recentTranscripts[0].confidence).toBe(0.9);
            expect(model.recentTranscripts[0].timestamp).toBeDefined();
        });

        test('should maintain transcript history with maximum length', () => {
            model.maxTranscriptHistory = 3;

            for (let i = 0; i < 5; i++) {
                model.addTranscript(`Transcript ${i}`);
            }

            expect(model.recentTranscripts).toHaveLength(3);
            expect(model.recentTranscripts[0].text).toBe('Transcript 2');
            expect(model.recentTranscripts[2].text).toBe('Transcript 4');
        });
    });

    describe('Context Enrichment', () => {
        test('should provide enriched context from contextual bridge', () => {
            model.contextualBridge.updateContext('audio', { volume: 0.7 });
            model.contextualBridge.updateContext('visual', { engagement: 0.8 });

            const enrichedContext = model.getEnrichedContext();

            expect(enrichedContext.audio).toBeDefined();
            expect(enrichedContext.audio.volume).toBe(0.7);
            expect(enrichedContext.visual).toBeDefined();
            expect(enrichedContext.visual.engagement).toBe(0.8);
        });

        test('should provide fallback context when bridge is unavailable', () => {
            model.contextualBridge = null;

            const enrichedContext = model.getEnrichedContext();

            expect(enrichedContext.audio).toBe(model.contextBuffer.audio);
            expect(enrichedContext.visual).toBe(model.contextBuffer.visual);
            expect(enrichedContext.summary).toBeDefined();
            expect(enrichedContext.summary.hasRecentContext).toBe(true);
        });
    });

    describe('Context Updates', () => {
        test('should update visual context', () => {
            const visualData = { engagement: 0.9, attention: 0.8 };
            model.updateVisualContext(visualData);

            expect(model.contextBuffer.visual.engagement).toBe(0.9);
            expect(model.contextBuffer.visual.attention).toBe(0.8);
            expect(model.contextBuffer.visual.lastUpdate).toBeDefined();
        });

        test('should update environmental context', () => {
            const environmentalData = { backgroundNoise: 'medium', interruptionRisk: 'high' };
            model.updateEnvironmentalContext(environmentalData);

            expect(model.contextBuffer.environmental.backgroundNoise).toBe('medium');
            expect(model.contextBuffer.environmental.interruptionRisk).toBe('high');
            expect(model.contextBuffer.environmental.lastUpdate).toBeDefined();
        });
    });

    describe('Context Callbacks', () => {
        test('should register context update callback', () => {
            const callback = vi.fn();
            model.onContextUpdate(callback);

            // Verify callback was registered with the contextual bridge
            expect(model.contextualBridge.contextUpdateCallbacks.has(callback)).toBe(true);
        });

        test('should register significant context change callback', () => {
            const callback = vi.fn();
            model.onSignificantContextChange(callback);

            // Verify callback was registered with the contextual bridge
            expect(model.contextualBridge.significantChangeCallbacks.has(callback)).toBe(true);
        });

        test('should remove context callbacks', () => {
            const callback = vi.fn();
            model.onContextUpdate(callback);
            model.onSignificantContextChange(callback);

            model.removeContextCallback(callback);

            expect(model.contextualBridge.contextUpdateCallbacks.has(callback)).toBe(false);
            expect(model.contextualBridge.significantChangeCallbacks.has(callback)).toBe(false);
        });
    });

    describe('Context Trends', () => {
        test('should calculate context trends', () => {
            // Add some context history
            model.contextHistory = [
                { conversation: { userInterest: 0.3 }, audio: { activityLevel: 'low' } },
                { conversation: { userInterest: 0.5 }, audio: { activityLevel: 'medium' } },
                { conversation: { userInterest: 0.7 }, audio: { activityLevel: 'high' } }
            ];

            const trends = model.getContextTrends();

            expect(trends).toBeDefined();
            expect(trends.engagement).toBe('increasing');
            expect(trends.activity).toBe('increasing');
        });
    });

    describe('Cleanup and Reset', () => {
        test('should cleanup dual brain components', async () => {
            const callback = vi.fn();
            model.setContextUpdateCallback(callback);
            model.addTranscript('Test transcript');

            await model.cleanup();

            expect(model.contextAnalysisTimer).toBeNull();
            expect(model.recentTranscripts).toEqual([]);
            expect(model.contextHistory).toEqual([]);
        });

        test('should reset dual brain components', () => {
            model.addTranscript('Test transcript');
            model.contextHistory = [{ test: 'data' }];
            model.contextBuffer.audio.volume = 0.8;

            model.reset();

            expect(model.recentTranscripts).toEqual([]);
            expect(model.contextHistory).toEqual([]);
            expect(model.contextBuffer.audio.volume).toBe(0);
        });
    });

    describe('Error Handling', () => {
        test('should handle missing contextual bridge gracefully', () => {
            model.contextualBridge = null;

            expect(() => {
                model.onContextUpdate(vi.fn());
                model.onSignificantContextChange(vi.fn());
                model.removeContextCallback(vi.fn());
            }).not.toThrow();

            const enrichedContext = model.getEnrichedContext();
            expect(enrichedContext).toBeDefined();
        });

        test('should handle cleanup with null timer', async () => {
            model.contextAnalysisTimer = null;

            expect(async () => {
                await model.cleanup();
            }).not.toThrow();
        });
    });
});