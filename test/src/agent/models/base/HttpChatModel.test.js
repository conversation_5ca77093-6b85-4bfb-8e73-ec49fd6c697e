/**
 * Comprehensive Unit Tests for HttpChatModel
 * Tests the HTTP-specific base class for HTTP-based chat models
 * 
 * Coverage Areas:
 * - Constructor and HTTP-specific configuration
 * - HTTP request/retry logic with various failure scenarios
 * - Metrics tracking and performance measurement
 * - API method tests (invoke, _generate) 
 * - Error handling for HTTP-specific errors
 * - Configuration validation
 * - Health checks and timeout calculations
 * - Abstract method enforcement
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { HttpChatModel } from '@/agent/models/base/HttpChatModel.js';
import { BaseChatModel } from '@/agent/models/base/BaseChatModel.js';
import { AIMessage, HumanMessage } from '@langchain/core/messages';

// Mock implementation for testing the HttpChatModel abstract class
class TestHttpChatModel extends HttpChatModel {
    constructor(options = {}) {
        super({
            ...options,
            provider: 'test-http',
            baseUrl: 'https://api.test.com'
        });
    }

    // Implement abstract methods required by HttpChatModel
    async _performHttpRequest(messages, options = {}, context = {}) {
        // Mock HTTP request implementation
        if (this._shouldSimulateFailure) {
            throw new Error(this._failureMessage || 'HTTP request failed');
        }
        
        return {
            content: 'Test HTTP response',
            message: new AIMessage({ content: 'Test HTTP response' }),
            generations: [{
                text: 'Test HTTP response',
                message: new AIMessage({ content: 'Test HTTP response' })
            }]
        };
    }

    _parseHttpResponse(response) {
        return {
            content: response.content || 'Parsed response',
            message: new AIMessage({ content: response.content || 'Parsed response' })
        };
    }

    // Test utility methods
    _simulateFailure(shouldFail = true, message = 'Simulated failure') {
        this._shouldSimulateFailure = shouldFail;
        this._failureMessage = message;
    }

    _resetFailureSimulation() {
        this._shouldSimulateFailure = false;
        this._failureMessage = null;
    }
}

describe('HttpChatModel', () => {
    let model;
    let consoleLogSpy;
    let consoleWarnSpy;
    let consoleErrorSpy;

    beforeEach(() => {
        // Suppress console output during tests
        consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
        consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
        consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

        model = new TestHttpChatModel({
            apiKey: 'test-api-key',
            model: 'test-http-model',
            baseUrl: 'https://api.test.com',
            timeout: 5000,
            retryAttempts: 3,
            retryDelay: 100,
            maxTokens: 2000
        });
    });

    afterEach(() => {
        vi.clearAllMocks();
        consoleLogSpy?.mockRestore();
        consoleWarnSpy?.mockRestore();
        consoleErrorSpy?.mockRestore();
    });

    describe('Constructor and Configuration', () => {
        it('should create HttpChatModel with correct inheritance', () => {
            expect(model).toBeInstanceOf(HttpChatModel);
            expect(model).toBeInstanceOf(BaseChatModel);
            expect(model.constructor.name).toBe('TestHttpChatModel');
        });

        it('should initialize HTTP-specific configuration with defaults', () => {
            expect(model.httpConfig).toMatchObject({
                baseUrl: 'https://api.test.com',
                timeout: 5000,
                retryAttempts: 3,
                retryDelay: 100,
                maxTokens: 2000,
                streaming: false
            });
        });

        it('should initialize request metrics with zero values', () => {
            expect(model.requestMetrics).toMatchObject({
                totalRequests: 0,
                successfulRequests: 0,
                failedRequests: 0,
                averageResponseTime: 0,
                totalResponseTime: 0
            });
        });

        it('should set apiMode to http', () => {
            expect(model.apiMode).toBe('http');
        });

        it('should allow custom HTTP configuration', () => {
            const customModel = new TestHttpChatModel({
                baseUrl: 'https://custom.api.com',
                timeout: 10000,
                retryAttempts: 5,
                retryDelay: 500,
                maxTokens: 4000,
                streaming: true,
                httpConfig: {
                    customHeader: 'test-value'
                }
            });

            expect(customModel.httpConfig).toMatchObject({
                baseUrl: 'https://custom.api.com',
                timeout: 10000,
                retryAttempts: 5,
                retryDelay: 500,
                maxTokens: 4000,
                streaming: true,
                customHeader: 'test-value'
            });
        });

        it('should merge httpConfig options correctly', () => {
            const customModel = new TestHttpChatModel({
                timeout: 8000,
                httpConfig: {
                    timeout: 12000, // Should override the direct timeout
                    customOption: 'test'
                }
            });

            expect(customModel.httpConfig.timeout).toBe(12000);
            expect(customModel.httpConfig.customOption).toBe('test');
        });
    });

    describe('Configuration Validation', () => {
        it('should validate HTTP configuration successfully', () => {
            expect(() => {
                new TestHttpChatModel({
                    baseUrl: 'https://valid.api.com',
                    timeout: 5000
                });
            }).not.toThrow();
        });

        it('should validate HTTP configuration with API key', () => {
            expect(() => {
                new TestHttpChatModel({
                    apiKey: 'test-key',
                    timeout: 5000
                });
            }).not.toThrow();
        });

        it('should throw error when neither baseUrl nor apiKey provided', () => {
            expect(() => {
                new TestHttpChatModel({
                    timeout: 5000
                    // No baseUrl or apiKey
                });
            }).toThrow('HTTP model requires either baseUrl or apiKey configuration');
        });

        it('should warn about very low timeout values', () => {
            new TestHttpChatModel({
                baseUrl: 'https://api.test.com',
                timeout: 50 // Very low timeout
            });

            expect(consoleWarnSpy).toHaveBeenCalledWith(
                expect.stringContaining('HTTP timeout is very low')
            );
        });
    });

    describe('HTTP Request and Retry Logic', () => {
        it('should make successful HTTP request', async () => {
            const messages = [new HumanMessage({ content: 'Test message' })];
            
            const response = await model._makeHttpRequest(messages);

            expect(response).toMatchObject({
                content: 'Test HTTP response',
                message: expect.any(AIMessage)
            });

            expect(model.requestMetrics.totalRequests).toBe(1);
            expect(model.requestMetrics.successfulRequests).toBe(1);
            expect(model.requestMetrics.failedRequests).toBe(0);
        });

        it('should track request metrics correctly', async () => {
            const messages = [new HumanMessage({ content: 'Test' })];
            const startTime = Date.now();
            
            await model._makeHttpRequest(messages);
            
            expect(model.requestMetrics.totalRequests).toBe(1);
            expect(model.requestMetrics.successfulRequests).toBe(1);
            expect(model.requestMetrics.totalResponseTime).toBeGreaterThan(0);
            expect(model.requestMetrics.averageResponseTime).toBeGreaterThan(0);
        });

        it('should retry on retryable errors', async () => {
            const messages = [new HumanMessage({ content: 'Test' })];
            let callCount = 0;
            
            // Mock _performHttpRequest to fail first two times, succeed on third
            model._performHttpRequest = vi.fn().mockImplementation(() => {
                callCount++;
                if (callCount <= 2) {
                    throw new Error('ECONNRESET'); // Retryable error
                }
                return {
                    content: 'Success after retry',
                    message: new AIMessage({ content: 'Success after retry' })
                };
            });

            const response = await model._makeHttpRequest(messages);

            expect(model._performHttpRequest).toHaveBeenCalledTimes(3);
            expect(response.content).toBe('Success after retry');
            expect(model.requestMetrics.successfulRequests).toBe(1);
        });

        it('should not retry non-retryable errors', async () => {
            const messages = [new HumanMessage({ content: 'Test' })];
            
            model._simulateFailure(true, 'Invalid API key'); // Non-retryable

            await expect(model._makeHttpRequest(messages)).rejects.toThrow('Invalid API key');
            expect(model.requestMetrics.totalRequests).toBe(1);
            expect(model.requestMetrics.failedRequests).toBe(1);
        });

        it('should exhaust retry attempts for persistent retryable errors', async () => {
            const messages = [new HumanMessage({ content: 'Test' })];
            
            model._simulateFailure(true, 'ENOTFOUND'); // Always retryable

            await expect(model._makeHttpRequest(messages)).rejects.toThrow('ENOTFOUND');
            expect(model.requestMetrics.totalRequests).toBe(1);
            expect(model.requestMetrics.failedRequests).toBe(1);
        });

        it('should apply retry delay between attempts', async () => {
            const messages = [new HumanMessage({ content: 'Test' })];
            const startTime = Date.now();
            let callCount = 0;

            model._performHttpRequest = vi.fn().mockImplementation(() => {
                callCount++;
                if (callCount <= 2) {
                    throw new Error('Request timeout'); // Retryable
                }
                return { content: 'Success', message: new AIMessage({ content: 'Success' }) };
            });

            await model._makeHttpRequest(messages);
            const totalTime = Date.now() - startTime;

            // Should have taken at least some delay time (100ms * 1 + 100ms * 2 = 300ms minimum)
            expect(totalTime).toBeGreaterThan(200); // Account for test timing variations
            expect(model._performHttpRequest).toHaveBeenCalledTimes(3);
        });
    });

    describe('API Methods - invoke()', () => {
        it('should invoke successfully with basic messages', async () => {
            const messages = [new HumanMessage({ content: 'Hello' })];
            
            const response = await model.invoke(messages);

            expect(response).toBeInstanceOf(AIMessage);
            expect(response.content).toBe('Test HTTP response');
            expect(model.metrics.requests).toBe(1);
            expect(model.metrics.successes).toBe(1);
        });

        it('should handle invoke with options', async () => {
            const messages = [new HumanMessage({ content: 'Hello' })];
            const options = {
                temperature: 0.8,
                maxTokens: 1000,
                urgency: 'high'
            };
            
            const response = await model.invoke(messages, options);

            expect(response).toBeInstanceOf(AIMessage);
            expect(response.content).toBe('Test HTTP response');
        });

        it('should check API limits before invoke', async () => {
            const messages = [new HumanMessage({ content: 'Test' })];
            
            // Mock API limits check to fail
            model._checkApiLimits = vi.fn().mockReturnValue(false);

            await expect(model.invoke(messages)).rejects.toThrow(
                'API rate limit exceeded. Please wait before making more requests.'
            );

            expect(model._checkApiLimits).toHaveBeenCalled();
        });

        it('should record metrics on invoke failure', async () => {
            const messages = [new HumanMessage({ content: 'Test' })];
            
            model._simulateFailure(true, 'Test invoke failure');

            await expect(model.invoke(messages)).rejects.toThrow('Test invoke failure');
            
            expect(model.metrics.requests).toBe(1);
            expect(model.metrics.failures).toBe(1);
            expect(model.metrics.successes).toBe(0);
        });

        it('should handle context parameters correctly', async () => {
            const messages = [new HumanMessage({ content: 'Test' })];
            const options = {
                urgency: 'critical',
                isDualBrainAnalysis: true,
                isContextualAnalysis: true
            };

            // Spy on _makeHttpRequest to verify context is passed
            const httpRequestSpy = vi.spyOn(model, '_makeHttpRequest');
            
            await model.invoke(messages, options);

            expect(httpRequestSpy).toHaveBeenCalledWith(
                messages,
                options,
                expect.objectContaining({
                    streaming: false,
                    hasToolCalls: false,
                    messageCount: 1,
                    urgency: 'critical',
                    isDualBrainAnalysis: true,
                    isContextualAnalysis: true
                })
            );
        });
    });

    describe('API Methods - _generate()', () => {
        it('should generate response successfully', async () => {
            const messages = [new HumanMessage({ content: 'Generate test' })];
            
            const result = await model._generate(messages);

            expect(result).toHaveProperty('generations');
            expect(result.generations).toHaveLength(1);
            expect(result.generations[0]).toMatchObject({
                text: 'Test HTTP response',
                message: expect.any(AIMessage)
            });
        });

        it('should handle _generate with options and runManager', async () => {
            const messages = [new HumanMessage({ content: 'Test' })];
            const options = { temperature: 0.9 };
            const runManager = {}; // Mock run manager

            const result = await model._generate(messages, options, runManager);

            expect(result.generations[0].message.content).toBe('Test HTTP response');
        });

        it('should check API limits in _generate', async () => {
            const messages = [new HumanMessage({ content: 'Test' })];
            
            model._checkApiLimits = vi.fn().mockReturnValue(false);

            await expect(model._generate(messages)).rejects.toThrow(
                'API rate limit exceeded'
            );
        });

        it('should record metrics on _generate failure', async () => {
            const messages = [new HumanMessage({ content: 'Test' })];
            
            model._simulateFailure(true, 'Generate failure');

            await expect(model._generate(messages)).rejects.toThrow('Generate failure');
            
            expect(model.metrics.requests).toBe(1);
            expect(model.metrics.failures).toBe(1);
        });

        it('should return correct LangChain ChatGeneration format', async () => {
            const messages = [new HumanMessage({ content: 'Test' })];
            
            // Mock response with tool calls
            model._performHttpRequest = vi.fn().mockResolvedValue({
                content: 'Response with tools',
                tool_calls: [{ name: 'test_tool', args: {} }],
                generations: [{
                    text: 'Custom generation text',
                    message: new AIMessage({ content: 'Custom message' })
                }]
            });

            const result = await model._generate(messages);

            expect(result.generations).toHaveLength(1);
            expect(result.generations[0].text).toBe('Custom generation text');
            expect(result.generations[0].message.content).toBe('Custom message');
        });
    });

    describe('HTTP-specific Utilities', () => {
        it('should build HTTP headers correctly', () => {
            const headers = model._buildHttpHeaders();

            expect(headers).toMatchObject({
                'Content-Type': 'application/json',
                'User-Agent': 'HttpChatModel/1.0'
            });
        });

        it('should build HTTP headers with custom options', () => {
            const customHeaders = {
                'Authorization': 'Bearer test-token',
                'X-Custom-Header': 'custom-value'
            };

            const headers = model._buildHttpHeaders({ headers: customHeaders });

            expect(headers).toMatchObject({
                'Content-Type': 'application/json',
                'User-Agent': 'HttpChatModel/1.0',
                'Authorization': 'Bearer test-token',
                'X-Custom-Header': 'custom-value'
            });
        });

        it('should build request payload correctly', () => {
            const messages = [
                new HumanMessage({ content: 'Hello' }),
                new AIMessage({ content: 'Hi' })
            ];
            const options = { temperature: 0.8, max_tokens: 1500 };

            const payload = model._buildRequestPayload(messages, options);

            expect(payload).toMatchObject({
                messages: [
                    { role: 'user', content: 'Hello' },
                    { role: 'assistant', content: 'Hi' }
                ],
                max_tokens: 1500,
                temperature: 0.8
            });
        });

        it('should build request payload with defaults', () => {
            const messages = [new HumanMessage({ content: 'Test' })];

            const payload = model._buildRequestPayload(messages);

            expect(payload).toMatchObject({
                messages: [{ role: 'user', content: 'Test' }],
                max_tokens: 2000, // httpConfig default
                temperature: 0.7 // model default
            });
        });

        it('should calculate HTTP timeout correctly', () => {
            const timeout1 = model._calculateTimeout();
            expect(timeout1).toBe(5000); // Base timeout

            const context = { urgency: 'high' };
            const timeout2 = model._calculateTimeout(context);
            expect(timeout2).toBeLessThan(5000); // Reduced for high urgency
        });
    });

    describe('HTTP Metrics and Performance', () => {
        it('should record HTTP-specific metrics correctly', () => {
            model._recordHttpMetrics(1500, true);

            expect(model.requestMetrics).toMatchObject({
                totalRequests: 1,
                successfulRequests: 1,
                failedRequests: 0,
                totalResponseTime: 1500,
                averageResponseTime: 1500
            });
        });

        it('should record failed HTTP metrics correctly', () => {
            model._recordHttpMetrics(800, false);

            expect(model.requestMetrics).toMatchObject({
                totalRequests: 1,
                successfulRequests: 0,
                failedRequests: 1,
                totalResponseTime: 0,
                averageResponseTime: 0
            });
        });

        it('should calculate average response time over multiple requests', () => {
            model._recordHttpMetrics(1000, true);
            model._recordHttpMetrics(2000, true);
            model._recordHttpMetrics(1500, true);

            expect(model.requestMetrics.successfulRequests).toBe(3);
            expect(model.requestMetrics.averageResponseTime).toBe(1500); // (1000+2000+1500)/3
        });

        it('should get HTTP-specific metrics', () => {
            model._recordHttpMetrics(1200, true);
            model._recordHttpMetrics(0, false);

            const httpMetrics = model.getHttpMetrics();

            expect(httpMetrics).toMatchObject({
                totalRequests: 2,
                successfulRequests: 1,
                failedRequests: 1,
                averageResponseTime: 1200,
                totalResponseTime: 1200,
                successRate: '50.00%',
                failureRate: '50.00%'
            });
        });

        it('should handle zero requests in metrics calculation', () => {
            const httpMetrics = model.getHttpMetrics();

            expect(httpMetrics.successRate).toBe('0%');
            expect(httpMetrics.failureRate).toBe('0%');
        });

        it('should get enhanced metrics including HTTP data', () => {
            model._recordHttpMetrics(1000, true);
            
            const metrics = model.getMetrics();

            expect(metrics).toHaveProperty('http');
            expect(metrics.http).toMatchObject({
                totalRequests: 1,
                successfulRequests: 1,
                successRate: '100.00%'
            });

            expect(metrics).toHaveProperty('config');
            expect(metrics.config).toMatchObject({
                timeout: 5000,
                retryAttempts: 3,
                maxTokens: 2000
            });
        });

        it('should reset HTTP metrics correctly', () => {
            model._recordHttpMetrics(1000, true);
            model._recordHttpMetrics(500, false);

            model.resetHttpMetrics();

            expect(model.requestMetrics).toMatchObject({
                totalRequests: 0,
                successfulRequests: 0,
                failedRequests: 0,
                averageResponseTime: 0,
                totalResponseTime: 0
            });
        });
    });

    describe('Health Check', () => {
        it('should perform HTTP health check successfully', async () => {
            const healthResult = await model._performHealthCheck();

            expect(healthResult).toMatchObject({
                httpEndpoint: 'https://api.test.com',
                responseTime: expect.any(Number),
                hasResponse: true
            });
        });

        it('should handle health check failure', async () => {
            model._simulateFailure(true, 'Health check failed');

            await expect(model._performHealthCheck()).rejects.toThrow(
                'HTTP health check failed: Health check failed'
            );
        });

        it('should use configured baseUrl in health check', async () => {
            const healthResult = await model._performHealthCheck();
            expect(healthResult.httpEndpoint).toBe('https://api.test.com');
        });

        it('should handle health check when only apiKey is configured', async () => {
            const apiKeyModel = new TestHttpChatModel({
                apiKey: 'test-key'
                // No baseUrl
            });

            const healthResult = await apiKeyModel._performHealthCheck();
            expect(healthResult.httpEndpoint).toBe('configured');
        });
    });

    describe('Abstract Method Enforcement', () => {
        it('should throw error for unimplemented _parseHttpResponse', () => {
            const baseModel = new HttpChatModel({
                baseUrl: 'https://test.com'
            });

            expect(() => baseModel._parseHttpResponse({})).toThrow(
                '_parseHttpResponse() must be implemented by HTTP model subclass'
            );
        });

        it('should throw error for unimplemented _performHttpRequest', async () => {
            const baseModel = new HttpChatModel({
                baseUrl: 'https://test.com'
            });

            await expect(baseModel._performHttpRequest([])).rejects.toThrow(
                '_performHttpRequest() must be implemented by HTTP model subclass'
            );
        });
    });

    describe('WebSocket Method Restrictions', () => {
        it('should throw errors for WebSocket-specific methods', () => {
            expect(() => model.buildWebSocketUrl()).toThrow(
                'HttpChatModel does not support WebSocket connections'
            );

            expect(() => model.getConnectionOptions()).toThrow(
                'HttpChatModel does not support WebSocket connections'
            );

            expect(() => model.processProviderMessage({})).toThrow(
                'HttpChatModel does not support WebSocket message processing'
            );
        });

        it('should resolve initializeSession without WebSocket setup', async () => {
            const result = await model.initializeSession({ test: 'config' });
            expect(result).toBe(true);
        });

        it('should throw error for audio transmission', async () => {
            await expect(model._sendAudioToProvider(new ArrayBuffer(8), 'pcm16'))
                .rejects.toThrow('Audio transmission not supported by HttpChatModel');
        });
    });

    describe('Cleanup', () => {
        it('should cleanup HTTP resources', async () => {
            model._recordHttpMetrics(1000, true);
            
            await model.cleanup();

            expect(model.requestMetrics.totalRequests).toBe(0);
            expect(model.requestMetrics.successfulRequests).toBe(0);
        });

        it('should call parent cleanup', async () => {
            const superCleanupSpy = vi.spyOn(BaseChatModel.prototype, 'cleanup').mockResolvedValue();
            
            await model.cleanup();

            expect(superCleanupSpy).toHaveBeenCalled();
            superCleanupSpy.mockRestore();
        });
    });
});