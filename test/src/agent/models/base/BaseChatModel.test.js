/**
 * Comprehensive Unit Tests for BaseChatModel
 * Tests the universal base class that provides foundation for all model implementations
 * 
 * Coverage Areas:
 * - LangChain v0.3 compliance (bindTools, invocationParams)
 * - Streaming capabilities and state management
 * - Error handling and retry logic
 * - Security features (credential management, safe JSON parsing)
 * - Performance metrics and monitoring
 * - API limits and rate limiting
 * - Tool binding and execution
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { BaseChatModel, StreamingState, StreamingCloseCodes, createBaseConfig } from '@/agent/models/base/BaseChatModel.js';
import { AIMessage, HumanMessage } from '@langchain/core/messages';

// Mock implementations for testing the abstract base class
class TestChatModel extends BaseChatModel {
    constructor(options = {}) {
        super({
            ...options,
            provider: 'test',
            apiMode: 'http'
        });
    }

    // Implement required abstract methods
    async invoke(messages, options = {}) {
        return new AIMessage({ content: 'Test response' });
    }

    buildWebSocketUrl() {
        return 'ws://test.example.com/ws';
    }

    getConnectionOptions() {
        return { headers: { 'Authorization': 'Bearer test' } };
    }

    processProviderMessage(message) {
        return { processed: true };
    }

    async initializeSession(config = {}) {
        return true;
    }

    async _sendAudioToProvider(audioData, format) {
        return true;
    }
}

describe('BaseChatModel', () => {
    let model;

    beforeEach(() => {
        model = new TestChatModel({
            apiKey: 'test-api-key',
            model: 'test-model',
            timeout: 5000
        });
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('Configuration and Initialization', () => {
        it('should create base configuration with correct defaults', () => {
            const config = createBaseConfig();
            
            expect(config).toMatchObject({
                apiKey: '',
                endpoint: '',
                model: '',
                provider: 'universal',
                timeout: 500,
                temperature: 0.7,
                maxTokens: 2000,
                topP: 0.8,
                toolChoice: 'auto',
                maxRetries: 2,
                retryDelay: 100,
                audioConfig: {
                    sampleRate: 16000,
                    bitDepth: 16,
                    channels: 1,
                    format: 'pcm16'
                },
                rateLimiting: {
                    enabled: true,
                    maxChunksPerSecond: 5,
                    minIntervalMs: 200
                }
            });
        });

        it('should initialize with custom configuration', () => {
            const customModel = new TestChatModel({
                apiKey: 'custom-key',
                model: 'custom-model',
                timeout: 10000,
                temperature: 0.9,
                maxRetries: 5
            });

            expect(customModel.apiKey).toBe('custom-key');
            expect(customModel.model).toBe('custom-model');
            expect(customModel.timeout).toBe(10000);
            expect(customModel.temperature).toBe(0.9);
            expect(customModel.maxRetries).toBe(5);
        });

        it('should set correct provider and API mode', () => {
            expect(model.provider).toBe('test');
            expect(model.apiMode).toBe('http');
            expect(model._llmType()).toBe('test-http');
        });

        it('should initialize metrics with zero values', () => {
            expect(model.metrics).toMatchObject({
                requests: 0,
                successes: 0,
                failures: 0,
                totalResponseTime: 0,
                averageResponseTime: 0,
                healthChecks: 0,
                toolCalls: 0,
                lastRequestTime: null,
                lastResponseTime: null
            });
        });

        it('should initialize streaming stats with zero values', () => {
            expect(model.stats).toMatchObject({
                connectionsAttempted: 0,
                connectionsSuccessful: 0,
                messagesReceived: 0,
                messagesSent: 0,
                audioChunksSent: 0,
                reconnections: 0,
                lastConnectTime: null,
                uptime: 0
            });
        });
    });

    describe('LangChain v0.3 Compliance', () => {
        it('should return correct invocation parameters', () => {
            const params = model.invocationParams();
            
            expect(params).toMatchObject({
                model_name: 'test-model',
                api_mode: 'http',
                temperature: 0.7,
                max_tokens: 2000,
                top_p: 0.8,
                timeout: 5000,
                tools: [],
                tool_choice: 'auto',
                retry_config: {
                    max_retries: 2,
                    retry_delay: 100
                }
            });
        });

        it('should bind tools correctly', () => {
            const testTool = {
                name: 'test_tool',
                description: 'A test tool',
                schema: { type: 'object', properties: {} }
            };

            const boundModel = model.bindTools([testTool]);
            
            expect(boundModel.boundTools).toHaveLength(1);
            expect(boundModel.boundTools[0]).toEqual(testTool);
            expect(boundModel).not.toBe(model); // Should return new instance
        });

        it('should handle multiple tool binding', () => {
            const tools = [
                { name: 'tool1', description: 'Tool 1' },
                { name: 'tool2', description: 'Tool 2' },
                { name: 'tool3', description: 'Tool 3' }
            ];

            const boundModel = model.bindTools(tools);
            
            expect(boundModel.boundTools).toHaveLength(3);
            expect(boundModel.boundTools.map(t => t.name)).toEqual(['tool1', 'tool2', 'tool3']);
        });

        it('should replace existing tools with same name', () => {
            const tool1 = { name: 'duplicate', description: 'Original tool' };
            const tool2 = { name: 'duplicate', description: 'Updated tool' };

            const boundModel = model.bindTools([tool1]).bindTools([tool2]);
            
            expect(boundModel.boundTools).toHaveLength(1);
            expect(boundModel.boundTools[0].description).toBe('Updated tool');
        });

        it('should handle invalid tools gracefully', () => {
            const invalidTools = [
                null,
                undefined,
                { description: 'No name' },
                { name: '', description: 'Empty name' }
            ];

            const boundModel = model.bindTools(invalidTools);
            
            expect(boundModel.boundTools).toHaveLength(0);
        });
    });

    describe('Streaming State Management', () => {
        it('should start in DISCONNECTED state', () => {
            expect(model.state).toBe(StreamingState.DISCONNECTED);
        });

        it('should transition states correctly', () => {
            const stateChanges = [];
            model.on('stateChange', (newState, oldState) => {
                stateChanges.push({ newState, oldState });
            });

            model._setState(StreamingState.CONNECTING);
            model._setState(StreamingState.CONNECTED);
            model._setState(StreamingState.SESSION_READY);

            expect(stateChanges).toHaveLength(3);
            expect(stateChanges[0]).toEqual({ 
                newState: StreamingState.CONNECTING, 
                oldState: StreamingState.DISCONNECTED 
            });
            expect(stateChanges[2].newState).toBe(StreamingState.SESSION_READY);
        });

        it('should provide correct session status checks', () => {
            expect(model.isConnected()).toBe(false);
            expect(model.isSessionReady()).toBe(false);

            // Simulate connection
            model.socket = { readyState: 1 }; // WebSocket.OPEN
            expect(model.isConnected()).toBe(true);

            // Simulate session ready
            model._setState(StreamingState.SESSION_READY);
            model.sessionStabilized = true;
            expect(model.isSessionReady()).toBe(true);
        });
    });

    describe('Error Handling and Retry Logic', () => {
        it('should identify retryable errors correctly', () => {
            const retryableErrors = [
                new Error('ENOTFOUND'),
                new Error('ECONNRESET'),
                new Error('Request timeout'),
                new Error('Status: 503'),
                new Error('Status: 429')
            ];

            retryableErrors.forEach(error => {
                expect(model._isRetryableError(error)).toBe(true);
            });
        });

        it('should identify non-retryable errors correctly', () => {
            const nonRetryableErrors = [
                new Error('Invalid API key'),
                new Error('Status: 400'),
                new Error('Status: 401'),
                new Error('Syntax error')
            ];

            nonRetryableErrors.forEach(error => {
                expect(model._isRetryableError(error)).toBe(false);
            });
        });

        it('should enhance errors with context', () => {
            const originalError = new Error('Test error');
            const context = { operation: 'test', timestamp: Date.now() };
            
            const enhancedError = model._handleError(originalError, context);
            
            expect(enhancedError.message).toBe('Test error');
            expect(enhancedError.originalError).toBe(originalError);
            expect(enhancedError.context).toEqual(context);
            expect(enhancedError.model).toBe('test-model');
            expect(enhancedError.apiMode).toBe('http');
        });
    });

    describe('Performance Metrics', () => {
        it('should record metrics for successful requests', () => {
            model._recordMetrics(1500, true);
            
            expect(model.metrics.requests).toBe(1);
            expect(model.metrics.successes).toBe(1);
            expect(model.metrics.failures).toBe(0);
            expect(model.metrics.totalResponseTime).toBe(1500);
            expect(model.metrics.averageResponseTime).toBe(1500);
            expect(model.metrics.lastResponseTime).toBe(1500);
        });

        it('should record metrics for failed requests', () => {
            model._recordMetrics(0, false);
            
            expect(model.metrics.requests).toBe(1);
            expect(model.metrics.successes).toBe(0);
            expect(model.metrics.failures).toBe(1);
            expect(model.metrics.totalResponseTime).toBe(0);
            expect(model.metrics.averageResponseTime).toBe(0);
        });

        it('should calculate average response time correctly', () => {
            model._recordMetrics(1000, true);
            model._recordMetrics(2000, true);
            model._recordMetrics(1500, true);
            
            expect(model.metrics.successes).toBe(3);
            expect(model.metrics.averageResponseTime).toBe(1500); // (1000+2000+1500)/3
        });

        it('should provide comprehensive metrics', () => {
            model._recordMetrics(1000, true);
            model.metrics.toolCalls = 2;
            
            const metrics = model.getMetrics();
            
            expect(metrics).toMatchObject({
                model: 'test-model',
                apiMode: 'http',
                boundToolsCount: 0,
                supportsFunctionCalling: false,
                successRate: 1,
                errorRate: 0
            });

            expect(metrics.streaming).toBeDefined();
            expect(metrics.security).toBeDefined();
        });
    });

    describe('API Limits and Rate Limiting', () => {
        it('should check API limits correctly', () => {
            model.apiLimits.testMode = true;
            model.apiLimits.maxAttempts = 3;
            model.apiLimits.currentAttempts = 0;
            
            expect(model._checkApiLimits()).toBe(true);
            expect(model.apiLimits.currentAttempts).toBe(1);
            
            expect(model._checkApiLimits()).toBe(true);
            expect(model.apiLimits.currentAttempts).toBe(2);
            
            expect(model._checkApiLimits()).toBe(true);
            expect(model.apiLimits.currentAttempts).toBe(3);
            
            // Should fail on 4th attempt
            expect(model._checkApiLimits()).toBe(false);
        });

        it('should reset API limits', () => {
            model.apiLimits.currentAttempts = 5;
            model.resetApiLimits();
            
            expect(model.apiLimits.currentAttempts).toBe(0);
        });

        it('should provide API limits status', () => {
            model.apiLimits.testMode = true;
            model.apiLimits.maxAttempts = 10;
            model.apiLimits.currentAttempts = 3;
            
            const status = model.getApiLimitsStatus();
            
            expect(status).toMatchObject({
                enabled: true,
                testMode: true,
                currentAttempts: 3,
                maxAttempts: 10,
                remainingAttempts: 7,
                limitReached: false
            });
        });
    });

    describe('Tool Execution', () => {
        it('should execute bound tools correctly', async () => {
            const mockTool = {
                name: 'test_tool',
                func: vi.fn().mockResolvedValue('tool result')
            };

            model.boundTools = [mockTool];
            
            const toolCalls = [{ 
                id: 'call_123', 
                name: 'test_tool', 
                args: { param: 'value' } 
            }];

            const results = await model._executeToolCalls(toolCalls);
            
            expect(results).toHaveLength(1);
            expect(results[0]).toMatchObject({
                id: 'call_123',
                name: 'test_tool',
                result: 'tool result'
            });
            expect(mockTool.func).toHaveBeenCalledWith({ param: 'value' });
        });

        it('should handle tool execution errors', async () => {
            const mockTool = {
                name: 'failing_tool',
                func: vi.fn().mockRejectedValue(new Error('Tool failed'))
            };

            model.boundTools = [mockTool];
            
            const toolCalls = [{ 
                id: 'call_456', 
                name: 'failing_tool', 
                args: {} 
            }];

            const results = await model._executeToolCalls(toolCalls);
            
            expect(results[0]).toMatchObject({
                id: 'call_456',
                name: 'failing_tool',
                error: 'Tool failed'
            });
        });

        it('should handle missing tools', async () => {
            const toolCalls = [{ 
                id: 'call_789', 
                name: 'nonexistent_tool', 
                args: {} 
            }];

            const results = await model._executeToolCalls(toolCalls);
            
            expect(results[0]).toMatchObject({
                id: 'call_789',
                name: 'nonexistent_tool',
                error: "Tool 'nonexistent_tool' not found in bound tools"
            });
        });
    });

    describe('Utility Methods', () => {
        it('should format messages correctly', () => {
            const messages = [
                new HumanMessage({ content: 'Hello' }),
                new AIMessage({ content: 'Hi there' })
            ];

            const formatted = model._formatMessages(messages);
            
            expect(formatted).toEqual([
                { role: 'user', content: 'Hello' },
                { role: 'assistant', content: 'Hi there' }
            ]);
        });

        it('should calculate adaptive timeouts', () => {
            const baseTimeout = 5000;
            
            // Normal timeout
            expect(model._calculateAdaptiveTimeout(baseTimeout)).toBe(5000);
            
            // With streaming
            const streamingContext = { streaming: true, streamingTimeout: 10000 };
            expect(model._calculateAdaptiveTimeout(baseTimeout, streamingContext)).toBe(10000);
            
            // With tool calls
            const toolContext = { hasToolCalls: true, toolCallTimeout: 3000 };
            expect(model._calculateAdaptiveTimeout(baseTimeout, toolContext)).toBe(3000);
            
            // With urgency
            const urgentContext = { urgency: 'high' };
            expect(model._calculateAdaptiveTimeout(baseTimeout, urgentContext)).toBe(4000); // 80% of base
        });

        it('should convert ArrayBuffer to base64', () => {
            const buffer = new Uint8Array([1, 2, 3, 4, 5]);
            const base64 = model._arrayBufferToBase64(buffer);
            
            expect(typeof base64).toBe('string');
            expect(base64.length).toBeGreaterThan(0);
        });
    });

    describe('Cleanup and Reset', () => {
        it('should reset all metrics', () => {
            // Set some metrics
            model.metrics.requests = 5;
            model.metrics.successes = 3;
            model.stats.connectionsAttempted = 2;
            
            model.reset();
            
            expect(model.metrics.requests).toBe(0);
            expect(model.metrics.successes).toBe(0);
            expect(model.stats.connectionsAttempted).toBe(0);
        });

        it('should cleanup resources', async () => {
            model.socket = { readyState: 1 };
            model._setState(StreamingState.CONNECTED);
            
            const disconnectSpy = vi.spyOn(model, 'disconnect').mockResolvedValue();
            
            await model.cleanup();
            
            expect(disconnectSpy).toHaveBeenCalled();
        });
    });
});

// Test StreamingState and StreamingCloseCodes constants
describe('Streaming Constants', () => {
    it('should provide correct streaming states', () => {
        expect(StreamingState.DISCONNECTED).toBe('disconnected');
        expect(StreamingState.CONNECTING).toBe('connecting');
        expect(StreamingState.CONNECTED).toBe('connected');
        expect(StreamingState.SESSION_READY).toBe('session_ready');
    });

    it('should provide correct close codes', () => {
        expect(StreamingCloseCodes[1000]).toBe('Normal closure');
        expect(StreamingCloseCodes[1006]).toBe('Abnormal closure (connection lost)');
        expect(StreamingCloseCodes[1011]).toBe('Server internal error');
        expect(StreamingCloseCodes[4001]).toBe('Authentication failed');
    });
});