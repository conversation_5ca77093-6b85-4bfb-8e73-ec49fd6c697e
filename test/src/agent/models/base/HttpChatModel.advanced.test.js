/**
 * Advanced Test Suite for HttpChatModel.js - TestWriter2 Contribution
 * 
 * Focus Areas:
 * 1. Error handling and edge cases
 * 2. Metrics tracking and recording
 * 3. Health check functionality
 * 4. LangChain compatibility validation  
 * 5. Performance and timeout scenarios
 * 
 * This suite complements basic functionality tests with comprehensive error conditions,
 * performance edge cases, and advanced integration scenarios.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { HttpChatModel } from '@/agent/models/base/HttpChatModel.js';
import { HumanMessage, AIMessage } from '@langchain/core/messages';

// Test implementation of HttpChatModel for testing purposes
class TestHttpChatModel extends HttpChatModel {
  constructor(options = {}) {
    super({
      apiKey: 'test-key',
      baseUrl: 'https://api.test.com/v1/chat',
      ...options
    });
    
    // Mock implementation tracking
    this.mockResponses = [];
    this.mockErrors = [];
    this.callHistory = [];
  }

  async _performHttpRequest(messages, options = {}, context = {}) {
    this.callHistory.push({ messages, options, context, timestamp: Date.now() });
    
    // Check for mock errors
    if (this.mockErrors.length > 0) {
      const error = this.mockErrors.shift();
      throw error;
    }
    
    // Return mock responses
    if (this.mockResponses.length > 0) {
      return this.mockResponses.shift();
    }
    
    // Default mock response
    return {
      generations: [{
        text: 'Mock HTTP response',
        message: new AIMessage({ content: 'Mock HTTP response' }),
        tool_calls: []
      }]
    };
  }

  _parseHttpResponse(response) {
    return response;
  }

  // Test helpers
  setMockResponse(response) {
    this.mockResponses.push(response);
  }

  setMockError(error) {
    this.mockErrors.push(error);
  }

  getCallHistory() {
    return this.callHistory;
  }

  clearHistory() {
    this.callHistory = [];
    this.mockResponses = [];
    this.mockErrors = [];
  }
}

describe('HttpChatModel - Advanced Test Suite', () => {
  let model;
  let consoleSpy;

  beforeEach(() => {
    model = new TestHttpChatModel({
      timeout: 1000,
      retryAttempts: 3,
      retryDelay: 100
    });
    consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
    vi.clearAllTimers();
    vi.useFakeTimers();
  });

  afterEach(() => {
    model.clearHistory();
    consoleSpy.mockRestore();
    vi.clearAllMocks();
    vi.useRealTimers();
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle network timeout errors gracefully', async () => {
      const timeoutError = new Error('Request timeout after 1000ms');
      timeoutError.name = 'AbortError';
      model.setMockError(timeoutError);

      await expect(model.invoke([new HumanMessage('test')])).rejects.toThrow('Request timeout');
      
      const metrics = model.getHttpMetrics();
      expect(metrics.failureRate).toBe('100%');
    });

    it('should handle connection reset errors with retries', async () => {
      const connectionError = new Error('ECONNRESET connection lost');
      const connectionError2 = new Error('ECONNRESET connection lost');
      const successResponse = {
        generations: [{
          text: 'Success after retries',
          message: new AIMessage({ content: 'Success after retries' }),
          tool_calls: []
        }]
      };

      model.setMockError(connectionError);
      model.setMockError(connectionError2);
      model.setMockResponse(successResponse);

      const result = await model.invoke([new HumanMessage('test')]);
      
      expect(result.generations[0].text).toBe('Success after retries');
      expect(model.getCallHistory()).toHaveLength(3); // 2 failures + 1 success
    });

    it('should properly identify retryable vs non-retryable errors', () => {
      const retryableErrors = [
        new Error('ENOTFOUND api.test.com'),
        new Error('ECONNRESET connection lost'),
        new Error('ETIMEDOUT operation timed out'),
        new Error('Request timeout after 5000ms'),
        new Error('ECONNREFUSED connection refused')
      ];

      const nonRetryableErrors = [
        new Error('Authentication failed'),
        new Error('Invalid API key provided'),
        new Error('Model not found'),
        new Error('Invalid request format'),
        new Error('Rate limit exceeded - quota')
      ];

      retryableErrors.forEach(error => {
        expect(model._isRetryableError(error)).toBe(true);
      });

      nonRetryableErrors.forEach(error => {
        expect(model._isRetryableError(error)).toBe(false);
      });
    });

    it('should handle malformed response data', async () => {
      const malformedResponses = [
        null,
        undefined,
        {},
        { generations: null },
        { generations: [] },
        { generations: [{}] },
        { generations: [{ text: null }] }
      ];

      for (const response of malformedResponses) {
        model.clearHistory();
        model.setMockResponse(response);

        // Should not throw, but should handle gracefully
        try {
          const result = await model.invoke([new HumanMessage('test')]);
          // Should provide default or fallback response
          expect(result).toBeDefined();
          expect(result.generations).toBeDefined();
        } catch (error) {
          // If it throws, should be a meaningful error message
          expect(error.message).toMatch(/invalid|malformed|missing/i);
        }
      }
    });

    it('should handle extremely long message content', async () => {
      const longMessage = new HumanMessage({ 
        content: 'A'.repeat(100000) // 100KB message
      });

      model.setMockResponse({
        generations: [{
          text: 'Handled long message',
          message: new AIMessage({ content: 'Handled long message' }),
          tool_calls: []
        }]
      });

      const result = await model.invoke([longMessage]);
      
      expect(result.generations[0].text).toBe('Handled long message');
      const callHistory = model.getCallHistory();
      expect(callHistory[0].messages[0].content).toHaveLength(100000);
    });

    it('should handle special characters and encoding issues', async () => {
      const specialMessages = [
        new HumanMessage({ content: '🚀 Emoji test with 中文 and àáâãäå' }),
        new HumanMessage({ content: 'JSON breaking: {"test": "value"} and \n\r\t' }),
        new HumanMessage({ content: 'SQL injection: \'; DROP TABLE users; --' }),
        new HumanMessage({ content: 'XSS: <script>alert("xss")</script>' }),
        new HumanMessage({ content: 'Unicode: \\u0000\\u0001\\u001F' })
      ];

      for (const message of specialMessages) {
        model.clearHistory();
        model.setMockResponse({
          generations: [{
            text: 'Handled special characters',
            message: new AIMessage({ content: 'Handled special characters' }),
            tool_calls: []
          }]
        });

        const result = await model.invoke([message]);
        expect(result.generations[0].text).toBe('Handled special characters');
      }
    });

    it('should handle concurrent requests properly', async () => {
      const concurrentRequests = [];
      
      for (let i = 0; i < 5; i++) {
        model.setMockResponse({
          generations: [{
            text: `Response ${i}`,
            message: new AIMessage({ content: `Response ${i}` }),
            tool_calls: []
          }]
        });
        
        concurrentRequests.push(
          model.invoke([new HumanMessage(`Request ${i}`)])
        );
      }

      const results = await Promise.all(concurrentRequests);
      
      expect(results).toHaveLength(5);
      expect(model.getCallHistory()).toHaveLength(5);
      
      // Each request should have been tracked separately
      const metrics = model.getHttpMetrics();
      expect(metrics.totalRequests).toBe(5);
      expect(metrics.successfulRequests).toBe(5);
    });
  });

  describe('Metrics Tracking and Recording', () => {
    it('should accurately track request metrics', async () => {
      // Successful request
      model.setMockResponse({
        generations: [{ text: 'success', message: new AIMessage({ content: 'success' }), tool_calls: [] }]
      });
      await model.invoke([new HumanMessage('test1')]);

      // Failed request
      model.setMockError(new Error('API Error'));
      try {
        await model.invoke([new HumanMessage('test2')]);
      } catch (e) {}

      // Another successful request
      model.setMockResponse({
        generations: [{ text: 'success2', message: new AIMessage({ content: 'success2' }), tool_calls: [] }]
      });
      await model.invoke([new HumanMessage('test3')]);

      const metrics = model.getHttpMetrics();
      expect(metrics.totalRequests).toBe(3);
      expect(metrics.successfulRequests).toBe(2);
      expect(metrics.failedRequests).toBe(1);
      expect(metrics.successRate).toBe('66.67%');
      expect(metrics.failureRate).toBe('33.33%');
    });

    it('should calculate average response times correctly', async () => {
      const responseTimes = [100, 200, 300];
      
      for (const responseTime of responseTimes) {
        model.setMockResponse({
          generations: [{ text: 'test', message: new AIMessage({ content: 'test' }), tool_calls: [] }]
        });
        
        const startTime = Date.now();
        vi.setSystemTime(startTime);
        
        // Mock the delay
        const invokePromise = model.invoke([new HumanMessage('test')]);
        vi.advanceTimersByTime(responseTime);
        vi.setSystemTime(startTime + responseTime);
        
        await invokePromise;
      }

      const metrics = model.getHttpMetrics();
      expect(metrics.averageResponseTime).toBe(200); // (100 + 200 + 300) / 3
    });

    it('should track response time percentiles', async () => {
      const responseTimes = [50, 100, 150, 200, 250, 300, 350, 400, 450, 500];
      
      for (const responseTime of responseTimes) {
        model.setMockResponse({
          generations: [{ text: 'test', message: new AIMessage({ content: 'test' }), tool_calls: [] }]
        });
        
        const startTime = Date.now();
        vi.setSystemTime(startTime);
        
        const invokePromise = model.invoke([new HumanMessage('test')]);
        vi.advanceTimersByTime(responseTime);
        vi.setSystemTime(startTime + responseTime);
        
        await invokePromise;
      }

      const fullMetrics = model.getMetrics();
      expect(fullMetrics.http.averageResponseTime).toBe(275); // Average
      expect(fullMetrics.http.successRate).toBe('100%');
    });

    it('should reset metrics correctly', () => {
      // Generate some activity
      model.requestMetrics.totalRequests = 10;
      model.requestMetrics.successfulRequests = 8;
      model.requestMetrics.failedRequests = 2;
      model.requestMetrics.totalResponseTime = 5000;
      model.requestMetrics.averageResponseTime = 625;

      model.resetHttpMetrics();

      const metrics = model.getHttpMetrics();
      expect(metrics.totalRequests).toBe(0);
      expect(metrics.successfulRequests).toBe(0);
      expect(metrics.failedRequests).toBe(0);
      expect(metrics.averageResponseTime).toBe(0);
      expect(metrics.totalResponseTime).toBe(0);
    });

    it('should include configuration in metrics', () => {
      const testModel = new TestHttpChatModel({
        timeout: 2500,
        retryAttempts: 5,
        maxTokens: 4000
      });

      const metrics = testModel.getMetrics();
      expect(metrics.config.timeout).toBe(2500);
      expect(metrics.config.retryAttempts).toBe(5);
      expect(metrics.config.maxTokens).toBe(4000);
    });
  });

  describe('Health Check Functionality', () => {
    it('should perform successful health check', async () => {
      model.setMockResponse({
        generations: [{
          text: 'health check ok',
          message: new AIMessage({ content: 'health check ok' }),
          tool_calls: []
        }]
      });

      const health = await model.healthCheck();
      
      expect(health.healthy).toBe(true);
      expect(health.httpCheck).toBe(true);
      expect(health.responseTime).toBeGreaterThan(0);
      expect(health.timestamp).toBeDefined();
      
      // Should have made a minimal test request
      const callHistory = model.getCallHistory();
      expect(callHistory).toHaveLength(1);
      expect(callHistory[0].options.max_tokens).toBe(1);
    });

    it('should handle health check failures', async () => {
      model.setMockError(new Error('Health check connection failed'));

      const health = await model.healthCheck();
      
      expect(health.healthy).toBe(false);
      expect(health.error).toContain('Health check connection failed');
      expect(health.timestamp).toBeDefined();
    });

    it('should timeout health checks appropriately', async () => {
      model.setMockError(new Error('Request timeout after 1000ms'));

      const health = await model.healthCheck();
      
      expect(health.healthy).toBe(false);
      expect(health.error).toContain('timeout');
    });

    it('should include diagnostic information in health check', async () => {
      model.setMockResponse({
        generations: [{
          text: 'diagnostic response',
          message: new AIMessage({ content: 'diagnostic response' }),
          tool_calls: []
        }]
      });

      const health = await model.healthCheck();
      
      expect(health.config).toBeDefined();
      expect(health.config.timeout).toBe(1000);
      expect(health.config.retryAttempts).toBe(3);
      expect(health.metrics).toBeDefined();
    });
  });

  describe('LangChain Compatibility Validation', () => {
    it('should implement required LangChain interface methods', () => {
      expect(typeof model._llmType).toBe('function');
      expect(typeof model.invocationParams).toBe('function');
      expect(typeof model._generate).toBe('function');
      expect(typeof model.invoke).toBe('function');
    });

    it('should return correct LLM type', () => {
      expect(model._llmType()).toBe('http-chat-model');
    });

    it('should provide proper invocation parameters', () => {
      const params = model.invocationParams({ customParam: 'test' });
      
      expect(params).toHaveProperty('model_name');
      expect(params).toHaveProperty('api_mode', 'http');
      expect(params).toHaveProperty('timeout');
      expect(params).toHaveProperty('customParam', 'test');
    });

    it('should handle LangChain _generate method correctly', async () => {
      model.setMockResponse({
        generations: [{
          text: 'LangChain compatible response',
          message: new AIMessage({ content: 'LangChain compatible response' }),
          tool_calls: []
        }]
      });

      const runManager = { onText: vi.fn() };
      const result = await model._generate([new HumanMessage('test')], {}, runManager);
      
      expect(result).toHaveProperty('generations');
      expect(result.generations).toBeInstanceOf(Array);
      expect(result.generations[0]).toHaveProperty('text');
      expect(result.generations[0]).toHaveProperty('message');
    });

    it('should support tool binding correctly', () => {
      const testTools = [
        {
          name: 'test_tool_1',
          description: 'First test tool',
          schema: { type: 'object', properties: {} }
        },
        {
          name: 'test_tool_2', 
          description: 'Second test tool',
          schema: { type: 'object', properties: {} }
        }
      ];

      const boundModel = model.bindTools(testTools);
      
      expect(boundModel).toBeInstanceOf(HttpChatModel);
      expect(boundModel.boundTools).toHaveLength(2);
      expect(boundModel.boundTools[0].name).toBe('test_tool_1');
      expect(boundModel.boundTools[1].name).toBe('test_tool_2');
    });

    it('should handle tool calls in responses', async () => {
      const mockResponseWithTools = {
        generations: [{
          text: '',
          tool_calls: [
            {
              id: 'call_123',
              name: 'test_tool',
              args: { param: 'value' }
            }
          ],
          message: new AIMessage({
            content: '',
            tool_calls: [
              {
                id: 'call_123',
                name: 'test_tool',
                args: { param: 'value' }
              }
            ]
          })
        }]
      };

      model.setMockResponse(mockResponseWithTools);
      const result = await model.invoke([new HumanMessage('use tools')]);
      
      expect(result.generations[0].tool_calls).toHaveLength(1);
      expect(result.generations[0].tool_calls[0].name).toBe('test_tool');
      expect(result.generations[0].tool_calls[0].args.param).toBe('value');
    });

    it('should maintain message history format correctly', async () => {
      const conversationHistory = [
        new HumanMessage({ content: 'First human message' }),
        new AIMessage({ content: 'First AI response' }),
        new HumanMessage({ content: 'Second human message' })
      ];

      model.setMockResponse({
        generations: [{
          text: 'Response to history',
          message: new AIMessage({ content: 'Response to history' }),
          tool_calls: []
        }]
      });

      await model.invoke(conversationHistory);
      
      const callHistory = model.getCallHistory();
      const sentMessages = callHistory[0].messages;
      
      expect(sentMessages).toHaveLength(3);
      expect(sentMessages[0].content).toBe('First human message');
      expect(sentMessages[1].content).toBe('First AI response');
      expect(sentMessages[2].content).toBe('Second human message');
    });
  });

  describe('Performance and Timeout Scenarios', () => {
    it('should enforce timeout limits strictly', async () => {
      const shortTimeoutModel = new TestHttpChatModel({ timeout: 100 });
      
      // Mock a slow response
      shortTimeoutModel.setMockError(new Error('Request timeout after 100ms'));

      const startTime = Date.now();
      
      await expect(
        shortTimeoutModel.invoke([new HumanMessage('test')])
      ).rejects.toThrow('timeout');
      
      // Should not have taken much longer than timeout
      const elapsed = Date.now() - startTime;
      expect(elapsed).toBeLessThan(200); // Allow some overhead
    });

    it('should handle adaptive timeout calculations', () => {
      const baseTimeout = 1000;
      const context = {
        messageCount: 5,
        hasToolCalls: true,
        streaming: false,
        urgency: 'high'
      };

      const adaptiveTimeout = model._calculateTimeout(context);
      
      expect(adaptiveTimeout).toBeGreaterThan(0);
      expect(adaptiveTimeout).toBeLessThanOrEqual(model.httpConfig.timeout * 2);
    });

    it('should retry failed requests with exponential backoff', async () => {
      const connectionError = new Error('ECONNRESET');
      
      // Set up multiple failures then success
      model.setMockError(connectionError);
      model.setMockError(connectionError);
      model.setMockResponse({
        generations: [{
          text: 'Success after retries',
          message: new AIMessage({ content: 'Success after retries' }),
          tool_calls: []
        }]
      });

      const startTime = Date.now();
      vi.setSystemTime(startTime);
      
      const invokePromise = model.invoke([new HumanMessage('test')]);
      
      // Advance time to simulate retry delays
      vi.advanceTimersByTime(100); // First retry delay
      vi.advanceTimersByTime(200); // Second retry delay (exponential)
      
      const result = await invokePromise;
      
      expect(result.generations[0].text).toBe('Success after retries');
      expect(model.getCallHistory()).toHaveLength(3); // 2 failures + 1 success
    });

    it('should handle burst request patterns', async () => {
      const burstSize = 10;
      const requests = [];

      // Set up responses for burst
      for (let i = 0; i < burstSize; i++) {
        model.setMockResponse({
          generations: [{
            text: `Burst response ${i}`,
            message: new AIMessage({ content: `Burst response ${i}` }),
            tool_calls: []
          }]
        });
      }

      // Execute burst
      for (let i = 0; i < burstSize; i++) {
        requests.push(model.invoke([new HumanMessage(`Burst request ${i}`)]));
      }

      const results = await Promise.all(requests);
      
      expect(results).toHaveLength(burstSize);
      
      const metrics = model.getHttpMetrics();
      expect(metrics.totalRequests).toBe(burstSize);
      expect(metrics.successfulRequests).toBe(burstSize);
      expect(metrics.successRate).toBe('100%');
    });

    it('should measure and report accurate latency metrics', async () => {
      const testLatencies = [100, 250, 500, 1000, 2000];
      
      for (const latency of testLatencies) {
        model.setMockResponse({
          generations: [{
            text: `Response with ${latency}ms latency`,
            message: new AIMessage({ content: `Response with ${latency}ms latency` }),
            tool_calls: []
          }]
        });
        
        const startTime = Date.now();
        vi.setSystemTime(startTime);
        
        const invokePromise = model.invoke([new HumanMessage('latency test')]);
        vi.advanceTimersByTime(latency);
        vi.setSystemTime(startTime + latency);
        
        await invokePromise;
      }

      const metrics = model.getHttpMetrics();
      expect(metrics.averageResponseTime).toBe(770); // (100+250+500+1000+2000)/5
      expect(metrics.totalResponseTime).toBe(3850);
    });

    it('should handle memory pressure scenarios', async () => {
      // Simulate processing many large messages
      const largeMessages = [];
      for (let i = 0; i < 100; i++) {
        largeMessages.push(new HumanMessage({
          content: `Large message ${i}: ${'X'.repeat(10000)}`
        }));
      }

      model.setMockResponse({
        generations: [{
          text: 'Handled large batch',
          message: new AIMessage({ content: 'Handled large batch' }),
          tool_calls: []
        }]
      });

      const result = await model.invoke(largeMessages);
      
      expect(result.generations[0].text).toBe('Handled large batch');
      
      const callHistory = model.getCallHistory();
      expect(callHistory[0].messages).toHaveLength(100);
    });
  });

  describe('Integration Edge Cases', () => {
    it('should handle cleanup during active requests', async () => {
      model.setMockResponse({
        generations: [{
          text: 'Response during cleanup',
          message: new AIMessage({ content: 'Response during cleanup' }),
          tool_calls: []
        }]
      });

      const invokePromise = model.invoke([new HumanMessage('test')]);
      
      // Trigger cleanup while request is in flight
      const cleanupPromise = model.cleanup();
      
      const [result] = await Promise.all([invokePromise, cleanupPromise]);
      
      expect(result.generations[0].text).toBe('Response during cleanup');
      
      // Metrics should be reset after cleanup
      const metrics = model.getHttpMetrics();
      expect(metrics.totalRequests).toBe(0);
    });

    it('should handle configuration changes between requests', async () => {
      // First request with original config
      model.setMockResponse({
        generations: [{
          text: 'First response',
          message: new AIMessage({ content: 'First response' }),
          tool_calls: []
        }]
      });
      
      await model.invoke([new HumanMessage('first')]);
      expect(model.httpConfig.timeout).toBe(1000);
      
      // Change configuration
      model.httpConfig.timeout = 2000;
      model.httpConfig.retryAttempts = 5;
      
      model.setMockResponse({
        generations: [{
          text: 'Second response',
          message: new AIMessage({ content: 'Second response' }),
          tool_calls: []
        }]
      });
      
      await model.invoke([new HumanMessage('second')]);
      
      const metrics = model.getMetrics();
      expect(metrics.config.timeout).toBe(2000);
      expect(metrics.config.retryAttempts).toBe(5);
    });
  });
});