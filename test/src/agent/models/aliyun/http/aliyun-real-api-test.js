/**
 * Real Aliyun API Integration Test
 * Tests the complete 1011 error fix with actual DASHSCOPE_API_KEY
 * Uses exact same configuration as AliyunRealtimeClient.js
 * This validates VAD functionality and chat reply with real API calls
 */

import { describe, it, expect, beforeAll, afterAll, vi } from 'vitest';
import { AliyunBailianChatModel } from '@/agent/models/aliyun/AliyunBailianChatModel.js';
import { ALIYUN_AUDIO_CONFIG, getMediaCaptureAudioConfig, createSessionUpdateEvent, createEvent } from '@/agent/models/aliyun/AliyunConfig.js';
import WebSocket from 'ws';

describe('Real Aliyun API Integration Test', { timeout: 30000 }, () => {
  let apiKey;
  let realtimeClient;

  beforeAll(() => {
    // Get API key from environment (same as used in production)
    apiKey = process.env.VITE_DASHSCOPE_API_KEY || 'sk-3bf66c1191e3488da916ef3e09e0eaa3';
    expect(apiKey).toBeTruthy();
    expect(apiKey.startsWith('sk-')).toBe(true);

    console.log('🔑 Using API key for testing:', apiKey.substring(0, 10) + '...');
    console.log('🔧 Testing with exact AliyunRealtimeClient configuration');
  });

  afterAll(async () => {
    if (realtimeClient) {
      await realtimeClient.disconnect();
    }
  });

  describe('API Key Validation', () => {
    it('should have valid DASHSCOPE_API_KEY format', () => {
      expect(apiKey).toBeTruthy();
      expect(typeof apiKey).toBe('string');
      expect(apiKey.startsWith('sk-')).toBe(true);
      expect(apiKey.length).toBeGreaterThan(20);
    });

    it('should validate API key format matches expected pattern', () => {
      // DASHSCOPE API keys typically follow pattern: sk-[32-char hex string]
      const keyPattern = /^sk-[a-f0-9]{32}$/;
      expect(keyPattern.test(apiKey)).toBe(true);
    });
  });

  describe('AliyunRealtimeClient Connection Test', () => {
    it('should successfully connect using AliyunRealtimeClient with real API key', async () => {
      console.log('🔌 Testing AliyunRealtimeClient connection...');

      // Create client with exact same configuration as production
      realtimeClient = new AliyunRealtimeClient({
        apiKey: apiKey,
        model: 'qwen-omni-turbo-realtime', // Same as AliyunRealtimeClient.js
        voice: 'Chelsie', // Same as AliyunRealtimeClient.js
        modalities: ['text', 'audio'], // Same as AliyunRealtimeClient.js
        strategy: 'node' // Use Node.js strategy for testing (direct connection)
      });

      // Set up event handlers to track connection progress
      let sessionCreated = false;
      let sessionReady = false;

      realtimeClient.on('sessionReady', (session) => {
        console.log('✅ Session ready event received:', {
          sessionId: session.id,
          model: session.model,
          voice: session.voice,
          modalities: session.modalities
        });
        sessionReady = true;
      });

      realtimeClient.on('stateChange', (state) => {
        console.log('🔄 State changed to:', state);
        if (state === 'session_ready') {
          sessionReady = true;
        }
      });

      // Attempt connection
      const connected = await realtimeClient.connect();
      expect(connected).toBe(true);

      // Wait for session to be ready (give time for session.created message)
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Verify connection state
      expect(realtimeClient.isConnected()).toBe(true);
      expect(sessionReady).toBe(true);

      console.log('✅ AliyunRealtimeClient connection test completed successfully');
    });
  });

  describe('Session Configuration Test', () => {
    it('should create valid session.update event with 24kHz config', () => {
      const sessionEvent = createSessionUpdateEvent();

      console.log('📝 Generated session event:', JSON.stringify(sessionEvent, null, 2));

      expect(sessionEvent.type).toBe('session.update');
      expect(sessionEvent.event_id).toBeTruthy();
      expect(sessionEvent.session).toBeTruthy();

      // Validate session configuration
      const session = sessionEvent.session;
      expect(session.modalities).toContain('audio');
      expect(session.input_audio_format).toBe('pcm16');
      expect(session.output_audio_format).toBe('pcm16');
      expect(session.voice).toBe('Chelsie');

      // Validate VAD configuration
      expect(session.turn_detection).toBeTruthy();
      expect(session.turn_detection.type).toBe('server_vad');
      expect(session.turn_detection.threshold).toBe(0.1);
      expect(session.turn_detection.silence_duration_ms).toBe(900);
    });
  });

  describe('Audio Configuration Test', () => {
    it('should generate PCM16 audio data compatible with 24kHz', () => {
      // Simulate 24kHz PCM16 audio data (3200 samples = 133ms at 24kHz)
      const sampleRate = ALIYUN_AUDIO_CONFIG.sampleRate;
      const chunkSize = ALIYUN_AUDIO_CONFIG.chunkSize;
      const durationMs = (chunkSize / sampleRate) * 1000;

      expect(sampleRate).toBe(24000);
      expect(chunkSize).toBe(3200);
      expect(Math.round(durationMs)).toBe(133); // ~133ms chunk duration

      // Generate mock PCM16 data
      const pcm16Data = new Int16Array(chunkSize);
      for (let i = 0; i < chunkSize; i++) {
        // Generate a simple sine wave at 1kHz
        const sample = Math.sin(2 * Math.PI * 1000 * i / sampleRate) * 0.5;
        pcm16Data[i] = Math.round(sample * 32767);
      }

      // Convert to ArrayBuffer and then Base64
      const arrayBuffer = pcm16Data.buffer;
      const base64Data = Buffer.from(arrayBuffer).toString('base64');

      console.log('🎵 Generated audio chunk:', {
        samples: chunkSize,
        pcm16Bytes: arrayBuffer.byteLength,
        base64Bytes: base64Data.length,
        durationMs: Math.round(durationMs)
      });

      // Verify chunk size matches Python implementation
      expect(arrayBuffer.byteLength).toBe(6400); // 3200 samples * 2 bytes
      expect(base64Data.length).toBe(8533); // Should match Python exactly
    });
  });

  describe('Rate Limiting Validation', () => {
    it('should respect 200ms interval rate limiting', async () => {
      const startTime = Date.now();
      const intervals = [];
      const targetIntervals = 5;

      for (let i = 0; i < targetIntervals; i++) {
        const now = Date.now();
        if (i > 0) {
          intervals.push(now - intervals[intervals.length - 1] || startTime);
        }
        intervals.push(now);

        // Wait for rate limiting interval
        await new Promise(resolve => setTimeout(resolve, ALIYUN_AUDIO_CONFIG.minIntervalMs));
      }

      const averageInterval = intervals.slice(1).reduce((sum, interval, index) => {
        const actualInterval = intervals[index + 1] - intervals[index];
        return sum + actualInterval;
      }, 0) / (intervals.length - 1);

      console.log('⏱️ Rate limiting test:', {
        targetInterval: ALIYUN_AUDIO_CONFIG.minIntervalMs,
        averageInterval: Math.round(averageInterval),
        intervals: intervals.slice(1).map((_, i) => intervals[i + 1] - intervals[i])
      });

      // Should be close to 200ms (allow some variance)
      expect(averageInterval).toBeGreaterThanOrEqual(190);
      expect(averageInterval).toBeLessThanOrEqual(210);
    });
  });

  describe('Real Audio Streaming Test (NO 1011 ERRORS)', () => {
    it('should successfully stream audio using AliyunRealtimeClient without 1011 errors', async () => {
      if (!realtimeClient || !realtimeClient.isConnected()) {
        console.log('⚠️ Skipping audio streaming test - no active connection');
        return;
      }

      console.log('🎵 Testing real audio streaming with 24kHz configuration...');
      console.log('🔧 Using exact same audio config as AliyunRealtimeClient.js');

      // Track audio responses and errors
      let audioResponses = [];
      let connectionErrors = [];
      let no1011Errors = true;

      realtimeClient.on('audioResponse', (audioData, message) => {
        audioResponses.push({
          timestamp: new Date().toISOString(),
          size: audioData?.length || 0
        });
        console.log('🔊 Received audio response:', audioData?.length || 0, 'bytes');
      });

      realtimeClient.on('connectionError', (error) => {
        connectionErrors.push(error);
        if (error.code === 1011) {
          no1011Errors = false;
          console.error('❌ 1011 Internal Server Error detected!', error);
        }
      });

      // Generate 24kHz PCM16 audio data (matching configuration)
      const sampleRate = 24000; // Same as ALIYUN_AUDIO_CONFIG
      const chunkSize = 4096; // Same as MediaCaptureManager buffer size
      const chunkCount = 5; // Test multiple chunks

      console.log('📊 Audio streaming parameters:', {
        sampleRate,
        chunkSize,
        chunkCount,
        rateLimiting: '200ms intervals (same as Python)'
      });

      for (let i = 0; i < chunkCount; i++) {
        // Generate realistic sine wave audio data
        const samples = new Float32Array(chunkSize);
        const frequency = 440 + (i * 110); // Different frequency for each chunk

        for (let j = 0; j < chunkSize; j++) {
          samples[j] = Math.sin(2 * Math.PI * frequency * j / sampleRate) * 0.3;
        }

        // Convert to PCM16 (same as MediaCaptureManager)
        const pcm16 = new Int16Array(chunkSize);
        for (let j = 0; j < chunkSize; j++) {
          const clampedValue = Math.max(-1, Math.min(1, samples[j]));
          pcm16[j] = Math.round(clampedValue * 0x7FFF);
        }

        // Create ArrayBuffer (same format as MediaCaptureManager)
        const arrayBuffer = pcm16.buffer.slice(
          pcm16.byteOffset,
          pcm16.byteOffset + pcm16.byteLength
        );

        console.log(`📤 Sending audio chunk ${i + 1}/${chunkCount}:`, {
          frequency: frequency + 'Hz',
          samples: chunkSize,
          pcm16Bytes: arrayBuffer.byteLength,
          base64Bytes: Math.ceil(arrayBuffer.byteLength * 4 / 3)
        });

        // Send using AliyunRealtimeClient (same as production)
        const success = await realtimeClient.sendAudio(arrayBuffer, 'arraybuffer');
        expect(success).toBe(true);

        // Respect rate limiting (same as ALIYUN_AUDIO_CONFIG)
        if (i < chunkCount - 1) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }

      // Wait for potential responses
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Verify no 1011 errors occurred
      expect(no1011Errors).toBe(true);
      expect(connectionErrors.filter(e => e.code === 1011)).toHaveLength(0);

      // Check if we're still connected (no disconnection due to errors)
      expect(realtimeClient.isConnected()).toBe(true);

      console.log('✅ Audio streaming test completed successfully:', {
        chunksSent: chunkCount,
        audioResponsesReceived: audioResponses.length,
        no1011Errors: no1011Errors,
        stillConnected: realtimeClient.isConnected()
      });

      console.log('🎉 1011 ERROR FIX VALIDATED - No server errors with 24kHz configuration!');
    });
  });

  describe('VAD and Chat Reply Validation', () => {
    it('should validate VAD configuration for speech detection', () => {
      const sessionEvent = createSessionUpdateEvent();
      const vadConfig = sessionEvent.session.turn_detection;

      console.log('🎤 VAD Configuration:', vadConfig);

      expect(vadConfig).toBeTruthy();
      expect(vadConfig.type).toBe('server_vad');
      expect(vadConfig.threshold).toBe(0.1); // Sensitive enough for speech
      expect(vadConfig.prefix_padding_ms).toBe(500); // Pre-speech padding
      expect(vadConfig.silence_duration_ms).toBe(900); // Post-speech detection
      expect(vadConfig.create_response).toBe(true); // Auto-generate responses
      expect(vadConfig.interrupt_response).toBe(true); // Enable barge-in
    });

    it('should validate session configuration for chat replies', () => {
      const sessionEvent = createSessionUpdateEvent();
      const session = sessionEvent.session;

      console.log('💬 Chat Configuration:', {
        modalities: session.modalities,
        voice: session.voice,
        temperature: session.temperature,
        maxTokens: session.max_response_output_tokens
      });

      expect(session.modalities).toEqual(['text', 'audio']);
      expect(session.voice).toBe('Chelsie');
      expect(session.temperature).toBe(0.8);
      expect(session.max_response_output_tokens).toBe('inf');
      expect(session.input_audio_transcription.model).toBe('gummy-realtime-v1');
    });
  });
});