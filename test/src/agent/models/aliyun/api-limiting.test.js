/**
 * API Limiting Test for Aliyun Models
 * Tests that both HTTP and WebSocket models properly apply BaseChatModel API limits
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { AliyunHttpChatModel } from '@/agent/models/aliyun/AliyunHttpChatModel.js';
import { AliyunWebSocketChatModel } from '@/agent/models/aliyun/AliyunWebSocketChatModel.js';
import { HumanMessage } from '@langchain/core/messages';

describe('Aliyun Models API Limiting', () => {

    beforeEach(() => {
        // Reset environment variables for each test
        process.env.VITE_API_MAX_ATTEMPTS = '2'; // Low limit for testing
        process.env.VITE_API_TEST_MODE = 'true';
        process.env.VITE_API_RATE_LIMIT_ENABLED = 'true';
    });

    it('should apply API limits to AliyunHttpChatModel', async () => {
        const httpModel = new AliyunHttpChatModel({
            apiKey: 'test-key',
            model: 'qwen-turbo'
        });

        const testMessage = [new HumanMessage('Test message')];

        // First attempt should fail due to missing server
        let attempt1Error = null;
        try {
            await httpModel.invoke(testMessage);
        } catch (error) {
            attempt1Error = error;
        }

        // Second attempt should fail due to missing server
        let attempt2Error = null;
        try {
            await httpModel.invoke(testMessage);
        } catch (error) {
            attempt2Error = error;
        }

        // Third attempt should fail due to API limits
        let attempt3Error = null;
        try {
            await httpModel.invoke(testMessage);
        } catch (error) {
            attempt3Error = error;
        }

        // Verify the third attempt failed due to API limits
        expect(attempt3Error).toBeTruthy();
        expect(attempt3Error.message).toContain('API limit reached');
        expect(attempt3Error.message).toContain('2/2 attempts used');

        // Verify we have the expected API limit tracking
        // Counter shows 2 because the 3rd attempt was blocked before incrementing
        expect(httpModel.apiLimits.currentAttempts).toBe(2);
        expect(httpModel.apiLimits.maxAttempts).toBe(2);
        expect(httpModel.apiLimits.testMode).toBe(true);
    });

    it('should apply API limits to AliyunWebSocketChatModel', async () => {
        const wsModel = new AliyunWebSocketChatModel({
            apiKey: 'test-key',
            model: 'qwen-omni-turbo-realtime-2025-05-08'
        });

        const testMessage = [new HumanMessage('Test message')];

        // First attempt should fail due to missing connection
        let attempt1Error = null;
        try {
            await wsModel.invoke(testMessage);
        } catch (error) {
            attempt1Error = error;
        }

        // Second attempt should fail due to missing connection
        let attempt2Error = null;
        try {
            await wsModel.invoke(testMessage);
        } catch (error) {
            attempt2Error = error;
        }

        // Third attempt should fail due to API limits
        let attempt3Error = null;
        try {
            await wsModel.invoke(testMessage);
        } catch (error) {
            attempt3Error = error;
        }

        // Verify the third attempt failed due to API limits
        expect(attempt3Error).toBeTruthy();
        expect(attempt3Error.message).toContain('API limit reached');
        expect(attempt3Error.message).toContain('2/2 attempts used');

        // Verify we have the expected API limit tracking
        // Counter shows 2 because the 3rd attempt was blocked before incrementing
        expect(wsModel.apiLimits.currentAttempts).toBe(2);
        expect(wsModel.apiLimits.maxAttempts).toBe(2);
        expect(wsModel.apiLimits.testMode).toBe(true);
    });

    it('should allow disabling API limits via environment variables', async () => {
        // Disable test mode
        process.env.VITE_API_TEST_MODE = 'false';

        const httpModel = new AliyunHttpChatModel({
            apiKey: 'test-key',
            model: 'qwen-turbo'
        });

        const testMessage = [new HumanMessage('Test message')];

        // Multiple attempts should all fail due to missing server, not API limits
        for (let i = 0; i < 5; i++) {
            let error = null;
            try {
                await httpModel.invoke(testMessage);
            } catch (err) {
                error = err;
            }

            // Should not fail due to API limits when test mode is disabled
            expect(error.message).not.toContain('API limit reached');
        }

        // Verify API limits are disabled
        expect(httpModel.apiLimits.testMode).toBe(false);
        expect(httpModel.apiLimits.currentAttempts).toBeGreaterThan(2);
    });

    it('should respect VITE_API_MAX_ATTEMPTS setting', async () => {
        // Set higher limit
        process.env.VITE_API_MAX_ATTEMPTS = '5';

        const httpModel = new AliyunHttpChatModel({
            apiKey: 'test-key',
            model: 'qwen-turbo'
        });

        expect(httpModel.apiLimits.maxAttempts).toBe(5);

        const testMessage = [new HumanMessage('Test message')];

        // Should allow 5 attempts before hitting limit
        for (let i = 0; i < 5; i++) {
            try {
                await httpModel.invoke(testMessage);
            } catch (error) {
                // Expected to fail due to missing server, not API limits
                expect(error.message).not.toContain('API limit reached');
            }
        }

        // 6th attempt should hit API limit
        let limitError = null;
        try {
            await httpModel.invoke(testMessage);
        } catch (error) {
            limitError = error;
        }

        expect(limitError).toBeTruthy();
        expect(limitError.message).toContain('API limit reached');
        expect(limitError.message).toContain('5/5 attempts used');
    });
});