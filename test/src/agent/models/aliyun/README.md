# Aliyun Tests

This directory contains tests for the Aliyun DashScope integration, focusing on the current `AliyunHttpChatModel` and `AliyunWebSocketChatModel` implementations.

## Test Organization

The tests are organized into the following categories:

### Core Functionality Tests
- `AliyunConfig.test.js` - Configuration constants and validation
- `AliyunModelFactory.test.js` - Model factory and selection logic
- `AliyunHttpChatModel.test.js` - HTTP model functionality
- `AliyunHttpChatModel.advanced.test.js` - Advanced HTTP scenarios
- `langchain-v3-compliance.test.js` - LangChain v3 compatibility tests
- `aliyun-api-validation.test.js` - API validation and parameter testing

### HTTP API Tests
- `http/aliyun-real-api-test.js` - Full integration tests with real API

### WebSocket Connection Tests
Note: WebSocket tests are planned for future implementation focusing on the current `AliyunWebSocketChatModel`.

## Running Tests

### Mock Tests
These tests don't require an API key and can be run with:
```bash
node test/run-tests.js aliyunModels
```

### Real API Tests
These tests require an Aliyun API key and can be run with:
```bash
VITE_DASHSCOPE_API_KEY=your_api_key node test/run-tests.js aliyunRealApi
```

### Enhanced Test Runner Commands
```bash
# Quick configuration tests only
node test/run-tests.js aliyunModels

# Real API integration tests
VITE_DASHSCOPE_API_KEY=sk-your-key node test/run-tests.js aliyunRealApi --verbose

# All tests with coverage
VITE_DASHSCOPE_API_KEY=sk-your-key node test/run-tests.js aliyunRealApi --coverage
```

## Test Utilities
Test utilities have been consolidated to reduce redundancy and improve maintainability around the current AliyunHttpChatModel and AliyunWebSocketChatModel implementations.

## Current Architecture
- **AliyunHttpChatModel**: HTTP-based chat model for standard API calls
- **AliyunWebSocketChatModel**: WebSocket-based model for realtime interactions  
- **AliyunConfig**: Centralized configuration management
- **AliyunModelFactory**: Intelligent model selection and creation

## Related Documentation
- Model implementations in `src/agent/models/aliyun/`
- Configuration details in `AliyunConfig.js`
