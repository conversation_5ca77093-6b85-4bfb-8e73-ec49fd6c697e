/**
 * Comprehensive AliyunConfig Test Suite
 * Tests configuration validation, audio parameters, session management, and VAD settings
 * Includes real API integration tests when API key is provided
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
    // Core configuration
    ALIYUN_AUDIO_CONFIG,
    ALIY<PERSON>_VAD_CONFIG,
    ALIYUN_WEBSOCKET_CONFIG,
    ALIYUN_HTTP_CONFIG,
    ALIYUN_SAMPLE_RATE,
    ALIYUN_SYSTEM_PROMPTS,
    ALIYUN_MODELS,
    
    // State constants
    ConnectionState,
    SessionState,
    VADState,
    AliyunEventType,
    SessionMode,
    SupportedVoices,
    VoiceGenderMap,
    WebSocketCloseCodes,
    
    // Utility functions
    generateEventId,
    buildValidTurnDetection,
    buildValidSessionConfig,
    createSessionUpdateEvent,
    createEvent,
    getMediaCaptureAudioConfig,
    getRealtimeClientConfig,
    validateAudioConfig,
    createDefaultConfig,
    createMediaConfig,
    createRateLimitConfig,
    createErrorRecoveryConfig,
    createError,
    validateConfig,
    validateHttpConfig,
    sendAudioWithRateLimit,
    cleanupRealtimeConnection,
    createPythonCompatibleSessionUpdate,
    getWebSocketProxyConfig,
    getHttpEndpoint,
    getServerHttpEndpoint,
    createHybridConfig,
    validateHybridConfig
} from '@/agent/models/aliyun/AliyunConfig.js';

const REAL_API_KEY = process.env.VITE_DASHSCOPE_API_KEY;
const TEST_TIMEOUT = 10000; // 10 seconds for real API calls

describe('AliyunConfig - Core Configuration', () => {
    describe('Audio Configuration', () => {
        it('should have correct audio parameters for VAD', () => {
            expect(ALIYUN_AUDIO_CONFIG.sampleRate).toBe(16000);
            expect(ALIYUN_AUDIO_CONFIG.bitDepth).toBe(16);
            expect(ALIYUN_AUDIO_CONFIG.numChannels).toBe(1);
            expect(ALIYUN_AUDIO_CONFIG.channels).toBe(1);
            expect(ALIYUN_AUDIO_CONFIG.format).toBe('pcm16');
            expect(ALIYUN_AUDIO_CONFIG.inputFormat).toBe('pcm16');
            expect(ALIYUN_AUDIO_CONFIG.outputFormat).toBe('pcm16');
        });

        it('should have correct rate limiting configuration', () => {
            expect(ALIYUN_AUDIO_CONFIG.minIntervalMs).toBe(200);
            expect(ALIYUN_AUDIO_CONFIG.maxChunksPerSecond).toBe(5);
            expect(ALIYUN_AUDIO_CONFIG.chunkSize).toBe(3200);
            expect(ALIYUN_AUDIO_CONFIG.chunkDurationMs).toBe(200);
        });

        it('should have valid audio context configuration', () => {
            const config = ALIYUN_AUDIO_CONFIG.audioContextConfig;
            expect(config.sampleRate).toBe(ALIYUN_SAMPLE_RATE);
            expect(config.echoCancellation).toBe(true);
            expect(config.noiseSuppression).toBe(true);
            expect(config.autoGainControl).toBe(true);
        });

        it('should have supported voices configured', () => {
            expect(ALIYUN_AUDIO_CONFIG.supportedVoices).toContain('Chelsie');
            expect(ALIYUN_AUDIO_CONFIG.supportedVoices).toContain('Serena');
            expect(ALIYUN_AUDIO_CONFIG.supportedVoices).toContain('Ethan');
            expect(ALIYUN_AUDIO_CONFIG.supportedVoices).toContain('Cherry');
            expect(ALIYUN_AUDIO_CONFIG.defaultVoice).toBe('Chelsie');
        });
    });

    describe('VAD Configuration', () => {
        it('should have correct VAD parameters matching Python implementation', () => {
            expect(ALIYUN_VAD_CONFIG.type).toBe('server_vad');
            expect(ALIYUN_VAD_CONFIG.threshold).toBe(0.1);
            expect(ALIYUN_VAD_CONFIG.prefix_padding_ms).toBe(500);
            expect(ALIYUN_VAD_CONFIG.silence_duration_ms).toBe(900);
            expect(ALIYUN_VAD_CONFIG.audio_format).toBe('pcm16');
        });

        it('should have correct transcription model', () => {
            expect(ALIYUN_VAD_CONFIG.transcription_model).toBe('gummy-realtime-v1');
        });

        it('should have rate limiting enabled', () => {
            expect(ALIYUN_VAD_CONFIG.rateLimitingEnabled).toBe(true);
            expect(ALIYUN_VAD_CONFIG.minTimeBetweenSends).toBe(90);
        });

        it('should have debug options configured', () => {
            expect(ALIYUN_VAD_CONFIG.debug.logVADEvents).toBe(true);
            expect(ALIYUN_VAD_CONFIG.debug.trackMessageTypes).toBe(true);
        });
    });

    describe('WebSocket Configuration', () => {
        it('should have correct connection timing', () => {
            expect(ALIYUN_WEBSOCKET_CONFIG.pingInterval).toBe(20000);
            expect(ALIYUN_WEBSOCKET_CONFIG.pingTimeout).toBe(10000);
            expect(ALIYUN_WEBSOCKET_CONFIG.handshakeTimeout).toBe(10000);
        });

        it('should have session stabilization delays', () => {
            expect(ALIYUN_WEBSOCKET_CONFIG.sessionUpdateDelay.client).toBe(500);
            expect(ALIYUN_WEBSOCKET_CONFIG.sessionUpdateDelay.queued).toBe(1000);
        });

        it('should have correct endpoint and model', () => {
            expect(ALIYUN_WEBSOCKET_CONFIG.endpoint).toBe('wss://dashscope.aliyuncs.com/api-ws/v1/realtime');
            expect(ALIYUN_WEBSOCKET_CONFIG.defaultModel).toBe('qwen-omni-turbo-realtime');
        });

        it('should have correct default session config matching Python', () => {
            const sessionConfig = ALIYUN_WEBSOCKET_CONFIG.defaultSessionConfig;
            expect(sessionConfig.modalities).toEqual(['text', 'audio']);
            expect(sessionConfig.voice).toBe('Chelsie');
            expect(sessionConfig.input_audio_format).toBe('pcm16');
            expect(sessionConfig.output_audio_format).toBe('pcm16');
            expect(sessionConfig.input_audio_transcription.model).toBe('gummy-realtime-v1');
            expect(sessionConfig.turn_detection.type).toBe('server_vad');
            expect(sessionConfig.turn_detection.threshold).toBe(0.1);
            expect(sessionConfig.turn_detection.prefix_padding_ms).toBe(500);
            expect(sessionConfig.turn_detection.silence_duration_ms).toBe(900);
        });
    });

    describe('HTTP Configuration', () => {
        it('should have correct model configurations', () => {
            expect(ALIYUN_HTTP_CONFIG.models.primary).toBe('qwen-plus');
            expect(ALIYUN_HTTP_CONFIG.models.fallback).toBe('qwen-turbo');
            expect(ALIYUN_HTTP_CONFIG.models.premium).toBe('qwen-max');
        });

        it('should have performance settings for autonomous tools', () => {
            expect(ALIYUN_HTTP_CONFIG.timeout).toBe(5000);
            expect(ALIYUN_HTTP_CONFIG.maxRetries).toBe(2);
            expect(ALIYUN_HTTP_CONFIG.retryDelay).toBe(100);
        });

        it('should have rate limiting configuration', () => {
            expect(ALIYUN_HTTP_CONFIG.rateLimiting.requestsPerSecond).toBe(10);
            expect(ALIYUN_HTTP_CONFIG.rateLimiting.burstLimit).toBe(20);
            expect(ALIYUN_HTTP_CONFIG.rateLimiting.windowSize).toBe(1000);
        });

        it('should have correct tool configuration', () => {
            expect(ALIYUN_HTTP_CONFIG.toolConfig.toolChoice).toBe('auto');
            expect(ALIYUN_HTTP_CONFIG.toolConfig.maxToolCalls).toBe(5);
            expect(ALIYUN_HTTP_CONFIG.toolConfig.toolTimeout).toBe(300);
        });
    });

    describe('Model Constants', () => {
        it('should have correct model mappings', () => {
            expect(ALIYUN_MODELS.TRANSCRIPTION).toBe('gummy-realtime-v1');
            expect(ALIYUN_MODELS.DEFAULT_REALTIME).toBe('qwen-omni-turbo-realtime');
            expect(ALIYUN_MODELS.QWEN_OMNI_TURBO).toBe('qwen-omni-turbo');
            expect(ALIYUN_MODELS.HTTP_PRIMARY).toBe('qwen-plus');
            expect(ALIYUN_MODELS.HTTP_FALLBACK).toBe('qwen-turbo');
            expect(ALIYUN_MODELS.HTTP_PREMIUM).toBe('qwen-max');
        });
    });

    describe('State Constants', () => {
        it('should have complete connection states', () => {
            const expectedStates = ['disconnected', 'connecting', 'connected', 'authenticated', 'ready', 'error', 'closing'];
            expectedStates.forEach(state => {
                expect(Object.values(ConnectionState)).toContain(state);
            });
        });

        it('should have complete session states', () => {
            const expectedStates = ['none', 'creating', 'created', 'updating', 'ready', 'error'];
            expectedStates.forEach(state => {
                expect(Object.values(SessionState)).toContain(state);
            });
        });

        it('should have complete VAD states', () => {
            const expectedStates = ['idle', 'listening', 'speech_detected', 'speech_ended', 'timeout'];
            expectedStates.forEach(state => {
                expect(Object.values(VADState)).toContain(state);
            });
        });
    });

    describe('Event Types', () => {
        it('should have all required session events', () => {
            expect(AliyunEventType.SESSION_CREATED).toBe('session.created');
            expect(AliyunEventType.SESSION_UPDATED).toBe('session.updated');
            expect(AliyunEventType.SESSION_UPDATE).toBe('session.update');
        });

        it('should have all required audio buffer events', () => {
            expect(AliyunEventType.INPUT_AUDIO_BUFFER_APPEND).toBe('input_audio_buffer.append');
            expect(AliyunEventType.INPUT_AUDIO_BUFFER_COMMIT).toBe('input_audio_buffer.commit');
            expect(AliyunEventType.INPUT_AUDIO_BUFFER_COMMITTED).toBe('input_audio_buffer.committed');
            expect(AliyunEventType.INPUT_AUDIO_BUFFER_CLEAR).toBe('input_audio_buffer.clear');
            expect(AliyunEventType.INPUT_AUDIO_BUFFER_CLEARED).toBe('input_audio_buffer.cleared');
        });

        it('should have correct VAD event names matching Python', () => {
            expect(AliyunEventType.INPUT_AUDIO_BUFFER_SPEECH_STARTED).toBe('input_audio_buffer.speech_started');
            expect(AliyunEventType.INPUT_AUDIO_BUFFER_SPEECH_STOPPED).toBe('input_audio_buffer.speech_stopped');
        });

        it('should have all required response events', () => {
            expect(AliyunEventType.RESPONSE_CREATE).toBe('response.create');
            expect(AliyunEventType.RESPONSE_CREATED).toBe('response.created');
            expect(AliyunEventType.RESPONSE_DONE).toBe('response.done');
            expect(AliyunEventType.RESPONSE_AUDIO_DELTA).toBe('response.audio.delta');
            expect(AliyunEventType.RESPONSE_AUDIO_DONE).toBe('response.audio.done');
            expect(AliyunEventType.RESPONSE_TEXT_DELTA).toBe('response.text.delta');
            expect(AliyunEventType.RESPONSE_TEXT_DONE).toBe('response.text.done');
        });
    });

    describe('Voice Configuration', () => {
        it('should have correct supported voices', () => {
            expect(SupportedVoices.CHELSIE).toBe('Chelsie');
            expect(SupportedVoices.SERENA).toBe('Serena');
            expect(SupportedVoices.ETHAN).toBe('Ethan');
            expect(SupportedVoices.CHERRY).toBe('Cherry');
        });

        it('should have correct gender mapping', () => {
            expect(VoiceGenderMap.female).toBe(SupportedVoices.SERENA);
            expect(VoiceGenderMap.male).toBe(SupportedVoices.ETHAN);
            expect(VoiceGenderMap.f).toBe(SupportedVoices.SERENA);
            expect(VoiceGenderMap.m).toBe(SupportedVoices.ETHAN);
        });
    });

    describe('WebSocket Close Codes', () => {
        it('should have correct close code descriptions', () => {
            expect(WebSocketCloseCodes[1000]).toBe('Normal closure');
            expect(WebSocketCloseCodes[1006]).toBe('Abnormal closure (connection lost)');
            expect(WebSocketCloseCodes[1011]).toBe('Server internal error');
            expect(WebSocketCloseCodes[4001]).toBe('Authentication failed');
            expect(WebSocketCloseCodes[4008]).toBe('Rate limit exceeded');
            expect(WebSocketCloseCodes[4009]).toBe('Audio format error');
        });
    });
});

describe('AliyunConfig - Utility Functions', () => {
    describe('Event ID Generation', () => {
        it('should generate unique event IDs', () => {
            const id1 = generateEventId();
            const id2 = generateEventId();
            
            expect(id1).not.toBe(id2);
            expect(id1).toMatch(/^event_\d+_[a-z0-9]+$/);
            expect(id2).toMatch(/^event_\d+_[a-z0-9]+$/);
        });

        it('should include timestamp and randomness', () => {
            const id = generateEventId();
            const parts = id.split('_');
            
            expect(parts).toHaveLength(3);
            expect(parts[0]).toBe('event');
            expect(parseInt(parts[1])).toBeGreaterThan(0);
            expect(parts[2].length).toBeGreaterThan(0);
        });
    });

    describe('Session Configuration Building', () => {
        it('should build valid turn detection config', () => {
            const turnDetection = buildValidTurnDetection();
            
            expect(turnDetection.type).toBe('server_vad');
            expect(turnDetection.threshold).toBe(0.1);
            expect(turnDetection.prefix_padding_ms).toBe(500);
            expect(turnDetection.silence_duration_ms).toBe(900);
            expect(turnDetection.create_response).toBe(true);
            expect(turnDetection.interrupt_response).toBe(true);
        });

        it('should build valid session config', () => {
            const session = buildValidSessionConfig();
            
            expect(session.modalities).toEqual(['text', 'audio']);
            expect(session.voice).toBe('Chelsie');
            expect(session.input_audio_format).toBe('pcm16');
            expect(session.output_audio_format).toBe('pcm16');
            expect(session.input_audio_transcription.model).toBe('gummy-realtime-v1');
            expect(session.turn_detection.type).toBe('server_vad');
            expect(session.tools).toEqual([]);
            expect(session.tool_choice).toBe('auto');
            expect(session.temperature).toBe(0.8);
        });

        it('should accept custom configurations', () => {
            const customSession = buildValidSessionConfig({
                voice: 'Ethan',
                temperature: 0.5,
                modalities: ['text']
            });
            
            expect(customSession.voice).toBe('Ethan');
            expect(customSession.temperature).toBe(0.5);
            expect(customSession.modalities).toEqual(['text']);
        });
    });

    describe('Event Creation', () => {
        it('should create properly formatted events', () => {
            const event = createEvent(AliyunEventType.SESSION_UPDATE, {
                session: { voice: 'Chelsie' }
            });
            
            expect(event.type).toBe('session.update');
            expect(event.session.voice).toBe('Chelsie');
            expect(event.event_id).toMatch(/^event_\d+_[a-z0-9]+$/);
        });

        it('should create session update events', () => {
            const sessionUpdate = createSessionUpdateEvent({
                voice: 'Ethan',
                temperature: 0.5
            });
            
            expect(sessionUpdate.type).toBe('session.update');
            expect(sessionUpdate.session.voice).toBe('Ethan');
            expect(sessionUpdate.session.temperature).toBe(0.5);
            expect(sessionUpdate.event_id).toBeTruthy();
        });
    });

    describe('Python Compatible Session Updates', () => {
        it('should create exact Python format', () => {
            const pythonUpdate = createPythonCompatibleSessionUpdate();
            
            expect(pythonUpdate.type).toBe('session.update');
            expect(pythonUpdate.session.modalities).toEqual(['text', 'audio']);
            expect(pythonUpdate.session.voice).toBe('Chelsie');
            expect(pythonUpdate.session.input_audio_format).toBe('pcm16');
            expect(pythonUpdate.session.output_audio_format).toBe('pcm16');
            expect(pythonUpdate.session.input_audio_transcription.model).toBe('gummy-realtime-v1');
            expect(pythonUpdate.session.turn_detection.type).toBe('server_vad');
            expect(pythonUpdate.session.turn_detection.threshold).toBe(0.1);
            expect(pythonUpdate.session.turn_detection.prefix_padding_ms).toBe(500);
            expect(pythonUpdate.session.turn_detection.silence_duration_ms).toBe(900);
        });

        it('should handle voice overrides', () => {
            const pythonUpdate = createPythonCompatibleSessionUpdate({ voice: 'Ethan' });
            expect(pythonUpdate.session.voice).toBe('Ethan');
        });

        it('should handle VAD disable', () => {
            const pythonUpdate = createPythonCompatibleSessionUpdate({ disableVAD: true });
            expect(pythonUpdate.session.turn_detection).toBe(null);
        });
    });

    describe('Audio Configuration Validation', () => {
        it('should validate correct audio config', () => {
            const validation = validateAudioConfig({
                sampleRate: 16000,
                bitDepth: 16,
                channels: 1
            });
            
            expect(validation.isValid).toBe(true);
            expect(validation.errors).toHaveLength(0);
        });

        it('should detect invalid sample rate', () => {
            const validation = validateAudioConfig({
                sampleRate: 44100,
                bitDepth: 16,
                channels: 1
            });
            
            expect(validation.isValid).toBe(false);
            expect(validation.errors).toContain(expect.stringContaining('Sample rate must be 16000Hz'));
        });

        it('should detect invalid bit depth', () => {
            const validation = validateAudioConfig({
                sampleRate: 16000,
                bitDepth: 24,
                channels: 1
            });
            
            expect(validation.isValid).toBe(false);
            expect(validation.errors).toContain(expect.stringContaining('Bit depth must be 16-bit'));
        });

        it('should detect invalid channel count', () => {
            const validation = validateAudioConfig({
                sampleRate: 16000,
                bitDepth: 16,
                channels: 2
            });
            
            expect(validation.isValid).toBe(false);
            expect(validation.errors).toContain(expect.stringContaining('Audio must be mono'));
        });

        it('should warn about VAD threshold range', () => {
            const validation = validateAudioConfig({
                sampleRate: 16000,
                bitDepth: 16,
                channels: 1,
                vadThreshold: 2.0
            });
            
            expect(validation.isValid).toBe(true);
            expect(validation.warnings).toContain(expect.stringContaining('VAD threshold should be in range'));
        });
    });

    describe('Configuration Validation', () => {
        it('should validate complete valid config', () => {
            const config = {
                apiKey: 'test-api-key',
                model: 'qwen-omni-turbo-realtime',
                realtimeEndpoint: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime',
                temperature: 0.8,
                maxTokens: 2048
            };
            
            const validation = validateConfig(config);
            expect(validation.isValid).toBe(true);
            expect(validation.errors).toHaveLength(0);
        });

        it('should detect missing API key', () => {
            const config = {
                model: 'qwen-omni-turbo-realtime',
                realtimeEndpoint: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime'
            };
            
            const validation = validateConfig(config);
            expect(validation.isValid).toBe(false);
            expect(validation.errors).toContain('API key is required');
        });

        it('should detect invalid temperature', () => {
            const config = {
                apiKey: 'test-key',
                model: 'qwen-omni-turbo-realtime',
                realtimeEndpoint: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime',
                temperature: 3.0
            };
            
            const validation = validateConfig(config);
            expect(validation.isValid).toBe(false);
            expect(validation.errors).toContain('Temperature must be between 0 and 2');
        });
    });

    describe('HTTP Configuration Validation', () => {
        it('should validate HTTP config', () => {
            const config = {
                apiKey: 'test-key',
                model: 'qwen-plus',
                temperature: 0.7,
                maxTokens: 2000,
                http: {
                    endpoint: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
                    timeout: 500
                }
            };
            
            const validation = validateHttpConfig(config);
            expect(validation.isValid).toBe(true);
            expect(validation.errors).toHaveLength(0);
        });

        it('should detect missing HTTP endpoint', () => {
            const config = {
                apiKey: 'test-key',
                model: 'qwen-plus',
                http: {}
            };
            
            const validation = validateHttpConfig(config);
            expect(validation.isValid).toBe(false);
            expect(validation.errors).toContain('HTTP endpoint is required');
        });

        it('should detect invalid HTTP timeout', () => {
            const config = {
                apiKey: 'test-key',
                model: 'qwen-plus',
                http: {
                    endpoint: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
                    timeout: 50
                }
            };
            
            const validation = validateHttpConfig(config);
            expect(validation.isValid).toBe(false);
            expect(validation.errors).toContain('HTTP timeout must be at least 100ms');
        });
    });

    describe('Hybrid Configuration', () => {
        it('should create hybrid config with defaults', () => {
            const config = createHybridConfig({ apiKey: 'test-key' });
            
            expect(config.apiKey).toBe('test-key');
            expect(config.model).toBe(ALIYUN_MODELS.DEFAULT_REALTIME);
            expect(config.http.models.primary).toBe(ALIYUN_MODELS.HTTP_PRIMARY);
            expect(config.websocket.endpoint).toBe(ALIYUN_WEBSOCKET_CONFIG.endpoint);
            expect(config.modelSelection.autonomousTools).toBe('http');
            expect(config.modelSelection.voiceInteraction).toBe('websocket');
        });

        it('should validate hybrid config', () => {
            const config = createHybridConfig({ apiKey: 'test-key' });
            const validation = validateHybridConfig(config);
            
            expect(validation.isValid).toBe(true);
            expect(validation.errors).toHaveLength(0);
        });

        it('should detect performance conflicts in hybrid config', () => {
            const config = createHybridConfig({
                apiKey: 'test-key',
                http: { timeout: 1000 },
                performance: { maxResponseTime: 500 }
            });
            
            const validation = validateHybridConfig(config);
            expect(validation.warnings).toContain(expect.stringContaining('HTTP timeout'));
        });
    });

    describe('Rate Limited Audio Transmission', () => {
        it('should enforce rate limiting', () => {
            const state = { lastSendTime: null };
            const mockSendFunction = vi.fn(() => true);
            const audioData = new ArrayBuffer(3200);
            
            // First call should succeed
            const result1 = sendAudioWithRateLimit(mockSendFunction, audioData, state);
            expect(result1).toBe(true);
            expect(mockSendFunction).toHaveBeenCalledTimes(1);
            
            // Immediate second call should be rate limited
            const result2 = sendAudioWithRateLimit(mockSendFunction, audioData, state);
            expect(result2).toBe(false);
            expect(mockSendFunction).toHaveBeenCalledTimes(1); // Still only one call
        });

        it('should allow transmission after interval', async () => {
            const state = { lastSendTime: Date.now() - 250 }; // 250ms ago
            const mockSendFunction = vi.fn(() => true);
            const audioData = new ArrayBuffer(3200);
            
            const result = sendAudioWithRateLimit(mockSendFunction, audioData, state);
            expect(result).toBe(true);
            expect(mockSendFunction).toHaveBeenCalledTimes(1);
        });
    });

    describe('Connection Cleanup', () => {
        it('should cleanup connection state', () => {
            const mockConnection = {
                readyState: 1,
                send: vi.fn(),
                close: vi.fn()
            };
            
            const state = {
                sessionStabilized: true,
                sessionId: 'test-session',
                sessionConfig: { voice: 'Chelsie' },
                hasReceivedVadEvents: true,
                lastAudioSendTime: Date.now()
            };
            
            cleanupRealtimeConnection(mockConnection, state);
            
            expect(mockConnection.send).toHaveBeenCalledWith(expect.stringContaining('input_audio_buffer.clear'));
            expect(mockConnection.close).toHaveBeenCalledWith(1000, 'User stopped listening');
            expect(state.sessionStabilized).toBe(false);
            expect(state.sessionId).toBe(null);
            expect(state.sessionConfig).toBe(null);
            expect(state.hasReceivedVadEvents).toBe(false);
            expect(state.lastAudioSendTime).toBe(0);
        });
    });

    describe('Endpoint Selection', () => {
        it('should provide correct HTTP endpoints', () => {
            const endpoint = getHttpEndpoint();
            expect(endpoint).toMatch(/^https?:\/\//);
            
            const serverEndpoint = getServerHttpEndpoint();
            expect(serverEndpoint).toBe('https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation');
        });
    });
});

describe('AliyunConfig - Configuration Builders', () => {
    describe('Default Configuration', () => {
        it('should create complete default config', () => {
            const config = createDefaultConfig();
            
            expect(config.apiKey).toBe('');
            expect(config.model).toBe('qwen-omni-turbo-realtime');
            expect(config.realtimeEndpoint).toBe('wss://dashscope.aliyuncs.com/api-ws/v1/realtime');
            expect(config.temperature).toBe(0.7);
            expect(config.maxTokens).toBe(2048);
            expect(config.streaming).toBe(true);
            expect(config.audioConfig.sampleRate).toBe(16000);
            expect(config.audioConfig.channels).toBe(1);
            expect(config.audioConfig.bitDepth).toBe(16);
            expect(config.audioConfig.format).toBe('pcm16');
        });
    });

    describe('Media Configuration', () => {
        it('should create media config with correct audio parameters', () => {
            const config = createMediaConfig();
            
            expect(config.audio.sampleRate).toBe(16000);
            expect(config.audio.channels).toBe(1);
            expect(config.audio.bitDepth).toBe(16);
            expect(config.audio.encoding).toBe('base64');
        });

        it('should have correct image configuration', () => {
            const config = createMediaConfig();
            
            expect(config.image.formats).toEqual(['jpg', 'jpeg']);
            expect(config.image.maxSizeKB).toBe(500);
            expect(config.image.encoding).toBe('base64');
            expect(config.image.rateLimit).toBe(2);
        });

        it('should have correct video configuration', () => {
            const config = createMediaConfig();
            
            expect(config.video.frameRate).toBe(2);
            expect(config.video.maxFrames).toBe(30);
            expect(config.video.quality).toBe(0.8);
        });
    });

    describe('Rate Limit Configuration', () => {
        it('should create rate limit config with audio limits', () => {
            const config = createRateLimitConfig();
            
            expect(config.audio.maxChunksPerSecond).toBe(5);
            expect(config.audio.minIntervalMs).toBe(200);
            expect(config.audio.maxTokensPerSecond).toBe(10);
            expect(config.audio.windowSizeMs).toBe(1000);
            expect(config.audio.maxQueueSize).toBe(50);
        });

        it('should create rate limit config with image limits', () => {
            const config = createRateLimitConfig();
            
            expect(config.image.maxImagesPerSecond).toBe(2);
            expect(config.image.minIntervalMs).toBe(500);
            expect(config.image.windowSizeMs).toBe(1000);
            expect(config.image.maxQueueSize).toBe(10);
        });
    });

    describe('Error Recovery Configuration', () => {
        it('should create error recovery config', () => {
            const config = createErrorRecoveryConfig();
            
            expect(config.maxRetries).toBe(3);
            expect(config.retryDelayMs).toBe(2000);
            expect(config.exponentialBackoff).toBe(true);
            expect(config.maxRetryDelayMs).toBe(10000);
        });

        it('should have error-specific recovery strategies', () => {
            const config = createErrorRecoveryConfig();
            
            expect(config.errorRecoveryStrategies[1011]).toBe('session_config_recovery');
            expect(config.errorRecoveryStrategies[1006]).toBe('connection_retry');
            expect(config.errorRecoveryStrategies[4001]).toBe('auth_refresh');
            expect(config.errorRecoveryStrategies[4008]).toBe('rate_limit_backoff');
            expect(config.errorRecoveryStrategies[4009]).toBe('audio_format_fix');
        });
    });

    describe('Media Capture Configuration', () => {
        it('should convert to MediaCaptureManager format', () => {
            const config = getMediaCaptureAudioConfig();
            
            expect(config.audio.sampleRate).toBe(16000);
            expect(config.audio.channels).toBe(1);
            expect(config.audio.bitDepth).toBe(16);
            expect(config.vadMode).toBe('server');
            expect(config.vadConfig.threshold).toBe(0.1);
            expect(config.targetSampleRate).toBe(16000);
            expect(config.chunkSize).toBe(3200);
        });

        it('should accept overrides', () => {
            const config = getMediaCaptureAudioConfig({
                sampleRate: 24000,
                vadThreshold: 0.2,
                chunkSize: 1600
            });
            
            expect(config.audio.sampleRate).toBe(24000);
            expect(config.vadConfig.threshold).toBe(0.2);
            expect(config.chunkSize).toBe(1600);
        });
    });

    describe('Realtime Client Configuration', () => {
        it('should create realtime client config', () => {
            const config = getRealtimeClientConfig();
            
            expect(config.audioConfig.sampleRate).toBe(16000);
            expect(config.audioConfig.channels).toBe(1);
            expect(config.audioConfig.bitDepth).toBe(16);
            expect(config.vadConfig.threshold).toBe(0.1);
            expect(config.model).toBe('qwen-omni-turbo-realtime');
            expect(config.vadMode).toBe('server');
        });
    });

    describe('Error Creation', () => {
        it('should create error objects', () => {
            const error = createError('test_error', 'Test message', 500, { detail: 'test' });
            
            expect(error.type).toBe('test_error');
            expect(error.message).toBe('Test message');
            expect(error.code).toBe(500);
            expect(error.details.detail).toBe('test');
            expect(error.timestamp).toBeGreaterThan(0);
        });
    });
});

// Real API Integration Tests (only run if API key is provided)
if (REAL_API_KEY) {
    describe('AliyunConfig - Real API Integration', () => {
        describe('Configuration Validation with Real API', () => {
            it('should validate real API key format', () => {
                expect(REAL_API_KEY).toMatch(/^sk-[a-zA-Z0-9]+$/);
            });

            it('should create hybrid config with real API key', () => {
                const config = createHybridConfig({ apiKey: REAL_API_KEY });
                const validation = validateHybridConfig(config);
                
                expect(validation.isValid).toBe(true);
                expect(validation.errors).toHaveLength(0);
                expect(config.apiKey).toBe(REAL_API_KEY);
            });

            it('should validate audio configuration for real VAD usage', () => {
                const validation = validateAudioConfig(ALIYUN_AUDIO_CONFIG);
                
                expect(validation.isValid).toBe(true);
                expect(validation.errors).toHaveLength(0);
            });

            it('should create Python-compatible session for real WebSocket', () => {
                const sessionUpdate = createPythonCompatibleSessionUpdate();
                
                // Verify it matches exact Python implementation format
                expect(sessionUpdate.type).toBe('session.update');
                expect(sessionUpdate.session.turn_detection.threshold).toBe(0.1);
                expect(sessionUpdate.session.turn_detection.silence_duration_ms).toBe(900);
            });
        });

        describe('Endpoint Configuration', () => {
            it('should have accessible HTTP endpoints', async () => {
                const httpEndpoint = getHttpEndpoint();
                const serverEndpoint = getServerHttpEndpoint();
                
                expect(httpEndpoint).toBeTruthy();
                expect(serverEndpoint).toBe('https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation');
            });

            it('should create working WebSocket proxy config', () => {
                const proxyConfig = getWebSocketProxyConfig({ apiKey: REAL_API_KEY });
                
                expect(proxyConfig.endpoint).toBe('wss://dashscope.aliyuncs.com/api-ws/v1/realtime');
                expect(proxyConfig.pingInterval).toBe(20000);
                expect(proxyConfig.defaultSessionConfig.turn_detection.threshold).toBe(0.1);
            });
        });

        describe('Audio Processing Configuration', () => {
            it('should create MediaCapture config compatible with real audio', () => {
                const config = getMediaCaptureAudioConfig();
                
                // Verify real audio processing requirements
                expect(config.audio.sampleRate).toBe(16000);
                expect(config.audio.bitDepth).toBe(16);
                expect(config.audio.channels).toBe(1);
                expect(config.vadConfig.threshold).toBe(0.1);
                
                // Test validation
                const validation = validateAudioConfig(config.audio);
                expect(validation.isValid).toBe(true);
            });

            it('should configure rate limiting for real API usage', () => {
                const rateLimitConfig = createRateLimitConfig();
                
                // Verify matches Python implementation rates
                expect(rateLimitConfig.audio.maxChunksPerSecond).toBe(5);
                expect(rateLimitConfig.audio.minIntervalMs).toBe(200);
            });
        });

        describe('Model Selection for Real API', () => {
            it('should have correct model names for real API calls', () => {
                expect(ALIYUN_MODELS.HTTP_PRIMARY).toBe('qwen-plus');
                expect(ALIYUN_MODELS.HTTP_FALLBACK).toBe('qwen-turbo');
                expect(ALIYUN_MODELS.HTTP_PREMIUM).toBe('qwen-max');
                expect(ALIYUN_MODELS.DEFAULT_REALTIME).toBe('qwen-omni-turbo-realtime');
                expect(ALIYUN_MODELS.TRANSCRIPTION).toBe('gummy-realtime-v1');
            });

            it('should create complete configuration for real model usage', () => {
                const defaultConfig = createDefaultConfig();
                defaultConfig.apiKey = REAL_API_KEY;
                
                const validation = validateConfig(defaultConfig);
                expect(validation.isValid).toBe(true);
                
                // Test HTTP config
                const httpValidation = validateHttpConfig({
                    ...defaultConfig,
                    http: {
                        endpoint: getServerHttpEndpoint(),
                        timeout: 5000
                    }
                });
                expect(httpValidation.isValid).toBe(true);
            });
        });

        describe('Session Management Configuration', () => {
            it('should create session update events compatible with real API', () => {
                const sessionUpdate = createPythonCompatibleSessionUpdate({ voice: 'Ethan' });
                
                // Verify it will work with real WebSocket API
                expect(sessionUpdate.type).toBe('session.update');
                expect(sessionUpdate.event_id).toMatch(/^event_\d+$/);
                expect(sessionUpdate.session.voice).toBe('Ethan');
                expect(sessionUpdate.session.modalities).toEqual(['text', 'audio']);
                expect(sessionUpdate.session.input_audio_format).toBe('pcm16');
                expect(sessionUpdate.session.output_audio_format).toBe('pcm16');
            });

            it('should handle all supported voices', () => {
                const voices = ['Chelsie', 'Serena', 'Ethan', 'Cherry'];
                
                voices.forEach(voice => {
                    const sessionUpdate = createPythonCompatibleSessionUpdate({ voice });
                    expect(sessionUpdate.session.voice).toBe(voice);
                    
                    const sessionConfig = buildValidSessionConfig({ voice });
                    expect(sessionConfig.voice).toBe(voice);
                });
            });
        });

        describe('Error Recovery for Real API', () => {
            it('should have appropriate recovery strategies for real API errors', () => {
                const errorConfig = createErrorRecoveryConfig();
                
                // Test specific Aliyun error codes
                expect(errorConfig.errorRecoveryStrategies[1011]).toBe('session_config_recovery');
                expect(errorConfig.errorRecoveryStrategies[4001]).toBe('auth_refresh');
                expect(errorConfig.errorRecoveryStrategies[4008]).toBe('rate_limit_backoff');
            });

            it('should create appropriate error objects for API responses', () => {
                const authError = createError('auth_error', 'Invalid API key', 4001, {
                    apiKey: 'sk-xxx...xxx',
                    endpoint: getServerHttpEndpoint()
                });
                
                expect(authError.type).toBe('auth_error');
                expect(authError.code).toBe(4001);
                expect(authError.details.apiKey).toBeTruthy();
                expect(authError.timestamp).toBeGreaterThan(0);
            });
        });
    });
} else {
    console.log('Skipping real API tests - no API key provided (set VITE_DASHSCOPE_API_KEY)');
}

describe('AliyunConfig - Edge Cases and Performance', () => {
    describe('Configuration Edge Cases', () => {
        it('should handle null/undefined configurations gracefully', () => {
            expect(() => buildValidSessionConfig(null)).not.toThrow();
            expect(() => buildValidTurnDetection(undefined)).not.toThrow();
            expect(() => getMediaCaptureAudioConfig({})).not.toThrow();
        });

        it('should handle extreme VAD parameters', () => {
            const extremeVAD = buildValidTurnDetection({
                threshold: -1.0,
                silenceDurationMs: 100,
                prefixPaddingMs: 10000
            });
            
            expect(extremeVAD.threshold).toBe(-1.0);
            expect(extremeVAD.silence_duration_ms).toBe(100);
            expect(extremeVAD.prefix_padding_ms).toBe(10000);
        });

        it('should handle empty arrays and objects in session config', () => {
            const sessionConfig = buildValidSessionConfig({
                tools: [],
                modalities: []
            });
            
            expect(sessionConfig.tools).toEqual([]);
            expect(sessionConfig.modalities).toEqual([]);
        });
    });

    describe('Performance Considerations', () => {
        it('should generate event IDs quickly', () => {
            const startTime = performance.now();
            
            const ids = [];
            for (let i = 0; i < 1000; i++) {
                ids.push(generateEventId());
            }
            
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            expect(duration).toBeLessThan(100); // Should generate 1000 IDs in under 100ms
            expect(new Set(ids).size).toBe(1000); // All should be unique
        });

        it('should validate configurations quickly', () => {
            const config = createDefaultConfig();
            config.apiKey = 'test-key';
            
            const startTime = performance.now();
            
            for (let i = 0; i < 100; i++) {
                validateConfig(config);
            }
            
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            expect(duration).toBeLessThan(50); // 100 validations in under 50ms
        });

        it('should handle rapid session config creation', () => {
            const startTime = performance.now();
            
            for (let i = 0; i < 100; i++) {
                buildValidSessionConfig({
                    voice: i % 2 === 0 ? 'Chelsie' : 'Ethan',
                    temperature: i * 0.01
                });
            }
            
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            expect(duration).toBeLessThan(50); // 100 configs in under 50ms
        });
    });

    describe('Memory Usage', () => {
        it('should not leak memory during repeated config creation', () => {
            const initialMemory = process.memoryUsage().heapUsed;
            
            // Create many configs
            for (let i = 0; i < 1000; i++) {
                const config = createDefaultConfig();
                buildValidSessionConfig();
                validateConfig(config);
            }
            
            // Force garbage collection if available
            if (global.gc) {
                global.gc();
            }
            
            const finalMemory = process.memoryUsage().heapUsed;
            const memoryIncrease = finalMemory - initialMemory;
            
            // Memory increase should be reasonable (less than 10MB)
            expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
        });
    });

    describe('Concurrent Configuration Usage', () => {
        it('should handle concurrent config creation safely', async () => {
            const promises = [];
            
            for (let i = 0; i < 100; i++) {
                promises.push(new Promise((resolve) => {
                    const config = createDefaultConfig();
                    config.apiKey = `test-key-${i}`;
                    
                    const validation = validateConfig(config);
                    const sessionConfig = buildValidSessionConfig({ voice: `Voice-${i}` });
                    
                    resolve({ config, validation, sessionConfig });
                }));
            }
            
            const results = await Promise.all(promises);
            
            expect(results).toHaveLength(100);
            results.forEach((result, index) => {
                expect(result.config.apiKey).toBe(`test-key-${index}`);
                expect(result.validation.isValid).toBe(true);
                expect(result.sessionConfig.voice).toBe(`Voice-${index}`);
            });
        });
    });
});