/**
 * Comprehensive test suite for AliyunHttpChatModel
 * Tests HTTP model functionality, tool calling, and performance requirements
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { AliyunHttpChatModel } from '@/agent/models/aliyun/AliyunHttpChatModel.js';
import { HumanMessage, AIMessage } from '@langchain/core/messages';

const REAL_API_KEY = process.env.VITE_DASHSCOPE_API_KEY;
const TEST_TIMEOUT = 10000; // 10 second timeout for real API calls

describe('AliyunHttpChatModel', () => {
    let httpModel;
    
    beforeEach(() => {
        httpModel = new AliyunHttpChatModel({
            apiKey: REAL_API_KEY || 'test-key',
            model: 'qwen-plus',
            timeout: 500
        });
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('Model Initialization', () => {
        it('should initialize with correct default configuration', () => {
            const model = new AliyunHttpChatModel();
            
            expect(model.model).toBe('qwen-plus');
            expect(model.timeout).toBe(500);
            expect(model.temperature).toBe(0.7);
            expect(model.maxTokens).toBe(2000);
            expect(model.boundTools).toEqual([]);
        });

        it('should initialize with custom configuration', () => {
            const customModel = new AliyunHttpChatModel({
                model: 'qwen-turbo',
                timeout: 300,
                temperature: 0.5,
                maxTokens: 1500
            });
            
            expect(customModel.model).toBe('qwen-turbo');
            expect(customModel.timeout).toBe(300);
            expect(customModel.temperature).toBe(0.5);
            expect(customModel.maxTokens).toBe(1500);
        });

        it('should validate API key requirement', () => {
            expect(() => {
                new AliyunHttpChatModel({
                    apiKey: '',
                    model: 'qwen-plus'
                });
            }).toThrow('API key is required');
        });

        it('should validate supported models', () => {
            expect(() => {
                new AliyunHttpChatModel({
                    apiKey: 'test-key',
                    model: 'unsupported-model'
                });
            }).toThrow('Unsupported model');
        });

        it('should warn about timeout exceeding sub-600ms requirement', () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
            
            new AliyunHttpChatModel({
                apiKey: 'test-key',
                timeout: 700
            });
            
            // Should have logged a warning about timeout
            expect(consoleSpy).toHaveBeenCalled();
            consoleSpy.mockRestore();
        });
    });

    describe('LangChain Compatibility', () => {
        it('should return correct LLM type', () => {
            expect(httpModel._llmType()).toBe('aliyun-http');
        });

        it('should provide correct invocation parameters', () => {
            const params = httpModel.invocationParams();
            
            expect(params.model_name).toBe('qwen-plus');
            expect(params.api_mode).toBe('http');
            expect(params.timeout).toBe(500);
            expect(params.temperature).toBe(0.7);
            expect(params.max_tokens).toBe(2000);
            expect(params.tools).toEqual([]);
        });

        it('should include bound tools in invocation parameters', () => {
            const testTool = {
                name: 'test_tool',
                description: 'Test tool for testing',
                schema: { type: 'object', properties: {} }
            };
            
            const boundModel = httpModel.bindTools([testTool]);
            const params = boundModel.invocationParams();
            
            expect(params.tools).toHaveLength(1);
            expect(params.tools[0].function.name).toBe('test_tool');
        });
    });

    describe('Tool Binding', () => {
        it('should bind tools correctly', () => {
            const tools = [
                {
                    name: 'analyze_context',
                    description: 'Analyze conversation context',
                    schema: {
                        type: 'object',
                        properties: {
                            messages: { type: 'array' }
                        }
                    }
                },
                {
                    name: 'decide_mode',
                    description: 'Decide communication mode',
                    schema: {
                        type: 'object',
                        properties: {
                            context: { type: 'object' }
                        }
                    }
                }
            ];
            
            const boundModel = httpModel.bindTools(tools);
            
            expect(boundModel.boundTools).toHaveLength(2);
            expect(boundModel.boundTools[0].name).toBe('analyze_context');
            expect(boundModel.boundTools[1].name).toBe('decide_mode');
        });

        it('should replace existing tools with same name', () => {
            const tool1 = { name: 'test_tool', description: 'First version' };
            const tool2 = { name: 'test_tool', description: 'Second version' };
            
            const boundModel1 = httpModel.bindTools([tool1]);
            const boundModel2 = boundModel1.bindTools([tool2]);
            
            expect(boundModel2.boundTools).toHaveLength(1);
            expect(boundModel2.boundTools[0].description).toBe('Second version');
        });

        it('should handle invalid tools gracefully', () => {
            const invalidTools = [
                null,
                undefined,
                { description: 'No name' },
                { name: '', description: 'Empty name' }
            ];
            
            const boundModel = httpModel.bindTools(invalidTools);
            
            expect(boundModel.boundTools).toHaveLength(0);
        });

        it('should return original model when passed non-array', () => {
            const result = httpModel.bindTools('not-an-array');
            
            expect(result).toBe(httpModel);
        });
    });

    describe('HTTP Request Building', () => {
        it('should build correct request payload', () => {
            const messages = [
                new HumanMessage({ content: 'Hello, how are you?' })
            ];
            
            const payload = httpModel._buildRequestPayload(messages);
            
            expect(payload.model).toBe('qwen-plus');
            expect(payload.input.messages).toHaveLength(1);
            expect(payload.input.messages[0].role).toBe('user');
            expect(payload.input.messages[0].content).toBe('Hello, how are you?');
            expect(payload.parameters.temperature).toBe(0.7);
            expect(payload.parameters.max_tokens).toBe(2000);
            expect(payload.parameters.result_format).toBe('message');
        });

        it('should include tools in request when bound', () => {
            const tools = [{
                name: 'test_tool',
                description: 'Test tool',
                schema: { type: 'object', properties: {} }
            }];
            
            const boundModel = httpModel.bindTools(tools);
            const messages = [new HumanMessage({ content: 'Test' })];
            const payload = boundModel._buildRequestPayload(messages);
            
            expect(payload.parameters.tools).toHaveLength(1);
            expect(payload.parameters.tools[0].function.name).toBe('test_tool');
            expect(payload.parameters.tool_choice).toBe('auto');
        });

        it('should format different message types correctly', () => {
            const messages = [
                new HumanMessage({ content: 'User message' }),
                new AIMessage({ content: 'AI response' })
            ];
            
            const formattedMessages = httpModel._formatMessages(messages);
            
            expect(formattedMessages).toHaveLength(2);
            expect(formattedMessages[0].role).toBe('user');
            expect(formattedMessages[1].role).toBe('assistant');
        });

        it('should build correct HTTP headers', () => {
            const headers = httpModel._buildHeaders();
            
            expect(headers['Content-Type']).toBe('application/json');
            expect(headers['Authorization']).toContain('Bearer');
            expect(headers['User-Agent']).toBe('Hologram-Software/1.0');
            expect(headers['X-DashScope-SSE']).toBe('disable');
        });
    });

    describe('Response Parsing', () => {
        it('should parse successful response correctly', () => {
            const mockResponse = {
                code: '200',
                output: {
                    choices: [{
                        message: {
                            content: 'Hello! How can I help you?',
                            role: 'assistant'
                        },
                        finish_reason: 'stop'
                    }],
                    usage: {
                        prompt_tokens: 10,
                        completion_tokens: 8,
                        total_tokens: 18
                    }
                }
            };
            
            const result = httpModel._parseResponse(mockResponse);
            
            expect(result.generations).toHaveLength(1);
            expect(result.generations[0].text).toBe('Hello! How can I help you?');
            expect(result.generations[0].message.content).toBe('Hello! How can I help you?');
            expect(result.generations[0].tool_calls).toEqual([]);
        });

        it('should parse response with tool calls', () => {
            const mockResponse = {
                code: '200',
                output: {
                    choices: [{
                        message: {
                            content: '',
                            role: 'assistant',
                            tool_calls: [{
                                id: 'call_123',
                                function: {
                                    name: 'analyze_context',
                                    arguments: '{"messages": ["Hello"]}'
                                }
                            }]
                        },
                        finish_reason: 'tool_calls'
                    }],
                    usage: {
                        prompt_tokens: 15,
                        completion_tokens: 5,
                        total_tokens: 20
                    }
                }
            };
            
            const result = httpModel._parseResponse(mockResponse);
            
            expect(result.generations[0].tool_calls).toHaveLength(1);
            expect(result.generations[0].tool_calls[0].name).toBe('analyze_context');
            expect(result.generations[0].tool_calls[0].args).toEqual({ messages: ['Hello'] });
        });

        it('should handle API error responses', () => {
            const errorResponse = {
                code: '400',
                message: 'Invalid request parameters'
            };
            
            expect(() => {
                httpModel._parseResponse(errorResponse);
            }).toThrow('Aliyun API Error: 400 - Invalid request parameters');
        });

        it('should handle malformed responses', () => {
            expect(() => {
                httpModel._parseResponse({});
            }).toThrow('Invalid response format: missing output');
            
            expect(() => {
                httpModel._parseResponse({ output: {} });
            }).toThrow('Invalid response format: missing choices');
        });
    });

    describe('Error Handling and Retries', () => {
        it('should identify retryable errors correctly', () => {
            const retryableErrors = [
                new Error('ENOTFOUND'),
                new Error('ECONNRESET connection lost'),
                new Error('Request timeout after 500ms'),
                new Error('ETIMEDOUT')
            ];
            
            retryableErrors.forEach(error => {
                expect(httpModel._isRetryableError(error)).toBe(true);
            });
        });

        it('should identify non-retryable errors correctly', () => {
            const nonRetryableErrors = [
                new Error('Authentication failed'),
                new Error('Invalid API key'),
                new Error('Model not found')
            ];
            
            nonRetryableErrors.forEach(error => {
                expect(httpModel._isRetryableError(error)).toBe(false);
            });
        });
    });

    describe('Performance and Metrics', () => {
        it('should provide correct metrics', () => {
            const metrics = httpModel.getMetrics();
            
            expect(metrics.model).toBe('qwen-plus');
            expect(metrics.timeout).toBe(500);
            expect(metrics.maxRetries).toBe(2);
            expect(metrics.boundToolsCount).toBe(0);
            expect(metrics.supportsFunctionCalling).toBe(true);
            expect(metrics.apiMode).toBe('http');
        });

        it('should update metrics for bound tools', () => {
            const tools = [
                { name: 'tool1', description: 'First tool' },
                { name: 'tool2', description: 'Second tool' }
            ];
            
            const boundModel = httpModel.bindTools(tools);
            const metrics = boundModel.getMetrics();
            
            expect(metrics.boundToolsCount).toBe(2);
        });
    });

    // Real API Integration Tests (only run if API key is provided)
    if (REAL_API_KEY) {
        describe('Real API Integration', () => {
            it('should make successful HTTP API call', async () => {
                const messages = [
                    new HumanMessage({ content: 'Say hello briefly' })
                ];
                
                const response = await httpModel.invoke(messages);
                
                expect(response.generations).toHaveLength(1);
                expect(response.generations[0].text).toBeTruthy();
                expect(typeof response.generations[0].text).toBe('string');
            }, TEST_TIMEOUT);

            it('should respect timeout constraints', async () => {
                const fastModel = new AliyunHttpChatModel({
                    apiKey: REAL_API_KEY,
                    model: 'qwen-turbo', // Faster model
                    timeout: 300
                });
                
                const startTime = Date.now();
                const messages = [
                    new HumanMessage({ content: 'Quick response please' })
                ];
                
                try {
                    await fastModel.invoke(messages);
                    const responseTime = Date.now() - startTime;
                    expect(responseTime).toBeLessThan(600); // Sub-600ms requirement
                } catch (error) {
                    // Timeout error is acceptable for this test
                    expect(error.message).toContain('timeout');
                }
            }, TEST_TIMEOUT);

            it('should handle tool calling with real API', async () => {
                const tools = [{
                    name: 'get_weather',
                    description: 'Get current weather information',
                    schema: {
                        type: 'object',
                        properties: {
                            location: {
                                type: 'string',
                                description: 'City name'
                            }
                        },
                        required: ['location']
                    }
                }];
                
                const boundModel = httpModel.bindTools(tools);
                const messages = [
                    new HumanMessage({ 
                        content: 'What is the weather like in Beijing? Use the get_weather tool.' 
                    })
                ];
                
                const response = await boundModel.invoke(messages);
                
                expect(response.generations).toHaveLength(1);
                // Should either have tool calls or explain why it couldn't use tools
                expect(
                    response.generations[0].tool_calls.length > 0 || 
                    response.generations[0].text.length > 0
                ).toBe(true);
            }, TEST_TIMEOUT);

            it('should perform health check successfully', async () => {
                const healthResult = await httpModel.healthCheck();
                
                expect(healthResult.healthy).toBe(true);
                expect(healthResult.responseTime).toBeGreaterThan(0);
                expect(healthResult.model).toBe('qwen-plus');
                expect(healthResult.timestamp).toBeTruthy();
            }, TEST_TIMEOUT);

            it('should test different models performance', async () => {
                const models = ['qwen-turbo', 'qwen-plus'];
                const results = [];
                
                for (const modelName of models) {
                    const testModel = new AliyunHttpChatModel({
                        apiKey: REAL_API_KEY,
                        model: modelName,
                        timeout: 600
                    });
                    
                    const startTime = Date.now();
                    const messages = [
                        new HumanMessage({ content: 'Brief test response' })
                    ];
                    
                    try {
                        const response = await testModel.invoke(messages);
                        const responseTime = Date.now() - startTime;
                        
                        results.push({
                            model: modelName,
                            responseTime,
                            success: true,
                            hasContent: !!response.generations[0].text
                        });
                    } catch (error) {
                        results.push({
                            model: modelName,
                            responseTime: Date.now() - startTime,
                            success: false,
                            error: error.message
                        });
                    }
                }
                
                // At least one model should work
                expect(results.some(r => r.success)).toBe(true);
                
                // Log results for performance analysis
                console.log('Model Performance Results:', results);
            }, TEST_TIMEOUT * 2);
        });
    } else {
        console.log('Skipping real API tests - no API key provided');
    }

    describe('Edge Cases and Error Conditions', () => {
        it('should handle empty messages array', async () => {
            await expect(httpModel.invoke([])).rejects.toThrow();
        });

        it('should handle very long content gracefully', async () => {
            const longContent = 'A'.repeat(10000);
            const messages = [new HumanMessage({ content: longContent })];
            
            // Should either succeed or fail gracefully (no crashes)
            try {
                await httpModel.invoke(messages);
            } catch (error) {
                expect(error).toBeInstanceOf(Error);
            }
        });

        it('should handle special characters in content', () => {
            const specialContent = '🚀 Special chars: àáâãäå ñ © ® ™ < > & " \' \\';
            const messages = [new HumanMessage({ content: specialContent })];
            
            const payload = httpModel._buildRequestPayload(messages);
            expect(payload.input.messages[0].content).toBe(specialContent);
        });
    });

    describe('LangGraph Streaming Compatibility', () => {
        it('should create proper AIMessage chunks for LangChain v0.3', () => {
            // Test that our _createAIMessageChunk creates compatible messages
            const testContent = 'Test streaming content';
            const testToolCalls = [{
                id: 'call_123',
                name: 'test_tool',
                args: { param: 'value' }
            }];
            
            // Test content-only chunk
            const contentChunk = httpModel._createAIMessageChunk(testContent);
            expect(contentChunk).toBeInstanceOf(AIMessage);
            expect(contentChunk.content).toBe(testContent);
            
            // Critical: Test that we can set 'name' property (this was the original error)
            expect(() => {
                contentChunk.name = 'test-name';
            }).not.toThrow();
            expect(contentChunk.name).toBe('test-name');
            
            // Test chunk with tool calls
            const toolChunk = httpModel._createAIMessageChunk('', testToolCalls);
            expect(toolChunk).toBeInstanceOf(AIMessage);
            expect(toolChunk.tool_calls).toEqual(testToolCalls);
            
            // Test setting name on tool chunk
            expect(() => {
                toolChunk.name = 'tool-chunk-name';
            }).not.toThrow();
            expect(toolChunk.name).toBe('tool-chunk-name');
        });

        it('should format streaming chunks correctly for LangChain v0.3', async () => {
            // Mock a streaming response to test chunk formatting
            const mockStreamChunk = {
                message: httpModel._createAIMessageChunk('Hello'),
                text: 'Hello'
            };
            
            // Verify chunk has correct structure
            expect(mockStreamChunk).toHaveProperty('message');
            expect(mockStreamChunk).toHaveProperty('text');
            expect(mockStreamChunk.message).toBeInstanceOf(AIMessage);
            expect(mockStreamChunk.text).toBe('Hello');
            
            // Verify message properties can be modified (LangGraph requirement)
            expect(() => {
                mockStreamChunk.message.name = 'streaming-message';
                mockStreamChunk.message.id = 'msg_123';
            }).not.toThrow();
        });

        it('should handle streaming tool calls properly', () => {
            // Test partial tool call accumulation (as happens in streaming)
            const partialToolCall = {
                id: 'call_123',
                name: 'calculate',
                args: '{"expression":'
            };
            
            const chunk = httpModel._createAIMessageChunk('', [partialToolCall]);
            expect(chunk).toBeInstanceOf(AIMessage);
            expect(chunk.tool_calls).toContain(partialToolCall);
            
            // Test that chunk can be modified by LangGraph
            expect(() => {
                chunk.name = 'partial-tool-call';
                chunk.usage_metadata = { input_tokens: 10, output_tokens: 5 };
            }).not.toThrow();
        });

        // Integration test with mock streaming to verify the fix works end-to-end
        it('should support _streamResponseChunks method for LangGraph compatibility', async () => {
            // This is a mock test to verify the method exists and has the right signature
            // We don't test actual streaming here as it requires real API calls
            
            expect(httpModel._streamResponseChunks).toBeDefined();
            expect(typeof httpModel._streamResponseChunks).toBe('function');
            
            // Verify method signature matches LangChain v0.3 expectations
            const methodString = httpModel._streamResponseChunks.toString();
            expect(methodString).toContain('messages');
            expect(methodString).toContain('options');
            expect(methodString).toContain('context');
        });
    });
});