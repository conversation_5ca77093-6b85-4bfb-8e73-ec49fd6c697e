/**
 * Aliyun System 2 Tool Calling Tests
 * 
 * Tests Aliyun HTTP models (qwen-plus, qwen-turbo, qwen-max) for tool calling
 * capabilities when used as System 2 in dual-brain architecture
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { AliyunHttpChatModel } from '../../../../src/agent/models/aliyun/AliyunHttpChatModel.js';
import { AliyunModelFactory } from '../../../../src/agent/models/aliyun/AliyunModelFactory.js';

// Mock the logger
vi.mock('../../../../src/utils/logger.ts', () => ({
  createLogger: () => ({
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    setLogLevel: vi.fn()
  }),
  LogLevel: { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3 }
}));

// Mock environment variables
vi.mock('../../../../src/config/env.ts', () => ({
  getEnvVar: vi.fn().mockImplementation((key, defaultValue) => {
    const mockEnvVars = {
      'VITE_DASHSCOPE_API_KEY': 'mock-api-key',
      'VITE_ALIYUN_MODEL': 'qwen-plus'
    };
    return mockEnvVars[key] || defaultValue;
  })
}));

// Mock fetch for API calls
global.fetch = vi.fn();

describe('Aliyun System 2 Tool Calling', () => {
  let httpModel;
  let mockTools;

  beforeEach(() => {
    // Create mock tools
    mockTools = [
      {
        name: 'control_avatar_speech',
        description: 'Control avatar speaking behavior',
        function: {
          name: 'control_avatar_speech',
          description: 'Control when and how the avatar speaks',
          parameters: {
            type: 'object',
            properties: {
              action: {
                type: 'string',
                enum: ['speak', 'stop_speaking', 'check_status'],
                description: 'Speech control action'
              },
              text: {
                type: 'string',
                description: 'Text for avatar to speak (required for speak action)'
              },
              voice: {
                type: 'string',
                enum: ['Serena', 'Ethan', 'Chelsie', 'Cherry'],
                description: 'Voice to use for speaking'
              }
            },
            required: ['action']
          }
        }
      },
      {
        name: 'select_animation',
        description: 'Select and trigger avatar animation',
        function: {
          name: 'select_animation',
          description: 'Select animation based on context and intent',
          parameters: {
            type: 'object',
            properties: {
              animationQuery: {
                type: 'string',
                description: 'Animation request or description'
              },
              category: {
                type: 'string',
                description: 'Animation category to search within'
              }
            },
            required: ['animationQuery']
          }
        }
      }
    ];

    // Initialize HTTP model for System 2
    httpModel = new AliyunHttpChatModel({
      modelName: 'qwen-plus',
      apiKey: 'mock-api-key',
      temperature: 0.7,
      maxTokens: 2048,
      enableDualBrain: true,
      systemType: 'system2'
    });

    // Clear all mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Tool Binding for System 2', () => {
    it('should bind tools correctly for System 2', () => {
      const boundModel = httpModel.bindTools(mockTools);

      expect(boundModel).toBeDefined();
      expect(boundModel.tools).toHaveLength(2);
      expect(boundModel.tools[0].function.name).toBe('control_avatar_speech');
      expect(boundModel.tools[1].function.name).toBe('select_animation');
    });

    it('should format tools for Aliyun API', () => {
      const boundModel = httpModel.bindTools(mockTools);
      const formattedTools = boundModel._formatToolsForAPI(mockTools);

      expect(formattedTools).toHaveLength(2);
      expect(formattedTools[0]).toHaveProperty('type', 'function');
      expect(formattedTools[0]).toHaveProperty('function');
      expect(formattedTools[0].function).toHaveProperty('name');
      expect(formattedTools[0].function).toHaveProperty('description');
      expect(formattedTools[0].function).toHaveProperty('parameters');
    });

    it('should set appropriate tool_choice for System 2', () => {
      const boundModel = httpModel.bindTools(mockTools, {
        tool_choice: 'auto'
      });

      expect(boundModel.toolChoice).toBe('auto');
    });
  });

  describe('API Request Formation', () => {
    it('should include tools in API request payload', async () => {
      const boundModel = httpModel.bindTools(mockTools);
      
      // Mock successful API response with tool calls
      const mockApiResponse = {
        choices: [
          {
            message: {
              role: 'assistant',
              content: 'I can help you with that.',
              tool_calls: [
                {
                  id: 'call_1',
                  type: 'function',
                  function: {
                    name: 'control_avatar_speech',
                    arguments: JSON.stringify({
                      action: 'speak',
                      text: 'Hello! How can I assist you?',
                      voice: 'Serena'
                    })
                  }
                }
              ]
            },
            finish_reason: 'tool_calls'
          }
        ],
        usage: {
          prompt_tokens: 150,
          completion_tokens: 50,
          total_tokens: 200
        }
      };

      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockApiResponse),
        headers: new Headers({ 'content-type': 'application/json' })
      });

      const messages = [
        { role: 'user', content: 'Can you speak to me?' }
      ];

      const result = await boundModel.invoke(messages);

      // Verify fetch was called with tools
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('dashscope.aliyuncs.com'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Authorization': 'Bearer mock-api-key',
            'Content-Type': 'application/json'
          }),
          body: expect.stringContaining('"tools"')
        })
      );

      // Parse the request body to verify tools were included
      const requestBody = JSON.parse(global.fetch.mock.calls[0][1].body);
      expect(requestBody.tools).toHaveLength(2);
      expect(requestBody.tool_choice).toBeDefined();

      // Verify result contains tool calls
      expect(result.content).toBe('I can help you with that.');
      expect(result.tool_calls).toHaveLength(1);
      expect(result.tool_calls[0].function.name).toBe('control_avatar_speech');
    });

    it('should handle tool choice configurations', async () => {
      const boundModel = httpModel.bindTools(mockTools, {
        tool_choice: { type: 'function', function: { name: 'control_avatar_speech' } }
      });

      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          choices: [{ message: { role: 'assistant', content: 'Response' } }],
          usage: { total_tokens: 100 }
        })
      });

      await boundModel.invoke([{ role: 'user', content: 'Test' }]);

      const requestBody = JSON.parse(global.fetch.mock.calls[0][1].body);
      expect(requestBody.tool_choice).toEqual({
        type: 'function',
        function: { name: 'control_avatar_speech' }
      });
    });
  });

  describe('Tool Call Response Processing', () => {
    it('should process tool calls correctly', async () => {
      const boundModel = httpModel.bindTools(mockTools);

      const mockApiResponse = {
        choices: [
          {
            message: {
              role: 'assistant',
              content: '',
              tool_calls: [
                {
                  id: 'call_1',
                  type: 'function',
                  function: {
                    name: 'control_avatar_speech',
                    arguments: JSON.stringify({
                      action: 'speak',
                      text: 'Hello from System 2!',
                      voice: 'Serena'
                    })
                  }
                },
                {
                  id: 'call_2',
                  type: 'function',
                  function: {
                    name: 'select_animation',
                    arguments: JSON.stringify({
                      animationQuery: 'happy greeting',
                      category: 'communication'
                    })
                  }
                }
              ]
            },
            finish_reason: 'tool_calls'
          }
        ],
        usage: { total_tokens: 250 }
      };

      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockApiResponse)
      });

      const result = await boundModel.invoke([
        { role: 'user', content: 'Please greet me happily' }
      ]);

      expect(result.tool_calls).toHaveLength(2);
      
      // Verify first tool call (speaking)
      expect(result.tool_calls[0].function.name).toBe('control_avatar_speech');
      const speakArgs = JSON.parse(result.tool_calls[0].function.arguments);
      expect(speakArgs.action).toBe('speak');
      expect(speakArgs.text).toBe('Hello from System 2!');
      expect(speakArgs.voice).toBe('Serena');

      // Verify second tool call (animation)
      expect(result.tool_calls[1].function.name).toBe('select_animation');
      const animArgs = JSON.parse(result.tool_calls[1].function.arguments);
      expect(animArgs.animationQuery).toBe('happy greeting');
      expect(animArgs.category).toBe('communication');
    });

    it('should handle malformed tool call arguments gracefully', async () => {
      const boundModel = httpModel.bindTools(mockTools);

      const mockApiResponse = {
        choices: [
          {
            message: {
              role: 'assistant',
              content: 'I had trouble with the tool call',
              tool_calls: [
                {
                  id: 'call_1',
                  type: 'function',
                  function: {
                    name: 'control_avatar_speech',
                    arguments: 'invalid_json{'
                  }
                }
              ]
            },
            finish_reason: 'tool_calls'
          }
        ],
        usage: { total_tokens: 100 }
      };

      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockApiResponse)
      });

      const result = await boundModel.invoke([
        { role: 'user', content: 'Test malformed args' }
      ]);

      expect(result.tool_calls).toHaveLength(1);
      expect(result.tool_calls[0].function.arguments).toBe('invalid_json{');
      // The model should still return the malformed arguments for error handling upstream
    });
  });

  describe('Model Factory Integration', () => {
    it('should create System 2 model with tool binding capability', async () => {
      const factory = new AliyunModelFactory();
      
      const system2Model = await factory.createAutonomousModel(mockTools, {
        modelName: 'qwen-plus',
        systemType: 'system2',
        enableFunctionCalling: true
      });

      expect(system2Model).toBeDefined();
      expect(typeof system2Model.bindTools).toBe('function');
    });

    it('should verify tool calling capabilities', async () => {
      const factory = new AliyunModelFactory();
      
      const capabilities = await factory.testModelCapabilities('qwen-plus');

      expect(capabilities.supported).toBe(true);
      expect(capabilities.capabilities).toContain('function_calling');
    });
  });

  describe('System 2 Specific Configurations', () => {
    it('should use appropriate model configurations for System 2', () => {
      const system2Config = {
        modelName: 'qwen-plus', // More capable model for reasoning
        temperature: 0.3, // Lower temperature for more focused reasoning
        maxTokens: 4096, // Higher token limit for complex responses
        systemType: 'system2',
        enableFunctionCalling: true
      };

      const model = new AliyunHttpChatModel(system2Config);

      expect(model.modelName).toBe('qwen-plus');
      expect(model.temperature).toBe(0.3);
      expect(model.maxTokens).toBe(4096);
    });

    it('should handle complex reasoning prompts with tool calls', async () => {
      const boundModel = httpModel.bindTools(mockTools);

      const complexPrompt = `
        User has been quiet for 10 seconds. As System 2, analyze the situation and decide:
        1. Should I proactively engage?
        2. What animation would be appropriate?
        3. What should I say?
        
        Consider: User profile shows they appreciate gentle check-ins.
      `;

      const mockApiResponse = {
        choices: [
          {
            message: {
              role: 'assistant',
              content: 'Based on my analysis, I should provide a gentle check-in.',
              tool_calls: [
                {
                  id: 'call_1',
                  type: 'function',
                  function: {
                    name: 'select_animation',
                    arguments: JSON.stringify({
                      animationQuery: 'gentle talking gesture',
                      category: 'communication'
                    })
                  }
                },
                {
                  id: 'call_2',
                  type: 'function',
                  function: {
                    name: 'control_avatar_speech',
                    arguments: JSON.stringify({
                      action: 'speak',
                      text: 'Is everything alright? I\'m here if you need anything.',
                      voice: 'Serena'
                    })
                  }
                }
              ]
            },
            finish_reason: 'tool_calls'
          }
        ],
        usage: { total_tokens: 300 }
      };

      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockApiResponse)
      });

      const result = await boundModel.invoke([
        { role: 'user', content: complexPrompt }
      ]);

      expect(result.content).toContain('analysis');
      expect(result.tool_calls).toHaveLength(2);
      expect(result.tool_calls[0].function.name).toBe('select_animation');
      expect(result.tool_calls[1].function.name).toBe('control_avatar_speech');
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      const boundModel = httpModel.bindTools(mockTools);

      global.fetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        text: () => Promise.resolve('Invalid tool configuration')
      });

      await expect(
        boundModel.invoke([{ role: 'user', content: 'Test error' }])
      ).rejects.toThrow();
    });

    it('should handle network timeouts', async () => {
      const boundModel = httpModel.bindTools(mockTools);

      global.fetch.mockRejectedValueOnce(new Error('Network timeout'));

      await expect(
        boundModel.invoke([{ role: 'user', content: 'Test timeout' }])
      ).rejects.toThrow('Network timeout');
    });

    it('should validate tool schemas before API call', () => {
      const invalidTools = [
        {
          name: 'invalid_tool',
          // Missing required fields
        }
      ];

      expect(() => {
        httpModel.bindTools(invalidTools);
      }).toThrow(); // Should throw validation error
    });
  });

  describe('Performance Considerations', () => {
    it('should handle multiple concurrent tool calls efficiently', async () => {
      const boundModel = httpModel.bindTools(mockTools);

      const mockApiResponse = {
        choices: [
          {
            message: {
              role: 'assistant',
              content: 'Processing multiple actions',
              tool_calls: Array.from({ length: 5 }, (_, i) => ({
                id: `call_${i}`,
                type: 'function',
                function: {
                  name: i % 2 === 0 ? 'control_avatar_speech' : 'select_animation',
                  arguments: JSON.stringify({ 
                    action: 'speak', 
                    text: `Action ${i}` 
                  })
                }
              }))
            },
            finish_reason: 'tool_calls'
          }
        ],
        usage: { total_tokens: 500 }
      };

      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockApiResponse)
      });

      const start = Date.now();
      const result = await boundModel.invoke([
        { role: 'user', content: 'Execute multiple actions' }
      ]);
      const duration = Date.now() - start;

      expect(result.tool_calls).toHaveLength(5);
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should track token usage for cost optimization', async () => {
      const boundModel = httpModel.bindTools(mockTools);

      const mockApiResponse = {
        choices: [
          {
            message: {
              role: 'assistant',
              content: 'Token usage tracking test',
              tool_calls: []
            },
            finish_reason: 'stop'
          }
        ],
        usage: {
          prompt_tokens: 200,
          completion_tokens: 50,
          total_tokens: 250
        }
      };

      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockApiResponse)
      });

      const result = await boundModel.invoke([
        { role: 'user', content: 'Track my tokens' }
      ]);

      expect(result.usage).toBeDefined();
      expect(result.usage.total_tokens).toBe(250);
      expect(result.usage.prompt_tokens).toBe(200);
      expect(result.usage.completion_tokens).toBe(50);
    });
  });
});