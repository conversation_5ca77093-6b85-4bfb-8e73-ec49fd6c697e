/**
 * Comprehensive test suite for AliyunModelFactory
 * Tests model selection, caching, and hybrid HTTP/WebSocket management
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { AliyunModelFactory } from '@/agent/models/aliyun/AliyunModelFactory.js';
import { AliyunHttpChatModel } from '@/agent/models/aliyun/AliyunHttpChatModel.js';
import { AliyunWebSocketChatModel } from '@/agent/models/aliyun/AliyunWebSocketChatModel.js';
import { ALIYUN_MODELS } from '@/agent/models/aliyun/AliyunConfig.js';

const REAL_API_KEY = process.env.VITE_DASHSCOPE_API_KEY;
const TEST_TIMEOUT = 10000;

describe('AliyunModelFactory', () => {
    let factory;

    beforeEach(() => {
        factory = new AliyunModelFactory({
            apiKey: REAL_API_KEY || 'test-key'
        });
    });

    afterEach(async () => {
        await factory.shutdown();
        vi.clearAllMocks();
    });

    describe('Factory Initialization', () => {
        it('should initialize with correct default configuration', () => {
            expect(factory.config.apiKey).toBeTruthy();
            expect(factory.config.http.models.primary).toBe('qwen-plus');
            expect(factory.config.http.timeout).toBe(500);
            expect(factory.config.websocket.defaultModel).toBe(ALIYUN_MODELS.DEFAULT_REALTIME);
        });

        it('should validate configuration on initialization', () => {
            expect(() => {
                new AliyunModelFactory({
                    apiKey: ''
                });
            }).toThrow('Invalid configuration');
        });

        it('should set provider field correctly for HTTP models', async () => {
            if (!REAL_API_KEY) {
                console.warn('Skipping real API test - no API key provided');
                return;
            }

            try {
                const httpModel = await factory.getHttpModel('qwen-plus');
                expect(httpModel.provider).toBe('Aliyun');
                expect(httpModel.config.provider).toBe('Aliyun');
            } catch (error) {
                // If the test fails due to provider field missing, we'll catch and validate the error
                expect(error.message).not.toContain('Cannot read properties of undefined (reading \'provider\')');
                // Re-throw if it's a different error
                if (error.message.includes('Cannot read properties of undefined (reading \'provider\')')) {
                    throw new Error('Provider field is still undefined in model configuration');
                }
            }
        });

        it('should handle config access safely in constructor', () => {
            // This test ensures that the DualBrainChatModel doesn't fail due to config access timing issues
            expect(() => {
                new AliyunHttpChatModel({
                    model: 'qwen-plus',
                    apiKey: 'test-key',
                    provider: 'Aliyun'
                });
            }).not.toThrow('Cannot read properties of undefined (reading \'provider\')');
        });

        it('should accept custom configuration', () => {
            const customFactory = new AliyunModelFactory({
                apiKey: 'test-key',
                http: {
                    timeout: 300,
                    models: {
                        primary: 'qwen-turbo'
                    }
                }
            });

            expect(customFactory.config.http.timeout).toBe(300);
            expect(customFactory.config.http.models.primary).toBe('qwen-turbo');
        });
    });

    describe('HTTP Model Management', () => {
        it('should create HTTP model with default configuration', async () => {
            // Mock health check to avoid real API call
            vi.spyOn(AliyunHttpChatModel.prototype, 'healthCheck')
                .mockResolvedValue({ healthy: true, responseTime: 100 });

            const httpModel = await factory.getHttpModel();

            expect(httpModel).toBeInstanceOf(AliyunHttpChatModel);
            expect(httpModel.model).toBe('qwen-plus');
            expect(httpModel.timeout).toBe(500);
        });

        it('should create HTTP model with specific model name', async () => {
            vi.spyOn(AliyunHttpChatModel.prototype, 'healthCheck')
                .mockResolvedValue({ healthy: true, responseTime: 100 });

            const httpModel = await factory.getHttpModel('qwen-turbo');

            expect(httpModel.model).toBe('qwen-turbo');
        });

        it('should cache HTTP models correctly', async () => {
            vi.spyOn(AliyunHttpChatModel.prototype, 'healthCheck')
                .mockResolvedValue({ healthy: true, responseTime: 100 });

            const model1 = await factory.getHttpModel('qwen-plus');
            const model2 = await factory.getHttpModel('qwen-plus');

            expect(model1).toBe(model2); // Should be same cached instance
        });

        it('should create different instances for different configurations', async () => {
            vi.spyOn(AliyunHttpChatModel.prototype, 'healthCheck')
                .mockResolvedValue({ healthy: true, responseTime: 100 });

            const model1 = await factory.getHttpModel('qwen-plus', { temperature: 0.5 });
            const model2 = await factory.getHttpModel('qwen-plus', { temperature: 0.8 });

            expect(model1).not.toBe(model2); // Should be different instances
        });

        it('should fallback to turbo model on primary model failure', async () => {
            let callCount = 0;
            vi.spyOn(AliyunHttpChatModel.prototype, 'healthCheck')
                .mockImplementation(() => {
                    callCount++;
                    if (callCount === 1) {
                        return Promise.resolve({ healthy: false, error: 'Primary model failed' });
                    }
                    return Promise.resolve({ healthy: true, responseTime: 100 });
                });

            const httpModel = await factory.getHttpModel('qwen-plus');

            expect(httpModel.model).toBe('qwen-turbo'); // Should fallback
        });
    });

    describe('WebSocket Model Management', () => {
        it('should create WebSocket model', async () => {
            const websocketModel = await factory.getWebSocketModel();

            expect(websocketModel).toBeInstanceOf(AliyunWebSocketChatModel);
            expect(websocketModel.model).toBe(ALIYUN_MODELS.DEFAULT_REALTIME);
        });

        it('should cache WebSocket model', async () => {
            const model1 = await factory.getWebSocketModel();
            const model2 = await factory.getWebSocketModel();

            expect(model1).toBe(model2); // Should be same cached instance
        });
    });

    describe('Model Selection Logic', () => {
        it('should select WebSocket for voice input', () => {
            const modelType = factory.selectModelType('chat', {
                inputType: 'voice'
            });

            expect(modelType).toBe('websocket');
        });

        it('should select HTTP for autonomous tasks', () => {
            const autonomousTaskTypes = [
                'analyze_conversation_context',
                'decide_communication_mode',
                'track_conversation_topics',
                'manage_conversation_memory'
            ];

            autonomousTaskTypes.forEach(taskType => {
                const modelType = factory.selectModelType(taskType);
                expect(modelType).toBe('http');
            });
        });

        it('should select HTTP for tool-requiring tasks', () => {
            const modelType = factory.selectModelType('general_task', {
                requiresTools: true
            });

            expect(modelType).toBe('http');
        });

        it('should select HTTP for complex tasks', () => {
            const modelType = factory.selectModelType('analysis', {
                complexity: 'high'
            });

            expect(modelType).toBe('http');
        });

        it('should default to HTTP for unknown tasks', () => {
            const modelType = factory.selectModelType('unknown_task');

            expect(modelType).toBe('http');
        });
    });

    describe('HTTP Model Selection', () => {
        it('should select turbo for cost-sensitive tasks', () => {
            const modelName = factory.selectHttpModel({
                costSensitive: true
            });

            expect(modelName).toBe('qwen-turbo');
        });

        it('should select max for high complexity tasks', () => {
            const modelName = factory.selectHttpModel({
                complexity: 'high',
                responseTime: 'normal'
            });

            expect(modelName).toBe('qwen-max');
        });

        it('should select plus as balanced default', () => {
            const modelName = factory.selectHttpModel({
                complexity: 'medium'
            });

            expect(modelName).toBe('qwen-plus');
        });

        it('should avoid max for fast response requirements', () => {
            const modelName = factory.selectHttpModel({
                complexity: 'high',
                responseTime: 'fast'
            });

            expect(modelName).toBe('qwen-plus'); // Should not use max for fast response
        });
    });

    describe('Model Selection for Tasks', () => {
        it('should select appropriate model for autonomous analysis', async () => {
            vi.spyOn(AliyunHttpChatModel.prototype, 'healthCheck')
                .mockResolvedValue({ healthy: true, responseTime: 100 });

            const model = await factory.getModelForTask('analyze_conversation_context');

            expect(model).toBeInstanceOf(AliyunHttpChatModel);
            expect(model.model).toBe('qwen-plus');
        });

        it('should select WebSocket for voice tasks', async () => {
            const model = await factory.getModelForTask('voice_interaction', {
                inputType: 'voice'
            });

            expect(model).toBeInstanceOf(AliyunWebSocketChatModel);
        });

        it('should select optimized model for cost-sensitive tasks', async () => {
            vi.spyOn(AliyunHttpChatModel.prototype, 'healthCheck')
                .mockResolvedValue({ healthy: true, responseTime: 100 });

            const model = await factory.getModelForTask('simple_analysis', {
                costSensitive: true
            });

            expect(model.model).toBe('qwen-turbo');
        });
    });

    describe('Autonomous Model Creation', () => {
        it('should create autonomous model with tools', async () => {
            vi.spyOn(AliyunHttpChatModel.prototype, 'healthCheck')
                .mockResolvedValue({ healthy: true, responseTime: 100 });

            const tools = [
                {
                    name: 'analyze_context',
                    description: 'Analyze conversation context',
                    schema: { type: 'object', properties: {} }
                }
            ];

            const autonomousModel = await factory.createAutonomousModel(tools);

            expect(autonomousModel).toBeInstanceOf(AliyunHttpChatModel);
            expect(autonomousModel.boundTools).toHaveLength(1);
            expect(autonomousModel.timeout).toBe(500); // Aggressive timeout
            expect(autonomousModel.temperature).toBe(0.1); // Low temperature for consistency
        });

        it('should select optimal model for autonomous tools', async () => {
            vi.spyOn(AliyunHttpChatModel.prototype, 'healthCheck')
                .mockResolvedValue({ healthy: true, responseTime: 100 });

            const autonomousModel = await factory.createAutonomousModel([], {
                complexity: 'high',
                responseTime: 'normal'
            });

            expect(autonomousModel.model).toBe('qwen-max');
        });
    });

    describe('Fallback Chain Execution', () => {
        it('should execute with default fallback chain', async () => {
            let attemptCount = 0;
            const mockTask = vi.fn().mockImplementation((model) => {
                attemptCount++;
                if (attemptCount === 1) {
                    throw new Error('First model failed');
                }
                return `Success with ${model.model}`;
            });

            vi.spyOn(AliyunHttpChatModel.prototype, 'healthCheck')
                .mockResolvedValue({ healthy: true, responseTime: 100 });

            const result = await factory.executeWithFallback(mockTask);

            expect(result).toContain('Success');
            expect(attemptCount).toBeGreaterThan(1);
        });

        it('should use custom fallback chain', async () => {
            const customChain = [
                { model: 'qwen-turbo', type: 'http', timeout: 300 }
            ];

            const mockTask = vi.fn().mockReturnValue('Success with turbo');

            vi.spyOn(AliyunHttpChatModel.prototype, 'healthCheck')
                .mockResolvedValue({ healthy: true, responseTime: 100 });

            const result = await factory.executeWithFallback(mockTask, customChain);

            expect(result).toBe('Success with turbo');
        });

        it('should throw error when all models fail', async () => {
            const failingTask = vi.fn().mockRejectedValue(new Error('All models failed'));

            await expect(factory.executeWithFallback(failingTask)).rejects.toThrow('All models in fallback chain failed');
        });
    });

    describe('Metrics and Performance', () => {
        it('should track factory metrics', async () => {
            vi.spyOn(AliyunHttpChatModel.prototype, 'healthCheck')
                .mockResolvedValue({ healthy: true, responseTime: 100 });

            await factory.getHttpModel('qwen-plus');
            await factory.getWebSocketModel();

            const metrics = factory.getMetrics();

            expect(metrics.totalRequests).toBeGreaterThan(0);
            expect(metrics.httpRequests).toBeGreaterThan(0);
            expect(metrics.cacheSize.http).toBeGreaterThan(0);
            expect(metrics.averageResponseTime).toBeGreaterThan(0);
        });

        it('should track model usage patterns', async () => {
            vi.spyOn(AliyunHttpChatModel.prototype, 'healthCheck')
                .mockResolvedValue({ healthy: true, responseTime: 100 });

            await factory.getModelForTask('analyze_conversation_context');
            await factory.getModelForTask('voice_interaction', { inputType: 'voice' });

            const metrics = factory.getMetrics();

            expect(metrics.modelSelection.httpRatio).toBeGreaterThan(0);
            expect(metrics.modelSelection.websocketRatio).toBeGreaterThan(0);
        });

        it('should update metrics on errors', async () => {
            vi.spyOn(AliyunHttpChatModel.prototype, 'healthCheck')
                .mockRejectedValue(new Error('Health check failed'));

            try {
                await factory.getModelForTask('failing_task');
            } catch (error) {
                // Expected to fail
            }

            const metrics = factory.getMetrics();
            expect(metrics.errors).toBeGreaterThan(0);
        });
    });

    describe('Cache Management', () => {
        it('should clear cache correctly', async () => {
            vi.spyOn(AliyunHttpChatModel.prototype, 'healthCheck')
                .mockResolvedValue({ healthy: true, responseTime: 100 });

            await factory.getHttpModel('qwen-plus');
            await factory.getWebSocketModel();

            let metrics = factory.getMetrics();
            expect(metrics.cacheSize.http).toBeGreaterThan(0);
            expect(metrics.cacheSize.websocket).toBeGreaterThan(0);

            factory.clearCache();

            metrics = factory.getMetrics();
            expect(metrics.cacheSize.http).toBe(0);
            expect(metrics.cacheSize.websocket).toBe(0);
        });
    });

    describe('Resource Management', () => {
        it('should shutdown gracefully', async () => {
            vi.spyOn(AliyunHttpChatModel.prototype, 'healthCheck')
                .mockResolvedValue({ healthy: true, responseTime: 100 });

            await factory.getHttpModel('qwen-plus');
            await factory.getWebSocketModel();

            // Should not throw
            await expect(factory.shutdown()).resolves.not.toThrow();

            const metrics = factory.getMetrics();
            expect(metrics.cacheSize.http).toBe(0);
            expect(metrics.cacheSize.websocket).toBe(0);
        });
    });

    // Real API Integration Tests (only run if API key is provided)
    if (REAL_API_KEY) {
        describe('Real API Integration', () => {
            it('should create working HTTP model', async () => {
                const httpModel = await factory.getHttpModel('qwen-turbo');

                const healthCheck = await httpModel.healthCheck();
                expect(healthCheck.healthy).toBe(true);
                expect(healthCheck.responseTime).toBeGreaterThan(0);
            }, TEST_TIMEOUT);

            it('should demonstrate model selection performance', async () => {
                const tasks = [
                    { type: 'analyze_conversation_context', expectHttp: true },
                    { type: 'voice_interaction', context: { inputType: 'voice' }, expectHttp: false },
                    { type: 'simple_task', context: { costSensitive: true }, expectHttp: true }
                ];

                const results = [];

                for (const task of tasks) {
                    const startTime = Date.now();
                    try {
                        const model = await factory.getModelForTask(task.type, task.context);
                        const responseTime = Date.now() - startTime;

                        results.push({
                            task: task.type,
                            isHttp: model instanceof AliyunHttpChatModel,
                            model: model.model || 'websocket',
                            responseTime,
                            success: true
                        });

                        // Verify expectation
                        if (task.expectHttp) {
                            expect(model).toBeInstanceOf(AliyunHttpChatModel);
                        } else {
                            expect(model).toBeInstanceOf(AliyunWebSocketChatModel);
                        }

                    } catch (error) {
                        results.push({
                            task: task.type,
                            responseTime: Date.now() - startTime,
                            success: false,
                            error: error.message
                        });
                    }
                }

                console.log('Model Selection Results:', results);

                // All tasks should succeed
                expect(results.every(r => r.success)).toBe(true);

                // Performance should be reasonable (under 1 second for model creation)
                expect(results.every(r => r.responseTime < 1000)).toBe(true);
            }, TEST_TIMEOUT);

            it('should demonstrate autonomous model creation', async () => {
                const tools = [
                    {
                        name: 'analyze_context',
                        description: 'Analyze conversation context for autonomous decision making',
                        schema: {
                            type: 'object',
                            properties: {
                                messages: { type: 'array' },
                                depth: { type: 'string', enum: ['basic', 'detailed'] }
                            },
                            required: ['messages']
                        }
                    }
                ];

                const autonomousModel = await factory.createAutonomousModel(tools, {
                    complexity: 'medium',
                    responseTime: 'fast'
                });

                expect(autonomousModel).toBeInstanceOf(AliyunHttpChatModel);
                expect(autonomousModel.boundTools).toHaveLength(1);
                expect(autonomousModel.model).toBe('qwen-plus'); // Expected for medium complexity, fast response

                // Test that the model works
                const healthCheck = await autonomousModel.healthCheck();
                expect(healthCheck.healthy).toBe(true);
            }, TEST_TIMEOUT);
        });
    } else {
        console.log('Skipping real API tests - no API key provided');
    }
});