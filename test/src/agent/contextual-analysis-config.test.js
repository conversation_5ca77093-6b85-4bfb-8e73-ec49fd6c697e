/**
 * Contextual Analysis Configuration Tests
 * 
 * Tests for the centralized configuration system including:
 * - Default configuration values
 * - Configuration merging
 * - Configuration validation
 * - Edge cases and error handling
 */

import { jest } from '@jest/globals';
import {
    DEFAULT_CONTEXTUAL_CONFIG,
    createContextualConfig,
    validateContextualConfig
} from '@/agent/services/conversation/ContextualAnalysisConfig.js';

describe('ContextualAnalysisConfig', () => {
    describe('Default Configuration', () => {
        test('should have all required default values', () => {
            expect(DEFAULT_CONTEXTUAL_CONFIG.silenceThreshold).toBe(15000);
            expect(DEFAULT_CONTEXTUAL_CONFIG.engagementThreshold).toBe(0.4);
            expect(DEFAULT_CONTEXTUAL_CONFIG.stalledConversationThreshold).toBe(30000);
            expect(DEFAULT_CONTEXTUAL_CONFIG.maxRecentInteractions).toBe(10);
        });

        test('should have proactive configuration', () => {
            expect(DEFAULT_CONTEXTUAL_CONFIG.proactive).toBeDefined();
            expect(DEFAULT_CONTEXTUAL_CONFIG.proactive.continuousAnalysisFrequency).toBe(2000);
            expect(DEFAULT_CONTEXTUAL_CONFIG.proactive.decisionCooldown).toBe(5000);
            expect(DEFAULT_CONTEXTUAL_CONFIG.proactive.confidenceThreshold).toBe(0.6);
        });

        test('should have audio configuration', () => {
            expect(DEFAULT_CONTEXTUAL_CONFIG.audio).toBeDefined();
            expect(DEFAULT_CONTEXTUAL_CONFIG.audio.volumeThreshold).toBe(0.1);
            expect(DEFAULT_CONTEXTUAL_CONFIG.audio.qualityThreshold).toBe(0.6);
            expect(DEFAULT_CONTEXTUAL_CONFIG.audio.freshContextWindow).toBe(5000);
        });

        test('should have visual configuration', () => {
            expect(DEFAULT_CONTEXTUAL_CONFIG.visual).toBeDefined();
            expect(DEFAULT_CONTEXTUAL_CONFIG.visual.engagementThreshold).toBe(0.5);
            expect(DEFAULT_CONTEXTUAL_CONFIG.visual.freshContextWindow).toBe(3000);
            expect(DEFAULT_CONTEXTUAL_CONFIG.visual.faceDetectionWeight).toBe(0.25);
        });

        test('should have trigger configurations', () => {
            expect(DEFAULT_CONTEXTUAL_CONFIG.triggers).toBeDefined();
            expect(DEFAULT_CONTEXTUAL_CONFIG.triggers.userSilence.baseConfidence).toBe(0.4);
            expect(DEFAULT_CONTEXTUAL_CONFIG.triggers.lowEngagement.priority).toBe('high');
            expect(DEFAULT_CONTEXTUAL_CONFIG.triggers.guidanceNeeded.baseConfidence).toBe(0.6);
        });

        test('should have speaking decision weights', () => {
            expect(DEFAULT_CONTEXTUAL_CONFIG.speakingDecision).toBeDefined();
            expect(DEFAULT_CONTEXTUAL_CONFIG.speakingDecision.prolongedSilenceWeight).toBe(0.4);
            expect(DEFAULT_CONTEXTUAL_CONFIG.speakingDecision.lowEngagementWeight).toBe(0.5);
            expect(DEFAULT_CONTEXTUAL_CONFIG.speakingDecision.guidanceOpportunityWeight).toBe(0.6);
        });
    });

    describe('Configuration Creation', () => {
        test('should create config with defaults when no overrides provided', () => {
            const config = createContextualConfig();

            expect(config.silenceThreshold).toBe(15000);
            expect(config.proactive.continuousAnalysisFrequency).toBe(2000);
            expect(config.audio.volumeThreshold).toBe(0.1);
            expect(config.visual.engagementThreshold).toBe(0.5);
        });

        test('should override top-level properties correctly', () => {
            const config = createContextualConfig({
                silenceThreshold: 20000,
                engagementThreshold: 0.5,
                maxRecentInteractions: 15
            });

            expect(config.silenceThreshold).toBe(20000);
            expect(config.engagementThreshold).toBe(0.5);
            expect(config.maxRecentInteractions).toBe(15);
            // Ensure other defaults are preserved
            expect(config.stalledConversationThreshold).toBe(30000);
        });

        test('should override nested properties correctly', () => {
            const config = createContextualConfig({
                proactive: {
                    confidenceThreshold: 0.8,
                    decisionCooldown: 3000
                },
                audio: {
                    volumeThreshold: 0.2
                }
            });

            expect(config.proactive.confidenceThreshold).toBe(0.8);
            expect(config.proactive.decisionCooldown).toBe(3000);
            // Ensure other proactive defaults are preserved
            expect(config.proactive.continuousAnalysisFrequency).toBe(2000);

            expect(config.audio.volumeThreshold).toBe(0.2);
            // Ensure other audio defaults are preserved
            expect(config.audio.qualityThreshold).toBe(0.6);
        });

        test('should handle deep nested overrides', () => {
            const config = createContextualConfig({
                triggers: {
                    userSilence: {
                        baseConfidence: 0.6
                    },
                    lowEngagement: {
                        priority: 'medium'
                    }
                }
            });

            expect(config.triggers.userSilence.baseConfidence).toBe(0.6);
            expect(config.triggers.userSilence.maxConfidence).toBe(1.0); // Default preserved
            expect(config.triggers.lowEngagement.priority).toBe('medium');
            expect(config.triggers.lowEngagement.baseConfidence).toBe(0.5); // Default preserved
        });

        test('should handle partial overrides without affecting other properties', () => {
            const config = createContextualConfig({
                visual: {
                    engagementThreshold: 0.7
                }
            });

            expect(config.visual.engagementThreshold).toBe(0.7);
            expect(config.visual.freshContextWindow).toBe(3000); // Default preserved
            expect(config.audio.volumeThreshold).toBe(0.1); // Other sections preserved
        });

        test('should handle array properties correctly', () => {
            const config = createContextualConfig({
                multimodalEngagement: {
                    defaultWeights: {
                        text: 0.2,
                        audio: 0.5,
                        visual: 0.3
                    }
                }
            });

            expect(config.multimodalEngagement.defaultWeights.text).toBe(0.2);
            expect(config.multimodalEngagement.defaultWeights.audio).toBe(0.5);
            expect(config.multimodalEngagement.defaultWeights.visual).toBe(0.3);
        });
    });

    describe('Configuration Validation', () => {
        test('should validate correct configuration', () => {
            const validConfig = createContextualConfig();
            const validation = validateContextualConfig(validConfig);

            expect(validation.isValid).toBe(true);
            expect(validation.errors).toHaveLength(0);
        });

        test('should detect invalid numeric thresholds', () => {
            const invalidConfig = {
                silenceThreshold: -1000,
                engagementThreshold: 'invalid',
                stalledConversationThreshold: null
            };

            const validation = validateContextualConfig(invalidConfig);

            expect(validation.isValid).toBe(false);
            expect(validation.errors).toContain('silenceThreshold must be a positive number');
            expect(validation.errors).toContain('engagementThreshold must be a positive number');
            expect(validation.errors).toContain('stalledConversationThreshold must be a positive number');
        });

        test('should validate proactive configuration', () => {
            const invalidConfig = {
                silenceThreshold: 15000,
                engagementThreshold: 0.4,
                stalledConversationThreshold: 30000,
                proactive: {
                    continuousAnalysisFrequency: 500, // Too low
                    confidenceThreshold: 1.5 // Too high
                }
            };

            const validation = validateContextualConfig(invalidConfig);

            expect(validation.isValid).toBe(false);
            expect(validation.errors).toContain('proactive.continuousAnalysisFrequency must be at least 1000ms');
            expect(validation.errors).toContain('proactive.confidenceThreshold must be between 0 and 1');
        });

        test('should validate multimodal thresholds', () => {
            const invalidConfig = {
                silenceThreshold: 15000,
                engagementThreshold: 0.4,
                stalledConversationThreshold: 30000,
                audio: {
                    volumeThreshold: 'invalid'
                },
                visual: {
                    engagementThreshold: 'also_invalid'
                }
            };

            const validation = validateContextualConfig(invalidConfig);

            expect(validation.isValid).toBe(false);
            expect(validation.errors).toContain('audio.volumeThreshold must be a number');
            expect(validation.errors).toContain('visual.engagementThreshold must be a number');
        });

        test('should allow valid edge case values', () => {
            const edgeCaseConfig = {
                silenceThreshold: 0, // Zero is valid
                engagementThreshold: 0, // Zero is valid
                stalledConversationThreshold: 1, // Minimum positive
                proactive: {
                    continuousAnalysisFrequency: 1000, // Minimum allowed
                    confidenceThreshold: 0 // Minimum allowed
                }
            };

            const validation = validateContextualConfig(edgeCaseConfig);

            expect(validation.isValid).toBe(true);
            expect(validation.errors).toHaveLength(0);
        });

        test('should reject extreme values', () => {
            const extremeConfig = {
                silenceThreshold: 15000,
                engagementThreshold: 0.4,
                stalledConversationThreshold: 30000,
                proactive: {
                    continuousAnalysisFrequency: 100, // Too low
                    confidenceThreshold: 2.0 // Too high
                }
            };

            const validation = validateContextualConfig(extremeConfig);

            expect(validation.isValid).toBe(false);
            expect(validation.errors.length).toBeGreaterThan(0);
        });
    });

    describe('Edge Cases', () => {
        test('should handle null and undefined overrides', () => {
            const config1 = createContextualConfig(null);
            const config2 = createContextualConfig(undefined);

            expect(config1.silenceThreshold).toBe(15000);
            expect(config2.silenceThreshold).toBe(15000);
        });

        test('should handle empty object overrides', () => {
            const config = createContextualConfig({});

            expect(config.silenceThreshold).toBe(15000);
            expect(config.proactive.continuousAnalysisFrequency).toBe(2000);
        });

        test('should handle nested null values', () => {
            const config = createContextualConfig({
                proactive: null,
                audio: {
                    volumeThreshold: null
                }
            });

            // Should use defaults when null values are provided
            expect(config.proactive).toBeDefined();
            expect(config.audio.volumeThreshold).toBeNull();
        });

        test('should preserve function properties if any exist', () => {
            const customFunction = () => 'test';
            const config = createContextualConfig({
                customFunction
            });

            expect(config.customFunction).toBe(customFunction);
        });

        test('should handle circular references gracefully', () => {
            const circularObj = { prop: 'value' };
            circularObj.self = circularObj;

            // Should not throw
            expect(() => {
                createContextualConfig({ circularObj });
            }).not.toThrow();
        });
    });

    describe('Configuration Usage Scenarios', () => {
        test('should support development environment overrides', () => {
            const devConfig = createContextualConfig({
                proactive: {
                    continuousAnalysisFrequency: 5000, // Slower for debugging
                    confidenceThreshold: 0.3 // Lower threshold for testing
                },
                audio: {
                    volumeThreshold: 0.05 // More sensitive
                }
            });

            expect(devConfig.proactive.continuousAnalysisFrequency).toBe(5000);
            expect(devConfig.proactive.confidenceThreshold).toBe(0.3);
            expect(devConfig.audio.volumeThreshold).toBe(0.05);
        });

        test('should support production environment overrides', () => {
            const prodConfig = createContextualConfig({
                proactive: {
                    confidenceThreshold: 0.8 // Higher threshold for production
                },
                conversationHealth: {
                    stalledThreshold: 45000 // Longer patience in production
                }
            });

            expect(prodConfig.proactive.confidenceThreshold).toBe(0.8);
            expect(prodConfig.conversationHealth.stalledThreshold).toBe(45000);
        });

        test('should support user preference overrides', () => {
            const userConfig = createContextualConfig({
                silenceThreshold: 10000, // User prefers quicker responses
                visual: {
                    engagementThreshold: 0.3 // User has different engagement patterns
                }
            });

            expect(userConfig.silenceThreshold).toBe(10000);
            expect(userConfig.visual.engagementThreshold).toBe(0.3);
        });

        test('should support A/B testing configurations', () => {
            const variantA = createContextualConfig({
                speakingDecision: {
                    prolongedSilenceWeight: 0.3
                }
            });

            const variantB = createContextualConfig({
                speakingDecision: {
                    prolongedSilenceWeight: 0.5
                }
            });

            expect(variantA.speakingDecision.prolongedSilenceWeight).toBe(0.3);
            expect(variantB.speakingDecision.prolongedSilenceWeight).toBe(0.5);
            // Other weights should remain the same
            expect(variantA.speakingDecision.lowEngagementWeight).toBe(0.5);
            expect(variantB.speakingDecision.lowEngagementWeight).toBe(0.5);
        });
    });

    describe('Performance Considerations', () => {
        test('should create configurations efficiently', () => {
            const start = Date.now();

            for (let i = 0; i < 1000; i++) {
                createContextualConfig({
                    silenceThreshold: 15000 + i,
                    proactive: { confidenceThreshold: 0.6 + (i * 0.0001) }
                });
            }

            const duration = Date.now() - start;
            expect(duration).toBeLessThan(100); // Should be very fast
        });

        test('should validate configurations efficiently', () => {
            const config = createContextualConfig();
            const start = Date.now();

            for (let i = 0; i < 1000; i++) {
                validateContextualConfig(config);
            }

            const duration = Date.now() - start;
            expect(duration).toBeLessThan(50); // Should be very fast
        });
    });
}); 