/**
 * Refactored Architecture Integration Test
 * 
 * Tests that the refactored system works correctly:
 * 1. BaseStateManager focuses only on LangGraph StateGraph operations
 * 2. ContextualAnalysisService handles conversation analysis
 * 3. Conversation tools provide LLM access to contextual decisions
 * 4. Tool integration works with the new architecture
 */

import { vi, describe, test, expect, beforeAll, beforeEach } from 'vitest';

describe('Refactored Architecture Integration', () => {
    let BaseStateManager;
    let ContextualAnalysisService;
    let conversationTools;
    let toolManager;
    let mockAgentService;

    beforeAll(async () => {
        // Import components
        const { BaseStateManager: BSM } = await import('@/agent/state/BaseStateManager.js');
        const { ContextualAnalysisService: CAS } = await import('@/agent/services/conversation/ContextualAnalysisService.js');
        const conversationModule = await import('@/agent/tools/conversation/conversation.js');
        const { toolManager: tm } = await import('@/agent/tools/index.js');

        BaseStateManager = BSM;
        ContextualAnalysisService = CAS;
        conversationTools = conversationModule;
        toolManager = tm;

        // Setup mock agent service
        mockAgentService = {
            callbacks: {},
            generateResponse: vi.fn()
        };
    });

    beforeEach(() => {
        // Clear tool manager
        toolManager.clearAllTools();
        
        // Reset mocks
        vi.clearAllMocks();
    });

    describe('BaseStateManager Refactoring', () => {
        test('should focus only on LangGraph StateGraph operations', async () => {
            const stateManager = new BaseStateManager(mockAgentService);

            // Verify no contextual analysis methods exist
            expect(stateManager._performContextualAnalysis).toBeUndefined();
            expect(stateManager._updateContextualState).toBeUndefined();
            expect(stateManager._calculateEngagementLevel).toBeUndefined();
            expect(stateManager._assessConversationHealth).toBeUndefined();
            expect(stateManager.updateUserInteraction).toBeUndefined();
            expect(stateManager.updateAvatarResponse).toBeUndefined();
            expect(stateManager.getContextualResponseStats).toBeUndefined();

            // Verify core LangGraph methods exist
            expect(stateManager.initializeWorkflow).toBeDefined();
            expect(stateManager.executeWorkflow).toBeDefined();
            expect(stateManager.streamWorkflow).toBeDefined();
            expect(stateManager.getWorkflowState).toBeDefined();
            expect(stateManager.updateWorkflowState).toBeDefined();

            // Verify state management methods exist
            expect(stateManager.transitionToState).toBeDefined();
            expect(stateManager.getStateInfo).toBeDefined();
            expect(stateManager.resetState).toBeDefined();
        });

        test('should handle state transitions correctly', async () => {
            const stateManager = new BaseStateManager(mockAgentService);
            
            // Test state transition
            const transitionResult = await stateManager.transitionToState('LISTENING');
            expect(transitionResult).toBe(true);
            expect(stateManager.currentState).toBe('LISTENING');

            // Test state info
            const stateInfo = stateManager.getStateInfo();
            expect(stateInfo.current).toBe('LISTENING');
            expect(stateInfo.previous).toBe('IDLE');
            expect(stateInfo.isListening).toBe(true);
        });

        test('should not have contextual response configuration', () => {
            const stateManager = new BaseStateManager(mockAgentService);
            
            expect(stateManager.contextualResponse).toBeUndefined();
        });
    });

    describe('ContextualAnalysisService', () => {
        test('should handle contextual analysis independently', async () => {
            const service = new ContextualAnalysisService();

            // Test interaction tracking
            service.updateUserInteraction({
                content: 'Hello',
                type: 'message'
            });

            service.updateAvatarResponse({
                content: 'Hi there!',
                type: 'response'
            });

            // Test contextual analysis
            const analysisResult = service.performContextualAnalysis();
            expect(analysisResult.success).toBe(true);
            expect(analysisResult.analysis).toBeDefined();
            expect(analysisResult.analysis.context).toBeDefined();

            // Test communication mode decision
            const decisionResult = service.decideCommunicationMode();
            expect(decisionResult.success).toBe(true);
            expect(decisionResult.decision).toBeDefined();
            expect(decisionResult.decision.recommendedMode).toBeDefined();
        });

        test('should calculate engagement levels correctly', () => {
            const service = new ContextualAnalysisService();

            // No interactions - should return default engagement
            const initialEngagement = service.calculateEngagementLevel();
            expect(initialEngagement).toBe(0.5);

            // Add interactions
            service.updateUserInteraction({ content: 'Test 1' });
            service.updateUserInteraction({ content: 'Test 2' });

            const updatedEngagement = service.calculateEngagementLevel();
            expect(updatedEngagement).toBeGreaterThan(0.3);
        });

        test('should assess conversation health correctly', () => {
            const service = new ContextualAnalysisService();

            // Initial health should be healthy
            let health = service.assessConversationHealth();
            expect(health).toBe('healthy');

            // Simulate stalled conversation (modify silence duration)
            service.context.silenceDuration = 35000; // 35 seconds
            health = service.assessConversationHealth();
            expect(health).toBe('stalled');

            // Simulate declining engagement
            service.context.silenceDuration = 10000; // 10 seconds
            service.context.engagementLevel = 0.2;
            health = service.assessConversationHealth();
            expect(health).toBe('declining');
        });
    });

    describe('Conversation Tools Integration', () => {
        test('should provide LLM access to contextual analysis', async () => {
            // Test analyze_conversation_context tool
            const result = await conversationTools.analyzeConversationContext.invoke({
                forceAnalysis: true,
                includeRecommendations: true,
                analysisDepth: 'detailed'
            });

            expect(result.success).toBe(true);
            expect(result.analysis).toBeDefined();
            expect(result.analysis.context).toBeDefined();
            expect(result.analysis.currentState).toBeDefined();
        });

        test('should provide communication mode decisions', async () => {
            const result = await conversationTools.decideCommunicationMode.invoke({
                considerContext: true,
                considerEngagement: true,
                considerSilence: true,
                urgencyThreshold: 'medium'
            });

            expect(result.success).toBe(true);
            expect(result.decision).toBeDefined();
            expect(result.decision.recommendedMode).toBeDefined();
            expect(result.decision.confidence).toBeDefined();
            expect(result.decision.urgency).toBeDefined();
        });

        test('should track conversation topics', async () => {
            const result = await conversationTools.trackConversationTopics.invoke({
                extractTopics: true,
                analyzeTrendMentioned: true,
                identifyOpportunities: true
            });

            expect(result.success).toBe(true);
            expect(result.topicAnalysis).toBeDefined();
            expect(result.topicAnalysis.conversationHealth).toBeDefined();
            expect(result.topicAnalysis.suggestions).toBeDefined();
        });

        test('should manage conversation memory', async () => {
            const result = await conversationTools.manageConversationMemory.invoke({
                retrievePatterns: true,
                storeInteraction: false,
                analyzePreferences: true
            });

            expect(result.success).toBe(true);
            expect(result.memoryData).toBeDefined();
            expect(result.memoryData.contextSummary).toBeDefined();
        });

        test('should update interaction history', async () => {
            const result = await conversationTools.updateInteractionHistory.invoke({
                interactionType: 'user',
                content: 'Test message',
                metadata: { timestamp: Date.now() }
            });

            expect(result.success).toBe(true);
            expect(result.interactionRecorded).toBe(true);
            expect(result.currentStats).toBeDefined();
        });
    });

    describe('Tool Integration', () => {
        test('should register conversation tools in tool manager', async () => {
            // Register the conversation tool collection
            toolManager.registerToolCollection(
                conversationTools.contextualConversationToolCollection.name,
                conversationTools.contextualConversationToolCollection.tools,
                conversationTools.contextualConversationToolCollection.description
            );

            // Verify tools are registered
            const stats = toolManager.getStatistics();
            expect(stats.totalTools).toBeGreaterThan(0);
            expect(stats.totalCollections).toBeGreaterThan(0);

            // Verify specific tools exist
            expect(toolManager.getTool('analyze_conversation_context')).toBeDefined();
            expect(toolManager.getTool('decide_communication_mode')).toBeDefined();
            expect(toolManager.getTool('track_conversation_topics')).toBeDefined();
            expect(toolManager.getTool('manage_conversation_memory')).toBeDefined();
            expect(toolManager.getTool('update_interaction_history')).toBeDefined();
        });

        test('should execute conversation tools through tool manager', async () => {
            // Register tools
            toolManager.registerToolCollection(
                conversationTools.contextualConversationToolCollection.name,
                conversationTools.contextualConversationToolCollection.tools,
                conversationTools.contextualConversationToolCollection.description
            );

            // Execute tool through manager
            const result = await toolManager.executeTool('analyze_conversation_context', {
                forceAnalysis: true,
                includeRecommendations: true
            });

            expect(result.success).toBe(true);
            expect(result.analysis).toBeDefined();
        });
    });

    describe('Architecture Separation', () => {
        test('should maintain clean separation between state management and contextual analysis', () => {
            const stateManager = new BaseStateManager(mockAgentService);
            const contextualService = new ContextualAnalysisService();

            // BaseStateManager should not have contextual analysis methods
            expect(stateManager._performContextualAnalysis).toBeUndefined();
            expect(stateManager.updateUserInteraction).toBeUndefined();

            // ContextualAnalysisService should have all contextual methods
            expect(contextualService.performContextualAnalysis).toBeDefined();
            expect(contextualService.updateUserInteraction).toBeDefined();
            expect(contextualService.calculateEngagementLevel).toBeDefined();
            expect(contextualService.assessConversationHealth).toBeDefined();

            // Should be able to work independently
            expect(() => stateManager.getStateInfo()).not.toThrow();
            expect(() => contextualService.getContextualStats()).not.toThrow();
        });

        test('should allow LLM to make contextual decisions through tools', async () => {
            // Simulate LLM calling tools for contextual analysis
            const analysisResult = await conversationTools.analyzeConversationContext.invoke({
                analysisDepth: 'comprehensive'
            });

            const decisionResult = await conversationTools.decideCommunicationMode.invoke({
                urgencyThreshold: 'high'
            });

            // Both should work independently of BaseStateManager
            expect(analysisResult.success).toBe(true);
            expect(decisionResult.success).toBe(true);
            
            // Results should provide actionable insights
            expect(decisionResult.decision.recommendedMode).toBeDefined();
            expect(decisionResult.decision.actions).toBeDefined();
        });

        test('should maintain backward compatibility for state operations', async () => {
            const stateManager = new BaseStateManager(mockAgentService);

            // State operations should work as before
            await stateManager.transitionToState('PROCESSING');
            expect(stateManager.currentState).toBe('PROCESSING');

            const stateInfo = stateManager.getStateInfo();
            expect(stateInfo.isProcessing).toBe(true);

            stateManager.resetState();
            expect(stateManager.currentState).toBe('IDLE');
        });
    });

    describe('Integration Test: Full Workflow', () => {
        test('should support complete LLM-driven contextual workflow', async () => {
            // 1. Initialize components
            const stateManager = new BaseStateManager(mockAgentService);
            
            // Register conversation tools
            toolManager.registerToolCollection(
                conversationTools.contextualConversationToolCollection.name,
                conversationTools.contextualConversationToolCollection.tools,
                conversationTools.contextualConversationToolCollection.description
            );

            // 2. Simulate state management (BaseStateManager)
            await stateManager.transitionToState('LISTENING');
            expect(stateManager.currentState).toBe('LISTENING');

            // 3. Simulate LLM calling contextual analysis tools
            const analysisResult = await toolManager.executeTool('analyze_conversation_context', {
                forceAnalysis: true,
                includeRecommendations: true
            });

            expect(analysisResult.success).toBe(true);

            // 4. Simulate LLM making communication decisions
            const decisionResult = await toolManager.executeTool('decide_communication_mode', {
                urgencyThreshold: 'medium'
            });

            expect(decisionResult.success).toBe(true);
            expect(decisionResult.decision.recommendedMode).toBeDefined();

            // 5. Update interaction history through tools
            const historyResult = await toolManager.executeTool('update_interaction_history', {
                interactionType: 'user',
                content: 'User input received'
            });

            expect(historyResult.success).toBe(true);

            // 6. State management continues independently
            await stateManager.transitionToState('PROCESSING');
            expect(stateManager.currentState).toBe('PROCESSING');

            // Verify both systems work together without interference
            const finalStateInfo = stateManager.getStateInfo();
            expect(finalStateInfo.isProcessing).toBe(true);
            expect(finalStateInfo.history).toHaveLength(2); // IDLE->LISTENING, LISTENING->PROCESSING
        });
    });
});

export default {};