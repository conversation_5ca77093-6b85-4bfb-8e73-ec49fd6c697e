/**
 * Tool Validation Fix Test
 * 
 * Tests the enhanced validateToolsForLangGraph function that fixes the
 * "Cannot set properties of undefined (setting 'name')" error.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { validateToolsForLangGraph } from '@/agent/config/LangGraphConfig.js';
import { debugToolsForLangGraph } from '@/agent/config/debug/ToolDebugger.js';

describe('Tool Validation Fix Tests', () => {
    let mockLogger;
    
    beforeEach(() => {
        // Mock the logger to capture validation messages
        mockLogger = {
            debug: vi.fn(),
            info: vi.fn(),
            warn: vi.fn(),
            error: vi.fn()
        };
    });

    describe('Undefined/Null Tool Handling', () => {
        it('should filter out undefined tools without throwing', () => {
            const toolsWithUndefined = [
                { name: 'valid_tool', description: 'Valid tool', schema: {}, invoke: vi.fn() },
                undefined,
                { name: 'another_valid_tool', description: 'Another valid tool', schema: {}, invoke: vi.fn() },
                null,
                { name: 'third_valid_tool', description: 'Third valid tool', schema: {}, invoke: vi.fn() }
            ];

            const result = validateToolsForLangGraph(toolsWithUndefined, {
                skipInvalidTools: true,
                requireDescription: true,
                requireSchema: true,
                requireInvokeMethod: true
            });

            expect(result).toHaveLength(3);
            expect(result.map(t => t.name)).toEqual(['valid_tool', 'another_valid_tool', 'third_valid_tool']);
        });

        it('should throw descriptive error for undefined tools when skipInvalidTools is false', () => {
            const toolsWithUndefined = [
                { name: 'valid_tool', description: 'Valid tool', schema: {}, invoke: vi.fn() },
                undefined
            ];

            expect(() => {
                validateToolsForLangGraph(toolsWithUndefined, {
                    skipInvalidTools: false,
                    requireDescription: true,
                    requireSchema: true,
                    requireInvokeMethod: true
                });
            }).toThrow('Tool at index 1 is undefined. This will cause LangGraph ReactAgent to fail with "Cannot set properties of undefined (setting \'name\')"');
        });

        it('should handle null tools correctly', () => {
            const toolsWithNull = [
                { name: 'valid_tool', description: 'Valid tool', schema: {}, invoke: vi.fn() },
                null
            ];

            expect(() => {
                validateToolsForLangGraph(toolsWithNull, {
                    skipInvalidTools: false
                });
            }).toThrow('Tool at index 1 is null');
        });
    });

    describe('Non-Object Tool Handling', () => {
        it('should filter out non-object tools', () => {
            const toolsWithNonObjects = [
                { name: 'valid_tool', description: 'Valid tool', schema: {}, invoke: vi.fn() },
                'string_tool',
                123,
                true,
                { name: 'another_valid_tool', description: 'Another valid tool', schema: {}, invoke: vi.fn() }
            ];

            const result = validateToolsForLangGraph(toolsWithNonObjects, {
                skipInvalidTools: true,
                requireDescription: true,
                requireSchema: true,
                requireInvokeMethod: true
            });

            expect(result).toHaveLength(2);
            expect(result.map(t => t.name)).toEqual(['valid_tool', 'another_valid_tool']);
        });
    });

    describe('Missing Name Property', () => {
        it('should filter out tools without name property', () => {
            const toolsWithoutNames = [
                { name: 'valid_tool', description: 'Valid tool', schema: {}, invoke: vi.fn() },
                { description: 'Tool without name', schema: {}, invoke: vi.fn() },
                { name: '', description: 'Tool with empty name', schema: {}, invoke: vi.fn() },
                { name: 'another_valid_tool', description: 'Another valid tool', schema: {}, invoke: vi.fn() }
            ];

            const result = validateToolsForLangGraph(toolsWithoutNames, {
                skipInvalidTools: true,
                requireDescription: true,
                requireSchema: true,
                requireInvokeMethod: true
            });

            expect(result).toHaveLength(2);
            expect(result.map(t => t.name)).toEqual(['valid_tool', 'another_valid_tool']);
        });
    });

    describe('Invalid Invoke Method', () => {
        it('should filter out tools with non-function invoke methods', () => {
            const toolsWithInvalidInvoke = [
                { name: 'valid_tool', description: 'Valid tool', schema: {}, invoke: vi.fn() },
                { name: 'invalid_invoke_string', description: 'Invalid invoke', schema: {}, invoke: 'not_a_function' },
                { name: 'invalid_invoke_number', description: 'Invalid invoke', schema: {}, invoke: 123 },
                { name: 'another_valid_tool', description: 'Another valid tool', schema: {}, invoke: vi.fn() }
            ];

            const result = validateToolsForLangGraph(toolsWithInvalidInvoke, {
                skipInvalidTools: true,
                requireDescription: true,
                requireSchema: true,
                requireInvokeMethod: true
            });

            expect(result).toHaveLength(2);
            expect(result.map(t => t.name)).toEqual(['valid_tool', 'another_valid_tool']);
        });
    });

    describe('Non-Array Input', () => {
        it('should handle non-array input gracefully', () => {
            const result = validateToolsForLangGraph(null, {
                skipInvalidTools: true
            });

            expect(result).toEqual([]);
        });

        it('should throw error for non-array input when skipInvalidTools is false', () => {
            expect(() => {
                validateToolsForLangGraph('not_an_array', {
                    skipInvalidTools: false
                });
            }).toThrow('Tools must be an array, got string');
        });
    });

    describe('Tool Normalization', () => {
        it('should normalize tools with all required properties', () => {
            const rawTools = [
                {
                    name: 'test_tool',
                    description: 'Test tool',
                    schema: { type: 'object', properties: { param: { type: 'string' } } },
                    invoke: vi.fn()
                }
            ];

            const result = validateToolsForLangGraph(rawTools, {
                skipInvalidTools: true,
                requireDescription: true,
                requireSchema: true,
                requireInvokeMethod: true
            });

            expect(result).toHaveLength(1);
            expect(result[0]).toMatchObject({
                name: 'test_tool',
                description: 'Test tool',
                schema: { type: 'object', properties: { param: { type: 'string' } } }
            });
            expect(typeof result[0].invoke).toBe('function');
        });

        it('should add default description and schema when missing', () => {
            const rawTools = [
                {
                    name: 'test_tool',
                    invoke: vi.fn()
                }
            ];

            const result = validateToolsForLangGraph(rawTools, {
                skipInvalidTools: true,
                requireDescription: true,
                requireSchema: true,
                requireInvokeMethod: true,
                defaultSchema: { type: 'object', properties: {} }
            });

            expect(result).toHaveLength(1);
            expect(result[0].description).toBe('Tool: test_tool');
            expect(result[0].schema).toEqual({ type: 'object', properties: {} });
        });
    });

    describe('Debug Function Tests', () => {
        it('should provide detailed debugging information', () => {
            const problematicTools = [
                { name: 'valid_tool', description: 'Valid tool', schema: {}, invoke: vi.fn() },
                undefined,
                null,
                'string_tool',
                { description: 'No name', schema: {}, invoke: vi.fn() },
                { name: 'no_invoke', description: 'No invoke method', schema: {} }
            ];

            const debugReport = debugToolsForLangGraph(problematicTools);

            expect(debugReport.totalTools).toBe(6);
            expect(debugReport.validTools).toHaveLength(1);
            expect(debugReport.undefinedTools).toHaveLength(2); // undefined and null
            expect(debugReport.invalidTools.length).toBeGreaterThan(0);
            expect(debugReport.recommendations.length).toBeGreaterThan(0);
        });
    });

    describe('Real-world Scenario Tests', () => {
        it('should handle typical tool registration failure scenarios', () => {
            // Simulate tools that might come from failed async imports or service creation
            const realisticFailedTools = [
                { name: 'control_avatar_speech', description: 'Control avatar speech', schema: {}, invoke: vi.fn() },
                undefined, // Failed tool creation
                { name: 'select_animation', description: 'Select animation', schema: {}, invoke: vi.fn() },
                null, // Another failed tool creation
                { name: 'web_search' }, // Missing invoke method
                { name: 123, description: 'Invalid name type', schema: {}, invoke: vi.fn() }, // Wrong name type
                { name: 'speak_response', description: 'Speak response', schema: {}, invoke: vi.fn() }
            ];

            const result = validateToolsForLangGraph(realisticFailedTools, {
                skipInvalidTools: true,
                requireDescription: true,
                requireSchema: true,
                requireInvokeMethod: true
            });

            expect(result).toHaveLength(4); // Only valid tools should remain
            expect(result.map(t => t.name)).toEqual([
                'control_avatar_speech',
                'select_animation',
                '123', // Should be converted to string
                'speak_response'
            ]);
        });
    });
});