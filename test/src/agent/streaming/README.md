# Agent Streaming Tests - Unified Architecture

This directory contains all tests related to the unified LangGraph agent streaming functionality after successful consolidation.

## Test Files

### Core Streaming Tests
- **`StreamingManager.test.js`** - Tests for the unified LangGraph streaming implementation (26 tests passing)
  - Unified streaming with LangGraph + performance optimization
  - Sub-600ms response targeting and performance coordination
  - Stream configuration and validation
  - State update processing
  - Message chunk processing  
  - Tool execution chunks
  - Session management and cleanup
  - Error handling and recovery
  - Enterprise-grade patterns validation

### Integration Streaming Tests
- **`langchain-streaming.test.js`** - Integration tests for LangGraph agent streaming using real vLLM API calls
  - Real vLLM API integration via apiProxy.ts
  - Agent service streaming workflows
  - Memory and context integration
  - LangGraph native streaming capabilities
  - Performance monitoring
  - Error handling with real endpoints

## Related Streaming Tests

### Media Streaming
- `/test/src/media/streaming-audio.test.js` - Audio streaming and capture tests

### General LangChain Streaming  
- `/test/langchain/streaming-test.js` - General LangChain streaming functionality tests

## Running Tests

```bash
# Run all agent streaming tests
npm test test/src/agent/streaming/

# Run specific streaming test files
npm test test/src/agent/streaming/StreamingManager.test.js
npm test test/src/agent/streaming/langchain-streaming.test.js

# Run with real API integration (requires API setup)
TEST_REAL_API=true npm test test/src/agent/streaming/langchain-streaming.test.js
```

## Test Configuration

The integration tests support configuration via environment variables:
- `TEST_REAL_API=true` - Enable real API calls for integration testing
- API endpoints are configured via `apiProxy.ts`

## Unified Architecture

These tests verify the unified consolidated LangGraph streaming pipeline:

1. **LangGraph Agent Service** (`@/agent/core.js`)
2. **Unified Streaming Manager** (`@/agent/streaming/StreamingManager.js` - 1500+ lines)
3. **Enhanced Input Coordination** (`@/media/modality/index.ts` - 1200+ lines with LangGraph integration)
4. **Performance Coordination Service** (New coordination layer for Grade A enterprise patterns)
5. **vLLM API Integration** (`@/utils/apiProxy.ts`)
6. **Memory Management** (conversation context)
7. **Tool Integration** (animation, conversation tools)

The unified streaming implementation consolidates previous functionality with:
- **Grade A Enterprise Patterns**: B+ → A architecture improvement
- **Sub-600ms Response Targeting**: Performance-optimized streaming coordination
- **LangGraph + Performance Integration**: Unified streaming with enterprise optimization
- **Enhanced Multimodal Processing**: LangGraph message conversion and coordination
- **Eliminated Redundancy**: Consolidated OptimizedStreamingManager and MultimodalInputHandler
- **Multi-modal streaming** (text, tool calls, state updates)
- **Session-based conversation management**
- **Real-time performance monitoring and coordination**
- **Graceful error handling and recovery**
