/**
 * Unified StreamingManager Tests
 * Tests the consolidated LangGraph streaming implementation with:
 * - Enhanced performance coordination features
 * - Unified patterns with consolidated streaming optimization
 * - Sub-600ms response time validation
 * - Comprehensive multimodal support
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { StreamingManager } from '@/agent/streaming/StreamingManager.js';

// Mock logger
vi.mock('@/utils/logger.js', () => ({
    createLogger: vi.fn(() => ({
        info: vi.fn(),
        debug: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        setLogLevel: vi.fn()
    }))
}));

describe('StreamingManager', () => {
    let streamingManager;
    let mockAgent;

    beforeEach(() => {
        streamingManager = new StreamingManager({
            enableValueStream: true,
            enableUpdateStream: true,
            enableMessageStream: true,
            bufferSize: 1000,
            // Enhanced configuration options
            preferredMode: 'messages',
            enableMultiMode: true,
            chunkBuffer: 50,
            targetLatency: 600,
            adaptiveThrottling: true
        });

        // Mock LangGraph agent with enhanced capabilities
        mockAgent = {
            stream: vi.fn(),
            streamEvents: vi.fn()
        };

        vi.clearAllMocks();
    });

    afterEach(() => {
        streamingManager.dispose();
    });

    describe('Initialization', () => {
        it('should initialize with correct configuration', () => {
            expect(streamingManager).toBeDefined();
            expect(streamingManager.options.enableValueStream).toBe(true);
            expect(streamingManager.options.enableUpdateStream).toBe(true);
            expect(streamingManager.options.enableMessageStream).toBe(true);
            expect(streamingManager.options.bufferSize).toBe(1000);
        });

        it('should have empty active sessions initially', () => {
            const sessions = streamingManager.getActiveSessions();
            expect(Object.keys(sessions)).toHaveLength(0);
        });
    });

    describe('Stream Configuration', () => {
        it('should create valid stream config', () => {
            const config = streamingManager.createStreamConfig({
                modes: ['updates', 'messages'],
                sessionId: 'test_session'
            });

            expect(config.streamMode).toEqual(['updates', 'messages']);
            expect(config.configurable.thread_id).toBe('test_session');
        });

        it('should validate stream modes', () => {
            const config = streamingManager.createStreamConfig({
                modes: ['invalid_mode', 'updates'],
                sessionId: 'test_session'
            });

            expect(config.streamMode).toEqual(['updates']);
        });

        it('should use default modes when invalid modes provided', () => {
            const config = streamingManager.createStreamConfig({
                modes: ['invalid_mode'],
                sessionId: 'test_session'
            });

            expect(config.streamMode).toEqual(['updates', 'messages']);
        });
    });

    describe('Stream Processing', () => {
        it('should process state update chunks', async () => {
            const mockStream = (async function* () {
                yield {
                    type: 'updates',
                    data: {
                        messages: [{ role: 'assistant', content: 'Hello' }],
                        agentMode: 'speaking'
                    }
                };
            })();

            mockAgent.stream.mockReturnValue(mockStream);

            const callbacks = {
                onStateUpdate: vi.fn(),
                onMessageChunk: vi.fn(),
                onComplete: vi.fn()
            };

            const processedStream = await streamingManager.startStream(
                mockAgent,
                { userInput: 'test' },
                { sessionId: 'test_session', ...callbacks }
            );

            const chunks = [];
            for await (const chunk of processedStream) {
                chunks.push(chunk);
            }

            expect(chunks).toHaveLength(1);
            expect(chunks[0].type).toBe('state_update');
            expect(chunks[0].updates).toBeDefined();
            expect(callbacks.onStateUpdate).toHaveBeenCalled();
            expect(callbacks.onComplete).toHaveBeenCalled();
        });

        it('should process message chunks', async () => {
            const mockStream = (async function* () {
                yield {
                    type: 'messages',
                    content: 'Hello ',
                    messageType: 'token'
                };
                yield {
                    type: 'messages',
                    content: 'world!',
                    messageType: 'token'
                };
            })();

            mockAgent.stream.mockReturnValue(mockStream);

            const callbacks = {
                onMessageChunk: vi.fn(),
                onComplete: vi.fn()
            };

            const processedStream = await streamingManager.startStream(
                mockAgent,
                { userInput: 'test' },
                { sessionId: 'test_session', ...callbacks }
            );

            const chunks = [];
            for await (const chunk of processedStream) {
                chunks.push(chunk);
            }

            expect(chunks).toHaveLength(2);
            expect(chunks[0].type).toBe('message_chunk');
            expect(chunks[0].content).toBe('Hello ');
            expect(chunks[1].type).toBe('message_chunk');
            expect(chunks[1].content).toBe('world!');
            expect(callbacks.onMessageChunk).toHaveBeenCalledTimes(2);
        });

        it('should process tool execution chunks', async () => {
            const mockStream = (async function* () {
                yield {
                    type: 'tool_calls',
                    tool_calls: [
                        {
                            id: 'call_1',
                            name: 'test_tool',
                            args: { input: 'test' }
                        }
                    ]
                };
            })();

            mockAgent.stream.mockReturnValue(mockStream);

            const callbacks = {
                onToolCall: vi.fn(),
                onComplete: vi.fn()
            };

            const processedStream = await streamingManager.startStream(
                mockAgent,
                { userInput: 'test' },
                { sessionId: 'test_session', ...callbacks }
            );

            const chunks = [];
            for await (const chunk of processedStream) {
                chunks.push(chunk);
            }

            expect(chunks).toHaveLength(1);
            expect(chunks[0].type).toBe('tool_execution');
            expect(chunks[0].toolCalls).toHaveLength(1);
            expect(callbacks.onToolCall).toHaveBeenCalled();
        });

        it('should handle string chunks', async () => {
            const mockStream = (async function* () {
                yield 'Hello ';
                yield 'world!';
            })();

            mockAgent.stream.mockReturnValue(mockStream);

            const callbacks = {
                onMessageChunk: vi.fn(),
                onComplete: vi.fn()
            };

            const processedStream = await streamingManager.startStream(
                mockAgent,
                { userInput: 'test' },
                { sessionId: 'test_session', ...callbacks }
            );

            const chunks = [];
            for await (const chunk of processedStream) {
                chunks.push(chunk);
            }

            expect(chunks).toHaveLength(2);
            expect(chunks[0].type).toBe('text_content');
            expect(chunks[0].content).toBe('Hello ');
            expect(chunks[1].type).toBe('text_content');
            expect(chunks[1].content).toBe('world!');
            expect(callbacks.onMessageChunk).toHaveBeenCalledTimes(2);
        });
    });

    describe('Session Management', () => {
        it('should track streaming sessions', async () => {
            const mockStream = (async function* () {
                yield { type: 'messages', content: 'test' };
            })();

            mockAgent.stream.mockReturnValue(mockStream);

            const sessionId = 'tracked_session';
            const processedStream = await streamingManager.startStream(
                mockAgent,
                { userInput: 'test' },
                { sessionId }
            );

            // Consume stream
            for await (const chunk of processedStream) {
                // Process chunks
            }

            const session = streamingManager.getSessionStatus(sessionId);
            expect(session).toBeDefined();
            expect(session.status).toBe('completed');
            expect(session.startTime).toBeDefined();
            expect(session.endTime).toBeDefined();
            expect(session.totalChunks).toBe(1);
        });

        it('should handle session errors', async () => {
            const mockStream = (async function* () {
                throw new Error('Stream error');
            })();

            mockAgent.stream.mockReturnValue(mockStream);

            const callbacks = {
                onError: vi.fn()
            };

            const sessionId = 'error_session';

            try {
                const processedStream = await streamingManager.startStream(
                    mockAgent,
                    { userInput: 'test' },
                    { sessionId, ...callbacks }
                );

                for await (const chunk of processedStream) {
                    // Process chunks
                }
            } catch (error) {
                expect(error.message).toBe('Stream error');
            }

            const session = streamingManager.getSessionStatus(sessionId);
            expect(session.status).toBe('failed');
            expect(callbacks.onError).toHaveBeenCalled();
        });

        it('should stop streaming sessions', async () => {
            const sessionId = 'stop_test_session';
            streamingManager.activeSessions.set(sessionId, {
                startTime: Date.now(),
                status: 'active'
            });

            const result = streamingManager.stopStream(sessionId);
            expect(result).toBe(true);

            const session = streamingManager.getSessionStatus(sessionId);
            expect(session.status).toBe('stopped');
        });

        it('should clean up old sessions', () => {
            const now = Date.now();
            const oldTime = now - 400000; // 6+ minutes ago

            streamingManager.activeSessions.set('old_session', {
                startTime: oldTime,
                status: 'completed'
            });

            streamingManager.activeSessions.set('recent_session', {
                startTime: now - 100000, // Recent
                status: 'completed'
            });

            streamingManager.cleanupSessions(300000); // 5 minute threshold

            expect(streamingManager.getSessionStatus('old_session')).toBeNull();
            expect(streamingManager.getSessionStatus('recent_session')).toBeDefined();
        });
    });

    describe('Token Streaming', () => {
        it('should create token-level stream', async () => {
            const mockStream = (async function* () {
                yield { type: 'messages', content: 'Hello world' };
                yield { type: 'messages', content: ' from tokens' };
            })();

            const onToken = vi.fn();
            const onComplete = vi.fn();

            const tokenStream = streamingManager.createTokenStream(mockStream, {
                sessionId: 'token_test',
                onToken,
                onComplete
            });

            const tokens = [];
            for await (const token of tokenStream) {
                tokens.push(token);
            }

            expect(tokens.length).toBeGreaterThan(0);
            expect(onToken).toHaveBeenCalled();
            expect(onComplete).toHaveBeenCalled();
        });
    });

    describe('Error Handling', () => {
        it('should handle chunk processing errors gracefully', async () => {
            const mockStream = (async function* () {
                yield { malformed: 'chunk' };  // Invalid chunk format
            })();

            mockAgent.stream.mockReturnValue(mockStream);

            const processedStream = await streamingManager.startStream(
                mockAgent,
                { userInput: 'test' },
                { sessionId: 'error_test' }
            );

            const chunks = [];
            for await (const chunk of processedStream) {
                chunks.push(chunk);
            }

            expect(chunks).toHaveLength(1);
            expect(chunks[0].type).toBe('generic_object');
        });

        it('should handle agent stream creation errors', async () => {
            mockAgent.stream.mockRejectedValue(new Error('Agent error'));

            await expect(
                streamingManager.startStream(
                    mockAgent,
                    { userInput: 'test' },
                    { sessionId: 'agent_error_test' }
                )
            ).rejects.toThrow('Agent error');
        });
    });

    describe('Enhanced Features - Performance Coordination', () => {
        it('should initialize with enhanced configuration options', () => {
            expect(streamingManager.options.preferredMode).toBe('messages');
            expect(streamingManager.options.enableMultiMode).toBe(true);
            expect(streamingManager.options.chunkBuffer).toBe(50);
            expect(streamingManager.options.targetLatency).toBe(600);
            expect(streamingManager.options.adaptiveThrottling).toBe(true);
        });

        it('should support performance coordination initialization', async () => {
            // Mock the performance components import
            vi.doMock('./PerformanceOptimizer.js', () => ({
                PerformanceOptimizer: vi.fn().mockImplementation(() => ({
                    optimizeStreamingConfig: vi.fn(),
                    getMetrics: vi.fn(() => ({ avgResponseTime: 300 }))
                }))
            }));

            // Test async initialization (this would normally happen during first stream)
            expect(streamingManager.performanceInitialized).toBe(false);
            
            // Initialize performance coordination
            await streamingManager.initializePerformanceCoordination();
            
            expect(streamingManager.performanceInitialized).toBe(true);
        });

        it('should provide performance metrics interface', () => {
            const metrics = streamingManager.getPerformanceMetrics();
            
            // Should return error when not initialized
            expect(metrics.error).toContain('Performance coordination not initialized');
            expect(metrics.unified.performanceGrade).toBe('Unknown');
        });
    });

    describe('Enhanced Features - Native Stream Processing', () => {
        it('should support startNativeStream with multimodal options', async () => {
            const mockStream = (async function* () {
                yield {
                    messages: [{
                        content: 'Hello',
                        tool_calls: []
                    }]
                };
            })();

            mockAgent.stream.mockReturnValue(mockStream);

            const options = {
                sessionId: 'native_test',
                enableMultimodal: true,
                audioFormat: 'float32',
                videoFormat: 'base64',
                modes: ['messages', 'updates']
            };

            const processedStream = await streamingManager.startNativeStream(
                mockAgent,
                { messages: [{ role: 'user', content: 'test' }] },
                options
            );

            const chunks = [];
            for await (const chunk of processedStream) {
                chunks.push(chunk);
            }

            expect(chunks).toHaveLength(1);
            expect(chunks[0].type).toBe('message');
            expect(chunks[0].content).toBe('Hello');
            
            // Verify session was stored with enhanced metadata
            const session = streamingManager.getSessionStatus('native_test');
            expect(session?.type).toBe('native_stream');
        });

        it('should process LangGraph chunks with enhanced patterns', () => {
            const chunk = {
                messages: [{
                    content: 'Test message',
                    tool_calls: [{ id: 'tool_1', function: { name: 'test_tool' } }],
                    constructor: { name: 'AIMessageChunk' }
                }]
            };

            const processedChunk = streamingManager.processLangGraphChunk(chunk, 'test_session', 1);

            expect(processedChunk.type).toBe('message');
            expect(processedChunk.content).toBe('Test message');
            expect(processedChunk.toolCalls).toHaveLength(1);
            expect(processedChunk.metadata.hasToolCalls).toBe(true);
            expect(processedChunk.sessionId).toBe('test_session');
            expect(processedChunk.chunkIndex).toBe(1);
        });
    });

    describe('Enhanced Features - Token Stream Optimization', () => {
        it('should create optimized token stream with natural delays', async () => {
            const mockMessageStream = (async function* () {
                yield { type: 'message', content: 'Hello world test' };
            })();

            const tokens = [];
            const onToken = vi.fn((tokenData) => {
                tokens.push(tokenData);
            });

            const tokenStream = streamingManager.createTokenStream(mockMessageStream, {
                sessionId: 'token_optimization_test',
                tokenDelay: 5, // Short delay for testing
                onToken
            });

            const startTime = Date.now();
            for await (const token of tokenStream) {
                // Verify token structure
                expect(token.token).toBeDefined();
                expect(token.tokenIndex).toBeGreaterThan(0);
                expect(token.fullText).toBeDefined();
                expect(token.sessionId).toBe('token_optimization_test');
            }
            const endTime = Date.now();

            // Verify tokens were created
            expect(tokens.length).toBeGreaterThan(0);
            expect(onToken).toHaveBeenCalled();
            
            // Verify timing includes delays (should be longer than immediate)
            expect(endTime - startTime).toBeGreaterThan(0);
        });

        it('should support legacy token stream for compatibility', async () => {
            const mockStream = (async function* () {
                yield { type: 'messages', content: 'Legacy test' };
            })();

            const tokens = [];
            const onToken = vi.fn();
            const onComplete = vi.fn();

            const tokenStream = streamingManager.createLegacyTokenStream(mockStream, {
                sessionId: 'legacy_test',
                onToken,
                onComplete
            });

            for await (const token of tokenStream) {
                tokens.push(token);
            }

            expect(tokens.length).toBeGreaterThan(0);
            expect(onToken).toHaveBeenCalled();
            expect(onComplete).toHaveBeenCalled();
        });
    });

    describe('Enhanced Features - Sub-600ms Performance Validation', () => {
        it('should target sub-600ms response times', () => {
            // Verify target latency configuration
            expect(streamingManager.options.targetLatency).toBe(600);
        });

        it('should complete stream processing within reasonable time', async () => {
            const mockStream = (async function* () {
                yield { type: 'message', content: 'Fast response' };
            })();

            mockAgent.stream.mockReturnValue(mockStream);

            const startTime = Date.now();
            
            const processedStream = await streamingManager.startStream(
                mockAgent,
                { userInput: 'test performance' },
                { sessionId: 'performance_test' }
            );

            const chunks = [];
            for await (const chunk of processedStream) {
                chunks.push(chunk);
            }

            const endTime = Date.now();
            const processingTime = endTime - startTime;

            // For testing, allow reasonable processing time (real-world would be sub-600ms)
            expect(processingTime).toBeLessThan(1000); // 1 second for test environment
            expect(chunks).toHaveLength(1);
        });
    });

    describe('Cleanup and Disposal', () => {
        it('should dispose properly with enhanced cleanup', () => {
            streamingManager.activeSessions.set('test', { status: 'active' });
            streamingManager.audioStreamingState.set('audio_test', { active: true });

            streamingManager.dispose();

            expect(streamingManager.getActiveSessions()).toEqual({});
            expect(streamingManager.performanceInitialized).toBe(false);
            expect(streamingManager.performanceOptimizer).toBeNull();
            expect(streamingManager.performanceTracker).toBeNull();
            expect(streamingManager.performanceCoordinator).toBeNull();
        });
    });
});