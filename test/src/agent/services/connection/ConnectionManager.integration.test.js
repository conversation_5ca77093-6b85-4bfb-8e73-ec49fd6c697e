/**
 * ConnectionManager Integration Tests
 * 
 * Real-world integration tests demonstrating how ConnectionManager
 * eliminates race conditions and redundancy across multiple systems:
 * - AliyunWebSocketChatModel integration
 * - DualBrainCoordinator integration  
 * - Multiple concurrent clients
 * - System failure scenarios
 * - Performance under load
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ConnectionManager, ConnectionState } from '../../../src/agent/services/connection/ConnectionManager.js';

// Mock the existing problematic systems
class MockAliyunWebSocketChatModel {
  constructor() {
    this.connectionState = 'disconnected';
    this.isRealtimeModeActive = vi.fn(() => this.connectionState === 'connected');
    this.waitForRealtimeReady = vi.fn(() => Promise.resolve(true));
    this.connectionManager = null;
  }

  async connectWithConnectionManager() {
    this.connectionManager = await ConnectionManager.getInstance();
    
    const config = {
      url: 'ws://aliyun-api.com/websocket',
      initializeSession: async () => {
        // Simulate <PERSON>yun session setup
        await new Promise(resolve => setTimeout(resolve, 50));
        this.connectionManager.emit('sessionReady', {
          sessionId: 'aliyun-session-123'
        });
      }
    };

    const connected = await this.connectionManager.connect(config);
    if (connected) {
      this.connectionState = 'connected';
    }
    return connected;
  }

  async disconnect() {
    if (this.connectionManager) {
      await this.connectionManager.disconnect();
      this.connectionState = 'disconnected';
    }
  }

  send(message) {
    return this.connectionManager?.send(message) || false;
  }
}

class MockDualBrainCoordinator {
  constructor() {
    this.isActive = false;
    this.connectionManager = null;
    this.system1Ready = false;
  }

  async initialize() {
    this.connectionManager = await ConnectionManager.getInstance();
    
    // Listen for connection events
    this.connectionManager.on('connected', () => {
      this.system1Ready = true;
    });

    this.connectionManager.on('disconnected', () => {
      this.system1Ready = false;
    });

    this.isActive = true;
  }

  async ensureSystem1Ready() {
    if (!this.connectionManager) {
      throw new Error('ConnectionManager not initialized');
    }

    const ready = await this.connectionManager.waitForReady(5000);
    if (!ready) {
      throw new Error('System 1 not ready - realtime mode not active');
    }

    return this.connectionManager.isReady();
  }

  async invokeSystem1(input) {
    const ready = await this.ensureSystem1Ready();
    if (!ready) {
      throw new Error('System 1 not ready for invocation');
    }

    return this.connectionManager.send({
      type: 'invoke',
      input,
      timestamp: Date.now()
    });
  }
}

// Mock multiple client systems
class MockWebSocketClient {
  constructor(clientId) {
    this.clientId = clientId;
    this.connectionManager = null;
    this.messagesReceived = [];
    this.isConnected = false;
  }

  async connect() {
    this.connectionManager = await ConnectionManager.getInstance();
    
    this.connectionManager.on('message', (message) => {
      this.messagesReceived.push({
        clientId: this.clientId,
        message,
        timestamp: Date.now()
      });
    });

    this.connectionManager.on('connected', () => {
      this.isConnected = true;
    });

    this.connectionManager.on('disconnected', () => {
      this.isConnected = false;
    });

    const config = {
      url: `ws://test.com/${this.clientId}`,
      initializeSession: async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        this.connectionManager.emit('sessionReady', {
          sessionId: `session-${this.clientId}`
        });
      }
    };

    return this.connectionManager.connect(config);
  }

  send(message) {
    return this.connectionManager?.send({
      ...message,
      clientId: this.clientId
    }) || false;
  }

  disconnect() {
    return this.connectionManager?.disconnect();
  }
}

describe('ConnectionManager Integration Tests', () => {
  
  beforeEach(() => {
    ConnectionManager._resetInstance();
    vi.clearAllMocks();
  });

  afterEach(async () => {
    ConnectionManager._resetInstance();
  });

  describe('Single Connection Enforcement', () => {
    it('should enforce single connection across multiple systems', async () => {
      const aliyunModel = new MockAliyunWebSocketChatModel();
      const dualBrain = new MockDualBrainCoordinator();
      const client1 = new MockWebSocketClient('client-1');
      const client2 = new MockWebSocketClient('client-2');

      // All systems try to connect simultaneously
      const connectPromises = [
        aliyunModel.connectWithConnectionManager(),
        client1.connect(),
        client2.connect()
      ];

      const results = await Promise.all(connectPromises);

      // All should succeed but use the same connection
      expect(results.every(r => r === true)).toBe(true);

      // Initialize dual brain after connection established
      await dualBrain.initialize();

      // All systems should share the same ConnectionManager instance
      expect(aliyunModel.connectionManager).toBe(client1.connectionManager);
      expect(client1.connectionManager).toBe(client2.connectionManager);
      expect(client2.connectionManager).toBe(dualBrain.connectionManager);

      // All should report ready status
      expect(aliyunModel.isRealtimeModeActive()).toBe(true);
      expect(dualBrain.system1Ready).toBe(true);
      expect(client1.isConnected).toBe(true);
      expect(client2.isConnected).toBe(true);
    });

    it('should coordinate state across all systems', async () => {
      const aliyunModel = new MockAliyunWebSocketChatModel();
      const dualBrain = new MockDualBrainCoordinator();

      // Connect Aliyun model
      await aliyunModel.connectWithConnectionManager();
      
      // Initialize dual brain
      await dualBrain.initialize();

      // Both should see connected state
      expect(aliyunModel.isRealtimeModeActive()).toBe(true);
      expect(dualBrain.system1Ready).toBe(true);

      // Disconnect from one system
      await aliyunModel.disconnect();

      // Both should see disconnected state
      expect(aliyunModel.isRealtimeModeActive()).toBe(false);
      expect(dualBrain.system1Ready).toBe(false);
    });
  });

  describe('Race Condition Elimination', () => {
    it('should eliminate connection race conditions', async () => {
      const systems = [
        new MockAliyunWebSocketChatModel(),
        new MockWebSocketClient('client-1'),
        new MockWebSocketClient('client-2'),
        new MockWebSocketClient('client-3')
      ];

      // All systems try to connect at the exact same time
      const connectPromises = systems.map(system => 
        system.connectWithConnectionManager ? 
          system.connectWithConnectionManager() : 
          system.connect()
      );

      const results = await Promise.all(connectPromises);

      // All connections should succeed
      expect(results.every(r => r === true)).toBe(true);

      // All should share the same connection
      const connectionManager = await ConnectionManager.getInstance();
      expect(connectionManager.isReady()).toBe(true);
      expect(connectionManager.getConnectionState()).toBe(ConnectionState.STABILIZED);
    });

    it('should handle rapid connect/disconnect from multiple systems', async () => {
      const aliyunModel = new MockAliyunWebSocketChatModel();
      const dualBrain = new MockDualBrainCoordinator();
      
      // Rapid connection cycles
      for (let i = 0; i < 5; i++) {
        // Connect
        await aliyunModel.connectWithConnectionManager();
        await dualBrain.initialize();
        
        expect(aliyunModel.isRealtimeModeActive()).toBe(true);
        expect(dualBrain.system1Ready).toBe(true);
        
        // Disconnect
        await aliyunModel.disconnect();
        
        expect(aliyunModel.isRealtimeModeActive()).toBe(false);
        expect(dualBrain.system1Ready).toBe(false);
        
        // Reset for next cycle
        dualBrain.isActive = false;
        ConnectionManager._resetInstance();
      }
    });

    it('should prevent dual brain readiness race conditions', async () => {
      const dualBrain = new MockDualBrainCoordinator();
      
      // Initialize dual brain
      await dualBrain.initialize();

      // Simulate concurrent readiness checks (the original race condition)
      const readinessChecks = Array(10).fill().map(() => 
        dualBrain.ensureSystem1Ready()
      );

      // All should fail consistently (not ready yet)
      const results = await Promise.allSettled(readinessChecks);
      expect(results.every(r => r.status === 'rejected')).toBe(true);

      // Now establish connection
      const connectionManager = await ConnectionManager.getInstance();
      await connectionManager.connect({
        url: 'ws://test.com',
        initializeSession: async () => {
          setTimeout(() => {
            connectionManager.emit('sessionReady', { sessionId: 'test' });
          }, 20);
        }
      });

      // Now all readiness checks should succeed
      const readyChecks = Array(10).fill().map(() => 
        dualBrain.ensureSystem1Ready()
      );

      const readyResults = await Promise.all(readyChecks);
      expect(readyResults.every(r => r === true)).toBe(true);
    });

    it('should handle concurrent invocations safely', async () => {
      const dualBrain = new MockDualBrainCoordinator();
      
      // Establish connection first
      await dualBrain.initialize();
      const connectionManager = await ConnectionManager.getInstance();
      await connectionManager.connect({
        url: 'ws://test.com',
        initializeSession: async () => {
          setTimeout(() => {
            connectionManager.emit('sessionReady', { sessionId: 'test' });
          }, 20);
        }
      });

      // Concurrent invocations
      const invocations = Array(20).fill().map((_, i) => 
        dualBrain.invokeSystem1(`message-${i}`)
      );

      const results = await Promise.all(invocations);
      
      // All invocations should succeed
      expect(results.every(r => r === true)).toBe(true);
      
      // Should have sent all messages
      const metrics = connectionManager.getMetrics();
      expect(metrics.messagesSent).toBe(20);
    });
  });

  describe('System Failure Recovery', () => {
    it('should recover from connection failures affecting all systems', async () => {
      const aliyunModel = new MockAliyunWebSocketChatModel();
      const dualBrain = new MockDualBrainCoordinator();
      const client = new MockWebSocketClient('recovery-test');

      // Establish connections
      await aliyunModel.connectWithConnectionManager();
      await dualBrain.initialize();
      await client.connect();

      // All systems should be ready
      expect(aliyunModel.isRealtimeModeActive()).toBe(true);
      expect(dualBrain.system1Ready).toBe(true);
      expect(client.isConnected).toBe(true);

      const connectionManager = await ConnectionManager.getInstance();
      
      // Simulate connection failure (1011 - service restart)
      connectionManager._socket.close(1011, 'Service restart');

      // Wait for recovery
      await new Promise(resolve => setTimeout(resolve, 200));

      // All systems should automatically recover
      expect(aliyunModel.isRealtimeModeActive()).toBe(true);
      expect(dualBrain.system1Ready).toBe(true);
      expect(client.isConnected).toBe(true);
    });

    it('should handle partial system failures gracefully', async () => {
      const aliyunModel = new MockAliyunWebSocketChatModel();
      const dualBrain = new MockDualBrainCoordinator();

      // Connect systems
      await aliyunModel.connectWithConnectionManager();
      await dualBrain.initialize();

      // Simulate failure in one system's readiness check
      aliyunModel.isRealtimeModeActive = vi.fn(() => {
        throw new Error('Simulated readiness failure');
      });

      // DualBrain should still work via ConnectionManager
      const connectionManager = await ConnectionManager.getInstance();
      expect(connectionManager.isReady()).toBe(true);

      // Connection should still be active despite individual system issues
      expect(connectionManager.getConnectionState()).toBe(ConnectionState.STABILIZED);
    });
  });

  describe('Performance Under Load', () => {
    it('should handle high-frequency operations from multiple systems', async () => {
      const numSystems = 10;
      const messagesPerSystem = 100;
      
      // Create multiple client systems
      const clients = Array(numSystems).fill().map((_, i) => 
        new MockWebSocketClient(`load-test-${i}`)
      );

      // Connect all clients
      await Promise.all(clients.map(client => client.connect()));

      const connectionManager = await ConnectionManager.getInstance();
      const startTime = Date.now();

      // Send many messages concurrently from all systems
      const sendPromises = [];
      clients.forEach(client => {
        for (let i = 0; i < messagesPerSystem; i++) {
          sendPromises.push(
            Promise.resolve(client.send({
              type: 'load-test',
              messageId: i,
              timestamp: Date.now()
            }))
          );
        }
      });

      const results = await Promise.all(sendPromises);
      const endTime = Date.now();

      // All messages should be sent successfully
      const successfulSends = results.filter(r => r === true).length;
      expect(successfulSends).toBe(numSystems * messagesPerSystem);

      // Performance metrics
      const totalMessages = numSystems * messagesPerSystem;
      const duration = endTime - startTime;
      const throughput = totalMessages / (duration / 1000);

      console.log(`Performance: ${totalMessages} messages in ${duration}ms (${throughput.toFixed(2)} msg/sec)`);
      
      // Should maintain reasonable throughput
      expect(throughput).toBeGreaterThan(100); // At least 100 messages/sec
      
      // Connection should remain stable
      expect(connectionManager.isReady()).toBe(true);
      
      // Metrics should be accurate
      const metrics = connectionManager.getMetrics();
      expect(metrics.messagesSent).toBe(totalMessages);
    });

    it('should maintain memory efficiency under load', async () => {
      const connectionManager = await ConnectionManager.getInstance();
      
      // Connect
      await connectionManager.connect({
        url: 'ws://memory-test.com',
        initializeSession: async () => {
          setTimeout(() => {
            connectionManager.emit('sessionReady', { sessionId: 'memory-test' });
          }, 20);
        }
      });

      // Get initial memory usage
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Create many event listeners (simulate multiple systems)
      const listeners = [];
      for (let i = 0; i < 1000; i++) {
        const listener = () => {};
        connectionManager.on('message', listener);
        listeners.push(listener);
      }

      // Send many messages
      for (let i = 0; i < 1000; i++) {
        connectionManager.send({ type: 'memory-test', id: i });
      }

      // Remove listeners
      listeners.forEach(listener => {
        connectionManager.off('message', listener);
      });

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      // Check memory usage
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);

      // Connection should still be healthy
      expect(connectionManager.isReady()).toBe(true);
    });
  });

  describe('Real-World Integration Scenarios', () => {
    it('should integrate with existing AliyunWebSocketChatModel pattern', async () => {
      const aliyunModel = new MockAliyunWebSocketChatModel();
      
      // Original pattern - each model manages its own connection
      // NEW pattern - delegate to ConnectionManager
      await aliyunModel.connectWithConnectionManager();
      
      expect(aliyunModel.isRealtimeModeActive()).toBe(true);
      expect(aliyunModel.send({ type: 'test' })).toBe(true);
      
      // Should work seamlessly with existing interfaces
      expect(typeof aliyunModel.isRealtimeModeActive).toBe('function');
      expect(typeof aliyunModel.send).toBe('function');
      expect(typeof aliyunModel.disconnect).toBe('function');
    });

    it('should integrate with DualBrainCoordinator readiness checks', async () => {
      const dualBrain = new MockDualBrainCoordinator();
      
      await dualBrain.initialize();
      
      // Original race condition scenario
      try {
        await dualBrain.invokeSystem1('test before connection');
        expect(false).toBe(true); // Should not reach here
      } catch (error) {
        expect(error.message).toContain('not ready');
      }

      // Establish connection
      const connectionManager = await ConnectionManager.getInstance();
      await connectionManager.connect({
        url: 'ws://test.com',
        initializeSession: async () => {
          setTimeout(() => {
            connectionManager.emit('sessionReady', { sessionId: 'test' });
          }, 20);
        }
      });

      // Now should work
      const success = await dualBrain.invokeSystem1('test after connection');
      expect(success).toBe(true);
    });

    it('should handle WebSocket proxy scenarios', async () => {
      const connectionManager = await ConnectionManager.getInstance({
        connectionTimeout: 5000,
        stabilizationTimeout: 3000
      });

      // Simulate proxy WebSocket connection with session negotiation
      const connected = await connectionManager.connect({
        url: 'ws://proxy.aliyun.com/websocket',
        options: {
          headers: {
            'Authorization': 'Bearer test-token',
            'X-Client-Type': 'unified-connection-manager'
          }
        },
        initializeSession: async () => {
          // Simulate complex proxy session setup
          await new Promise(resolve => setTimeout(resolve, 100));
          
          // Multiple handshake messages
          connectionManager.emit('message', {
            type: 'handshake_start',
            data: { proxyVersion: '1.0' }
          });
          
          await new Promise(resolve => setTimeout(resolve, 50));
          
          connectionManager.emit('sessionReady', {
            sessionId: 'proxy-session-abc123',
            proxyId: 'proxy-node-1',
            capabilities: ['audio', 'text', 'multimodal']
          });
        }
      });

      expect(connected).toBe(true);
      expect(connectionManager.isReady()).toBe(true);
      
      const health = connectionManager.getHealthStatus();
      expect(health.sessionId).toBe('proxy-session-abc123');
      expect(health.isHealthy).toBe(true);
    });

    it('should handle complete system lifecycle', async () => {
      // Phase 1: System startup - multiple components initialize
      const aliyunModel = new MockAliyunWebSocketChatModel();
      const dualBrain = new MockDualBrainCoordinator();
      const clients = [
        new MockWebSocketClient('app-client'),
        new MockWebSocketClient('monitoring-client')
      ];

      // Parallel initialization (typical startup scenario)
      await Promise.all([
        aliyunModel.connectWithConnectionManager(),
        dualBrain.initialize(),
        ...clients.map(client => client.connect())
      ]);

      // Phase 2: Normal operation
      expect(aliyunModel.isRealtimeModeActive()).toBe(true);
      expect(dualBrain.system1Ready).toBe(true);
      clients.forEach(client => expect(client.isConnected).toBe(true));

      // Phase 3: System under load
      const operations = [];
      for (let i = 0; i < 50; i++) {
        operations.push(dualBrain.invokeSystem1(`operation-${i}`));
        operations.push(clients[0].send({ type: 'app-message', id: i }));
        operations.push(clients[1].send({ type: 'monitoring', id: i }));
      }

      const results = await Promise.all(operations);
      expect(results.every(r => r === true)).toBe(true);

      // Phase 4: Failure and recovery
      const connectionManager = await ConnectionManager.getInstance();
      connectionManager._socket.close(1006, 'Connection lost');

      // Wait for recovery
      await new Promise(resolve => setTimeout(resolve, 300));

      // Phase 5: Post-recovery validation
      expect(connectionManager.isReady()).toBe(true);
      expect(aliyunModel.isRealtimeModeActive()).toBe(true);
      expect(dualBrain.system1Ready).toBe(true);

      // Phase 6: Clean shutdown
      await connectionManager.disconnect();
      
      expect(aliyunModel.isRealtimeModeActive()).toBe(false);
      expect(dualBrain.system1Ready).toBe(false);
      clients.forEach(client => expect(client.isConnected).toBe(false));
    });
  });
});