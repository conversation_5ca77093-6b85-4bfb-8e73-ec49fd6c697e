/**
 * ConnectionManager Test Suite
 * 
 * Tests the simplified WebSocket connection management functionality
 * Ensures proper state transitions, error handling, and session management
 */

import { expect } from 'chai';
import sinon from 'sinon';
import { ConnectionManager, ConnectionState, ConnectionErrorType } from '../../../../../src/agent/services/connection/ConnectionManager.js';

describe('ConnectionManager', () => {
  let connectionManager;
  let mockWebSocket;
  let sandbox;

  beforeEach(async () => {
    sandbox = sinon.createSandbox();

    // Mock WebSocket
    mockWebSocket = {
      readyState: 1, // OPEN
      addEventListener: sandbox.stub(),
      removeEventListener: sandbox.stub(),
      send: sandbox.stub(),
      close: sandbox.stub()
    };

    // Mock WebSocket constructor
    global.WebSocket = sandbox.stub().returns(mockWebSocket);
    global.WebSocket.OPEN = 1;
    global.WebSocket.CONNECTING = 0;
    global.WebSocket.CLOSING = 2;
    global.WebSocket.CLOSED = 3;

    // Reset singleton
    ConnectionManager._instance = null;
  });

  afterEach(() => {
    sandbox.restore();
    if (connectionManager) {
      connectionManager.dispose();
    }
  });

  describe('Initialization', () => {
    it('should create singleton instance', async () => {
      const instance1 = await ConnectionManager.getInstance();
      const instance2 = await ConnectionManager.getInstance();

      expect(instance1).to.equal(instance2);
      expect(instance1).to.be.instanceOf(ConnectionManager);
    });

    it('should initialize with default configuration', async () => {
      connectionManager = await ConnectionManager.getInstance();

      expect(connectionManager.config.connectionTimeout).to.equal(5000);
      expect(connectionManager.config.maxRetries).to.equal(3);
      expect(connectionManager.config.retryDelay).to.equal(1000);
      expect(connectionManager.config.sessionInitTimeout).to.equal(3000);
    });

    it('should accept custom configuration', async () => {
      connectionManager = await ConnectionManager.getInstance({
        connectionTimeout: 10000,
        maxRetries: 5,
        retryDelay: 2000
      });

      expect(connectionManager.config.connectionTimeout).to.equal(10000);
      expect(connectionManager.config.maxRetries).to.equal(5);
      expect(connectionManager.config.retryDelay).to.equal(2000);
    });
  });

  describe('Connection State Management', () => {
    beforeEach(async () => {
      connectionManager = await ConnectionManager.getInstance();
    });

    it('should start in DISCONNECTED state', () => {
      expect(connectionManager.getConnectionState().state).to.equal(ConnectionState.DISCONNECTED);
    });

    it('should validate state transitions', () => {
      expect(connectionManager._canTransitionTo(ConnectionState.CONNECTING)).to.be.true;
      expect(connectionManager._canTransitionTo(ConnectionState.CONNECTED)).to.be.false;
    });

    it('should emit state change events', (done) => {
      connectionManager.once('stateChange', (event) => {
        expect(event.oldState).to.equal(ConnectionState.DISCONNECTED);
        expect(event.newState).to.equal(ConnectionState.CONNECTING);
        done();
      });

      connectionManager._setState(ConnectionState.CONNECTING);
    });
  });

  describe('WebSocket Connection', () => {
    beforeEach(async () => {
      connectionManager = await ConnectionManager.getInstance();
    });

    it('should successfully establish connection', async () => {
      // Setup WebSocket to trigger open event
      setTimeout(() => {
        const openHandler = mockWebSocket.addEventListener.getCall(0).args[1];
        openHandler();
      }, 10);

      const wsConfig = {
        url: 'wss://example.com/websocket',
        options: {}
      };

      const result = await connectionManager.connect(wsConfig);

      expect(result).to.be.true;
      expect(connectionManager.getConnectionState().state).to.equal(ConnectionState.CONNECTED);
      expect(global.WebSocket).to.have.been.calledWith(wsConfig.url);
    });

    it('should handle connection timeout', async () => {
      // Don't trigger open event to simulate timeout
      const wsConfig = {
        url: 'wss://example.com/websocket',
        options: {}
      };

      try {
        await connectionManager.connect(wsConfig);
        expect.fail('Should have thrown timeout error');
      } catch (error) {
        expect(error.message).to.include('timeout');
        expect(connectionManager.getConnectionState().state).to.equal(ConnectionState.ERROR);
      }
    });

    it('should handle WebSocket errors during connection', async () => {
      // Setup WebSocket to trigger error event
      setTimeout(() => {
        const errorHandler = mockWebSocket.addEventListener.getCall(1).args[1];
        errorHandler(new Error('Connection failed'));
      }, 10);

      const wsConfig = {
        url: 'wss://example.com/websocket',
        options: {}
      };

      try {
        await connectionManager.connect(wsConfig);
        expect.fail('Should have thrown connection error');
      } catch (error) {
        expect(error.message).to.include('WebSocket connection failed');
      }
    });
  });

  describe('Session Management', () => {
    beforeEach(async () => {
      connectionManager = await ConnectionManager.getInstance();
    });

    it('should initialize session successfully', async () => {
      let sessionInitCalled = false;

      // Setup WebSocket to trigger open event
      setTimeout(() => {
        const openHandler = mockWebSocket.addEventListener.getCall(0).args[1];
        openHandler();
      }, 10);

      const wsConfig = {
        url: 'wss://example.com/websocket',
        initializeSession: async () => {
          sessionInitCalled = true;
          return Promise.resolve();
        }
      };

      const result = await connectionManager.connect(wsConfig);

      expect(result).to.be.true;
      expect(sessionInitCalled).to.be.true;
      expect(connectionManager.getConnectionState().sessionReady).to.be.true;
    });

    it('should handle session initialization timeout', async () => {
      // Setup WebSocket to trigger open event
      setTimeout(() => {
        const openHandler = mockWebSocket.addEventListener.getCall(0).args[1];
        openHandler();
      }, 10);

      const wsConfig = {
        url: 'wss://example.com/websocket',
        initializeSession: async () => {
          // Simulate long-running session init
          return new Promise(() => { }); // Never resolves
        }
      };

      const result = await connectionManager.connect(wsConfig);

      // Should still succeed even if session init times out
      expect(result).to.be.true;
      expect(connectionManager.getConnectionState().state).to.equal(ConnectionState.CONNECTED);
      expect(connectionManager.getConnectionState().sessionReady).to.be.false;
    });

    it('should handle session.created messages', () => {
      connectionManager._socket = mockWebSocket;
      connectionManager._setState(ConnectionState.CONNECTED);

      const sessionData = {
        type: 'session.created',
        session: { id: 'test-session-123' }
      };

      connectionManager._handleMessage({
        data: JSON.stringify(sessionData)
      });

      expect(connectionManager.getConnectionState().sessionId).to.equal('test-session-123');
      expect(connectionManager.getConnectionState().sessionReady).to.be.true;
    });
  });

  describe('Message Sending', () => {
    beforeEach(async () => {
      connectionManager = await ConnectionManager.getInstance();
      connectionManager._socket = mockWebSocket;
      connectionManager._setState(ConnectionState.CONNECTED);
    });

    it('should send messages when connection is ready', () => {
      const message = { type: 'test', data: 'hello' };

      connectionManager.send(message);

      expect(mockWebSocket.send).to.have.been.calledOnce;
      expect(mockWebSocket.send).to.have.been.calledWith(JSON.stringify(message));
    });

    it('should send string messages directly', () => {
      const message = 'hello world';

      connectionManager.send(message);

      expect(mockWebSocket.send).to.have.been.calledWith(message);
    });

    it('should reject sending when not ready', () => {
      connectionManager._setState(ConnectionState.DISCONNECTED);

      expect(() => {
        connectionManager.send({ test: 'data' });
      }).to.throw('Connection not ready');
    });

    it('should reject sending when WebSocket is closing', () => {
      mockWebSocket.readyState = 2; // CLOSING

      expect(() => {
        connectionManager.send({ test: 'data' });
      }).to.throw('WebSocket is not ready for sending');
    });

    it('should handle send errors gracefully', () => {
      mockWebSocket.send.throws(new Error('Send failed'));

      expect(() => {
        connectionManager.send({ test: 'data' });
      }).to.throw('Failed to send message through WebSocket');
    });
  });

  describe('Error Classification', () => {
    beforeEach(async () => {
      connectionManager = await ConnectionManager.getInstance();
    });

    it('should classify timeout errors', () => {
      const error = new Error('Connection timeout after 5000ms');
      const type = connectionManager._classifyError(error);
      expect(type).to.equal(ConnectionErrorType.TIMEOUT_ERROR);
    });

    it('should classify authentication errors', () => {
      const error = new Error('401 Unauthorized');
      const type = connectionManager._classifyError(error);
      expect(type).to.equal(ConnectionErrorType.AUTH_ERROR);
    });

    it('should classify protocol errors', () => {
      const error = new Error('WebSocket protocol error');
      const type = connectionManager._classifyError(error);
      expect(type).to.equal(ConnectionErrorType.PROTOCOL_ERROR);
    });

    it('should classify session errors', () => {
      const error = new Error('Session initialization failed');
      const type = connectionManager._classifyError(error);
      expect(type).to.equal(ConnectionErrorType.SESSION_ERROR);
    });

    it('should default to network error for unknown errors', () => {
      const error = new Error('Something went wrong');
      const type = connectionManager._classifyError(error);
      expect(type).to.equal(ConnectionErrorType.NETWORK_ERROR);
    });

    it('should handle undefined error messages', () => {
      const error = {};
      const type = connectionManager._classifyError(error);
      expect(type).to.equal(ConnectionErrorType.NETWORK_ERROR);
    });
  });

  describe('Close Code Interpretation', () => {
    beforeEach(async () => {
      connectionManager = await ConnectionManager.getInstance();
    });

    it('should interpret common close codes', () => {
      expect(connectionManager._getCloseCodeMeaning(1000)).to.equal('Normal Closure');
      expect(connectionManager._getCloseCodeMeaning(1006)).to.equal('Abnormal Closure');
      expect(connectionManager._getCloseCodeMeaning(1011)).to.equal('Internal Error');
    });

    it('should handle unknown close codes', () => {
      expect(connectionManager._getCloseCodeMeaning(9999)).to.equal('Unknown (9999)');
    });
  });

  describe('Ready State Helpers', () => {
    beforeEach(async () => {
      connectionManager = await ConnectionManager.getInstance();
    });

    it('should convert ready states to text', () => {
      expect(connectionManager._getReadyStateText(0)).to.equal('CONNECTING');
      expect(connectionManager._getReadyStateText(1)).to.equal('OPEN');
      expect(connectionManager._getReadyStateText(2)).to.equal('CLOSING');
      expect(connectionManager._getReadyStateText(3)).to.equal('CLOSED');
      expect(connectionManager._getReadyStateText(99)).to.equal('UNKNOWN');
    });
  });

  describe('Retry Logic', () => {
    beforeEach(async () => {
      connectionManager = await ConnectionManager.getInstance({
        maxRetries: 2,
        retryDelay: 100
      });
    });

    it('should retry on network errors', () => {
      const error = new Error('Network failure');
      connectionManager._retryAttempts = 1;

      const shouldRetry = connectionManager._shouldRetry(error, ConnectionErrorType.NETWORK_ERROR);
      expect(shouldRetry).to.be.true;
    });

    it('should not retry after max attempts', () => {
      const error = new Error('Network failure');
      connectionManager._retryAttempts = 3;

      const shouldRetry = connectionManager._shouldRetry(error, ConnectionErrorType.NETWORK_ERROR);
      expect(shouldRetry).to.be.false;
    });

    it('should not retry auth errors', () => {
      const error = new Error('401 Unauthorized');
      connectionManager._retryAttempts = 1;

      const shouldRetry = connectionManager._shouldRetry(error, ConnectionErrorType.AUTH_ERROR);
      expect(shouldRetry).to.be.false;
    });
  });

  describe('Cleanup and Disposal', () => {
    beforeEach(async () => {
      connectionManager = await ConnectionManager.getInstance();
    });

    it('should dispose resources properly', async () => {
      connectionManager._socket = mockWebSocket;
      connectionManager._setState(ConnectionState.CONNECTED);

      await connectionManager.dispose();

      expect(mockWebSocket.close).to.have.been.called;
      expect(connectionManager._isDisposed).to.be.true;
      expect(ConnectionManager._instance).to.be.null;
    });

    it('should disconnect properly', async () => {
      connectionManager._socket = mockWebSocket;
      connectionManager._setState(ConnectionState.CONNECTED);

      await connectionManager.disconnect(1000, 'Test disconnect');

      expect(mockWebSocket.close).to.have.been.calledWith(1000, 'Test disconnect');
      expect(connectionManager.getConnectionState().state).to.equal(ConnectionState.DISCONNECTED);
    });
  });
});