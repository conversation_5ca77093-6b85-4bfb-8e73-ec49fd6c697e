/**
 * Multimodal ContextualAnalysisService Tests
 * 
 * Tests the enhanced multimodal capabilities including audio, visual, and profile analysis
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import { ContextualAnalysisService, CONTEXTUAL_TRIGGERS } from '../../../../src/agent/services/conversation/ContextualAnalysisService.js';

describe('ContextualAnalysisService - Multimodal Capabilities', () => {
    let service;
    
    beforeEach(() => {
        service = new ContextualAnalysisService();
    });
    
    afterEach(() => {
        vi.restoreAllMocks();
    });
    
    describe('Audio Context Analysis', () => {
        test('should update audio context with volume data', () => {
            const audioData = {
                volume: 0.8,
                quality: 0.9,
                speechRate: 2.5,
                sentiment: 'positive',
                sentimentConfidence: 0.7,
                pauseDuration: 500
            };
            
            service.updateAudioContext(audioData);
            
            expect(service.context.audio.currentVolume).toBeCloseTo(0.8);
            expect(service.context.audio.qualityScore).toBeCloseTo(0.9);
            expect(service.context.audio.speechRate).toBeCloseTo(2.5);
            expect(service.context.audio.pauseDuration).toBe(500);
            expect(service.context.audio.sentimentHistory).toHaveLength(1);
            expect(service.context.audio.sentimentHistory[0].sentiment).toBe('positive');
            expect(service.context.audio.lastAudioUpdate).toBeTruthy();
        });
        
        test('should handle invalid audio data gracefully', () => {
            const invalidAudioData = {
                volume: -1, // Invalid negative volume
                quality: 2, // Invalid quality > 1
                speechRate: -1, // Invalid negative speech rate
                sentiment: null
            };
            
            service.updateAudioContext(invalidAudioData);
            
            expect(service.context.audio.currentVolume).toBe(0); // Clamped to 0
            expect(service.context.audio.qualityScore).toBe(1); // Clamped to 1
            expect(service.context.audio.speechRate).toBe(0); // Clamped to 0
        });
        
        test('should maintain rolling average for volume', () => {
            service.updateAudioContext({ volume: 0.8 });
            service.updateAudioContext({ volume: 0.6 });
            service.updateAudioContext({ volume: 0.4 });
            
            // Rolling average should be between the values due to smoothing
            expect(service.context.audio.averageVolume).toBeGreaterThan(0.4);
            expect(service.context.audio.averageVolume).toBeLessThan(0.8);
        });
        
        test('should limit sentiment history size', () => {
            const maxHistory = service.config.audio.sentimentWindow;
            
            // Add more sentiments than the limit
            for (let i = 0; i < maxHistory + 5; i++) {
                service.updateAudioContext({
                    sentiment: i % 2 === 0 ? 'positive' : 'negative',
                    sentimentConfidence: 0.8
                });
            }
            
            expect(service.context.audio.sentimentHistory).toHaveLength(maxHistory);
        });
        
        test('should calculate audio engagement score', () => {
            // Set up good audio conditions
            service.context.audio.currentVolume = 0.7;
            service.context.audio.qualityScore = 0.9;
            service.context.audio.speechRate = 2.5; // Optimal speech rate
            service.context.audio.pauseDuration = 1000; // Short pause
            service.context.audio.sentimentHistory = [
                { sentiment: 'positive', confidence: 0.8 },
                { sentiment: 'positive', confidence: 0.7 }
            ];
            
            const audioEngagement = service._calculateAudioEngagement();
            
            expect(audioEngagement).toBeGreaterThan(0.6);
            expect(audioEngagement).toBeLessThanOrEqual(1.0);
        });
    });
    
    describe('Visual Context Analysis', () => {
        test('should update visual context with face detection data', () => {
            const visualData = {
                faceDetected: true,
                emotion: 'happy',
                emotionConfidence: 0.8,
                eyeContactLevel: 0.7,
                attentionScore: 0.9,
                bodyLanguage: 'engaged',
                movementLevel: 0.2
            };
            
            service.updateVisualContext(visualData);
            
            expect(service.context.visual.faceDetected).toBe(true);
            expect(service.context.visual.currentEmotion).toBe('happy');
            expect(service.context.visual.emotionConfidence).toBeCloseTo(0.8);
            expect(service.context.visual.eyeContactLevel).toBeCloseTo(0.7);
            expect(service.context.visual.attentionScore).toBeCloseTo(0.9);
            expect(service.context.visual.bodyLanguage).toBe('engaged');
            expect(service.context.visual.movementLevel).toBeCloseTo(0.2);
            expect(service.context.visual.lastVisualUpdate).toBeTruthy();
        });
        
        test('should ignore low-confidence emotion detection', () => {
            const lowConfidenceData = {
                emotion: 'sad',
                emotionConfidence: 0.3 // Below threshold
            };
            
            service.updateVisualContext(lowConfidenceData);
            
            expect(service.context.visual.currentEmotion).toBe('neutral'); // Should remain default
            expect(service.context.visual.emotionConfidence).toBe(0); // Should remain default
        });
        
        test('should clamp visual metrics to valid ranges', () => {
            const extremeData = {
                eyeContactLevel: 2.0, // Above 1.0
                attentionScore: -0.5, // Below 0.0
                movementLevel: 1.5 // Above 1.0
            };
            
            service.updateVisualContext(extremeData);
            
            expect(service.context.visual.eyeContactLevel).toBe(1.0);
            expect(service.context.visual.attentionScore).toBe(0.0);
            expect(service.context.visual.movementLevel).toBe(1.0);
        });
        
        test('should calculate visual engagement score', () => {
            // Set up good visual conditions
            service.context.visual.faceDetected = true;
            service.context.visual.currentEmotion = 'happy';
            service.context.visual.emotionConfidence = 0.8;
            service.context.visual.eyeContactLevel = 0.8;
            service.context.visual.attentionScore = 0.9;
            service.context.visual.movementLevel = 0.3; // Optimal movement
            
            const visualEngagement = service._calculateVisualEngagement();
            
            expect(visualEngagement).toBeGreaterThan(0.7);
            expect(visualEngagement).toBeLessThanOrEqual(1.0);
        });
    });
    
    describe('User Profile Analysis', () => {
        test('should update user profile with preferences', () => {
            const profileData = {
                preferences: {
                    verbosity: 0.8,
                    interactivity: 0.6
                },
                patterns: {
                    avgWordsPerSentence: 12,
                    wordCount: 100
                },
                communicationStyle: 'detailed',
                interactionMode: 'inquiry_focused',
                attentionSpan: 180000,
                responseTimePreference: 'fast'
            };
            
            service.updateUserProfile(profileData);
            
            expect(service.context.profile.sessionPreferences.verbosity).toBeCloseTo(0.08); // Learning rate applied
            expect(service.context.profile.sessionPreferences.interactivity).toBeCloseTo(0.06);
            expect(service.context.profile.communicationStyle).toBe('detailed');
            expect(service.context.profile.preferredInteractionMode).toBe('inquiry_focused');
            expect(service.context.profile.responseTimePreference).toBe('fast');
            expect(service.context.profile.lastProfileUpdate).toBeTruthy();
        });
        
        test('should apply adaptive learning to attention span', () => {
            const currentSpan = 120000;
            service.context.profile.attentionSpan = currentSpan;
            
            const newSpan = 180000;
            service.updateUserProfile({ attentionSpan: newSpan });
            
            // Should be between original and new value due to learning rate
            expect(service.context.profile.attentionSpan).toBeGreaterThan(currentSpan);
            expect(service.context.profile.attentionSpan).toBeLessThan(newSpan);
        });
        
        test('should analyze text patterns correctly', () => {
            const shortText = "Yes. No. Maybe.";
            const shortAnalysis = service._analyzeTextPatterns(shortText);
            expect(shortAnalysis.communicationStyle).toBe('concise');
            
            const longText = "I would really appreciate if you could provide me with a very detailed and comprehensive explanation of the various factors that contribute to this complex situation.";
            const longAnalysis = service._analyzeTextPatterns(longText);
            expect(longAnalysis.communicationStyle).toBe('detailed');
            
            const questionText = "How does this work? What are the options?";
            const questionAnalysis = service._analyzeTextPatterns(questionText);
            expect(questionAnalysis.interactionMode).toBe('inquiry_focused');
            
            const commandText = "Please do this. Can you help with that?";
            const commandAnalysis = service._analyzeTextPatterns(commandText);
            expect(commandAnalysis.interactionMode).toBe('mixed');
        });
    });
    
    describe('Multimodal Input Processing', () => {
        test('should process valid multimodal input', async () => {
            const multimodalInput = {
                text: "Hello, how are you?",
                audio: new Float32Array([0.1, 0.2, 0.15, 0.3]),
                video: ["base64frame1", "base64frame2"]
            };
            
            const result = await service.processMultimodalInput(multimodalInput);
            
            expect(result.success).toBe(true);
            expect(result.normalizedInput).toBeDefined();
            expect(result.normalizedInput.text).toBe("Hello, how are you?");
            expect(result.normalizedInput.audio).toBeInstanceOf(Float32Array);
            expect(result.normalizedInput.video).toEqual(["base64frame1", "base64frame2"]);
        });
        
        test('should handle invalid multimodal input', async () => {
            const invalidInput = {
                text: "Hello",
                video: [123, 456] // Invalid non-string frames
            };
            
            const result = await service.processMultimodalInput(invalidInput);
            
            expect(result.success).toBe(false);
            expect(result.error).toBe('Invalid multimodal input');
            expect(result.details).toBeDefined();
        });
        
        test('should update interaction tracking for multimodal input', async () => {
            const multimodalInput = {
                text: "Test message",
                audio: new Float32Array([0.1, 0.2])
            };
            
            await service.processMultimodalInput(multimodalInput);
            
            expect(service.context.recentInteractions).toHaveLength(1);
            expect(service.context.recentInteractions[0].multimodal).toBe(true);
            expect(service.context.recentInteractions[0].audioData).toBe(true);
        });
    });
    
    describe('Multimodal Engagement Calculation', () => {
        test('should calculate combined engagement score', () => {
            // Set up good conditions across all modalities
            service.context.recentInteractions = [
                { type: 'user', timestamp: Date.now() - 1000 },
                { type: 'user', timestamp: Date.now() - 2000 }
            ];
            service.context.audio.currentVolume = 0.8;
            service.context.audio.qualityScore = 0.9;
            service.context.audio.speechRate = 2.5;
            service.context.visual.faceDetected = true;
            service.context.visual.attentionScore = 0.8;
            service.context.visual.eyeContactLevel = 0.7;
            
            const engagement = service.calculateMultimodalEngagement();
            
            expect(engagement.combined.score).toBeGreaterThan(0.5);
            expect(engagement.combined.confidence).toBeGreaterThan(0);
            expect(engagement.combined.level).toBeDefined();
            expect(engagement.modalities.text.score).toBeGreaterThan(0);
            expect(engagement.modalities.audio.score).toBeGreaterThan(0);
            expect(engagement.modalities.visual.score).toBeGreaterThan(0);
        });
        
        test('should use custom weights for engagement calculation', () => {
            const weights = { text: 0.1, audio: 0.8, visual: 0.1 };
            
            // Set high audio engagement, low others
            service.context.audio.currentVolume = 0.9;
            service.context.audio.qualityScore = 0.9;
            service.context.visual.attentionScore = 0.2;
            
            const engagement = service.calculateMultimodalEngagement(weights);
            
            expect(engagement.modalities.audio.weight).toBe(0.8);
            expect(engagement.modalities.audio.contribution).toBeGreaterThan(
                engagement.modalities.visual.contribution
            );
        });
        
        test('should categorize engagement levels correctly', () => {
            expect(service._categorizeEngagement(0.9)).toBe('very_high');
            expect(service._categorizeEngagement(0.7)).toBe('high');
            expect(service._categorizeEngagement(0.5)).toBe('medium');
            expect(service._categorizeEngagement(0.3)).toBe('low');
            expect(service._categorizeEngagement(0.1)).toBe('very_low');
        });
    });
    
    describe('Multimodal Statistics', () => {
        test('should provide comprehensive multimodal stats', () => {
            // Set up some context data
            service.context.audio.currentVolume = 0.7;
            service.context.audio.lastAudioUpdate = Date.now() - 1000;
            service.context.visual.faceDetected = true;
            service.context.visual.lastVisualUpdate = Date.now() - 2000;
            service.context.profile.communicationStyle = 'detailed';
            service.context.profile.lastProfileUpdate = Date.now() - 3000;
            
            const stats = service.getMultimodalStats();
            
            expect(stats.multimodal).toBeDefined();
            expect(stats.multimodal.audio.currentVolume).toBe(0.7);
            expect(stats.multimodal.visual.faceDetected).toBe(true);
            expect(stats.multimodal.profile.communicationStyle).toBe('detailed');
            expect(stats.multimodal.engagement).toBeDefined();
            expect(stats.multimodal.dataAvailability.hasRecentAudio).toBe(true);
            expect(stats.multimodal.dataAvailability.hasRecentVisual).toBe(true);
            expect(stats.multimodal.dataAvailability.hasProfileData).toBe(true);
        });
        
        test('should indicate data availability correctly', () => {
            // Set up old data (beyond 5 second threshold)
            const oldTimestamp = Date.now() - 10000;
            service.context.audio.lastAudioUpdate = oldTimestamp;
            service.context.visual.lastVisualUpdate = oldTimestamp;
            
            const stats = service.getMultimodalStats();
            
            expect(stats.multimodal.dataAvailability.hasRecentAudio).toBe(false);
            expect(stats.multimodal.dataAvailability.hasRecentVisual).toBe(false);
        });
    });
    
    describe('Multimodal Triggers', () => {
        test('should detect audio volume drop trigger', () => {
            service.context.audio.currentVolume = 0.05; // Below threshold
            service.context.audio.lastAudioUpdate = Date.now() - 1000; // Recent
            
            const triggers = service._evaluateMultimodalTriggers();
            
            const volumeDropTrigger = triggers.find(t => t.type === CONTEXTUAL_TRIGGERS.AUDIO_VOLUME_DROP);
            expect(volumeDropTrigger).toBeDefined();
            expect(volumeDropTrigger.priority).toBe('medium');
            expect(volumeDropTrigger.confidence).toBeGreaterThan(0.5);
        });
        
        test('should detect audio quality degradation trigger', () => {
            service.context.audio.qualityScore = 0.3; // Below threshold
            
            const triggers = service._evaluateMultimodalTriggers();
            
            const qualityTrigger = triggers.find(t => t.type === CONTEXTUAL_TRIGGERS.AUDIO_QUALITY_DEGRADED);
            expect(qualityTrigger).toBeDefined();
            expect(qualityTrigger.priority).toBe('low');
        });
        
        test('should detect negative voice sentiment trigger', () => {
            service.context.audio.sentimentHistory = [
                { sentiment: 'negative', confidence: 0.8 },
                { sentiment: 'negative', confidence: 0.7 },
                { sentiment: 'negative', confidence: 0.9 }
            ];
            
            const triggers = service._evaluateMultimodalTriggers();
            
            const sentimentTrigger = triggers.find(t => t.type === CONTEXTUAL_TRIGGERS.VOICE_SENTIMENT_NEGATIVE);
            expect(sentimentTrigger).toBeDefined();
            expect(sentimentTrigger.priority).toBe('high');
        });
        
        test('should detect facial disengagement trigger', () => {
            service.context.visual.attentionScore = 0.3; // Below threshold
            
            const triggers = service._evaluateMultimodalTriggers();
            
            const disengagementTrigger = triggers.find(t => t.type === CONTEXTUAL_TRIGGERS.FACIAL_DISENGAGEMENT);
            expect(disengagementTrigger).toBeDefined();
            expect(disengagementTrigger.priority).toBe('high');
        });
        
        test('should detect visual distraction trigger', () => {
            service.context.visual.movementLevel = 0.8; // High movement
            
            const triggers = service._evaluateMultimodalTriggers();
            
            const distractionTrigger = triggers.find(t => t.type === CONTEXTUAL_TRIGGERS.VISUAL_DISTRACTION);
            expect(distractionTrigger).toBeDefined();
            expect(distractionTrigger.priority).toBe('medium');
        });
        
        test('should filter low-confidence triggers', () => {
            // Set up conditions that would create low-confidence triggers
            service.context.audio.currentVolume = 0.11; // Just above threshold
            service.context.visual.attentionScore = 0.51; // Just above threshold
            
            const triggers = service._evaluateMultimodalTriggers();
            
            // Should not contain any multimodal triggers due to low confidence
            const multimodalTriggers = triggers.filter(t => 
                [CONTEXTUAL_TRIGGERS.AUDIO_VOLUME_DROP, 
                 CONTEXTUAL_TRIGGERS.FACIAL_DISENGAGEMENT].includes(t.type)
            );
            expect(multimodalTriggers).toHaveLength(0);
        });
    });
    
    describe('Backward Compatibility', () => {
        test('should maintain compatibility with existing methods', () => {
            // Test that existing methods still work as before
            const analysis = service.performContextualAnalysis();
            expect(analysis).resolves.toBeDefined();
            
            const engagement = service.calculateEngagementLevel();
            expect(engagement).toBeGreaterThanOrEqual(0);
            expect(engagement).toBeLessThanOrEqual(1);
            
            const health = service.assessConversationHealth();
            expect(['thriving', 'healthy', 'declining', 'stalled']).toContain(health);
            
            const decision = service.decideCommunicationMode();
            expect(decision.success).toBe(true);
            expect(decision.decision).toBeDefined();
        });
        
        test('should reset all context including multimodal data', () => {
            // Set up some multimodal context
            service.context.audio.currentVolume = 0.8;
            service.context.visual.faceDetected = true;
            service.context.profile.communicationStyle = 'detailed';
            
            service.resetContext();
            
            // Check that multimodal context is reset
            expect(service.context.audio.currentVolume).toBe(0);
            expect(service.context.visual.faceDetected).toBe(false);
            expect(service.context.profile.communicationStyle).toBe('balanced');
        });
        
        test('should include multimodal triggers in contextual analysis', async () => {
            // Set up conditions for multimodal triggers
            service.context.audio.currentVolume = 0.05; // Low volume
            service.context.audio.lastAudioUpdate = Date.now() - 1000;
            service.context.visual.attentionScore = 0.3; // Low attention
            
            const analysis = await service.performContextualAnalysis();
            
            expect(analysis.success).toBe(true);
            expect(analysis.analysis.triggers.length).toBeGreaterThan(0);
            
            // Should include multimodal triggers
            const triggerTypes = analysis.analysis.triggers.map(t => t.type);
            expect(triggerTypes).toEqual(expect.arrayContaining([
                expect.stringMatching(/audio|visual|facial/)
            ]));
        });
    });
    
    describe('Error Handling', () => {
        test('should handle errors in audio context update gracefully', () => {
            // Simulate error by passing undefined
            expect(() => service.updateAudioContext(undefined)).not.toThrow();
            
            // Should maintain valid state
            expect(service.context.audio.currentVolume).toBeGreaterThanOrEqual(0);
            expect(service.context.audio.qualityScore).toBeGreaterThanOrEqual(0);
        });
        
        test('should handle errors in visual context update gracefully', () => {
            expect(() => service.updateVisualContext(null)).not.toThrow();
            
            // Should maintain valid state
            expect(service.context.visual.attentionScore).toBeGreaterThanOrEqual(0);
            expect(service.context.visual.eyeContactLevel).toBeGreaterThanOrEqual(0);
        });
        
        test('should handle errors in profile update gracefully', () => {
            expect(() => service.updateUserProfile({ invalid: 'data' })).not.toThrow();
            
            // Should maintain valid state
            expect(service.context.profile.communicationStyle).toBeDefined();
            expect(service.context.profile.attentionSpan).toBeGreaterThan(0);
        });
        
        test('should handle errors in engagement calculation gracefully', () => {
            // Corrupt context data
            service.context.audio = null;
            service.context.visual = null;
            
            const engagement = service.calculateMultimodalEngagement();
            
            expect(engagement.combined.score).toBe(0.5); // Fallback score
            expect(engagement.error).toBeDefined();
        });
    });
});