/**
 * Comprehensive test suite for SessionCoordinator
 * 
 * Tests all critical session management functionality:
 * - Session lifecycle management
 * - Python-compatible configuration generation
 * - State synchronization and transitions
 * - Event coordination
 * - Session health monitoring
 * - Race condition prevention
 */

import { SessionCoordinator } from '../SessionCoordinator.js';
import { SessionState, AliyunEventType } from '../AliyunConfig.js';

describe('SessionCoordinator', () => {
    let sessionCoordinator;
    let mockConnectionManager;
    let mockLogger;

    beforeEach(() => {
        // Mock connection manager
        mockConnectionManager = {
            isConnected: jest.fn(() => true),
            sendMessage: jest.fn(() => Promise.resolve(true)),
            on: jest.fn(),
            off: jest.fn()
        };

        // Mock logger
        mockLogger = {
            info: jest.fn(),
            debug: jest.fn(),
            warn: jest.fn(),
            error: jest.fn()
        };

        sessionCoordinator = new SessionCoordinator(mockConnectionManager, mockLogger);
    });

    afterEach(() => {
        if (sessionCoordinator) {
            sessionCoordinator.dispose();
        }
    });

    describe('Initialization', () => {
        test('should initialize with correct default state', () => {
            expect(sessionCoordinator.getSessionState()).toBe(SessionState.UNINITIALIZED);
            expect(sessionCoordinator.isSessionReady()).toBe(false);
            expect(sessionCoordinator.sessionId).toBeNull();
        });

        test('should validate connection manager on initialization', async () => {
            mockConnectionManager.isConnected.mockReturnValue(false);
            
            await expect(sessionCoordinator.initializeSession()).rejects.toThrow(
                'Connection manager not available or not connected'
            );
        });

        test('should prevent multiple initialization attempts', async () => {
            // Start first initialization
            const promise1 = sessionCoordinator.initializeSession();
            
            // State should be CREATING
            expect(sessionCoordinator.getSessionState()).toBe(SessionState.CREATING);
            
            // Second initialization should wait for first
            const promise2 = sessionCoordinator.initializeSession();
            
            // Simulate session.created event
            sessionCoordinator.handleSessionCreated({
                type: AliyunEventType.SESSION_CREATED,
                session: { id: 'test-session-1' }
            });

            // Simulate session.updated event
            sessionCoordinator.handleSessionUpdated({
                type: AliyunEventType.SESSION_UPDATED,
                session: { 
                    id: 'test-session-1',
                    turn_detection: {
                        type: 'server_vad',
                        threshold: 0.8,
                        silence_duration_ms: 1500
                    }
                }
            });

            const result1 = await promise1;
            const result2 = await promise2;

            expect(result1).toBe(true);
            expect(result2).toBe(true);
            expect(sessionCoordinator.getSessionState()).toBe(SessionState.READY);
        });
    });

    describe('Python-Compatible Configuration', () => {
        test('should generate valid Python-compatible VAD configuration', () => {
            const config = sessionCoordinator.createPythonCompatibleConfig();
            
            expect(config).toEqual({
                modalities: ["text", "audio"],
                voice: expect.any(String),
                input_audio_format: "pcm16",
                output_audio_format: "pcm16",
                input_audio_transcription: {
                    model: "gummy-realtime-v1"
                },
                turn_detection: {
                    type: "server_vad",
                    threshold: 0.8,
                    prefix_padding_ms: 500,
                    silence_duration_ms: 1500
                }
            });
        });

        test('should handle manual VAD mode configuration', () => {
            // Temporarily override VAD config
            const originalVadConfig = require('../AliyunConfig.js').ALIYUN_VAD_CONFIG;
            originalVadConfig.type = null;
            
            const config = sessionCoordinator.createPythonCompatibleConfig();
            
            expect(config.turn_detection).toBeNull();
            
            // Restore original config
            originalVadConfig.type = 'server_vad';
        });

        test('should apply configuration overrides correctly', () => {
            const overrides = {
                voice: 'Cherry',
                vadThreshold: 0.9,
                silenceDurationMs: 2000
            };
            
            const config = sessionCoordinator.createPythonCompatibleConfig(overrides);
            
            expect(config.voice).toBe('Cherry');
            expect(config.turn_detection.threshold).toBe(0.9);
            expect(config.turn_detection.silence_duration_ms).toBe(2000);
        });
    });

    describe('State Management', () => {
        test('should transition states correctly during initialization', async () => {
            const initPromise = sessionCoordinator.initializeSession();
            
            // Should be in CREATING state
            expect(sessionCoordinator.getSessionState()).toBe(SessionState.CREATING);
            
            // Simulate session.created
            sessionCoordinator.handleSessionCreated({
                type: AliyunEventType.SESSION_CREATED,
                session: { id: 'test-session' }
            });
            
            expect(sessionCoordinator.getSessionState()).toBe(SessionState.CREATED);
            
            // Simulate session.updated
            sessionCoordinator.handleSessionUpdated({
                type: AliyunEventType.SESSION_UPDATED,
                session: { 
                    id: 'test-session',
                    turn_detection: { type: 'server_vad' }
                }
            });
            
            expect(sessionCoordinator.getSessionState()).toBe(SessionState.READY);
            
            await initPromise;
        });

        test('should maintain state history', async () => {
            const initPromise = sessionCoordinator.initializeSession();
            
            sessionCoordinator.handleSessionCreated({
                type: AliyunEventType.SESSION_CREATED,
                session: { id: 'test-session' }
            });
            
            sessionCoordinator.handleSessionUpdated({
                type: AliyunEventType.SESSION_UPDATED,
                session: { id: 'test-session' }
            });
            
            await initPromise;
            
            const sessionInfo = sessionCoordinator.getSessionInfo();
            expect(sessionInfo.stateHistory).toHaveLength(3); // UNINITIALIZED -> CREATING -> CREATED -> READY
            
            const lastTransition = sessionInfo.stateHistory[sessionInfo.stateHistory.length - 1];
            expect(lastTransition.to).toBe(SessionState.READY);
        });

        test('should handle error state transitions', () => {
            sessionCoordinator._transitionToState(SessionState.ERROR);
            
            expect(sessionCoordinator.getSessionState()).toBe(SessionState.ERROR);
            expect(sessionCoordinator.isSessionReady()).toBe(false);
        });
    });

    describe('Event Handling', () => {
        test('should handle session.created events correctly', () => {
            const event = {
                type: AliyunEventType.SESSION_CREATED,
                session: { 
                    id: 'test-session-123',
                    model: 'qwen-omni-turbo-realtime'
                }
            };
            
            sessionCoordinator._transitionToState(SessionState.CREATING);
            sessionCoordinator.handleSessionCreated(event);
            
            expect(sessionCoordinator.sessionId).toBe('test-session-123');
            expect(sessionCoordinator.getSessionState()).toBe(SessionState.CREATED);
        });

        test('should handle session.updated events with VAD configuration', () => {
            const event = {
                type: AliyunEventType.SESSION_UPDATED,
                session: {
                    id: 'test-session-123',
                    turn_detection: {
                        type: 'server_vad',
                        threshold: 0.8,
                        prefix_padding_ms: 500,
                        silence_duration_ms: 1500
                    }
                }
            };
            
            sessionCoordinator.sessionId = 'test-session-123';
            sessionCoordinator._transitionToState(SessionState.CONFIGURING);
            sessionCoordinator.handleSessionUpdated(event);
            
            expect(sessionCoordinator.getSessionState()).toBe(SessionState.READY);
            
            const sessionInfo = sessionCoordinator.getSessionInfo();
            expect(sessionInfo.vadEnabled).toBe(true);
        });

        test('should handle event handler registration and notification', () => {
            const mockHandler = jest.fn();
            
            sessionCoordinator.on('sessionReady', mockHandler);
            
            // Trigger session ready event
            sessionCoordinator._notifyEventHandlers('sessionReady', { test: 'data' });
            
            expect(mockHandler).toHaveBeenCalledWith({ test: 'data' });
        });
    });

    describe('Session Updates', () => {
        beforeEach(async () => {
            // Initialize session first
            const initPromise = sessionCoordinator.initializeSession();
            
            sessionCoordinator.handleSessionCreated({
                type: AliyunEventType.SESSION_CREATED,
                session: { id: 'test-session' }
            });
            
            sessionCoordinator.handleSessionUpdated({
                type: AliyunEventType.SESSION_UPDATED,
                session: { id: 'test-session' }
            });
            
            await initPromise;
        });

        test('should update session configuration successfully', async () => {
            const updates = {
                voice: 'Cherry',
                vadThreshold: 0.9
            };
            
            const result = await sessionCoordinator.updateSession(updates);
            
            expect(result).toBe(true);
            expect(mockConnectionManager.sendMessage).toHaveBeenCalledTimes(2); // Once for init, once for update
            
            // Check that overrides were applied
            const config = sessionCoordinator.pythonCompatibleConfig;
            expect(config.voice).toBe('Cherry');
            expect(config.turn_detection.threshold).toBe(0.9);
        });

        test('should track update count in health metrics', async () => {
            await sessionCoordinator.updateSession({ voice: 'Serena' });
            await sessionCoordinator.updateSession({ vadThreshold: 0.7 });
            
            const healthStatus = sessionCoordinator.getHealthStatus();
            expect(healthStatus.updateCount).toBe(2);
        });
    });

    describe('Health Monitoring', () => {
        test('should provide comprehensive health status', () => {
            const healthStatus = sessionCoordinator.getHealthStatus();
            
            expect(healthStatus).toEqual({
                isHealthy: expect.any(Boolean),
                healthScore: expect.any(Number),
                status: expect.any(String),
                uptime: expect.any(Number),
                state: sessionCoordinator.getSessionState(),
                errorCount: expect.any(Number),
                updateCount: expect.any(Number),
                vadEnabled: expect.any(Boolean),
                lastCheck: expect.any(Number),
                recommendations: expect.any(Array)
            });
        });

        test('should calculate health score correctly', () => {
            // Test healthy session
            sessionCoordinator._transitionToState(SessionState.READY);
            sessionCoordinator.healthMetrics.errorCount = 0;
            
            let healthStatus = sessionCoordinator.getHealthStatus();
            expect(healthStatus.healthScore).toBe(100);
            expect(healthStatus.status).toBe('Excellent');
            
            // Test session with errors
            sessionCoordinator.healthMetrics.errorCount = 2;
            healthStatus = sessionCoordinator.getHealthStatus();
            expect(healthStatus.healthScore).toBeLessThan(100);
        });

        test('should provide relevant recommendations', () => {
            // Test uninitialized session
            const healthStatus = sessionCoordinator.getHealthStatus();
            expect(healthStatus.recommendations).toContain('Initialize or fix session to reach READY state');
            
            // Test ready session
            sessionCoordinator._transitionToState(SessionState.READY);
            sessionCoordinator.healthMetrics.errorCount = 0;
            
            const readyHealthStatus = sessionCoordinator.getHealthStatus();
            expect(readyHealthStatus.recommendations).toContain('Session is healthy - no action required');
        });
    });

    describe('Configuration Validation', () => {
        test('should validate configuration correctly', () => {
            const validConfig = {
                modalities: ['text', 'audio'],
                voice: 'Chelsie',
                input_audio_format: 'pcm16',
                output_audio_format: 'pcm16',
                turn_detection: {
                    type: 'server_vad',
                    threshold: 0.8,
                    silence_duration_ms: 1500
                }
            };
            
            const validation = sessionCoordinator.validateConfiguration(validConfig);
            
            expect(validation.isValid).toBe(true);
            expect(validation.errors).toHaveLength(0);
        });

        test('should detect configuration errors', () => {
            const invalidConfig = {
                modalities: [], // Empty modalities
                input_audio_format: 'mp3' // Invalid format
            };
            
            const validation = sessionCoordinator.validateConfiguration(invalidConfig);
            
            expect(validation.isValid).toBe(false);
            expect(validation.errors.length).toBeGreaterThan(0);
            expect(validation.errors).toContain('At least one modality is required');
            expect(validation.errors).toContain('Input audio format must be pcm16 for VAD compatibility');
        });

        test('should provide warnings for suboptimal configurations', () => {
            const suboptimalConfig = {
                modalities: ['text', 'audio'],
                voice: 'UnknownVoice',
                turn_detection: {
                    type: 'server_vad',
                    threshold: 1.5, // Out of range
                    silence_duration_ms: 100 // Too short
                }
            };
            
            const validation = sessionCoordinator.validateConfiguration(suboptimalConfig);
            
            expect(validation.warnings.length).toBeGreaterThan(0);
            expect(validation.warnings).toContain('VAD threshold should be between 0 and 1');
            expect(validation.warnings).toContain('Silence duration below 500ms may cause frequent interruptions');
        });
    });

    describe('Wait for Ready', () => {
        test('should return immediately if already ready', async () => {
            sessionCoordinator._transitionToState(SessionState.READY);
            
            const startTime = Date.now();
            const result = await sessionCoordinator.waitForReady(1000);
            const endTime = Date.now();
            
            expect(result).toBe(true);
            expect(endTime - startTime).toBeLessThan(100); // Should be immediate
        });

        test('should wait for ready state and resolve when reached', async () => {
            sessionCoordinator._transitionToState(SessionState.CREATING);
            
            const waitPromise = sessionCoordinator.waitForReady(2000);
            
            // Simulate state transition after 100ms
            setTimeout(() => {
                sessionCoordinator._transitionToState(SessionState.READY);
            }, 100);
            
            const result = await waitPromise;
            expect(result).toBe(true);
        });

        test('should timeout if ready state not reached', async () => {
            sessionCoordinator._transitionToState(SessionState.CREATING);
            
            const result = await sessionCoordinator.waitForReady(100); // Short timeout
            
            expect(result).toBe(false);
        });

        test('should return false if session enters error state', async () => {
            sessionCoordinator._transitionToState(SessionState.CREATING);
            
            const waitPromise = sessionCoordinator.waitForReady(2000);
            
            // Simulate error state after 100ms
            setTimeout(() => {
                sessionCoordinator._transitionToState(SessionState.ERROR);
            }, 100);
            
            const result = await waitPromise;
            expect(result).toBe(false);
        });
    });

    describe('Session Reset', () => {
        test('should reset session state completely', async () => {
            // Initialize and setup session
            const initPromise = sessionCoordinator.initializeSession();
            
            sessionCoordinator.handleSessionCreated({
                type: AliyunEventType.SESSION_CREATED,
                session: { id: 'test-session' }
            });
            
            sessionCoordinator.handleSessionUpdated({
                type: AliyunEventType.SESSION_UPDATED,
                session: { id: 'test-session' }
            });
            
            await initPromise;
            
            // Verify session is ready
            expect(sessionCoordinator.isSessionReady()).toBe(true);
            expect(sessionCoordinator.sessionId).toBe('test-session');
            
            // Reset session
            const resetResult = await sessionCoordinator.resetSession();
            
            expect(resetResult).toBe(true);
            expect(sessionCoordinator.getSessionState()).toBe(SessionState.UNINITIALIZED);
            expect(sessionCoordinator.sessionId).toBeNull();
            expect(sessionCoordinator.sessionConfig).toBeNull();
            expect(sessionCoordinator.pythonCompatibleConfig).toBeNull();
        });
    });

    describe('Cleanup and Disposal', () => {
        test('should cleanup resources properly', () => {
            const mockHandler = jest.fn();
            sessionCoordinator.on('sessionReady', mockHandler);
            
            sessionCoordinator.dispose();
            
            expect(sessionCoordinator.getSessionState()).toBe(SessionState.UNINITIALIZED);
            expect(sessionCoordinator.eventHandlers.sessionReady).toHaveLength(0);
        });
    });

    describe('Race Condition Prevention', () => {
        test('should handle rapid session events correctly', async () => {
            const initPromise = sessionCoordinator.initializeSession();
            
            // Rapidly fire session events
            sessionCoordinator.handleSessionCreated({
                type: AliyunEventType.SESSION_CREATED,
                session: { id: 'test-session' }
            });
            
            sessionCoordinator.handleSessionUpdated({
                type: AliyunEventType.SESSION_UPDATED,
                session: { id: 'test-session' }
            });
            
            const result = await initPromise;
            
            expect(result).toBe(true);
            expect(sessionCoordinator.getSessionState()).toBe(SessionState.READY);
            expect(sessionCoordinator.sessionId).toBe('test-session');
        });

        test('should handle concurrent update requests', async () => {
            // Initialize session first
            const initPromise = sessionCoordinator.initializeSession();
            
            sessionCoordinator.handleSessionCreated({
                type: AliyunEventType.SESSION_CREATED,
                session: { id: 'test-session' }
            });
            
            sessionCoordinator.handleSessionUpdated({
                type: AliyunEventType.SESSION_UPDATED,
                session: { id: 'test-session' }
            });
            
            await initPromise;
            
            // Make concurrent updates
            const update1 = sessionCoordinator.updateSession({ voice: 'Cherry' });
            const update2 = sessionCoordinator.updateSession({ vadThreshold: 0.9 });
            
            const results = await Promise.all([update1, update2]);
            
            expect(results[0]).toBe(true);
            expect(results[1]).toBe(true);
            
            // Both updates should be applied
            const config = sessionCoordinator.pythonCompatibleConfig;
            expect(config.voice).toBe('Cherry');
            expect(config.turn_detection.threshold).toBe(0.9);
        });
    });
});

/**
 * Integration test demonstrating SessionCoordinator usage
 */
describe('SessionCoordinator Integration', () => {
    test('complete session lifecycle with VAD configuration', async () => {
        const mockConnectionManager = {
            isConnected: () => true,
            sendMessage: jest.fn(() => Promise.resolve(true))
        };
        
        const coordinator = new SessionCoordinator(mockConnectionManager);
        
        // Track state changes
        const stateChanges = [];
        coordinator.on('sessionCreated', (data) => stateChanges.push('created'));
        coordinator.on('sessionUpdated', (data) => stateChanges.push('updated'));
        coordinator.on('sessionReady', (data) => stateChanges.push('ready'));
        
        // Initialize with custom VAD settings
        const initPromise = coordinator.initializeSession({
            voice: 'Cherry',
            vadThreshold: 0.9,
            silenceDurationMs: 2000
        });
        
        // Simulate server events
        coordinator.handleSessionCreated({
            type: AliyunEventType.SESSION_CREATED,
            session: { id: 'integration-test-session' }
        });
        
        coordinator.handleSessionUpdated({
            type: AliyunEventType.SESSION_UPDATED,
            session: {
                id: 'integration-test-session',
                turn_detection: {
                    type: 'server_vad',
                    threshold: 0.9,
                    silence_duration_ms: 2000
                }
            }
        });
        
        const result = await initPromise;
        
        // Verify successful initialization
        expect(result).toBe(true);
        expect(coordinator.isSessionReady()).toBe(true);
        expect(stateChanges).toEqual(['created', 'updated', 'ready']);
        
        // Verify Python-compatible configuration
        const sessionInfo = coordinator.getSessionInfo();
        expect(sessionInfo.vadEnabled).toBe(true);
        expect(sessionInfo.vadConfiguration.threshold).toBe(0.9);
        expect(sessionInfo.vadConfiguration.silence_duration_ms).toBe(2000);
        
        // Verify health status
        const healthStatus = coordinator.getHealthStatus();
        expect(healthStatus.isHealthy).toBe(true);
        expect(healthStatus.healthScore).toBe(100);
        
        // Update configuration
        const updateResult = await coordinator.updateSession({
            voice: 'Serena',
            vadThreshold: 0.8
        });
        
        expect(updateResult).toBe(true);
        
        // Verify send message was called correctly
        expect(mockConnectionManager.sendMessage).toHaveBeenCalledTimes(2); // Initial + update
        
        const lastCall = mockConnectionManager.sendMessage.mock.calls[1][0];
        expect(lastCall.type).toBe('session.update');
        expect(lastCall.session.voice).toBe('Serena');
        expect(lastCall.session.turn_detection.threshold).toBe(0.8);
        
        coordinator.dispose();
    });
});