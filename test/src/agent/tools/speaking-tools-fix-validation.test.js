/**
 * Test script to validate the consolidated speaking tools work correctly
 * and that the LangGraph ReactAgent error is fixed
 */

import { describe, test, expect, vi, beforeEach } from 'vitest';
import { agentSpeakingToolCollection, AGENT_SPEAKING_TOOL_NAMES } from '../../../../src/agent/tools/avatar/speaking.js';
import { debugToolsForLangGraph } from '../../../../src/agent/config/debug/ToolDebugger.js';

describe('Speaking Tools LangGraph ReactAgent Fix Validation', () => {
    test('should have exactly 3 consolidated tools', () => {
        expect(agentSpeakingToolCollection).toBeDefined();
        expect(agentSpeakingToolCollection.length).toBe(3);
        expect(agentSpeakingToolCollection.map(t => t.name)).toEqual([
            'control_avatar_speech',
            'speak_response', 
            'voice_profile_manager'
        ]);
    });

    test('should have all tools with proper LangGraph structure', () => {
        for (const tool of agentSpeakingToolCollection) {
            expect(tool).toBeDefined();
            expect(tool).not.toBeNull();
            expect(typeof tool).toBe('object');
            expect(tool.name).toBeDefined();
            expect(typeof tool.name).toBe('string');
            expect(tool.description).toBeDefined();
            expect(typeof tool.description).toBe('string');
            expect(tool.schema).toBeDefined();
            expect(tool.invoke).toBeDefined();
            expect(typeof tool.invoke).toBe('function');
        }
    });

    test('should pass LangGraph validation without undefined/null tools', () => {
        const debugReport = debugToolsForLangGraph(agentSpeakingToolCollection);
        
        expect(debugReport.totalTools).toBe(3);
        expect(debugReport.validTools.length).toBe(3);
        expect(debugReport.undefinedTools.length).toBe(0);
        expect(debugReport.nullTools.length).toBe(0);
        expect(debugReport.invalidTools.length).toBe(0);
        expect(debugReport.missingProperties.length).toBe(0);
    });

    test('should not cause "Cannot set properties of undefined (setting name)" error', () => {
        // Simulate what LangGraph ReactAgent does internally
        let hasUndefinedNameError = false;
        
        try {
            for (let i = 0; i < agentSpeakingToolCollection.length; i++) {
                const tool = agentSpeakingToolCollection[i];
                
                // These checks should not throw errors
                expect(tool).toBeDefined();
                expect(tool).not.toBeNull();
                expect(tool.name).toBeDefined();
                
                // This is the line that was causing the error in LangGraph
                let testName = tool.name; // Should not throw
                expect(typeof testName).toBe('string');
                expect(testName.length).toBeGreaterThan(0);
            }
        } catch (error) {
            hasUndefinedNameError = true;
            console.error('Unexpected error during tool validation:', error);
        }
        
        expect(hasUndefinedNameError).toBe(false);
    });

    test('should have no audio playing tools (handled by @src/media/)', () => {
        const toolNames = agentSpeakingToolCollection.map(t => t.name);
        
        // These tools should NOT be present (they were duplicates or handled elsewhere)
        expect(toolNames).not.toContain('play_audio');
        expect(toolNames).not.toContain('process_llm_audio_response');
        
        // Only these 3 essential tools should remain
        expect(toolNames).toContain('control_avatar_speech');
        expect(toolNames).toContain('speak_response');
        expect(toolNames).toContain('voice_profile_manager');
    });

    test('should have proper tool name exports for compatibility', () => {
        expect(AGENT_SPEAKING_TOOL_NAMES).toBeDefined();
        expect(AGENT_SPEAKING_TOOL_NAMES.CONTROL_AVATAR_SPEECH).toBe('control_avatar_speech');
        expect(AGENT_SPEAKING_TOOL_NAMES.SPEAK_RESPONSE).toBe('speak_response');
        expect(AGENT_SPEAKING_TOOL_NAMES.VOICE_PROFILE_MANAGER).toBe('voice_profile_manager');
    });

    test('should validate debug report recommendations', () => {
        const debugReport = debugToolsForLangGraph(agentSpeakingToolCollection);
        
        // Should have positive recommendations indicating success
        expect(debugReport.recommendations).toContain('✅ Found 3 valid tools for ReactAgent');
        
        // Should NOT have any error recommendations
        const errorRecommendations = debugReport.recommendations.filter(rec => 
            rec.includes('🚨 CRITICAL') || 
            rec.includes('❌') ||
            rec.includes('undefined tools')
        );
        expect(errorRecommendations.length).toBe(0);
    });

    test('should have all individual tool exports for backward compatibility', () => {
        // Test imports to ensure compatibility exports work
        expect(() => {
            const { agentTTSTool, voiceProfileTool, avatarSpeechControlTool } = 
                require('../../../../src/agent/tools/avatar/speaking.js');
            
            expect(agentTTSTool).toBeDefined();
            expect(voiceProfileTool).toBeDefined();
            expect(avatarSpeechControlTool).toBeDefined();
        }).not.toThrow();
    });

    test('comprehensive fix validation - everything should work correctly', () => {
        const debugReport = debugToolsForLangGraph(agentSpeakingToolCollection);
        
        // All critical checks for the fix
        const isFullyFixed = 
            debugReport.undefinedTools.length === 0 && 
            debugReport.invalidTools.length === 0 && 
            debugReport.validTools.length === agentSpeakingToolCollection.length &&
            agentSpeakingToolCollection.length === 3 &&
            agentSpeakingToolCollection.every(tool => 
                tool && 
                tool.name && 
                typeof tool.name === 'string' &&
                typeof tool.invoke === 'function'
            );
        
        expect(isFullyFixed).toBe(true);
        
        if (isFullyFixed) {
            console.log('✅ SUCCESS: All speaking tools are properly consolidated');
            console.log('✅ SUCCESS: "Cannot set properties of undefined (setting \'name\')" error should be FIXED');
            console.log('✅ SUCCESS: LangGraph ReactAgent should work with these tools');
        }
    });
});