/**
 * System 2 Speaking Tools Integration Tests
 * 
 * Tests that speaking tools work correctly when called from System 2
 * through the enhanced dual-brain architecture
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  avatarSpeechControlTool,
  agentTTSTool,
  voiceProfileTool,
  playAudioTool,
  getSharedSpeakingService
} from '../../../../src/agent/tools/avatar/speaking.js';

// Mock the logger
vi.mock('../../../../src/utils/logger.ts', () => ({
  createLogger: () => ({
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    setLogLevel: vi.fn()
  }),
  LogLevel: { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3 }
}));

// Mock audio utilities
vi.mock('../../../../src/media/modality/audio.ts', () => ({
  playBase64Audio: vi.fn().mockResolvedValue({ duration: 1000 }),
  AudioProcessor: vi.fn().mockImplementation(() => ({
    processAndPlay: vi.fn().mockResolvedValue({
      audioPlayed: true,
      audioLength: 1000,
      error: null
    })
  }))
}));

// Mock environment variables
vi.mock('../../../../src/config/env.ts', () => ({
  getEnvVar: vi.fn().mockReturnValue('mock-api-key')
}));

describe('System 2 Speaking Tools Integration', () => {
  let mockAvatarController;
  let mockAliyunModel;
  let mockAudioPlayer;
  let testConfig;

  beforeEach(() => {
    // Mock avatar controller with state manager
    mockAvatarController = {
      stateManager: {
        startSpeaking: vi.fn().mockResolvedValue(true),
        stopSpeaking: vi.fn().mockResolvedValue(true),
        getState: vi.fn().mockReturnValue({
          isSpeaking: false,
          isListening: true,
          current: 'idle'
        }),
        setState: vi.fn().mockResolvedValue(true)
      },
      isSpeaking: false,
      isListening: true,
      stopSpeaking: vi.fn().mockResolvedValue(true),
      setSilentMode: vi.fn().mockResolvedValue(true)
    };

    // Mock Aliyun model for realtime TTS
    mockAliyunModel = {
      isRealtimeModeActive: vi.fn().mockReturnValue(false),
      updateRealtimeSession: vi.fn().mockResolvedValue(true),
      sendRealtimeText: vi.fn().mockResolvedValue(true)
    };

    // Mock audio player
    mockAudioPlayer = {
      playAudioChunk: vi.fn().mockResolvedValue(true)
    };

    // Test configuration passed to tools
    testConfig = {
      avatarController: mockAvatarController,
      aliyunModel: mockAliyunModel,
      audioPlayer: mockAudioPlayer,
      sessionId: 'test_system2_session'
    };

    // Clear any previous global state
    if (globalThis.talkingAvatar) {
      delete globalThis.talkingAvatar;
    }
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Avatar Speech Control Tool', () => {
    it('should execute speak action from System 2', async () => {
      const input = {
        action: 'speak',
        text: 'Hello from System 2!',
        voice: 'Serena',
        options: {
          streaming: true,
          priority: 'high'
        }
      };

      const result = await avatarSpeechControlTool.func(input, testConfig);

      expect(result.success).toBe(true);
      expect(result.action).toBe('speak');
      expect(result.speaking).toBe(true);
      expect(result.method).toBe('voice_cloning'); // Will use CosyVoice since no realtime
      expect(mockAvatarController.stateManager.startSpeaking).toHaveBeenCalledWith('test_system2_session');
    });

    it('should handle speak action with realtime model active', async () => {
      // Configure realtime mode as active
      mockAliyunModel.isRealtimeModeActive.mockReturnValue(true);
      
      const input = {
        action: 'speak',
        text: 'Realtime speech from System 2',
        voice: 'Chelsie'
      };

      const result = await avatarSpeechControlTool.func(input, testConfig);

      expect(result.success).toBe(true);
      expect(result.method).toBe('realtime');
      expect(mockAliyunModel.updateRealtimeSession).toHaveBeenCalledWith({ voice: 'Chelsie' });
      expect(mockAliyunModel.sendRealtimeText).toHaveBeenCalledWith('Realtime speech from System 2');
    });

    it('should execute stop_speaking action', async () => {
      const input = {
        action: 'stop_speaking'
      };

      const result = await avatarSpeechControlTool.func(input, testConfig);

      expect(result.success).toBe(true);
      expect(result.action).toBe('stop_speaking');
      expect(result.speaking).toBe(false);
      expect(mockAvatarController.stateManager.stopSpeaking).toHaveBeenCalledWith('test_system2_session');
    });

    it('should check avatar status', async () => {
      const input = {
        action: 'check_status'
      };

      const result = await avatarSpeechControlTool.func(input, testConfig);

      expect(result.success).toBe(true);
      expect(result.action).toBe('check_status');
      expect(result.speaking).toBe(false);
      expect(result.listening).toBe(true);
      expect(result.currentState).toBe('idle');
    });

    it('should set silent mode', async () => {
      const input = {
        action: 'set_silent_mode',
        options: { silent: true }
      };

      const result = await avatarSpeechControlTool.func(input, testConfig);

      expect(result.success).toBe(true);
      expect(result.action).toBe('set_silent_mode');
      expect(result.silentMode).toBe(true);
      expect(mockAvatarController.stateManager.setState).toHaveBeenCalled();
    });

    it('should handle missing avatar controller gracefully', async () => {
      const configWithoutAvatar = { ...testConfig, avatarController: null };
      
      const input = {
        action: 'speak',
        text: 'Test without avatar'
      };

      const result = await avatarSpeechControlTool.func(input, configWithoutAvatar);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Avatar controller not available');
    });

    it('should validate required text parameter for speak action', async () => {
      const input = {
        action: 'speak'
        // Missing text parameter
      };

      const result = await avatarSpeechControlTool.func(input, testConfig);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Text parameter is required');
    });
  });

  describe('Agent TTS Tool', () => {
    it('should execute TTS from System 2', async () => {
      const input = {
        text: 'System 2 reasoning complete',
        voice: 'Ethan',
        useVoiceCloning: false,
        options: {
          streaming: true,
          rate: 1.2
        }
      };

      const result = await agentTTSTool.func(input, testConfig);

      expect(result.success).toBe(true);
      expect(result.text).toBe('System 2 reasoning complete');
      expect(result.voice).toBe('Ethan');
      expect(result.method).toBe('voice_cloning'); // CosyVoice method
      expect(result.duration).toBeGreaterThan(0);
    });

    it('should use voice cloning when requested', async () => {
      const input = {
        text: 'Voice cloning test from System 2',
        voice: 'Serena',
        useVoiceCloning: true,
        voiceProfile: 'custom_profile',
        referenceAudio: 'base64audiodata...'
      };

      const result = await agentTTSTool.func(input, testConfig);

      expect(result.success).toBe(true);
      expect(result.method).toBe('voice_cloning');
    });

    it('should handle missing audio player', async () => {
      const configWithoutAudio = { ...testConfig, audioPlayer: null };
      
      const input = {
        text: 'Test without audio player'
      };

      const result = await agentTTSTool.func(input, configWithoutAudio);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Audio player not available');
    });

    it('should validate text parameter', async () => {
      const input = {
        // Missing text parameter
        voice: 'Serena'
      };

      const result = await agentTTSTool.func(input, testConfig);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Text parameter is required');
    });
  });

  describe('Voice Profile Tool', () => {
    it('should create voice profile', async () => {
      const input = {
        action: 'create',
        profileName: 'system2_voice',
        referenceAudio: 'base64audiodata...',
        metadata: {
          createdBy: 'system2',
          purpose: 'reasoning_responses'
        }
      };

      const result = await voiceProfileTool.func(input, testConfig);

      expect(result.success).toBe(true);
      expect(result.action).toBe('create');
      expect(result.profile.profileName).toBe('system2_voice');
      expect(result.profile.created).toBe(true);
    });

    it('should list available voices', async () => {
      const input = {
        action: 'list'
      };

      const result = await voiceProfileTool.func(input, testConfig);

      expect(result.success).toBe(true);
      expect(result.action).toBe('list');
      expect(result.voices).toHaveProperty('realtime');
      expect(result.voices).toHaveProperty('clone');
      expect(result.voices).toHaveProperty('profiles');
    });

    it('should validate required parameters for create action', async () => {
      const input = {
        action: 'create',
        profileName: 'test_profile'
        // Missing referenceAudio
      };

      const result = await voiceProfileTool.func(input, testConfig);

      expect(result.success).toBe(false);
      expect(result.error).toContain('referenceAudio are required');
    });
  });

  describe('Play Audio Tool', () => {
    it('should play base64 audio', async () => {
      const input = {
        audio: 'base64encodedaudiodata...',
        format: 'mp3'
      };

      const result = await playAudioTool.func(input);

      expect(result.success).toBe(true);
      expect(result.played).toBe(true);
      expect(result.format).toBe('mp3');
      expect(result.duration).toBe(1000);
    });

    it('should validate audio parameter', async () => {
      const input = {
        // Missing audio parameter
        format: 'wav'
      };

      const result = await playAudioTool.func(input);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Audio parameter is required');
    });

    it('should validate audio format', async () => {
      const input = {
        audio: 'base64audiodata...',
        format: 'unsupported_format'
      };

      const result = await playAudioTool.func(input);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Unsupported audio format');
    });
  });

  describe('Shared Speaking Service', () => {
    it('should provide singleton speaking service instance', () => {
      const service1 = getSharedSpeakingService();
      const service2 = getSharedSpeakingService();

      expect(service1).toBe(service2); // Same instance
      expect(service1).toBeDefined();
    });

    it('should configure shared service with options', () => {
      const options = {
        audioPlayer: mockAudioPlayer,
        aliyunModel: mockAliyunModel,
        sessionId: 'shared_test_session'
      };

      const service = getSharedSpeakingService(options);

      expect(service).toBeDefined();
      expect(service.audioPlayer).toBe(mockAudioPlayer);
      expect(service.aliyunModel).toBe(mockAliyunModel);
      expect(service.sessionId).toBe('shared_test_session');
    });
  });

  describe('System 2 Integration Scenarios', () => {
    it('should handle System 2 complex reasoning with speech output', async () => {
      // Simulate System 2 calling multiple tools in sequence
      
      // First call: Check avatar status
      const statusResult = await avatarSpeechControlTool.func(
        { action: 'check_status' },
        testConfig
      );
      expect(statusResult.success).toBe(true);

      // Second call: Speak reasoning result
      const speakResult = await avatarSpeechControlTool.func({
        action: 'speak',
        text: 'Based on my analysis, I recommend the following approach...',
        voice: 'Serena',
        options: { priority: 'high' }
      }, testConfig);
      expect(speakResult.success).toBe(true);

      // Verify state management coordination
      expect(mockAvatarController.stateManager.startSpeaking).toHaveBeenCalledWith('test_system2_session');
    });

    it('should handle System 2 proactive engagement with voice cloning', async () => {
      // Simulate proactive System 2 decision with custom voice
      
      const voiceResult = await voiceProfileTool.func({
        action: 'create',
        profileName: 'proactive_voice',
        referenceAudio: 'base64data...',
        metadata: { purpose: 'proactive_engagement' }
      }, testConfig);
      expect(voiceResult.success).toBe(true);

      const ttsResult = await agentTTSTool.func({
        text: 'I noticed you\'ve been quiet. Would you like to chat?',
        voice: 'Serena',
        useVoiceCloning: true,
        voiceProfile: 'proactive_voice'
      }, testConfig);
      expect(ttsResult.success).toBe(true);
    });

    it('should handle System 2 error recovery with fallback speech', async () => {
      // Simulate error in primary speech method with fallback
      
      // First, try with a malformed request
      const errorResult = await avatarSpeechControlTool.func({
        action: 'speak',
        text: '', // Empty text should cause error
        voice: 'Serena'
      }, testConfig);
      expect(errorResult.success).toBe(false);

      // Then fallback to simple audio playback
      const fallbackResult = await playAudioTool.func({
        audio: 'base64fallbackaudiodata...',
        format: 'wav'
      });
      expect(fallbackResult.success).toBe(true);
    });
  });
});