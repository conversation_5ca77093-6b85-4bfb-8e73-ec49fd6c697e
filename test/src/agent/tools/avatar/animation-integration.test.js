/**
 * System 2 Animation Tools Integration Tests
 * 
 * Tests that animation tools work correctly when called from System 2
 * through the enhanced dual-brain architecture with LangGraph semantic search
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  selectAnimationTool,
  listAnimationsTool,
  recommendAnimationsTool,
  stopAnimationTool,
  randomTalkingAnimationTool
} from '../../../../src/agent/tools/avatar/animation.js';

// Mock the logger
vi.mock('../../../../src/utils/logger.ts', () => ({
  createLogger: () => ({
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    setLogLevel: vi.fn()
  }),
  LogLevel: { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3 }
}));

// Mock AnimationConfig
vi.mock('../../../../src/animation/AnimationConfig.js', () => ({
  ANIMATION_REGISTRY: {
    byId: {
      'dance_happy': {
        id: 'dance_happy',
        name: 'Happy Dance',
        category: 'dance',
        subcategory: 'celebration',
        description: 'Joyful celebration dance',
        filename: 'dance_happy.fbx',
        loopable: true
      },
      'talking': {
        id: 'talking',
        name: 'Talking Gesture',
        category: 'communication',
        subcategory: 'speaking',
        description: 'Standard talking animation',
        filename: 'talking.fbx',
        loopable: true
      },
      'boxing_jab': {
        id: 'boxing_jab',
        name: 'Boxing Jab',
        category: 'combat',
        subcategory: 'striking',
        description: 'Quick boxing jab',
        filename: 'boxing_jab.fbx',
        loopable: false
      },
      'sitting_talking': {
        id: 'sitting_talking',
        name: 'Sitting Talk',
        category: 'communication',
        subcategory: 'speaking',
        description: 'Talking while sitting',
        filename: 'sitting_talking.fbx',
        loopable: true
      }
    },
    byCategory: {
      'dance': ['dance_happy'],
      'communication': ['talking', 'sitting_talking'],
      'combat': ['boxing_jab']
    },
    getById: vi.fn((id) => {
      const registry = {
        'dance_happy': {
          id: 'dance_happy',
          name: 'Happy Dance',
          category: 'dance',
          description: 'Joyful celebration dance',
          loopable: true
        },
        'talking': {
          id: 'talking',
          name: 'Talking Gesture',
          category: 'communication',
          description: 'Standard talking animation',
          loopable: true
        },
        'boxing_jab': {
          id: 'boxing_jab',
          name: 'Boxing Jab',
          category: 'combat',
          description: 'Quick boxing jab',
          loopable: false
        },
        'sitting_talking': {
          id: 'sitting_talking',
          name: 'Sitting Talk',
          category: 'communication',
          description: 'Talking while sitting',
          loopable: true
        }
      };
      return registry[id] || null;
    }),
    getByCategory: vi.fn((category) => {
      const categories = {
        'dance': [{ id: 'dance_happy', name: 'Happy Dance', category: 'dance' }],
        'communication': [
          { id: 'talking', name: 'Talking Gesture', category: 'communication' },
          { id: 'sitting_talking', name: 'Sitting Talk', category: 'communication' }
        ],
        'combat': [{ id: 'boxing_jab', name: 'Boxing Jab', category: 'combat' }]
      };
      return categories[category] || [];
    }),
    getAllAnimations: vi.fn(() => ({
      'dance_happy': { id: 'dance_happy', name: 'Happy Dance', category: 'dance' },
      'talking': { id: 'talking', name: 'Talking Gesture', category: 'communication' },
      'boxing_jab': { id: 'boxing_jab', name: 'Boxing Jab', category: 'combat' },
      'sitting_talking': { id: 'sitting_talking', name: 'Sitting Talk', category: 'communication' }
    })),
    initialize: vi.fn()
  }
}));

// Mock LangChain embeddings
vi.mock('langchain/vectorstores/memory', () => ({
  MemoryVectorStore: {
    fromTexts: vi.fn().mockResolvedValue({
      similaritySearchWithScore: vi.fn().mockResolvedValue([
        [
          { 
            metadata: {
              animationId: 'dance_happy',
              category: 'dance',
              description: 'Joyful celebration dance',
              semanticTags: ['happy', 'dance', 'celebration'],
              emotionalProfile: 'happiness',
              usageScenarios: ['celebration', 'positive_feedback']
            }
          },
          0.95
        ]
      ]),
      asRetriever: vi.fn().mockReturnValue({
        getRelevantDocuments: vi.fn().mockResolvedValue([])
      })
    })
  }
}));

describe('System 2 Animation Tools Integration', () => {
  let mockTalkingAvatar;
  let testOptions;

  beforeEach(() => {
    // Mock TalkingAvatar instance for stopAnimationTool
    mockTalkingAvatar = {
      currentTalkingAnimation: {
        file: 'current_animation.fbx'
      },
      animator: {
        stopAnimation: vi.fn(),
        mixer: {
          stopAllAction: vi.fn()
        }
      },
      talkingHead: {
        stopSpeaking: vi.fn()
      }
    };

    // Set global reference for stopAnimationTool
    globalThis.talkingAvatar = mockTalkingAvatar;

    // Clear last animation ID for repeat prevention tests
    globalThis.lastTalkingAnimationId = null;

    // Test options for LLM integration
    testOptions = {
      llm: {
        // Mock LLM for multi-query generation
        invoke: vi.fn().mockResolvedValue('alternative query 1\nalternative query 2\nalternative query 3')
      }
    };
  });

  afterEach(() => {
    vi.clearAllMocks();
    delete globalThis.talkingAvatar;
    delete globalThis.lastTalkingAnimationId;
  });

  describe('Select Animation Tool', () => {
    it('should select animation using LangGraph semantic search for System 2 reasoning', async () => {
      const input = {
        animationQuery: 'happy celebration dance',
        reasoningContext: 'User expressed joy, System 2 recommends celebratory response',
        category: 'dance'
      };

      const result = await selectAnimationTool.func(input, testOptions);

      expect(result.success).toBe(true);
      expect(result.animationId).toBe('dance_happy');
      expect(result.method).toBe('langgraph_search');
      expect(result.reasoning).toContain('System 2 recommends celebratory response');
      expect(result.searchScore).toBeGreaterThan(0.9);
      expect(result.enhancedMetadata).toHaveProperty('semanticTags');
      expect(result.alternatives).toBeDefined();
    });

    it('should fallback to keyword matching when semantic search fails', async () => {
      // Mock vector store to return empty results
      const { MemoryVectorStore } = await import('langchain/vectorstores/memory');
      MemoryVectorStore.fromTexts.mockResolvedValueOnce({
        similaritySearchWithScore: vi.fn().mockResolvedValue([])
      });

      const input = {
        animationQuery: 'boxing fight',
        reasoningContext: 'System 2 analysis suggests combat animation'
      };

      const result = await selectAnimationTool.func(input, testOptions);

      expect(result.success).toBe(true);
      expect(result.animationId).toBe('boxing_jab'); // Fallback keyword match
      expect(result.method).toBe('fallback_search');
    });

    it('should handle category-specific searches', async () => {
      const input = {
        animationQuery: 'speaking gesture',
        category: 'communication'
      };

      const result = await selectAnimationTool.func(input, testOptions);

      expect(result.success).toBe(true);
      expect(result.category).toBe('communication');
    });

    it('should return error when no animation found', async () => {
      // Mock empty registry
      const { ANIMATION_REGISTRY } = await import('../../../../src/animation/AnimationConfig.js');
      ANIMATION_REGISTRY.getById.mockReturnValue(null);

      // Mock vector store to return empty results
      const { MemoryVectorStore } = await import('langchain/vectorstores/memory');
      MemoryVectorStore.fromTexts.mockResolvedValueOnce({
        similaritySearchWithScore: vi.fn().mockResolvedValue([])
      });

      const input = {
        animationQuery: 'nonexistent animation'
      };

      const result = await selectAnimationTool.func(input, testOptions);

      expect(result.success).toBe(false);
      expect(result.error).toContain('No animation found');
      expect(result.suggestions).toBeDefined();
    });
  });

  describe('List Animations Tool', () => {
    it('should list all animations for System 2 analysis', async () => {
      const input = {
        includeDescriptions: true,
        limit: 10
      };

      const result = await listAnimationsTool.func(input);

      expect(result.success).toBe(true);
      expect(result.animations).toHaveLength(4);
      expect(result.totalCount).toBe(4);
      expect(result.availableCategories).toContain('dance');
      expect(result.availableCategories).toContain('communication');
      expect(result.animations[0]).toHaveProperty('description');
    });

    it('should list animations by category', async () => {
      const input = {
        category: 'communication',
        includeDescriptions: false
      };

      const result = await listAnimationsTool.func(input);

      expect(result.success).toBe(true);
      expect(result.category).toBe('communication');
      expect(result.animations.every(anim => anim.category === 'communication')).toBe(true);
    });

    it('should respect limit parameter', async () => {
      const input = {
        limit: 2
      };

      const result = await listAnimationsTool.func(input);

      expect(result.success).toBe(true);
      expect(result.animations).toHaveLength(2);
      expect(result.hasMore).toBe(true);
    });
  });

  describe('Recommend Animations Tool', () => {
    it('should provide contextual recommendations for System 2 decisions', async () => {
      const input = {
        userMood: 'excited',
        conversationContext: 'User just achieved a goal and is celebrating',
        userIntent: 'wants to see celebratory response',
        limit: 3
      };

      const result = await recommendAnimationsTool.func(input);

      expect(result.success).toBe(true);
      expect(result.recommendations).toHaveLength(1); // Based on mocked search
      expect(result.context.mood).toBe('excited');
      expect(result.searchQuery).toContain('excited');
      expect(result.recommendations[0]).toHaveProperty('contextualFit');
      expect(result.recommendations[0]).toHaveProperty('stateAlignment');
    });

    it('should extract animation keywords from conversation context', async () => {
      const input = {
        conversationContext: 'The user wants to learn martial arts and asked about fighting techniques',
        userIntent: 'combat training',
        limit: 2
      };

      const result = await recommendAnimationsTool.func(input);

      expect(result.success).toBe(true);
      expect(result.searchQuery).toContain('fight');
    });

    it('should handle empty context gracefully', async () => {
      const input = {
        limit: 1
      };

      const result = await recommendAnimationsTool.func(input);

      expect(result.success).toBe(true);
      expect(result.recommendations).toBeDefined();
    });
  });

  describe('Stop Animation Tool', () => {
    it('should stop current animations when called from System 2', async () => {
      const input = {
        stopType: 'current',
        reason: 'System 2 decided to interrupt for urgent response'
      };

      const result = await stopAnimationTool.func(input);

      expect(result.success).toBe(true);
      expect(result.stopType).toBe('current');
      expect(result.stoppedAnimations).toHaveLength(1);
      expect(result.reason).toContain('System 2 decided to interrupt');
      expect(mockTalkingAvatar.animator.stopAnimation).toHaveBeenCalled();
    });

    it('should stop all animations', async () => {
      const input = {
        stopType: 'all',
        reason: 'System 2 emergency stop'
      };

      const result = await stopAnimationTool.func(input);

      expect(result.success).toBe(true);
      expect(result.stopType).toBe('all');
      expect(mockTalkingAvatar.animator.stopAnimation).toHaveBeenCalled();
      expect(mockTalkingAvatar.talkingHead.stopSpeaking).toHaveBeenCalled();
    });

    it('should stop only talking animations', async () => {
      const input = {
        stopType: 'talking_only'
      };

      const result = await stopAnimationTool.func(input);

      expect(result.success).toBe(true);
      expect(result.stopType).toBe('talking_only');
    });

    it('should handle case when no animations are running', async () => {
      // Clear current animation
      mockTalkingAvatar.currentTalkingAnimation = null;
      
      const input = {
        stopType: 'current'
      };

      const result = await stopAnimationTool.func(input);

      expect(result.success).toBe(true);
      expect(result.stoppedAnimations).toHaveLength(0);
      expect(result.message).toContain('No animations were currently running');
    });

    it('should handle missing TalkingAvatar gracefully', async () => {
      delete globalThis.talkingAvatar;

      const input = {
        stopType: 'current'
      };

      const result = await stopAnimationTool.func(input);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Animation system not available');
    });
  });

  describe('Random Talking Animation Tool', () => {
    it('should select contextual talking animation for System 2 responses', async () => {
      const input = {
        mood: 'confident',
        intensity: 'medium',
        preventRepeats: true
      };

      const result = await randomTalkingAnimationTool.func(input);

      expect(result.success).toBe(true);
      expect(result.mood).toBe('confident');
      expect(result.intensity).toBe('medium');
      expect(result.method).toBe('random_talking_selection');
      expect(result.metadata.isTalkingAnimation).toBe(true);
      expect(result.metadata.isRandomSelection).toBe(true);
    });

    it('should prevent repeating the same animation', async () => {
      // Set a previous animation
      globalThis.lastTalkingAnimationId = 'talking';

      const input = {
        mood: 'neutral',
        preventRepeats: true
      };

      const result = await randomTalkingAnimationTool.func(input);

      expect(result.success).toBe(true);
      // Should not repeat the same animation
      expect(result.animationId).not.toBe('talking');
    });

    it('should fallback to hardcoded animations when search fails', async () => {
      // Mock empty search results
      const { MemoryVectorStore } = await import('langchain/vectorstores/memory');
      MemoryVectorStore.fromTexts.mockResolvedValueOnce({
        similaritySearchWithScore: vi.fn().mockResolvedValue([])
      });

      const input = {
        mood: 'calm',
        intensity: 'low'
      };

      const result = await randomTalkingAnimationTool.func(input);

      expect(result.success).toBe(true);
      expect(result.method).toBe('fallback_talking_selection');
      expect(result.metadata.isFallback).toBe(true);
    });

    it('should handle different mood contexts', async () => {
      const moodTests = [
        { mood: 'excited', intensity: 'high' },
        { mood: 'serious', intensity: 'low' },
        { mood: 'playful', intensity: 'medium' }
      ];

      for (const test of moodTests) {
        const result = await randomTalkingAnimationTool.func(test);
        expect(result.success).toBe(true);
        expect(result.mood).toBe(test.mood);
        expect(result.intensity).toBe(test.intensity);
      }
    });
  });

  describe('System 2 Integration Scenarios', () => {
    it('should handle complex System 2 reasoning with animation selection', async () => {
      // Simulate System 2 complex decision making
      
      // Step 1: System 2 lists available animations
      const listResult = await listAnimationsTool.func({
        category: 'dance',
        includeDescriptions: true
      });
      expect(listResult.success).toBe(true);

      // Step 2: System 2 selects specific animation based on context
      const selectResult = await selectAnimationTool.func({
        animationQuery: 'celebratory dance for achievement',
        reasoningContext: 'System 2 analysis: User achieved milestone, celebratory response appropriate'
      }, testOptions);
      expect(selectResult.success).toBe(true);

      // Step 3: System 2 decides to stop any current animations first
      const stopResult = await stopAnimationTool.func({
        stopType: 'current',
        reason: 'System 2 clearing stage for new celebration'
      });
      expect(stopResult.success).toBe(true);
    });

    it('should handle System 2 proactive engagement scenario', async () => {
      // Simulate proactive System 2 decision during quiet period
      
      // Step 1: Get recommendations based on context
      const recommendResult = await recommendAnimationsTool.func({
        userMood: 'neutral',
        conversationContext: 'Long period of silence',
        userIntent: 'may need engagement',
        limit: 3
      });
      expect(recommendResult.success).toBe(true);

      // Step 2: Select a talking animation for engagement
      const talkingResult = await randomTalkingAnimationTool.func({
        mood: 'friendly',
        intensity: 'low',
        preventRepeats: true
      });
      expect(talkingResult.success).toBe(true);
      expect(talkingResult.metadata.isTalkingAnimation).toBe(true);
    });

    it('should handle System 2 error recovery with animation fallback', async () => {
      // Simulate error in primary animation selection with fallback
      
      // Try to select non-existent animation
      const errorResult = await selectAnimationTool.func({
        animationQuery: 'completely_nonexistent_animation'
      }, testOptions);

      if (!errorResult.success) {
        // Fallback to random talking animation
        const fallbackResult = await randomTalkingAnimationTool.func({
          mood: 'neutral',
          intensity: 'medium'
        });
        expect(fallbackResult.success).toBe(true);
      }
    });

    it('should validate System 2 animation coordination with speaking', async () => {
      // Test coordination between animation and speaking tools
      
      // Select animation first
      const animationResult = await selectAnimationTool.func({
        animationQuery: 'talking gesture',
        reasoningContext: 'System 2 preparing for speech output'
      }, testOptions);
      expect(animationResult.success).toBe(true);

      // Verify it's a compatible talking animation
      expect(animationResult.category).toBe('communication');
      
      // This would typically be followed by a speaking tool call
      // (tested in system2-speaking-integration.test.js)
    });
  });
});