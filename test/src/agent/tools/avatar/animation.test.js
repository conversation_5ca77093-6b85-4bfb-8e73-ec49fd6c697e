/**
 * Animation Tools Integration Tests
 * Tests the LangGraphAnimationRetriever and animation tools with semantic search
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
    selectAnimationTool,
    listAnimationsTool,
    recommendAnimationsTool,
    createAnimationToolNode,
    createUnifiedToolNode
} from '@/agent/tools/avatar/animation.js';

// Mock ANIMATION_REGISTRY
const mockAnimationRegistry = {
    'wave_greeting': {
        id: 'wave_greeting',
        name: 'Wave Greeting',
        description: 'A friendly waving gesture for greetings',
        duration: 2000,
        category: 'greeting',
        tags: ['friendly', 'welcoming', 'hello'],
        emotionalProfile: {
            mood: 'positive',
            energy: 'medium',
            formality: 'casual'
        }
    },
    'happy_dance': {
        id: 'happy_dance',
        name: 'Happy Dance',
        description: 'An energetic dancing movement expressing joy',
        duration: 4000,
        category: 'dance',
        tags: ['joyful', 'energetic', 'celebration'],
        emotionalProfile: {
            mood: 'very_positive',
            energy: 'high',
            formality: 'casual'
        }
    },
    'thinking_pose': {
        id: 'thinking_pose',
        name: 'Thinking Pose',
        description: 'A contemplative pose indicating deep thought',
        duration: 3000,
        category: 'emotion',
        tags: ['contemplative', 'focused', 'intellectual'],
        emotionalProfile: {
            mood: 'neutral',
            energy: 'low',
            formality: 'professional'
        }
    },
    'martial_arts_kick': {
        id: 'martial_arts_kick',
        name: 'Martial Arts Kick',
        description: 'A powerful martial arts kicking motion',
        duration: 2500,
        category: 'action',
        tags: ['powerful', 'martial', 'action', 'kick'],
        emotionalProfile: {
            mood: 'determined',
            energy: 'very_high',
            formality: 'neutral'
        }
    },
    'sad_expression': {
        id: 'sad_expression',
        name: 'Sad Expression',
        description: 'A melancholic facial expression and posture',
        duration: 2000,
        category: 'emotion',
        tags: ['sad', 'melancholic', 'emotional'],
        emotionalProfile: {
            mood: 'negative',
            energy: 'low',
            formality: 'neutral'
        }
    }
};

// Mock the animation registry import
vi.mock('@/animation/AnimationConfig.js', () => ({
    ANIMATION_REGISTRY: {
        'wave_greeting': {
            id: 'wave_greeting',
            name: 'Wave Greeting',
            description: 'A friendly waving gesture for greetings',
            duration: 2000,
            category: 'greeting',
            tags: ['friendly', 'welcoming', 'hello'],
            emotionalProfile: {
                mood: 'positive',
                energy: 'medium',
                formality: 'casual'
            }
        },
        'happy_dance': {
            id: 'happy_dance',
            name: 'Happy Dance',
            description: 'An energetic dancing movement expressing joy',
            duration: 4000,
            category: 'dance',
            tags: ['joyful', 'energetic', 'celebration'],
            emotionalProfile: {
                mood: 'very_positive',
                energy: 'high',
                formality: 'casual'
            }
        },
        'thinking_pose': {
            id: 'thinking_pose',
            name: 'Thinking Pose',
            description: 'A contemplative pose indicating deep thought',
            duration: 3000,
            category: 'emotion',
            tags: ['contemplative', 'focused', 'intellectual'],
            emotionalProfile: {
                mood: 'neutral',
                energy: 'low',
                formality: 'professional'
            }
        },
        'martial_arts_kick': {
            id: 'martial_arts_kick',
            name: 'Martial Arts Kick',
            description: 'A powerful martial arts kicking motion',
            duration: 2500,
            category: 'action',
            tags: ['powerful', 'martial', 'action', 'kick'],
            emotionalProfile: {
                mood: 'determined',
                energy: 'very_high',
                formality: 'neutral'
            }
        },
        'sad_expression': {
            id: 'sad_expression',
            name: 'Sad Expression',
            description: 'A melancholic facial expression and posture',
            duration: 2000,
            category: 'emotion',
            tags: ['sad', 'melancholic', 'emotional'],
            emotionalProfile: {
                mood: 'negative',
                energy: 'low',
                formality: 'neutral'
            }
        }
    }
}));

// Mock logger
vi.mock('@/utils/logger.js', () => ({
    createLogger: vi.fn(() => ({
        info: vi.fn(),
        debug: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        setLogLevel: vi.fn()
    }))
}));

// Mock LangChain components
vi.mock('langchain/vectorstores/memory', () => ({
    MemoryVectorStore: vi.fn().mockImplementation(() => ({
        addDocuments: vi.fn().mockResolvedValue(undefined),
        similaritySearch: vi.fn().mockResolvedValue([
            {
                pageContent: JSON.stringify(mockAnimationRegistry.happy_dance),
                metadata: { animationId: 'happy_dance' }
            }
        ])
    }))
}));

vi.mock('langchain/retrievers/multi_query', () => ({
    MultiQueryRetriever: vi.fn().mockImplementation(() => ({
        getRelevantDocuments: vi.fn().mockResolvedValue([
            {
                pageContent: JSON.stringify(mockAnimationRegistry.happy_dance),
                metadata: { animationId: 'happy_dance' }
            }
        ])
    }))
}));

describe('Animation Tools', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe('selectAnimationTool', () => {
        it('should select animation based on query', async () => {
            const result = await selectAnimationTool.invoke({
                animationQuery: 'happy dancing movement',
                reasoningContext: 'User wants to celebrate'
            });

            expect(result.success).toBe(true);
            expect(result.selectedAnimation).toBeDefined();
            expect(result.selectedAnimation.id).toBe('happy_dance');
            expect(result.contextualFit).toBeGreaterThan(0);
            expect(result.enhancedMetadata).toBeDefined();
            expect(result.enhancedMetadata.semanticTags).toBeDefined();
        });

        it('should provide fallback when query has no matches', async () => {
            const result = await selectAnimationTool.invoke({
                animationQuery: 'nonexistent impossible animation',
                reasoningContext: 'Testing fallback'
            });

            expect(result.success).toBe(true);
            expect(result.selectedAnimation).toBeDefined();
            expect(result.method).toBe('fallback_selection');
        });

        it('should filter by category when specified', async () => {
            const result = await selectAnimationTool.invoke({
                animationQuery: 'movement',
                category: 'greeting'
            });

            expect(result.success).toBe(true);
            expect(result.selectedAnimation).toBeDefined();
            // Should prioritize animations from the greeting category
        });

        it('should validate input parameters', async () => {
            const result = await selectAnimationTool.invoke({
                animationQuery: '',
                reasoningContext: 'Empty query test'
            });

            expect(result.success).toBe(false);
            expect(result.error).toContain('Animation query is required');
        });
    });

    describe('listAnimationsTool', () => {
        it('should list all animations', async () => {
            const result = await listAnimationsTool.invoke({});

            expect(result.success).toBe(true);
            expect(result.animations).toBeDefined();
            expect(Array.isArray(result.animations)).toBe(true);
            expect(result.animations.length).toBe(5);
            expect(result.totalCount).toBe(5);
        });

        it('should filter by category', async () => {
            const result = await listAnimationsTool.invoke({
                category: 'emotion'
            });

            expect(result.success).toBe(true);
            expect(result.animations).toBeDefined();
            expect(result.animations.length).toBe(2); // thinking_pose and sad_expression
            expect(result.filteredBy).toBe('emotion');
        });

        it('should limit results when specified', async () => {
            const result = await listAnimationsTool.invoke({
                limit: 2
            });

            expect(result.success).toBe(true);
            expect(result.animations.length).toBe(2);
            expect(result.hasMore).toBe(true);
        });

        it('should include descriptions when requested', async () => {
            const result = await listAnimationsTool.invoke({
                includeDescriptions: true,
                limit: 1
            });

            expect(result.success).toBe(true);
            expect(result.animations[0].description).toBeDefined();
            expect(result.animations[0].emotionalProfile).toBeDefined();
        });
    });

    describe('recommendAnimationsTool', () => {
        it('should recommend animations based on mood', async () => {
            const result = await recommendAnimationsTool.invoke({
                userMood: 'excited',
                conversationContext: 'User just achieved something great',
                userIntent: 'celebration'
            });

            expect(result.success).toBe(true);
            expect(result.recommendations).toBeDefined();
            expect(Array.isArray(result.recommendations)).toBe(true);
            expect(result.recommendations.length).toBeGreaterThan(0);
            expect(result.analysisReason).toBeDefined();
        });

        it('should handle different mood contexts', async () => {
            const result = await recommendAnimationsTool.invoke({
                userMood: 'contemplative',
                conversationContext: 'Deep philosophical discussion',
                userIntent: 'thinking'
            });

            expect(result.success).toBe(true);
            expect(result.recommendations).toBeDefined();
            // Should recommend thinking or contemplative animations
        });

        it('should limit recommendations when specified', async () => {
            const result = await recommendAnimationsTool.invoke({
                userMood: 'happy',
                limit: 2
            });

            expect(result.success).toBe(true);
            expect(result.recommendations.length).toBeLessThanOrEqual(2);
        });

        it('should provide analysis reasoning', async () => {
            const result = await recommendAnimationsTool.invoke({
                userMood: 'energetic',
                conversationContext: 'Party planning discussion'
            });

            expect(result.success).toBe(true);
            expect(result.analysisReason).toBeDefined();
            expect(typeof result.analysisReason).toBe('string');
            expect(result.analysisReason.length).toBeGreaterThan(0);
        });
    });

    describe('Enhanced Metadata and Semantic Analysis', () => {
        it('should provide enhanced metadata for selected animations', async () => {
            const result = await selectAnimationTool.invoke({
                animationQuery: 'martial arts action',
                reasoningContext: 'User wants dynamic movement'
            });

            expect(result.success).toBe(true);
            expect(result.enhancedMetadata).toBeDefined();
            expect(result.enhancedMetadata.semanticTags).toBeDefined();
            expect(result.enhancedMetadata.emotionalProfile).toBeDefined();
            expect(result.enhancedMetadata.usageScenarios).toBeDefined();
            expect(result.enhancedMetadata.movementCharacteristics).toBeDefined();
        });

        it('should calculate contextual fit scores', async () => {
            const result = await selectAnimationTool.invoke({
                animationQuery: 'greeting gesture',
                reasoningContext: 'User wants to welcome someone'
            });

            expect(result.success).toBe(true);
            expect(result.contextualFit).toBeDefined();
            expect(typeof result.contextualFit).toBe('number');
            expect(result.contextualFit).toBeGreaterThanOrEqual(0);
            expect(result.contextualFit).toBeLessThanOrEqual(1);
        });
    });

    describe('State Management and Learning', () => {
        it('should track query history', async () => {
            // Make multiple queries
            await selectAnimationTool.invoke({
                animationQuery: 'happy movement',
                reasoningContext: 'First query'
            });

            await selectAnimationTool.invoke({
                animationQuery: 'dance celebration',
                reasoningContext: 'Second query'
            });

            const result = await selectAnimationTool.invoke({
                animationQuery: 'joyful expression',
                reasoningContext: 'Third query'
            });

            expect(result.success).toBe(true);
            // State management should influence scoring based on previous queries
        });

        it('should adapt to user preferences over time', async () => {
            // Simulate preference for action animations
            await selectAnimationTool.invoke({
                animationQuery: 'action movement',
                category: 'action'
            });

            await selectAnimationTool.invoke({
                animationQuery: 'dynamic gesture',
                category: 'action'
            });

            const result = await selectAnimationTool.invoke({
                animationQuery: 'energetic movement'
            });

            expect(result.success).toBe(true);
            expect(result.stateAlignment).toBeDefined();
            // Should show influence of previous action category preferences
        });
    });

    describe('Error Handling and Fallbacks', () => {
        it('should handle empty animation registry gracefully', async () => {
            // Mock empty registry
            vi.doMock('../../../../src/agent/animation/AnimationConfig.js', () => ({
                ANIMATION_REGISTRY: {}
            }));

            const result = await listAnimationsTool.invoke({});

            expect(result.success).toBe(true);
            expect(result.animations).toBeDefined();
            expect(Array.isArray(result.animations)).toBe(true);
        });

        it('should provide meaningful error messages', async () => {
            const result = await selectAnimationTool.invoke({
                animationQuery: null,
                reasoningContext: 'Null query test'
            });

            expect(result.success).toBe(false);
            expect(result.error).toBeDefined();
            expect(typeof result.error).toBe('string');
        });
    });

    describe('Tool Node Factories', () => {
        it('should create animation tool node', () => {
            const animationToolNode = createAnimationToolNode({
                validateInput: true,
                validateOutput: true
            });

            expect(animationToolNode).toBeDefined();
            expect(animationToolNode.options.validateInput).toBe(true);
            expect(animationToolNode.options.validateOutput).toBe(true);
        });

        it('should create unified tool node', () => {
            const unifiedToolNode = createUnifiedToolNode({
                loggerName: 'UnifiedAnimationTest',
                handleErrors: true
            });

            expect(unifiedToolNode).toBeDefined();
            expect(unifiedToolNode.options.handleErrors).toBe(true);
        });
    });

    describe('Performance and Optimization', () => {
        it('should handle large query volumes efficiently', async () => {
            const startTime = Date.now();
            
            const promises = Array.from({ length: 10 }, (_, i) => 
                selectAnimationTool.invoke({
                    animationQuery: `test query ${i}`,
                    reasoningContext: `Performance test ${i}`
                })
            );

            const results = await Promise.all(promises);
            const endTime = Date.now();
            const executionTime = endTime - startTime;

            expect(results).toHaveLength(10);
            expect(results.every(r => r.success)).toBe(true);
            expect(executionTime).toBeLessThan(5000); // Should complete within 5 seconds
        });

        it('should cache repeated queries efficiently', async () => {
            const query = {
                animationQuery: 'cached test query',
                reasoningContext: 'Cache performance test'
            };

            // First execution
            const startTime1 = Date.now();
            const result1 = await selectAnimationTool.invoke(query);
            const executionTime1 = Date.now() - startTime1;

            // Second execution (should be faster due to caching)
            const startTime2 = Date.now();
            const result2 = await selectAnimationTool.invoke(query);
            const executionTime2 = Date.now() - startTime2;

            expect(result1.success).toBe(true);
            expect(result2.success).toBe(true);
            expect(result1.selectedAnimation.id).toBe(result2.selectedAnimation.id);
            // Second execution should be significantly faster
        });
    });
});