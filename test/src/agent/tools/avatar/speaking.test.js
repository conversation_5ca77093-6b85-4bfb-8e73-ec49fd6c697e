/**
 * Speaking Tools Integration Tests
 * Tests the AgentSpeakingService and related speaking tools
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
    AgentSpeakingService,
    getSharedSpeakingService,
    agentTTSTool,
    avatarSpeechControlTool,
    voiceProfileTool,
    playAudioTool,
    llmAudioHandlerTool,
    createSpeakingToolNode
} from '@/agent/tools/avatar/speaking.js';

// Mock environment variables
vi.mock('@/config/env.ts', () => ({
    getEnvVar: vi.fn((key, defaultValue) => {
        if (key === 'VITE_DASHSCOPE_API_KEY') {
            return 'test_api_key_123';
        }
        return defaultValue;
    })
}));

// Mock audio utilities
vi.mock('@/media/modality/audio.ts', () => ({
    playBase64Audio: vi.fn().mockResolvedValue({ 
        success: true, 
        duration: 2.5 
    }),
    AudioProcessor: vi.fn().mockImplementation(() => ({
        processAndPlay: vi.fn().mockResolvedValue({
            audioPlayed: true,
            audioLength: 1024,
            duration: 2.5
        })
    }))
}));

// Mock fetch for API calls
global.fetch = vi.fn();

describe('AgentSpeakingService', () => {
    let speakingService;
    let mockAudioPlayer;
    let mockAliyunModel;

    beforeEach(() => {
        mockAudioPlayer = {
            playAudioChunk: vi.fn().mockResolvedValue(true),
            isPlaying: false
        };

        mockAliyunModel = {
            isRealtimeModeActive: vi.fn().mockReturnValue(false),
            updateRealtimeSession: vi.fn().mockResolvedValue(true),
            sendRealtimeText: vi.fn().mockResolvedValue(true)
        };

        speakingService = new AgentSpeakingService({
            audioPlayer: mockAudioPlayer,
            aliyunModel: mockAliyunModel,
            sessionId: 'test_session'
        });

        // Reset fetch mock
        global.fetch.mockReset();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('Service Initialization', () => {
        it('should initialize with correct configuration', () => {
            expect(speakingService).toBeDefined();
            expect(speakingService.apiKey).toBe('test_api_key_123');
            expect(speakingService.audioPlayer).toBe(mockAudioPlayer);
            expect(speakingService.aliyunModel).toBe(mockAliyunModel);
            expect(speakingService.sessionId).toBe('test_session');
        });

        it('should have supported voices configured', () => {
            const voices = speakingService.getAvailableVoices();
            
            expect(voices.realtime).toContain('Chelsie');
            expect(voices.realtime).toContain('Serena');
            expect(voices.realtime).toContain('Ethan');
            expect(voices.realtime).toContain('Cherry');
            
            expect(voices.clone).toContain('loongstella');
            expect(voices.clone).toContain('longxiaocheng');
            expect(voices.clone).toContain('longyue');
            expect(voices.clone).toContain('longxiaobai');
        });
    });

    describe('Shared Service Pattern', () => {
        it('should return same instance from getSharedSpeakingService', () => {
            const service1 = getSharedSpeakingService();
            const service2 = getSharedSpeakingService();
            
            expect(service1).toBe(service2);
        });
    });

    describe('Speech Methods', () => {
        it('should use realtime model when active', async () => {
            mockAliyunModel.isRealtimeModeActive.mockReturnValue(true);
            
            const result = await speakingService.speak('Hello world', {
                voice: 'Serena'
            });

            expect(result.success).toBe(true);
            expect(result.method).toBe('realtime');
            expect(mockAliyunModel.updateRealtimeSession).toHaveBeenCalledWith({ voice: 'Serena' });
            expect(mockAliyunModel.sendRealtimeText).toHaveBeenCalledWith('Hello world');
        });

        it('should use CosyVoice when realtime is not active', async () => {
            // Mock successful API response
            global.fetch.mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({
                    output: {
                        audio: 'base64audiodata'
                    }
                })
            });

            const result = await speakingService.speak('Hello world', {
                voice: 'Serena',
                streaming: false
            });

            expect(result.success).toBe(true);
            expect(result.method).toBe('voice_cloning');
            expect(global.fetch).toHaveBeenCalledWith(
                expect.stringContaining('dashscope.aliyuncs.com'),
                expect.objectContaining({
                    method: 'POST',
                    headers: expect.objectContaining({
                        'Authorization': 'Bearer test_api_key_123',
                        'Content-Type': 'application/json'
                    })
                })
            );
        });

        it('should handle voice cloning with profile', async () => {
            global.fetch.mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({
                    output: { audio: 'base64audiodata' }
                })
            });

            const result = await speakingService.speak('Hello world', {
                useVoiceCloning: true,
                voiceProfile: 'custom_profile',
                referenceAudio: 'base64referenceaudio'
            });

            expect(result.success).toBe(true);
            expect(result.method).toBe('voice_cloning');
            
            const fetchCall = global.fetch.mock.calls[0];
            const requestBody = JSON.parse(fetchCall[1].body);
            expect(requestBody.parameters.voice_clone).toBeDefined();
            expect(requestBody.parameters.voice_clone.voice_profile).toBe('custom_profile');
        });
    });

    describe('Voice Profile Management', () => {
        it('should create voice profile', async () => {
            const result = await speakingService.createVoiceProfile(
                'test_profile',
                'base64referenceaudio',
                { description: 'Test profile' }
            );

            expect(result.success).toBe(true);
            expect(result.profileName).toBe('test_profile');
            expect(result.created).toBe(true);
            
            const voices = speakingService.getAvailableVoices();
            expect(voices.profiles).toContain('test_profile');
        });
    });

    describe('Avatar Speech Control', () => {
        it('should control avatar speech', async () => {
            const mockAvatarController = {
                stateManager: {
                    startSpeaking: vi.fn().mockResolvedValue(true),
                    stopSpeaking: vi.fn().mockResolvedValue(true),
                    getState: vi.fn().mockReturnValue({
                        isSpeaking: false,
                        isListening: true,
                        current: 'listening'
                    })
                }
            };

            global.fetch.mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({
                    output: { audio: 'base64audiodata' }
                })
            });

            const result = await speakingService.controlAvatarSpeech({
                action: 'speak',
                text: 'Hello avatar',
                voice: 'Serena'
            }, {
                avatarController: mockAvatarController,
                sessionId: 'avatar_test'
            });

            expect(result.success).toBe(true);
            expect(result.action).toBe('speak');
            expect(result.speaking).toBe(true);
            expect(mockAvatarController.stateManager.startSpeaking).toHaveBeenCalledWith('avatar_test');
        });

        it('should check avatar status', async () => {
            const mockAvatarController = {
                stateManager: {
                    getState: vi.fn().mockReturnValue({
                        isSpeaking: true,
                        isListening: false,
                        current: 'speaking'
                    })
                }
            };

            const result = await speakingService.controlAvatarSpeech({
                action: 'check_status'
            }, {
                avatarController: mockAvatarController
            });

            expect(result.success).toBe(true);
            expect(result.action).toBe('check_status');
            expect(result.speaking).toBe(true);
            expect(result.listening).toBe(false);
            expect(result.currentState).toBe('speaking');
        });
    });
});

describe('Speaking Tools', () => {
    let mockConfig;

    beforeEach(() => {
        mockConfig = {
            audioPlayer: {
                playAudioChunk: vi.fn().mockResolvedValue(true)
            },
            aliyunModel: {
                isRealtimeModeActive: vi.fn().mockReturnValue(false)
            },
            sessionId: 'tool_test_session',
            avatarController: {
                stateManager: {
                    startSpeaking: vi.fn().mockResolvedValue(true),
                    getState: vi.fn().mockReturnValue({
                        isSpeaking: false,
                        isListening: true
                    })
                }
            }
        };

        global.fetch.mockResolvedValue({
            ok: true,
            json: () => Promise.resolve({
                output: { audio: 'base64audiodata' }
            })
        });
    });

    describe('agentTTSTool', () => {
        it('should execute text-to-speech conversion', async () => {
            const result = await agentTTSTool.invoke({
                text: 'Hello from TTS tool',
                voice: 'Serena'
            }, mockConfig);

            expect(result.success).toBe(true);
            expect(result.text).toBe('Hello from TTS tool');
            expect(result.voice).toBe('Serena');
            expect(result.method).toBeDefined();
        });

        it('should validate input parameters', async () => {
            const result = await agentTTSTool.invoke({
                text: '',
                voice: 'Serena'
            }, mockConfig);

            expect(result.success).toBe(false);
            expect(result.error).toContain('non-empty string');
        });
    });

    describe('avatarSpeechControlTool', () => {
        it('should control avatar speech actions', async () => {
            const result = await avatarSpeechControlTool.invoke({
                action: 'speak',
                text: 'Avatar test message',
                voice: 'Ethan'
            }, mockConfig);

            expect(result.success).toBe(true);
            expect(result.action).toBe('speak');
            expect(result.speaking).toBe(true);
        });

        it('should handle stop speaking action', async () => {
            mockConfig.avatarController.stateManager.stopSpeaking = vi.fn().mockResolvedValue(true);

            const result = await avatarSpeechControlTool.invoke({
                action: 'stop_speaking'
            }, mockConfig);

            expect(result.success).toBe(true);
            expect(result.action).toBe('stop_speaking');
            expect(result.speaking).toBe(false);
        });
    });

    describe('voiceProfileTool', () => {
        it('should create voice profile', async () => {
            const result = await voiceProfileTool.invoke({
                action: 'create',
                profileName: 'test_profile',
                referenceAudio: 'base64referenceaudio',
                metadata: { description: 'Test profile' }
            }, mockConfig);

            expect(result.success).toBe(true);
            expect(result.action).toBe('create');
            expect(result.profile.profileName).toBe('test_profile');
        });

        it('should list available voices', async () => {
            const result = await voiceProfileTool.invoke({
                action: 'list'
            }, mockConfig);

            expect(result.success).toBe(true);
            expect(result.action).toBe('list');
            expect(result.voices).toBeDefined();
            expect(result.voices.realtime).toBeDefined();
            expect(result.voices.clone).toBeDefined();
        });
    });

    describe('playAudioTool', () => {
        it('should play base64 audio', async () => {
            const result = await playAudioTool.invoke({
                audio: 'base64audiodata',
                format: 'wav'
            });

            expect(result.success).toBe(true);
            expect(result.played).toBe(true);
            expect(result.format).toBe('wav');
        });

        it('should validate audio parameter', async () => {
            const result = await playAudioTool.invoke({
                audio: '',
                format: 'wav'
            });

            expect(result.success).toBe(false);
            expect(result.error).toContain('non-empty base64 string');
        });
    });

    describe('llmAudioHandlerTool', () => {
        it('should process LLM response with audio', async () => {
            const mockLLMResponse = {
                additional_kwargs: {
                    audio: {
                        data: 'base64audiodata'
                    }
                }
            };

            const result = await llmAudioHandlerTool.invoke({
                response: mockLLMResponse
            });

            expect(result.success).toBe(true);
            expect(result.audioFound).toBe(true);
        });

        it('should handle response without audio', async () => {
            const mockLLMResponse = {
                content: 'Text only response'
            };

            const result = await llmAudioHandlerTool.invoke({
                response: mockLLMResponse
            });

            expect(result.success).toBe(true);
            expect(result.audioFound).toBe(false);
        });
    });
});

describe('Speaking Tool Node Factory', () => {
    it('should create speaking tool node with enhanced features', () => {
        const speakingToolNode = createSpeakingToolNode({
            validateInput: true,
            validateOutput: true,
            handleErrors: true
        });

        expect(speakingToolNode).toBeDefined();
        expect(speakingToolNode.options.validateInput).toBe(true);
        expect(speakingToolNode.options.validateOutput).toBe(true);
        expect(speakingToolNode.options.handleErrors).toBe(true);
    });
});