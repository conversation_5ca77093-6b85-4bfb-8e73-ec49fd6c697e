/**
 * Web Search Tool Tests
 * Test-driven development for web search functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { WebSearchTool, webSearchTool, createWebSearchToolNode } from '../../../../src/agent/tools/search/WebSearchTool.js';

describe('WebSearchTool', () => {
    let searchTool;
    let mockWebFetch;

    beforeEach(() => {
        // Mock WebFetch function
        mockWebFetch = vi.fn();
        searchTool = new WebSearchTool({
            webFetch: mockWebFetch
        });
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('Basic Search Functionality', () => {
        it('should perform a basic web search', async () => {
            const mockResult = {
                success: true,
                data: {
                    title: 'Test Title',
                    content: 'Test content about the search query',
                    url: 'https://example.com'
                }
            };

            mockWebFetch.mockResolvedValue(mockResult);

            const result = await searchTool.execute({
                query: 'test search query',
                maxResults: 5
            });

            expect(mockWebFetch).toHaveBeenCalledWith({
                url: expect.stringContaining('test search query'),
                prompt: expect.stringContaining('search')
            });
            expect(result.success).toBe(true);
            expect(result.results).toBeDefined();
        });

        it('should handle search errors gracefully', async () => {
            mockWebFetch.mockRejectedValue(new Error('Network error'));

            const result = await searchTool.execute({
                query: 'test query'
            });

            expect(result.success).toBe(false);
            expect(result.error).toContain('Network error');
        });

        it('should validate search parameters', async () => {
            const result = await searchTool.execute({
                // Missing required query parameter
                maxResults: 5
            });

            expect(result.success).toBe(false);
            expect(result.error).toContain('query is required');
        });
    });

    describe('Anime Character Search', () => {
        it('should search for One Piece characters', async () => {
            const mockResult = {
                success: true,
                data: {
                    title: 'Monkey D. Luffy - One Piece Wiki',
                    content: 'Monkey D. Luffy is the captain of the Straw Hat Pirates...',
                    url: 'https://onepiece.fandom.com/wiki/Monkey_D._Luffy'
                }
            };

            mockWebFetch.mockResolvedValue(mockResult);

            const result = await searchTool.execute({
                query: 'Monkey D. Luffy One Piece character',
                searchType: 'anime_character',
                anime: 'One Piece'
            });

            expect(result.success).toBe(true);
            expect(result.character).toBeDefined();
            expect(result.character.name).toContain('Luffy');
            expect(result.character.anime).toBe('One Piece');
        });

        it('should search for One-Punch Man characters', async () => {
            const mockResult = {
                success: true,
                data: {
                    title: 'Saitama - One-Punch Man Wiki',
                    content: 'Saitama is the main protagonist of the One-Punch Man series...',
                    url: 'https://onepunchman.fandom.com/wiki/Saitama'
                }
            };

            mockWebFetch.mockResolvedValue(mockResult);

            const result = await searchTool.execute({
                query: 'Saitama One-Punch Man character',
                searchType: 'anime_character',
                anime: 'One-Punch Man'
            });

            expect(result.success).toBe(true);
            expect(result.character.name).toContain('Saitama');
            expect(result.character.anime).toBe('One-Punch Man');
        });

        it('should extract character personality traits', async () => {
            const mockResult = {
                success: true,
                data: {
                    title: 'Roronoa Zoro - One Piece Wiki',
                    content: 'Roronoa Zoro is a swordsman and the first member to join the Straw Hat Pirates. He is serious, stoic, and dedicated to his training...',
                    url: 'https://onepiece.fandom.com/wiki/Roronoa_Zoro'
                }
            };

            mockWebFetch.mockResolvedValue(mockResult);

            const result = await searchTool.execute({
                query: 'Roronoa Zoro personality traits',
                searchType: 'anime_character',
                extractPersonality: true
            });

            expect(result.success).toBe(true);
            expect(result.character.personality).toBeDefined();
            expect(result.character.personality.traits).toContain('serious');
        });
    });

    describe('Character Data Structuring', () => {
        it('should structure character data according to CharacterService format', async () => {
            const mockResult = {
                success: true,
                data: {
                    title: 'Test Character',
                    content: 'A test character who is friendly and enthusiastic...'
                }
            };

            mockWebFetch.mockResolvedValue(mockResult);

            const result = await searchTool.execute({
                query: 'test character',
                searchType: 'anime_character',
                structureForService: true
            });

            expect(result.success).toBe(true);
            expect(result.character).toHaveProperty('id');
            expect(result.character).toHaveProperty('name');
            expect(result.character).toHaveProperty('description');
            expect(result.character).toHaveProperty('personality');
            expect(result.character).toHaveProperty('voiceStyle');
            expect(result.character).toHaveProperty('systemPrompt');
            expect(result.character).toHaveProperty('avatar');
        });

        it('should generate personality metrics (0-1 scale)', async () => {
            const result = await searchTool.execute({
                query: 'test character',
                searchType: 'anime_character',
                structureForService: true
            });

            if (result.success && result.character.personality) {
                expect(result.character.personality.formality).toBeGreaterThanOrEqual(0);
                expect(result.character.personality.formality).toBeLessThanOrEqual(1);
                expect(result.character.personality.enthusiasm).toBeGreaterThanOrEqual(0);
                expect(result.character.personality.enthusiasm).toBeLessThanOrEqual(1);
                expect(result.character.personality.empathy).toBeGreaterThanOrEqual(0);
                expect(result.character.personality.empathy).toBeLessThanOrEqual(1);
                expect(result.character.personality.creativity).toBeGreaterThanOrEqual(0);
                expect(result.character.personality.creativity).toBeLessThanOrEqual(1);
                expect(result.character.personality.directness).toBeGreaterThanOrEqual(0);
                expect(result.character.personality.directness).toBeLessThanOrEqual(1);
            }
        });
    });

    describe('Caching and Performance', () => {
        it('should cache search results', async () => {
            const mockResult = {
                success: true,
                data: { title: 'Test', content: 'Test content' }
            };

            mockWebFetch.mockResolvedValue(mockResult);

            // First call
            await searchTool.execute({
                query: 'test query',
                enableCache: true
            });

            // Second call should use cache
            await searchTool.execute({
                query: 'test query',
                enableCache: true
            });

            // WebFetch should only be called once due to caching
            expect(mockWebFetch).toHaveBeenCalledTimes(1);
        });

        it('should respect cache TTL', async () => {
            const mockResult = {
                success: true,
                data: { title: 'Test', content: 'Test content' }
            };

            mockWebFetch.mockResolvedValue(mockResult);

            // Configure short cache TTL for testing
            searchTool.setCacheTTL(100); // 100ms

            await searchTool.execute({
                query: 'test query',
                enableCache: true
            });

            // Wait for cache to expire
            await new Promise(resolve => setTimeout(resolve, 150));

            await searchTool.execute({
                query: 'test query',
                enableCache: true
            });

            // Should call WebFetch twice due to cache expiration
            expect(mockWebFetch).toHaveBeenCalledTimes(2);
        });
    });
});

describe('LangGraph Tool Integration', () => {
    it('should create a valid LangChain tool instance', () => {
        expect(webSearchTool).toBeDefined();
        expect(webSearchTool.name).toBe('web_search');
        expect(webSearchTool.description).toContain('search');
        expect(webSearchTool.schema).toBeDefined();
    });

    it('should create tool node for LangGraph workflows', () => {
        const toolNode = createWebSearchToolNode();
        
        expect(toolNode).toBeDefined();
        expect(typeof toolNode.invoke).toBe('function');
    });

    it('should handle tool execution through LangChain interface', async () => {
        const mockWebFetch = vi.fn().mockResolvedValue({
            success: true,
            data: { title: 'Test', content: 'Test content' }
        });

        // Mock the global webFetch function
        global.webFetch = mockWebFetch;

        const result = await webSearchTool.invoke({
            query: 'test search',
            maxResults: 3
        });

        expect(result.success).toBe(true);
        expect(mockWebFetch).toHaveBeenCalled();
    });
});