{"timestamp": "2025-08-14T02:44:27.913Z", "environment": {"hasApiKey": false, "nodeVersion": "v23.5.0", "platform": "darwin"}, "summary": {"total": 4, "passed": 0, "failed": 4, "skipped": 2, "passRate": 0, "totalDuration": 3736, "avgDuration": 934}, "tests": [{"file": "dualbrain-coordinator-enhanced.test.js", "passed": false, "duration": 1000, "hasOutput": true, "hasError": true}, {"file": "modern-trigger-system-validation.test.js", "passed": false, "duration": 896, "hasOutput": true, "hasError": true}, {"file": "real-api-integration.test.js", "passed": false, "duration": 922, "hasOutput": true, "hasError": true}, {"file": "dual-brain-integration.test.js", "passed": false, "duration": 918, "hasOutput": true, "hasError": true}], "enhancements": ["Fixed pending decision deadlock prevention", "Enhanced System 1 → System 2 context flow", "Consolidated decision-making logic", "Real API integration with timeout handling", "Audio output control architecture"]}