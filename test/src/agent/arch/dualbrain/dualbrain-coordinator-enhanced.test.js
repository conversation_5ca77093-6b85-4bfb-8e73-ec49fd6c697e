/**
 * Enhanced DualBrainCoordinator Tests - December 2024 Fixes
 * 
 * Comprehensive test suite for the enhanced DualBrainCoordinator covering:
 * - Fixed pending decision deadlock prevention
 * - Enhanced System 1 → System 2 context flow
 * - Consolidated decision-making logic
 * - Real API integration with Dashscope
 * - Audio output control architecture
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { DualBrainCoordinator } from '@/agent/arch/dualbrain/DualBrainCoordinator.js';
import { createLogger } from '@/utils/logger.ts';
import { createMockAgentService } from '../../../utils/test-helpers.js';

const logger = createLogger('DualBrainEnhancedTest');

describe('Enhanced DualBrainCoordinator - December 2024 Fixes', () => {
  let coordinator;
  let mockAgentService;
  let realApiKey;

  beforeEach(async () => {
    // Get real API key for integration tests
    realApiKey = process.env.VITE_DASHSCOPE_API_KEY;

    // Create enhanced mock agent service
    mockAgentService = createMockAgentService({
      enableDualBrain: true,
      realtimeCapable: true,
      models: {
        system1: {
          constructor: { name: 'AliyunWebSocketChatModel' },
          apiMode: 'websocket',
          invoke: vi.fn().mockResolvedValue({ content: 'System 1 response' })
        },
        system2: {
          constructor: { name: 'AliyunHttpChatModel' },
          apiMode: 'http',
          invoke: vi.fn().mockResolvedValue({ content: '{"shouldAct": true, "confidence": 0.8, "reason": "test_decision"}' })
        }
      }
    });

    coordinator = new DualBrainCoordinator(mockAgentService, {
      decisionCooldown: 1000, // Shorter for testing
      system2AnalysisInterval: 5000,
      deadlockPrevention: true,
      enhancedContextFlow: true
    });

    await coordinator.initialize();
  });

  afterEach(async () => {
    if (coordinator) {
      coordinator.dispose();
    }
  });

  describe('1. Fixed Pending Decision Deadlock Prevention', () => {
    it('should prevent deadlock with stuck pending decisions', async () => {
      // Manually add a stuck pending decision to simulate the issue
      const stuckDecisionId = 'stuck_decision_test';
      coordinator._pendingDecisions.set(stuckDecisionId, {
        startTime: Date.now() - 35000, // 35 seconds ago (older than 30s timeout)
        contextData: {}
      });

      expect(coordinator._pendingDecisions.size).toBe(1);

      // Generate a new decision - should clean up the stuck one first
      const decision = await coordinator.generateProactiveDecision({
        test: 'deadlock_prevention'
      });

      // Should not return decision_pending due to cleanup
      expect(decision.reason).not.toBe('decision_pending');
      expect(coordinator._pendingDecisions.size).toBeLessThanOrEqual(1); // Either 0 or 1 (new decision)
    });

    it('should clean up multiple stuck pending decisions', async () => {
      // Add multiple stuck decisions
      const now = Date.now();
      coordinator._pendingDecisions.set('stuck1', { startTime: now - 35000, contextData: {} });
      coordinator._pendingDecisions.set('stuck2', { startTime: now - 40000, contextData: {} });
      coordinator._pendingDecisions.set('recent', { startTime: now - 5000, contextData: {} }); // Recent, shouldn't be cleaned

      expect(coordinator._pendingDecisions.size).toBe(3);

      // Call cleanup directly
      coordinator._cleanupStuckPendingDecisions();

      // Should clean up only the stuck ones, keep the recent one
      expect(coordinator._pendingDecisions.size).toBe(1);
      expect(coordinator._pendingDecisions.has('recent')).toBe(true);
    });

    it('should handle decision cooldown properly after cleanup', async () => {
      // Generate first decision
      const decision1 = await coordinator.generateProactiveDecision({ test: 'cooldown1' });
      expect(decision1.reason).not.toBe('decision_cooldown');

      // Immediately try another - should hit cooldown
      const decision2 = await coordinator.generateProactiveDecision({ test: 'cooldown2' });
      expect(decision2.reason).toBe('decision_cooldown');

      // Wait for cooldown to pass
      await new Promise(resolve => setTimeout(resolve, 1100));

      // Should work again
      const decision3 = await coordinator.generateProactiveDecision({ test: 'cooldown3' });
      expect(decision3.reason).not.toBe('decision_cooldown');
    });
  });

  describe('2. Enhanced System 1 → System 2 Context Flow', () => {
    it('should build comprehensive System 1 contextual descriptions', async () => {
      const contextData = {
        audioActivity: true,
        environmentalState: 'active_conversation'
      };

      // Mock internal state
      coordinator.state.lastSystem1Response = { timestamp: Date.now(), content: 'test' };
      coordinator.state.contextBuffer = [
        { type: 'conversation', data: { content: 'user said hello' }, timestamp: Date.now() },
        { type: 'user_interaction', data: { intent: 'greeting' }, timestamp: Date.now() }
      ];

      const description = coordinator._buildSystem1ContextualDescription(contextData);

      expect(description).toContain('Audio Environment: Active user audio detected');
      expect(description).toContain('Environmental State: active_conversation');
      expect(description).toContain('Recent System 1 Activity: User interaction within last session');
      expect(description).toContain('Context Buffer: 2 recent context items');
    });

    it('should retrieve user memory profiles', async () => {
      // Mock agent service memory context
      mockAgentService.getMemoryContext = vi.fn().mockReturnValue({
        userPreferences: 'likes technical discussions',
        conversationStyle: 'analytical and detailed',
        recentInteractions: 'discussed AI architecture'
      });

      const profiles = coordinator._getUserMemoryProfiles();

      expect(profiles.userPreferences).toBe('likes technical discussions');
      expect(profiles.conversationStyle).toBe('analytical and detailed');
      expect(profiles.interactionHistory).toBe('discussed AI architecture');
    });

    it('should get avatar profiles with character context', async () => {
      const profiles = coordinator._getAvatarProfiles();

      expect(profiles).toHaveProperty('personality');
      expect(profiles).toHaveProperty('responseStyle');
      expect(profiles).toHaveProperty('communicationMode');
      expect(profiles).toHaveProperty('proactivityLevel');
      expect(profiles.responseStyle).toBe('Analytical and thoughtful assistant');
    });

    it('should extract conversation history from context buffer', async () => {
      coordinator.state.contextBuffer = [
        {
          type: 'conversation',
          data: { content: 'Hello AI', summary: 'User greeting' },
          timestamp: Date.now() - 1000
        },
        {
          type: 'user_interaction',
          data: { intent: 'question', content: 'What can you do?' },
          timestamp: Date.now() - 2000
        },
        {
          type: 'system_update',
          data: { status: 'running' },
          timestamp: Date.now() - 3000
        }
      ];

      const history = coordinator._getRecentConversationHistory();

      expect(history).toHaveLength(2); // Only conversation and user_interaction types
      expect(history[0].summary).toBe('User greeting');
      expect(history[1].type).toBe('user_interaction');
    });

    it('should build enhanced proactive analysis prompt with rich context', async () => {
      const contextData = {
        audioActivity: true,
        environmentalState: 'engaged'
      };

      coordinator.state.contextBuffer = [
        { type: 'conversation', data: { content: 'test' }, timestamp: Date.now() }
      ];

      // Mock the createSystem2AnalysisPrompt function
      const mockCreatePrompt = vi.fn().mockReturnValue('Enhanced prompt with context');

      // We can't easily mock the import, so let's test the context building instead
      const enhancedContextData = {
        ...contextData,
        system1Context: coordinator._buildSystem1ContextualDescription(contextData),
        userMemoryProfiles: coordinator._getUserMemoryProfiles(),
        avatarProfiles: coordinator._getAvatarProfiles(),
        conversationHistory: coordinator._getRecentConversationHistory(),
        recentContext: coordinator.state.contextBuffer.slice(0, 5)
      };

      expect(enhancedContextData.system1Context).toBeDefined();
      expect(enhancedContextData.userMemoryProfiles).toBeDefined();
      expect(enhancedContextData.avatarProfiles).toBeDefined();
      expect(enhancedContextData.conversationHistory).toBeDefined();
    });
  });

  describe('3. Decision-Making Logic Consolidation', () => {
    it('should make decisions only through DualBrainCoordinator', async () => {
      const decision = await coordinator.generateProactiveDecision({
        test: 'consolidation',
        audioActivity: false
      });

      expect(decision).toHaveProperty('shouldAct');
      expect(decision).toHaveProperty('confidence');
      expect(decision).toHaveProperty('reason');
      expect(typeof decision.shouldAct).toBe('boolean');
    });

    it('should handle System 2 model invocation properly', async () => {
      // Mock System 2 response
      mockAgentService.getModel('system2').invoke.mockResolvedValue({
        content: JSON.stringify({
          shouldAct: true,
          confidence: 0.9,
          reason: 'user_engagement_needed',
          urgency: 'medium',
          toolsRequired: [
            {
              type: 'speech',
              name: 'control_avatar_speech',
              reasoning: 'User appears to need assistance'
            }
          ]
        })
      });

      const decision = await coordinator.generateProactiveDecision({
        conversational: { messages: [] },
        environmental: { activity: 'monitoring' }
      });

      expect(decision.shouldAct).toBe(true);
      expect(decision.confidence).toBe(0.9);
      expect(decision.reason).toBe('user_engagement_needed');
      expect(decision.toolsRequired).toHaveLength(1);
      expect(decision.toolsRequired[0].type).toBe('speech');
    });

    it('should handle System 2 analysis timeout gracefully', async () => {
      // Mock System 2 to timeout
      mockAgentService.getModel('system2').invoke.mockImplementation(() =>
        new Promise((resolve) => setTimeout(resolve, 20000)) // 20 second delay
      );

      const startTime = Date.now();
      const decision = await coordinator.generateProactiveDecision({ test: 'timeout' });
      const elapsed = Date.now() - startTime;

      // Should timeout in 15 seconds as configured
      expect(elapsed).toBeLessThan(18000);
      expect(decision.reason).toBe('decision_error');
      expect(decision.shouldAct).toBe(false);
    });
  });

  describe('4. Modern Trigger System Implementation Tests', () => {
    let triggerSystemCoordinator;
    
    beforeEach(async () => {
      // Create coordinator with modern trigger system enabled
      triggerSystemCoordinator = new DualBrainCoordinator(mockAgentService, {
        modernTriggerSystem: true,
        hybridTriggers: true,
        adaptiveIntervals: true,
        userActivationControls: true
      });
      await triggerSystemCoordinator.initialize();
    });

    afterEach(() => {
      if (triggerSystemCoordinator) {
        triggerSystemCoordinator.dispose();
      }
    });

    it('should initialize hybrid trigger system with activity monitoring', async () => {
      expect(triggerSystemCoordinator.triggerSystem).toBeDefined();
      expect(triggerSystemCoordinator.triggerSystem.system1Triggers).toContain('user-input');
      expect(triggerSystemCoordinator.triggerSystem.system2Triggers).toContain('complex-decision-needed');
      expect(triggerSystemCoordinator.triggerSystem.intervals).toHaveProperty('quiet', 30000);
      expect(triggerSystemCoordinator.triggerSystem.intervals).toHaveProperty('active', 5000);
      expect(triggerSystemCoordinator.triggerSystem.activityScore).toBe(0);
    });

    it('should handle System 1 fast triggers with activity scoring', async () => {
      const triggerData = {
        type: 'user-input',
        content: 'Hello AI!',
        timestamp: Date.now()
      };

      triggerSystemCoordinator._handleSystem1Trigger('user-input', triggerData, {
        priority: 'immediate',
        requiresSystem2: false
      });

      expect(triggerSystemCoordinator.triggerSystem.activityScore).toBeGreaterThan(0);
      expect(triggerSystemCoordinator.triggerSystem.systemState).toBe('active');
    });

    it('should escalate complex requests to System 2', async () => {
      const complexInput = {
        content: 'Please analyze the implications of quantum computing on modern cryptography and explain the reasoning behind your conclusions',
        complexity: 'high',
        requiresReasoning: true
      };

      const shouldEscalate = triggerSystemCoordinator._shouldEscalateToSystem2(complexInput);
      expect(shouldEscalate).toBe(true);
    });

    it('should manage adaptive intervals based on system state', async () => {
      // Test quiet state (30s intervals)
      triggerSystemCoordinator.triggerSystem.systemState = 'quiet';
      triggerSystemCoordinator._scheduleAdaptiveFallback();
      expect(triggerSystemCoordinator.triggerSystem.currentInterval).toBe(30000);

      // Test active state (5s intervals) 
      triggerSystemCoordinator.triggerSystem.systemState = 'active';
      triggerSystemCoordinator._scheduleAdaptiveFallback();
      expect(triggerSystemCoordinator.triggerSystem.currentInterval).toBe(5000);

      // Test emergency state (1s intervals)
      triggerSystemCoordinator.triggerSystem.systemState = 'emergency';
      triggerSystemCoordinator._scheduleAdaptiveFallback();
      expect(triggerSystemCoordinator.triggerSystem.currentInterval).toBe(1000);
    });

    it('should decay activity score over time', async () => {
      // Add initial activity
      triggerSystemCoordinator._updateSystemActivity('system1', 'user-input');
      const initialScore = triggerSystemCoordinator.triggerSystem.activityScore;
      expect(initialScore).toBeGreaterThan(0);

      // Simulate activity decay
      triggerSystemCoordinator.triggerSystem.activityScore *= 0.95; // 5% decay
      expect(triggerSystemCoordinator.triggerSystem.activityScore).toBeLessThan(initialScore);
    });

    // Skip if no real API key
    const conditionalTest = realApiKey ? it : it.skip;

    conditionalTest('should make real API calls to Dashscope for System 2 decisions', async () => {
      // Create coordinator with real models (this would need actual model setup)
      // This is a placeholder for integration with real API
      const realDecision = await coordinator.generateProactiveDecision({
        conversational: {
          messages: [
            { role: 'user', content: 'Hello, how are you?' }
          ]
        },
        environmental: {
          activity: 'conversation',
          timestamp: Date.now()
        }
      });

      expect(realDecision).toHaveProperty('shouldAct');
      expect(realDecision).toHaveProperty('confidence');
      expect(realDecision).toHaveProperty('reason');

      logger.info('Real API decision result:', realDecision);
    }, 30000); // 30 second timeout for real API calls

    conditionalTest('should handle real API failures gracefully', async () => {
      // Test with invalid context to trigger API error handling
      const decision = await coordinator.generateProactiveDecision({
        invalidContext: 'this should trigger error handling'
      });

      // Should still return a valid decision structure even on API failure
      expect(decision).toHaveProperty('shouldAct');
      expect(decision.shouldAct).toBe(false);
    });
  });

  describe('5. User Activation Controls & Privacy', () => {
    it('should enforce user activation controls for media capture', async () => {
      // Test user activation control logic
      const mediaActivationCheck = {
        userActivated: false,
        mediaType: 'audio-video'
      };

      // Should prevent capture without user activation
      const shouldPreventCapture = !mediaActivationCheck.userActivated;
      expect(shouldPreventCapture).toBe(true);
    });

    it('should validate session modality configuration', () => {
      // Test System 2 text-only output configuration
      const system2Config = {
        forSystem2: true,
        modalities: ['text'], // System 2 should only output text
        inputModalities: ['text', 'audio'], // Can receive both
        outputModalities: ['text'] // Only outputs text
      };

      expect(system2Config.outputModalities).toEqual(['text']);
      expect(system2Config.modalities).toEqual(['text']);
      expect(system2Config.forSystem2).toBe(true);
    });

    it('should document System 2 control over proactive speech', () => {
      // Test that the architecture supports System 2 audio control
      const status = coordinator.getStatus();

      expect(status).toHaveProperty('hasModel');
      expect(status).toHaveProperty('isActive');

      // Verify that decisions include audio control information
      expect(coordinator._buildProactiveAnalysisPrompt).toBeDefined();
      expect(coordinator._parseProactiveDecision).toBeDefined();
    });

    it('should make decisions about when to generate audio output', async () => {
      // Mock a decision that should trigger audio output
      mockAgentService.getModel('system2').invoke.mockResolvedValue({
        content: JSON.stringify({
          shouldAct: true,
          confidence: 0.8,
          reason: 'user_needs_engagement',
          urgency: 'medium',
          toolsRequired: [
            {
              type: 'speech',
              name: 'control_avatar_speech',
              reasoning: 'Contextual analysis indicates speaking opportunity'
            }
          ],
          suggestedAction: 'Engage user with helpful response'
        })
      });

      const decision = await coordinator.generateProactiveDecision({
        conversational: { silenceDuration: 5000 },
        environmental: { activity: 'waiting' }
      });

      expect(decision.shouldAct).toBe(true);
      expect(decision.toolsRequired).toHaveLength(1);
      expect(decision.toolsRequired[0].type).toBe('speech');
      expect(decision.suggestedAction).toContain('Engage user');
    });

    it('should prevent inappropriate audio output', async () => {
      // Mock a decision that should NOT trigger audio output
      mockAgentService.getModel('system2').invoke.mockResolvedValue({
        content: JSON.stringify({
          shouldAct: false,
          confidence: 0.7,
          reason: 'user_currently_speaking',
          urgency: 'low',
          toolsRequired: [],
          suggestedAction: 'Continue monitoring'
        })
      });

      const decision = await coordinator.generateProactiveDecision({
        conversational: { userCurrentlySpeaking: true },
        environmental: { activity: 'active_conversation' }
      });

      expect(decision.shouldAct).toBe(false);
      expect(decision.toolsRequired).toHaveLength(0);
      expect(decision.reason).toBe('user_currently_speaking');
    });
  });

  describe('6. Performance and Health Monitoring', () => {
    it('should provide comprehensive status information', () => {
      const status = coordinator.getStatus();

      expect(status).toHaveProperty('hasModel');
      expect(status).toHaveProperty('isInitialized');
      expect(status).toHaveProperty('isActive');
      expect(status).toHaveProperty('lastDecisionTime');
      expect(status).toHaveProperty('coordinationMode');
    });

    it('should track decision timing and performance', async () => {
      const startTime = Date.now();

      await coordinator.generateProactiveDecision({ test: 'performance' });

      const elapsed = Date.now() - startTime;

      // Should complete within reasonable time (5 seconds for test)
      expect(elapsed).toBeLessThan(5000);

      // Check that decision time was updated
      expect(coordinator.lastDecisionTime).toBeGreaterThan(startTime);
    });

    it('should handle circuit breaker functionality', () => {
      // Test circuit breaker state
      expect(coordinator.circuitBreaker).toHaveProperty('system1Failures');
      expect(coordinator.circuitBreaker).toHaveProperty('system2Failures');
      expect(coordinator.circuitBreaker).toHaveProperty('maxFailures');

      // Test failure tracking
      coordinator._recordCircuitBreakerFailure('system2');
      expect(coordinator.circuitBreaker.system2Failures).toBe(1);
    });

    it('should manage connection state properly', () => {
      expect(coordinator._activeConnections).toBeDefined();
      expect(coordinator._globalConnectionLock).toBe(false);
      expect(coordinator._pendingDecisions).toBeDefined();
    });
  });

  describe('7. Error Handling and Resilience', () => {
    it('should handle missing models gracefully', async () => {
      // Create coordinator with missing System 2 model
      const incompleteAgentService = createMockAgentService({
        enableDualBrain: true,
        models: {
          system1: mockAgentService.getModel('system1')
          // Missing system2 model
        }
      });

      incompleteAgentService.getModel = vi.fn().mockImplementation((name) => {
        if (name === 'system2') return null;
        return mockAgentService.getModel(name);
      });

      const incompleteCoordinator = new DualBrainCoordinator(incompleteAgentService);

      // Should handle missing model gracefully
      const decision = await incompleteCoordinator.generateProactiveDecision({ test: 'missing_model' });

      expect(decision.shouldAct).toBe(false);
      expect(decision.reason).toBe('decision_error');
    });

    it('should handle malformed System 2 responses', async () => {
      // Mock malformed JSON response
      mockAgentService.getModel('system2').invoke.mockResolvedValue({
        content: 'invalid json response {malformed'
      });

      const decision = await coordinator.generateProactiveDecision({ test: 'malformed' });

      // Should fall back gracefully
      expect(decision).toHaveProperty('shouldAct');
      expect(decision).toHaveProperty('confidence');
      expect(decision.shouldAct).toBe(false);
    });

    it('should handle network errors gracefully', async () => {
      // Mock network error
      mockAgentService.getModel('system2').invoke.mockRejectedValue(new Error('Network timeout'));

      const decision = await coordinator.generateProactiveDecision({ test: 'network_error' });

      expect(decision.shouldAct).toBe(false);
      expect(decision.reason).toBe('decision_error');
      expect(decision.error).toBe('Network timeout');
    });
  });
});

/**
 * Modern Trigger System Performance Benchmarks
 */
describe('Modern Trigger System - Performance Benchmarks', () => {
  let coordinator;
  let mockAgentService;

  beforeEach(async () => {
    mockAgentService = createMockAgentService({
      enableDualBrain: true,
      realtimeCapable: true
    });

    coordinator = new DualBrainCoordinator(mockAgentService, {
      decisionCooldown: 100, // Very short for benchmarking
      modernTriggerSystem: true,
      hybridTriggers: true
    });

    await coordinator.initialize();
  });

  afterEach(() => {
    if (coordinator) {
      coordinator.dispose();
    }
  });

  it('should make decisions within performance targets', async () => {
    const iterations = 10;
    const times = [];

    for (let i = 0; i < iterations; i++) {
      const start = Date.now();
      await coordinator.generateProactiveDecision({ iteration: i });
      const elapsed = Date.now() - start;
      times.push(elapsed);

      // Wait brief moment to avoid cooldown
      await new Promise(resolve => setTimeout(resolve, 150));
    }

    const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
    const maxTime = Math.max(...times);

    logger.info(`Performance Results: avg=${avgTime.toFixed(0)}ms, max=${maxTime.toFixed(0)}ms`);

    // Should meet performance targets
    expect(avgTime).toBeLessThan(1000); // Average under 1 second
    expect(maxTime).toBeLessThan(3000);  // Max under 3 seconds
  });

  it('should demonstrate performance improvements with trigger system', async () => {
    const metrics = {
      triggerResponses: 0,
      fallbackResponses: 0,
      avgTriggerTime: 0,
      avgFallbackTime: 0
    };

    // Test event-driven triggers (should be faster)
    const triggerStart = Date.now();
    coordinator._handleSystem1Trigger('user-input', { content: 'test' }, {
      priority: 'immediate',
      requiresSystem2: false
    });
    metrics.avgTriggerTime = Date.now() - triggerStart;
    metrics.triggerResponses++;

    // Test fallback analysis (slower but comprehensive)
    const fallbackStart = Date.now();
    await coordinator._executeFallbackAnalysis();
    metrics.avgFallbackTime = Date.now() - fallbackStart;
    metrics.fallbackResponses++;

    logger.info('Trigger System Performance Metrics:', metrics);

    // Event triggers should be significantly faster than fallback analysis
    expect(metrics.avgTriggerTime).toBeLessThan(metrics.avgFallbackTime);
    expect(metrics.avgTriggerTime).toBeLessThan(100); // Under 100ms for triggers
  });

  it('should show activity-based optimization benefits', async () => {
    // Test quiet period (should use 30s intervals)
    coordinator.triggerSystem.systemState = 'quiet';
    coordinator.triggerSystem.activityScore = 0;
    
    const quietInterval = coordinator.triggerSystem.intervals.quiet;
    expect(quietInterval).toBe(30000);

    // Test active period (should use 5s intervals) 
    coordinator._updateSystemActivity('system1', 'user-input');
    coordinator._updateSystemStateFromActivity();
    
    const activeInterval = coordinator.triggerSystem.intervals.active;
    expect(activeInterval).toBe(5000);

    // Demonstrate 6x improvement in quiet periods
    const improvementRatio = quietInterval / activeInterval;
    expect(improvementRatio).toBe(6); // 30s / 5s = 6x optimization

    logger.info(`Activity-based optimization: ${improvementRatio}x interval adjustment`);
  });
});