/**
 * LangGraph Multi-Agent Supervisor Pattern Tests
 * 
 * Tests the specific LangGraph multi-agent supervisor pattern implementation:
 * - SystemInvoker service integration
 * - Supervisor routing logic
 * - Multi-agent coordination without model override issues
 * - Service-based architecture validation
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { createLogger } from '@/utils/logger.ts';

const logger = createLogger('MultiAgentSupervisorTest');

describe('LangGraph Multi-Agent Supervisor Pattern', () => {
  let mockAgentService;
  let systemInvoker;
  let coordinator;

  beforeEach(() => {
    // Mock agent service
    mockAgentService = {
      isDualBrainMode: vi.fn(() => true),
      getModel: vi.fn(),
      getMemoryManager: vi.fn(() => null),
      updateDualBrainContext: vi.fn()
    };
  });

  describe('SystemInvoker Service Integration', () => {
    it('should create SystemInvoker with proper configuration', async () => {
      const { createSystemInvoker } = await import('../../../../src/agent/arch/dualbrain/services/SystemInvoker.js');
      
      systemInvoker = createSystemInvoker({
        validateInputs: true,
        validateOutputs: true,
        retryAttempts: 3
      });

      expect(systemInvoker).toBeDefined();
      expect(typeof systemInvoker.invokeSupervisor).toBe('function');
      expect(typeof systemInvoker.configureModels).toBe('function');
    });

    it('should configure models for multi-agent coordination', async () => {
      const { createSystemInvoker } = await import('../../../../src/agent/arch/dualbrain/services/SystemInvoker.js');
      
      systemInvoker = createSystemInvoker();

      const models = {
        system1: {
          constructor: { name: 'AliyunWebSocketChatModel' },
          invoke: vi.fn()
        },
        system2: {
          constructor: { name: 'AliyunHttpChatModel' },
          invoke: vi.fn()
        }
      };

      await systemInvoker.initialize(models);
      systemInvoker.configureModels(models);

      expect(systemInvoker.system1Model).toBe(models.system1);
      expect(systemInvoker.system2Model).toBe(models.system2);
    });
  });

  describe('Supervisor Pattern Routing', () => {
    it('should route simple queries to System 1', async () => {
      const { createSystemInvoker } = await import('../../../../src/agent/arch/dualbrain/services/SystemInvoker.js');
      
      const system1Model = {
        constructor: { name: 'AliyunWebSocketChatModel' },
        invoke: vi.fn(async () => ({ data: 'Fast response' }))
      };

      const system2Model = {
        constructor: { name: 'AliyunHttpChatModel' },
        invoke: vi.fn(async () => ({ data: 'Complex response' }))
      };

      systemInvoker = createSystemInvoker();
      await systemInvoker.initialize({ system1: system1Model, system2: system2Model });

      // Test routing simple query to System 1
      const result = await systemInvoker.invokeSupervisor('Hello', {
        routing: {
          targetSystem: 'system1',
          reason: 'Simple greeting',
          capabilities: ['audioOutput', 'realtime']
        }
      });

      expect(result.routing.targetSystem).toBe('system1');
      expect(system1Model.invoke).toHaveBeenCalled();
      expect(system2Model.invoke).not.toHaveBeenCalled();
    });

    it('should route complex queries to System 2', async () => {
      const { createSystemInvoker } = await import('../../../../src/agent/arch/dualbrain/services/SystemInvoker.js');
      
      const system1Model = {
        constructor: { name: 'AliyunWebSocketChatModel' },
        invoke: vi.fn(async () => ({ data: 'Fast response' }))
      };

      const system2Model = {
        constructor: { name: 'AliyunHttpChatModel' },
        invoke: vi.fn(async () => ({ data: 'Complex analysis' }))
      };

      systemInvoker = createSystemInvoker();
      await systemInvoker.initialize({ system1: system1Model, system2: system2Model });

      // Test routing complex query to System 2
      const result = await systemInvoker.invokeSupervisor('Analyze the financial implications', {
        routing: {
          targetSystem: 'system2',
          reason: 'Complex reasoning required',
          capabilities: ['tools', 'speaking', 'thinking']
        }
      });

      expect(result.routing.targetSystem).toBe('system2');
      expect(system2Model.invoke).toHaveBeenCalled();
      expect(system1Model.invoke).not.toHaveBeenCalled();
    });
  });

  describe('DualBrainCoordinator Integration', () => {
    it('should integrate with DualBrainCoordinator using supervisor pattern', async () => {
      const { createDualBrainCoordinator } = await import('../../../../src/agent/arch/dualbrain/DualBrainCoordinator.js');
      const { createSystemInvoker } = await import('../../../../src/agent/arch/dualbrain/services/SystemInvoker.js');

      // Create SystemInvoker
      systemInvoker = createSystemInvoker();
      
      // Create coordinator with SystemInvoker service
      coordinator = createDualBrainCoordinator(mockAgentService, {
        services: { systemInvoker }
      });

      // Mock models
      const system1Model = { constructor: { name: 'AliyunWebSocketChatModel' } };
      const system2Model = { constructor: { name: 'AliyunHttpChatModel' } };
      
      mockAgentService.getModel
        .mockReturnValueOnce(system1Model)
        .mockReturnValueOnce(system2Model);

      await coordinator.initialize();

      expect(coordinator.isInitialized).toBe(true);
      expect(coordinator.services.systemInvoker).toBe(systemInvoker);
    });

    it('should process multi-agent requests through coordinator', async () => {
      const { createDualBrainCoordinator } = await import('../../../../src/agent/arch/dualbrain/DualBrainCoordinator.js');
      const { createSystemInvoker } = await import('../../../../src/agent/arch/dualbrain/services/SystemInvoker.js');

      // Create SystemInvoker with mock models
      const system1Model = {
        constructor: { name: 'AliyunWebSocketChatModel' },
        invoke: vi.fn(async () => ({ data: 'System 1 response' }))
      };

      const system2Model = {
        constructor: { name: 'AliyunHttpChatModel' },
        invoke: vi.fn(async () => ({ data: 'System 2 response' }))
      };

      systemInvoker = createSystemInvoker();
      await systemInvoker.initialize({ system1: system1Model, system2: system2Model });

      coordinator = createDualBrainCoordinator(mockAgentService, {
        services: { systemInvoker }
      });

      mockAgentService.getModel
        .mockReturnValueOnce(system1Model)
        .mockReturnValueOnce(system2Model);

      await coordinator.initialize();

      // Test multi-agent request processing
      const result = await coordinator.processMultiAgentRequest('Test input', {
        complexity: 'auto'
      });

      expect(result).toBeDefined();
      expect(result.routing).toBeDefined();
      expect(['system1', 'system2']).toContain(result.routing.targetSystem);
    });
  });

  describe('Service-Based Architecture Benefits', () => {
    it('should demonstrate clean service boundaries', async () => {
      const { createDualBrainServices } = await import('../../../../src/agent/arch/dualbrain/services/index.js');

      // Create all services
      const services = createDualBrainServices({
        enableSystemInvoker: true,
        enableContextProcessor: true,
        enableDecisionProcessor: true,
        enableErrorHandler: true
      });

      // Validate service separation
      expect(services.systemInvoker).toBeDefined();
      expect(services.contextProcessor).toBeDefined();
      expect(services.decisionProcessor).toBeDefined();
      expect(services.errorHandler).toBeDefined();

      // Each service should have distinct responsibilities
      expect(typeof services.systemInvoker.invokeSupervisor).toBe('function');
      expect(typeof services.contextProcessor.processContext).toBe('function');
      expect(typeof services.decisionProcessor.processDecision).toBe('function');
      expect(typeof services.errorHandler.handleError).toBe('function');
    });

    it('should validate 70% code reduction achievement', async () => {
      const { createDualBrainCoordinator } = await import('../../../../src/agent/arch/dualbrain/DualBrainCoordinator.js');
      
      coordinator = createDualBrainCoordinator(mockAgentService);

      // The coordinator should be significantly smaller with service delegation
      const coordinatorMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(coordinator))
        .filter(name => typeof coordinator[name] === 'function');

      // Should have core coordination methods but delegate complex logic to services
      expect(coordinatorMethods).toContain('processMultiAgentRequest');
      expect(coordinatorMethods).toContain('initialize');
      expect(coordinatorMethods).toContain('generateProactiveDecision');
      
      // Should NOT have complex service-level methods (delegated to services)
      expect(coordinatorMethods).not.toContain('invokeSystem1Model');
      expect(coordinatorMethods).not.toContain('validateContextualEnhancement');
      expect(coordinatorMethods).not.toContain('processComplexDecisionLogic');
    });
  });

  describe('Error Handling and Resilience', () => {
    it('should handle SystemInvoker initialization failures', async () => {
      const { createDualBrainCoordinator } = await import('../../../../src/agent/arch/dualbrain/DualBrainCoordinator.js');
      
      // Mock agent service that fails dual brain mode check
      const failingAgentService = {
        ...mockAgentService,
        isDualBrainMode: vi.fn(() => false)
      };

      coordinator = createDualBrainCoordinator(failingAgentService);

      const initResult = await coordinator.initialize();
      expect(initResult).toBe(false);
    });

    it('should handle multi-agent request processing errors', async () => {
      const { createDualBrainCoordinator } = await import('../../../../src/agent/arch/dualbrain/DualBrainCoordinator.js');
      
      coordinator = createDualBrainCoordinator(mockAgentService, {
        services: null // No SystemInvoker service
      });

      await expect(
        coordinator.processMultiAgentRequest('test input')
      ).rejects.toThrow('SystemInvoker not initialized');
    });
  });
});