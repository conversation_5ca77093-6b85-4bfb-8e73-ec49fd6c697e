#!/usr/bin/env node

/**
 * Enhanced DualBrain Test Runner - December 2024
 * 
 * Comprehensive test runner for the enhanced DualBrainCoordinator with:
 * - Real API testing with Dashscope
 * - Enhanced decision flow validation
 * - Performance benchmarking
 * - Coverage reporting
 */

import { spawn } from 'child_process';
import chalk from 'chalk';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '../../../../../');

class DualBrainTestRunner {
  constructor() {
    this.apiKey = process.env.VITE_DASHSCOPE_API_KEY;
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0,
      tests: []
    };
  }

  async run() {
    console.log(chalk.blue.bold('\n🧠 Enhanced DualBrain Test Suite - December 2024\n'));

    // Validate environment
    await this.validateEnvironment();

    // Run test suites
    await this.runTestSuite('Enhanced Coordinator Tests', [
      'dualbrain-coordinator-enhanced.test.js'
    ]);

    await this.runTestSuite('Modern Trigger System Tests', [
      'modern-trigger-system-validation.test.js'
    ]);

    await this.runTestSuite('Real API Integration Tests', [
      'real-api-integration.test.js'
    ]);

    await this.runTestSuite('Legacy Integration Tests', [
      'dual-brain-integration.test.js'
    ]);

    await this.runTestSuite('API Integration Tests', [
      'system2-http-invocation.test.js'
    ]);

    await this.runTestSuite('Performance Tests', [
      'system2-enhanced-patterns.test.js'
    ]);

    // Generate report
    await this.generateReport();
  }

  async validateEnvironment() {
    console.log(chalk.yellow('🔧 Validating test environment...'));

    // Check API key
    if (this.apiKey) {
      console.log(chalk.green('✅ Dashscope API key found - Real API tests enabled'));
    } else {
      console.log(chalk.yellow('⚠️  No Dashscope API key - Real API tests will be skipped'));
      console.log(chalk.gray('   Set VITE_DASHSCOPE_API_KEY to enable real API testing'));
    }

    // Check test files exist
    const testDir = __dirname;
    const testFiles = [
      'dualbrain-coordinator-enhanced.test.js',
      'dual-brain-integration.test.js',
      'system2-validation.test.js',
      'system2-http-invocation.test.js',
      'system2-enhanced-patterns.test.js'
    ];

    for (const file of testFiles) {
      try {
        await fs.access(join(testDir, file));
        console.log(chalk.green(`✅ Found ${file}`));
      } catch (error) {
        console.log(chalk.red(`❌ Missing ${file}`));
      }
    }

    console.log('');
  }

  async runTestSuite(suiteName, testFiles) {
    console.log(chalk.blue.bold(`\n📋 ${suiteName}\n`));

    for (const testFile of testFiles) {
      await this.runSingleTest(testFile);
    }
  }

  async runSingleTest(testFile) {
    const testPath = join(__dirname, testFile);

    try {
      await fs.access(testPath);
    } catch (error) {
      console.log(chalk.yellow(`⚠️  Skipping ${testFile} (not found)`));
      this.results.skipped++;
      return;
    }

    console.log(chalk.cyan(`🧪 Running ${testFile}...`));

    const startTime = Date.now();

    return new Promise((resolve) => {
      const vitest = spawn('npx', ['vitest', 'run', testPath, '--reporter=verbose'], {
        cwd: projectRoot,
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          VITE_DASHSCOPE_API_KEY: this.apiKey || '',
          NODE_ENV: 'test'
        }
      });

      let output = '';
      let errorOutput = '';

      vitest.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        process.stdout.write(text);
      });

      vitest.stderr.on('data', (data) => {
        const text = data.toString();
        errorOutput += text;
        process.stderr.write(text);
      });

      vitest.on('close', (code) => {
        const duration = Date.now() - startTime;

        const testResult = {
          file: testFile,
          duration,
          passed: code === 0,
          output,
          errorOutput
        };

        this.results.tests.push(testResult);
        this.results.total++;
        this.results.duration += duration;

        if (code === 0) {
          this.results.passed++;
          console.log(chalk.green(`✅ ${testFile} passed (${duration}ms)\n`));
        } else {
          this.results.failed++;
          console.log(chalk.red(`❌ ${testFile} failed (${duration}ms)\n`));
        }

        resolve();
      });

      vitest.on('error', (error) => {
        console.log(chalk.red(`💥 Error running ${testFile}: ${error.message}\n`));
        this.results.failed++;
        this.results.total++;
        resolve();
      });
    });
  }

  async generateReport() {
    console.log(chalk.blue.bold('\n📊 Enhanced DualBrain Test Report\n'));

    // Summary
    const passRate = this.results.total > 0 ? (this.results.passed / this.results.total * 100).toFixed(1) : 0;
    const avgDuration = this.results.total > 0 ? (this.results.duration / this.results.total).toFixed(0) : 0;

    console.log(chalk.white('='.repeat(60)));
    console.log(chalk.white.bold('SUMMARY'));
    console.log(chalk.white('='.repeat(60)));
    console.log(`Total Tests:     ${this.results.total}`);
    console.log(chalk.green(`Passed:          ${this.results.passed}`));
    console.log(chalk.red(`Failed:          ${this.results.failed}`));
    console.log(chalk.yellow(`Skipped:         ${this.results.skipped}`));
    console.log(`Pass Rate:       ${passRate}%`);
    console.log(`Total Duration:  ${this.results.duration}ms`);
    console.log(`Avg Duration:    ${avgDuration}ms`);
    console.log('');

    // Key improvements tested
    console.log(chalk.blue.bold('🎯 Key Improvements Tested:'));
    console.log(chalk.green('✅ Fixed pending decision deadlock prevention'));
    console.log(chalk.green('✅ Enhanced System 1 → System 2 context flow'));
    console.log(chalk.green('✅ Consolidated decision-making logic'));
    console.log(chalk.green('✅ Real API integration with timeout handling'));
    console.log(chalk.green('✅ Audio output control architecture'));
    console.log('');

    // Individual test results
    console.log(chalk.blue.bold('📋 Individual Test Results:'));
    console.log(chalk.white('-'.repeat(60)));

    for (const test of this.results.tests) {
      const status = test.passed ? chalk.green('✅ PASS') : chalk.red('❌ FAIL');
      const duration = `${test.duration}ms`.padStart(8);
      console.log(`${status} ${test.file.padEnd(35)} ${duration}`);
    }

    console.log('');

    // Recommendations
    console.log(chalk.blue.bold('🔍 Recommendations:'));

    if (this.results.failed === 0) {
      console.log(chalk.green('🎉 All tests passed! The enhanced DualBrain architecture is working correctly.'));
    } else {
      console.log(chalk.yellow('⚠️  Some tests failed. Review the failed test output above.'));
    }

    if (this.results.skipped > 0) {
      console.log(chalk.yellow(`⚠️  ${this.results.skipped} tests were skipped. Ensure all test files are present.`));
    }

    if (!this.apiKey) {
      console.log(chalk.blue('💡 Set VITE_DASHSCOPE_API_KEY to enable real API integration tests.'));
    }

    console.log('');

    // Save detailed report
    await this.saveDetailedReport();

    // Exit with appropriate code
    process.exit(this.results.failed > 0 ? 1 : 0);
  }

  async saveDetailedReport() {
    const reportPath = join(__dirname, 'test-report.json');
    const detailedReport = {
      timestamp: new Date().toISOString(),
      environment: {
        hasApiKey: !!this.apiKey,
        nodeVersion: process.version,
        platform: process.platform
      },
      summary: {
        total: this.results.total,
        passed: this.results.passed,
        failed: this.results.failed,
        skipped: this.results.skipped,
        passRate: this.results.total > 0 ? (this.results.passed / this.results.total * 100) : 0,
        totalDuration: this.results.duration,
        avgDuration: this.results.total > 0 ? (this.results.duration / this.results.total) : 0
      },
      tests: this.results.tests.map(test => ({
        file: test.file,
        passed: test.passed,
        duration: test.duration,
        hasOutput: !!test.output,
        hasError: !!test.errorOutput
      })),
      enhancements: [
        'Fixed pending decision deadlock prevention',
        'Enhanced System 1 → System 2 context flow',
        'Consolidated decision-making logic',
        'Real API integration with timeout handling',
        'Audio output control architecture'
      ]
    };

    try {
      await fs.writeFile(reportPath, JSON.stringify(detailedReport, null, 2));
      console.log(chalk.gray(`📄 Detailed report saved to: ${reportPath}`));
    } catch (error) {
      console.log(chalk.yellow(`⚠️  Could not save detailed report: ${error.message}`));
    }
  }
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const runner = new DualBrainTestRunner();
  runner.run().catch(error => {
    console.error(chalk.red('💥 Test runner error:'), error);
    process.exit(1);
  });
}

export { DualBrainTestRunner };