/**
 * Dual Brain Architecture - Multi-Agent Supervisor Pattern Integration Tests
 * 
 * Updated test suite for LangGraph Multi-Agent Supervisor Pattern:
 * - LangGraph Multi-Agent Supervisor pattern validation
 * - Service-based architecture testing (70% code reduction)
 * - SystemInvoker service integration
 * - Provider-agnostic core.js integration  
 * - Multi-agent coordination without model override issues
 * 
 * Key Tests:
 * - Multi-agent request processing via supervisor pattern
 * - Routing logic for System 1 (fast brain) vs System 2 (reasoning brain)
 * - Service boundary validation with SystemInvoker
 * - Core.js generalization compliance
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { spawn } from 'child_process';
import chalk from 'chalk';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { createLogger } from '@/utils/logger.js';
import { createMockAgentService } from '../../utils/test-helpers.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const logger = createLogger('DualBrainIntegrationTest');

describe('Dual Brain Architecture - Comprehensive Integration', () => {
  let mockAgentService;
  let coordinator;
  let testServer;

  beforeEach(async () => {
    mockAgentService = createMockAgentService({
      enableDualBrain: true,
      realtimeCapable: true
    });
  });

  afterEach(async () => {
    if (coordinator && typeof coordinator.shutdown === 'function') {
      await coordinator.shutdown();
    }
    if (testServer) {
      testServer.kill();
    }
  });

  describe('LangGraph Multi-Agent Supervisor Pattern', () => {
    it('should process multi-agent requests via supervisor pattern', async () => {
      const { createDualBrainCoordinator } = await import('@/agent/arch/dualbrain/DualBrainCoordinator.js');
      const { createSystemInvoker } = await import('@/agent/arch/dualbrain/services/SystemInvoker.js');

      // Create mock models for System 1 and System 2
      const system1Model = {
        constructor: { name: 'AliyunWebSocketChatModel' },
        invoke: vi.fn(async () => ({ content: 'Fast System 1 response' }))
      };

      const system2Model = {
        constructor: { name: 'AliyunHttpChatModel' },
        invoke: vi.fn(async () => ({ content: 'Reasoning System 2 response' }))
      };

      // Create SystemInvoker service
      const systemInvoker = createSystemInvoker();
      await systemInvoker.initialize({
        system1: system1Model,
        system2: system2Model
      });

      // Create coordinator with services
      coordinator = createDualBrainCoordinator(mockAgentService, {
        services: { systemInvoker }
      });

      mockAgentService.getModel
        .mockReturnValueOnce(system1Model)  // for 'system1'
        .mockReturnValueOnce(system2Model); // for 'system2'

      await coordinator.initialize();

      // Test simple query routing to System 1
      const simpleResult = await coordinator.processMultiAgentRequest('Hello', {
        complexity: 'low'
      });

      expect(simpleResult.routing.targetSystem).toBe('system1');
      expect(simpleResult.routing.capabilities).toContain('audioOutput');
      expect(system1Model.invoke).toHaveBeenCalled();

      // Test complex query routing to System 2
      const complexResult = await coordinator.processMultiAgentRequest('Analyze the implications of AI development', {
        complexity: 'high'
      });

      expect(complexResult.routing.targetSystem).toBe('system2');
      expect(complexResult.routing.capabilities).toContain('tools');
      expect(system2Model.invoke).toHaveBeenCalled();
    });

    it('should validate supervisor routing logic', async () => {
      const { createDualBrainCoordinator } = await import('@/agent/arch/dualbrain/DualBrainCoordinator.js');

      coordinator = createDualBrainCoordinator(mockAgentService);

      // Test routing decisions
      const testCases = [
        {
          input: 'Hi',
          expected: 'system1',
          reason: 'Simple query should route to System 1'
        },
        {
          input: 'Please analyze the complex financial implications of this decision and provide detailed reasoning',
          expected: 'system2', 
          reason: 'Complex analysis should route to System 2'
        },
        {
          input: 'How does quantum computing work?',
          options: { requiresReasoning: true },
          expected: 'system2',
          reason: 'Explicit reasoning requirement should route to System 2'
        }
      ];

      for (const testCase of testCases) {
        const routing = coordinator._routeToAppropriateSystem(testCase.input, testCase.options || {});
        expect(routing.targetSystem).toBe(testCase.expected, testCase.reason);
      }
    });

    it('should validate service-based architecture integration', async () => {
      const { createDualBrainServices } = await import('@/agent/arch/dualbrain/services/index.js');
      const { createDualBrainCoordinator } = await import('@/agent/arch/dualbrain/DualBrainCoordinator.js');

      // Create all services
      const services = createDualBrainServices({
        enableSystemInvoker: true,
        enableContextProcessor: true,
        enableDecisionProcessor: true,
        enableErrorHandler: true
      });

      expect(services.systemInvoker).toBeDefined();
      expect(services.contextProcessor).toBeDefined();
      expect(services.decisionProcessor).toBeDefined();
      expect(services.errorHandler).toBeDefined();

      // Test coordinator with services
      coordinator = createDualBrainCoordinator(mockAgentService, {
        services
      });

      const status = coordinator.getCoordinationStatus();
      expect(status.services.systemInvoker).toBe('ready');
      expect(status.multiAgentPattern).toBe('supervisor_implemented');
    });

    it('should maintain provider-agnostic core.js integration', async () => {
      // Test that coordinator works with different model types
      const genericModels = {
        system1: {
          constructor: { name: 'GenericWebSocketModel' },
          invoke: vi.fn(async () => ({ content: 'Generic System 1' }))
        },
        system2: {
          constructor: { name: 'GenericHttpModel' },
          invoke: vi.fn(async () => ({ content: 'Generic System 2' }))
        }
      };

      mockAgentService.getModel
        .mockReturnValueOnce(genericModels.system1)
        .mockReturnValueOnce(genericModels.system2);

      const { createDualBrainCoordinator } = await import('@/agent/arch/dualbrain/DualBrainCoordinator.js');
      coordinator = createDualBrainCoordinator(mockAgentService);

      await coordinator.initialize();
      
      // Should work with any model type that implements the interface
      expect(coordinator.isInitialized).toBe(true);
      expect(mockAgentService.isDualBrainMode).toHaveBeenCalled();
    });
  });

  describe('System 2 Invocation & Timing Fixes', () => {
    const TEST_CONFIG = {
      REALTIME_STABILIZATION_TIMEOUT: 10000,
      SYSTEM2_INVOCATION_TIMEOUT: 8000,
      PERIODIC_ANALYSIS_INTERVAL: 3000,
      SESSION_READY_TIMEOUT: 15000
    };

    it('should handle WebSocket readiness before System 2 invocation', async () => {
      const { createDualBrainCoordinator } = await import('@/agent/arch/dualbrain/DualBrainCoordinator.js');
      
      const mockWebSocketModel = {
        constructor: { name: 'AliyunWebSocketChatModel' },
        isRealtimeModeActive: vi.fn(() => false), // Start as not ready
        waitForRealtimeReady: vi.fn(async (timeout) => {
          // Simulate becoming ready after delay
          setTimeout(() => {
            mockWebSocketModel.isRealtimeModeActive.mockReturnValue(true);
          }, 1000);
          return true;
        }),
        invoke: vi.fn(async () => ({ content: 'System 2 response' }))
      };

      mockAgentService.getModel.mockReturnValue(mockWebSocketModel);

      coordinator = await createDualBrainCoordinator({
        system1: mockWebSocketModel,
        system2: mockWebSocketModel
      }, TEST_CONFIG);

      coordinator.agentService = mockAgentService;

      // Test System 2 invocation with timing fix
      const result = await coordinator._invokeSystem2('test input', { 
        requiresRealtime: true,
        timeout: TEST_CONFIG.SYSTEM2_INVOCATION_TIMEOUT
      });

      expect(mockWebSocketModel.waitForRealtimeReady).toHaveBeenCalledWith(
        expect.any(Number)
      );
      expect(mockWebSocketModel.invoke).toHaveBeenCalled();
      expect(result).toBeDefined();
    });

    it('should validate session stabilization before coordination', async () => {
      const stabilizationSteps = [];
      
      const mockModel = {
        constructor: { name: 'AliyunWebSocketChatModel' },
        isRealtimeModeActive: vi.fn(() => true),
        realtimeSessionStabilized: false,
        waitForRealtimeReady: vi.fn(async () => {
          stabilizationSteps.push('waitForRealtimeReady_called');
          // Simulate session stabilization
          mockModel.realtimeSessionStabilized = true;
          stabilizationSteps.push('session_stabilized');
          return true;
        }),
        initializeRealtimeMode: vi.fn(async () => {
          stabilizationSteps.push('realtime_initialized');
          return true;
        })
      };

      // Test proper sequencing
      await mockModel.initializeRealtimeMode();
      const isReady = await mockModel.waitForRealtimeReady(TEST_CONFIG.REALTIME_STABILIZATION_TIMEOUT);
      
      expect(stabilizationSteps).toEqual([
        'realtime_initialized',
        'waitForRealtimeReady_called',
        'session_stabilized'
      ]);
      expect(isReady).toBe(true);
      expect(mockModel.realtimeSessionStabilized).toBe(true);
    });
  });

  describe('Enhanced Integration Patterns', () => {
    it('should support advanced coordination patterns', async () => {
      const { createDualBrainCoordinator } = await import('@/agent/arch/dualbrain/DualBrainCoordinator.js');
      
      const enhancedModel = {
        constructor: { name: 'AliyunWebSocketChatModel' },
        isRealtimeModeActive: vi.fn(() => true),
        invoke: vi.fn(async (input, options) => {
          // Simulate enhanced API capabilities
          return {
            content: 'Enhanced response',
            reasoning: options.enable_thinking ? 'Deep analysis performed' : null,
            character_context: options.character_model ? 'Character-aware response' : null,
            environmental_awareness: options.environmental_context ? 'Context-aware' : null
          };
        })
      };

      mockAgentService.getModel.mockReturnValue(enhancedModel);

      coordinator = await createDualBrainCoordinator({
        system1: enhancedModel,
        system2: enhancedModel
      }, {
        enableEnhancedIntegration: true,
        characterModelIntegration: true,
        environmentalAwareness: true
      });

      coordinator.agentService = mockAgentService;

      const result = await coordinator._invokeSystem2('complex analysis task', {
        enable_thinking: true,
        character_model: { personality: 'analytical' },
        environmental_context: { awareness_level: 'high' }
      });

      expect(result.reasoning).toBe('Deep analysis performed');
      expect(result.character_context).toBe('Character-aware response');
      expect(result.environmental_awareness).toBe('Context-aware');
    });

    it('should handle multi-modal coordination', async () => {
      const mockMultiModalModel = {
        constructor: { name: 'AliyunWebSocketChatModel' },
        isRealtimeModeActive: vi.fn(() => true),
        processMultiModalInput: vi.fn(async (audioData, visualData) => {
          return {
            audioAnalysis: 'Speech detected',
            visualAnalysis: 'Gesture recognized',
            combinedInsight: 'Coordinated understanding'
          };
        }),
        invoke: vi.fn(async () => ({ content: 'Multi-modal response' }))
      };

      const result = await mockMultiModalModel.processMultiModalInput(
        { format: 'pcm16', samples: [1, 2, 3] },
        { poses: [{ landmarks: [] }] }
      );

      expect(result.audioAnalysis).toBe('Speech detected');
      expect(result.visualAnalysis).toBe('Gesture recognized');
      expect(result.combinedInsight).toBe('Coordinated understanding');
    });
  });

  describe('System Validation & Performance', () => {
    it('should validate dual brain architecture components', async () => {
      const { createDualBrainCoordinator } = await import('@/agent/arch/dualbrain/DualBrainCoordinator.js');
      
      const validationChecks = {
        hasSystem1: false,
        hasSystem2: false,
        hasCoordination: false,
        hasPeriodicAnalysis: false,
        hasProactiveDecisions: false
      };

      const mockModel = {
        constructor: { name: 'AliyunWebSocketChatModel' },
        isRealtimeModeActive: vi.fn(() => true),
        invoke: vi.fn(async () => ({ content: 'Response' }))
      };

      coordinator = await createDualBrainCoordinator({
        system1: mockModel,
        system2: mockModel
      }, {
        enableProactiveDecisions: true,
        enablePeriodicAnalysis: true
      });

      // Validate architecture components
      validationChecks.hasSystem1 = !!coordinator.systems?.system1;
      validationChecks.hasSystem2 = !!coordinator.systems?.system2;
      validationChecks.hasCoordination = typeof coordinator.coordinateDecision === 'function';
      validationChecks.hasPeriodicAnalysis = !!coordinator.periodicAnalysis;
      validationChecks.hasProactiveDecisions = coordinator.options?.enableProactiveDecisions;

      // All components should be present
      Object.values(validationChecks).forEach(check => {
        expect(check).toBe(true);
      });
    });

    it('should meet performance benchmarks', async () => {
      const performanceMetrics = {
        initializationTime: 0,
        responseTime: 0,
        memoryUsage: 0
      };

      const startTime = Date.now();
      
      // Initialize dual brain system
      const { createDualBrainCoordinator } = await import('@/agent/arch/dualbrain/DualBrainCoordinator.js');
      
      const fastModel = {
        constructor: { name: 'AliyunWebSocketChatModel' },
        isRealtimeModeActive: vi.fn(() => true),
        invoke: vi.fn(async () => {
          // Simulate fast response
          await new Promise(resolve => setTimeout(resolve, 100));
          return { content: 'Fast response' };
        })
      };

      coordinator = await createDualBrainCoordinator({
        system1: fastModel,
        system2: fastModel
      }, {});

      performanceMetrics.initializationTime = Date.now() - startTime;

      // Test response time
      const responseStartTime = Date.now();
      coordinator.agentService = mockAgentService;
      mockAgentService.getModel.mockReturnValue(fastModel);
      
      await coordinator._invokeSystem2('performance test');
      performanceMetrics.responseTime = Date.now() - responseStartTime;

      // Performance assertions
      expect(performanceMetrics.initializationTime).toBeLessThan(5000); // < 5 seconds
      expect(performanceMetrics.responseTime).toBeLessThan(2000); // < 2 seconds
    });

    it('should handle error scenarios gracefully', async () => {
      const errorScenarios = [
        'connection_failure',
        'session_timeout',
        'api_limit_exceeded',
        'invalid_response'
      ];

      const results = [];

      for (const scenario of errorScenarios) {
        const errorModel = {
          constructor: { name: 'AliyunWebSocketChatModel' },
          isRealtimeModeActive: vi.fn(() => scenario !== 'connection_failure'),
          invoke: vi.fn(async () => {
            switch (scenario) {
              case 'connection_failure':
                throw new Error('Connection failed');
              case 'session_timeout':
                throw new Error('Session timeout');
              case 'api_limit_exceeded':
                throw new Error('API limit exceeded');
              case 'invalid_response':
                return null; // Invalid response
              default:
                return { content: 'Success' };
            }
          })
        };

        try {
          const { createDualBrainCoordinator } = await import('@/agent/arch/dualbrain/DualBrainCoordinator.js');
          
          coordinator = await createDualBrainCoordinator({
            system1: errorModel,
            system2: errorModel
          }, {});

          coordinator.agentService = mockAgentService;
          mockAgentService.getModel.mockReturnValue(errorModel);

          await coordinator._invokeSystem2('error test');
          results.push({ scenario, status: 'success' });
        } catch (error) {
          results.push({ scenario, status: 'handled', error: error.message });
        }
      }

      // All error scenarios should be handled (not crash the system)
      expect(results).toHaveLength(errorScenarios.length);
      results.forEach(result => {
        expect(['success', 'handled']).toContain(result.status);
      });
    });
  });

  describe('Integration Test Utilities', () => {
    it('should provide comprehensive test validation', () => {
      const validationSuite = {
        validateDualBrainCoordinator: (coordinator) => {
          return {
            hasValidSystems: !!(coordinator.systems?.system1 && coordinator.systems?.system2),
            hasValidOptions: !!coordinator.options,
            hasValidMethods: typeof coordinator._invokeSystem2 === 'function',
            isReady: coordinator.isInitialized !== false
          };
        },
        
        validateSystemIntegration: async (coordinator) => {
          const checks = [];
          
          try {
            // Test System 1 integration
            if (coordinator.systems?.system1) {
              checks.push({ component: 'system1', status: 'available' });
            }
            
            // Test System 2 integration
            if (coordinator.systems?.system2) {
              checks.push({ component: 'system2', status: 'available' });
            }
            
            // Test coordination
            if (typeof coordinator.coordinateDecision === 'function') {
              checks.push({ component: 'coordination', status: 'functional' });
            }
            
            return { valid: checks.length >= 3, checks };
          } catch (error) {
            return { valid: false, error: error.message };
          }
        }
      };

      expect(typeof validationSuite.validateDualBrainCoordinator).toBe('function');
      expect(typeof validationSuite.validateSystemIntegration).toBe('function');
    });
  });
});