/**
 * System 2 Tool Calling Integration Tests
 * 
 * Tests the enhanced SystemInvoker with agent service integration
 * for proper tool calling in System 2 (Reasoning Brain)
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SystemInvoker } from '@/agent/arch/dualbrain/services/SystemInvoker.js';
import { DualBrainCoordinator } from '@/agent/arch/dualbrain/DualBrainCoordinator.js';

// Mock the logger to avoid console spam during tests
vi.mock('@/utils/logger.ts', () => ({
  createLogger: () => ({
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    setLogLevel: vi.fn()
  }),
  LogLevelValues: { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3 },
  LogLevel: { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3 }
}));

describe('System 2 Tool Calling Integration', () => {
  let systemInvoker;
  let mockAgentService;
  let mockSystem2Model;

  beforeEach(() => {
    // Create mock System 2 model
    mockSystem2Model = {
      invoke: vi.fn(),
      constructor: { name: 'AliyunHttpChatModel' }
    };

    // Create mock agent service with tools
    mockAgentService = {
      processInput: vi.fn(),
      tools: [
        { name: 'control_avatar_speech', func: vi.fn() },
        { name: 'select_animation', func: vi.fn() },
        { name: 'speak_response', func: vi.fn() }
      ]
    };

    // Initialize SystemInvoker
    systemInvoker = new SystemInvoker({
      validateInputs: false,
      validateOutputs: false
    });

    systemInvoker.configureModels({
      system2: mockSystem2Model
    });

    systemInvoker.setAgentService(mockAgentService);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Agent Service Integration', () => {
    it('should configure agent service for tool calling', () => {
      expect(systemInvoker.agentService).toBe(mockAgentService);
      expect(systemInvoker.agentService.tools).toHaveLength(3);
    });

    it('should use agent service when enableToolCalling is true', async () => {
      // Mock agent service response with tool calls
      const mockAgentResponse = {
        content: 'I can help you with that!',
        tool_calls: [
          {
            function: {
              name: 'control_avatar_speech',
              arguments: JSON.stringify({
                action: 'speak',
                text: 'Hello there!',
                voice: 'Serena'
              })
            }
          }
        ]
      };

      mockAgentService.processInput.mockResolvedValue(mockAgentResponse);

      const request = {
        input: 'Can you speak to me?',
        context: { enableToolCalling: true }
      };

      const result = await systemInvoker.invokeSystem2(request);

      expect(result.status).toBe('success');
      expect(result.data.metadata.method).toBe('agent_service_with_tools');
      expect(result.data.metadata.toolCallsExecuted).toBe(1);
      expect(mockAgentService.processInput).toHaveBeenCalledWith(
        'Can you speak to me?',
        expect.objectContaining({
          useSystem2: true,
          enableTools: true,
          streaming: false,
          dualBrainContext: expect.objectContaining({
            isSystem2: true,
            supervisorRouting: true,
            enableToolCalling: true
          })
        })
      );
    });

    it('should fallback to direct model when agent service fails', async () => {
      // Mock agent service to throw error
      mockAgentService.processInput.mockRejectedValue(new Error('Agent service failed'));
      
      // Mock direct model response
      const mockDirectResponse = { content: 'Direct response' };
      mockSystem2Model.invoke.mockResolvedValue(mockDirectResponse);

      const request = {
        input: 'Test fallback',
        context: { enableToolCalling: true }
      };

      const result = await systemInvoker.invokeSystem2(request);

      expect(result.status).toBe('success');
      expect(mockAgentService.processInput).toHaveBeenCalled();
      expect(mockSystem2Model.invoke).toHaveBeenCalled();
    });

    it('should use direct model when enableToolCalling is false', async () => {
      const mockDirectResponse = { content: 'Direct model response' };
      mockSystem2Model.invoke.mockResolvedValue(mockDirectResponse);

      const request = {
        input: 'Simple query',
        context: { enableToolCalling: false }
      };

      const result = await systemInvoker.invokeSystem2(request);

      expect(result.status).toBe('success');
      expect(mockAgentService.processInput).not.toHaveBeenCalled();
      expect(mockSystem2Model.invoke).toHaveBeenCalled();
    });
  });

  describe('Tool Call Response Processing', () => {
    it('should correctly process multiple tool calls', async () => {
      const mockAgentResponse = {
        content: 'I can dance and speak!',
        tool_calls: [
          {
            function: {
              name: 'select_animation',
              arguments: JSON.stringify({ animationQuery: 'happy dance' })
            }
          },
          {
            function: {
              name: 'control_avatar_speech',
              arguments: JSON.stringify({
                action: 'speak',
                text: 'Watch me dance!'
              })
            }
          }
        ]
      };

      mockAgentService.processInput.mockResolvedValue(mockAgentResponse);

      const request = {
        input: 'Can you dance and tell me about it?',
        context: { enableToolCalling: true }
      };

      const result = await systemInvoker.invokeSystem2(request);

      expect(result.status).toBe('success');
      expect(result.data.metadata.toolCallsExecuted).toBe(2);
      expect(result.data.data.tool_calls).toHaveLength(2);
    });

    it('should handle agent response without tool calls', async () => {
      const mockAgentResponse = {
        content: 'Just a text response',
        tool_calls: []
      };

      mockAgentService.processInput.mockResolvedValue(mockAgentResponse);

      const request = {
        input: 'Tell me something',
        context: { enableToolCalling: true }
      };

      const result = await systemInvoker.invokeSystem2(request);

      expect(result.status).toBe('success');
      expect(result.data.metadata.toolCallsExecuted).toBe(0);
    });
  });

  describe('Message Format Handling', () => {
    it('should extract content from message array format', async () => {
      const mockAgentResponse = {
        content: 'Response from array format',
        tool_calls: []
      };

      mockAgentService.processInput.mockResolvedValue(mockAgentResponse);

      const messageArray = [
        { role: 'user', content: 'Hello from array format' }
      ];

      const request = {
        input: 'ignored when array provided',
        context: { enableToolCalling: true }
      };

      // Directly test the internal method with message array
      await systemInvoker._invokeSystem2Model(messageArray, { enableToolCalling: true });

      expect(mockAgentService.processInput).toHaveBeenCalledWith(
        'Hello from array format',
        expect.any(Object)
      );
    });

    it('should handle string input format', async () => {
      const mockAgentResponse = {
        content: 'Response from string format',
        tool_calls: []
      };

      mockAgentService.processInput.mockResolvedValue(mockAgentResponse);

      const stringInput = 'Direct string input';

      // Directly test the internal method with string
      await systemInvoker._invokeSystem2Model(stringInput, { enableToolCalling: true });

      expect(mockAgentService.processInput).toHaveBeenCalledWith(
        'Direct string input',
        expect.any(Object)
      );
    });
  });

  describe('Error Handling', () => {
    it('should throw error when System 2 model is not initialized', async () => {
      const uninitializedInvoker = new SystemInvoker();
      
      await expect(
        uninitializedInvoker._invokeSystem2Model('test', { enableToolCalling: true })
      ).rejects.toThrow('System 2 model not initialized');
    });

    it('should handle malformed requests gracefully', async () => {
      const mockAgentResponse = { content: 'Graceful handling' };
      mockAgentService.processInput.mockResolvedValue(mockAgentResponse);

      const malformedRequest = null;

      const result = await systemInvoker._invokeSystem2Model(
        malformedRequest, 
        { enableToolCalling: true }
      );

      expect(result.data).toBe(mockAgentResponse);
      expect(mockAgentService.processInput).toHaveBeenCalledWith(
        'null',
        expect.any(Object)
      );
    });
  });
});

describe('DualBrainCoordinator Tool Calling Integration', () => {
  let coordinator;
  let mockAgentService;

  beforeEach(() => {
    mockAgentService = {
      isDualBrainMode: vi.fn().mockReturnValue(true),
      getModel: vi.fn().mockImplementation((type) => ({
        constructor: { name: `Mock${type}Model` }
      })),
      tools: [
        { name: 'control_avatar_speech' },
        { name: 'select_animation' }
      ]
    };

    coordinator = new DualBrainCoordinator(mockAgentService, {
      enableProactiveDecisions: true
    });
  });

  describe('SystemInvoker Configuration', () => {
    it('should configure SystemInvoker with agent service during initialization', async () => {
      // Mock services creation
      coordinator.services = {
        systemInvoker: {
          initialize: vi.fn(),
          configureModels: vi.fn(),
          setAgentService: vi.fn()
        }
      };

      const result = await coordinator.initialize();

      expect(result).toBe(true);
      expect(coordinator.services.systemInvoker.setAgentService).toHaveBeenCalledWith(
        mockAgentService
      );
    });

    it('should verify tool availability during initialization', async () => {
      coordinator.services = {
        systemInvoker: {
          initialize: vi.fn(),
          configureModels: vi.fn(),
          setAgentService: vi.fn()
        }
      };

      await coordinator.initialize();

      expect(mockAgentService.tools.length).toBe(2);
    });
  });

  describe('Routing with Tool Calling Capabilities', () => {
    it('should route complex queries to System 2 with tool capabilities', () => {
      const routing = coordinator._routeToAppropriateSystem(
        'Can you dance for me and explain what you are doing?',
        { requiresReasoning: true }
      );

      expect(routing.targetSystem).toBe('system2');
      expect(routing.capabilities).toContain('tools');
      expect(routing.capabilities).toContain('speaking');
      expect(routing.capabilities).toContain('thinking');
    });

    it('should route proactive decisions to System 2 with tool capabilities', () => {
      const routing = coordinator._routeToAppropriateSystem(
        'Proactive engagement needed',
        { isProactive: true }
      );

      expect(routing.targetSystem).toBe('system2');
      expect(routing.capabilities).toContain('tools');
      expect(routing.capabilities).toContain('speaking');
    });

    it('should route simple queries to System 1 without tool capabilities', () => {
      const routing = coordinator._routeToAppropriateSystem(
        'Hi',
        { complexity: 'low' }
      );

      expect(routing.targetSystem).toBe('system1');
      expect(routing.capabilities).not.toContain('tools');
      expect(routing.capabilities).toContain('audioOutput');
    });
  });
});