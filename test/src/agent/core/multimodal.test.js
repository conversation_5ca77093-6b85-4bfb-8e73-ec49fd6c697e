/**
 * LangGraphAgentService Multimodal Tests
 * Tests multimodal functionality including audio, video, and mixed input processing
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { LangGraphAgentService } from '@/agent/core.js';

describe('LangGraphAgentService - Multimodal', () => {
  let agentService;

  beforeEach(async () => {
    agentService = new LangGraphAgentService({
      provider: 'aliyun',
      model: 'qwen-omni-turbo',
      applicationType: 'multimodal-assistant'
    });
  });

  afterEach(async () => {
    if (agentService) {
      await agentService.dispose();
    }
  });

  describe('Audio Input Processing', () => {
    it('should handle audio input messages', async () => {
      // Mock audio data
      const audioMessage = {
        role: 'user',
        content: 'Test audio message',
        input_audio: 'base64-encoded-audio-data'
      };

      // Test that agent can process audio input
      expect(() => {
        agentService.validateRequest({
          messages: [audioMessage],
          modalities: ['text', 'audio']
        });
      }).not.toThrow();
    });

    it('should initialize realtime audio capabilities', async () => {
      await agentService.initialize();

      // Check if realtime interface is available
      const hasRealtimeCapability = agentService.isRealtimeModeActive !== undefined;
      expect(hasRealtimeCapability).toBe(true);
    });
  });

  describe('Mixed Modality Processing', () => {
    it('should handle text and audio combination', async () => {
      const mixedMessage = {
        role: 'user',
        content: [
          { type: 'text', text: 'Hello, can you hear this audio?' },
          { type: 'audio', data: 'base64-audio-data' }
        ]
      };

      expect(() => {
        agentService.validateRequest({
          messages: [mixedMessage],
          modalities: ['text', 'audio']
        });
      }).not.toThrow();
    });
  });

  describe('Media Capture Integration', () => {
    it('should support media capture connection', async () => {
      await agentService.initialize();

      // Mock MediaCaptureManager
      const mockMediaCapture = {
        isCapturing: vi.fn().mockReturnValue(false),
        startCapture: vi.fn(),
        stopCapture: vi.fn()
      };

      // Test connection method exists
      expect(typeof agentService.connectMediaCapture).toBe('function');
    });
  });

  describe('Streaming with Multimodal Data', () => {
    it('should handle streaming with audio output', async () => {
      await agentService.initialize();

      const streamOptions = {
        stream: true,
        modalities: ['text', 'audio'],
        audioConfig: {
          voice: 'female',
          format: 'pcm16'
        }
      };

      // Test that streaming manager can handle multimodal options
      const streamingManager = agentService.getStreamingManager();
      expect(streamingManager).toBeDefined();
    });
  });
});
