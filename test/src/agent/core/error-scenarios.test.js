/**
 * Error <PERSON><PERSON> Tests
 * 
 * Tests various error scenarios and edge cases related to realtime session timing,
 * ensuring robust error handling and graceful degradation when timing constraints fail.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock dependencies
vi.mock('../../../src/utils/logger.js', () => ({
  createLogger: vi.fn(() => ({
    info: vi.fn(),
    debug: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
  })),
  LogLevel: { DEBUG: 'debug', INFO: 'info' },
  setModuleLogLevel: vi.fn()
}));

vi.mock('../../../src/media/modality/audio.ts', () => ({
  RealtimeAudioManager: vi.fn(() => ({ resetSession: vi.fn() }))
}));

vi.mock('../../../src/agent/models/aliyun/AliyunConfig.js', () => ({
  ALIYUN_AUDIO_CONFIG: { bitDepth: 16, channels: 1, numChannels: 1, minIntervalMs: 100 },
  ALIYUN_VAD_CONFIG: { type: 'server_vad', threshold: 0.5, silence_duration_ms: 1500 },
  ALIYUN_WEBSOCKET_CONFIG: { endpoint: 'wss://test.endpoint', defaultModel: 'qwen-omni-turbo' },
  ALIYUN_SAMPLE_RATE: 16000,
  AliyunEventType: { SESSION_CREATED: 'session.created', SESSION_UPDATED: 'session.updated' },
  generateEventId: vi.fn(() => 'test-event-id'),
  cleanupRealtimeConnection: vi.fn(),
  createPythonCompatibleSessionUpdate: vi.fn(() => ({ type: 'session.update' })),
  validateAudioConfig: vi.fn(() => ({ isValid: true, warnings: [], errors: [] }))
}));

vi.mock('../../../src/utils/portManager.js', () => ({
  getDownloadServerPort: vi.fn(() => 3001)
}));

vi.mock('../../../src/agent/models/base/WebSocketChatModel.js', () => ({
  WebSocketChatModel: vi.fn(() => ({
    apiKey: 'test-key', model: 'qwen-omni-turbo',
    logger: { info: vi.fn(), debug: vi.fn(), warn: vi.fn(), error: vi.fn() },
    contextBuffer: { conversation: {} }, recentTranscripts: [],
    addTranscript: vi.fn(), _checkApiLimits: vi.fn(() => true),
    _recordApiCost: vi.fn(), _arrayBufferToBase64: vi.fn(),
    _stopContinuousContextAnalysis: vi.fn()
  }))
}));

describe('Error Scenario Timing Tests', () => {
  let AliyunWebSocketChatModel;
  let DualBrainCoordinator;
  let system1Model;
  let coordinator;
  let mockAgentService;

  beforeEach(async () => {
    vi.clearAllMocks();

    // Import modules after mocks
    const aliyunModule = await import('../../../src/agent/models/aliyun/AliyunWebSocketChatModel.js');
    AliyunWebSocketChatModel = aliyunModule.AliyunWebSocketChatModel;

    const coordinatorModule = await import('../../../src/agent/arch/dualbrain/DualBrainCoordinator.js');
    DualBrainCoordinator = coordinatorModule.DualBrainCoordinator;

    // Create model
    system1Model = new AliyunWebSocketChatModel({
      apiKey: 'test-key',
      model: 'qwen-omni-turbo'
    });

    // Mock agent service
    mockAgentService = {
      getModel: vi.fn((type) => {
        if (type === 'system1') return system1Model;
        if (type === 'system2') return { constructor: { name: 'HttpModel' } };
        return null;
      }),
      isDualBrainMode: vi.fn(() => true),
      generateResponse: vi.fn(() => Promise.resolve('test response')),
      options: { agentConfig: { enableDualBrain: true } }
    };

    // Create coordinator
    coordinator = new DualBrainCoordinator(mockAgentService, {
      system2AnalysisInterval: 1000,
      decisionCooldown: 500,
      enableProactiveDecisions: true
    });

    coordinator.isInitialized = true;
  });

  afterEach(async () => {
    if (coordinator && coordinator.isActive) {
      await coordinator.stopDualBrainSystems();
    }
    if (system1Model) {
      system1Model.closeRealtimeMode();
    }
  });

  describe('WebSocket Connection Errors', () => {
    it('should handle WebSocket creation failure', () => {
      // Mock WebSocket to throw on creation
      global.WebSocket = vi.fn(() => {
        throw new Error('WebSocket creation failed');
      });

      system1Model.realtimeSocket = null;
      system1Model.realtimeSessionStabilized = false;

      expect(system1Model.isRealtimeModeActive()).toBe(false);
      expect(() => system1Model.isRealtimeModeActive()).not.toThrow();
    });

    it('should handle WebSocket ready state corruption', () => {
      // Mock WebSocket with corrupted ready state
      system1Model.realtimeSocket = {
        readyState: 'invalid', // Should be a number
        send: vi.fn(),
        close: vi.fn()
      };
      system1Model.realtimeSessionStabilized = true;

      const result = system1Model.isRealtimeModeActive();
      expect(result).toBe(false); // Should handle gracefully
    });

    it('should handle WebSocket with undefined properties', () => {
      system1Model.realtimeSocket = {
        // Missing readyState property
        send: vi.fn(),
        close: vi.fn()
      };
      system1Model.realtimeSessionStabilized = true;

      expect(() => system1Model.isRealtimeModeActive()).not.toThrow();
      expect(system1Model.isRealtimeModeActive()).toBe(false);
    });

    it('should handle WebSocket close during readiness check', async () => {
      const mockWebSocket = {
        readyState: 1, // Initially OPEN
        send: vi.fn(),
        close: vi.fn()
      };

      system1Model.realtimeSocket = mockWebSocket;
      system1Model.realtimeSessionStabilized = false;

      // Simulate WebSocket closing during wait
      setTimeout(() => {
        mockWebSocket.readyState = 3; // CLOSED
      }, 100);

      const result = await system1Model.waitForRealtimeReady(500);
      expect(result).toBe(false);
    });

    it('should handle WebSocket send errors gracefully', async () => {
      const mockWebSocket = {
        readyState: 1,
        send: vi.fn(() => {
          throw new Error('Send failed');
        }),
        close: vi.fn()
      };

      system1Model.realtimeSocket = mockWebSocket;
      system1Model.realtimeSessionStabilized = true;

      // Should not throw when WebSocket send fails
      expect(() => system1Model._sendRealtimeMessage({ type: 'test' })).not.toThrow();
    });
  });

  describe('Session Stabilization Errors', () => {
    it('should handle rapid session state flapping', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      system1Model.realtimeSocket = mockWebSocket;
      system1Model.realtimeSessionStabilized = false;

      // Simulate rapid state flapping
      let flipCount = 0;
      const interval = setInterval(() => {
        system1Model.realtimeSessionStabilized = !system1Model.realtimeSessionStabilized;
        flipCount++;
        if (flipCount >= 20) {
          clearInterval(interval);
          system1Model.realtimeSessionStabilized = true; // Final stable state
        }
      }, 10);

      const result = await system1Model.waitForRealtimeReady(1000);
      expect(result).toBe(true);
      clearInterval(interval);
    });

    it('should handle session stabilization timeout', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      system1Model.realtimeSocket = mockWebSocket;
      system1Model.realtimeSessionStabilized = false;

      const startTime = Date.now();
      const result = await system1Model.waitForRealtimeReady(200); // Short timeout
      const elapsed = Date.now() - startTime;

      expect(result).toBe(false);
      expect(elapsed).toBeGreaterThanOrEqual(200);
      expect(system1Model.logger.warn).toHaveBeenCalledWith(
        '⚠️ [RealtimeReady] Timeout waiting for session stabilization'
      );
    });

    it('should handle coordinator startup when session never stabilizes', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      system1Model.realtimeSocket = mockWebSocket;
      system1Model.realtimeSessionStabilized = false;

      // Mock waitForRealtimeReady to always timeout
      system1Model.waitForRealtimeReady = vi.fn(() => Promise.resolve(false));

      const result = await coordinator.startDualBrainSystems();

      expect(result).toBe(true); // Should still start
      expect(coordinator.isActive).toBe(true);
      expect(coordinator.logger.warn).toHaveBeenCalledWith(
        expect.stringContaining('Timeout waiting for System 1 realtime mode')
      );
    });

    it('should handle session state corruption', () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      system1Model.realtimeSocket = mockWebSocket;
      
      // Corrupt session state
      system1Model.realtimeSessionStabilized = 'invalid'; // Should be boolean

      const result = system1Model.isRealtimeModeActive();
      expect(result).toBe(true); // Should still work due to truthy value
    });
  });

  describe('Coordinator Error Scenarios', () => {
    it('should handle missing System 1 model gracefully', async () => {
      mockAgentService.getModel.mockReturnValue(null);

      await coordinator._ensureSystem1RealtimeReady();

      expect(coordinator.logger.warn).toHaveBeenCalledWith(
        '⚠️ System 1 model not available - dual brain functionality will be limited'
      );
    });

    it('should handle System 1 model with missing methods', async () => {
      const incompleteModel = {
        constructor: { name: 'IncompleteModel' }
        // Missing isRealtimeModeActive method
      };

      mockAgentService.getModel.mockReturnValue(incompleteModel);

      await coordinator._ensureSystem1RealtimeReady();

      expect(coordinator.logger.debug).toHaveBeenCalledWith(
        'ℹ️ System 1 model does not support realtime mode - skipping readiness check'
      );
    });

    it('should handle System 1 model method throwing exceptions', async () => {
      system1Model.isRealtimeModeActive = vi.fn(() => {
        throw new Error('Method call failed');
      });

      await coordinator._ensureSystem1RealtimeReady();

      expect(coordinator.logger.error).toHaveBeenCalledWith(
        '❌ Error ensuring System 1 realtime readiness:',
        expect.any(Error)
      );
    });

    it('should handle proactive decision generation errors', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      system1Model.realtimeSocket = mockWebSocket;
      system1Model.realtimeSessionStabilized = true;
      
      coordinator.isActive = true;
      coordinator.lastDecisionTime = 0;

      // Mock generateResponse to fail
      mockAgentService.generateResponse.mockRejectedValue(new Error('API call failed'));

      const decision = await coordinator.generateProactiveDecision();

      expect(decision.shouldAct).toBe(false);
      expect(decision.reason).toBe('decision_error');
      expect(decision.error).toBe('API call failed');
    });

    it('should handle System 2 unavailable during proactive decisions', async () => {
      coordinator.isActive = true;
      coordinator.lastDecisionTime = 0;

      // Mock System 2 not available
      mockAgentService.getModel.mockImplementation((type) => {
        if (type === 'system2') return null;
        return system1Model;
      });

      await expect(coordinator._invokeSystem2('test')).rejects.toThrow(
        'System 2 model not available from agent service'
      );
    });

    it('should handle agent service unavailable', async () => {
      coordinator.agentService = null;

      await expect(() => coordinator._invokeSystem1('test')).toThrow();
      await expect(() => coordinator._invokeSystem2('test')).toThrow();
    });
  });

  describe('Timing and Race Condition Errors', () => {
    it('should handle concurrent readiness checks', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      system1Model.realtimeSocket = mockWebSocket;
      system1Model.realtimeSessionStabilized = false;

      // Start multiple concurrent readiness checks
      const promises = Array(10).fill(null).map(() => 
        coordinator._ensureSystem1RealtimeReady()
      );

      // Stabilize after some checks have started
      setTimeout(() => {
        system1Model.realtimeSessionStabilized = true;
      }, 100);

      const results = await Promise.allSettled(promises);
      expect(results.every(result => result.status === 'fulfilled')).toBe(true);
    });

    it('should handle timeout during fallback polling', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      system1Model.realtimeSocket = mockWebSocket;
      system1Model.realtimeSessionStabilized = false;

      // Remove waitForRealtimeReady to trigger fallback
      system1Model.waitForRealtimeReady = undefined;

      const startTime = Date.now();
      await coordinator._ensureSystem1RealtimeReady();
      const elapsed = Date.now() - startTime;

      expect(elapsed).toBeGreaterThanOrEqual(10000); // Should wait full timeout
      expect(coordinator.logger.warn).toHaveBeenCalledWith(
        '⚠️ Timeout waiting for System 1 realtime mode to become active'
      );
    });

    it('should handle rapid start/stop cycles', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      system1Model.realtimeSocket = mockWebSocket;
      system1Model.realtimeSessionStabilized = true;

      // Rapid start/stop cycles
      for (let i = 0; i < 5; i++) {
        await coordinator.startDualBrainSystems();
        expect(coordinator.isActive).toBe(true);
        
        await coordinator.stopDualBrainSystems();
        expect(coordinator.isActive).toBe(false);
      }
    });

    it('should handle memory pressure during timing operations', async () => {
      // Create memory pressure
      const largeArrays = Array(100).fill(null).map((_, i) => 
        new Array(10000).fill(i)
      );

      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      system1Model.realtimeSocket = mockWebSocket;
      system1Model.realtimeSessionStabilized = false;

      setTimeout(() => {
        system1Model.realtimeSessionStabilized = true;
      }, 100);

      const result = await system1Model.waitForRealtimeReady(1000);

      expect(result).toBe(true);

      // Cleanup memory
      largeArrays.length = 0;
    });

    it('should handle system clock changes during timing', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      system1Model.realtimeSocket = mockWebSocket;
      system1Model.realtimeSessionStabilized = false;

      // Mock Date.now to simulate clock change
      const originalDateNow = Date.now;
      let clockOffset = 0;
      Date.now = vi.fn(() => originalDateNow() + clockOffset);

      const waitPromise = system1Model.waitForRealtimeReady(1000);

      // Simulate clock jumping forward during wait
      setTimeout(() => {
        clockOffset = 5000; // Jump forward 5 seconds
        system1Model.realtimeSessionStabilized = true;
      }, 200);

      const result = await waitPromise;

      // Should still work despite clock change
      expect(result).toBe(true);

      // Restore original Date.now
      Date.now = originalDateNow;
    });
  });

  describe('Resource Cleanup Errors', () => {
    it('should handle cleanup when resources are already null', () => {
      system1Model.realtimeSocket = null;
      system1Model.realtimeSessionStabilized = false;

      expect(() => system1Model.closeRealtimeMode()).not.toThrow();
    });

    it('should handle cleanup when WebSocket close throws', () => {
      const mockWebSocket = {
        readyState: 1,
        send: vi.fn(),
        close: vi.fn(() => {
          throw new Error('Close failed');
        })
      };

      system1Model.realtimeSocket = mockWebSocket;

      expect(() => system1Model.closeRealtimeMode()).not.toThrow();
    });

    it('should handle coordinator disposal during active operations', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      system1Model.realtimeSocket = mockWebSocket;
      system1Model.realtimeSessionStabilized = true;

      await coordinator.startDualBrainSystems();

      // Start a long-running operation
      const operationPromise = coordinator.generateProactiveDecision();

      // Dispose during operation
      coordinator.dispose();

      // Operation should still complete
      const result = await operationPromise;
      expect(result).toBeDefined();
    });

    it('should handle multiple disposal calls', () => {
      expect(() => {
        coordinator.dispose();
        coordinator.dispose();
        coordinator.dispose();
      }).not.toThrow();
    });
  });

  describe('Edge Case Scenarios', () => {
    it('should handle extremely rapid state changes', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      system1Model.realtimeSocket = mockWebSocket;

      // Change state every millisecond
      let changeCount = 0;
      const interval = setInterval(() => {
        system1Model.realtimeSessionStabilized = !system1Model.realtimeSessionStabilized;
        changeCount++;
        if (changeCount >= 100) {
          clearInterval(interval);
          system1Model.realtimeSessionStabilized = true;
        }
      }, 1);

      const result = await system1Model.waitForRealtimeReady(1000);
      expect(result).toBe(true);
      clearInterval(interval);
    });

    it('should handle zero timeout scenarios', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      system1Model.realtimeSocket = mockWebSocket;
      system1Model.realtimeSessionStabilized = false;

      const result = await system1Model.waitForRealtimeReady(0);
      expect(result).toBe(false);
    });

    it('should handle negative timeout scenarios', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      system1Model.realtimeSocket = mockWebSocket;
      system1Model.realtimeSessionStabilized = false;

      const result = await system1Model.waitForRealtimeReady(-1000);
      expect(result).toBe(false);
    });

    it('should handle very large timeout values', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      system1Model.realtimeSocket = mockWebSocket;
      system1Model.realtimeSessionStabilized = false;

      setTimeout(() => {
        system1Model.realtimeSessionStabilized = true;
      }, 100);

      const result = await system1Model.waitForRealtimeReady(Number.MAX_SAFE_INTEGER);
      expect(result).toBe(true);
    });
  });
});