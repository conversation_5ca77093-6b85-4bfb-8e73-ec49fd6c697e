/**
 * Test suite for LangGraphAgentService - Tool Integration Tests
 * Tests tool execution with LangGraph ToolNode, animation tools, and tool management
 */

// Import mocks
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';

// Import the class to test
import { LangGraphAgentService } from '@/agent/core.js';
import { agentTTSTool } from '@/agent/tools/avatar/speaking.js';

// Mock logger
vi.mock('../../../src/utils/logger.js', () => ({
    createLogger: vi.fn(() => ({
        info: vi.fn(),
        debug: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        setLogLevel: vi.fn()
    })),
    LogLevel: { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3, NONE: 4 }
}));

// Mock API proxy
vi.mock('../../../src/utils/apiProxy.js', () => ({
    fetchVllmApi: vi.fn()
}));

describe('LangGraphAgentService - Tool Integration', () => {
    let agentService;
    let mockTTSService;
    let mockAudioPlayer;
    let mockAnimationController;

    beforeEach(async () => {
        vi.clearAllMocks();

        // Create mock services
        mockTTSService = {
            speak: vi.fn().mockResolvedValue({ success: true }),
            constructor: { name: 'MockTTSService' }
        };

        mockAudioPlayer = {
            playChunk: vi.fn().mockResolvedValue(true)
        };

        mockAnimationController = {
            triggerAnimation: vi.fn().mockResolvedValue({ success: true })
        };

        agentService = new LangGraphAgentService({
            autoRegisterTools: true,
            services: {
                ttsService: mockTTSService,
                audioPlayer: mockAudioPlayer,
                animationController: mockAnimationController
            }
        });

        await agentService.initialize();
    });

    afterEach(() => {
        if (agentService && typeof agentService.dispose === 'function') {
            agentService.dispose();
        }
    });

    describe('ToolNode Integration', () => {
        it('should create service-aware ToolNode on initialization', () => {
            expect(agentService.toolNode).toBeDefined();
            expect(typeof agentService.toolNode.invoke).toBe('function');
        });

        it('should register TTS and animation tools', () => {
            const toolNames = agentService.tools.map(t => t.name);
            expect(toolNames).toContain('speak_response');
            expect(toolNames).toContain('select_animation');
            expect(agentService.tools.length).toBeGreaterThan(0);
        });

        it('should bind tools to LLM for automatic tool calling', () => {
            expect(agentService.model).toBeDefined();
            expect(agentService.tools.length).toBeGreaterThan(0);
        });
    });

    describe('Tool Execution via ToolNode', () => {
        it('should execute TTS tool through ToolNode', async () => {
            // Mock state with TTS tool call
            const mockInput = {
                messages: [{
                    role: 'assistant',
                    content: 'I will speak this text',
                    tool_calls: [{
                        name: 'speak_response',
                        args: { text: 'Hello world' },
                        id: 'call_123',
                        type: 'tool_call'
                    }]
                }]
            };

            // Execute ToolNode
            const result = await agentService.toolNode.invoke(mockInput);

            // Verify ToolNode execution
            expect(result).toBeDefined();
            expect(result.messages).toBeDefined();
            expect(Array.isArray(result.messages)).toBe(true);
        });

        it('should pass services to tools via ToolNode config', async () => {
            const mockInput = {
                messages: [{
                    role: 'assistant',
                    content: '',
                    tool_calls: [{
                        name: 'speak_response',
                        args: { text: 'Test message' },
                        id: 'call_456',
                        type: 'tool_call'
                    }]
                }]
            };

            // Execute and verify services are available
            await agentService.toolNode.invoke(mockInput);

            // Since the ToolNode wrapper injects services, the TTS service should be called
            // This is verified indirectly through the tool execution
            expect(agentService.services.ttsService).toBeDefined();
            expect(agentService.services.audioPlayer).toBeDefined();
        });

        it('should handle tool execution errors gracefully', async () => {
            // Mock TTS service error
            mockTTSService.speak.mockRejectedValue(new Error('TTS service unavailable'));

            const mockInput = {
                messages: [{
                    role: 'assistant',
                    content: '',
                    tool_calls: [{
                        name: 'speak_response',
                        args: { text: 'Error test' },
                        id: 'call_error',
                        type: 'tool_call'
                    }]
                }]
            };

            // Should not throw but handle error gracefully
            const result = await agentService.toolNode.invoke(mockInput);
            expect(result).toBeDefined();
        });
    });

    describe('Animation Tool Integration', () => {
        it('should execute animation tools through ToolNode', async () => {
            const mockInput = {
                messages: [{
                    role: 'assistant',
                    content: 'I will show a greeting animation',
                    tool_calls: [{
                        name: 'select_animation',
                        args: { animation: 'greeting', context: 'user_greeting' },
                        id: 'call_anim',
                        type: 'tool_call'
                    }]
                }]
            };

            const result = await agentService.toolNode.invoke(mockInput);
            expect(result).toBeDefined();
            expect(result.messages).toBeDefined();
        });
    });

    describe('LangGraph Workflow Integration', () => {
        it('should route to ToolNode when tool calls detected', () => {
            const stateWithTools = {
                messages: [{
                    tool_calls: [{ name: 'speak_response', args: { text: 'test' } }]
                }]
            };

            const route = agentService._routeAfterGeneration(stateWithTools);
            expect(route).toBe('executeTools');
        });

        it('should skip ToolNode when no tool calls', () => {
            const stateWithoutTools = {
                messages: [{
                    content: 'Regular response',
                    tool_calls: []
                }]
            };

            const route = agentService._routeAfterGeneration(stateWithoutTools);
            expect(route).toBe('updateAgent');
        });

        it('should integrate ToolNode in complete workflow', async () => {
            // Mock LLM response with tool calls
            const mockResponse = {
                content: 'I will speak this message',
                tool_calls: [{
                    name: 'speak_response',
                    args: { text: 'Hello from agent' },
                    id: 'workflow_call'
                }]
            };

            // Mock the LLM invoke method
            agentService.model.invoke = vi.fn().mockResolvedValue(mockResponse);

            // Test complete workflow
            const result = await agentService.generateResponse('Say hello', {
                sessionId: 'test-workflow',
                stream: false
            });

            expect(result).toBeDefined();
            expect(typeof result).toBe('string');
        });
    });

    describe('Service Injection', () => {
        it('should inject services into ToolNode config', async () => {
            const originalInvoke = agentService.toolNode.invoke;
            let capturedConfig;

            // Spy on ToolNode invoke to capture config
            agentService.toolNode.invoke = vi.fn().mockImplementation(async (input, config) => {
                capturedConfig = config;
                return { messages: [] };
            });

            const mockInput = {
                messages: [{
                    tool_calls: [{ name: 'speak_response', args: { text: 'test' } }]
                }]
            };

            await agentService.toolNode.invoke(mockInput);

            expect(capturedConfig).toBeDefined();
            expect(capturedConfig.ttsService).toBe(mockTTSService);
            expect(capturedConfig.audioPlayer).toBe(mockAudioPlayer);
            expect(capturedConfig.animationController).toBe(mockAnimationController);
        });
    });
});
