/**
 * Integration Test Suite for LangGraph Agent Service with TalkingAvatar
 * Tests the complete integration flow from TalkingAvatar to LangGraph workflow
 */

import { describe, it, beforeEach, afterEach, expect, vi } from 'vitest';
import { LangGraphAgentService } from '@/agent/core.js';

// Mock dependencies
vi.mock('@/utils/logger.js', () => ({
    createLogger: vi.fn(() => ({
        info: vi.fn(),
        debug: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        setLogLevel: vi.fn()
    })),
    setModuleLogLevel: vi.fn(),
    LogLevel: {
        DEBUG: 0,
        INFO: 1,
        WARN: 2,
        ERROR: 3,
        NONE: 4
    }
}));

vi.mock('@/utils/apiProxy.js', () => ({
    fetchVllmApi: vi.fn()
}));

vi.mock('@/config/index.ts', () => ({
    config: {
        endpoints: {
            vllm: 'http://localhost:8000'
        }
    }
}));

// Mock TalkingAvatar-like interface
const createMockTalkingAvatar = () => ({
    llmService: 'agent',
    voiceConfig: {
        currentLanguage: 'english',
        currentGender: 'male',
        voiceMood: 'neutral'
    },
    services: {
        ttsService: {
            speak: vi.fn()
        },
        audioPlayer: {
            play: vi.fn()
        },
        animationController: {
            triggerAnimation: vi.fn()
        }
    },
    mediaRecorder: {
        isActive: false
    },
    agentService: null
});

describe('LangGraph Agent Service Integration with TalkingAvatar', () => {
    let agentService;
    let mockTalkingAvatar;
    let mockFetchVllmApi;

    beforeEach(() => {
        // Reset mocks
        vi.clearAllMocks();

        // Get the mock function
        const { fetchVllmApi } = require('../../../src/utils/apiProxy.js');
        mockFetchVllmApi = fetchVllmApi;

        // Create mock TalkingAvatar
        mockTalkingAvatar = createMockTalkingAvatar();

        // Create agent service with TalkingAvatar-like configuration
        agentService = new LangGraphAgentService({
            vllmEndpoint: 'http://test-endpoint:8000',
            temperature: 0.7,
            maxTokens: 2048,
            maxIterations: 10,
            enableMemory: true,
            maxRetries: 3,
            enableStructuredOutput: true,
            enableAdvancedStreaming: true,
            services: mockTalkingAvatar.services
        });

        // Attach agent service to mock TalkingAvatar
        mockTalkingAvatar.agentService = agentService;
    });

    afterEach(() => {
        if (agentService) {
            agentService.dispose();
        }
    });

    describe('TalkingAvatar Integration Scenarios', () => {
        beforeEach(async () => {
            // Mock successful initialization
            mockFetchVllmApi.mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ data: [] })
            });

            await agentService.initialize();
        });

        it('should handle TalkingAvatar text input flow', async () => {
            // Mock LLM response for regular chat
            mockFetchVllmApi.mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({
                    choices: [{
                        message: {
                            content: 'Hello! How can I help you today?'
                        }
                    }]
                })
            });

            // Simulate TalkingAvatar calling agent service
            const userInput = 'Hello, how are you?';
            const contextInfo = {
                language: mockTalkingAvatar.voiceConfig.currentLanguage,
                gender: mockTalkingAvatar.voiceConfig.currentGender,
                mood: mockTalkingAvatar.voiceConfig.voiceMood,
                sessionId: 'talking-avatar-session'
            };

            const response = await agentService.generateResponse(userInput, contextInfo);

            // Verify response matches TalkingAvatar expectations
            expect(response).toBeDefined();
            expect(typeof response.responseText).toBe('string');
            expect(response.responseText).toBe('Hello! How can I help you today?');
            expect(response.hasTools).toBe(false);
            expect(response.hasAnimation).toBe(false);
            expect(response.isStructured).toBe(false);
        });

        it('should handle TalkingAvatar animation request flow', async () => {
            // Mock LLM response that triggers animation
            mockFetchVllmApi.mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({
                    choices: [{
                        message: {
                            content: 'Sure! I would love to dance for you!'
                        }
                    }]
                })
            });

            const userInput = 'Can you dance for me?';
            const contextInfo = {
                language: 'english',
                gender: 'female',
                mood: 'happy',
                use_animation: true,
                sessionId: 'animation-session'
            };

            const response = await agentService.generateResponse(userInput, contextInfo);

            // Verify animation was triggered
            expect(response.responseText).toContain('dance');
            expect(response.hasAnimation).toBe(true);
            expect(response.workflowState.currentAnimation).toBeDefined();
            expect(response.toolResults).toContainEqual(
                expect.objectContaining({
                    tool: 'selectAnimation',
                    result: expect.objectContaining({
                        file: 'dance'
                    })
                })
            );
        });

        it('should handle TalkingAvatar multimodal input flow', async () => {
            // Mock LLM response for multimodal input
            mockFetchVllmApi.mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({
                    choices: [{
                        message: {
                            content: 'I understand you sent audio. Let me respond appropriately.'
                        }
                    }]
                })
            });

            // Simulate multimodal input from TalkingAvatar
            const multimodalInput = {
                text: 'Describe what you heard',
                audio: new ArrayBuffer(2048),
                mediaType: 'audio/wav',
                contextualInformation: {
                    currentEmotion: 'curious',
                    avatarState: {
                        isAnimating: false,
                        isSpeaking: false
                    }
                },
                asrMetadata: {
                    detectedLanguage: 'english',
                    detectedEmotion: 'curious',
                    confidence: 0.92
                }
            };

            const contextInfo = {
                language: 'english',
                gender: 'male',
                mood: 'curious',
                isAudio: true,
                mediaType: 'audio',
                sessionId: 'multimodal-session'
            };

            const response = await agentService.generateResponse(multimodalInput, contextInfo);

            // Verify multimodal processing
            expect(response.responseText).toContain('audio');
            expect(response.workflowState.isMultimodal).toBe(true);
        });

        it('should handle TalkingAvatar streaming flow', async () => {
            // Mock streaming LLM response
            const mockReader = {
                read: vi.fn()
                    .mockResolvedValueOnce({
                        done: false,
                        value: new TextEncoder().encode('data: {"choices":[{"delta":{"content":"Hello"}}]}\n\n')
                    })
                    .mockResolvedValueOnce({
                        done: false,
                        value: new TextEncoder().encode('data: {"choices":[{"delta":{"content":" there!"}}]}\n\n')
                    })
                    .mockResolvedValueOnce({
                        done: false,
                        value: new TextEncoder().encode('data: {"choices":[{"delta":{"content":" How can I help?"}}]}\n\n')
                    })
                    .mockResolvedValueOnce({
                        done: true,
                        value: null
                    }),
                releaseLock: vi.fn()
            };

            mockFetchVllmApi.mockResolvedValue({
                ok: true,
                body: { getReader: () => mockReader }
            });

            // Simulate TalkingAvatar streaming request
            const userInput = 'Tell me a story';
            const contextInfo = {
                language: 'english',
                gender: 'female',
                mood: 'storytelling',
                stream: true,
                sessionId: 'streaming-session'
            };

            const chunks = [];
            const streamGenerator = agentService.generateResponse(userInput, contextInfo);

            for await (const chunk of streamGenerator) {
                chunks.push(chunk);

                // Simulate TalkingAvatar processing chunks
                if (chunk.type === 'content' && chunk.content) {
                    // Would trigger TTS in real TalkingAvatar
                    expect(chunk.content).toBeDefined();
                }
            }

            expect(chunks.length).toBeGreaterThan(0);
            expect(chunks.some(chunk => chunk.type === 'content')).toBe(true);
        });

        it('should handle TalkingAvatar error scenarios gracefully', async () => {
            // Mock LLM failure
            mockFetchVllmApi.mockRejectedValue(new Error('LLM service unavailable'));

            const userInput = 'Hello';
            const contextInfo = {
                language: 'english',
                use_tools: true,
                use_animation: true,
                sessionId: 'error-session'
            };

            const response = await agentService.generateResponse(userInput, contextInfo);

            // Verify graceful fallback
            expect(response).toBeDefined();
            expect(response.fallback).toBe(true);
            expect(response.responseText).toBeDefined();
            expect(response.hasTools).toBe(false);
            expect(response.hasAnimation).toBe(false);
        });
    });

    describe('TalkingAvatar Service Integration', () => {
        beforeEach(async () => {
            mockFetchVllmApi.mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ data: [] })
            });

            await agentService.initialize();
        });

        it('should integrate with TTS service', async () => {
            mockFetchVllmApi.mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({
                    choices: [{
                        message: {
                            content: 'This message should trigger TTS.'
                        }
                    }]
                })
            });

            const response = await agentService.generateResponse('Say something', {
                use_tts: true,
                sessionId: 'tts-session'
            });

            expect(response.responseText).toBe('This message should trigger TTS.');
            expect(mockTalkingAvatar.services.ttsService.speak).toBeDefined();
        });

        it('should integrate with animation controller', async () => {
            mockFetchVllmApi.mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({
                    choices: [{
                        message: {
                            content: 'Let me wave at you!'
                        }
                    }]
                })
            });

            const response = await agentService.generateResponse('Wave at me', {
                use_animation: true,
                sessionId: 'animation-controller-session'
            });

            expect(response.hasAnimation).toBe(true);
            expect(mockTalkingAvatar.services.animationController.triggerAnimation).toBeDefined();
        });

        it('should integrate with audio player', async () => {
            mockFetchVllmApi.mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({
                    choices: [{
                        message: {
                            content: 'Playing audio response for you.'
                        }
                    }]
                })
            });

            const response = await agentService.generateResponse('Play something', {
                sessionId: 'audio-player-session'
            });

            expect(response.responseText).toBe('Playing audio response for you.');
            expect(mockTalkingAvatar.services.audioPlayer.play).toBeDefined();
        });
    });

    describe('State Management with TalkingAvatar Sessions', () => {
        beforeEach(async () => {
            mockFetchVllmApi.mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ data: [] })
            });

            await agentService.initialize();
        });

        it('should maintain conversation state across multiple interactions', async () => {
            // Mock LLM responses for conversation
            mockFetchVllmApi
                .mockResolvedValueOnce({
                    ok: true,
                    json: () => Promise.resolve({
                        choices: [{ message: { content: 'Nice to meet you, Alice!' } }]
                    })
                })
                .mockResolvedValueOnce({
                    ok: true,
                    json: () => Promise.resolve({
                        choices: [{ message: { content: 'Hello again, Alice! How can I help you today?' } }]
                    })
                });

            const sessionId = 'conversation-session';

            // First interaction
            const response1 = await agentService.generateResponse('My name is Alice', {
                sessionId,
                language: 'english'
            });

            expect(response1.responseText).toContain('Alice');

            // Get conversation state
            const state = await agentService.getState(sessionId);
            expect(state).toBeDefined();
            expect(state.messages.length).toBeGreaterThan(0);

            // Second interaction (should remember context)
            const response2 = await agentService.generateResponse('Hello again', {
                sessionId,
                language: 'english'
            });

            expect(response2.responseText).toContain('Alice');
        });

        it('should handle session cleanup and disposal', async () => {
            const sessionId = 'cleanup-session';

            // Create some state
            await agentService.generateResponse('Create some state', { sessionId });

            // Verify state exists
            const stateBefore = await agentService.getState(sessionId);
            expect(stateBefore).toBeDefined();

            // Dispose service
            agentService.dispose();

            // Verify cleanup
            expect(agentService._initialized).toBe(false);
        });
    });

    describe('Performance and Reliability', () => {
        beforeEach(async () => {
            mockFetchVllmApi.mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ data: [] })
            });

            await agentService.initialize();
        });

        it('should handle concurrent requests efficiently', async () => {
            // Mock consistent LLM responses
            mockFetchVllmApi.mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({
                    choices: [{
                        message: {
                            content: 'Concurrent response'
                        }
                    }]
                })
            });

            // Create multiple concurrent requests
            const promises = Array(5).fill(0).map((_, index) =>
                agentService.generateResponse(`Request ${index}`, {
                    sessionId: `concurrent-session-${index}`,
                    language: 'english'
                })
            );

            const responses = await Promise.all(promises);

            // Verify all requests completed successfully
            expect(responses).toHaveLength(5);
            responses.forEach(response => {
                expect(response.responseText).toBe('Concurrent response');
                expect(response.hasTools).toBeDefined();
                expect(response.hasAnimation).toBeDefined();
            });
        });

        it('should handle timeout scenarios', async () => {
            // Mock slow LLM response
            mockFetchVllmApi.mockImplementation(() =>
                new Promise(resolve => setTimeout(() => resolve({
                    ok: true,
                    json: () => Promise.resolve({
                        choices: [{ message: { content: 'Delayed response' } }]
                    })
                }), 100))
            );

            const startTime = Date.now();
            const response = await agentService.generateResponse('Test timeout', {
                sessionId: 'timeout-session',
                timeout: 5000
            });
            const endTime = Date.now();

            expect(response).toBeDefined();
            expect(endTime - startTime).toBeLessThan(5000);
        });

        it('should provide performance metrics', () => {
            const config = agentService.getConfig();
            expect(config).toEqual({
                vllmEndpoint: expect.any(String),
                temperature: 0.7,
                maxTokens: 2048,
                initialized: true
            });

            const toolStats = agentService.getToolStats();
            expect(toolStats).toBeDefined();
            expect(typeof toolStats.totalExecutions).toBe('number');
        });
    });

    describe('Tool Integration with TalkingAvatar', () => {
        beforeEach(async () => {
            mockFetchVllmApi.mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ data: [] })
            });

            await agentService.initialize();
        });

        it('should register TalkingAvatar-specific tools', () => {
            const avatarTools = [
                {
                    name: 'changeExpression',
                    description: 'Change avatar facial expression',
                    parameters: {
                        type: 'object',
                        properties: {
                            expression: { type: 'string' },
                            intensity: { type: 'number' }
                        }
                    },
                    execute: vi.fn()
                },
                {
                    name: 'adjustVolume',
                    description: 'Adjust TTS volume',
                    parameters: {
                        type: 'object',
                        properties: {
                            volume: { type: 'number' }
                        }
                    },
                    execute: vi.fn()
                }
            ];

            const registered = agentService.registerTools('TalkingAvatarTools', avatarTools);

            expect(registered).toEqual(['changeExpression', 'adjustVolume']);

            const toolStats = agentService.getToolStats();
            expect(toolStats).toBeDefined();
        });

        it('should coordinate multiple tools in complex scenarios', async () => {
            // Mock LLM response that triggers multiple tools
            mockFetchVllmApi.mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({
                    choices: [{
                        message: {
                            content: 'I will dance happily while speaking loudly for you!'
                        }
                    }]
                })
            });

            const response = await agentService.generateResponse(
                'Dance happily and speak loudly',
                {
                    use_tools: true,
                    use_animation: true,
                    use_tts: true,
                    sessionId: 'complex-tools-session'
                }
            );

            expect(response.responseText).toContain('dance');
            expect(response.responseText).toContain('happily');
            expect(response.responseText).toContain('loudly');
            expect(response.hasTools).toBe(true);
            expect(response.hasAnimation).toBe(true);
        });
    });
}); 