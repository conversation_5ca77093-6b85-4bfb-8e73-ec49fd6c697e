# DualBrain Optimization Testing Guide

## Overview

This document outlines the testing approach for the optimized DualBrainCoordinator system after the December 2024 performance enhancements.

## Key API Changes

### 1. Removed `routeRequest` Method

**Before:**
```javascript
const response = await dualBrainCoordinator.routeRequest(input, {
    complexity: 'high',
    requiresReasoning: true
});
```

**After:**
```javascript
const response = await dualBrainCoordinator.generateProactiveDecision({
    conversational: {
        messages: [{ role: 'user', content: input }],
        complexity: 'high',
        requiresReasoning: true
    },
    environmental: {
        activity: 'complex_analysis',
        timestamp: Date.now()
    }
});
```

### 2. New Response Format

**generateProactiveDecision Response:**
```javascript
{
    shouldAct: boolean,      // Whether proactive action should be taken
    confidence: number,      // Confidence score (0-1)
    reason: string,          // Reason for decision
    urgency: string,         // 'low', 'medium', 'high', 'critical'
    toolsRequired?: array,   // Tools needed for action
    suggestedAction?: string // Human-readable action description
}
```

## Testing Strategy

### 1. System 1 Optimization Tests

Test that System 1 responses are now fast and lightweight:

```javascript
describe('System 1 Performance', () => {
    it('should respond within 100ms for simple inputs', async () => {
        const startTime = Date.now();
        
        const response = await coordinator.generateProactiveDecision({
            conversational: {
                messages: [{ role: 'user', content: 'Hello' }],
                complexity: 'low'
            },
            environmental: {
                activity: 'greeting',
                timestamp: Date.now()
            }
        });
        
        const responseTime = Date.now() - startTime;
        
        expect(responseTime).toBeLessThan(100);
        expect(response.shouldAct).toBeDefined();
    });
});
```

### 2. Function Consolidation Tests

Verify that duplicate functions have been removed and consolidated methods work:

```javascript
describe('Function Consolidation', () => {
    it('should use consolidated environmental context method', () => {
        // Verify _buildConsolidatedEnvironmentalContext exists
        expect(coordinator._buildConsolidatedEnvironmentalContext).toBeDefined();
        
        // Verify old methods no longer exist
        expect(coordinator._buildSystem1ContextualDescription).toBeUndefined();
        expect(coordinator._formatEnvironmentalContext).toBeUndefined();
    });
});
```

### 3. Integration Test Updates

**Character Context Testing:**
```javascript
describe('Character Integration', () => {
    it('should maintain character consistency with new API', async () => {
        await agentCoordinator.updateCharacterPersonality(character);
        
        const response = await dualBrainCoordinator.generateProactiveDecision({
            conversational: {
                messages: [{ role: 'user', content: 'Tell me about yourself' }],
                complexity: 'medium'
            },
            environmental: {
                activity: 'character_interaction',
                timestamp: Date.now()
            }
        });
        
        // Verify new response format
        expect(response).toHaveProperty('shouldAct');
        expect(response).toHaveProperty('confidence');
        expect(response).toHaveProperty('reason');
        
        // Verify character context was applied
        const model = mockAgentService.getModel('system2');
        expect(model.updateSystemPrompt).toHaveBeenCalledWith(
            expect.stringContaining(character.name)
        );
    });
});
```

## Mock Service Requirements

Updated mock services must include:

```javascript
getModel: vi.fn().mockImplementation((modelName) => ({
    constructor: { name: `Mock${modelName}Model` },
    apiMode: modelName === 'system1' ? 'websocket' : 'http',
    invoke: vi.fn().mockResolvedValue({ 
        content: JSON.stringify({
            shouldAct: true,
            confidence: 0.8,
            reason: 'mock_decision',
            urgency: 'medium'
        })
    }),
    isConnected: vi.fn().mockReturnValue(true),
    isSessionReady: vi.fn().mockReturnValue(true),
    updateSystemPrompt: vi.fn().mockResolvedValue(true),
    setCharacterContext: vi.fn().mockResolvedValue(true)
}))
```

## Performance Benchmarks

### Expected Performance Improvements:

1. **System 1 Response Time**: < 100ms (previously could exceed 1000ms)
2. **Memory Usage**: Reduced by ~30% for System 1 operations
3. **Code Maintainability**: 40+ lines of duplicate code removed

### Test Targets:

```javascript
describe('Performance Benchmarks', () => {
    it('should meet System 1 performance targets', async () => {
        const times = [];
        
        for (let i = 0; i < 10; i++) {
            const start = Date.now();
            await coordinator.generateProactiveDecision({
                conversational: { messages: [{ role: 'user', content: 'test' }] },
                environmental: { activity: 'performance_test', timestamp: Date.now() }
            });
            times.push(Date.now() - start);
        }
        
        const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
        const maxTime = Math.max(...times);
        
        expect(avgTime).toBeLessThan(500);  // Average under 500ms
        expect(maxTime).toBeLessThan(1000); // Max under 1000ms
    });
});
```

## Common Issues and Solutions

### 1. Test Setup Navigator Error

**Issue:** `TypeError: Cannot set property navigator`

**Solution:**
```javascript
// In test/setup.js
if (!global.navigator) {
    global.navigator = {};
}
Object.assign(global.navigator, {
    mediaDevices: { /* mock methods */ }
});
```

### 2. Missing Model Methods

**Issue:** Tests expect `updateSystemPrompt` but mock doesn't provide it

**Solution:**
Update mock models to include all expected methods:
```javascript
updateSystemPrompt: vi.fn().mockResolvedValue(true),
setCharacterContext: vi.fn().mockResolvedValue(true)
```

### 3. Response Format Mismatch

**Issue:** Tests expect old response format

**Solution:**
Update expectations to match new `generateProactiveDecision` format:
```javascript
expect(response).toHaveProperty('shouldAct');
expect(response).toHaveProperty('confidence');
expect(response).toHaveProperty('reason');
```

## Migration Checklist

- [ ] Replace all `routeRequest` calls with `generateProactiveDecision`
- [ ] Update response format expectations
- [ ] Add required mock methods to test services
- [ ] Fix test setup navigator override
- [ ] Verify performance benchmarks pass
- [ ] Update integration test character context tests
- [ ] Validate error handling with new API

## Related Files

- `test/src/utils/test-helpers.js` - Mock service definitions
- `test/setup.js` - Global test setup
- `test/integration/role-playing-system.test.js` - Character integration tests
- `test/integration/role-playing-validation.test.js` - Character validation tests
- `test/src/agent/arch/dualbrain/dualbrain-coordinator-enhanced.test.js` - Core coordinator tests