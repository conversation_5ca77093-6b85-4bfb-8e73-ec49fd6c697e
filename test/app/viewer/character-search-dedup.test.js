import { describe, it, expect } from 'vitest';

describe('CharacterSearchTool deduplication', () => {
  it('components CharacterSearchTool should expose execute()', async () => {
    const mod = await import('../../../app/viewer/components/CharacterSearchTool.js');
    const CharacterSearchTool = mod.default || mod.CharacterSearchTool;
    const tool = new CharacterSearchTool();
    expect(typeof tool.execute).toBe('function');
  });

  it('services CharacterSearchTool should not exist (deduplicated)', async () => {
    let failed = false;
    try {
      await import('../../../app/viewer/services/CharacterSearchTool.js');
    } catch (e) {
      failed = true;
    }
    expect(failed).toBe(true);
  });
});


