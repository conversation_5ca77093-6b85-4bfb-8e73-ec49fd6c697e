/**
 * Vitest Test Setup
 * Global test configuration and mocks for the test environment
 */

import { vi } from 'vitest';

// Mock environment variables
if (!process.env.VITE_DASHSCOPE_API_KEY) {
  process.env.VITE_DASHSCOPE_API_KEY = 'mock-api-key';
}

// Mock console methods to reduce noise during testing
const originalConsole = { ...console };

// Store original methods for restoration
global.originalConsole = originalConsole;

// Mock logger to reduce test output noise
global.mockLogger = {
  info: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  debug: vi.fn(),
  log: vi.fn()
};

// Mock setTimeout and setInterval for faster tests
vi.stubGlobal('setTimeout', (callback, delay = 0) => {
  if (delay > 100) {
    // Reduce large timeouts for testing
    return originalConsole.setTimeout(callback, 100);
  }
  return originalConsole.setTimeout(callback, delay);
});

vi.stubGlobal('setInterval', (callback, interval = 0) => {
  if (interval > 100) {
    // Reduce large intervals for testing
    return originalConsole.setInterval(callback, 100);
  }
  return originalConsole.setInterval(callback, interval);
});

// Mock fetch for API testing
global.fetch = vi.fn();

// Mock AudioContext for audio-related tests
global.AudioContext = vi.fn(() => ({
  createScriptProcessor: vi.fn(() => ({
    connect: vi.fn(),
    disconnect: vi.fn(),
    onaudioprocess: null
  })),
  createGain: vi.fn(() => ({
    connect: vi.fn(),
    disconnect: vi.fn(),
    gain: { value: 1 }
  })),
  destination: {},
  close: vi.fn(),
  resume: vi.fn()
}));

// Mock MediaDevices for media capture testing
if (!global.navigator) {
  global.navigator = {};
}
Object.assign(global.navigator, {
  mediaDevices: {
    getUserMedia: vi.fn(() => Promise.resolve({
      getTracks: () => [{
        stop: vi.fn(),
        readyState: 'live'
      }]
    }))
  }
});

// Mock WebSocket
global.WebSocket = vi.fn(() => ({
  send: vi.fn(),
  close: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  readyState: 1, // OPEN
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3
}));

// Test configuration
export const testConfig = {
  timeout: 30000,
  apiKey: process.env.VITE_DASHSCOPE_API_KEY,
  enableRealApi: !!process.env.VITE_DASHSCOPE_API_KEY && process.env.VITE_DASHSCOPE_API_KEY !== 'mock-api-key',
  mockDelays: {
    short: 10,  // Instead of 100ms
    medium: 50, // Instead of 1000ms
    long: 100   // Instead of 5000ms
  }
};

console.log('🧪 Test setup complete');
console.log(`📊 Real API testing: ${testConfig.enableRealApi ? 'ENABLED' : 'DISABLED'}`);
if (testConfig.enableRealApi) {
  console.log(`🔑 API Key: ${testConfig.apiKey.substring(0, 8)}****`);
}