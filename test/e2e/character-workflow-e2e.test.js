/**
 * End-to-End Character Workflow Test Suite
 * 
 * Comprehensive testing for character search, selection, and role-playing workflow
 * Tests integration between CharacterService, AgentCoordinator, DualBrainCoordinator,
 * voice input, and UI components.
 * 
 * Test Scenarios:
 * 1. Text-based character search and selection
 * 2. Voice-based character search workflow  
 * 3. Character personality application to dual brain
 * 4. System integration validation
 * 5. Error handling and edge cases
 * 6. Performance benchmarking
 * 7. UI/UX flow validation
 */

import { describe, test, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { CharacterService } from '../../app/viewer/services/CharacterService.js';
import { AgentCoordinator } from '../../app/viewer/services/agentCoordinator.js';
import { DualBrainCoordinator } from '../../src/agent/arch/dualbrain/DualBrainCoordinator.js';
import { transcribeAudioWithSTT } from '../../src/utils/audioTranscription.js';

// Test utilities and mocks
import { createMockAudio, createMockCharacterDatabase, mockVoiceInput } from '../utils/test-helpers.js';
import { performanceTimer, validateUIFlow } from '../utils/e2e-helpers.js';

describe('Character Workflow End-to-End Tests', () => {
  let characterService;
  let agentCoordinator; 
  let mockAgentService;
  let testContainer;
  let performanceMetrics = {};

  // Test characters database
  const testCharacters = {
    luffy: {
      id: 'monkey_d_luffy',
      name: 'Monkey D. Luffy',
      description: 'Captain of the Straw Hat Pirates with rubber powers',
      personality: {
        formality: 0.2,
        enthusiasm: 0.9,
        empathy: 0.8,
        creativity: 0.7,
        directness: 0.8
      },
      voiceStyle: 'energetic_casual',
      systemPrompt: 'You are Monkey D. Luffy, the rubber pirate captain.',
      avatar: '/assets/luffy-avatar.png'
    },
    saitama: {
      id: 'saitama_one_punch',
      name: 'Saitama',
      description: 'One-Punch Man hero who can defeat any enemy with one punch',
      personality: {
        formality: 0.3,
        enthusiasm: 0.4,
        empathy: 0.6,
        creativity: 0.3,
        directness: 0.9
      },
      voiceStyle: 'deadpan_casual',
      systemPrompt: 'You are Saitama, the bald caped hero who defeats enemies with one punch.',
      avatar: '/assets/saitama-avatar.png'
    }
  };

  beforeAll(async () => {
    console.log('🚀 Setting up E2E test environment...');

    // Create test container
    testContainer = document.createElement('div');
    testContainer.id = 'test-viewer-container';
    document.body.appendChild(testContainer);

    // Mock agent service with dual brain support
    mockAgentService = {
      getDualBrainCoordinator: jest.fn(),
      updateCharacterContext: jest.fn(),
      getModel: jest.fn((type) => ({
        updateSystemPrompt: jest.fn(),
        invoke: jest.fn(),
        isRealtimeModeActive: jest.fn(() => true)
      })),
      isDualBrainMode: jest.fn(() => true),
      initialize: jest.fn(() => Promise.resolve()),
      generateResponse: jest.fn(),
      initializeRealtimeMode: jest.fn(() => Promise.resolve(true))
    };

    // Initialize services
    characterService = new CharacterService({
      agentCoordinator: { getAgentService: () => mockAgentService },
      enablePersistence: false
    });

    agentCoordinator = new AgentCoordinator({
      enableVADHandlers: false
    });

    console.log('✅ E2E test environment setup complete');
  });

  afterAll(async () => {
    console.log('🧹 Cleaning up E2E test environment...');
    
    if (characterService) {
      characterService.dispose();
    }
    
    if (agentCoordinator) {
      await agentCoordinator.dispose();
    }

    if (testContainer) {
      document.body.removeChild(testContainer);
    }

    console.log('✅ E2E test environment cleanup complete');
  });

  beforeEach(() => {
    // Reset mocks and clear any previous state
    jest.clearAllMocks();
    performanceMetrics = {};
  });

  describe('Text-Based Character Search Workflow', () => {
    test('should successfully search and select Luffy character', async () => {
      const timer = performanceTimer();

      // Test character search
      const searchTerm = 'Luffy';
      const searchResults = await searchCharacterDatabase(searchTerm);

      expect(searchResults).toHaveLength(1);
      expect(searchResults[0].name).toBe('Monkey D. Luffy');
      
      // Test character selection and personality application
      const selectedCharacter = searchResults[0];
      const contextApplied = await characterService.setCharacterContext(selectedCharacter);

      expect(contextApplied).toBe(true);
      expect(characterService.getCurrentCharacter()).toEqual(selectedCharacter);

      // Verify dual brain integration
      expect(mockAgentService.updateCharacterContext).toHaveBeenCalledWith(
        expect.objectContaining({
          identity: expect.objectContaining({
            name: 'Monkey D. Luffy'
          }),
          personality: expect.objectContaining({
            traits: selectedCharacter.personality
          })
        })
      );

      performanceMetrics.textSearch = timer.end();
      expect(performanceMetrics.textSearch).toBeLessThan(1000); // < 1 second
    });

    test('should handle character not found gracefully', async () => {
      const searchTerm = 'NonexistentCharacter';
      const searchResults = await searchCharacterDatabase(searchTerm);

      expect(searchResults).toHaveLength(0);
    });

    test('should validate personality trait mapping accuracy', async () => {
      const character = testCharacters.saitama;
      await characterService.setCharacterContext(character);

      const currentCharacter = characterService.getCurrentCharacter();
      
      // Validate personality traits are within valid ranges
      Object.values(currentCharacter.personality).forEach(value => {
        expect(value).toBeGreaterThanOrEqual(0);
        expect(value).toBeLessThanOrEqual(1);
      });

      // Validate Saitama's specific traits
      expect(currentCharacter.personality.directness).toBeGreaterThan(0.8);
      expect(currentCharacter.personality.enthusiasm).toBeLessThan(0.5);
    });
  });

  describe('Voice-Based Character Search Workflow', () => {
    test('should transcribe voice input and extract character name', async () => {
      const timer = performanceTimer();

      // Mock audio input: "I want to be Saitama"
      const mockAudioBlob = createMockAudio('I want to be Saitama');
      
      // Test voice transcription
      const transcriptionResult = await transcribeAudioWithSTT(mockAudioBlob, {
        language: 'en-US',
        timeout: 5000
      });

      expect(transcriptionResult).toEqual(
        expect.objectContaining({
          success: true,
          transcript: expect.stringContaining('Saitama')
        })
      );

      // Test character name extraction from transcription
      const extractedCharacterName = extractCharacterNameFromTranscript(transcriptionResult.transcript);
      expect(extractedCharacterName).toBe('Saitama');

      // Test character search and selection
      const searchResults = await searchCharacterDatabase(extractedCharacterName);
      expect(searchResults).toHaveLength(1);
      expect(searchResults[0].name).toBe('Saitama');

      performanceMetrics.voiceWorkflow = timer.end();
      expect(performanceMetrics.voiceWorkflow).toBeLessThan(3000); // < 3 seconds
    });

    test('should handle voice transcription errors gracefully', async () => {
      // Mock failed audio transcription
      const mockAudioBlob = createMockAudio('unintelligible_audio_noise');
      
      const transcriptionResult = await transcribeAudioWithSTT(mockAudioBlob, {
        language: 'en-US',
        timeout: 5000
      });

      if (!transcriptionResult.success) {
        expect(transcriptionResult.error).toBeDefined();
        expect(transcriptionResult.fallbackStrategy).toBeDefined();
      }
    });

    test('should validate voice quality requirements', async () => {
      const mockHighQualityAudio = createMockAudio('I want to be Luffy', { quality: 'high' });
      const mockLowQualityAudio = createMockAudio('I want to be Luffy', { quality: 'low' });

      const highQualityResult = await transcribeAudioWithSTT(mockHighQualityAudio);
      const lowQualityResult = await transcribeAudioWithSTT(mockLowQualityAudio);

      if (highQualityResult.success && lowQualityResult.success) {
        expect(highQualityResult.confidence).toBeGreaterThan(lowQualityResult.confidence);
      }
    });
  });

  describe('Character Analysis and System Integration', () => {
    test('should analyze character and generate System 2 summary', async () => {
      const timer = performanceTimer();

      // Select character
      const character = testCharacters.luffy;
      await characterService.setCharacterContext(character);

      // Test dual brain coordinator integration
      const dualBrainCoordinator = new DualBrainCoordinator(mockAgentService, {
        enableProactiveDecisions: true,
        system2AnalysisInterval: 1000
      });

      const initialized = await dualBrainCoordinator.initialize();
      expect(initialized).toBe(true);

      // Test character context application to dual brain
      const contextData = {
        character: character,
        analysisType: 'personality_integration'
      };

      const proactiveDecision = await dualBrainCoordinator.generateProactiveDecision(contextData);

      expect(proactiveDecision).toEqual(
        expect.objectContaining({
          shouldAct: expect.any(Boolean),
          confidence: expect.any(Number),
          reason: expect.any(String)
        })
      );

      performanceMetrics.systemIntegration = timer.end();
      expect(performanceMetrics.systemIntegration).toBeLessThan(2000); // < 2 seconds
    });

    test('should validate System 1/System 2 coordination', async () => {
      const mockDualBrainCoordinator = {
        updateSystem1Context: jest.fn(),
        updateSystem2Context: jest.fn(),
        updateCoordinationParameters: jest.fn()
      };

      mockAgentService.getDualBrainCoordinator.mockReturnValue(mockDualBrainCoordinator);

      const character = testCharacters.saitama;
      await characterService.setCharacterContext(character);

      // Verify dual brain coordination calls were made
      expect(mockDualBrainCoordinator.updateSystem1Context).toHaveBeenCalledWith(
        expect.objectContaining({
          personality: expect.any(Object),
          communication: expect.any(Object)
        })
      );

      expect(mockDualBrainCoordinator.updateSystem2Context).toHaveBeenCalledWith(
        expect.objectContaining({
          guidelines: expect.any(Array),
          consistencyRules: expect.any(Array)
        })
      );
    });

    test('should validate character consistency metrics', async () => {
      const character = testCharacters.luffy;
      await characterService.setCharacterContext(character);

      const consistencyMetrics = characterService.getConsistencyMetrics();

      expect(consistencyMetrics).toEqual(
        expect.objectContaining({
          responseAlignment: expect.any(Number),
          personalityStability: expect.any(Number),
          contextualRelevance: expect.any(Number)
        })
      );

      // All metrics should be between 0 and 1
      Object.values(consistencyMetrics).forEach(metric => {
        if (typeof metric === 'number') {
          expect(metric).toBeGreaterThanOrEqual(0);
          expect(metric).toBeLessThanOrEqual(1);
        }
      });
    });
  });

  describe('UI Workflow Integration Tests', () => {
    test('should validate complete UI flow from search to role-play', async () => {
      const timer = performanceTimer();

      // Simulate UI workflow steps
      const uiSteps = [
        'character_search_input',
        'search_results_display', 
        'character_selection',
        'personality_preview',
        'confirm_selection',
        'role_play_ready'
      ];

      const uiFlowResult = await validateUIFlow(uiSteps, {
        searchTerm: 'Luffy',
        expectedCharacter: testCharacters.luffy,
        container: testContainer
      });

      expect(uiFlowResult.success).toBe(true);
      expect(uiFlowResult.completedSteps).toEqual(uiSteps);

      performanceMetrics.uiWorkflow = timer.end();
      expect(performanceMetrics.uiWorkflow).toBeLessThan(5000); // < 5 seconds
    });

    test('should handle UI error states gracefully', async () => {
      const errorScenarios = [
        'network_timeout',
        'character_not_found',
        'voice_input_failed',
        'dual_brain_unavailable'
      ];

      for (const scenario of errorScenarios) {
        const errorHandlingResult = await simulateUIError(scenario);
        
        expect(errorHandlingResult).toEqual(
          expect.objectContaining({
            errorHandled: true,
            userNotified: true,
            fallbackProvided: true
          })
        );
      }
    });

    test('should validate UI responsiveness across different screen sizes', async () => {
      const screenSizes = [
        { width: 320, height: 568, name: 'mobile' },
        { width: 768, height: 1024, name: 'tablet' },
        { width: 1920, height: 1080, name: 'desktop' }
      ];

      for (const size of screenSizes) {
        // Simulate screen resize
        Object.defineProperty(window, 'innerWidth', { value: size.width, writable: true });
        Object.defineProperty(window, 'innerHeight', { value: size.height, writable: true });
        window.dispatchEvent(new Event('resize'));

        const responsiveResult = await testResponsiveLayout(testContainer, size);
        
        expect(responsiveResult).toEqual(
          expect.objectContaining({
            layoutValid: true,
            elementsVisible: true,
            interactionsWorking: true
          })
        );
      }
    });
  });

  describe('Performance Benchmarks', () => {
    test('should meet character search performance requirements', async () => {
      const searchBenchmarks = [];

      // Test multiple search scenarios
      const searchTerms = ['Luffy', 'Saitama', 'Naruto', 'NonExistent'];

      for (const term of searchTerms) {
        const timer = performanceTimer();
        await searchCharacterDatabase(term);
        searchBenchmarks.push(timer.end());
      }

      // All searches should complete within 500ms
      searchBenchmarks.forEach(time => {
        expect(time).toBeLessThan(500);
      });

      const averageSearchTime = searchBenchmarks.reduce((a, b) => a + b, 0) / searchBenchmarks.length;
      expect(averageSearchTime).toBeLessThan(300); // Average < 300ms
    });

    test('should meet voice processing performance requirements', async () => {
      const voiceBenchmarks = [];

      // Test different audio lengths
      const audioLengths = [1, 3, 5, 10]; // seconds

      for (const length of audioLengths) {
        const timer = performanceTimer();
        const mockAudio = createMockAudio('Test audio', { duration: length });
        await transcribeAudioWithSTT(mockAudio);
        voiceBenchmarks.push(timer.end());
      }

      // Voice processing should scale reasonably with audio length
      voiceBenchmarks.forEach((time, index) => {
        const expectedMaxTime = 2000 + (audioLengths[index] * 500); // Base 2s + 500ms per second
        expect(time).toBeLessThan(expectedMaxTime);
      });
    });

    test('should validate memory usage during character switching', async () => {
      const initialMemory = getMemoryUsage();

      // Switch between characters multiple times
      for (let i = 0; i < 10; i++) {
        const character = i % 2 === 0 ? testCharacters.luffy : testCharacters.saitama;
        await characterService.setCharacterContext(character);
      }

      const finalMemory = getMemoryUsage();
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be reasonable (< 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle network connectivity issues', async () => {
      // Simulate network offline
      Object.defineProperty(navigator, 'onLine', { value: false, writable: true });

      const result = await searchCharacterDatabase('Luffy');
      
      expect(result).toEqual(
        expect.objectContaining({
          error: 'network_unavailable',
          fallbackData: expect.any(Array)
        })
      );
    });

    test('should handle invalid character data gracefully', async () => {
      const invalidCharacter = {
        id: 'invalid_character',
        name: '',
        personality: {
          formality: 2.0, // Invalid: > 1.0
          enthusiasm: -0.5, // Invalid: < 0
          empathy: 'high', // Invalid: not a number
        }
      };

      const result = await characterService.setCharacterContext(invalidCharacter);
      expect(result).toBe(false);
    });

    test('should handle concurrent character selections', async () => {
      // Test race condition handling
      const concurrentSelections = [
        characterService.setCharacterContext(testCharacters.luffy),
        characterService.setCharacterContext(testCharacters.saitama),
        characterService.setCharacterContext(testCharacters.luffy)
      ];

      const results = await Promise.allSettled(concurrentSelections);
      
      // At least one should succeed
      const successful = results.filter(r => r.status === 'fulfilled' && r.value === true);
      expect(successful.length).toBeGreaterThan(0);

      // Final state should be consistent
      const currentCharacter = characterService.getCurrentCharacter();
      expect(currentCharacter).toBeDefined();
      expect(currentCharacter.name).toMatch(/(Monkey D\. Luffy|Saitama)/);
    });

    test('should validate character data integrity', async () => {
      const character = testCharacters.luffy;
      await characterService.setCharacterContext(character);

      const storedCharacter = characterService.getCurrentCharacter();
      
      // Verify no data corruption occurred
      expect(storedCharacter).toEqual(character);
      expect(storedCharacter.personality.enthusiasm).toBe(0.9);
      expect(storedCharacter.systemPrompt).toBe(character.systemPrompt);
    });
  });

  // Helper functions
  async function searchCharacterDatabase(searchTerm) {
    // Simulate character database search
    const results = Object.values(testCharacters).filter(character => 
      character.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      character.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return results;
  }

  function extractCharacterNameFromTranscript(transcript) {
    const characterNames = ['Luffy', 'Saitama', 'Naruto', 'Goku'];
    return characterNames.find(name => 
      transcript.toLowerCase().includes(name.toLowerCase())
    ) || null;
  }

  async function simulateUIError(errorType) {
    // Mock different UI error scenarios
    switch (errorType) {
      case 'network_timeout':
        return { errorHandled: true, userNotified: true, fallbackProvided: true };
      case 'character_not_found':
        return { errorHandled: true, userNotified: true, fallbackProvided: true };
      case 'voice_input_failed':
        return { errorHandled: true, userNotified: true, fallbackProvided: true };
      case 'dual_brain_unavailable':
        return { errorHandled: true, userNotified: true, fallbackProvided: false };
      default:
        return { errorHandled: false, userNotified: false, fallbackProvided: false };
    }
  }

  async function testResponsiveLayout(container, screenSize) {
    // Mock responsive layout testing
    return {
      layoutValid: true,
      elementsVisible: true,
      interactionsWorking: true,
      screenSize: screenSize.name
    };
  }

  function getMemoryUsage() {
    if (typeof performance !== 'undefined' && performance.memory) {
      return performance.memory.usedJSHeapSize;
    }
    return 0;
  }

  // Generate test report after all tests
  afterAll(() => {
    console.log('\n📊 E2E Test Performance Report:');
    console.log('================================');
    Object.entries(performanceMetrics).forEach(([test, time]) => {
      console.log(`${test}: ${time}ms`);
    });
    console.log('================================\n');
  });
});