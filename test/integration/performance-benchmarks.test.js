/**
 * Performance Benchmarks for Role-Playing System
 * 
 * Comprehensive performance testing covering:
 * - Character search and selection performance
 * - Voice processing latency
 * - System 2 analysis speed
 * - Memory usage efficiency  
 * - Concurrent operation handling
 * - Real-world usage simulation
 */

import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import { CharacterService } from '../../app/viewer/services/CharacterService.js';
import { AgentCoordinator } from '../../app/viewer/services/agentCoordinator.ts';
import { DualBrainCoordinator } from '../../src/agent/arch/dualbrain/DualBrainCoordinator.js';
import { RolePlayingPanel } from '../../app/viewer/components/RolePlayingPanel.js';

describe('Role-Playing System Performance Benchmarks', () => {
    let characterService;
    let agentCoordinator;
    let dualBrainCoordinator;
    let rolePlayingPanel;
    let mockAgentService;
    let testContainer;
    
    // Performance test configuration
    const PERFORMANCE_THRESHOLDS = {
        CHARACTER_SEARCH_MAX_MS: 100,
        CHARACTER_APPLICATION_MAX_MS: 500,
        VOICE_PROCESSING_MAX_MS: 300,
        SYSTEM2_ANALYSIS_MAX_MS: 2000,
        UI_RENDER_MAX_MS: 200,
        MEMORY_INCREASE_MAX_MB: 20,
        CONCURRENT_OPERATIONS_MAX_MS: 1000
    };

    // Test dataset for performance testing
    const performanceTestCharacters = Array.from({ length: 100 }, (_, i) => ({
        id: `perf_char_${i}`,
        name: `Performance Character ${i}`,
        description: `Test character ${i} for performance validation with longer description text to simulate realistic data loads`,
        avatar: ['🎭', '🎪', '🎨', '🎯', '🎲'][i % 5],
        personality: {
            formality: Math.random(),
            enthusiasm: Math.random(),
            empathy: Math.random(),
            creativity: Math.random(),
            directness: Math.random()
        },
        voiceStyle: ['professional', 'casual', 'authoritative', 'expressive', 'energetic'][i % 5],
        systemPrompt: `You are Performance Character ${i}. ${Array(10).fill('Additional context').join(' ')}.`
    }));

    beforeEach(async () => {
        // Setup DOM environment
        global.document = {
            createElement: jest.fn(() => ({
                className: '',
                classList: { add: jest.fn(), remove: jest.fn(), contains: jest.fn(), toggle: jest.fn() },
                appendChild: jest.fn(),
                setAttribute: jest.fn(),
                addEventListener: jest.fn(),
                querySelector: jest.fn(),
                querySelectorAll: jest.fn(() => []),
                innerHTML: '',
                style: {},
                parentNode: { removeChild: jest.fn() }
            })),
            body: { appendChild: jest.fn() }
        };

        global.localStorage = {
            getItem: jest.fn(),
            setItem: jest.fn(),
            removeItem: jest.fn()
        };

        testContainer = { appendChild: jest.fn() };

        // Mock agent service optimized for performance testing
        mockAgentService = {
            initialize: jest.fn().mockResolvedValue(true),
            isDualBrainMode: jest.fn().mockReturnValue(true),
            getModel: jest.fn().mockImplementation((type) => ({
                constructor: { name: `${type || 'primary'}Model` },
                apiMode: 'http',
                updateSystemPrompt: jest.fn().mockResolvedValue(true),
                isRealtimeModeActive: jest.fn().mockReturnValue(true),
                invoke: jest.fn().mockResolvedValue('Performance test response'),
                generateResponse: jest.fn().mockImplementation(async (input) => {
                    // Simulate realistic API latency
                    await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100));
                    return `Performance response for: ${input}`;
                })
            })),
            updateCharacterContext: jest.fn().mockResolvedValue(true),
            generateResponse: jest.fn().mockImplementation(async (input) => {
                await new Promise(resolve => setTimeout(resolve, 100));
                return 'Mock performance response';
            }),
            setDualBrainCoordinator: jest.fn(),
            getDualBrainCoordinator: jest.fn(),
            updateDualBrainContext: jest.fn(),
            options: {
                agentConfig: {
                    enableDualBrain: true,
                    periodicAnalysisInterval: 2000,
                    decisionCooldown: 5000
                }
            }
        };

        // Initialize services
        characterService = new CharacterService({
            enablePersistence: false,
            storageKey: 'perf_test_character_data'
        });

        agentCoordinator = new AgentCoordinator({
            logger: { info: jest.fn(), warn: jest.fn(), error: jest.fn(), debug: jest.fn() },
            enableVADHandlers: false
        });

        rolePlayingPanel = new RolePlayingPanel({
            container: testContainer,
            characterService,
            agentCoordinator,
            onCharacterChange: jest.fn(),
            onPersonalityUpdate: jest.fn()
        });

        // Add performance test characters
        performanceTestCharacters.forEach(char => {
            rolePlayingPanel.addCharacterPreset(char);
        });
    });

    afterEach(async () => {
        if (rolePlayingPanel) rolePlayingPanel.dispose();
        if (agentCoordinator) await agentCoordinator.dispose();
        if (characterService) characterService.dispose();
        if (dualBrainCoordinator) {
            await dualBrainCoordinator.stopDualBrainSystems();
            dualBrainCoordinator.dispose();
        }
        jest.clearAllMocks();
    });

    describe('1. Character Search Performance', () => {
        test('should search through large character datasets efficiently', async () => {
            const startTime = performance.now();
            
            // Search for specific character in large dataset
            const searchTerm = 'Performance Character 42';
            const characters = rolePlayingPanel.getCharacterPresets();
            const results = characters.filter(char => 
                char.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                char.description.toLowerCase().includes(searchTerm.toLowerCase())
            );
            
            const endTime = performance.now();
            const searchTime = endTime - startTime;
            
            expect(searchTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CHARACTER_SEARCH_MAX_MS);
            expect(results).toHaveLength(1);
            expect(results[0].name).toBe('Performance Character 42');
        });

        test('should handle fuzzy search efficiently', async () => {
            const startTime = performance.now();
            
            // Fuzzy search with partial matches
            const searchTerms = [
                'Character 1',  // Should match multiple
                'Performance',  // Should match all
                'perf_char_5',  // Should match ID
                'energetic'     // Should match voice style
            ];
            
            const allResults = [];
            searchTerms.forEach(term => {
                const characters = rolePlayingPanel.getCharacterPresets();
                const results = characters.filter(char =>
                    char.name.toLowerCase().includes(term.toLowerCase()) ||
                    char.id.toLowerCase().includes(term.toLowerCase()) ||
                    char.voiceStyle.toLowerCase().includes(term.toLowerCase())
                );
                allResults.push(...results);
            });
            
            const endTime = performance.now();
            const searchTime = endTime - startTime;
            
            expect(searchTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CHARACTER_SEARCH_MAX_MS * 2);
            expect(allResults.length).toBeGreaterThan(0);
        });

        test('should sort search results by relevance efficiently', async () => {
            const startTime = performance.now();
            
            const searchTerm = 'Character';
            const characters = rolePlayingPanel.getCharacterPresets();
            
            // Search with relevance scoring
            const results = characters
                .map(char => {
                    let score = 0;
                    const lowerTerm = searchTerm.toLowerCase();
                    
                    // Name match (highest weight)
                    if (char.name.toLowerCase().includes(lowerTerm)) score += 10;
                    
                    // Description match (medium weight) 
                    if (char.description.toLowerCase().includes(lowerTerm)) score += 5;
                    
                    // ID match (lower weight)
                    if (char.id.toLowerCase().includes(lowerTerm)) score += 2;
                    
                    return { character: char, score };
                })
                .filter(item => item.score > 0)
                .sort((a, b) => b.score - a.score)
                .map(item => item.character);
            
            const endTime = performance.now();
            const searchTime = endTime - startTime;
            
            expect(searchTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CHARACTER_SEARCH_MAX_MS * 1.5);
            expect(results.length).toBeGreaterThan(0);
            
            // Verify sorting worked (higher scores first)
            if (results.length > 1) {
                expect(results[0].name).toContain('Character');
            }
        });

        test('should handle search with filters efficiently', async () => {
            const startTime = performance.now();
            
            const filters = {
                voiceStyle: 'professional',
                personalityRange: { empathy: { min: 0.5, max: 1.0 } },
                nameContains: 'Character'
            };
            
            const characters = rolePlayingPanel.getCharacterPresets();
            const results = characters.filter(char => {
                // Voice style filter
                if (filters.voiceStyle && char.voiceStyle !== filters.voiceStyle) {
                    return false;
                }
                
                // Personality range filter
                if (filters.personalityRange?.empathy) {
                    const empathy = char.personality.empathy;
                    if (empathy < filters.personalityRange.empathy.min || 
                        empathy > filters.personalityRange.empathy.max) {
                        return false;
                    }
                }
                
                // Name contains filter
                if (filters.nameContains && 
                    !char.name.toLowerCase().includes(filters.nameContains.toLowerCase())) {
                    return false;
                }
                
                return true;
            });
            
            const endTime = performance.now();
            const searchTime = endTime - startTime;
            
            expect(searchTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CHARACTER_SEARCH_MAX_MS);
            expect(results.length).toBeGreaterThan(0);
            
            // Verify filter worked
            results.forEach(char => {
                expect(char.voiceStyle).toBe('professional');
                expect(char.personality.empathy).toBeGreaterThanOrEqual(0.5);
            });
        });
    });

    describe('2. Character Application Performance', () => {
        test('should apply character configuration within performance threshold', async () => {
            const character = performanceTestCharacters[0];
            
            const startTime = performance.now();
            
            // Apply character through both services
            await characterService.setCharacterContext(character);
            agentCoordinator.agentService = mockAgentService;
            await agentCoordinator.updateCharacterPersonality(character);
            
            const endTime = performance.now();
            const applicationTime = endTime - startTime;
            
            expect(applicationTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CHARACTER_APPLICATION_MAX_MS);
            
            // Verify application succeeded
            const currentChar = characterService.getCurrentCharacter();
            expect(currentChar).toBeDefined();
            expect(currentChar.name).toBe(character.name);
        });

        test('should handle rapid character switching efficiently', async () => {
            const characters = performanceTestCharacters.slice(0, 10);
            
            const startTime = performance.now();
            
            // Rapidly switch between characters
            for (const character of characters) {
                await characterService.setCharacterContext(character);
                rolePlayingPanel.selectCharacter(character.id);
            }
            
            const endTime = performance.now();
            const switchingTime = endTime - startTime;
            
            expect(switchingTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CHARACTER_APPLICATION_MAX_MS * 2);
            
            // Final character should be applied
            const currentChar = characterService.getCurrentCharacter();
            expect(currentChar.name).toBe(characters[characters.length - 1].name);
        });

        test('should efficiently update personality traits', async () => {
            const character = performanceTestCharacters[0];
            rolePlayingPanel.selectCharacter(character.id);
            
            const startTime = performance.now();
            
            // Update multiple personality traits
            const traits = ['formality', 'enthusiasm', 'empathy', 'creativity', 'directness'];
            traits.forEach(trait => {
                rolePlayingPanel.updatePersonalityTrait(trait, Math.random());
            });
            
            const endTime = performance.now();
            const updateTime = endTime - startTime;
            
            expect(updateTime).toBeLessThan(PERFORMANCE_THRESHOLDS.UI_RENDER_MAX_MS);
            
            // Verify updates applied
            const currentChar = rolePlayingPanel.getCurrentCharacter();
            expect(currentChar).toBeDefined();
        });

        test('should handle bulk character operations efficiently', async () => {
            const characters = performanceTestCharacters.slice(0, 20);
            
            const startTime = performance.now();
            
            // Bulk operations
            const operations = characters.map(char => 
                characterService.setCharacterContext(char)
            );
            
            await Promise.all(operations);
            
            const endTime = performance.now();
            const bulkTime = endTime - startTime;
            
            expect(bulkTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CONCURRENT_OPERATIONS_MAX_MS);
            
            // Verify personality profiles were created
            const profiles = characterService.getPersonalityProfiles();
            expect(profiles.size).toBeGreaterThanOrEqual(characters.length);
        });
    });

    describe('3. Voice Processing Performance', () => {
        test('should process voice input with minimal latency', async () => {
            const voiceInputs = [
                'I want to be Character 1',
                'Select Performance Character 42',
                'Choose the energetic character',
                'Be like Character 77'
            ];
            
            const startTime = performance.now();
            
            // Process voice inputs
            const results = voiceInputs.map(input => {
                // Simulate voice processing pipeline
                const normalized = input.toLowerCase().trim();
                const extractedName = normalized.match(/(?:be|select|choose|want to be)\s+(?:like\s+)?(.+)/)?.[1];
                
                if (extractedName) {
                    const characters = rolePlayingPanel.getCharacterPresets();
                    const match = characters.find(char => 
                        char.name.toLowerCase().includes(extractedName) ||
                        extractedName.includes(char.name.toLowerCase().split(' ')[0].toLowerCase())
                    );
                    return { input, match: match?.name || null };
                }
                
                return { input, match: null };
            });
            
            const endTime = performance.now();
            const processingTime = endTime - startTime;
            
            expect(processingTime).toBeLessThan(PERFORMANCE_THRESHOLDS.VOICE_PROCESSING_MAX_MS);
            expect(results.filter(r => r.match).length).toBeGreaterThan(0);
        });

        test('should handle noisy voice input efficiently', async () => {
            const noisyInputs = [
                'I... uh... want to be... Character 5',
                'Select *cough* Performance Character... um... 10',
                'Choose the... what was it... energetic one',
                'Be like [UNCLEAR] Character 20 [STATIC]'
            ];
            
            const startTime = performance.now();
            
            // Clean and process noisy inputs
            const cleanedInputs = noisyInputs.map(input => {
                // Noise removal simulation
                return input
                    .replace(/\.\.\.|uh|um|\*\w+\*|\[UNCLEAR\]|\[STATIC\]/gi, '')
                    .replace(/\s+/g, ' ')
                    .trim();
            });
            
            // Process cleaned inputs
            const results = cleanedInputs.map(input => {
                const extractedName = input.match(/(?:be|select|choose)\s+(?:the\s+)?(.+)/i)?.[1];
                return { original: input, extracted: extractedName };
            });
            
            const endTime = performance.now();
            const cleaningTime = endTime - startTime;
            
            expect(cleaningTime).toBeLessThan(PERFORMANCE_THRESHOLDS.VOICE_PROCESSING_MAX_MS);
            expect(results.filter(r => r.extracted).length).toBeGreaterThan(0);
        });

        test('should efficiently match voice input to characters', async () => {
            const ambiguousInputs = [
                'Be the detective',
                'Choose the wizard',  
                'Select the energetic one',
                'I want the creative character'
            ];
            
            const startTime = performance.now();
            
            // Simulate semantic matching
            const matches = ambiguousInputs.map(input => {
                const characters = rolePlayingPanel.getCharacterPresets();
                const lowerInput = input.toLowerCase();
                
                // Semantic keyword matching
                let bestMatch = null;
                let bestScore = 0;
                
                characters.forEach(char => {
                    let score = 0;
                    
                    if (lowerInput.includes('detective') && char.description.toLowerCase().includes('detective')) score += 10;
                    if (lowerInput.includes('wizard') && char.description.toLowerCase().includes('wizard')) score += 10;
                    if (lowerInput.includes('energetic') && char.voiceStyle === 'energetic') score += 8;
                    if (lowerInput.includes('creative') && char.personality.creativity > 0.7) score += 8;
                    
                    if (score > bestScore) {
                        bestScore = score;
                        bestMatch = char;
                    }
                });
                
                return { input, match: bestMatch, score: bestScore };
            });
            
            const endTime = performance.now();
            const matchingTime = endTime - startTime;
            
            expect(matchingTime).toBeLessThan(PERFORMANCE_THRESHOLDS.VOICE_PROCESSING_MAX_MS);
            expect(matches.filter(m => m.match).length).toBeGreaterThan(0);
        });
    });

    describe('4. System 2 Analysis Performance', () => {
        beforeEach(async () => {
            agentCoordinator.agentService = mockAgentService;
            dualBrainCoordinator = new DualBrainCoordinator(mockAgentService, {
                enableProactiveDecisions: true,
                system2AnalysisInterval: 1000,
                decisionCooldown: 2000
            });
            await dualBrainCoordinator.initialize();
        });

        test('should complete character analysis within performance threshold', async () => {
            const character = performanceTestCharacters[0];
            
            const startTime = performance.now();
            
            // Perform System 2 analysis
            await characterService.setCharacterContext(character);
            await agentCoordinator.updateCharacterPersonality(character);
            
            const endTime = performance.now();
            const analysisTime = endTime - startTime;
            
            expect(analysisTime).toBeLessThan(PERFORMANCE_THRESHOLDS.SYSTEM2_ANALYSIS_MAX_MS);
            
            // Verify analysis completed
            const metrics = characterService.getConsistencyMetrics();
            expect(metrics).toBeDefined();
        });

        test('should handle concurrent analysis requests efficiently', async () => {
            const characters = performanceTestCharacters.slice(0, 5);
            
            const startTime = performance.now();
            
            // Concurrent analysis
            const analysisPromises = characters.map(char => 
                characterService.setCharacterContext(char)
            );
            
            await Promise.all(analysisPromises);
            
            const endTime = performance.now();
            const concurrentTime = endTime - startTime;
            
            expect(concurrentTime).toBeLessThan(PERFORMANCE_THRESHOLDS.SYSTEM2_ANALYSIS_MAX_MS * 1.5);
            
            // Verify all analyses completed
            const profiles = characterService.getPersonalityProfiles();
            expect(profiles.size).toBeGreaterThanOrEqual(characters.length);
        });

        test('should optimize analysis based on character complexity', async () => {
            // Simple character
            const simpleChar = {
                ...performanceTestCharacters[0],
                personality: { formality: 0.5, enthusiasm: 0.5, empathy: 0.5, creativity: 0.5, directness: 0.5 }
            };
            
            // Complex character
            const complexChar = {
                ...performanceTestCharacters[1],
                personality: {
                    formality: 0.7234,
                    enthusiasm: 0.4891,
                    empathy: 0.8456,
                    creativity: 0.7823,
                    directness: 0.6234
                }
            };
            
            // Measure simple character
            const simpleStart = performance.now();
            await characterService.setCharacterContext(simpleChar);
            const simpleEnd = performance.now();
            const simpleTime = simpleEnd - simpleStart;
            
            // Measure complex character
            const complexStart = performance.now();
            await characterService.setCharacterContext(complexChar);
            const complexEnd = performance.now();
            const complexTime = complexEnd - complexStart;
            
            // Complex should not be significantly slower
            expect(complexTime).toBeLessThan(simpleTime * 2);
            expect(complexTime).toBeLessThan(PERFORMANCE_THRESHOLDS.SYSTEM2_ANALYSIS_MAX_MS);
        });

        test('should generate proactive decisions efficiently', async () => {
            const character = performanceTestCharacters[0];
            await characterService.setCharacterContext(character);
            await dualBrainCoordinator.startDualBrainSystems();
            
            const startTime = performance.now();
            
            // Generate proactive decision
            const decision = await dualBrainCoordinator.generateProactiveDecision({
                environmental: { activity: 'idle' },
                conversational: { context: 'waiting' }
            });
            
            const endTime = performance.now();
            const decisionTime = endTime - startTime;
            
            expect(decisionTime).toBeLessThan(PERFORMANCE_THRESHOLDS.SYSTEM2_ANALYSIS_MAX_MS);
            expect(decision).toBeDefined();
        });
    });

    describe('5. Memory Usage Efficiency', () => {
        test('should maintain reasonable memory usage during character operations', async () => {
            const initialMemory = process.memoryUsage().heapUsed;
            
            // Perform multiple character operations
            for (let i = 0; i < 50; i++) {
                const character = performanceTestCharacters[i % 10];
                await characterService.setCharacterContext(character);
                rolePlayingPanel.selectCharacter(character.id);
            }
            
            // Force garbage collection if available
            if (global.gc) {
                global.gc();
            }
            
            const finalMemory = process.memoryUsage().heapUsed;
            const memoryIncrease = (finalMemory - initialMemory) / (1024 * 1024); // Convert to MB
            
            expect(memoryIncrease).toBeLessThan(PERFORMANCE_THRESHOLDS.MEMORY_INCREASE_MAX_MB);
        });

        test('should efficiently manage character history and profiles', async () => {
            const initialMemory = process.memoryUsage().heapUsed;
            
            // Create extensive character history
            for (let i = 0; i < 100; i++) {
                const character = performanceTestCharacters[i % 20];
                await characterService.setCharacterContext(character);
            }
            
            // Check memory usage
            const afterHistoryMemory = process.memoryUsage().heapUsed;
            const historyMemoryMB = (afterHistoryMemory - initialMemory) / (1024 * 1024);
            
            // Get data structures
            const history = characterService.getCharacterHistory();
            const profiles = characterService.getPersonalityProfiles();
            
            expect(historyMemoryMB).toBeLessThan(PERFORMANCE_THRESHOLDS.MEMORY_INCREASE_MAX_MB);
            expect(history.length).toBeLessThanOrEqual(50); // Should limit history size
            expect(profiles.size).toBeGreaterThan(0);
        });

        test('should handle memory cleanup on disposal', async () => {
            const initialMemory = process.memoryUsage().heapUsed;
            
            // Create multiple service instances
            const services = [];
            for (let i = 0; i < 10; i++) {
                const service = new CharacterService({
                    enablePersistence: false,
                    storageKey: `test_${i}`
                });
                
                // Use the service
                await service.setCharacterContext(performanceTestCharacters[0]);
                services.push(service);
            }
            
            const afterCreationMemory = process.memoryUsage().heapUsed;
            
            // Dispose all services
            services.forEach(service => service.dispose());
            
            if (global.gc) {
                global.gc();
            }
            
            const afterDisposalMemory = process.memoryUsage().heapUsed;
            const memoryRecovered = (afterCreationMemory - afterDisposalMemory) / (1024 * 1024);
            
            // Should recover most of the allocated memory
            expect(afterDisposalMemory).toBeLessThan(afterCreationMemory);
        });
    });

    describe('6. UI Rendering Performance', () => {
        test('should render character grid efficiently', async () => {
            const startTime = performance.now();
            
            // Simulate character grid rendering
            const characters = rolePlayingPanel.getCharacterPresets();
            
            // Mock DOM operations
            characters.forEach(character => {
                // Simulate creating character card
                const mockCard = {
                    className: 'character-card',
                    innerHTML: `
                        <div class="character-avatar">${character.avatar}</div>
                        <div class="character-name">${character.name}</div>
                        <div class="character-description">${character.description}</div>
                    `
                };
                
                // Simulate DOM append
                testContainer.appendChild(mockCard);
            });
            
            const endTime = performance.now();
            const renderTime = endTime - startTime;
            
            expect(renderTime).toBeLessThan(PERFORMANCE_THRESHOLDS.UI_RENDER_MAX_MS);
        });

        test('should update personality sliders efficiently', async () => {
            rolePlayingPanel.selectCharacter(performanceTestCharacters[0].id);
            
            const startTime = performance.now();
            
            // Update multiple sliders rapidly
            const traits = ['formality', 'enthusiasm', 'empathy', 'creativity', 'directness'];
            for (let i = 0; i < 50; i++) {
                const trait = traits[i % traits.length];
                const value = Math.random();
                rolePlayingPanel.updatePersonalityTrait(trait, value);
            }
            
            const endTime = performance.now();
            const updateTime = endTime - startTime;
            
            expect(updateTime).toBeLessThan(PERFORMANCE_THRESHOLDS.UI_RENDER_MAX_MS * 2);
        });

        test('should handle rapid character selection changes', async () => {
            const characters = performanceTestCharacters.slice(0, 20);
            
            const startTime = performance.now();
            
            // Rapidly select different characters
            characters.forEach(character => {
                rolePlayingPanel.selectCharacter(character.id);
            });
            
            const endTime = performance.now();
            const selectionTime = endTime - startTime;
            
            expect(selectionTime).toBeLessThan(PERFORMANCE_THRESHOLDS.UI_RENDER_MAX_MS * 1.5);
            
            // Final character should be selected
            const currentChar = rolePlayingPanel.getCurrentCharacter();
            expect(currentChar.name).toBe(characters[characters.length - 1].name);
        });
    });

    describe('7. Concurrent Operations Performance', () => {
        test('should handle multiple simultaneous character applications', async () => {
            const characters = performanceTestCharacters.slice(0, 10);
            
            const startTime = performance.now();
            
            // Concurrent character applications
            const operations = characters.map(async (character, index) => {
                // Stagger operations slightly to simulate real usage
                await new Promise(resolve => setTimeout(resolve, index * 10));
                
                await characterService.setCharacterContext(character);
                rolePlayingPanel.selectCharacter(character.id);
                
                return character.name;
            });
            
            const results = await Promise.all(operations);
            
            const endTime = performance.now();
            const concurrentTime = endTime - startTime;
            
            expect(concurrentTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CONCURRENT_OPERATIONS_MAX_MS);
            expect(results).toHaveLength(characters.length);
        });

        test('should maintain performance under high load simulation', async () => {
            const startTime = performance.now();
            
            // Simulate high load scenario
            const highLoadOperations = [];
            
            // Character searches
            for (let i = 0; i < 20; i++) {
                highLoadOperations.push(
                    Promise.resolve().then(() => {
                        const characters = rolePlayingPanel.getCharacterPresets();
                        return characters.filter(char => char.name.includes('Character'));
                    })
                );
            }
            
            // Character applications
            for (let i = 0; i < 10; i++) {
                const character = performanceTestCharacters[i];
                highLoadOperations.push(
                    characterService.setCharacterContext(character)
                );
            }
            
            // UI updates
            for (let i = 0; i < 15; i++) {
                highLoadOperations.push(
                    Promise.resolve().then(() => {
                        rolePlayingPanel.updatePersonalityTrait('enthusiasm', Math.random());
                    })
                );
            }
            
            await Promise.all(highLoadOperations);
            
            const endTime = performance.now();
            const highLoadTime = endTime - startTime;
            
            expect(highLoadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CONCURRENT_OPERATIONS_MAX_MS * 2);
        });

        test('should handle mixed read/write operations efficiently', async () => {
            const startTime = performance.now();
            
            // Mix of read and write operations
            const mixedOperations = [];
            
            // Writes (character applications)
            for (let i = 0; i < 5; i++) {
                mixedOperations.push(
                    characterService.setCharacterContext(performanceTestCharacters[i])
                );
            }
            
            // Reads (character retrieval)
            for (let i = 0; i < 10; i++) {
                mixedOperations.push(
                    Promise.resolve(characterService.getCurrentCharacter())
                );
            }
            
            // Mixed operations (personality updates)
            for (let i = 0; i < 8; i++) {
                mixedOperations.push(
                    Promise.resolve().then(() => {
                        rolePlayingPanel.selectCharacter(performanceTestCharacters[i % 3].id);
                        return rolePlayingPanel.getCurrentCharacter();
                    })
                );
            }
            
            const results = await Promise.all(mixedOperations);
            
            const endTime = performance.now();
            const mixedTime = endTime - startTime;
            
            expect(mixedTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CONCURRENT_OPERATIONS_MAX_MS);
            expect(results).toHaveLength(mixedOperations.length);
        });
    });

    describe('8. Real-World Usage Simulation', () => {
        test('should handle typical user workflow efficiently', async () => {
            const startTime = performance.now();
            
            // Simulate realistic user workflow
            
            // 1. User opens role-playing panel
            rolePlayingPanel.show();
            
            // 2. User searches for character
            const searchResults = rolePlayingPanel.getCharacterPresets()
                .filter(char => char.name.includes('Character 1'));
            
            // 3. User selects character
            if (searchResults.length > 0) {
                rolePlayingPanel.selectCharacter(searchResults[0].id);
            }
            
            // 4. User adjusts personality
            rolePlayingPanel.updatePersonalityTrait('enthusiasm', 0.8);
            rolePlayingPanel.updatePersonalityTrait('creativity', 0.7);
            
            // 5. User changes voice style
            rolePlayingPanel.updateVoiceStyle('energetic');
            
            // 6. User applies character
            await rolePlayingPanel.applyCharacterConfiguration();
            
            // 7. User tests with different character
            if (searchResults.length > 1) {
                rolePlayingPanel.selectCharacter(searchResults[1].id);
                await rolePlayingPanel.applyCharacterConfiguration();
            }
            
            const endTime = performance.now();
            const workflowTime = endTime - startTime;
            
            expect(workflowTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CONCURRENT_OPERATIONS_MAX_MS);
            
            // Verify workflow completed successfully
            const currentChar = rolePlayingPanel.getCurrentCharacter();
            expect(currentChar).toBeDefined();
        });

        test('should maintain performance during extended session', async () => {
            const sessionStart = performance.now();
            const performanceMetrics = [];
            
            // Simulate 30-minute session with periodic operations
            for (let minute = 0; minute < 5; minute++) { // Shortened for test
                const minuteStart = performance.now();
                
                // Typical operations per minute
                for (let operation = 0; operation < 10; operation++) {
                    const char = performanceTestCharacters[operation % 10];
                    
                    if (operation % 3 === 0) {
                        // Character selection
                        rolePlayingPanel.selectCharacter(char.id);
                    } else if (operation % 3 === 1) {
                        // Personality adjustment
                        rolePlayingPanel.updatePersonalityTrait('enthusiasm', Math.random());
                    } else {
                        // Character application
                        await characterService.setCharacterContext(char);
                    }
                    
                    // Small delay to simulate user think time
                    await new Promise(resolve => setTimeout(resolve, 10));
                }
                
                const minuteEnd = performance.now();
                performanceMetrics.push(minuteEnd - minuteStart);
            }
            
            const sessionEnd = performance.now();
            const totalSessionTime = sessionEnd - sessionStart;
            
            // Performance should not degrade significantly over time
            const averageMinuteTime = performanceMetrics.reduce((sum, time) => sum + time, 0) / performanceMetrics.length;
            const lastMinuteTime = performanceMetrics[performanceMetrics.length - 1];
            
            expect(lastMinuteTime).toBeLessThan(averageMinuteTime * 1.5); // No more than 50% degradation
            expect(totalSessionTime).toBeLessThan(10000); // Total test should complete quickly
        });

        test('should handle error scenarios gracefully without performance impact', async () => {
            const startTime = performance.now();
            
            // Mix successful and error operations
            const operations = [];
            
            // Valid operations
            for (let i = 0; i < 5; i++) {
                operations.push(
                    characterService.setCharacterContext(performanceTestCharacters[i])
                );
            }
            
            // Invalid operations (should fail gracefully)
            operations.push(
                characterService.setCharacterContext(null),
                characterService.setCharacterContext({}),
                characterService.setCharacterContext({
                    id: 'invalid',
                    personality: { invalid: 'data' }
                })
            );
            
            // More valid operations
            for (let i = 5; i < 10; i++) {
                operations.push(
                    characterService.setCharacterContext(performanceTestCharacters[i])
                );
            }
            
            // Execute all operations
            const results = await Promise.allSettled(operations);
            
            const endTime = performance.now();
            const errorHandlingTime = endTime - startTime;
            
            expect(errorHandlingTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CONCURRENT_OPERATIONS_MAX_MS);
            
            // Verify error handling
            const successful = results.filter(r => r.status === 'fulfilled');
            const failed = results.filter(r => r.status === 'rejected');
            
            expect(successful.length).toBeGreaterThan(0);
            expect(failed.length).toBeGreaterThan(0); // Some should fail
            expect(successful.length + failed.length).toBe(results.length);
        });
    });
});