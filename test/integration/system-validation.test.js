/**
 * System Integration Validation Tests
 * 
 * Validates the integration between all system components including:
 * - CharacterService ↔ AgentCoordinator integration
 * - AgentCoordinator ↔ DualBrainCoordinator integration  
 * - DualBrainCoordinator ↔ Model systems integration
 * - Voice input ↔ Character search integration
 * - UI ↔ Backend services integration
 * - Error handling and recovery workflows
 */

import { describe, test, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { CharacterService } from '../../app/viewer/services/CharacterService.js';
import { AgentCoordinator } from '../../app/viewer/services/agentCoordinator.js';
import { DualBrainCoordinator, createDualBrainCoordinator } from '../../src/agent/arch/dualbrain/DualBrainCoordinator.js';
import { createMockAgentService, performanceTimer } from '../utils/test-helpers.js';

describe('System Integration Validation', () => {
  let characterService;
  let agentCoordinator;
  let dualBrainCoordinator;
  let mockAgentService;
  let integrationResults = {};

  // Test character data
  const testCharacters = {
    luffy: {
      id: 'monkey_d_luffy',
      name: '<PERSON>',
      description: 'Energetic pirate captain with rubber powers',
      personality: {
        formality: 0.2,
        enthusiasm: 0.9,
        empathy: 0.8,
        creativity: 0.7,
        directness: 0.8
      },
      voiceStyle: 'energetic_casual',
      systemPrompt: 'You are Luffy, the energetic pirate captain!',
      avatar: '/assets/luffy.png'
    },
    saitama: {
      id: 'saitama_one_punch',
      name: 'Saitama',
      description: 'Bald hero who defeats enemies with one punch',
      personality: {
        formality: 0.3,
        enthusiasm: 0.4,
        empathy: 0.6,
        creativity: 0.3,
        directness: 0.9
      },
      voiceStyle: 'deadpan_casual',
      systemPrompt: 'You are Saitama, the one-punch hero.',
      avatar: '/assets/saitama.png'
    }
  };

  beforeAll(async () => {
    console.log('🔧 Setting up system integration test environment...');

    // Create comprehensive mock agent service
    mockAgentService = createMockAgentService({
      // Enhanced mocking for integration testing
      generateResponse: jest.fn(async (input, options) => {
        await new Promise(resolve => setTimeout(resolve, 100)); // Simulate processing
        return `Mock response for: ${typeof input === 'string' ? input : 'complex input'}`;
      }),
      
      setDualBrainCoordinator: jest.fn(),
      
      // Mock model retrieval with different behaviors
      getModel: jest.fn((type) => {
        const baseModel = {
          updateSystemPrompt: jest.fn(),
          invoke: jest.fn(),
          generateResponse: jest.fn()
        };

        switch (type) {
          case 'system1':
            return {
              ...baseModel,
              isRealtimeModeActive: jest.fn(() => true),
              apiMode: 'websocket'
            };
          case 'system2':
            return {
              ...baseModel,
              apiMode: 'http'
            };
          default:
            return baseModel;
        }
      })
    });

    // Initialize services with proper dependencies
    characterService = new CharacterService({
      enablePersistence: false,
      agentCoordinator: {
        getAgentService: () => mockAgentService
      }
    });

    agentCoordinator = new AgentCoordinator({
      enableVADHandlers: false
    });

    console.log('✅ System integration test environment ready');
  });

  afterAll(async () => {
    console.log('🧹 Cleaning up system integration tests...');
    
    if (characterService) characterService.dispose();
    if (agentCoordinator) await agentCoordinator.dispose();
    if (dualBrainCoordinator) dualBrainCoordinator.dispose();

    // Generate integration test report
    console.log('\n📊 System Integration Test Results:');
    console.log('====================================');
    Object.entries(integrationResults).forEach(([test, result]) => {
      console.log(`${test}: ${JSON.stringify(result, null, 2)}`);
    });
    console.log('====================================\n');
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('CharacterService ↔ AgentCoordinator Integration', () => {
    test('should successfully integrate character context with agent coordinator', async () => {
      const timer = performanceTimer();
      const character = testCharacters.luffy;

      // Test character context application
      const success = await characterService.setCharacterContext(character);
      const result = timer.end();

      expect(success).toBe(true);

      // Verify agent service interactions
      expect(mockAgentService.updateCharacterContext).toHaveBeenCalledWith(
        expect.objectContaining({
          identity: expect.objectContaining({
            name: character.name,
            role: character.id
          }),
          personality: expect.objectContaining({
            traits: character.personality,
            description: expect.any(String),
            guidelines: expect.any(Array)
          }),
          systemPrompt: expect.any(String),
          enhancedPrompt: expect.any(String)
        })
      );

      // Verify dual brain integration attempt
      expect(mockAgentService.getDualBrainCoordinator).toHaveBeenCalled();

      integrationResults.character_agent_integration = {
        success: true,
        processingTime: result.duration,
        contextApplied: true
      };
    });

    test('should handle agent coordinator failures gracefully', async () => {
      // Mock agent service failure
      const failingMockService = createMockAgentService({
        updateCharacterContext: jest.fn().mockRejectedValue(new Error('Agent service error')),
        getDualBrainCoordinator: jest.fn().mockReturnValue(null)
      });

      const failingCharacterService = new CharacterService({
        enablePersistence: false,
        agentCoordinator: {
          getAgentService: () => failingMockService
        }
      });

      const character = testCharacters.saitama;
      const success = await failingCharacterService.setCharacterContext(character);

      // Should handle failure gracefully and still store character locally
      expect(success).toBe(false);
      
      // But character should still be available locally
      const currentCharacter = failingCharacterService.getCurrentCharacter();
      expect(currentCharacter).toBeNull(); // Should not store if agent integration fails

      integrationResults.agent_failure_handling = {
        gracefulFailure: true,
        localStorageFallback: false // Character not stored on agent failure
      };
    });

    test('should validate personality trait propagation to models', async () => {
      const character = testCharacters.luffy;
      await characterService.setCharacterContext(character);

      // Verify System1 model update
      const system1Model = mockAgentService.getModel('system1');
      expect(system1Model.updateSystemPrompt).toHaveBeenCalled();

      // Verify System2 model update  
      const system2Model = mockAgentService.getModel('system2');
      expect(system2Model.updateSystemPrompt).toHaveBeenCalled();

      // Verify prompt enhancement
      const updateCalls = mockAgentService.updateCharacterContext.mock.calls[0][0];
      expect(updateCalls.enhancedPrompt).toContain(character.name);
      expect(updateCalls.enhancedPrompt).toContain('CHARACTER PERSONALITY');
      expect(updateCalls.enhancedPrompt).toContain('BEHAVIOR GUIDELINES');

      integrationResults.personality_propagation = {
        system1Updated: true,
        system2Updated: true,
        promptEnhanced: true,
        personalityMapped: true
      };
    });
  });

  describe('DualBrainCoordinator Integration', () => {
    test('should successfully create and initialize dual brain coordinator', async () => {
      const timer = performanceTimer();

      // Create dual brain coordinator
      const systems = {
        system1: mockAgentService.getModel('system1'),
        system2: mockAgentService.getModel('system2'),
        agentService: mockAgentService
      };

      dualBrainCoordinator = createDualBrainCoordinator(systems, {
        enableProactiveDecisions: true,
        system2AnalysisInterval: 5000,
        decisionCooldown: 3000
      });

      const initialized = await dualBrainCoordinator.initialize();
      const result = timer.end();

      expect(initialized).toBe(true);
      expect(dualBrainCoordinator.getStatus().isInitialized).toBe(true);

      integrationResults.dual_brain_initialization = {
        success: true,
        initializationTime: result.duration,
        coordinatorActive: true
      };
    });

    test('should integrate character context with dual brain systems', async () => {
      if (!dualBrainCoordinator) {
        // Create coordinator if not exists
        const systems = {
          system1: mockAgentService.getModel('system1'),
          system2: mockAgentService.getModel('system2'),
          agentService: mockAgentService
        };
        dualBrainCoordinator = createDualBrainCoordinator(systems);
        await dualBrainCoordinator.initialize();
      }

      const character = testCharacters.saitama;
      
      // Apply character through character service (which should integrate with dual brain)
      await characterService.setCharacterContext(character);

      // Verify dual brain coordinator received character context
      const dualBrainMock = mockAgentService.getDualBrainCoordinator();
      if (dualBrainMock) {
        expect(dualBrainMock.updateSystem1Context).toHaveBeenCalledWith(
          expect.objectContaining({
            personality: expect.any(Object),
            communication: expect.any(Object)
          })
        );

        expect(dualBrainMock.updateSystem2Context).toHaveBeenCalledWith(
          expect.objectContaining({
            guidelines: expect.any(Array),
            consistencyRules: expect.any(Array)
          })
        );
      }

      integrationResults.dual_brain_character_integration = {
        system1ContextApplied: true,
        system2ContextApplied: true,
        coordinationParametersSet: true
      };
    });

    test('should handle dual brain proactive decisions', async () => {
      if (!dualBrainCoordinator) {
        const systems = {
          system1: mockAgentService.getModel('system1'),
          system2: mockAgentService.getModel('system2'),
          agentService: mockAgentService
        };
        dualBrainCoordinator = createDualBrainCoordinator(systems);
        await dualBrainCoordinator.initialize();
      }

      const contextData = {
        recentContext: [{
          type: 'user_interaction',
          data: { message: 'Hello there!' },
          timestamp: Date.now()
        }]
      };

      const timer = performanceTimer();
      const decision = await dualBrainCoordinator.generateProactiveDecision(contextData);
      const result = timer.end();

      expect(decision).toEqual(
        expect.objectContaining({
          shouldAct: expect.any(Boolean),
          confidence: expect.any(Number),
          reason: expect.any(String)
        })
      );

      expect(result.duration).toBeLessThan(5000); // Should complete within 5 seconds

      integrationResults.proactive_decision_generation = {
        decisionGenerated: true,
        processingTime: result.duration,
        hasValidStructure: true
      };
    });
  });

  describe('Voice Input ↔ Character Search Integration', () => {
    test('should extract character names from voice transcription', async () => {
      const voiceInputs = [
        'I want to be Luffy',
        'Switch to Saitama character',
        'Change my character to Monkey D Luffy',
        'Select Saitama please'
      ];

      const extractionResults = [];

      for (const input of voiceInputs) {
        const extractedCharacter = extractCharacterNameFromTranscript(input);
        
        if (extractedCharacter) {
          // Test character search with extracted name
          const searchResults = await simulateCharacterSearch(extractedCharacter);
          
          extractionResults.push({
            input,
            extracted: extractedCharacter,
            foundCharacters: searchResults.length
          });
        }
      }

      // Verify all inputs produced valid extractions and searches
      expect(extractionResults).toHaveLength(voiceInputs.length);
      extractionResults.forEach(result => {
        expect(result.extracted).toBeDefined();
        expect(result.foundCharacters).toBeGreaterThan(0);
      });

      integrationResults.voice_character_extraction = {
        successfulExtractions: extractionResults.length,
        totalInputs: voiceInputs.length,
        extractionRate: (extractionResults.length / voiceInputs.length * 100).toFixed(1) + '%'
      };
    });

    test('should handle voice transcription errors in character workflow', async () => {
      const errorScenarios = [
        { input: 'mumblemumble unclear audio', expectedError: 'unclear_transcription' },
        { input: 'I want to be XYZ123NonExistent', expectedError: 'character_not_found' },
        { input: '', expectedError: 'empty_transcription' }
      ];

      const errorHandling = [];

      for (const scenario of errorScenarios) {
        try {
          const extractedCharacter = extractCharacterNameFromTranscript(scenario.input);
          
          if (!extractedCharacter) {
            errorHandling.push({
              scenario: scenario.input,
              errorType: 'extraction_failed',
              handled: true
            });
            continue;
          }

          const searchResults = await simulateCharacterSearch(extractedCharacter);
          
          if (searchResults.length === 0) {
            errorHandling.push({
              scenario: scenario.input,
              errorType: 'character_not_found',
              handled: true
            });
          }

        } catch (error) {
          errorHandling.push({
            scenario: scenario.input,
            errorType: 'processing_error',
            handled: true,
            error: error.message
          });
        }
      }

      // Verify all error scenarios were handled gracefully
      expect(errorHandling).toHaveLength(errorScenarios.length);
      errorHandling.forEach(result => {
        expect(result.handled).toBe(true);
      });

      integrationResults.voice_error_handling = {
        totalErrorScenarios: errorScenarios.length,
        handledErrors: errorHandling.length,
        errorTypes: [...new Set(errorHandling.map(r => r.errorType))]
      };
    });
  });

  describe('Cross-Component Communication', () => {
    test('should maintain data consistency across all components', async () => {
      const character = testCharacters.luffy;
      
      // Apply character through the full system
      const success = await characterService.setCharacterContext(character);
      expect(success).toBe(true);

      // Verify character is available in all components
      const storedCharacter = characterService.getCurrentCharacter();
      expect(storedCharacter).toEqual(character);

      // Verify agent coordinator received the update
      expect(mockAgentService.updateCharacterContext).toHaveBeenCalled();

      // Verify consistency metrics are being tracked
      const consistencyMetrics = characterService.getConsistencyMetrics();
      expect(consistencyMetrics).toEqual(
        expect.objectContaining({
          responseAlignment: expect.any(Number),
          personalityStability: expect.any(Number),
          contextualRelevance: expect.any(Number)
        })
      );

      integrationResults.data_consistency = {
        characterStored: true,
        agentContextUpdated: true,
        metricsTracked: true,
        crossComponentSync: true
      };
    });

    test('should handle component initialization order dependencies', async () => {
      // Test various initialization orders
      const initOrders = [
        ['character', 'agent', 'dual_brain'],
        ['agent', 'dual_brain', 'character'],
        ['dual_brain', 'character', 'agent']
      ];

      const orderResults = [];

      for (const order of initOrders) {
        const services = {};
        
        try {
          // Initialize in specified order
          for (const component of order) {
            switch (component) {
              case 'character':
                services.character = new CharacterService({ enablePersistence: false });
                break;
              case 'agent':
                services.agent = new AgentCoordinator({ enableVADHandlers: false });
                break;
              case 'dual_brain':
                if (mockAgentService) {
                  const systems = {
                    system1: mockAgentService.getModel('system1'),
                    system2: mockAgentService.getModel('system2'),
                    agentService: mockAgentService
                  };
                  services.dual_brain = createDualBrainCoordinator(systems);
                }
                break;
            }
          }

          orderResults.push({
            order: order.join(' → '),
            success: true,
            componentsCreated: Object.keys(services).length
          });

          // Cleanup
          Object.values(services).forEach(service => {
            if (service.dispose) service.dispose();
          });

        } catch (error) {
          orderResults.push({
            order: order.join(' → '),
            success: false,
            error: error.message
          });
        }
      }

      // All initialization orders should work
      orderResults.forEach(result => {
        expect(result.success).toBe(true);
      });

      integrationResults.initialization_order_flexibility = {
        testedOrders: orderResults.length,
        successfulOrders: orderResults.filter(r => r.success).length,
        orderIndependent: orderResults.every(r => r.success)
      };
    });

    test('should validate event flow between components', async () => {
      const eventFlow = [];

      // Mock event tracking
      const originalConsoleLog = console.log;
      console.log = (...args) => {
        if (args[0] && typeof args[0] === 'string') {
          if (args[0].includes('[CharacterService]') || args[0].includes('[AgentCoordinator]')) {
            eventFlow.push({
              component: args[0].match(/\[(\w+)\]/)?.[1] || 'unknown',
              event: args[0],
              timestamp: Date.now()
            });
          }
        }
        originalConsoleLog(...args);
      };

      try {
        const character = testCharacters.saitama;
        await characterService.setCharacterContext(character);

        // Restore console.log
        console.log = originalConsoleLog;

        // Analyze event flow
        const characterEvents = eventFlow.filter(e => e.component === 'CharacterService');
        const agentEvents = eventFlow.filter(e => e.component === 'AgentCoordinator');

        integrationResults.event_flow_validation = {
          totalEvents: eventFlow.length,
          characterServiceEvents: characterEvents.length,
          agentCoordinatorEvents: agentEvents.length,
          eventSequenceValid: eventFlow.length > 0
        };

      } finally {
        console.log = originalConsoleLog;
      }
    });
  });

  describe('Error Recovery and Resilience', () => {
    test('should recover from network failures gracefully', async () => {
      // Simulate network failure by making agent service calls fail
      const networkFailureMock = createMockAgentService({
        updateCharacterContext: jest.fn().mockRejectedValue(new Error('Network timeout')),
        generateResponse: jest.fn().mockRejectedValue(new Error('Connection failed'))
      });

      const resilientCharacterService = new CharacterService({
        enablePersistence: false,
        agentCoordinator: {
          getAgentService: () => networkFailureMock
        }
      });

      const character = testCharacters.luffy;
      const result = await resilientCharacterService.setCharacterContext(character);

      // Should handle network failure gracefully
      expect(result).toBe(false);
      
      // But should not crash the system
      expect(resilientCharacterService.getCurrentCharacter()).toBeNull();

      integrationResults.network_failure_recovery = {
        gracefulFailure: true,
        systemStable: true,
        errorHandled: true
      };
    });

    test('should handle partial component failures', async () => {
      // Create partially failing mock
      const partialFailureMock = createMockAgentService({
        updateCharacterContext: jest.fn(), // This works
        getDualBrainCoordinator: jest.fn().mockReturnValue({
          updateSystem1Context: jest.fn().mockRejectedValue(new Error('System1 failure')),
          updateSystem2Context: jest.fn(), // This works
          updateCoordinationParameters: jest.fn()
        })
      });

      const partialCharacterService = new CharacterService({
        enablePersistence: false,
        agentCoordinator: {
          getAgentService: () => partialFailureMock
        }
      });

      const character = testCharacters.saitama;
      const result = await partialCharacterService.setCharacterContext(character);

      // Should succeed despite partial failures
      expect(result).toBe(true);
      expect(partialCharacterService.getCurrentCharacter()).toEqual(character);

      integrationResults.partial_failure_handling = {
        overallSuccess: true,
        partialSystemsWorking: true,
        degradedModeOperational: true
      };
    });
  });

  // Helper functions
  function extractCharacterNameFromTranscript(transcript) {
    const characterNames = ['Luffy', 'Saitama', 'Naruto', 'Goku', 'Monkey D Luffy'];
    return characterNames.find(name => 
      transcript.toLowerCase().includes(name.toLowerCase().replace(' ', ''))
    );
  }

  async function simulateCharacterSearch(characterName) {
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const matchingCharacters = Object.values(testCharacters).filter(char =>
      char.name.toLowerCase().includes(characterName.toLowerCase()) ||
      characterName.toLowerCase().includes(char.name.toLowerCase().replace(' ', ''))
    );
    
    return matchingCharacters;
  }
});