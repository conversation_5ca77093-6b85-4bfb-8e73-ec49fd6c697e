/**
 * Audio Decision Pipeline Integration Tests
 * 
 * Tests for the complete audio processing pipeline:
 * - Audio input detection and routing
 * - System 1 text description generation from audio/video
 * - System 2 analysis and tool calling decisions
 * - DecisionProcessor tool execution and modality management
 * - VAD integration and stream processing triggers
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { DualBrainCoordinator } from '@/agent/arch/dualbrain/DualBrainCoordinator.js';
import { SystemInvoker } from '@/agent/arch/dualbrain/services/SystemInvoker.js';

// Mock logger
vi.mock('@/utils/logger.ts', () => ({
  createLogger: () => ({
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    setLogLevel: vi.fn()
  }),
  LogLevelValues: { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3 },
  LogLevel: { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3 }
}));

describe('Audio Decision Pipeline Integration', () => {
  let coordinator;
  let systemInvoker;
  let mockAgentService;
  let mockDecisionProcessor;
  let mockAudioManager;
  let mockVADHandler;

  beforeEach(async () => {
    // Create comprehensive mock audio manager
    mockAudioManager = {
      isAudioInputActive: vi.fn(() => true),
      getCurrentAudioLevel: vi.fn(() => 0.6),
      getAudioTranscription: vi.fn(() => ({
        text: 'Hello, can you tell me about machine learning?',
        confidence: 0.9,
        language: 'en-US',
        timestamp: Date.now()
      })),
      processAudioStream: vi.fn(async (audioData) => ({
        transcription: 'User is asking about machine learning concepts',
        audioLevel: 0.7,
        speechDetected: true,
        vadResult: { speechStarted: true, speechEnded: false }
      })),
      startAudioCapture: vi.fn(),
      stopAudioCapture: vi.fn(),
      resetSession: vi.fn()
    };

    // Create mock VAD handler
    mockVADHandler = {
      onSpeechStart: vi.fn(),
      onSpeechEnd: vi.fn(),
      onVADStateChange: vi.fn(),
      isVADActive: vi.fn(() => true),
      getVADConfig: vi.fn(() => ({
        threshold: 0.1,
        prefix_padding_ms: 500,
        silence_duration_ms: 900
      })),
      processSpeechEvent: vi.fn((eventType, data) => ({
        eventType,
        processed: true,
        shouldTriggerSystem1: eventType === 'speech_started',
        audioLevel: data.audioLevel || 0.5
      }))
    };

    // Create mock agent service with enhanced audio capabilities
    mockAgentService = {
      isDualBrainMode: vi.fn(() => true),
      getModel: vi.fn().mockImplementation((type) => {
        if (type === 'system1') {
          return {
            constructor: { name: 'AliyunWebSocketChatModel' },
            apiMode: 'websocket',
            generateTextDescription: vi.fn().mockResolvedValue({
              content: 'User is asking about machine learning concepts while looking directly at the camera',
              sceneAnalysis: {
                audioDescription: 'Clear speech with questioning tone',
                visualDescription: 'Single person facing camera, engaged posture',
                environmentDescription: 'Indoor setting, good lighting',
                userIntent: 'information_seeking',
                confidence: 0.85
              },
              processingTime: 150
            }),
            setModalities: vi.fn(),
            getCurrentModalities: vi.fn(() => ['text']),
            isRealtimeModeActive: vi.fn(() => true)
          };
        } else if (type === 'system2') {
          return {
            constructor: { name: 'AliyunHttpChatModel' },
            apiMode: 'http',
            invoke: vi.fn().mockResolvedValue({
              content: 'I can help explain machine learning concepts. Let me provide a clear explanation.',
              tool_calls: [
                {
                  function: {
                    name: 'control_avatar_speech',
                    arguments: JSON.stringify({
                      action: 'speak',
                      text: 'Machine learning is a subset of AI that enables computers to learn from data without being explicitly programmed.',
                      voice: 'Chelsie',
                      tone: 'educational',
                      pacing: 'moderate',
                      reasoning: 'User is asking for educational information about ML concepts'
                    })
                  }
                }
              ]
            })
          };
        }
        return null;
      }),
      generateResponse: vi.fn().mockResolvedValue({
        content: 'I can help explain machine learning concepts.',
        tool_calls: [
          {
            function: {
              name: 'control_avatar_speech',
              arguments: JSON.stringify({
                action: 'speak',
                text: 'Machine learning is a subset of artificial intelligence.',
                voice: 'Chelsie'
              })
            }
          }
        ]
      }),
      tools: [
        { name: 'control_avatar_speech', func: vi.fn() },
        { name: 'select_animation', func: vi.fn() }
      ],
      audioManager: mockAudioManager,
      vadHandler: mockVADHandler
    };

    // Create enhanced mock decision processor
    mockDecisionProcessor = {
      processDecision: vi.fn().mockResolvedValue({
        executed: true,
        toolsExecuted: ['control_avatar_speech'],
        modalityChanged: true,
        newModalities: ['text', 'audio'],
        audioOutput: {
          text: 'Machine learning is a subset of artificial intelligence.',
          voice: 'Chelsie',
          duration: 3500
        },
        sessionUpdated: true,
        eventId: 'audio_response_123'
      }),
      queueDecision: vi.fn(),
      getExecutionStatus: vi.fn(() => ({
        isExecuting: false,
        queueLength: 0,
        lastExecution: Date.now() - 1000
      }))
    };

    // Initialize SystemInvoker with enhanced integration
    systemInvoker = new SystemInvoker({
      validateInputs: false,
      validateOutputs: false
    });

    systemInvoker.configureModels({
      system1: mockAgentService.getModel('system1'),
      system2: mockAgentService.getModel('system2')
    });

    systemInvoker.setAgentService(mockAgentService);
    systemInvoker.setDecisionProcessor(mockDecisionProcessor);

    // Initialize coordinator with enhanced services
    coordinator = new DualBrainCoordinator(mockAgentService, {
      services: {
        systemInvoker,
        decisionProcessor: mockDecisionProcessor
      },
      audioProcessing: {
        enableVAD: true,
        enableStreamProcessing: true,
        audioInputTimeout: 5000
      }
    });

    await coordinator.initialize();
  });

  afterEach(() => {
    if (coordinator) {
      coordinator.dispose();
    }
  });

  describe('1. Audio Input Detection and Routing', () => {
    it('should detect audio input and route to System 1', async () => {
      const audioInput = {
        type: 'audio',
        format: 'pcm16',
        sampleRate: 16000,
        channels: 1,
        data: new ArrayBuffer(2048),
        timestamp: Date.now()
      };

      // Test audio input detection
      const isAudioActive = mockAudioManager.isAudioInputActive();
      expect(isAudioActive).toBe(true);

      // Test routing decision
      const routing = coordinator._routeToAppropriateSystem('audio input detected', {
        inputType: 'audio',
        hasAudioInput: true,
        audioData: audioInput
      });

      expect(routing.targetSystem).toBe('system1');
      expect(routing.reason).toBe('audio_input_routing');
      expect(routing.capabilities).toContain('audioInput');
      expect(routing.capabilities).toContain('realtime');
      expect(routing.useRealtime).toBe(true);
    });

    it('should process audio stream and extract transcription', async () => {
      const audioStreamData = {
        format: 'pcm16',
        sampleRate: 16000,
        frameSize: 1024,
        audioLevel: 0.7,
        timestamp: Date.now()
      };

      const streamResult = await mockAudioManager.processAudioStream(audioStreamData);

      expect(streamResult.transcription).toBe('User is asking about machine learning concepts');
      expect(streamResult.speechDetected).toBe(true);
      expect(streamResult.vadResult.speechStarted).toBe(true);
      expect(streamResult.audioLevel).toBe(0.7);
    });

    it('should handle VAD triggers for speech detection', () => {
      const speechStartEvent = {
        type: 'speech_started',
        audioLevel: 0.6,
        timestamp: Date.now(),
        confidence: 0.8
      };

      const vadResult = mockVADHandler.processSpeechEvent('speech_started', speechStartEvent);

      expect(vadResult.eventType).toBe('speech_started');
      expect(vadResult.processed).toBe(true);
      expect(vadResult.shouldTriggerSystem1).toBe(true);
      expect(mockVADHandler.onSpeechStart).toHaveBeenCalled();
    });
  });

  describe('2. System 1 Text Description Generation', () => {
    it('should generate comprehensive text descriptions from audio/video input', async () => {
      const system1Model = mockAgentService.getModel('system1');
      
      const audioVideoInput = {
        audioTranscription: 'Hello, can you explain machine learning to me?',
        audioLevel: 0.7,
        videoContext: {
          faces: 1,
          posture: 'seated_forward',
          attention: 'focused_on_camera',
          lighting: 'good',
          background: 'indoor_office'
        },
        timestamp: Date.now()
      };

      const textDescription = await system1Model.generateTextDescription(audioVideoInput);

      expect(textDescription.content).toContain('User is asking about machine learning concepts');
      expect(textDescription.sceneAnalysis.audioDescription).toBe('Clear speech with questioning tone');
      expect(textDescription.sceneAnalysis.visualDescription).toContain('Single person facing camera');
      expect(textDescription.sceneAnalysis.userIntent).toBe('information_seeking');
      expect(textDescription.sceneAnalysis.confidence).toBeGreaterThan(0.8);
      expect(textDescription.processingTime).toBeLessThan(200);
    });

    it('should maintain text-only modality during scene analysis', async () => {
      const system1Model = mockAgentService.getModel('system1');
      
      // Verify System 1 uses text-only modality for analysis
      const currentModalities = system1Model.getCurrentModalities();
      expect(currentModalities).toEqual(['text']);

      // Generate description should not change modalities yet
      await system1Model.generateTextDescription({
        audioInput: 'test audio',
        videoInput: 'test video'
      });

      // Modalities should remain text-only until System 2 decides
      expect(system1Model.getCurrentModalities()).toEqual(['text']);
    });

    it('should handle different types of audio input scenarios', async () => {
      const system1Model = mockAgentService.getModel('system1');
      
      const scenarios = [
        {
          name: 'question_about_tech',
          input: { audioTranscription: 'How does artificial intelligence work?' },
          expectedIntent: 'information_seeking'
        },
        {
          name: 'casual_greeting',
          input: { audioTranscription: 'Hello there, how are you doing?' },
          expectedIntent: 'social_interaction'
        },
        {
          name: 'request_for_help',
          input: { audioTranscription: 'Can you help me with my project?' },
          expectedIntent: 'assistance_request'
        }
      ];

      for (const scenario of scenarios) {
        system1Model.generateTextDescription.mockResolvedValueOnce({
          content: `Analysis of ${scenario.name}`,
          sceneAnalysis: {
            userIntent: scenario.expectedIntent,
            confidence: 0.8
          }
        });

        const result = await system1Model.generateTextDescription(scenario.input);
        expect(result.sceneAnalysis.userIntent).toBe(scenario.expectedIntent);
      }
    });
  });

  describe('3. System 1 → System 2 Context Handoff', () => {
    it('should pass rich context from System 1 to System 2', async () => {
      const system1Analysis = {
        content: 'User is asking about machine learning concepts while looking directly at the camera',
        sceneAnalysis: {
          audioDescription: 'Clear speech with questioning tone',
          visualDescription: 'Single person facing camera, engaged posture',
          userIntent: 'information_seeking',
          confidence: 0.85
        },
        originalAudio: 'Hello, can you explain machine learning to me?'
      };

      const system2Request = {
        input: system1Analysis.content,
        context: {
          system1Analysis,
          enableToolCalling: true,
          audioProcessingContext: {
            originalTranscription: system1Analysis.originalAudio,
            sceneConfidence: system1Analysis.sceneAnalysis.confidence,
            userIntent: system1Analysis.sceneAnalysis.userIntent
          }
        }
      };

      const result = await systemInvoker.invokeSystem2(system2Request);

      expect(result.status).toBe('success');
      expect(mockAgentService.generateResponse).toHaveBeenCalledWith(
        system1Analysis.content,
        expect.objectContaining({
          useSystem2: true,
          enableTools: true,
          dualBrainContext: expect.objectContaining({
            isSystem2: true,
            enableToolCalling: true
          })
        })
      );
    });

    it('should build contextual descriptions for System 2 analysis', async () => {
      const contextData = {
        audioActivity: true,
        environmentalState: 'engaged_conversation',
        system1Analysis: {
          userIntent: 'information_seeking',
          confidence: 0.85,
          audioLevel: 0.7
        }
      };

      coordinator.state.lastSystem1Response = {
        timestamp: Date.now(),
        content: 'User asking about ML concepts',
        analysis: contextData.system1Analysis
      };

      const contextDescription = coordinator._buildSystem1ContextualDescription(contextData);

      expect(contextDescription).toContain('Audio Environment: Active user audio detected');
      expect(contextDescription).toContain('Environmental State: engaged_conversation');
      expect(contextDescription).toContain('User Intent: information_seeking');
      expect(contextDescription).toContain('Analysis Confidence: 85%');
    });
  });

  describe('4. System 2 Analysis and Tool Calling Decisions', () => {
    it('should analyze System 1 context and make speaking decisions', async () => {
      const system2Model = mockAgentService.getModel('system2');
      
      const analysisRequest = [
        {
          role: 'system',
          content: 'System 1 Analysis: User is asking about machine learning concepts with high confidence (85%)'
        },
        {
          role: 'user',
          content: 'User is asking about machine learning concepts while looking directly at the camera'
        }
      ];

      const result = await system2Model.invoke(analysisRequest);

      expect(result.content).toContain('explain machine learning concepts');
      expect(result.tool_calls).toHaveLength(1);
      expect(result.tool_calls[0].function.name).toBe('control_avatar_speech');
      
      const toolArgs = JSON.parse(result.tool_calls[0].function.arguments);
      expect(toolArgs.action).toBe('speak');
      expect(toolArgs.text).toBeDefined();
      expect(toolArgs.voice).toBe('Chelsie');
      expect(toolArgs.reasoning).toBeDefined();
    });

    it('should make contextual decisions about when to speak vs when to stay silent', async () => {
      const scenarios = [
        {
          name: 'user_asking_question',
          context: {
            userIntent: 'information_seeking',
            audioLevel: 0.7,
            userCurrentlySpeaking: false
          },
          expectedDecision: 'should_speak'
        },
        {
          name: 'user_still_speaking',
          context: {
            userIntent: 'continuing_speech',
            audioLevel: 0.8,
            userCurrentlySpeaking: true
          },
          expectedDecision: 'should_wait'
        },
        {
          name: 'background_noise_only',
          context: {
            userIntent: 'no_clear_intent',
            audioLevel: 0.2,
            userCurrentlySpeaking: false
          },
          expectedDecision: 'should_wait'
        }
      ];

      const system2Model = mockAgentService.getModel('system2');

      for (const scenario of scenarios) {
        const shouldSpeak = scenario.expectedDecision === 'should_speak';
        
        system2Model.invoke.mockResolvedValueOnce({
          content: shouldSpeak ? 'I should respond to help the user' : 'I should wait for clearer input',
          tool_calls: shouldSpeak ? [
            {
              function: {
                name: 'control_avatar_speech',
                arguments: JSON.stringify({
                  action: 'speak',
                  text: 'Let me help you with that',
                  reasoning: `Decision for ${scenario.name}`
                })
              }
            }
          ] : []
        });

        const result = await systemInvoker.invokeSystem2({
          input: `Context: ${scenario.name}`,
          context: { 
            enableToolCalling: true,
            analysisContext: scenario.context
          }
        });

        if (shouldSpeak) {
          expect(result.data.tool_calls).toHaveLength(1);
          expect(result.data.tool_calls[0].function.name).toBe('control_avatar_speech');
        } else {
          expect(result.data.tool_calls).toHaveLength(0);
        }
      }
    });

    it('should include rich reasoning in tool calling decisions', async () => {
      const system2Model = mockAgentService.getModel('system2');
      
      const contextualRequest = {
        input: 'User is asking about machine learning with high engagement',
        context: {
          enableToolCalling: true,
          system1Analysis: {
            userIntent: 'information_seeking',
            confidence: 0.9,
            audioDescription: 'Clear, enthusiastic questioning',
            visualDescription: 'Attentive posture, direct eye contact'
          }
        }
      };

      const result = await systemInvoker.invokeSystem2(contextualRequest);
      
      expect(result.data.tool_calls).toHaveLength(1);
      
      const toolArgs = JSON.parse(result.data.tool_calls[0].function.arguments);
      expect(toolArgs.reasoning).toContain('User is asking for educational information');
      expect(toolArgs.tone).toBe('educational');
      expect(toolArgs.pacing).toBe('moderate');
    });
  });

  describe('5. DecisionProcessor Tool Execution and Modality Management', () => {
    it('should execute speaking tools and manage modality switching', async () => {
      const speakingDecision = {
        content: 'I can help explain machine learning concepts.',
        tool_calls: [
          {
            function: {
              name: 'control_avatar_speech',
              arguments: JSON.stringify({
                action: 'speak',
                text: 'Machine learning is a subset of artificial intelligence that enables computers to learn from data.',
                voice: 'Chelsie',
                tone: 'educational'
              })
            }
          }
        ]
      };

      const result = await mockDecisionProcessor.processDecision(speakingDecision, {
        source: 'system2_audio_decision',
        originalAudioContext: {
          userTranscription: 'Hello, can you explain machine learning to me?',
          userIntent: 'information_seeking'
        }
      });

      expect(result.executed).toBe(true);
      expect(result.toolsExecuted).toContain('control_avatar_speech');
      expect(result.modalityChanged).toBe(true);
      expect(result.newModalities).toEqual(['text', 'audio']);
      expect(result.audioOutput.text).toBeDefined();
      expect(result.audioOutput.voice).toBe('Chelsie');
      expect(result.sessionUpdated).toBe(true);
    });

    it('should queue decisions during active audio processing', async () => {
      // Mock decision processor as busy
      mockDecisionProcessor.getExecutionStatus.mockReturnValue({
        isExecuting: true,
        queueLength: 2,
        lastExecution: Date.now() - 500
      });

      const newDecision = {
        content: 'Another decision while processing',
        tool_calls: []
      };

      mockDecisionProcessor.queueDecision(newDecision);

      expect(mockDecisionProcessor.queueDecision).toHaveBeenCalledWith(newDecision);

      // Verify queue handling
      const status = mockDecisionProcessor.getExecutionStatus();
      expect(status.isExecuting).toBe(true);
      expect(status.queueLength).toBeGreaterThan(0);
    });

    it('should manage session modality updates during tool execution', async () => {
      const toolExecution = {
        toolName: 'control_avatar_speech',
        arguments: {
          action: 'speak',
          text: 'This is a test response',
          voice: 'Chelsie'
        },
        executionId: 'exec_123'
      };

      const result = await mockDecisionProcessor.processDecision({
        content: 'Processing tool execution',
        tool_calls: [
          {
            function: {
              name: toolExecution.toolName,
              arguments: JSON.stringify(toolExecution.arguments)
            }
          }
        ]
      });

      expect(result.sessionUpdated).toBe(true);
      expect(result.eventId).toBeDefined();
      expect(result.newModalities).toEqual(['text', 'audio']);
    });
  });

  describe('6. Complete Audio Decision Pipeline', () => {
    it('should execute complete pipeline: audio → description → decision → tool execution', async () => {
      const pipelineExecution = {
        startTime: Date.now(),
        phases: {}
      };

      // Phase 1: Audio input detection
      pipelineExecution.phases.audioInput = {
        timestamp: Date.now(),
        audioDetected: mockAudioManager.isAudioInputActive(),
        transcription: await mockAudioManager.getAudioTranscription()
      };

      expect(pipelineExecution.phases.audioInput.audioDetected).toBe(true);
      expect(pipelineExecution.phases.audioInput.transcription.text).toBeDefined();

      // Phase 2: System 1 text description
      const system1Model = mockAgentService.getModel('system1');
      pipelineExecution.phases.system1Analysis = await system1Model.generateTextDescription({
        audioTranscription: pipelineExecution.phases.audioInput.transcription.text
      });

      expect(pipelineExecution.phases.system1Analysis.content).toBeDefined();
      expect(pipelineExecution.phases.system1Analysis.sceneAnalysis).toBeDefined();

      // Phase 3: System 2 decision
      pipelineExecution.phases.system2Decision = await systemInvoker.invokeSystem2({
        input: pipelineExecution.phases.system1Analysis.content,
        context: { enableToolCalling: true }
      });

      expect(pipelineExecution.phases.system2Decision.status).toBe('success');
      expect(pipelineExecution.phases.system2Decision.data.tool_calls).toHaveLength(1);

      // Phase 4: Tool execution
      pipelineExecution.phases.toolExecution = await mockDecisionProcessor.processDecision(
        pipelineExecution.phases.system2Decision.data
      );

      expect(pipelineExecution.phases.toolExecution.executed).toBe(true);
      expect(pipelineExecution.phases.toolExecution.modalityChanged).toBe(true);

      // Phase 5: Complete pipeline validation
      pipelineExecution.endTime = Date.now();
      pipelineExecution.totalTime = pipelineExecution.endTime - pipelineExecution.startTime;

      expect(pipelineExecution.totalTime).toBeLessThan(5000); // Should complete in under 5 seconds
      expect(Object.keys(pipelineExecution.phases)).toHaveLength(4);
      
      // Verify each phase completed successfully
      expect(pipelineExecution.phases.audioInput.audioDetected).toBe(true);
      expect(pipelineExecution.phases.system1Analysis.content).toBeDefined();
      expect(pipelineExecution.phases.system2Decision.status).toBe('success');
      expect(pipelineExecution.phases.toolExecution.executed).toBe(true);
    });

    it('should handle pipeline errors gracefully with fallback strategies', async () => {
      // Mock System 1 failure
      const system1Model = mockAgentService.getModel('system1');
      system1Model.generateTextDescription.mockRejectedValueOnce(
        new Error('System 1 processing failed')
      );

      // Pipeline should continue with fallback
      const fallbackResult = await coordinator.processMultiAgentRequest(
        'fallback test audio input',
        { inputType: 'audio', enableFallback: true }
      );

      // Should get some result even with System 1 failure
      expect(fallbackResult).toBeDefined();
      expect(mockAgentService.generateResponse).toHaveBeenCalled();
    });

    it('should track pipeline performance metrics', async () => {
      const metrics = {
        audioDetectionTime: 0,
        system1ProcessingTime: 0,
        system2DecisionTime: 0,
        toolExecutionTime: 0,
        totalPipelineTime: 0,
        modalitySwitchTime: 0
      };

      let startTime = Date.now();

      // Audio detection
      mockAudioManager.isAudioInputActive();
      metrics.audioDetectionTime = Date.now() - startTime;

      // System 1 processing
      startTime = Date.now();
      const system1Model = mockAgentService.getModel('system1');
      await system1Model.generateTextDescription({ audioInput: 'test' });
      metrics.system1ProcessingTime = Date.now() - startTime;

      // System 2 decision
      startTime = Date.now();
      await systemInvoker.invokeSystem2({
        input: 'test input',
        context: { enableToolCalling: true }
      });
      metrics.system2DecisionTime = Date.now() - startTime;

      // Tool execution
      startTime = Date.now();
      await mockDecisionProcessor.processDecision({
        content: 'test',
        tool_calls: []
      });
      metrics.toolExecutionTime = Date.now() - startTime;

      metrics.totalPipelineTime = Object.values(metrics).reduce((sum, time) => sum + time, 0);

      // Verify performance targets
      expect(metrics.audioDetectionTime).toBeLessThan(50);
      expect(metrics.system1ProcessingTime).toBeLessThan(500);
      expect(metrics.system2DecisionTime).toBeLessThan(2000);
      expect(metrics.toolExecutionTime).toBeLessThan(1000);
      expect(metrics.totalPipelineTime).toBeLessThan(5000);
    });
  });

  describe('7. VAD Integration and Stream Processing', () => {
    it('should handle VAD state changes and trigger appropriate responses', () => {
      const vadEvents = [
        { type: 'speech_started', audioLevel: 0.6, shouldTriggerSystem1: true },
        { type: 'speech_ended', audioLevel: 0.1, shouldTriggerSystem1: false },
        { type: 'silence_detected', audioLevel: 0.05, shouldTriggerSystem1: false }
      ];

      vadEvents.forEach(event => {
        const result = mockVADHandler.processSpeechEvent(event.type, event);
        expect(result.shouldTriggerSystem1).toBe(event.shouldTriggerSystem1);
        expect(result.processed).toBe(true);
      });

      // Verify VAD callbacks were called
      expect(mockVADHandler.onSpeechStart).toHaveBeenCalled();
      expect(mockVADHandler.onSpeechEnd).toHaveBeenCalled();
    });

    it('should configure VAD settings for different scenarios', () => {
      const vadConfig = mockVADHandler.getVADConfig();

      expect(vadConfig.threshold).toBe(0.1);
      expect(vadConfig.prefix_padding_ms).toBe(500);
      expect(vadConfig.silence_duration_ms).toBe(900);

      // Test that VAD is active
      expect(mockVADHandler.isVADActive()).toBe(true);
    });

    it('should process continuous audio streams with VAD triggers', async () => {
      const audioStream = [
        { frameId: 1, audioLevel: 0.05, timestamp: 1000 }, // silence
        { frameId: 2, audioLevel: 0.15, timestamp: 1020 }, // speech start
        { frameId: 3, audioLevel: 0.70, timestamp: 1040 }, // active speech
        { frameId: 4, audioLevel: 0.65, timestamp: 1060 }, // continued speech
        { frameId: 5, audioLevel: 0.08, timestamp: 1080 }  // speech end
      ];

      const streamResults = [];

      for (const frame of audioStream) {
        const result = await mockAudioManager.processAudioStream(frame);
        streamResults.push({
          frameId: frame.frameId,
          speechDetected: result.speechDetected,
          vadResult: result.vadResult
        });
      }

      // Verify stream processing results
      expect(streamResults).toHaveLength(5);
      expect(streamResults.some(r => r.speechDetected)).toBe(true);
    });

    it('should coordinate VAD with dual brain system for real-time responses', async () => {
      // Simulate speech start triggering System 1
      const speechStartEvent = {
        type: 'speech_started',
        audioLevel: 0.7,
        timestamp: Date.now()
      };

      const vadResult = mockVADHandler.processSpeechEvent('speech_started', speechStartEvent);
      
      if (vadResult.shouldTriggerSystem1) {
        // Should trigger System 1 for immediate processing
        const routing = coordinator._routeToAppropriateSystem('VAD triggered', {
          inputType: 'audio',
          vadTrigger: true,
          realtime: true
        });

        expect(routing.targetSystem).toBe('system1');
        expect(routing.useRealtime).toBe(true);
      }

      // Record user interaction to update activity state
      coordinator.recordUserInteraction('audio');
      
      const activityStatus = coordinator.getNoActivityStatus();
      expect(activityStatus.timeSinceLastInput).toBeLessThan(1000);
    });
  });
});