/**
 * Integration tests for runtime recovery and 1011 reconnection fixes
 * Tests the comprehensive runtime error recovery mechanisms
 */

import { describe, it, expect, beforeEach, afterEach, vi, beforeAll, afterAll } from 'vitest';
import { AudioStreamingCoordinator } from '../../src/agent/runtime/AudioStreamingCoordinator.js';
import { RuntimeErrorRecovery } from '../../src/agent/runtime/RuntimeErrorRecovery.js';
import { VADEventBuffer } from '../../src/agent/runtime/VADEventBuffer.js';
import { ProgressiveSystemStartup } from '../../src/agent/runtime/ProgressiveSystemStartup.js';

describe('Runtime Recovery Integration Tests', () => {
    let audioCoordinator;
    let errorRecovery;
    let vadBuffer;
    let systemStartup;
    
    beforeEach(() => {
        audioCoordinator = new AudioStreamingCoordinator({
            bufferSize: 10,
            replayEnabled: true,
            logger: { debug: vi.fn(), info: vi.fn(), warn: vi.fn(), error: vi.fn() }
        });
        
        errorRecovery = new RuntimeErrorRecovery({
            maxRetries: 2,
            baseBackoffMs: 100,
            circuitBreakerThreshold: 3,
            logger: { debug: vi.fn(), info: vi.fn(), warn: vi.fn(), error: vi.fn() }
        });
        
        vadBuffer = new VADEventBuffer({
            bufferSize: 5,
            flushInterval: 100,
            logger: { debug: vi.fn(), info: vi.fn(), warn: vi.fn(), error: vi.fn() }
        });
        
        systemStartup = new ProgressiveSystemStartup({
            maxRetries: 2,
            retryDelayMs: 50,
            logger: { debug: vi.fn(), info: vi.fn(), warn: vi.fn(), error: vi.fn() }
        });
    });
    
    afterEach(async () => {
        if (audioCoordinator) await audioCoordinator.dispose?.();
        if (errorRecovery) await errorRecovery.dispose?.();
        if (vadBuffer) await vadBuffer.dispose?.();
        if (systemStartup) await systemStartup.dispose?.();
    });
    
    describe('AudioStreamingCoordinator', () => {
        it('should buffer audio during reconnection and replay after', async () => {
            // Start buffering
            audioCoordinator.startBuffering();
            
            // Buffer some audio data
            const audioData1 = { chunk: 'audio1', timestamp: Date.now() };
            const audioData2 = { chunk: 'audio2', timestamp: Date.now() + 100 };
            
            const buffered1 = audioCoordinator.bufferAudio(audioData1, { type: 'speech' });
            const buffered2 = audioCoordinator.bufferAudio(audioData2, { type: 'speech' });
            
            expect(buffered1).toBe(true);
            expect(buffered2).toBe(true);
            
            const status = audioCoordinator.getBufferStatus();
            expect(status.isBuffering).toBe(true);
            expect(status.bufferSize).toBe(2);
            
            // Register replay handler
            const replayedEvents = [];
            audioCoordinator.on('audioReplay', (data) => {
                replayedEvents.push(data);
            });
            
            // Stop buffering and replay
            await audioCoordinator.stopBuffering(true);
            
            // Wait for replay to complete
            await new Promise(resolve => setTimeout(resolve, 50));
            
            expect(replayedEvents).toHaveLength(2);
            expect(replayedEvents[0].audioData).toEqual(audioData1);
            expect(replayedEvents[1].audioData).toEqual(audioData2);
            expect(replayedEvents[0].isReplay).toBe(true);
        });\n        \n        it('should handle buffer overflow gracefully', async () => {\n            audioCoordinator.startBuffering();\n            \n            const overflowEvents = [];\n            audioCoordinator.on('bufferOverflow', (data) => {\n                overflowEvents.push(data);\n            });\n            \n            // Fill buffer beyond capacity\n            for (let i = 0; i < 15; i++) {\n                audioCoordinator.bufferAudio({ chunk: `audio${i}` });\n            }\n            \n            const status = audioCoordinator.getBufferStatus();\n            expect(status.bufferSize).toBe(10); // Should be capped at bufferSize\n            expect(overflowEvents.length).toBe(5); // 5 overflow events\n        });\n        \n        it('should skip replay for excessive delay', async () => {\n            const coordinator = new AudioStreamingCoordinator({\n                maxReplayDelayMs: 100,\n                logger: { debug: vi.fn(), info: vi.fn(), warn: vi.fn(), error: vi.fn() }\n            });\n            \n            coordinator.startBuffering();\n            coordinator.bufferAudio({ chunk: 'test' });\n            \n            // Wait longer than maxReplayDelayMs\n            await new Promise(resolve => setTimeout(resolve, 150));\n            \n            const replayedEvents = [];\n            coordinator.on('audioReplay', (data) => replayedEvents.push(data));\n            \n            await coordinator.stopBuffering(true);\n            await new Promise(resolve => setTimeout(resolve, 50));\n            \n            expect(replayedEvents).toHaveLength(0); // Should skip replay\n        });\n    });\n    \n    describe('RuntimeErrorRecovery', () => {\n        it('should execute operation with retry logic', async () => {\n            let attempts = 0;\n            const operation = vi.fn(() => {\n                attempts++;\n                if (attempts < 2) {\n                    throw new Error('Temporary failure');\n                }\n                return 'success';\n            });\n            \n            const result = await errorRecovery.executeWithCircuitBreaker(operation, 'test-op');\n            \n            expect(result).toBe('success');\n            expect(attempts).toBe(2);\n            expect(operation).toHaveBeenCalledTimes(2);\n        });\n        \n        it('should open circuit breaker after threshold failures', async () => {\n            const failingOperation = vi.fn(() => {\n                throw new Error('Always fails');\n            });\n            \n            // Fail enough times to trigger circuit breaker\n            for (let i = 0; i < 3; i++) {\n                await expect(\n                    errorRecovery.executeWithCircuitBreaker(failingOperation, 'failing-op')\n                ).rejects.toThrow();\n            }\n            \n            const status = errorRecovery.getStatus();\n            expect(status.circuitBreaker.state).toBe('open');\n            \n            // Next call should be blocked immediately\n            await expect(\n                errorRecovery.executeWithCircuitBreaker(failingOperation, 'blocked-op')\n            ).rejects.toThrow('Circuit breaker is open');\n        });\n        \n        it('should manage connection pool', async () => {\n            const connection1 = { id: 'conn1', data: 'test1' };\n            const connection2 = { id: 'conn2', data: 'test2' };\n            \n            errorRecovery.addConnection('conn1', connection1);\n            errorRecovery.addConnection('conn2', connection2);\n            \n            const retrieved1 = errorRecovery.getConnection('conn1');\n            const retrieved2 = errorRecovery.getConnection();\n            \n            expect(retrieved1).toBe(connection1);\n            expect([connection1, connection2]).toContain(retrieved2);\n            \n            const status = errorRecovery.getStatus();\n            expect(status.connectionPool.totalConnections).toBe(2);\n        });\n        \n        it('should perform health checks on connections', async () => {\n            const connection = { id: 'test', healthy: true };\n            const healthCheck = vi.fn(() => Promise.resolve(connection.healthy));\n            \n            errorRecovery.addConnection('test-conn', connection, healthCheck);\n            \n            const results = await errorRecovery.performHealthChecks();\n            expect(results.healthy).toBe(1);\n            expect(results.unhealthy).toBe(0);\n            expect(healthCheck).toHaveBeenCalledWith(connection);\n            \n            // Make connection unhealthy\n            connection.healthy = false;\n            \n            const results2 = await errorRecovery.performHealthChecks();\n            expect(results2.healthy).toBe(0);\n            expect(results2.unhealthy).toBe(1);\n        });\n    });\n    \n    describe('VADEventBuffer', () => {\n        it('should buffer VAD events and process them', async () => {\n            const processedEvents = [];\n            vadBuffer.registerHandler('voiceActivityDetected', (data, metadata) => {\n                processedEvents.push({ data, metadata });\n            });\n            \n            vadBuffer.startBuffering();\n            \n            // Buffer some events\n            vadBuffer.bufferEvent('voiceActivityDetected', { level: 0.8 }, { source: 'test' });\n            vadBuffer.bufferEvent('voiceActivityDetected', { level: 0.9 }, { source: 'test' });\n            \n            const status = vadBuffer.getStatus();\n            expect(status.buffering.bufferSize).toBe(2);\n            \n            // Stop buffering and flush\n            await vadBuffer.stopBuffering();\n            \n            // Wait for flush to complete\n            await new Promise(resolve => setTimeout(resolve, 150));\n            \n            expect(processedEvents).toHaveLength(2);\n            expect(processedEvents[0].data).toEqual({ level: 0.8 });\n            expect(processedEvents[1].data).toEqual({ level: 0.9 });\n        });\n        \n        it('should detect and skip duplicate events', async () => {\n            vadBuffer.startBuffering();\n            \n            const event1 = { level: 0.8 };\n            const event2 = { level: 0.8 }; // Duplicate\n            \n            const buffered1 = vadBuffer.bufferEvent('voiceActivityDetected', event1);\n            const buffered2 = vadBuffer.bufferEvent('voiceActivityDetected', event2);\n            \n            expect(buffered1).toBe(true);\n            expect(buffered2).toBe(false); // Should be rejected as duplicate\n            \n            const status = vadBuffer.getStatus();\n            expect(status.buffering.bufferSize).toBe(1);\n            expect(status.statistics.duplicateEvents).toBe(1);\n        });\n        \n        it('should handle event expiration', async () => {\n            const buffer = new VADEventBuffer({\n                maxEventAge: 50, // 50ms\n                logger: { debug: vi.fn(), info: vi.fn(), warn: vi.fn(), error: vi.fn() }\n            });\n            \n            const processedEvents = [];\n            buffer.registerHandler('test', (data) => processedEvents.push(data));\n            \n            buffer.startBuffering();\n            buffer.bufferEvent('test', { data: 'old' });\n            \n            // Wait for event to expire\n            await new Promise(resolve => setTimeout(resolve, 100));\n            \n            await buffer.stopBuffering();\n            await new Promise(resolve => setTimeout(resolve, 50));\n            \n            expect(processedEvents).toHaveLength(0); // Should be expired\n            \n            const status = buffer.getStatus();\n            expect(status.statistics.expiredEvents).toBe(1);\n            \n            await buffer.dispose();\n        });\n        \n        it('should retry failed event deliveries', async () => {\n            let attempts = 0;\n            vadBuffer.registerHandler('test', () => {\n                attempts++;\n                if (attempts < 2) {\n                    throw new Error('Handler failure');\n                }\n                return Promise.resolve();\n            });\n            \n            vadBuffer.startBuffering();\n            vadBuffer.bufferEvent('test', { data: 'test' });\n            \n            await vadBuffer.stopBuffering();\n            \n            // Wait for retries to complete\n            await new Promise(resolve => setTimeout(resolve, 200));\n            \n            expect(attempts).toBe(2); // Should have retried once\n        });\n    });\n    \n    describe('ProgressiveSystemStartup', () => {\n        it('should initialize components in dependency order', async () => {\n            const initOrder = [];\n            \n            systemStartup.registerComponent('database', {\n                initFunction: async () => {\n                    initOrder.push('database');\n                    await new Promise(resolve => setTimeout(resolve, 10));\n                },\n                dependencies: [],\n                criticalComponent: true\n            });\n            \n            systemStartup.registerComponent('auth', {\n                initFunction: async () => {\n                    initOrder.push('auth');\n                    await new Promise(resolve => setTimeout(resolve, 10));\n                },\n                dependencies: ['database'],\n                criticalComponent: true\n            });\n            \n            systemStartup.registerComponent('api', {\n                initFunction: async () => {\n                    initOrder.push('api');\n                    await new Promise(resolve => setTimeout(resolve, 10));\n                },\n                dependencies: ['database', 'auth'],\n                criticalComponent: false\n            });\n            \n            const success = await systemStartup.startSystem();\n            \n            expect(success).toBe(true);\n            expect(initOrder).toEqual(['database', 'auth', 'api']);\n            \n            const status = systemStartup.getStatus();\n            expect(status.components.initialized).toBe(3);\n            expect(status.components.failed).toBe(0);\n            expect(status.system.isReady).toBe(true);\n        });\n        \n        it('should handle component initialization failures', async () => {\n            const events = [];\n            systemStartup.on('componentFailed', (data) => events.push(data));\n            systemStartup.on('systemFailed', (data) => events.push(data));\n            \n            systemStartup.registerComponent('failing-critical', {\n                initFunction: async () => {\n                    throw new Error('Critical component failure');\n                },\n                dependencies: [],\n                criticalComponent: true,\n                retryAttempts: 1\n            });\n            \n            await expect(systemStartup.startSystem()).rejects.toThrow('Critical component');\n            \n            expect(events).toHaveLength(2); // componentFailed + systemFailed\n            expect(events[0].componentName).toBe('failing-critical');\n            expect(events[0].isCritical).toBe(true);\n        });\n        \n        it('should retry failed component initialization', async () => {\n            let attempts = 0;\n            \n            systemStartup.registerComponent('retry-component', {\n                initFunction: async () => {\n                    attempts++;\n                    if (attempts < 2) {\n                        throw new Error('Temporary failure');\n                    }\n                },\n                dependencies: [],\n                criticalComponent: false,\n                retryAttempts: 2\n            });\n            \n            const success = await systemStartup.startSystem();\n            \n            expect(success).toBe(true);\n            expect(attempts).toBe(2);\n            \n            const status = systemStartup.getStatus();\n            expect(status.components.initialized).toBe(1);\n        });\n        \n        it('should detect circular dependencies', () => {\n            systemStartup.registerComponent('a', {\n                initFunction: async () => {},\n                dependencies: ['b']\n            });\n            \n            expect(() => {\n                systemStartup.registerComponent('b', {\n                    initFunction: async () => {},\n                    dependencies: ['a']\n                });\n            }).toThrow('Circular dependency');\n        });\n        \n        it('should perform health checks on initialized components', async () => {\n            let healthCheckCalled = false;\n            \n            systemStartup.registerComponent('monitored', {\n                initFunction: async () => {},\n                dependencies: [],\n                healthCheck: async () => {\n                    healthCheckCalled = true;\n                    return true;\n                }\n            });\n            \n            await systemStartup.startSystem();\n            \n            // Trigger health check manually\n            await systemStartup._performHealthChecks();\n            \n            expect(healthCheckCalled).toBe(true);\n        });\n    });\n    \n    describe('Integration Scenarios', () => {\n        it('should handle complete 1011 reconnection scenario', async () => {\n            // Simulate 1011 reconnection scenario with all components\n            const vadEvents = [];\n            const audioEvents = [];\n            const errors = [];\n            \n            // Setup VAD buffer\n            vadBuffer.registerHandler('voiceActivityDetected', (data) => {\n                vadEvents.push(data);\n            });\n            \n            // Setup audio coordinator\n            audioCoordinator.on('audioReplay', (data) => {\n                audioEvents.push(data);\n            });\n            \n            // Start buffering (simulating reconnection)\n            vadBuffer.startBuffering();\n            audioCoordinator.startBuffering();\n            \n            // Buffer events during 'reconnection'\n            vadBuffer.bufferEvent('voiceActivityDetected', { level: 0.8 });\n            vadBuffer.bufferEvent('voiceActivityDetected', { level: 0.9 });\n            \n            audioCoordinator.bufferAudio({ chunk: 'audio1' });\n            audioCoordinator.bufferAudio({ chunk: 'audio2' });\n            \n            // Simulate reconnection complete\n            await vadBuffer.stopBuffering();\n            await audioCoordinator.stopBuffering(true);\n            \n            // Wait for processing\n            await new Promise(resolve => setTimeout(resolve, 200));\n            \n            // Verify all events were processed\n            expect(vadEvents).toHaveLength(2);\n            expect(audioEvents).toHaveLength(2);\n            \n            // Verify buffer status\n            const vadStatus = vadBuffer.getStatus();\n            const audioStatus = audioCoordinator.getBufferStatus();\n            \n            expect(vadStatus.buffering.isBuffering).toBe(false);\n            expect(audioStatus.isBuffering).toBe(false);\n            expect(vadStatus.statistics.processedEvents).toBe(2);\n        });\n        \n        it('should recover from multiple system failures', async () => {\n            const operations = [];\n            let systemFailures = 0;\n            \n            // Create operation that fails initially\n            const unreliableOperation = async () => {\n                operations.push(Date.now());\n                systemFailures++;\n                \n                if (systemFailures <= 2) {\n                    throw new Error('System temporary failure');\n                }\n                \n                return 'success';\n            };\n            \n            // Execute with error recovery\n            const result = await errorRecovery.executeWithCircuitBreaker(\n                unreliableOperation,\n                'unreliable-system'\n            );\n            \n            expect(result).toBe('success');\n            expect(operations).toHaveLength(3); // Initial + 2 retries\n            expect(systemFailures).toBe(3);\n            \n            const status = errorRecovery.getStatus();\n            expect(status.operations.retried).toBeGreaterThan(0);\n            expect(status.health.isHealthy).toBe(true);\n        });\n        \n        it('should coordinate progressive startup with runtime recovery', async () => {\n            const startupEvents = [];\n            let dbInitAttempts = 0;\n            \n            systemStartup.on('componentStarted', (data) => {\n                startupEvents.push(data.componentName);\n            });\n            \n            // Register components that use error recovery\n            systemStartup.registerComponent('database', {\n                initFunction: async () => {\n                    return await errorRecovery.executeWithCircuitBreaker(async () => {\n                        dbInitAttempts++;\n                        if (dbInitAttempts < 2) {\n                            throw new Error('DB connection failed');\n                        }\n                        return 'db-ready';\n                    }, 'db-init');\n                },\n                dependencies: [],\n                criticalComponent: true\n            });\n            \n            systemStartup.registerComponent('cache', {\n                initFunction: async () => {\n                    return await errorRecovery.executeWithCircuitBreaker(async () => {\n                        return 'cache-ready';\n                    }, 'cache-init');\n                },\n                dependencies: ['database'],\n                criticalComponent: false\n            });\n            \n            const success = await systemStartup.startSystem();\n            \n            expect(success).toBe(true);\n            expect(startupEvents).toEqual(['database', 'cache']);\n            expect(dbInitAttempts).toBe(2); // Should have retried once\n            \n            const systemStatus = systemStartup.getStatus();\n            const recoveryStatus = errorRecovery.getStatus();\n            \n            expect(systemStatus.system.isReady).toBe(true);\n            expect(recoveryStatus.operations.successful).toBe(2);\n        });\n    });\n});