/**
 * Role-Playing System Validation Framework
 * 
 * Comprehensive validation testing for:
 * - Character data accuracy
 * - Voice transcription quality  
 * - System 2 summarization effectiveness
 * - Dual brain personality consistency
 * - Performance benchmarks
 */

import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import { CharacterService } from '../../app/viewer/services/CharacterService.js';
import { AgentCoordinator } from '../../app/viewer/services/agentCoordinator.ts';
import { DualBrainCoordinator } from '../../src/agent/arch/dualbrain/DualBrainCoordinator.js';

describe('Role-Playing System Validation Tests', () => {
    let characterService;
    let agentCoordinator; 
    let mockAgentService;

    // Validation test data
    const validationCharacters = [
        {
            id: 'sherlock',
            name: '<PERSON>',
            description: 'Brilliant detective with exceptional deductive reasoning',
            avatar: '🔍',
            personality: {
                formality: 0.8,
                enthusiasm: 0.6,
                empathy: 0.4,
                creativity: 0.9,
                directness: 0.9
            },
            voiceStyle: 'authoritative',
            systemPrompt: 'You are <PERSON>, the world\'s greatest consulting detective. You use logical deduction and keen observation to solve mysteries.'
        },
        {
            id: 'gandalf',
            name: '<PERSON><PERSON><PERSON> the <PERSON>',
            description: 'Wise wizard with ancient knowledge and magical powers',
            avatar: '🧙‍♂️',
            personality: {
                formality: 0.7,
                enthusiasm: 0.5,
                empathy: 0.8,
                creativity: 0.8,
                directness: 0.6
            },
            voiceStyle: 'wise',
            systemPrompt: 'You are Gandalf the Grey, a wise wizard who guides others with ancient wisdom and magical insight.'
        }
    ];

    beforeEach(async () => {
        // Mock agent service with enhanced functionality
        mockAgentService = {
            initialize: jest.fn().mockResolvedValue(true),
            isDualBrainMode: jest.fn().mockReturnValue(true),
            getModel: jest.fn().mockImplementation((type) => ({
                constructor: { name: `${type || 'primary'}Model` },
                apiMode: 'http',
                updateSystemPrompt: jest.fn().mockResolvedValue(true),
                isRealtimeModeActive: jest.fn().mockReturnValue(true),
                invoke: jest.fn().mockImplementation(async (prompt) => {
                    // Simulate realistic model responses based on prompt content
                    if (prompt.includes('Sherlock Holmes')) {
                        return 'Elementary, my dear Watson. The evidence clearly indicates...';
                    } else if (prompt.includes('Gandalf')) {
                        return 'A wizard is never late, nor is he early. He arrives precisely when he means to.';
                    }
                    return 'I understand your request and will respond accordingly.';
                }),
                generateResponse: jest.fn().mockImplementation(async (input, options) => {
                    // Enhanced response simulation
                    if (options?.modelOverride?.constructor?.name?.includes('system2')) {
                        return {
                            content: `Analytical response for: ${input}`,
                            thinking: 'Detailed analysis of the request...',
                            confidence: 0.85
                        };
                    }
                    return `Response to: ${input}`;
                })
            })),
            updateCharacterContext: jest.fn().mockResolvedValue(true),
            generateResponse: jest.fn().mockResolvedValue('Mock response'),
            setDualBrainCoordinator: jest.fn(),
            getDualBrainCoordinator: jest.fn(),
            updateDualBrainContext: jest.fn(),
            options: {
                agentConfig: {
                    enableDualBrain: true,
                    periodicAnalysisInterval: 2000,
                    decisionCooldown: 5000
                }
            }
        };

        // Initialize services for validation
        characterService = new CharacterService({
            enablePersistence: false,
            storageKey: 'validation_character_data'
        });

        agentCoordinator = new AgentCoordinator({
            logger: { 
                info: jest.fn(), 
                warn: jest.fn(), 
                error: jest.fn(),
                debug: jest.fn()
            },
            enableVADHandlers: false
        });
        
        agentCoordinator.agentService = mockAgentService;
    });

    afterEach(async () => {
        if (agentCoordinator) {
            await agentCoordinator.dispose();
        }
        if (characterService) {
            characterService.dispose();
        }
    });

    describe('1. Character Data Accuracy Validation', () => {
        test('should validate character personality trait ranges', () => {
            validationCharacters.forEach(character => {
                Object.entries(character.personality).forEach(([trait, value]) => {
                    expect(value).toBeGreaterThanOrEqual(0);
                    expect(value).toBeLessThanOrEqual(1);
                    expect(typeof value).toBe('number');
                });
            });
        });

        test('should validate character data completeness', () => {
            const requiredFields = ['id', 'name', 'description', 'personality', 'voiceStyle', 'systemPrompt'];
            const personalityTraits = ['formality', 'enthusiasm', 'empathy', 'creativity', 'directness'];

            validationCharacters.forEach(character => {
                // Check required fields
                requiredFields.forEach(field => {
                    expect(character[field]).toBeDefined();
                    expect(character[field]).not.toBe('');
                });

                // Check personality traits
                personalityTraits.forEach(trait => {
                    expect(character.personality[trait]).toBeDefined();
                    expect(typeof character.personality[trait]).toBe('number');
                });
            });
        });

        test('should validate personality coherence scores', async () => {
            for (const character of validationCharacters) {
                await characterService.setCharacterContext(character);
                
                const profiles = characterService.getPersonalityProfiles();
                const profile = profiles.get(character.id);
                
                expect(profile).toBeDefined();
                expect(profile.consistencyScore).toBeGreaterThanOrEqual(0);
                expect(profile.consistencyScore).toBeLessThanOrEqual(1);
                
                // Sherlock should have high consistency (logical traits)
                if (character.id === 'sherlock') {
                    expect(profile.consistencyScore).toBeGreaterThan(0.7);
                }
            }
        });

        test('should validate character uniqueness and differentiation', () => {
            const sherlockChar = validationCharacters.find(c => c.id === 'sherlock');
            const gandalfChar = validationCharacters.find(c => c.id === 'gandalf');
            
            // Characters should have different personality profiles
            expect(sherlockChar.personality.empathy).not.toBe(gandalfChar.personality.empathy);
            expect(sherlockChar.voiceStyle).not.toBe(gandalfChar.voiceStyle);
            
            // Calculate personality distance
            const personalityDistance = Object.keys(sherlockChar.personality).reduce((sum, trait) => {
                return sum + Math.abs(sherlockChar.personality[trait] - gandalfChar.personality[trait]);
            }, 0);
            
            expect(personalityDistance).toBeGreaterThan(0.5); // Should be meaningfully different
        });

        test('should validate system prompt alignment with personality', async () => {
            for (const character of validationCharacters) {
                await characterService.setCharacterContext(character);
                
                const currentChar = characterService.getCurrentCharacter();
                
                // System prompt should reflect character traits
                if (character.id === 'sherlock') {
                    expect(currentChar.systemPrompt.toLowerCase()).toContain('detective');
                    expect(currentChar.systemPrompt.toLowerCase()).toContain('dedu');
                }
                
                if (character.id === 'gandalf') {
                    expect(currentChar.systemPrompt.toLowerCase()).toContain('wizard');
                    expect(currentChar.systemPrompt.toLowerCase()).toContain('wise');
                }
            }
        });
    });

    describe('2. Voice Transcription Quality Validation', () => {
        const voiceTestCases = [
            {
                audio_input: 'mock_audio_luffy.wav',
                expected_transcript: 'I want to be Luffy',
                expected_confidence: 0.9,
                expected_character: 'luffy'
            },
            {
                audio_input: 'mock_audio_sherlock.wav', 
                expected_transcript: 'Select Sherlock Holmes',
                expected_confidence: 0.85,
                expected_character: 'sherlock'
            },
            {
                audio_input: 'mock_audio_noisy.wav',
                expected_transcript: 'I want to be Gandalf',
                expected_confidence: 0.6,
                expected_character: 'gandalf'
            }
        ];

        test('should validate voice transcription accuracy', async () => {
            voiceTestCases.forEach(testCase => {
                // Simulate speech recognition result
                const transcript = testCase.expected_transcript;
                const confidence = testCase.expected_confidence;
                
                // Extract character name from transcript
                const characterMatch = transcript.match(/(?:be|select|choose)\s+(\w+(?:\s+\w+)?)/i);
                const extractedName = characterMatch?.[1];
                
                expect(extractedName).toBeDefined();
                
                // Validate confidence thresholds
                if (confidence > 0.8) {
                    expect(extractedName.toLowerCase()).toContain(testCase.expected_character);
                } else if (confidence > 0.6) {
                    // Should still attempt matching but with lower confidence
                    expect(extractedName).toBeDefined();
                }
            });
        });

        test('should handle voice input variations and ambiguity', () => {
            const ambiguousInputs = [
                { transcript: 'I want to be like Luffy', confidence: 0.7 },
                { transcript: 'Make me similar to Sherlock', confidence: 0.65 },
                { transcript: 'Choose the detective guy', confidence: 0.5 }
            ];

            ambiguousInputs.forEach(input => {
                const words = input.transcript.toLowerCase().split(' ');
                
                // Should identify intent even with variations
                const hasIntent = words.some(word => 
                    ['be', 'like', 'similar', 'choose', 'select', 'make'].includes(word)
                );
                
                expect(hasIntent).toBe(true);
                
                // Low confidence inputs should trigger confirmation
                if (input.confidence < 0.7) {
                    expect(input.confidence).toBeLessThan(0.7);
                }
            });
        });

        test('should validate noise resistance and error handling', () => {
            const noisyInputs = [
                { 
                    transcript: 'I... uh... want to be... *cough* ...Luffy', 
                    confidence: 0.4,
                    cleaned: 'I want to be Luffy'
                },
                {
                    transcript: 'Select [INAUDIBLE] Holmes',
                    confidence: 0.3,
                    cleaned: 'Select Holmes'
                }
            ];

            noisyInputs.forEach(input => {
                // Simulate noise cleaning
                const cleaned = input.transcript
                    .replace(/\.\.\.|uh|um|\*\w+\*|\[INAUDIBLE\]/gi, '')
                    .replace(/\s+/g, ' ')
                    .trim();
                
                expect(cleaned.length).toBeGreaterThan(0);
                
                // Low confidence should trigger retry or fallback
                if (input.confidence < 0.5) {
                    expect(input.confidence).toBeLessThan(0.5);
                }
            });
        });

        test('should validate multi-language character name handling', () => {
            const internationalNames = [
                { input: 'I want to be Natsu', expected: 'natsu' },
                { input: 'Select Ichigo', expected: 'ichigo' },
                { input: 'Choose Naruto', expected: 'naruto' }
            ];

            internationalNames.forEach(test => {
                const extractedName = test.input.match(/(?:be|select|choose)\s+(\w+)/i)?.[1];
                expect(extractedName?.toLowerCase()).toBe(test.expected);
            });
        });
    });

    describe('3. System 2 Summarization Effectiveness', () => {
        test('should generate comprehensive character analysis', async () => {
            const character = validationCharacters[0]; // Sherlock
            
            // Mock detailed System 2 analysis response
            const mockAnalysis = {
                content: `Character Analysis: Sherlock Holmes
                
                <thinking>
                Analyzing personality traits:
                - High formality (0.8): Proper Victorian gentleman speech patterns
                - Moderate enthusiasm (0.6): Passionate about cases but controlled
                - Low empathy (0.4): Often dismissive of emotions, focuses on logic
                - High creativity (0.9): Innovative deductive methods
                - High directness (0.9): Blunt, doesn't sugarcoat observations
                
                Coherence check: Traits align well with detective archetype
                Voice style "authoritative" matches high formality and directness
                </thinking>
                
                Personality Summary:
                - Analytical and methodical approach to problems
                - Formal speech patterns with Victorian-era vocabulary
                - Limited emotional expression but deep logical insights
                - Creative problem-solving through unconventional deduction
                - Direct communication style, sometimes insensitive
                
                Behavioral Predictions:
                - Will approach problems systematically
                - May dismiss emotional concerns in favor of facts
                - Likely to notice minute details others miss
                - Will use formal, precise language
                
                Consistency Rating: 0.85/1.0 - High coherence between traits`,
                thinking: 'Detailed character analysis with trait coherence validation',
                confidence: 0.88
            };

            mockAgentService.generateResponse.mockResolvedValue(mockAnalysis);
            
            await characterService.setCharacterContext(character);
            
            // Validate analysis was generated
            expect(mockAgentService.generateResponse).toHaveBeenCalled();
            
            const profiles = characterService.getPersonalityProfiles();
            const profile = profiles.get('sherlock');
            
            expect(profile).toBeDefined();
            expect(profile.consistencyScore).toBeGreaterThan(0.7);
        });

        test('should identify personality conflicts and inconsistencies', async () => {
            // Create character with conflicting traits
            const conflictedCharacter = {
                id: 'conflicted',
                name: 'Conflicted Character',
                description: 'Character with internal contradictions',
                avatar: '😵‍💫',
                personality: {
                    formality: 0.95,    // Very formal
                    enthusiasm: 0.95,   // Very enthusiastic (potential conflict)
                    empathy: 0.1,       // Very low empathy  
                    creativity: 0.95,   // Very creative
                    directness: 0.1     // Very indirect (conflicts with low empathy)
                },
                voiceStyle: 'professional',
                systemPrompt: 'You are a conflicted character with contradictory traits.'
            };

            await characterService.setCharacterContext(conflictedCharacter);
            
            const profiles = characterService.getPersonalityProfiles();
            const profile = profiles.get('conflicted');
            
            // Should detect conflicts and lower consistency score
            expect(profile.consistencyScore).toBeLessThan(0.6);
        });

        test('should provide actionable personality insights', async () => {
            const character = validationCharacters[1]; // Gandalf
            
            const mockInsights = {
                content: `Character Insights: Gandalf the Grey
                
                Strengths:
                - High empathy (0.8) enables deep understanding of others
                - Balanced creativity (0.8) and formality (0.7) for wise counsel
                - Moderate directness allows diplomatic approach
                
                Communication Style:
                - Uses metaphors and analogies (high creativity)
                - Speaks with gravitas and wisdom (formal but empathetic)
                - Considers emotional impact before speaking (moderate directness)
                
                Interaction Patterns:
                - Will guide rather than command
                - Provides wisdom through stories and examples
                - Shows patience with others' learning processes
                
                Optimization Suggestions:
                - Leverage high empathy for emotional intelligence
                - Use creative analogies to explain complex concepts
                - Balance wisdom-sharing with practical action`,
                actionableInsights: [
                    'Use storytelling to convey wisdom',
                    'Show patience with learning processes', 
                    'Balance guidance with emotional support'
                ]
            };

            mockAgentService.generateResponse.mockResolvedValue(mockInsights);
            
            await characterService.setCharacterContext(character);
            
            expect(mockAgentService.generateResponse).toHaveBeenCalledWith(
                expect.stringContaining('Gandalf'),
                expect.objectContaining({
                    priority: 'accuracy'  
                })
            );
        });

        test('should validate summarization accuracy against ground truth', async () => {
            const character = validationCharacters[0]; // Sherlock
            
            // Ground truth expectations for Sherlock
            const expectedTraits = {
                analytical: true,
                logical: true,
                observant: true,
                formal: true,
                direct: true,
                empathetic: false
            };

            await characterService.setCharacterContext(character);
            
            // Validate personality description generation
            const personalityDesc = characterService.generatePersonalityDescription(character.personality);
            
            // Should reflect expected traits
            expect(personalityDesc.toLowerCase()).toContain('formal');
            expect(personalityDesc.toLowerCase()).toContain('direct');
            
            // High formality should be recognized
            expect(character.personality.formality).toBeGreaterThan(0.7);
            expect(character.personality.directness).toBeGreaterThan(0.7);
            
            // Low empathy should be recognized  
            expect(character.personality.empathy).toBeLessThan(0.5);
        });
    });

    describe('4. Dual Brain Personality Consistency', () => {
        let dualBrainCoordinator;

        beforeEach(async () => {
            dualBrainCoordinator = new DualBrainCoordinator(mockAgentService, {
                enableProactiveDecisions: true,
                system2AnalysisInterval: 1000,  
                decisionCooldown: 2000
            });

            await dualBrainCoordinator.initialize();
            mockAgentService.getDualBrainCoordinator.mockReturnValue(dualBrainCoordinator);
        });

        afterEach(async () => {
            if (dualBrainCoordinator) {
                await dualBrainCoordinator.stopDualBrainSystems();
                dualBrainCoordinator.dispose();
            }
        });

        test('should maintain character consistency across System 1 responses', async () => {
            const character = validationCharacters[0]; // Sherlock
            
            await agentCoordinator.updateCharacterPersonality(character);
            await dualBrainCoordinator.startDualBrainSystems();
            
            // Test multiple System 1 (fast) responses
            const quickInputs = [
                'What do you see?',
                'Who did it?',
                'How do you know?'
            ];

            const responses = [];
            for (const input of quickInputs) {
                const response = await dualBrainCoordinator.generateProactiveDecision({
                    conversational: {
                        messages: [{ role: 'user', content: input }],
                        complexity: 'low',  // Should trigger System 1
                        requiresReasoning: false
                    },
                    environmental: {
                        activity: 'quick_response',
                        timestamp: Date.now()
                    }
                });
                responses.push(response);
            }

            // All responses should reflect Sherlock's personality
            expect(responses).toHaveLength(3);
            responses.forEach(response => {
                expect(response).toBeDefined();
                expect(response).toHaveProperty('shouldAct');
                expect(response).toHaveProperty('confidence');
                expect(response).toHaveProperty('reason');
            });

            // Verify System 1 model received character context
            const system1Model = mockAgentService.getModel('system1');
            expect(system1Model.updateSystemPrompt).toHaveBeenCalledWith(
                expect.stringContaining('Sherlock Holmes')
            );
        });

        test('should maintain character consistency in System 2 analysis', async () => {
            const character = validationCharacters[1]; // Gandalf
            
            await agentCoordinator.updateCharacterPersonality(character);  
            await dualBrainCoordinator.startDualBrainSystems();
            
            // Test System 2 (analytical) response
            const complexInput = 'What is the deeper meaning of this quest and how should we proceed?';
            
            const response = await dualBrainCoordinator.generateProactiveDecision({
                conversational: {
                    messages: [{ role: 'user', content: complexInput }],
                    complexity: 'high',
                    requiresReasoning: true  // Should trigger System 2
                },
                environmental: {
                    activity: 'complex_analysis',
                    timestamp: Date.now()
                }
            });

            expect(response).toBeDefined();
            expect(response).toHaveProperty('shouldAct');
            expect(response).toHaveProperty('confidence');
            expect(response).toHaveProperty('reason');
            
            // Verify System 2 model received character context
            const system2Model = mockAgentService.getModel('system2');
            expect(system2Model.updateSystemPrompt).toHaveBeenCalledWith(
                expect.stringContaining('Gandalf')
            );
        });

        test('should coordinate personality-consistent decisions between systems', async () => {
            const character = validationCharacters[0]; // Sherlock
            
            await agentCoordinator.updateCharacterPersonality(character);
            await dualBrainCoordinator.startDualBrainSystems();
            
            // Mock proactive decision with character personality
            const mockDecision = `{
                "shouldAct": true,
                "shouldSpeak": true,
                "confidence": 0.9,
                "reason": "Observation reveals important deduction",
                "urgency": "high",
                "suggestedAction": "Point out crucial detail others missed",
                "characterResponse": "analytical_insightful",
                "thinkingProcess": "Logical deduction based on available evidence",
                "contextualFactors": ["detail_oriented", "observational", "deductive"]
            }`;
            
            mockAgentService.generateResponse.mockResolvedValue(mockDecision);
            
            const decision = await dualBrainCoordinator.generateProactiveDecision({
                environmental: { clues: ['footprint', 'ash', 'torn_fabric'] }
            });
            
            expect(decision.shouldAct).toBe(true);
            expect(decision.characterResponse).toBe('analytical_insightful');
            expect(decision.contextualFactors).toContain('deductive');
        });

        test('should prevent personality drift over extended interactions', async () => {
            const character = validationCharacters[1]; // Gandalf
            
            await agentCoordinator.updateCharacterPersonality(character);
            await dualBrainCoordinator.startDualBrainSystems();
            
            // Simulate extended conversation
            const conversationInputs = [
                'Tell me about wisdom',
                'How should I face my fears?',
                'What is the purpose of our journey?',
                'Should we take the dangerous path?',
                'How do I know if I\'m making the right choice?'
            ];

            const responses = [];
            for (const input of conversationInputs) {
                const response = await dualBrainCoordinator.generateProactiveDecision({
                    conversational: {
                        messages: [{ role: 'user', content: input }],
                        complexity: 'high',
                        requiresReasoning: true
                    },
                    environmental: {
                        activity: 'extended_conversation',
                        timestamp: Date.now()
                    }
                });
                responses.push(response);
                
                // Small delay to simulate real conversation
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            // All responses should maintain character consistency
            expect(responses).toHaveLength(5);
            
            // Get consistency metrics
            const metrics = characterService.getConsistencyMetrics();
            
            // Consistency should remain high throughout extended interaction
            expect(metrics.personalityStability).toBeGreaterThan(0.7);
            expect(metrics.contextualRelevance).toBeGreaterThan(0.7);
        });

        test('should adapt personality expression to context while maintaining core traits', async () => {
            const character = validationCharacters[0]; // Sherlock
            
            await agentCoordinator.updateCharacterPersonality(character);
            await dualBrainCoordinator.startDualBrainSystems();
            
            // Test different contexts
            const contextualInputs = [
                { input: 'Comfort a grieving widow', context: 'emotional_support', expected_adaptation: 'more_empathetic' },
                { input: 'Explain deduction to Watson', context: 'teaching', expected_adaptation: 'patient_explanatory' },
                { input: 'Confront the criminal', context: 'confrontational', expected_adaptation: 'authoritative_direct' }
            ];

            for (const test of contextualInputs) {
                const response = await dualBrainCoordinator.generateProactiveDecision({
                    conversational: {
                        messages: [{ role: 'user', content: test.input }],
                        complexity: 'high',
                        requiresReasoning: true,
                        context: test.context
                    },
                    environmental: {
                        activity: 'contextual_adaptation',
                        context: test.context,
                        timestamp: Date.now()
                    }
                });
                
                expect(response).toBeDefined();
                expect(response).toHaveProperty('shouldAct');
                expect(response).toHaveProperty('confidence');
                expect(response).toHaveProperty('reason');
                
                // Core personality should remain (high directness, low empathy baseline)
                // but expression should adapt to context
                const currentChar = characterService.getCurrentCharacter();
                expect(currentChar.personality.directness).toBeGreaterThan(0.8); // Core trait maintained
                expect(currentChar.personality.formality).toBeGreaterThan(0.7); // Core trait maintained
            }
        });
    });

    describe('5. Performance Benchmarks', () => {
        test('should meet character search performance requirements', async () => {
            const startTime = performance.now();
            
            // Create large character dataset
            const largeCharacterSet = Array.from({ length: 1000 }, (_, i) => ({
                id: `character_${i}`,
                name: `Character ${i}`,
                description: `Test character number ${i}`,
                personality: {
                    formality: Math.random(),
                    enthusiasm: Math.random(),
                    empathy: Math.random(),
                    creativity: Math.random(),
                    directness: Math.random()
                }
            }));
            
            // Search through large dataset
            const searchTerm = 'Character 42';
            const results = largeCharacterSet.filter(char => 
                char.name.toLowerCase().includes(searchTerm.toLowerCase())
            );
            
            const endTime = performance.now();
            const searchTime = endTime - startTime;
            
            expect(searchTime).toBeLessThan(50); // Should complete within 50ms
            expect(results).toHaveLength(1);
            expect(results[0].name).toBe('Character 42');
        });

        test('should meet character application performance requirements', async () => {
            const character = validationCharacters[0];
            
            const startTime = performance.now();
            
            // Apply character configuration
            await characterService.setCharacterContext(character);
            await agentCoordinator.updateCharacterPersonality(character);
            
            const endTime = performance.now();
            const applicationTime = endTime - startTime;
            
            expect(applicationTime).toBeLessThan(500); // Should complete within 500ms
        });

        test('should handle concurrent character operations efficiently', async () => {
            const startTime = performance.now();
            
            // Execute multiple operations concurrently
            const operations = [
                () => characterService.setCharacterContext(validationCharacters[0]),
                () => characterService.setCharacterContext(validationCharacters[1]),
                () => agentCoordinator.updateCharacterPersonality(validationCharacters[0]),
                () => characterService.getPersonalityProfiles(),
                () => characterService.getConsistencyMetrics()
            ];
            
            await Promise.all(operations.map(op => op()));
            
            const endTime = performance.now();
            const concurrentTime = endTime - startTime;
            
            expect(concurrentTime).toBeLessThan(1000); // Should complete within 1 second
        });

        test('should maintain memory efficiency under load', async () => {
            const initialMemory = process.memoryUsage().heapUsed;
            
            // Simulate heavy usage
            for (let i = 0; i < 100; i++) {
                await characterService.setCharacterContext(validationCharacters[i % 2]);
                characterService.updatePersonalityProfile(validationCharacters[i % 2]);
            }
            
            // Force garbage collection if available
            if (global.gc) {
                global.gc();
            }
            
            const finalMemory = process.memoryUsage().heapUsed;
            const memoryIncrease = finalMemory - initialMemory;
            
            // Memory increase should be reasonable (less than 50MB)
            expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
        });

        test('should validate response time consistency', async () => {
            const character = validationCharacters[0];
            await characterService.setCharacterContext(character);
            
            const responseTimes = [];
            
            // Measure response times for multiple operations
            for (let i = 0; i < 10; i++) {
                const startTime = performance.now();
                await characterService.setCharacterContext(character);
                const endTime = performance.now();
                
                responseTimes.push(endTime - startTime);
            }
            
            // Calculate statistics
            const avgTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
            const maxTime = Math.max(...responseTimes);
            const minTime = Math.min(...responseTimes);
            
            expect(avgTime).toBeLessThan(100); // Average should be under 100ms
            expect(maxTime).toBeLessThan(200); // Max should be under 200ms
            expect(maxTime - minTime).toBeLessThan(150); // Variance should be reasonable
        });

        test('should scale efficiently with character complexity', async () => {
            // Simple character
            const simpleCharacter = {
                ...validationCharacters[0],
                personality: { formality: 0.5, enthusiasm: 0.5, empathy: 0.5, creativity: 0.5, directness: 0.5 }
            };
            
            // Complex character with detailed traits  
            const complexCharacter = {
                ...validationCharacters[1],
                personality: {
                    formality: 0.7234,
                    enthusiasm: 0.4891,
                    empathy: 0.8456,
                    creativity: 0.7823,
                    directness: 0.6234
                },
                additionalTraits: {
                    wisdom: 0.95,
                    patience: 0.88,
                    magical_knowledge: 0.92
                }
            };
            
            // Measure processing times
            const simpleStartTime = performance.now();
            await characterService.setCharacterContext(simpleCharacter);
            const simpleEndTime = performance.now();
            
            const complexStartTime = performance.now();
            await characterService.setCharacterContext(complexCharacter);
            const complexEndTime = performance.now();
            
            const simpleTime = simpleEndTime - simpleStartTime;
            const complexTime = complexEndTime - complexStartTime;
            
            // Complex character should not take more than 2x the time
            expect(complexTime).toBeLessThan(simpleTime * 2);
            expect(complexTime).toBeLessThan(300); // Absolute maximum
        });
    });

    describe('6. Integration Validation', () => {
        test('should validate end-to-end character workflow', async () => {
            const character = validationCharacters[0]; // Sherlock
            
            // Step 1: Character selection and validation
            const isValid = characterService.validateCharacterStructure(character);
            expect(isValid).toBe(true);
            
            // Step 2: Character application
            const applied = await characterService.setCharacterContext(character);
            expect(applied).toBe(true);
            
            // Step 3: Agent coordinator integration
            const coordinated = await agentCoordinator.updateCharacterPersonality(character);
            expect(coordinated).toBe(true);
            
            // Step 4: Verification
            const currentChar = characterService.getCurrentCharacter();
            expect(currentChar).toBeDefined();
            expect(currentChar.name).toBe('Sherlock Holmes');
            
            const metrics = characterService.getConsistencyMetrics();
            expect(metrics).toBeDefined();
        });

        test('should validate error recovery and fallback mechanisms', async () => {
            // Test with invalid character data
            const invalidCharacter = {
                id: 'invalid',
                name: '', // Empty name should cause validation failure
                personality: {
                    formality: 1.5, // Out of range should cause validation failure
                    enthusiasm: -0.5 // Out of range
                }
            };
            
            const isValid = characterService.validateCharacterStructure(invalidCharacter);
            expect(isValid).toBe(false);
            
            // System should handle gracefully
            const applied = await characterService.setCharacterContext(invalidCharacter);
            expect(applied).toBe(false);
            
            // Fallback to default should work
            const validCharacter = validationCharacters[0];
            const fallbackApplied = await characterService.setCharacterContext(validCharacter);
            expect(fallbackApplied).toBe(true);
        });

        test('should validate cross-component data consistency', async () => {
            const character = validationCharacters[1]; // Gandalf
            
            // Apply through character service
            await characterService.setCharacterContext(character);
            const serviceChar = characterService.getCurrentCharacter();
            
            // Apply through agent coordinator
            await agentCoordinator.updateCharacterPersonality(character);
            const coordinatorChar = agentCoordinator.getCurrentCharacterContext();
            
            // Both should have consistent data
            expect(serviceChar.name).toBe(coordinatorChar.identity.name);
            expect(serviceChar.personality.empathy).toBe(coordinatorChar.personality.traits.empathy);
            expect(serviceChar.voiceStyle).toBe(coordinatorChar.communication.voiceStyle);
        });
    });
});