/**
 * Comprehensive Integration Testing Plan for Role-Playing System
 * 
 * Tests all components working together:
 * - Character search functionality
 * - Voice input for character selection  
 * - System 2 character summarization
 * - Integration with dual brain system
 * - UI/UX flow validation
 */

import { describe, test, expect, beforeEach, afterEach, beforeAll, afterAll } from '@jest/globals';
import { CharacterService } from '../../app/viewer/services/CharacterService.js';
import { AgentCoordinator } from '../../app/viewer/services/agentCoordinator.ts';
import { DualBrainCoordinator } from '../../src/agent/arch/dualbrain/DualBrainCoordinator.js';
import { RolePlayingPanel } from '../../app/viewer/components/RolePlayingPanel.js';

describe('Role-Playing System Integration Tests', () => {
    let characterService;
    let agentCoordinator;
    let dualBrainCoordinator; 
    let rolePlayingPanel;
    let mockAgentService;
    let testContainer;

    // Test data
    const testCharacters = [
        {
            id: 'luffy',
            name: '<PERSON>',
            description: 'Optimistic, determined pirate captain with rubber powers',
            avatar: '🏴‍☠️',
            personality: {
                formality: 0.2,
                enthusiasm: 0.95,
                empathy: 0.8,
                creativity: 0.9,
                directness: 0.9
            },
            voiceStyle: 'energetic',
            systemPrompt: 'You are Monkey D. Luffy, captain of the Straw Hat Pirates. You are optimistic, determined, and always ready for adventure.'
        },
        {
            id: 'saitama',
            name: 'Saitama',
            description: 'Bored superhero who can defeat any enemy with one punch',
            avatar: '👊',
            personality: {
                formality: 0.3,
                enthusiasm: 0.2,
                empathy: 0.6,
                creativity: 0.4,
                directness: 0.9
            },
            voiceStyle: 'casual',
            systemPrompt: 'You are Saitama, a hero who can defeat any enemy with one punch. You are generally bored and unenthusiastic but still help people.'
        }
    ];

    beforeAll(async () => {
        // Setup DOM environment for UI testing
        global.document = {
            createElement: jest.fn(() => ({
                className: '',
                classList: {
                    add: jest.fn(),
                    remove: jest.fn(),
                    contains: jest.fn(),
                    toggle: jest.fn()
                },
                appendChild: jest.fn(),
                setAttribute: jest.fn(),
                addEventListener: jest.fn(),
                querySelector: jest.fn(),
                querySelectorAll: jest.fn(() => []),
                innerHTML: '',
                style: {},
                parentNode: {
                    removeChild: jest.fn()
                }
            })),
            body: {
                appendChild: jest.fn()
            }
        };

        global.localStorage = {
            getItem: jest.fn(),
            setItem: jest.fn(),
            removeItem: jest.fn()
        };

        global.console = {
            log: jest.fn(),
            warn: jest.fn(),
            error: jest.fn(),
            info: jest.fn()
        };
    });

    beforeEach(async () => {
        // Create test container
        testContainer = {
            appendChild: jest.fn()
        };

        // Mock agent service
        mockAgentService = {
            initialize: jest.fn().mockResolvedValue(true),
            isDualBrainMode: jest.fn().mockReturnValue(true),
            getModel: jest.fn().mockImplementation((type) => ({
                constructor: { name: `${type || 'primary'}Model` },
                apiMode: 'http',
                updateSystemPrompt: jest.fn().mockResolvedValue(true),
                isRealtimeModeActive: jest.fn().mockReturnValue(true),
                invoke: jest.fn().mockResolvedValue('Mock response'),
                generateResponse: jest.fn().mockResolvedValue('Mock response')
            })),
            updateCharacterContext: jest.fn().mockResolvedValue(true),
            generateResponse: jest.fn().mockResolvedValue('Mock response'),
            setDualBrainCoordinator: jest.fn(),
            getDualBrainCoordinator: jest.fn(),
            updateDualBrainContext: jest.fn(),
            options: {
                agentConfig: {
                    enableDualBrain: true,
                    periodicAnalysisInterval: 2000,
                    decisionCooldown: 5000
                }
            }
        };

        // Initialize services
        characterService = new CharacterService({
            enablePersistence: false,
            storageKey: 'test_character_data'
        });

        agentCoordinator = new AgentCoordinator({
            logger: { 
                info: jest.fn(), 
                warn: jest.fn(), 
                error: jest.fn(),
                debug: jest.fn()
            },
            enableVADHandlers: false
        });

        rolePlayingPanel = new RolePlayingPanel({
            container: testContainer,
            characterService,
            agentCoordinator,
            onCharacterChange: jest.fn(),
            onPersonalityUpdate: jest.fn()
        });

        // Add test characters
        testCharacters.forEach(char => {
            rolePlayingPanel.addCharacterPreset(char);
        });
    });

    afterEach(async () => {
        if (rolePlayingPanel) {
            rolePlayingPanel.dispose();
        }
        if (agentCoordinator) {
            await agentCoordinator.dispose();
        }
        if (characterService) {
            characterService.dispose();
        }
        jest.clearAllMocks();
    });

    describe('1. Character Search Functionality', () => {
        test('should find character by exact name match', async () => {
            const luffyCharacter = rolePlayingPanel.getCharacterPresets()
                .find(char => char.name === 'Monkey D. Luffy');
            
            expect(luffyCharacter).toBeDefined();
            expect(luffyCharacter.id).toBe('luffy');
            expect(luffyCharacter.personality.enthusiasm).toBe(0.95);
        });

        test('should find character by partial name match', async () => {
            const characters = rolePlayingPanel.getCharacterPresets();
            const luffyMatch = characters.find(char => 
                char.name.toLowerCase().includes('luffy')
            );
            
            expect(luffyMatch).toBeDefined();
            expect(luffyMatch.id).toBe('luffy');
        });

        test('should find character by description keywords', async () => {
            const characters = rolePlayingPanel.getCharacterPresets();
            const pirateMatch = characters.find(char => 
                char.description.toLowerCase().includes('pirate')
            );
            
            expect(pirateMatch).toBeDefined();
            expect(pirateMatch.id).toBe('luffy');
        });

        test('should return empty for non-existent character', async () => {
            const characters = rolePlayingPanel.getCharacterPresets();
            const nonExistent = characters.find(char => 
                char.name.toLowerCase().includes('nonexistent')
            );
            
            expect(nonExistent).toBeUndefined();
        });

        test('should handle case-insensitive search', async () => {
            const characters = rolePlayingPanel.getCharacterPresets();
            const saitamaMatch = characters.find(char => 
                char.name.toLowerCase().includes('saitama'.toLowerCase())
            );
            
            expect(saitamaMatch).toBeDefined();
            expect(saitamaMatch.id).toBe('saitama');
        });
    });

    describe('2. Voice Input for Character Selection', () => {
        let mockSpeechRecognition;

        beforeEach(() => {
            // Mock Web Speech API
            mockSpeechRecognition = {
                start: jest.fn(),
                stop: jest.fn(),
                addEventListener: jest.fn(),
                removeEventListener: jest.fn(),
                continuous: false,
                interimResults: false,
                lang: 'en-US'
            };

            global.webkitSpeechRecognition = jest.fn(() => mockSpeechRecognition);
            global.SpeechRecognition = jest.fn(() => mockSpeechRecognition);
        });

        test('should parse "I want to be Luffy" voice input', async () => {
            const voiceInput = "I want to be Luffy";
            const extractedName = voiceInput.match(/(?:I want to be|be|select|choose)\s+(\w+)/i)?.[1];
            
            expect(extractedName?.toLowerCase()).toBe('luffy');
            
            const matchedCharacter = rolePlayingPanel.getCharacterPresets()
                .find(char => char.name.toLowerCase().includes(extractedName?.toLowerCase()));
            
            expect(matchedCharacter).toBeDefined();
            expect(matchedCharacter.id).toBe('luffy');
        });

        test('should parse "I want to be Saitama" voice input', async () => {
            const voiceInput = "I want to be Saitama";
            const extractedName = voiceInput.match(/(?:I want to be|be|select|choose)\s+(\w+)/i)?.[1];
            
            expect(extractedName?.toLowerCase()).toBe('saitama');
            
            const matchedCharacter = rolePlayingPanel.getCharacterPresets()
                .find(char => char.name.toLowerCase().includes(extractedName?.toLowerCase()));
            
            expect(matchedCharacter).toBeDefined();
            expect(matchedCharacter.id).toBe('saitama');
        });

        test('should handle various voice input patterns', async () => {
            const patterns = [
                "Select Luffy",
                "Choose Saitama", 
                "Be Luffy",
                "I choose Saitama",
                "Make me Luffy"
            ];

            patterns.forEach(pattern => {
                const extractedName = pattern.match(/(?:select|choose|be|make me|I choose|I want to be)\s+(\w+)/i)?.[1];
                expect(extractedName).toBeDefined();
                
                const matchedCharacter = rolePlayingPanel.getCharacterPresets()
                    .find(char => char.name.toLowerCase().includes(extractedName?.toLowerCase()));
                expect(matchedCharacter).toBeDefined();
            });
        });

        test('should handle voice recognition errors gracefully', async () => {
            const errorHandler = jest.fn();
            
            // Simulate speech recognition error
            mockSpeechRecognition.addEventListener = jest.fn((event, callback) => {
                if (event === 'error') {
                    setTimeout(() => callback({ error: 'network' }), 100);
                }
            });

            // Should not throw error
            expect(() => {
                try {
                    mockSpeechRecognition.start();
                } catch (error) {
                    errorHandler(error);
                }
            }).not.toThrow();
        });

        test('should provide voice input confidence scoring', async () => {
            const voiceResults = [
                { transcript: "I want to be Luffy", confidence: 0.95 },
                { transcript: "I want to be lucky", confidence: 0.60 },
                { transcript: "I want to believe", confidence: 0.45 }
            ];

            voiceResults.forEach(result => {
                const isHighConfidence = result.confidence > 0.8;
                const containsCharacterName = rolePlayingPanel.getCharacterPresets()
                    .some(char => result.transcript.toLowerCase().includes(char.name.toLowerCase()));
                
                if (result.transcript.includes('Luffy')) {
                    expect(isHighConfidence).toBe(true);
                    expect(containsCharacterName).toBe(true);
                }
            });
        });
    });

    describe('3. System 2 Character Summarization', () => {
        beforeEach(async () => {
            // Initialize agent service with dual brain
            agentCoordinator.agentService = mockAgentService;
            await agentCoordinator.initializeAgentService({
                toolRegistrationConfig: {},
                toolOptions: {},
                modelProvider: 'aliyun',
                aliyunApiKey: 'test-key',
                modelOptions: {
                    defaultModel: 'qwen-turbo',
                    enableRealtime: true,
                    audioConfig: {}
                },
                agentConfig: {
                    enableAutonomousTools: true,
                    enableDualBrain: true,
                    maxIterations: 3
                }
            });
        });

        test('should generate character summary using System 2', async () => {
            const character = testCharacters[0]; // Luffy
            
            // Mock System 2 response
            const mockSystem2Response = {
                content: `Character Analysis: ${character.name}
                
                Personality Summary:
                - Extremely enthusiastic (0.95) - Shows high energy and excitement
                - Low formality (0.2) - Very casual and direct communication style  
                - High empathy (0.8) - Cares deeply about friends and others
                - Very creative (0.9) - Thinks outside the box, unconventional solutions
                - Direct (0.9) - Says what he means without sugar-coating
                
                Voice Style: Energetic - matches personality traits
                Core Motivation: Adventure, protecting friends, becoming Pirate King
                
                Consistency Check: Character traits are coherent and well-balanced for a shonen protagonist.`
            };

            mockAgentService.generateResponse.mockResolvedValue(mockSystem2Response);

            // Apply character to system
            await characterService.setCharacterContext(character);
            
            // Verify System 2 was called for analysis
            expect(mockAgentService.generateResponse).toHaveBeenCalled();
            
            // Verify character context was built correctly
            const currentChar = characterService.getCurrentCharacter();
            expect(currentChar).toBeDefined();
            expect(currentChar.name).toBe('Monkey D. Luffy');
            expect(currentChar.personality.enthusiasm).toBe(0.95);
        });

        test('should validate personality coherence', async () => {
            const character = testCharacters[1]; // Saitama
            
            // Apply character
            const result = await characterService.setCharacterContext(character);
            expect(result).toBe(true);
            
            // Check consistency metrics
            const metrics = characterService.getConsistencyMetrics();
            expect(metrics).toBeDefined();
            expect(typeof metrics.responseAlignment).toBe('number');
            expect(typeof metrics.personalityStability).toBe('number');
            expect(typeof metrics.contextualRelevance).toBe('number');
        });

        test('should handle character inconsistencies', async () => {
            // Create inconsistent character (high formality + high enthusiasm = potential conflict)
            const inconsistentCharacter = {
                id: 'inconsistent',
                name: 'Inconsistent Character',
                description: 'Test character with conflicting traits',
                avatar: '🤔',
                personality: {
                    formality: 0.95,      // Very formal
                    enthusiasm: 0.95,     // Very enthusiastic (potential conflict)
                    empathy: 0.5,
                    creativity: 0.5,
                    directness: 0.95      // Very direct
                },
                voiceStyle: 'professional',
                systemPrompt: 'Test prompt'
            };

            const result = await characterService.setCharacterContext(inconsistentCharacter);
            expect(result).toBe(true); // Should still work but flag inconsistencies
            
            // Check if consistency score reflects the conflict
            const profile = characterService.getPersonalityProfiles().get('inconsistent');
            expect(profile?.consistencyScore).toBeLessThan(0.8); // Should be lower due to conflicts
        });

        test('should provide personality trait explanations', async () => {
            const character = testCharacters[0]; // Luffy
            
            // Get personality description
            const personalityDesc = character.personality;
            
            // Verify trait interpretations
            expect(personalityDesc.formality).toBe(0.2); // Very casual
            expect(personalityDesc.enthusiasm).toBe(0.95); // Extremely enthusiastic  
            expect(personalityDesc.empathy).toBe(0.8); // High empathy
            expect(personalityDesc.creativity).toBe(0.9); // Very creative
            expect(personalityDesc.directness).toBe(0.9); // Very direct
            
            // Apply and get generated description
            await characterService.setCharacterContext(character);
            const currentChar = characterService.getCurrentCharacter();
            
            expect(currentChar).toBeDefined();
        });
    });

    describe('4. Integration with Existing Dual Brain System', () => {
        beforeEach(async () => {
            // Setup full dual brain system
            agentCoordinator.agentService = mockAgentService;
            
            // Initialize dual brain coordinator
            dualBrainCoordinator = new DualBrainCoordinator(mockAgentService, {
                enableProactiveDecisions: true,
                system2AnalysisInterval: 1000,
                decisionCooldown: 2000
            });

            await dualBrainCoordinator.initialize();
            mockAgentService.getDualBrainCoordinator.mockReturnValue(dualBrainCoordinator);
        });

        afterEach(async () => {
            if (dualBrainCoordinator) {
                await dualBrainCoordinator.stopDualBrainSystems();
                dualBrainCoordinator.dispose();
            }
        });

        test('should apply character context to System 1 (reactive)', async () => {
            const character = testCharacters[0]; // Luffy
            
            // Apply character through agent coordinator
            const result = await agentCoordinator.updateCharacterPersonality(character);
            expect(result).toBe(true);
            
            // Verify System 1 model was updated
            const system1Model = mockAgentService.getModel('system1');
            expect(system1Model.updateSystemPrompt).toHaveBeenCalled();
            
            // Verify the prompt contains character personality
            const promptCall = system1Model.updateSystemPrompt.mock.calls[0][0];
            expect(promptCall).toContain('Monkey D. Luffy');
            expect(promptCall).toContain('enthusiastic');
            expect(promptCall).toContain('energetic');
        });

        test('should apply character context to System 2 (analytical)', async () => {
            const character = testCharacters[1]; // Saitama
            
            // Apply character through agent coordinator  
            const result = await agentCoordinator.updateCharacterPersonality(character);
            expect(result).toBe(true);
            
            // Verify System 2 model was updated
            const system2Model = mockAgentService.getModel('system2');
            expect(system2Model.updateSystemPrompt).toHaveBeenCalled();
            
            // Verify the prompt contains consistency rules
            const promptCall = system2Model.updateSystemPrompt.mock.calls[0][0];
            expect(promptCall).toContain('Saitama');
            expect(promptCall).toContain('consistency');
            expect(promptCall).toContain('analytical');
        });

        test('should coordinate between systems with character context', async () => {
            const character = testCharacters[0]; // Luffy
            
            // Apply character
            await agentCoordinator.updateCharacterPersonality(character);
            
            // Start dual brain systems
            await dualBrainCoordinator.startDualBrainSystems();
            
            // Test proactive decision generation with character context
            const testInput = "What should I do when facing a strong enemy?";
            const response = await dualBrainCoordinator.generateProactiveDecision({
                conversational: {
                    messages: [{ role: 'user', content: testInput }],
                    complexity: 'high',
                    requiresReasoning: true
                },
                environmental: {
                    activity: 'character_interaction',
                    timestamp: Date.now()
                }
            });
            
            expect(response).toBeDefined();
            expect(response).toHaveProperty('shouldAct');
            expect(response).toHaveProperty('confidence');
            expect(response).toHaveProperty('reason');
            expect(typeof response.shouldAct).toBe('boolean');
        });

        test('should maintain character consistency across dual brain responses', async () => {
            const character = testCharacters[1]; // Saitama
            
            // Apply character
            await agentCoordinator.updateCharacterPersonality(character);
            await dualBrainCoordinator.startDualBrainSystems();
            
            // Test multiple responses
            const inputs = [
                "How do you feel about being a hero?",
                "What's your favorite hobby?", 
                "How do you deal with strong opponents?"
            ];
            
            for (const input of inputs) {
                const response = await dualBrainCoordinator.generateProactiveDecision({
                    conversational: {
                        messages: [{ role: 'user', content: input }]
                    },
                    environmental: {
                        activity: 'character_conversation',
                        timestamp: Date.now()
                    }
                });
                expect(response).toBeDefined();
                expect(response).toHaveProperty('shouldAct');
                expect(response).toHaveProperty('confidence');
                expect(response).toHaveProperty('reason');
            }
            
            // Verify proactive decisions were generated
            expect(inputs).toHaveLength(3);
        });

        test('should handle proactive decisions with character personality', async () => {
            const character = testCharacters[0]; // Luffy
            
            // Apply character and start systems
            await agentCoordinator.updateCharacterPersonality(character);
            await dualBrainCoordinator.startDualBrainSystems();
            
            // Mock System 2 proactive decision response
            const mockDecisionResponse = `{
                "shouldAct": true,
                "shouldSpeak": true,
                "confidence": 0.8,
                "reason": "User seems bored, Luffy would suggest an adventure",
                "urgency": "medium",
                "suggestedAction": "Suggest going on an adventure or trying something new",
                "characterResponse": "enthusiastic_encouraging"
            }`;
            
            mockAgentService.generateResponse.mockResolvedValue(mockDecisionResponse);
            
            // Generate proactive decision
            const decision = await dualBrainCoordinator.generateProactiveDecision({
                environmental: { activity: 'idle', mood: 'bored' }
            });
            
            expect(decision).toBeDefined();
            expect(decision.shouldAct).toBe(true);
            expect(decision.characterResponse).toBe('enthusiastic_encouraging');
        });
    });

    describe('5. UI/UX Flow Validation', () => {
        test('should render character selection panel', () => {
            expect(rolePlayingPanel.panel).toBeDefined();
            expect(testContainer.appendChild).toHaveBeenCalledWith(rolePlayingPanel.panel);
        });

        test('should handle character card selection', () => {
            const onCharacterChange = jest.fn();
            rolePlayingPanel.options.onCharacterChange = onCharacterChange;
            
            // Simulate character selection
            rolePlayingPanel.selectCharacter('luffy');
            
            expect(rolePlayingPanel.getCurrentCharacter()?.id).toBe('luffy');
            expect(rolePlayingPanel.getCurrentCharacter()?.name).toBe('Monkey D. Luffy');
        });

        test('should update personality sliders when character selected', () => {
            rolePlayingPanel.selectCharacter('saitama');
            
            const currentChar = rolePlayingPanel.getCurrentCharacter();
            expect(currentChar?.personality.enthusiasm).toBe(0.2); // Saitama is not enthusiastic
            expect(currentChar?.personality.directness).toBe(0.9); // But is very direct
        });

        test('should handle personality trait adjustments', () => {
            rolePlayingPanel.selectCharacter('luffy');
            
            // Adjust enthusiasm slider
            rolePlayingPanel.updatePersonalityTrait('enthusiasm', 0.7);
            
            const currentChar = rolePlayingPanel.getCurrentCharacter();
            expect(currentChar?.personality.enthusiasm).toBe(0.7);
            expect(currentChar?.id).toBe('custom'); // Should become custom when modified
        });

        test('should handle voice style changes', () => {
            rolePlayingPanel.selectCharacter('saitama');
            rolePlayingPanel.updateVoiceStyle('energetic');
            
            const currentChar = rolePlayingPanel.getCurrentCharacter();
            expect(currentChar?.voiceStyle).toBe('energetic');
        });

        test('should apply character configuration', async () => {
            const onCharacterChange = jest.fn();
            const onPersonalityUpdate = jest.fn();
            
            rolePlayingPanel.options.onCharacterChange = onCharacterChange;
            rolePlayingPanel.options.onPersonalityUpdate = onPersonalityUpdate;
            
            rolePlayingPanel.selectCharacter('luffy');
            await rolePlayingPanel.applyCharacterConfiguration();
            
            expect(onCharacterChange).toHaveBeenCalled();
            expect(onPersonalityUpdate).toHaveBeenCalled();
        });

        test('should handle panel minimize/maximize', () => {
            // Panel should start visible
            expect(rolePlayingPanel.panel.classList.contains('minimized')).toBe(false);
            
            // Minimize
            rolePlayingPanel.toggleMinimize();
            expect(rolePlayingPanel.options.minimized).toBe(true);
            
            // Maximize
            rolePlayingPanel.toggleMinimize();
            expect(rolePlayingPanel.options.minimized).toBe(false);
        });

        test('should handle show/hide functionality', () => {
            expect(rolePlayingPanel.isVisible).toBe(false);
            
            rolePlayingPanel.show();
            expect(rolePlayingPanel.isVisible).toBe(true);
            
            rolePlayingPanel.hide();
            expect(rolePlayingPanel.isVisible).toBe(false);
        });

        test('should validate form data before applying', async () => {
            // Try to apply without selecting character
            rolePlayingPanel.currentCharacter = null;
            
            await rolePlayingPanel.applyCharacterConfiguration();
            
            // Should handle gracefully without throwing
            expect(true).toBe(true); // Test passes if no error thrown
        });

        test('should provide visual feedback on apply', async () => {
            rolePlayingPanel.selectCharacter('luffy');
            
            // Mock DOM elements for visual feedback
            const mockApplyBtn = {
                classList: {
                    add: jest.fn(),
                    remove: jest.fn()
                }
            };
            
            rolePlayingPanel.panel = {
                querySelector: jest.fn().mockReturnValue(mockApplyBtn),
                classList: { contains: jest.fn() }
            };
            
            await rolePlayingPanel.applyCharacterConfiguration();
            
            // Should add 'applied' class for visual feedback
            expect(mockApplyBtn.classList.add).toHaveBeenCalledWith('applied');
        });
    });

    describe('6. End-to-End Workflow Testing', () => {
        test('should complete full character selection and application workflow', async () => {
            // Step 1: Show panel
            rolePlayingPanel.show();
            expect(rolePlayingPanel.isVisible).toBe(true);
            
            // Step 2: Search and select character
            rolePlayingPanel.selectCharacter('luffy');
            const selectedChar = rolePlayingPanel.getCurrentCharacter();
            expect(selectedChar?.name).toBe('Monkey D. Luffy');
            
            // Step 3: Adjust personality if needed
            rolePlayingPanel.updatePersonalityTrait('creativity', 0.95);
            expect(selectedChar?.personality.creativity).toBe(0.95);
            
            // Step 4: Apply to system
            await rolePlayingPanel.applyCharacterConfiguration();
            
            // Step 5: Verify integration with dual brain
            agentCoordinator.agentService = mockAgentService;
            const result = await agentCoordinator.updateCharacterPersonality(selectedChar);
            expect(result).toBe(true);
            
            // Workflow completed successfully
            expect(true).toBe(true);
        });

        test('should handle voice-to-character-application workflow', async () => {
            // Step 1: Process voice input
            const voiceInput = "I want to be Saitama";
            const extractedName = voiceInput.match(/I want to be (\w+)/i)?.[1];
            expect(extractedName).toBe('Saitama');
            
            // Step 2: Find matching character
            const matchedCharacter = rolePlayingPanel.getCharacterPresets()
                .find(char => char.name.toLowerCase().includes(extractedName.toLowerCase()));
            expect(matchedCharacter).toBeDefined();
            
            // Step 3: Apply character
            rolePlayingPanel.selectCharacter(matchedCharacter.id);
            await rolePlayingPanel.applyCharacterConfiguration();
            
            // Step 4: Verify character is active
            const currentChar = rolePlayingPanel.getCurrentCharacter();
            expect(currentChar?.name).toBe('Saitama');
            expect(currentChar?.personality.enthusiasm).toBe(0.2); // Saitama's low enthusiasm
        });

        test('should provide error recovery and fallbacks', async () => {
            // Test with invalid character ID
            rolePlayingPanel.selectCharacter('nonexistent');
            expect(rolePlayingPanel.getCurrentCharacter()).toBeNull();
            
            // Test with invalid voice input
            const invalidVoiceInput = "I want to be %#@$";
            const extractedName = invalidVoiceInput.match(/I want to be (\w+)/i)?.[1];
            expect(extractedName).toBeUndefined();
            
            // Should fallback gracefully
            const fallbackChar = rolePlayingPanel.getCharacterPresets()[0]; // Default
            rolePlayingPanel.selectCharacter(fallbackChar.id);
            expect(rolePlayingPanel.getCurrentCharacter()).toBeDefined();
        });
    });

    describe('7. Performance and Validation Tests', () => {
        test('should complete character search within performance threshold', async () => {
            const startTime = performance.now();
            
            // Search through large number of characters
            const characters = rolePlayingPanel.getCharacterPresets();
            const results = characters.filter(char => 
                char.name.toLowerCase().includes('luffy') ||
                char.description.toLowerCase().includes('pirate')
            );
            
            const endTime = performance.now();
            const searchTime = endTime - startTime;
            
            expect(searchTime).toBeLessThan(100); // Should complete within 100ms
            expect(results.length).toBeGreaterThan(0);
        });

        test('should validate character data integrity', async () => {
            const characters = rolePlayingPanel.getCharacterPresets();
            
            characters.forEach(character => {
                // Required fields
                expect(character.id).toBeDefined();
                expect(character.name).toBeDefined();
                expect(character.description).toBeDefined();
                expect(character.personality).toBeDefined();
                expect(character.voiceStyle).toBeDefined();
                expect(character.systemPrompt).toBeDefined();
                
                // Personality trait validation
                Object.entries(character.personality).forEach(([trait, value]) => {
                    expect(typeof value).toBe('number');
                    expect(value).toBeGreaterThanOrEqual(0);
                    expect(value).toBeLessThanOrEqual(1);
                });
            });
        });

        test('should handle concurrent character applications', async () => {
            const applications = [
                () => rolePlayingPanel.selectCharacter('luffy'),
                () => rolePlayingPanel.selectCharacter('saitama'),
                () => rolePlayingPanel.updatePersonalityTrait('enthusiasm', 0.8),
                () => rolePlayingPanel.updateVoiceStyle('energetic')
            ];
            
            // Execute all operations concurrently
            await Promise.all(applications.map(fn => Promise.resolve(fn())));
            
            // Should handle gracefully without errors
            expect(rolePlayingPanel.getCurrentCharacter()).toBeDefined();
        });

        test('should maintain memory efficiency during extended use', async () => {
            const initialMemory = process.memoryUsage().heapUsed;
            
            // Simulate extended usage
            for (let i = 0; i < 100; i++) {
                rolePlayingPanel.selectCharacter('luffy');
                rolePlayingPanel.updatePersonalityTrait('enthusiasm', Math.random());
                await rolePlayingPanel.applyCharacterConfiguration();
            }
            
            // Force garbage collection if available
            if (global.gc) {
                global.gc();
            }
            
            const finalMemory = process.memoryUsage().heapUsed;
            const memoryIncrease = finalMemory - initialMemory;
            
            // Memory increase should be reasonable (less than 10MB)
            expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
        });
    });
});