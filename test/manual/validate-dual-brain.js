/**
 * Manual validation test for dual brain system
 * Tests the fixes applied for WebSocket initialization and API call functionality
 */

import { createLogger } from '../../src/utils/logger.js';

async function validateDualBrainSystem() {
  const logger = createLogger('DualBrainValidation');
  
  try {
    logger.info('🧠 Starting dual brain system validation...');
    
    // Import the LangGraph Agent Service
    const { LangGraphAgentService } = await import('../../src/agent/core.js');
    
    // Create test config for dual brain mode
    const testConfig = {
      modelProvider: 'local',
      modelOptions: {
        defaultModel: 'llama3.2:latest',
        enableRealtime: true,
        audioConfig: {
          sampleRate: 24000,
          channels: 1
        }
      },
      agentConfig: {
        enableDualBrain: true,
        enableAutonomousTools: true,
        maxIterations: 3,
        periodicAnalysisInterval: 2000,
        decisionCooldown: 3000
      }
    };
    
    logger.info('🔧 Creating agent service with dual brain config...');
    const agentService = new LangGraphAgentService(testConfig);
    
    // Test initialization
    logger.info('🚀 Initializing agent service...');
    await agentService.initialize();
    logger.info('✅ Agent service initialized successfully');
    
    // Test dual brain mode detection
    const isDualBrain = agentService.isDualBrainMode();
    logger.info('🧠 Dual brain mode active:', isDualBrain);
    
    if (!isDualBrain) {
      throw new Error('Dual brain mode not detected - initialization may have failed');
    }
    
    // Test model availability
    const system1 = agentService.getModel('system1');
    const system2 = agentService.getModel('system2');
    logger.info('📡 System models available:', {
      hasSystem1: !!system1,
      hasSystem2: !!system2,
      system1Type: system1?.constructor?.name || 'N/A',
      system2Type: system2?.constructor?.name || 'N/A'
    });
    
    // Test coordinator attachment and status
    const coordinator = agentService.getDualBrainCoordinator();
    logger.info('🎯 Dual brain coordinator:', {
      hasCoordinator: !!coordinator,
      coordinatorInitialized: coordinator?.isInitialized,
      coordinatorActive: coordinator?.isActive
    });
    
    if (coordinator) {
      const status = coordinator.getStatus();
      logger.info('📊 Coordinator detailed status:', status);
      
      // Test coordinator initialization if not already done
      if (!coordinator.isInitialized) {
        logger.info('🔧 Initializing dual brain coordinator...');
        const initSuccess = await coordinator.initialize();
        logger.info('🎯 Coordinator initialization result:', initSuccess);
      }
      
      // Test starting dual brain systems
      if (!coordinator.isActive) {
        logger.info('🚀 Starting dual brain systems...');
        const startSuccess = await coordinator.startDualBrainSystems();
        logger.info('🎯 Dual brain systems start result:', startSuccess);
      }
    }
    
    // Test WebSocket model realtime mode check
    if (system1 && typeof system1.isRealtimeModeActive === 'function') {
      const isRealtimeActive = system1.isRealtimeModeActive();
      logger.info('🎙️ System 1 realtime mode active:', isRealtimeActive);
    }
    
    // Test basic functionality
    logger.info('🧪 Testing basic dual brain functionality...');
    
    // Wait a moment for systems to stabilize
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    logger.info('✅ Dual brain system validation completed successfully');
    logger.info('🎉 All fixes are working correctly - dual brain system is operational');
    
    return true;
    
  } catch (error) {
    logger.error('❌ Dual brain validation failed:', error.message);
    logger.error('Stack trace:', error.stack);
    return false;
  }
}

// Run validation if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  validateDualBrainSystem()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Validation error:', error);
      process.exit(1);
    });
}

export { validateDualBrainSystem };