<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Permission Manager Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .video-preview {
            width: 320px;
            height: 240px;
            background: #000;
            border-radius: 8px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
        }
        .success {
            color: #28a745;
        }
        .warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <h1>Camera Permission Manager Test</h1>
    <p>This page tests the centralized camera permission management system to ensure it prevents multiple permission requests and provides proper fallback handling.</p>

    <div>
        <h3>Controls</h3>
        <button class="button" onclick="testSingleRequest()">Test Single Request</button>
        <button class="button" onclick="testMultipleRequests()">Test Multiple Simultaneous Requests</button>
        <button class="button" onclick="checkPermissionStatus()">Check Permission Status</button>
        <button class="button" onclick="testCameraViewer()">Test CameraViewer Integration</button>
        <button class="button" onclick="testMediaCaptureManager()">Test MediaCaptureManager Integration</button>
        <button class="button" onclick="releaseAllAccess()">Release All Camera Access</button>
        <button class="button" onclick="clearLogs()">Clear Logs</button>
    </div>

    <div>
        <h3>Video Preview</h3>
        <video id="videoPreview" class="video-preview" autoplay muted playsinline></video>
    </div>

    <div>
        <h3>Status & Logs</h3>
        <div id="status" class="status">Ready to test camera permission management...</div>
    </div>

    <script type="module">
        // Import the camera permission manager (simulated for demo)
        // In actual implementation, this would be: import { cameraPermissionManager } from '../../app/viewer/services/cameraPermissionManager.js';
        
        // Mock implementation for demonstration
        class MockCameraPermissionManager {
            constructor() {
                this.permissionState = { granted: false, denied: false, pending: false };
                this.currentStream = null;
                this.streamReferences = new Set();
                this.activeRequest = null;
                this.requestQueue = [];
            }

            async requestCameraAccess(requesterId, options = {}) {
                this.log(`📷 Camera access requested by: ${requesterId}`);
                
                if (this.activeRequest) {
                    this.log(`📋 Queuing request from ${requesterId} (another request in progress)`);
                    return new Promise((resolve) => {
                        this.requestQueue.push({ requesterId, options, resolve });
                    });
                }

                this.activeRequest = this.performRequest(requesterId, options);
                const result = await this.activeRequest;
                this.activeRequest = null;
                this.processQueue();
                
                return result;
            }

            async performRequest(requesterId, options) {
                try {
                    this.permissionState.pending = true;
                    this.log(`🔄 Requesting getUserMedia for ${requesterId}...`);

                    // Show permission guidance
                    this.showPermissionGuidance(options.requestReason);

                    const stream = await navigator.mediaDevices.getUserMedia({
                        video: options.video || { width: { ideal: 640 }, height: { ideal: 480 } },
                        audio: options.audio || false
                    });

                    this.currentStream = stream;
                    this.streamReferences.add(requesterId);
                    this.permissionState = { granted: true, denied: false, pending: false };
                    
                    this.log(`✅ Camera permission granted for ${requesterId}`);
                    this.hidePermissionGuidance();

                    return { success: true, stream, permission: this.permissionState };
                } catch (error) {
                    this.permissionState = { 
                        granted: false, 
                        denied: true, 
                        pending: false, 
                        error: error.message 
                    };
                    
                    this.log(`❌ Camera permission denied for ${requesterId}: ${error.message}`, 'error');
                    this.showPermissionError(this.getUserFriendlyErrorMessage(error));
                    
                    return { success: false, error: error.message, permission: this.permissionState };
                }
            }

            processQueue() {
                if (this.requestQueue.length > 0) {
                    const next = this.requestQueue.shift();
                    this.log(`📋 Processing queued request from ${next.requesterId}`);
                    this.requestCameraAccess(next.requesterId, next.options).then(next.resolve);
                }
            }

            releaseCameraAccess(requesterId) {
                this.streamReferences.delete(requesterId);
                this.log(`📷 Camera access released by: ${requesterId}`);
                
                if (this.streamReferences.size === 0 && this.currentStream) {
                    this.currentStream.getTracks().forEach(track => track.stop());
                    this.currentStream = null;
                    this.log(`🛑 Camera stream stopped (no more references)`);
                }
            }

            async checkPermissionStatus() {
                try {
                    if (!navigator.permissions) {
                        return this.permissionState;
                    }
                    const permission = await navigator.permissions.query({ name: 'camera' });
                    return { 
                        granted: permission.state === 'granted',
                        denied: permission.state === 'denied',
                        pending: permission.state === 'prompt'
                    };
                } catch (error) {
                    return this.permissionState;
                }
            }

            getCurrentStream() {
                return this.currentStream;
            }

            getUserFriendlyErrorMessage(error) {
                if (error.name === 'NotAllowedError') {
                    return 'Camera access was denied. Please allow camera access and refresh the page.';
                }
                return `Camera error: ${error.message}`;
            }

            showPermissionGuidance(reason) {
                this.log(`💬 Showing permission guidance: ${reason || 'Camera access needed'}`);
            }

            hidePermissionGuidance() {
                this.log(`✅ Permission guidance hidden`);
            }

            showPermissionError(message) {
                this.log(`⚠️ Permission error: ${message}`, 'error');
            }

            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const statusEl = document.getElementById('status');
                const className = type === 'error' ? 'error' : type === 'warning' ? 'warning' : 'success';
                statusEl.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
                statusEl.scrollTop = statusEl.scrollHeight;
                console.log(message);
            }
        }

        // Create mock instance
        const cameraPermissionManager = new MockCameraPermissionManager();

        // Test functions
        window.testSingleRequest = async function() {
            cameraPermissionManager.log('🧪 Testing single camera request...');
            try {
                const result = await cameraPermissionManager.requestCameraAccess('TestRequester');
                if (result.success) {
                    const videoEl = document.getElementById('videoPreview');
                    videoEl.srcObject = result.stream;
                    cameraPermissionManager.log('✅ Single request test successful');
                } else {
                    cameraPermissionManager.log('❌ Single request test failed: ' + result.error, 'error');
                }
            } catch (error) {
                cameraPermissionManager.log('❌ Single request test error: ' + error.message, 'error');
            }
        };

        window.testMultipleRequests = async function() {
            cameraPermissionManager.log('🧪 Testing multiple simultaneous camera requests...');
            
            const requests = [
                cameraPermissionManager.requestCameraAccess('CameraViewer'),
                cameraPermissionManager.requestCameraAccess('MediaCaptureManager'),
                cameraPermissionManager.requestCameraAccess('PoseDetector'),
                cameraPermissionManager.requestCameraAccess('GestureController')
            ];

            try {
                const results = await Promise.all(requests);
                const successCount = results.filter(r => r.success).length;
                cameraPermissionManager.log(`✅ Multiple requests test: ${successCount}/${results.length} successful`);
                
                if (results[0].success) {
                    const videoEl = document.getElementById('videoPreview');
                    videoEl.srcObject = results[0].stream;
                }
            } catch (error) {
                cameraPermissionManager.log('❌ Multiple requests test error: ' + error.message, 'error');
            }
        };

        window.checkPermissionStatus = async function() {
            cameraPermissionManager.log('🔍 Checking permission status...');
            const status = await cameraPermissionManager.checkPermissionStatus();
            cameraPermissionManager.log(`📊 Permission status: ${JSON.stringify(status, null, 2)}`);
        };

        window.testCameraViewer = async function() {
            cameraPermissionManager.log('🧪 Testing CameraViewer integration...');
            try {
                const result = await cameraPermissionManager.requestCameraAccess('CameraViewer', {
                    requestReason: 'Camera access is needed to display video preview and enable gesture recognition features.'
                });
                cameraPermissionManager.log(result.success ? '✅ CameraViewer integration successful' : '❌ CameraViewer integration failed');
            } catch (error) {
                cameraPermissionManager.log('❌ CameraViewer integration error: ' + error.message, 'error');
            }
        };

        window.testMediaCaptureManager = async function() {
            cameraPermissionManager.log('🧪 Testing MediaCaptureManager integration...');
            try {
                const result = await cameraPermissionManager.requestCameraAccess('MediaCaptureManager', {
                    video: { width: { ideal: 1280 }, height: { ideal: 720 } },
                    requestReason: 'Media capture needs video access for streaming and processing.'
                });
                cameraPermissionManager.log(result.success ? '✅ MediaCaptureManager integration successful' : '❌ MediaCaptureManager integration failed');
            } catch (error) {
                cameraPermissionManager.log('❌ MediaCaptureManager integration error: ' + error.message, 'error');
            }
        };

        window.releaseAllAccess = function() {
            cameraPermissionManager.log('🧹 Releasing all camera access...');
            cameraPermissionManager.releaseCameraAccess('TestRequester');
            cameraPermissionManager.releaseCameraAccess('CameraViewer');
            cameraPermissionManager.releaseCameraAccess('MediaCaptureManager');
            cameraPermissionManager.releaseCameraAccess('PoseDetector');
            cameraPermissionManager.releaseCameraAccess('GestureController');
            
            const videoEl = document.getElementById('videoPreview');
            videoEl.srcObject = null;
            
            cameraPermissionManager.log('✅ All camera access released');
        };

        window.clearLogs = function() {
            document.getElementById('status').innerHTML = 'Logs cleared...\n';
        };

        // Initial status check
        window.addEventListener('load', () => {
            cameraPermissionManager.log('🎥 Camera Permission Manager Test Ready');
            checkPermissionStatus();
        });
    </script>
</body>
</html>