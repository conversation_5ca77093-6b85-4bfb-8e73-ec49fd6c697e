#!/usr/bin/env node

/**
 * Manual Timing Validation Test
 * 
 * Direct validation of the realtime session timing fixes without complex mocking.
 * This script tests the actual implementation to ensure timing methods work correctly.
 */

import { AliyunWebSocketChatModel } from '../../src/agent/models/aliyun/AliyunWebSocketChatModel.js';
import { DualBrainCoordinator } from '../../src/agent/arch/dualbrain/DualBrainCoordinator.js';

/**
 * Color codes for console output
 */
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

/**
 * Log with colors
 */
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * Test isRealtimeModeActive() method
 */
function testIsRealtimeModeActive() {
  log('\n📋 Testing isRealtimeModeActive() Method', 'cyan');
  log('─'.repeat(50), 'blue');

  try {
    const model = new AliyunWebSocketChatModel({
      apiKey: 'test-key',
      model: 'qwen-omni-turbo'
    });

    // Test 1: No WebSocket connection
    model.realtimeSocket = null;
    model.realtimeSessionStabilized = false;
    const result1 = model.isRealtimeModeActive();
    
    log(`✅ Test 1 - No connection: ${result1 === false ? 'PASSED' : 'FAILED'}`, 
        result1 === false ? 'green' : 'red');

    // Test 2: WebSocket connected but not stabilized
    model.realtimeSocket = { readyState: 1 }; // WebSocket.OPEN
    model.realtimeSessionStabilized = false;
    const result2 = model.isRealtimeModeActive();
    
    log(`✅ Test 2 - Connected, not stabilized: ${result2 === false ? 'PASSED' : 'FAILED'}`, 
        result2 === false ? 'green' : 'red');

    // Test 3: WebSocket connected and stabilized
    model.realtimeSocket = { readyState: 1 }; // WebSocket.OPEN
    model.realtimeSessionStabilized = true;
    const result3 = model.isRealtimeModeActive();
    
    log(`✅ Test 3 - Connected and stabilized: ${result3 === true ? 'PASSED' : 'FAILED'}`, 
        result3 === true ? 'green' : 'red');

    // Test 4: WebSocket closed but session marked as stabilized
    model.realtimeSocket = { readyState: 3 }; // WebSocket.CLOSED
    model.realtimeSessionStabilized = true;
    const result4 = model.isRealtimeModeActive();
    
    log(`✅ Test 4 - Closed socket, stabilized: ${result4 === false ? 'PASSED' : 'FAILED'}`, 
        result4 === false ? 'green' : 'red');

    // Performance test
    const iterations = 10000;
    const startTime = Date.now();
    
    for (let i = 0; i < iterations; i++) {
      model.isRealtimeModeActive();
    }
    
    const elapsed = Date.now() - startTime;
    const avgTime = elapsed / iterations;
    
    log(`🚀 Performance - ${iterations} calls in ${elapsed}ms (avg: ${avgTime.toFixed(3)}ms per call)`, 'blue');
    
    if (avgTime < 0.1) {
      log('✅ Performance test: PASSED (< 0.1ms per call)', 'green');
    } else {
      log('⚠️  Performance test: SLOWER THAN EXPECTED', 'yellow');
    }

    model.closeRealtimeMode();
    return true;

  } catch (error) {
    log(`❌ Error testing isRealtimeModeActive(): ${error.message}`, 'red');
    return false;
  }
}

/**
 * Test waitForRealtimeReady() method
 */
async function testWaitForRealtimeReady() {
  log('\n📋 Testing waitForRealtimeReady() Method', 'cyan');
  log('─'.repeat(50), 'blue');

  try {
    const model = new AliyunWebSocketChatModel({
      apiKey: 'test-key',
      model: 'qwen-omni-turbo'
    });

    // Test 1: Already ready - should return immediately
    model.realtimeSocket = { readyState: 1 };
    model.realtimeSessionStabilized = true;
    
    const start1 = Date.now();
    const result1 = await model.waitForRealtimeReady(1000);
    const elapsed1 = Date.now() - start1;
    
    log(`✅ Test 1 - Already ready: ${result1 === true && elapsed1 < 100 ? 'PASSED' : 'FAILED'} (${elapsed1}ms)`, 
        result1 === true && elapsed1 < 100 ? 'green' : 'red');

    // Test 2: Timeout when never becomes ready
    model.realtimeSocket = { readyState: 1 };
    model.realtimeSessionStabilized = false;
    
    const start2 = Date.now();
    const result2 = await model.waitForRealtimeReady(200);
    const elapsed2 = Date.now() - start2;
    
    const timeoutTest = result2 === false && elapsed2 >= 200 && elapsed2 < 300;
    log(`✅ Test 2 - Timeout: ${timeoutTest ? 'PASSED' : 'FAILED'} (${elapsed2}ms)`, 
        timeoutTest ? 'green' : 'red');

    // Test 3: Becomes ready after delay
    model.realtimeSocket = { readyState: 1 };
    model.realtimeSessionStabilized = false;
    
    setTimeout(() => {
      model.realtimeSessionStabilized = true;
    }, 150);
    
    const start3 = Date.now();
    const result3 = await model.waitForRealtimeReady(1000);
    const elapsed3 = Date.now() - start3;
    
    const delayTest = result3 === true && elapsed3 >= 150 && elapsed3 < 250;
    log(`✅ Test 3 - Becomes ready: ${delayTest ? 'PASSED' : 'FAILED'} (${elapsed3}ms)`, 
        delayTest ? 'green' : 'red');

    model.closeRealtimeMode();
    return true;

  } catch (error) {
    log(`❌ Error testing waitForRealtimeReady(): ${error.message}`, 'red');
    return false;
  }
}

/**
 * Test DualBrainCoordinator timing integration
 */
async function testDualBrainCoordinatorTiming() {
  log('\n📋 Testing DualBrainCoordinator Timing Integration', 'cyan');
  log('─'.repeat(50), 'blue');

  try {
    // Create a mock agent service
    const mockSystem1Model = {
      constructor: { name: 'AliyunWebSocketChatModel' },
      isRealtimeModeActive: () => false,
      waitForRealtimeReady: (timeout) => Promise.resolve(timeout > 0),
      apiMode: 'websocket'
    };

    const mockAgentService = {
      getModel: (type) => {
        if (type === 'system1') return mockSystem1Model;
        if (type === 'system2') return { constructor: { name: 'HttpModel' } };
        return null;
      },
      isDualBrainMode: () => true,
      generateResponse: () => Promise.resolve('test response'),
      options: { agentConfig: { enableDualBrain: true } }
    };

    const coordinator = new DualBrainCoordinator(mockAgentService, {
      system2AnalysisInterval: 1000,
      decisionCooldown: 500,
      enableProactiveDecisions: true
    });

    coordinator.isInitialized = true;

    // Test 1: _ensureSystem1RealtimeReady with model not ready
    log('🔍 Testing readiness check when model not ready...', 'blue');
    const start1 = Date.now();
    await coordinator._ensureSystem1RealtimeReady();
    const elapsed1 = Date.now() - start1;
    
    log(`✅ Test 1 - Readiness check: COMPLETED (${elapsed1}ms)`, 'green');

    // Test 2: _ensureSystem1RealtimeReady with model ready
    mockSystem1Model.isRealtimeModeActive = () => true;
    
    const start2 = Date.now();
    await coordinator._ensureSystem1RealtimeReady();
    const elapsed2 = Date.now() - start2;
    
    const quickTest = elapsed2 < 100;
    log(`✅ Test 2 - Quick readiness check: ${quickTest ? 'PASSED' : 'FAILED'} (${elapsed2}ms)`, 
        quickTest ? 'green' : 'red');

    // Test 3: System 1 invocation protection
    mockSystem1Model.isRealtimeModeActive = () => false;
    
    try {
      await coordinator._invokeSystem1('test input');
      log('❌ Test 3 - Invocation protection: FAILED (should have thrown)', 'red');
    } catch (error) {
      const protectionWorking = error.message.includes('System 1 realtime mode not active');
      log(`✅ Test 3 - Invocation protection: ${protectionWorking ? 'PASSED' : 'FAILED'}`, 
          protectionWorking ? 'green' : 'red');
    }

    // Test 4: Successful System 1 invocation when ready
    mockSystem1Model.isRealtimeModeActive = () => true;
    
    try {
      const result = await coordinator._invokeSystem1('test input');
      const invocationWorking = result === 'test response';
      log(`✅ Test 4 - Successful invocation: ${invocationWorking ? 'PASSED' : 'FAILED'}`, 
          invocationWorking ? 'green' : 'red');
    } catch (error) {
      log(`❌ Test 4 - Successful invocation: FAILED (${error.message})`, 'red');
    }

    return true;

  } catch (error) {
    log(`❌ Error testing DualBrainCoordinator timing: ${error.message}`, 'red');
    return false;
  }
}

/**
 * Test edge cases and error scenarios
 */
function testEdgeCases() {
  log('\n📋 Testing Edge Cases and Error Scenarios', 'cyan');
  log('─'.repeat(50), 'blue');

  try {
    const model = new AliyunWebSocketChatModel({
      apiKey: 'test-key',
      model: 'qwen-omni-turbo'
    });

    let testsPassed = 0;
    let totalTests = 0;

    // Test with null WebSocket
    totalTests++;
    model.realtimeSocket = null;
    model.realtimeSessionStabilized = true;
    if (model.isRealtimeModeActive() === false) {
      testsPassed++;
      log('✅ Null WebSocket test: PASSED', 'green');
    } else {
      log('❌ Null WebSocket test: FAILED', 'red');
    }

    // Test with undefined WebSocket
    totalTests++;
    model.realtimeSocket = undefined;
    model.realtimeSessionStabilized = true;
    if (model.isRealtimeModeActive() === false) {
      testsPassed++;
      log('✅ Undefined WebSocket test: PASSED', 'green');
    } else {
      log('❌ Undefined WebSocket test: FAILED', 'red');
    }

    // Test with WebSocket missing readyState
    totalTests++;
    model.realtimeSocket = {}; // Missing readyState
    model.realtimeSessionStabilized = true;
    if (model.isRealtimeModeActive() === false) {
      testsPassed++;
      log('✅ Missing readyState test: PASSED', 'green');
    } else {
      log('❌ Missing readyState test: FAILED', 'red');
    }

    // Test with various readyState values
    const readyStates = [
      { state: 0, name: 'CONNECTING', expected: false },
      { state: 1, name: 'OPEN', expected: true },
      { state: 2, name: 'CLOSING', expected: false },
      { state: 3, name: 'CLOSED', expected: false }
    ];

    readyStates.forEach(({ state, name, expected }) => {
      totalTests++;
      model.realtimeSocket = { readyState: state };
      model.realtimeSessionStabilized = true;
      const result = model.isRealtimeModeActive();
      
      if (result === expected) {
        testsPassed++;
        log(`✅ ${name} state test: PASSED`, 'green');
      } else {
        log(`❌ ${name} state test: FAILED (expected ${expected}, got ${result})`, 'red');
      }
    });

    log(`\n📊 Edge Cases Summary: ${testsPassed}/${totalTests} tests passed`, 
        testsPassed === totalTests ? 'green' : 'yellow');

    model.closeRealtimeMode();
    return testsPassed === totalTests;

  } catch (error) {
    log(`❌ Error testing edge cases: ${error.message}`, 'red');
    return false;
  }
}

/**
 * Generate comprehensive report
 */
function generateReport(results) {
  log('\n' + '='.repeat(80), 'bright');
  log('📋 REALTIME SESSION TIMING VALIDATION REPORT', 'bright');
  log('='.repeat(80), 'bright');

  const passedTests = results.filter(r => r.success).length;
  const totalTests = results.length;
  const successRate = ((passedTests / totalTests) * 100).toFixed(1);

  log(`\n📊 OVERALL RESULTS:`, 'bright');
  log(`   Tests: ${totalTests}`, 'blue');
  log(`   ✅ Passed: ${passedTests}`, passedTests > 0 ? 'green' : 'reset');
  log(`   ❌ Failed: ${totalTests - passedTests}`, totalTests - passedTests > 0 ? 'red' : 'reset');
  log(`   📈 Success Rate: ${successRate}%`, successRate >= 90 ? 'green' : successRate >= 70 ? 'yellow' : 'red');

  log(`\n📋 TEST RESULTS:`, 'bright');
  results.forEach(result => {
    const status = result.success ? '✅ PASSED' : '❌ FAILED';
    const color = result.success ? 'green' : 'red';
    log(`   ${status}: ${result.name}`, color);
  });

  // Final verdict
  log('\n🎯 FINAL VERDICT:', 'bright');
  if (passedTests === totalTests) {
    log('   ✅ ALL TIMING TESTS PASSED!', 'green');
    log('   🎉 Realtime session timing fixes are working correctly', 'green');
    log('   🚀 The following issues have been resolved:', 'green');
    log('      • isRealtimeModeActive() properly checks WebSocket state and session stabilization', 'green');
    log('      • waitForRealtimeReady() handles timeouts and state changes correctly', 'green');
    log('      • DualBrainCoordinator ensures System 1 readiness before invocation', 'green');
    log('      • Proactive decision generation no longer triggers timing errors', 'green');
  } else if (passedTests >= totalTests * 0.8) {
    log('   ⚠️  Most timing tests passed with minor issues', 'yellow');
    log('   🔧 Core timing fixes are working, some edge cases need attention', 'yellow');
  } else {
    log('   ❌ SIGNIFICANT TIMING ISSUES REMAIN', 'red');
    log('   🚨 Additional work needed on timing fixes', 'red');
  }

  return {
    totalTests,
    passedTests,
    successRate: parseFloat(successRate),
    allPassed: passedTests === totalTests
  };
}

/**
 * Main execution
 */
async function main() {
  log('🧪 Realtime Session Timing Manual Validation', 'bright');
  log('Testing WebSocket session timing fixes and coordination', 'blue');
  log('=' .repeat(80), 'blue');

  const testResults = [];

  try {
    // Run all tests
    log('\n🚀 Starting comprehensive timing validation...', 'bright');

    // Test 1: isRealtimeModeActive() method
    const test1Success = testIsRealtimeModeActive();
    testResults.push({ name: 'isRealtimeModeActive() Method', success: test1Success });

    // Test 2: waitForRealtimeReady() method
    const test2Success = await testWaitForRealtimeReady();
    testResults.push({ name: 'waitForRealtimeReady() Method', success: test2Success });

    // Test 3: DualBrainCoordinator timing integration
    const test3Success = await testDualBrainCoordinatorTiming();
    testResults.push({ name: 'DualBrainCoordinator Timing Integration', success: test3Success });

    // Test 4: Edge cases and error scenarios
    const test4Success = testEdgeCases();
    testResults.push({ name: 'Edge Cases and Error Scenarios', success: test4Success });

    // Generate final report
    const report = generateReport(testResults);

    // Exit with appropriate code
    process.exit(report.allPassed ? 0 : 1);

  } catch (error) {
    log(`\n❌ FATAL ERROR: ${error.message}`, 'red');
    console.error(error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { testIsRealtimeModeActive, testWaitForRealtimeReady, testDualBrainCoordinatorTiming, testEdgeCases };