#!/usr/bin/env node

/**
 * Realtime Session Timing Test Runner
 * 
 * Comprehensive test runner for validating realtime session timing fixes.
 * This script runs all timing-related tests and provides detailed reporting.
 */

import { spawn } from 'child_process';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '../..');

/**
 * Test suite configuration
 */
const testSuites = {
  unit: {
    name: 'Unit Tests - Realtime Session Timing',
    files: [
      'test/unit/realtime-session-timing.test.js',
      'test/unit/dualbrain-coordinator-timing.test.js'
    ],
    description: 'Tests individual methods and components for timing correctness'
  },
  integration: {
    name: 'Integration Tests - Session Coordination',
    files: [
      'test/integration/realtime-session-integration.test.js',
      'test/integration/error-scenario-timing.test.js'
    ],
    description: 'Tests complete workflows and error scenarios'
  },
  existing: {
    name: 'Existing Tests - Regression Prevention',
    files: [
      'test/integration/proactive-calling.test.js',
      'test/app/viewer/services/services-integration.test.js'
    ],
    description: 'Existing tests to ensure no regressions'
  }
};

/**
 * Color codes for console output
 */
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

/**
 * Log with colors
 */
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * Run a single test file
 */
function runTest(testFile) {
  return new Promise((resolve) => {
    log(`\n📋 Running: ${testFile}`, 'cyan');
    
    const startTime = Date.now();
    const child = spawn('npx', ['vitest', 'run', testFile, '--reporter=verbose'], {
      cwd: projectRoot,
      stdio: 'pipe',
      shell: true
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      const duration = Date.now() - startTime;
      const success = code === 0;

      if (success) {
        log(`✅ PASSED: ${testFile} (${duration}ms)`, 'green');
      } else {
        log(`❌ FAILED: ${testFile} (${duration}ms)`, 'red');
      }

      resolve({
        file: testFile,
        success,
        code,
        duration,
        stdout,
        stderr
      });
    });

    child.on('error', (error) => {
      log(`❌ ERROR: ${testFile} - ${error.message}`, 'red');
      resolve({
        file: testFile,
        success: false,
        code: -1,
        duration: Date.now() - startTime,
        stdout: '',
        stderr: error.message
      });
    });
  });
}

/**
 * Run a test suite
 */
async function runTestSuite(suiteName, suite) {
  log(`\n🚀 ${suite.name}`, 'bright');
  log(`📝 ${suite.description}`, 'blue');
  log('─'.repeat(80), 'blue');

  const results = [];
  
  for (const testFile of suite.files) {
    const result = await runTest(testFile);
    results.push(result);
    
    // Show details for failed tests
    if (!result.success && result.stderr) {
      log(`\n❌ Error Details for ${testFile}:`, 'red');
      console.log(result.stderr);
    }
  }

  // Suite summary
  const passed = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);

  log(`\n📊 ${suite.name} Summary:`, 'bright');
  log(`   ✅ Passed: ${passed}`, passed > 0 ? 'green' : 'reset');
  log(`   ❌ Failed: ${failed}`, failed > 0 ? 'red' : 'reset');
  log(`   ⏱️  Duration: ${totalDuration}ms`, 'blue');

  return {
    suiteName,
    results,
    passed,
    failed,
    totalDuration
  };
}

/**
 * Generate detailed report
 */
function generateReport(suiteResults) {
  log('\n' + '='.repeat(80), 'bright');
  log('📋 COMPREHENSIVE TIMING TEST REPORT', 'bright');
  log('='.repeat(80), 'bright');

  let totalPassed = 0;
  let totalFailed = 0;
  let totalDuration = 0;
  const failedTests = [];

  // Suite summaries
  for (const suite of suiteResults) {
    totalPassed += suite.passed;
    totalFailed += suite.failed;
    totalDuration += suite.totalDuration;

    suite.results.forEach(result => {
      if (!result.success) {
        failedTests.push(result);
      }
    });

    log(`\n📦 ${suite.suiteName.toUpperCase()}:`, 'cyan');
    log(`   ✅ ${suite.passed} passed`, 'green');
    log(`   ❌ ${suite.failed} failed`, suite.failed > 0 ? 'red' : 'reset');
    log(`   ⏱️  ${suite.totalDuration}ms`, 'blue');
  }

  // Overall summary
  log('\n📊 OVERALL SUMMARY:', 'bright');
  log(`   Tests: ${totalPassed + totalFailed}`, 'blue');
  log(`   ✅ Passed: ${totalPassed}`, totalPassed > 0 ? 'green' : 'reset');
  log(`   ❌ Failed: ${totalFailed}`, totalFailed > 0 ? 'red' : 'reset');
  log(`   ⏱️  Total Duration: ${totalDuration}ms`, 'blue');
  
  const successRate = totalPassed + totalFailed > 0 ? 
    ((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1) : 0;
  log(`   📈 Success Rate: ${successRate}%`, successRate >= 90 ? 'green' : successRate >= 70 ? 'yellow' : 'red');

  // Failed test details
  if (failedTests.length > 0) {
    log('\n❌ FAILED TESTS:', 'red');
    failedTests.forEach(test => {
      log(`   • ${test.file}`, 'red');
      if (test.stderr) {
        log(`     Error: ${test.stderr.split('\n')[0]}`, 'red');
      }
    });
  }

  // Timing analysis
  log('\n⏱️  TIMING ANALYSIS:', 'bright');
  const avgDuration = totalDuration / (totalPassed + totalFailed);
  log(`   Average test duration: ${avgDuration.toFixed(1)}ms`, 'blue');
  
  if (avgDuration > 5000) {
    log('   ⚠️  Some tests are running slowly - consider optimization', 'yellow');
  } else if (avgDuration < 100) {
    log('   🚀 Tests are running efficiently', 'green');
  }

  // Final verdict
  log('\n🎯 FINAL VERDICT:', 'bright');
  if (totalFailed === 0) {
    log('   ✅ ALL TIMING TESTS PASSED!', 'green');
    log('   🎉 Realtime session timing fixes are working correctly', 'green');
  } else if (totalFailed <= 2) {
    log('   ⚠️  Minor issues detected', 'yellow');
    log('   🔧 Most timing fixes are working, some refinement needed', 'yellow');
  } else {
    log('   ❌ SIGNIFICANT ISSUES DETECTED', 'red');
    log('   🚨 Timing fixes need attention before deployment', 'red');
  }

  return {
    totalPassed,
    totalFailed,
    totalDuration,
    successRate: parseFloat(successRate),
    avgDuration,
    failedTests
  };
}

/**
 * Main execution
 */
async function main() {
  log('🧪 Realtime Session Timing Test Suite', 'bright');
  log('Testing WebSocket session timing fixes and coordination', 'blue');
  log('=' .repeat(80), 'blue');

  try {
    const suiteResults = [];

    // Run each test suite
    for (const [suiteName, suite] of Object.entries(testSuites)) {
      const result = await runTestSuite(suiteName, suite);
      suiteResults.push(result);
    }

    // Generate final report
    const report = generateReport(suiteResults);

    // Exit with appropriate code
    process.exit(report.totalFailed > 0 ? 1 : 0);

  } catch (error) {
    log(`\n❌ FATAL ERROR: ${error.message}`, 'red');
    console.error(error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { runTest, runTestSuite, generateReport };