/**
 * Timing Performance Benchmarks
 * 
 * Performance tests specifically focused on timing operations to ensure
 * that the realtime session timing fixes don't introduce performance regressions.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock dependencies
vi.mock('../../../src/utils/logger.js', () => ({
  createLogger: vi.fn(() => ({
    info: vi.fn(),
    debug: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
  })),
  LogLevel: { DEBUG: 'debug', INFO: 'info' },
  setModuleLogLevel: vi.fn()
}));

vi.mock('../../../src/media/modality/audio.ts', () => ({
  RealtimeAudioManager: vi.fn(() => ({ resetSession: vi.fn() }))
}));

vi.mock('../../../src/agent/models/aliyun/AliyunConfig.js', () => ({
  ALIYUN_AUDIO_CONFIG: { bitDepth: 16, channels: 1, minIntervalMs: 100 },
  ALIYUN_VAD_CONFIG: { type: 'server_vad', threshold: 0.5, silence_duration_ms: 1500 },
  ALIYUN_WEBSOCKET_CONFIG: { endpoint: 'wss://test.endpoint', defaultModel: 'qwen-omni-turbo' },
  ALIYUN_SAMPLE_RATE: 16000,
  AliyunEventType: { SESSION_CREATED: 'session.created' },
  generateEventId: vi.fn(() => 'test-event-id'),
  cleanupRealtimeConnection: vi.fn(),
  createPythonCompatibleSessionUpdate: vi.fn(() => ({ type: 'session.update' })),
  validateAudioConfig: vi.fn(() => ({ isValid: true, warnings: [], errors: [] }))
}));

vi.mock('../../../src/utils/portManager.js', () => ({
  getDownloadServerPort: vi.fn(() => 3001)
}));

vi.mock('../../../src/agent/models/base/WebSocketChatModel.js', () => ({
  WebSocketChatModel: vi.fn(() => ({
    apiKey: 'test-key', model: 'qwen-omni-turbo',
    logger: { info: vi.fn(), debug: vi.fn(), warn: vi.fn(), error: vi.fn() },
    contextBuffer: { conversation: {} }, recentTranscripts: [],
    addTranscript: vi.fn(), _checkApiLimits: vi.fn(() => true),
    _recordApiCost: vi.fn(), _arrayBufferToBase64: vi.fn(),
    _stopContinuousContextAnalysis: vi.fn()
  }))
}));

describe('Timing Performance Benchmarks', () => {
  let AliyunWebSocketChatModel;
  let DualBrainCoordinator;
  let model;
  let coordinator;
  let mockAgentService;

  beforeEach(async () => {
    vi.clearAllMocks();

    // Import modules
    const aliyunModule = await import('../../../src/agent/models/aliyun/AliyunWebSocketChatModel.js');
    AliyunWebSocketChatModel = aliyunModule.AliyunWebSocketChatModel;

    const coordinatorModule = await import('../../../src/agent/arch/dualbrain/DualBrainCoordinator.js');
    DualBrainCoordinator = coordinatorModule.DualBrainCoordinator;

    // Create model
    model = new AliyunWebSocketChatModel({
      apiKey: 'test-key',
      model: 'qwen-omni-turbo'
    });

    // Mock agent service
    mockAgentService = {
      getModel: vi.fn(() => model),
      isDualBrainMode: vi.fn(() => true),
      generateResponse: vi.fn(() => Promise.resolve('test response')),
      options: { agentConfig: { enableDualBrain: true } }
    };

    // Create coordinator
    coordinator = new DualBrainCoordinator(mockAgentService, {
      system2AnalysisInterval: 1000,
      decisionCooldown: 500,
      enableProactiveDecisions: true
    });

    coordinator.isInitialized = true;
  });

  afterEach(() => {
    if (model) {
      model.closeRealtimeMode();
    }
    if (coordinator && coordinator.isActive) {
      coordinator.stopDualBrainSystems();
    }
  });

  describe('isRealtimeModeActive() Performance', () => {
    it('should complete readiness check in under 1ms per call', () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = true;

      const iterations = 10000;
      const results = [];

      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        const result = model.isRealtimeModeActive();
        const elapsed = performance.now() - startTime;
        
        results.push(elapsed);
        expect(result).toBe(true);
      }

      const avgTime = results.reduce((sum, time) => sum + time, 0) / results.length;
      const maxTime = Math.max(...results);
      const minTime = Math.min(...results);

      expect(avgTime).toBeLessThan(1); // Average under 1ms
      expect(maxTime).toBeLessThan(5); // Max under 5ms
      expect(minTime).toBeGreaterThanOrEqual(0);

      console.log(`🚀 isRealtimeModeActive() Performance: avg=${avgTime.toFixed(3)}ms, max=${maxTime.toFixed(3)}ms, min=${minTime.toFixed(3)}ms`);
    });

    it('should maintain consistent performance under different states', () => {
      const states = [
        { socket: null, stabilized: false },
        { socket: { readyState: 0 }, stabilized: false },
        { socket: { readyState: 1 }, stabilized: false },
        { socket: { readyState: 1 }, stabilized: true },
        { socket: { readyState: 3 }, stabilized: true }
      ];

      const iterations = 1000;
      const performanceData = {};

      states.forEach((state, index) => {
        model.realtimeSocket = state.socket;
        model.realtimeSessionStabilized = state.stabilized;

        const times = [];
        for (let i = 0; i < iterations; i++) {
          const startTime = performance.now();
          model.isRealtimeModeActive();
          const elapsed = performance.now() - startTime;
          times.push(elapsed);
        }

        const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
        performanceData[`state_${index}`] = avgTime;

        expect(avgTime).toBeLessThan(1); // Should be fast regardless of state
      });

      // All states should have similar performance (within 50% of each other)
      const avgTimes = Object.values(performanceData);
      const minAvg = Math.min(...avgTimes);
      const maxAvg = Math.max(...avgTimes);

      expect(maxAvg / minAvg).toBeLessThan(1.5); // Within 50% performance variance
    });
  });

  describe('waitForRealtimeReady() Performance', () => {
    it('should return immediately when already ready', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = true;

      const iterations = 100;
      const times = [];

      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        const result = await model.waitForRealtimeReady(1000);
        const elapsed = performance.now() - startTime;
        
        times.push(elapsed);
        expect(result).toBe(true);
      }

      const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      const maxTime = Math.max(...times);

      expect(avgTime).toBeLessThan(10); // Should be very fast when already ready
      expect(maxTime).toBeLessThan(50); // Even worst case should be reasonable

      console.log(`⚡ waitForRealtimeReady() (already ready) Performance: avg=${avgTime.toFixed(3)}ms, max=${maxTime.toFixed(3)}ms`);
    });

    it('should have predictable timing when waiting for stabilization', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = false;

      const stabilizationDelays = [50, 100, 200, 300];
      const results = [];

      for (const delay of stabilizationDelays) {
        // Reset state
        model.realtimeSessionStabilized = false;

        setTimeout(() => {
          model.realtimeSessionStabilized = true;
        }, delay);

        const startTime = performance.now();
        const result = await model.waitForRealtimeReady(1000);
        const elapsed = performance.now() - startTime;

        results.push({ delay, elapsed, result });
        expect(result).toBe(true);
        expect(elapsed).toBeGreaterThanOrEqual(delay - 10); // Allow some margin
        expect(elapsed).toBeLessThan(delay + 100); // Should not overshoot significantly
      }

      console.log('⏱️  waitForRealtimeReady() Timing Accuracy:', results);
    });

    it('should timeout accurately', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = false;

      const timeouts = [100, 200, 500, 1000];
      const results = [];

      for (const timeout of timeouts) {
        const startTime = performance.now();
        const result = await model.waitForRealtimeReady(timeout);
        const elapsed = performance.now() - startTime;

        results.push({ timeout, elapsed, result });
        expect(result).toBe(false);
        expect(elapsed).toBeGreaterThanOrEqual(timeout - 10);
        expect(elapsed).toBeLessThan(timeout + 100);
      }

      console.log('⏰ waitForRealtimeReady() Timeout Accuracy:', results);
    });
  });

  describe('DualBrainCoordinator Performance', () => {
    it('should complete _ensureSystem1RealtimeReady quickly when model is ready', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = true;

      const iterations = 50;
      const times = [];

      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        await coordinator._ensureSystem1RealtimeReady();
        const elapsed = performance.now() - startTime;
        times.push(elapsed);
      }

      const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      const maxTime = Math.max(...times);

      expect(avgTime).toBeLessThan(50); // Should be fast when already ready
      expect(maxTime).toBeLessThan(200); // Even worst case should be reasonable

      console.log(`🧠 _ensureSystem1RealtimeReady() (ready) Performance: avg=${avgTime.toFixed(3)}ms, max=${maxTime.toFixed(3)}ms`);
    });

    it('should handle rapid consecutive readiness checks efficiently', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = true;

      const concurrentChecks = 20;
      const startTime = performance.now();

      const promises = Array(concurrentChecks).fill(null).map(() => 
        coordinator._ensureSystem1RealtimeReady()
      );

      await Promise.all(promises);
      const totalElapsed = performance.now() - startTime;
      const avgPerCheck = totalElapsed / concurrentChecks;

      expect(avgPerCheck).toBeLessThan(100); // Should handle concurrency efficiently
      expect(totalElapsed).toBeLessThan(1000); // Total time should be reasonable

      console.log(`🔄 Concurrent readiness checks: ${concurrentChecks} checks in ${totalElapsed.toFixed(3)}ms (avg=${avgPerCheck.toFixed(3)}ms per check)`);
    });

    it('should maintain performance during proactive decision generation', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = true;
      coordinator.isActive = true;

      const iterations = 10;
      const times = [];

      for (let i = 0; i < iterations; i++) {
        coordinator.lastDecisionTime = 0; // Reset cooldown

        const startTime = performance.now();
        await coordinator.generateProactiveDecision();
        const elapsed = performance.now() - startTime;
        
        times.push(elapsed);
      }

      const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      const maxTime = Math.max(...times);

      // These are API calls so they'll be slower, but should still be reasonable
      expect(avgTime).toBeLessThan(5000); // Average under 5 seconds
      expect(maxTime).toBeLessThan(10000); // Max under 10 seconds

      console.log(`🎯 generateProactiveDecision() Performance: avg=${avgTime.toFixed(0)}ms, max=${maxTime.toFixed(0)}ms`);
    });
  });

  describe('Memory and Resource Performance', () => {
    it('should not leak memory during repeated operations', () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = true;

      // Get initial memory usage
      const initialMemory = process.memoryUsage().heapUsed;

      // Perform many operations
      const iterations = 10000;
      for (let i = 0; i < iterations; i++) {
        model.isRealtimeModeActive();
        
        // Simulate some state changes
        if (i % 100 === 0) {
          model.realtimeSessionStabilized = !model.realtimeSessionStabilized;
          model.realtimeSessionStabilized = true; // Reset to true
        }
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryGrowth = finalMemory - initialMemory;
      const memoryGrowthMB = memoryGrowth / (1024 * 1024);

      expect(memoryGrowthMB).toBeLessThan(10); // Should not grow significantly

      console.log(`💾 Memory usage: Initial=${(initialMemory / 1024 / 1024).toFixed(2)}MB, Final=${(finalMemory / 1024 / 1024).toFixed(2)}MB, Growth=${memoryGrowthMB.toFixed(2)}MB`);
    });

    it('should handle high-frequency state changes without performance degradation', () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      model.realtimeSocket = mockWebSocket;

      const iterations = 50000;
      const batchSize = 1000;
      const batchTimes = [];

      for (let batch = 0; batch < iterations / batchSize; batch++) {
        const batchStart = performance.now();
        
        for (let i = 0; i < batchSize; i++) {
          // Simulate rapid state changes
          model.realtimeSessionStabilized = i % 2 === 0;
          model.isRealtimeModeActive();
        }
        
        const batchElapsed = performance.now() - batchStart;
        batchTimes.push(batchElapsed);
      }

      // Check that performance doesn't degrade over time
      const firstHalfAvg = batchTimes.slice(0, batchTimes.length / 2)
        .reduce((sum, time) => sum + time, 0) / (batchTimes.length / 2);
      
      const secondHalfAvg = batchTimes.slice(batchTimes.length / 2)
        .reduce((sum, time) => sum + time, 0) / (batchTimes.length / 2);

      const performanceDegradation = secondHalfAvg / firstHalfAvg;

      expect(performanceDegradation).toBeLessThan(1.2); // No more than 20% degradation

      console.log(`📈 Performance consistency: First half avg=${firstHalfAvg.toFixed(3)}ms, Second half avg=${secondHalfAvg.toFixed(3)}ms, Degradation ratio=${performanceDegradation.toFixed(3)}`);
    });
  });

  describe('Edge Case Performance', () => {
    it('should handle extreme timeout values efficiently', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = false;

      const extremeTimeouts = [0, 1, Number.MAX_SAFE_INTEGER];
      const results = [];

      for (const timeout of extremeTimeouts) {
        const startTime = performance.now();
        const result = await model.waitForRealtimeReady(timeout);
        const elapsed = performance.now() - startTime;

        results.push({ timeout, elapsed, result });

        if (timeout === 0 || timeout === 1) {
          expect(elapsed).toBeLessThan(100); // Should handle quickly
        }
      }

      console.log('🔄 Extreme timeout handling:', results);
    });

    it('should handle null/undefined values gracefully without performance impact', () => {
      const testCases = [
        { socket: null, stabilized: null },
        { socket: undefined, stabilized: undefined },
        { socket: {}, stabilized: 'invalid' },
        { socket: { readyState: null }, stabilized: false }
      ];

      const iterations = 1000;

      testCases.forEach((testCase, index) => {
        model.realtimeSocket = testCase.socket;
        model.realtimeSessionStabilized = testCase.stabilized;

        const times = [];
        for (let i = 0; i < iterations; i++) {
          const startTime = performance.now();
          const result = model.isRealtimeModeActive();
          const elapsed = performance.now() - startTime;
          times.push(elapsed);
        }

        const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
        expect(avgTime).toBeLessThan(1); // Should still be fast despite invalid values

        console.log(`🛡️  Error case ${index} performance: avg=${avgTime.toFixed(3)}ms`);
      });
    });
  });

  describe('Real-world Performance Scenarios', () => {
    it('should handle typical usage patterns efficiently', async () => {
      // Simulate typical usage: connect, stabilize, check readiness multiple times, disconnect
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      
      const scenarios = [
        'connect', 'stabilize', 'check', 'check', 'check', 'check', 'check',
        'disconnect', 'connect', 'stabilize', 'check', 'check', 'disconnect'
      ];

      const startTime = performance.now();

      for (const scenario of scenarios) {
        switch (scenario) {
          case 'connect':
            model.realtimeSocket = mockWebSocket;
            model.realtimeSessionStabilized = false;
            break;
          case 'stabilize':
            model.realtimeSessionStabilized = true;
            break;
          case 'check':
            model.isRealtimeModeActive();
            break;
          case 'disconnect':
            model.realtimeSocket = null;
            model.realtimeSessionStabilized = false;
            break;
        }
      }

      const totalElapsed = performance.now() - startTime;
      expect(totalElapsed).toBeLessThan(100); // Entire scenario should be fast

      console.log(`🎭 Real-world scenario performance: ${totalElapsed.toFixed(3)}ms for ${scenarios.length} operations`);
    });

    it('should handle concurrent operations from multiple components', async () => {
      const mockWebSocket = { readyState: 1, send: vi.fn(), close: vi.fn() };
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = true;
      coordinator.isActive = true;

      // Simulate concurrent operations from different components
      const startTime = performance.now();

      const operations = await Promise.all([
        // UI checking readiness frequently
        ...Array(20).fill(null).map(() => model.isRealtimeModeActive()),
        
        // Coordinator ensuring readiness
        coordinator._ensureSystem1RealtimeReady(),
        coordinator._ensureSystem1RealtimeReady(),
        
        // Multiple readiness waits
        model.waitForRealtimeReady(100),
        model.waitForRealtimeReady(100),
        
        // State checks
        ...Array(10).fill(null).map(() => model.isRealtimeModeActive())
      ]);

      const totalElapsed = performance.now() - startTime;

      expect(operations.length).toBe(34); // All operations completed
      expect(totalElapsed).toBeLessThan(1000); // Should complete within reasonable time

      console.log(`🏃‍♂️ Concurrent operations performance: ${operations.length} operations in ${totalElapsed.toFixed(3)}ms`);
    });
  });
});