/**
 * WebSocket Timing Benchmarks Test Suite
 * 
 * Comprehensive timing analysis based on observed connection lifecycle patterns:
 * 
 * CRITICAL FINDINGS FROM LOG ANALYSIS:
 * =====================================
 * Initial Connection (Failed):
 * - 12:53:17.154Z: Connection started
 * - 12:53:17.420Z: Connected (266ms)
 * - 12:53:17.421Z: Stabilized (1ms after connected)
 * - 12:53:17.523Z: WebSocket closing detected (102ms after stabilized)
 * - 12:53:17.524Z: Session init failed (1ms after detection)
 * 
 * Recovery Connection (Successful):
 * - 12:53:18.541Z: Recovery started
 * - 12:53:18.702Z: Connected (161ms)
 * - 12:53:18.703Z: Stabilized (1ms after connected)
 * - 12:53:18.806Z: Session created successfully (103ms after stabilized)
 * 
 * KEY INSIGHTS:
 * - The 102ms Pattern: Critical server-side validation window
 * - Connection Speed Variance: 105ms difference (161ms vs 266ms)
 * - Stabilization Consistency: Always 1ms (not the problem)
 * - Success/Failure Window: Determined at ~103ms after stabilization
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { performance } from 'perf_hooks';

// Mock dependencies
vi.mock('../../../src/utils/logger.js', () => ({
  createLogger: vi.fn(() => ({
    info: vi.fn(),
    debug: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
  }))
}));

describe('WebSocket Timing Benchmarks - Critical Pattern Analysis', () => {
  let TimingBenchmarkAnalyzer;
  let analyzer;
  let mockConnectionManager;

  const CRITICAL_PATTERNS = {
    SERVER_VALIDATION_WINDOW: 102, // The critical 102ms server validation window
    STABILIZATION_DELAY: 1,        // Consistent 1ms stabilization
    CONNECTION_VARIANCE: 105,      // Observed variance between connections
    SUCCESS_THRESHOLD: 103,        // Minimum time for successful session creation
    FAST_CONNECTION: 161,          // Observed fast connection time
    SLOW_CONNECTION: 266           // Observed slow connection time
  };

  beforeEach(async () => {
    vi.clearAllMocks();

    // Import the analyzer
    const module = await import('../../../src/agent/services/monitoring/TimingBenchmarkAnalyzer.js');
    TimingBenchmarkAnalyzer = module.TimingBenchmarkAnalyzer;

    // Create analyzer instance
    analyzer = new TimingBenchmarkAnalyzer({
      sampleSize: 10, // Smaller for tests
      timeoutMs: 5000,
      benchmarkInterval: 10
    });

    // Mock connection manager
    mockConnectionManager = {
      state: 'DISCONNECTED',
      stabilized: false,
      lastConnectionTime: 0,
      lastStabilizationTime: 0,
      
      // Simulate connection behavior
      connect: vi.fn(() => {
        const connectionDelay = Math.random() > 0.5 ? 
          CRITICAL_PATTERNS.FAST_CONNECTION : 
          CRITICAL_PATTERNS.SLOW_CONNECTION;
        
        return new Promise((resolve) => {
          setTimeout(() => {
            mockConnectionManager.state = 'CONNECTED';
            mockConnectionManager.lastConnectionTime = performance.now();
            resolve();
          }, connectionDelay);
        });
      }),

      // Simulate stabilization
      waitForStabilization: vi.fn(() => {
        return new Promise((resolve) => {
          setTimeout(() => {
            mockConnectionManager.stabilized = true;
            mockConnectionManager.lastStabilizationTime = performance.now();
            resolve();
          }, CRITICAL_PATTERNS.STABILIZATION_DELAY);
        });
      }),

      // Simulate server validation
      validateSession: vi.fn(() => {
        return new Promise((resolve) => {
          const validationDelay = CRITICAL_PATTERNS.SERVER_VALIDATION_WINDOW + (Math.random() * 20 - 10);
          setTimeout(() => {
            const success = validationDelay >= CRITICAL_PATTERNS.SUCCESS_THRESHOLD;
            resolve({ success, validationTime: validationDelay });
          }, validationDelay);
        });
      }),

      disconnect: vi.fn(() => {
        mockConnectionManager.state = 'DISCONNECTED';
        mockConnectionManager.stabilized = false;
      })
    };
  });

  describe('Critical 102ms Pattern Analysis', () => {
    it('should identify the server validation window timing', async () => {
      const testRuns = 20;
      const timingData = [];

      for (let i = 0; i < testRuns; i++) {
        const startTime = performance.now();
        
        // Establish connection
        await mockConnectionManager.connect();
        const connectionTime = performance.now();
        
        // Wait for stabilization
        await mockConnectionManager.waitForStabilization();
        const stabilizationTime = performance.now();
        
        // Validate session (this is where the 102ms pattern occurs)
        const validationResult = await mockConnectionManager.validateSession();
        const endTime = performance.now();
        
        timingData.push({
          totalTime: endTime - startTime,
          connectionPhase: connectionTime - startTime,
          stabilizationPhase: stabilizationTime - connectionTime,
          validationPhase: endTime - stabilizationTime,
          success: validationResult.success,
          validationTime: validationResult.validationTime
        });
        
        mockConnectionManager.disconnect();
        await new Promise(resolve => setTimeout(resolve, 100)); // Brief cooldown
      }

      // Analyze the timing patterns
      const successfulRuns = timingData.filter(run => run.success);
      const failedRuns = timingData.filter(run => !run.success);
      
      // Validation phase should cluster around 102ms
      const validationTimes = timingData.map(run => run.validationPhase);
      const averageValidationTime = validationTimes.reduce((sum, time) => sum + time, 0) / validationTimes.length;
      
      expect(averageValidationTime).toBeGreaterThan(90);
      expect(averageValidationTime).toBeLessThan(130);
      
      // Stabilization should be consistently fast (around 1ms)
      const stabilizationTimes = timingData.map(run => run.stabilizationPhase);
      const maxStabilizationTime = Math.max(...stabilizationTimes);
      expect(maxStabilizationTime).toBeLessThan(50); // Should be very fast
      
      // Connection times should show variance
      const connectionTimes = timingData.map(run => run.connectionPhase);
      const connectionVariance = Math.max(...connectionTimes) - Math.min(...connectionTimes);
      expect(connectionVariance).toBeGreaterThan(50); // Should show variance
      
      console.log('📊 Critical Pattern Analysis:', {
        averageValidationTime: averageValidationTime.toFixed(2) + 'ms',
        maxStabilizationTime: maxStabilizationTime.toFixed(2) + 'ms',
        connectionVariance: connectionVariance.toFixed(2) + 'ms',
        successRate: ((successfulRuns.length / timingData.length) * 100).toFixed(1) + '%'
      });
    });

    it('should validate the critical timing thresholds', () => {
      const patterns = analyzer.knownPatterns;
      
      // These are the critical timing patterns from log analysis
      expect(patterns.CRITICAL_WINDOW).toBe(102);
      expect(patterns.STABILIZATION_DELAY).toBe(1);
      expect(patterns.CONNECTION_VARIANCE).toBe(105);
      expect(patterns.SUCCESS_THRESHOLD).toBe(103);
    });

    it('should measure connection speed variance accurately', async () => {
      // Test both fast and slow connection scenarios
      const fastConnectionTimes = [];
      const slowConnectionTimes = [];
      
      // Mock predictable connection times
      mockConnectionManager.connect = vi.fn()
        .mockImplementationOnce(() => {
          return new Promise(resolve => {
            setTimeout(() => {
              mockConnectionManager.state = 'CONNECTED';
              resolve();
            }, CRITICAL_PATTERNS.FAST_CONNECTION);
          });
        })
        .mockImplementationOnce(() => {
          return new Promise(resolve => {
            setTimeout(() => {
              mockConnectionManager.state = 'CONNECTED';
              resolve();
            }, CRITICAL_PATTERNS.SLOW_CONNECTION);
          });
        });

      // Measure fast connection
      const fastStart = performance.now();
      await mockConnectionManager.connect();
      const fastEnd = performance.now();
      fastConnectionTimes.push(fastEnd - fastStart);
      
      mockConnectionManager.disconnect();
      
      // Measure slow connection
      const slowStart = performance.now();
      await mockConnectionManager.connect();
      const slowEnd = performance.now();
      slowConnectionTimes.push(slowEnd - slowStart);
      
      const variance = slowConnectionTimes[0] - fastConnectionTimes[0];
      
      expect(variance).toBeGreaterThan(90); // Should match observed 105ms variance
      expect(variance).toBeLessThan(120);
      
      console.log('⚡ Connection Speed Analysis:', {
        fastConnection: fastConnectionTimes[0].toFixed(2) + 'ms',
        slowConnection: slowConnectionTimes[0].toFixed(2) + 'ms',
        variance: variance.toFixed(2) + 'ms'
      });
    });

    it('should benchmark server validation window consistency', async () => {
      const validationTests = [
        { waitTime: 50, expectedSuccess: false, label: 'Too early (50ms)' },
        { waitTime: 102, expectedSuccess: true, label: 'Critical window (102ms)' },
        { waitTime: 150, expectedSuccess: true, label: 'Safe window (150ms)' }
      ];

      for (const test of validationTests) {
        // Mock validation timing
        mockConnectionManager.validateSession = vi.fn(() => {
          return new Promise((resolve) => {
            setTimeout(() => {
              const success = test.waitTime >= CRITICAL_PATTERNS.SUCCESS_THRESHOLD;
              resolve({ 
                success, 
                validationTime: test.waitTime,
                withinCriticalWindow: test.waitTime >= CRITICAL_PATTERNS.SERVER_VALIDATION_WINDOW
              });
            }, test.waitTime);
          });
        });

        const startTime = performance.now();
        const result = await mockConnectionManager.validateSession();
        const actualTime = performance.now() - startTime;

        expect(result.success).toBe(test.expectedSuccess);
        expect(actualTime).toBeGreaterThanOrEqual(test.waitTime - 10);
        expect(actualTime).toBeLessThan(test.waitTime + 50);

        console.log(`🎯 ${test.label}:`, {
          expectedSuccess: test.expectedSuccess,
          actualSuccess: result.success,
          timing: actualTime.toFixed(2) + 'ms'
        });
      }
    });
  });

  describe('Performance Recommendations Validation', () => {
    it('should generate appropriate MediaCoordinator recommendations', () => {
      const recommendations = analyzer.generatePerformanceRecommendations();
      
      expect(recommendations.mediaCoordinatorUpdates).toBeDefined();
      expect(recommendations.mediaCoordinatorUpdates.waitForWebSocketConnection).toBeDefined();
      
      const waitRecommendation = recommendations.mediaCoordinatorUpdates.waitForWebSocketConnection;
      expect(waitRecommendation.recommendation).toContain('150ms');
      expect(waitRecommendation.reasoning).toContain('102ms');
    });

    it('should recommend minimum wait times based on server validation window', () => {
      const analysis = analyzer.analyzeCriticalTimingWindows();
      
      expect(analysis.timingRecommendations.minimumWaitAfterStabilization).toBeGreaterThanOrEqual(150);
      expect(analysis.criticalInsights.serverValidationWindow.criticality).toBe('HIGH');
    });

    it('should provide specific code change recommendations', () => {
      const recommendations = analyzer.generatePerformanceRecommendations();
      
      const codeChange = recommendations.mediaCoordinatorUpdates.waitForWebSocketConnection.codeChange;
      expect(codeChange).toContain('setTimeout(resolve, 150)');
      expect(codeChange).toContain('validateConnectionHealth');
    });
  });

  describe('Real-world Scenario Benchmarks', () => {
    it('should simulate the exact failure scenario from logs', async () => {
      // Simulate the exact timing from the failed connection:
      // Connect (266ms) → Stabilize (1ms) → Fail (102ms)
      
      mockConnectionManager.connect = vi.fn(() => {
        return new Promise(resolve => {
          setTimeout(() => {
            mockConnectionManager.state = 'CONNECTED';
            resolve();
          }, 266); // Exact timing from logs
        });
      });

      mockConnectionManager.waitForStabilization = vi.fn(() => {
        return new Promise(resolve => {
          setTimeout(() => {
            mockConnectionManager.stabilized = true;
            resolve();
          }, 1); // Exact timing from logs
        });
      });

      mockConnectionManager.validateSession = vi.fn(() => {
        return new Promise((resolve, reject) => {
          setTimeout(() => {
            // Simulate the failure that occurred at 102ms
            reject(new Error('WebSocket closing detected'));
          }, 102);
        });
      });

      const startTime = performance.now();
      let failed = false;
      
      try {
        await mockConnectionManager.connect();
        await mockConnectionManager.waitForStabilization();
        await mockConnectionManager.validateSession();
      } catch (error) {
        failed = true;
        const totalTime = performance.now() - startTime;
        
        // Should match the observed pattern: ~370ms total (266 + 1 + 102 + overhead)
        expect(totalTime).toBeGreaterThan(350);
        expect(totalTime).toBeLessThan(400);
        expect(error.message).toContain('WebSocket closing detected');
      }
      
      expect(failed).toBe(true);
      
      console.log('❌ Simulated exact failure scenario from logs');
    });

    it('should simulate the exact success scenario from logs', async () => {
      // Simulate the exact timing from the successful recovery:
      // Connect (161ms) → Stabilize (1ms) → Success (103ms)
      
      mockConnectionManager.connect = vi.fn(() => {
        return new Promise(resolve => {
          setTimeout(() => {
            mockConnectionManager.state = 'CONNECTED';
            resolve();
          }, 161); // Exact timing from logs
        });
      });

      mockConnectionManager.waitForStabilization = vi.fn(() => {
        return new Promise(resolve => {
          setTimeout(() => {
            mockConnectionManager.stabilized = true;
            resolve();
          }, 1); // Exact timing from logs
        });
      });

      mockConnectionManager.validateSession = vi.fn(() => {
        return new Promise(resolve => {
          setTimeout(() => {
            // Simulate the success that occurred at 103ms
            resolve({ 
              success: true, 
              sessionId: 'test-session',
              validationTime: 103
            });
          }, 103);
        });
      });

      const startTime = performance.now();
      let succeeded = false;
      
      try {
        await mockConnectionManager.connect();
        await mockConnectionManager.waitForStabilization();
        const result = await mockConnectionManager.validateSession();
        succeeded = result.success;
        
        const totalTime = performance.now() - startTime;
        
        // Should match the observed pattern: ~265ms total (161 + 1 + 103)
        expect(totalTime).toBeGreaterThan(250);
        expect(totalTime).toBeLessThan(290);
        expect(result.sessionId).toBe('test-session');
      } catch (error) {
        console.error('Unexpected error in success scenario:', error);
      }
      
      expect(succeeded).toBe(true);
      
      console.log('✅ Simulated exact success scenario from logs');
    });
  });

  describe('Timing Analysis Integration', () => {
    it('should integrate with existing MediaCoordinator timing logic', async () => {
      // Test the waitForWebSocketConnection method improvements
      const mockMediaCoordinator = {
        waitForWebSocketConnection: async function(agentService, timeoutMs = 10000) {
          const startTime = performance.now();
          
          while (performance.now() - startTime < timeoutMs) {
            const connectionManager = agentService?.getConnectionManager?.();
            
            if (connectionManager) {
              const state = connectionManager.getConnectionState();
              
              if (state === 'STABILIZED') {
                // NEW: Wait additional time for server-side validation
                // Based on analysis of 102ms critical window
                await new Promise(resolve => setTimeout(resolve, 150));
                
                // Validate connection health
                try {
                  const healthCheck = await this.validateConnectionHealth(connectionManager);
                  if (healthCheck.isHealthy) {
                    return true;
                  }
                } catch (error) {
                  console.warn('Health check failed:', error);
                }
              }
            }
            
            await new Promise(resolve => setTimeout(resolve, 100));
          }
          
          return false;
        },
        
        validateConnectionHealth: async function(connectionManager) {
          // Mock health validation
          return new Promise(resolve => {
            setTimeout(() => {
              resolve({ isHealthy: true, validationTime: performance.now() });
            }, 50);
          });
        }
      };

      // Test the enhanced waiting logic
      const mockAgentService = {
        getConnectionManager: () => ({
          getConnectionState: () => 'STABILIZED'
        })
      };

      const startTime = performance.now();
      const result = await mockMediaCoordinator.waitForWebSocketConnection(mockAgentService, 5000);
      const elapsed = performance.now() - startTime;

      expect(result).toBe(true);
      expect(elapsed).toBeGreaterThanOrEqual(150); // Should wait the additional 150ms
      expect(elapsed).toBeLessThan(300); // But not too long
      
      console.log('🔄 Enhanced MediaCoordinator timing:', {
        result: result,
        elapsed: elapsed.toFixed(2) + 'ms',
        includesValidationWait: elapsed >= 150
      });
    });
  });

  afterEach(() => {
    if (mockConnectionManager) {
      mockConnectionManager.disconnect();
    }
  });
});