#!/usr/bin/env node

/**
 * Performance Validation Runner
 * 
 * Executes comprehensive performance validation and generates detailed reports
 * for the WebSocket connection improvements.
 */

import { WebSocketPerformanceValidator } from './websocket-performance-validator.js';
import { writeFileSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __dirname = dirname(fileURLToPath(import.meta.url));

async function runPerformanceValidation() {
  console.log('🚀 Starting WebSocket Performance Validation Suite\n');
  
  try {
    // Initialize performance validator
    const validator = new WebSocketPerformanceValidator({
      iterations: 30,
      connectionTimeout: 8000,
      stabilizationTimeout: 3000,
      validationTimeout: 2000,
      cooldownPeriod: 100
    });

    // Set up progress reporting
    validator.on('progress', (info) => {
      console.log(`📊 ${info.stage}: ${info.progress}%`);
    });

    // Run comprehensive validation
    const results = await validator.validatePerformanceImprovements();

    // Generate detailed report
    const report = generateDetailedReport(results);

    // Save results
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const outputDir = join(__dirname, '../../reports');
    
    try {
      mkdirSync(outputDir, { recursive: true });
    } catch (err) {
      // Directory might already exist
    }

    const reportPath = join(outputDir, `websocket-performance-validation-${timestamp}.json`);
    const summaryPath = join(outputDir, `websocket-performance-summary-${timestamp}.md`);

    writeFileSync(reportPath, JSON.stringify(results, null, 2));
    writeFileSync(summaryPath, report);

    // Display summary
    console.log('\n' + report);
    console.log(`\n📋 Detailed results saved to: ${reportPath}`);
    console.log(`📄 Summary report saved to: ${summaryPath}`);

    // Exit with appropriate code
    const overallSuccess = results.summary.overallAssessment === 'EXCELLENT' || 
                          results.summary.overallAssessment === 'GOOD';
    process.exit(overallSuccess ? 0 : 1);

  } catch (error) {
    console.error('❌ Performance validation failed:', error);
    process.exit(1);
  }
}

function generateDetailedReport(results) {
  const summary = results.summary;
  
  let report = `# WebSocket Performance Validation Report

Generated: ${new Date().toISOString()}
Duration: ${summary.validationDuration.toFixed(2)}ms

## Overall Assessment: ${summary.overallAssessment}

`;

  // Expected vs Actual Results
  report += `## Expected vs Actual Performance

| Metric | Expected | Actual | Status |
|--------|----------|---------|---------|
`;

  Object.entries(summary.expectedVsActual).forEach(([key, data]) => {
    const status = data.met ? '✅ MET' : '❌ NOT MET';
    const metricName = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    report += `| ${metricName} | ${data.expected} | ${data.actual} | ${status} |\n`;
  });

  // Detailed Metrics
  report += `\n## Detailed Performance Metrics

### Connection Performance
- **Success Rate**: ${(results.connectionPerformance.successRate * 100).toFixed(1)}%
- **Average Connection Time**: ${results.connectionPerformance.avgConnectionTime.toFixed(2)}ms
- **Average Total Time**: ${results.connectionPerformance.avgTotalTime.toFixed(2)}ms
- **95th Percentile Total Time**: ${results.connectionPerformance.p95TotalTime.toFixed(2)}ms
- **Success Rate Improvement**: ${results.connectionPerformance.improvement.successRateImprovement.toFixed(1)}%
- **Speed Improvement**: ${results.connectionPerformance.improvement.speedImprovement.toFixed(1)}%

### MediaCoordinator Timeout Performance
- **Average Timeout**: ${results.timeoutPerformance.averageTimeout.toFixed(2)}ms
- **95th Percentile Timeout**: ${results.timeoutPerformance.p95Timeout.toFixed(2)}ms
- **Within Target Rate**: ${(results.timeoutPerformance.withinTargetRate * 100).toFixed(1)}%
- **Timeout Reduction**: ${results.timeoutPerformance.improvement.timeoutReduction.toFixed(1)}%
- **Reliability Improvement**: ${results.timeoutPerformance.improvement.reliabilityImprovement.toFixed(1)}%

### Error Recovery Performance
- **Recovery Rate**: ${(results.recoveryPerformance.recoveryRate * 100).toFixed(1)}%
- **Average Recovery Time**: ${results.recoveryPerformance.averageRecoveryTime.toFixed(2)}ms
- **Average Retry Count**: ${results.recoveryPerformance.averageRetryCount.toFixed(1)}
- **Recovery Rate Improvement**: ${results.recoveryPerformance.improvement.recoveryRateImprovement.toFixed(1)}%
- **Retry Reduction**: ${results.recoveryPerformance.improvement.retryReduction.toFixed(1)}%

### Memory Performance
- **Initial Memory**: ${(results.memoryPerformance.initialMemory / 1024 / 1024).toFixed(2)}MB
- **Final Memory**: ${(results.memoryPerformance.finalMemory / 1024 / 1024).toFixed(2)}MB
- **Memory Growth**: ${(results.memoryPerformance.memoryGrowth / 1024 / 1024).toFixed(2)}MB
- **Peak Memory**: ${(results.memoryPerformance.peakMemory / 1024 / 1024).toFixed(2)}MB
- **Memory Efficient**: ${results.memoryPerformance.improvement.memoryEfficiency ? 'Yes' : 'No'}
- **Stable Memory Usage**: ${results.memoryPerformance.improvement.stableMemoryUsage ? 'Yes' : 'No'}

### Browser Compatibility Performance
- **setTimeout Average**: ${results.browserPerformance.setTimeoutAverage.toFixed(2)}ms
- **setImmediate Average**: ${results.browserPerformance.setImmediateAverage.toFixed(2)}ms
- **Promise Timing Average**: ${results.browserPerformance.promiseTimingAverage.toFixed(2)}ms
- **Consistent Timing**: ${results.browserPerformance.improvement.consistentTiming ? 'Yes' : 'No'}
- **Cross-Platform Compatibility**: ${results.browserPerformance.improvement.crossPlatformCompatibility ? 'Yes' : 'No'}

`;

  // Achievements
  if (summary.achievements.length > 0) {
    report += `## 🎉 Achievements\n\n`;
    summary.achievements.forEach(achievement => {
      report += `- ${achievement}\n`;
    });
    report += '\n';
  }

  // Concerns
  if (summary.concerns.length > 0) {
    report += `## ⚠️ Concerns\n\n`;
    summary.concerns.forEach(concern => {
      report += `- ${concern}\n`;
    });
    report += '\n';
  }

  // Recommendations
  if (summary.recommendations.length > 0) {
    report += `## 💡 Recommendations\n\n`;
    summary.recommendations.forEach(recommendation => {
      report += `- ${recommendation}\n`;
    });
    report += '\n';
  }

  report += `## Performance Impact Summary

The WebSocket connection improvements have delivered significant performance enhancements:

1. **Connection Reliability**: ${results.connectionPerformance.improvement.successRateImprovement >= 0 ? 'IMPROVED' : 'DEGRADED'} by ${Math.abs(results.connectionPerformance.improvement.successRateImprovement).toFixed(1)}%
2. **Response Speed**: ${results.connectionPerformance.improvement.speedImprovement >= 0 ? 'IMPROVED' : 'DEGRADED'} by ${Math.abs(results.connectionPerformance.improvement.speedImprovement).toFixed(1)}%
3. **Timeout Efficiency**: ${results.timeoutPerformance.improvement.timeoutReduction >= 0 ? 'IMPROVED' : 'DEGRADED'} by ${Math.abs(results.timeoutPerformance.improvement.timeoutReduction).toFixed(1)}%
4. **Recovery Efficiency**: ${results.recoveryPerformance.improvement.retryReduction >= 0 ? 'IMPROVED' : 'DEGRADED'} by ${Math.abs(results.recoveryPerformance.improvement.retryReduction).toFixed(1)}%

### Key Performance Indicators (KPIs)

| KPI | Target | Achieved | Status |
|-----|--------|----------|---------|
| Connection Success Rate | ≥98% | ${(results.connectionPerformance.successRate * 100).toFixed(1)}% | ${results.connectionPerformance.successRate >= 0.98 ? '✅' : '❌'} |
| MediaCoordinator Timeout | ≤5000ms | ${results.timeoutPerformance.averageTimeout.toFixed(0)}ms | ${results.timeoutPerformance.averageTimeout <= 5000 ? '✅' : '❌'} |
| Error Recovery Rate | ≥95% | ${(results.recoveryPerformance.recoveryRate * 100).toFixed(1)}% | ${results.recoveryPerformance.recoveryRate >= 0.95 ? '✅' : '❌'} |
| Average Response Time | ≤1000ms | ${results.connectionPerformance.avgTotalTime.toFixed(0)}ms | ${results.connectionPerformance.avgTotalTime <= 1000 ? '✅' : '❌'} |

---

*This report validates the performance improvements achieved through the WebSocket connection optimization project.*
`;

  return report;
}

// Run if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runPerformanceValidation();
}