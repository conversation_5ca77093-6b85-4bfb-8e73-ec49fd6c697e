/**
 * Character System Performance Benchmark Tests
 * 
 * Comprehensive performance testing for character search, selection, and integration
 * Measures response times, memory usage, and system throughput under various conditions
 */

import { describe, test, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { CharacterService } from '../../app/viewer/services/CharacterService.js';
import { AgentCoordinator } from '../../app/viewer/services/agentCoordinator.js';
import { performanceTimer, MemoryTracker, TestDataGenerator } from '../utils/test-helpers.js';
import { transcribeAudioWithSTT } from '../../src/utils/audioTranscription.js';

describe('Character System Performance Benchmarks', () => {
  let characterService;
  let agentCoordinator;
  let memoryTracker;
  let performanceResults = {};

  // Performance thresholds (in milliseconds)
  const PERFORMANCE_THRESHOLDS = {
    CHARACTER_SEARCH: 500,      // Character search should complete within 500ms
    CHARACTER_SELECTION: 1000,  // Character selection within 1s
    VOICE_TRANSCRIPTION: 3000,  // Voice transcription within 3s
    PERSONALITY_APPLICATION: 2000, // Personality application within 2s
    DUAL_BRAIN_INIT: 5000,      // Dual brain initialization within 5s
    SYSTEM_INTEGRATION: 3000,   // Full system integration within 3s
    MEMORY_INCREASE_LIMIT: 50 * 1024 * 1024, // Max 50MB memory increase
    CONCURRENT_OPERATIONS: 10,  // Support 10 concurrent operations
    THROUGHPUT_MIN: 100         // Min 100 operations per second
  };

  beforeAll(async () => {
    console.log('🚀 Setting up performance test environment...');
    
    memoryTracker = new MemoryTracker();
    memoryTracker.takeSnapshot('test_start');

    // Initialize services with performance monitoring
    characterService = new CharacterService({
      enablePersistence: false,
      agentCoordinator: null
    });

    agentCoordinator = new AgentCoordinator({
      enableVADHandlers: false
    });

    memoryTracker.takeSnapshot('services_initialized');
    console.log('✅ Performance test environment ready');
  });

  afterAll(async () => {
    memoryTracker.takeSnapshot('test_end');
    
    // Generate performance report
    const memoryReport = memoryTracker.generateReport();
    console.log('\n📊 Performance Test Results:');
    console.log('================================');
    
    Object.entries(performanceResults).forEach(([testName, results]) => {
      console.log(`${testName}:`);
      if (Array.isArray(results)) {
        const times = results.map(r => typeof r === 'number' ? r : r.duration);
        const avg = times.reduce((a, b) => a + b, 0) / times.length;
        const min = Math.min(...times);
        const max = Math.max(...times);
        console.log(`  Average: ${avg.toFixed(2)}ms`);
        console.log(`  Min: ${min.toFixed(2)}ms`);
        console.log(`  Max: ${max.toFixed(2)}ms`);
      } else {
        console.log(`  Result: ${JSON.stringify(results, null, 2)}`);
      }
    });

    console.log('\nMemory Usage:');
    console.log(`Max Memory: ${(memoryReport.maxMemoryUsed / 1024 / 1024).toFixed(2)}MB`);
    console.log(`Memory Growth: ${(memoryReport.memoryGrowth / 1024 / 1024).toFixed(2)}MB`);
    console.log('================================\n');

    // Cleanup
    if (characterService) characterService.dispose();
    if (agentCoordinator) await agentCoordinator.dispose();
  });

  beforeEach(() => {
    // Clear any previous character state
    characterService?.clearStoredData();
  });

  describe('Character Search Performance', () => {
    test('should meet character search response time requirements', async () => {
      const searchTerms = ['Luffy', 'Saitama', 'Naruto', 'Goku', 'Vegeta'];
      const searchTimes = [];

      for (const term of searchTerms) {
        const timer = performanceTimer();
        
        // Simulate character database search
        const results = await simulateCharacterSearch(term);
        
        const result = timer.end();
        searchTimes.push(result.duration);
        
        // Verify search completed within threshold
        expect(result.duration).toBeLessThan(PERFORMANCE_THRESHOLDS.CHARACTER_SEARCH);
        expect(results).toBeInstanceOf(Array);
      }

      performanceResults.character_search = searchTimes;

      // Calculate and validate average search time
      const avgSearchTime = searchTimes.reduce((a, b) => a + b, 0) / searchTimes.length;
      expect(avgSearchTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CHARACTER_SEARCH * 0.8); // 80% of threshold
    });

    test('should handle concurrent character searches efficiently', async () => {
      const concurrentSearchCount = PERFORMANCE_THRESHOLDS.CONCURRENT_OPERATIONS;
      const searchTerms = Array(concurrentSearchCount).fill(0).map((_, i) => `Character${i}`);

      const timer = performanceTimer();
      
      // Execute concurrent searches
      const searchPromises = searchTerms.map(term => simulateCharacterSearch(term));
      const results = await Promise.all(searchPromises);
      
      const result = timer.end();
      
      // Verify concurrent performance
      const timePerSearch = result.duration / concurrentSearchCount;
      expect(timePerSearch).toBeLessThan(PERFORMANCE_THRESHOLDS.CHARACTER_SEARCH);
      
      performanceResults.concurrent_search = {
        totalTime: result.duration,
        concurrentCount: concurrentSearchCount,
        timePerSearch
      };
    });

    test('should maintain search performance under stress load', async () => {
      const stressTestCount = 100;
      const searchTimes = [];

      const timer = performanceTimer();

      for (let i = 0; i < stressTestCount; i++) {
        const searchTimer = performanceTimer();
        await simulateCharacterSearch(`StressTest${i}`);
        searchTimes.push(searchTimer.end().duration);
      }

      const result = timer.end();
      const throughput = stressTestCount / (result.duration / 1000); // operations per second

      expect(throughput).toBeGreaterThan(PERFORMANCE_THRESHOLDS.THROUGHPUT_MIN);
      
      // Verify no significant performance degradation
      const firstHalf = searchTimes.slice(0, stressTestCount / 2);
      const secondHalf = searchTimes.slice(stressTestCount / 2);
      
      const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
      const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;
      
      // Second half should not be more than 50% slower than first half
      expect(secondAvg).toBeLessThan(firstAvg * 1.5);

      performanceResults.stress_test = {
        totalOperations: stressTestCount,
        totalTime: result.duration,
        throughput: throughput.toFixed(2),
        performanceDegradation: ((secondAvg - firstAvg) / firstAvg * 100).toFixed(2) + '%'
      };
    });
  });

  describe('Character Selection and Application Performance', () => {
    test('should meet character selection performance requirements', async () => {
      const testCharacter = TestDataGenerator.generateRandomCharacter();
      const selectionTimes = [];

      // Test multiple character selections
      for (let i = 0; i < 10; i++) {
        const timer = performanceTimer();
        
        const success = await characterService.setCharacterContext(testCharacter);
        
        const result = timer.end();
        selectionTimes.push(result.duration);
        
        expect(success).toBe(true);
        expect(result.duration).toBeLessThan(PERFORMANCE_THRESHOLDS.CHARACTER_SELECTION);
      }

      performanceResults.character_selection = selectionTimes;
    });

    test('should efficiently apply personality traits to dual brain system', async () => {
      const testCharacter = TestDataGenerator.generateRandomCharacter();
      
      // Mock agent service with dual brain support
      const mockAgentService = createMockAgentService();
      characterService.options.agentCoordinator = {
        getAgentService: () => mockAgentService
      };

      const timer = performanceTimer();
      
      const success = await characterService.setCharacterContext(testCharacter);
      
      const result = timer.end();
      
      expect(success).toBe(true);
      expect(result.duration).toBeLessThan(PERFORMANCE_THRESHOLDS.PERSONALITY_APPLICATION);
      
      // Verify dual brain integration calls were made efficiently
      expect(mockAgentService.updateCharacterContext).toHaveBeenCalledTimes(1);
      
      performanceResults.personality_application = result.duration;
    });

    test('should handle character switching efficiently', async () => {
      const characters = Array(5).fill(0).map(() => TestDataGenerator.generateRandomCharacter());
      const switchingTimes = [];

      for (const character of characters) {
        const timer = performanceTimer();
        
        await characterService.setCharacterContext(character);
        
        const result = timer.end();
        switchingTimes.push(result.duration);
        
        expect(result.duration).toBeLessThan(PERFORMANCE_THRESHOLDS.CHARACTER_SELECTION);
      }

      // Verify no significant slowdown in character switching
      const avgSwitchTime = switchingTimes.reduce((a, b) => a + b, 0) / switchingTimes.length;
      expect(avgSwitchTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CHARACTER_SELECTION * 0.7);

      performanceResults.character_switching = switchingTimes;
    });
  });

  describe('Voice Input Performance', () => {
    test('should meet voice transcription performance requirements', async () => {
      const voiceInputs = [
        { phrase: 'I want to be Luffy', duration: 2 },
        { phrase: 'Switch to Saitama character', duration: 3 },
        { phrase: 'Change character to Naruto', duration: 3.5 },
        { phrase: 'Select Goku', duration: 1.5 }
      ];

      const transcriptionTimes = [];

      for (const input of voiceInputs) {
        const timer = performanceTimer();
        
        // Create mock audio blob
        const mockAudio = createMockAudioBlob(input.phrase, input.duration);
        
        // Simulate transcription
        const result = await simulateVoiceTranscription(mockAudio);
        
        const transcriptionResult = timer.end();
        transcriptionTimes.push(transcriptionResult.duration);
        
        expect(transcriptionResult.duration).toBeLessThan(PERFORMANCE_THRESHOLDS.VOICE_TRANSCRIPTION);
        expect(result.success).toBe(true);
        expect(result.transcript).toContain(input.phrase);
      }

      performanceResults.voice_transcription = transcriptionTimes;

      // Test transcription accuracy vs speed tradeoff
      const avgTranscriptionTime = transcriptionTimes.reduce((a, b) => a + b, 0) / transcriptionTimes.length;
      expect(avgTranscriptionTime).toBeLessThan(PERFORMANCE_THRESHOLDS.VOICE_TRANSCRIPTION * 0.8);
    });

    test('should handle various audio qualities efficiently', async () => {
      const audioQualities = ['low', 'medium', 'high'];
      const qualityResults = {};

      for (const quality of audioQualities) {
        const timer = performanceTimer();
        
        const mockAudio = createMockAudioBlob('Test character selection', 3, { quality });
        const result = await simulateVoiceTranscription(mockAudio);
        
        const transcriptionResult = timer.end();
        qualityResults[quality] = {
          duration: transcriptionResult.duration,
          success: result.success,
          confidence: result.confidence
        };
        
        expect(transcriptionResult.duration).toBeLessThan(PERFORMANCE_THRESHOLDS.VOICE_TRANSCRIPTION);
      }

      // Higher quality should have better confidence but similar processing time
      expect(qualityResults.high.confidence).toBeGreaterThan(qualityResults.low.confidence);
      expect(Math.abs(qualityResults.high.duration - qualityResults.low.duration)).toBeLessThan(1000);

      performanceResults.voice_quality_performance = qualityResults;
    });
  });

  describe('System Integration Performance', () => {
    test('should initialize agent coordinator efficiently', async () => {
      const mockConfig = {
        toolRegistrationConfig: {},
        toolOptions: {},
        modelProvider: 'aliyun',
        aliyunApiKey: 'test-key',
        modelOptions: {
          defaultModel: 'qwen-max',
          enableRealtime: true,
          audioConfig: {}
        },
        agentConfig: {
          enableAutonomousTools: true,
          enableDualBrain: true,
          maxIterations: 10
        }
      };

      const timer = performanceTimer();
      
      const success = await agentCoordinator.initializeAgentService(mockConfig);
      
      const result = timer.end();
      
      expect(success).toBe(true);
      expect(result.duration).toBeLessThan(PERFORMANCE_THRESHOLDS.DUAL_BRAIN_INIT);

      performanceResults.agent_coordinator_init = result.duration;
    });

    test('should handle full system integration within time limits', async () => {
      memoryTracker.takeSnapshot('integration_start');
      
      const timer = performanceTimer();
      
      // Simulate full system integration workflow
      const testCharacter = TestDataGenerator.generateRandomCharacter();
      
      // 1. Character search
      const searchResults = await simulateCharacterSearch(testCharacter.name);
      expect(searchResults).toHaveLength(1);
      
      // 2. Character selection
      const selectionSuccess = await characterService.setCharacterContext(testCharacter);
      expect(selectionSuccess).toBe(true);
      
      // 3. Dual brain integration (mocked)
      await simulateDualBrainIntegration(testCharacter);
      
      // 4. Voice input simulation
      const voiceInput = createMockAudioBlob(`I want to be ${testCharacter.name}`, 3);
      const voiceResult = await simulateVoiceTranscription(voiceInput);
      expect(voiceResult.success).toBe(true);
      
      const result = timer.end();
      memoryTracker.takeSnapshot('integration_end');
      
      expect(result.duration).toBeLessThan(PERFORMANCE_THRESHOLDS.SYSTEM_INTEGRATION);
      
      // Check memory usage
      const memoryDelta = memoryTracker.getMemoryDelta('integration_start', 'integration_end');
      if (memoryDelta) {
        expect(memoryDelta.usedDelta).toBeLessThan(PERFORMANCE_THRESHOLDS.MEMORY_INCREASE_LIMIT);
      }

      performanceResults.system_integration = {
        duration: result.duration,
        memoryDelta: memoryDelta?.usedDelta || 0
      };
    });

    test('should maintain performance under concurrent system operations', async () => {
      const concurrentOperations = PERFORMANCE_THRESHOLDS.CONCURRENT_OPERATIONS;
      const operations = [];
      
      const timer = performanceTimer();

      // Create concurrent system operations
      for (let i = 0; i < concurrentOperations; i++) {
        const operation = async () => {
          const character = TestDataGenerator.generateRandomCharacter();
          await simulateCharacterSearch(character.name);
          await characterService.setCharacterContext(character);
          return true;
        };
        
        operations.push(operation());
      }

      const results = await Promise.all(operations);
      const result = timer.end();

      // Verify all operations completed successfully
      expect(results.every(r => r === true)).toBe(true);
      
      // Verify concurrent performance
      const timePerOperation = result.duration / concurrentOperations;
      expect(timePerOperation).toBeLessThan(PERFORMANCE_THRESHOLDS.SYSTEM_INTEGRATION * 0.8);

      performanceResults.concurrent_system_operations = {
        totalTime: result.duration,
        operationCount: concurrentOperations,
        timePerOperation
      };
    });
  });

  describe('Memory Usage and Resource Management', () => {
    test('should not leak memory during repeated operations', async () => {
      const operationCount = 50;
      const memorySnapshots = [];
      
      for (let i = 0; i < operationCount; i++) {
        if (i % 10 === 0) {
          memoryTracker.takeSnapshot(`operation_${i}`);
          memorySnapshots.push(`operation_${i}`);
        }
        
        const character = TestDataGenerator.generateRandomCharacter();
        await characterService.setCharacterContext(character);
        
        // Force garbage collection if available
        if (global.gc) {
          global.gc();
        }
      }

      memoryTracker.takeSnapshot('memory_test_end');
      
      // Analyze memory growth
      const memoryGrowth = [];
      for (let i = 1; i < memorySnapshots.length; i++) {
        const delta = memoryTracker.getMemoryDelta(memorySnapshots[i-1], memorySnapshots[i]);
        if (delta) {
          memoryGrowth.push(delta.usedDelta);
        }
      }

      // Memory growth should stabilize (no significant increase in later operations)
      if (memoryGrowth.length > 2) {
        const earlyGrowth = memoryGrowth.slice(0, Math.floor(memoryGrowth.length / 2));
        const lateGrowth = memoryGrowth.slice(Math.floor(memoryGrowth.length / 2));
        
        const avgEarlyGrowth = earlyGrowth.reduce((a, b) => a + b, 0) / earlyGrowth.length;
        const avgLateGrowth = lateGrowth.reduce((a, b) => a + b, 0) / lateGrowth.length;
        
        // Later operations shouldn't use significantly more memory
        expect(avgLateGrowth).toBeLessThan(avgEarlyGrowth * 2);
      }

      performanceResults.memory_stability = {
        operationCount,
        memoryGrowthPattern: memoryGrowth,
        averageGrowth: memoryGrowth.reduce((a, b) => a + b, 0) / memoryGrowth.length
      };
    });

    test('should efficiently clean up resources on disposal', async () => {
      memoryTracker.takeSnapshot('cleanup_start');
      
      // Create multiple character services
      const services = [];
      for (let i = 0; i < 10; i++) {
        const service = new CharacterService({ enablePersistence: false });
        const character = TestDataGenerator.generateRandomCharacter();
        await service.setCharacterContext(character);
        services.push(service);
      }
      
      memoryTracker.takeSnapshot('services_created');
      
      // Dispose all services
      const timer = performanceTimer();
      for (const service of services) {
        service.dispose();
      }
      const result = timer.end();
      
      // Force garbage collection
      if (global.gc) {
        global.gc();
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      memoryTracker.takeSnapshot('cleanup_end');
      
      // Verify cleanup was fast
      expect(result.duration).toBeLessThan(1000); // Should cleanup quickly
      
      // Verify memory was reclaimed
      const memoryDelta = memoryTracker.getMemoryDelta('cleanup_start', 'cleanup_end');
      if (memoryDelta && memoryDelta.usedDelta > 0) {
        // Memory growth should be minimal after cleanup
        expect(memoryDelta.usedDelta).toBeLessThan(10 * 1024 * 1024); // < 10MB
      }

      performanceResults.resource_cleanup = {
        cleanupTime: result.duration,
        memoryReclaimed: memoryDelta?.usedDelta || 0
      };
    });
  });

  // Helper functions for performance testing
  async function simulateCharacterSearch(searchTerm) {
    // Simulate database query delay
    await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100));
    
    // Mock search results
    if (searchTerm.includes('NonExistent')) {
      return [];
    }
    
    return [TestDataGenerator.generateRandomCharacter()];
  }

  async function simulateVoiceTranscription(audioBlob) {
    // Simulate transcription processing time based on audio duration
    const metadata = audioBlob._mockMetadata;
    const processingTime = metadata.duration * 300 + Math.random() * 500; // 300ms per second + random
    
    await new Promise(resolve => setTimeout(resolve, processingTime));
    
    return {
      success: true,
      transcript: metadata.transcript,
      confidence: metadata.confidence,
      processingTime
    };
  }

  async function simulateDualBrainIntegration(character) {
    // Simulate dual brain coordination setup
    await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300));
    
    return {
      system1Applied: true,
      system2Applied: true,
      coordinationActive: true
    };
  }

  function createMockAudioBlob(transcript, duration, options = {}) {
    const { quality = 'medium' } = options;
    
    // Create mock audio data
    const sampleRate = 44100;
    const channels = 1;
    const mockAudioData = new ArrayBuffer(duration * sampleRate * channels * 2);
    const mockBlob = new Blob([mockAudioData], { type: 'audio/wav' });
    
    // Add metadata for testing
    mockBlob._mockMetadata = {
      transcript,
      duration,
      quality,
      confidence: quality === 'high' ? 0.95 : quality === 'medium' ? 0.8 : 0.6
    };
    
    return mockBlob;
  }

  function createMockAgentService() {
    return {
      getDualBrainCoordinator: jest.fn(() => ({
        updateSystem1Context: jest.fn(),
        updateSystem2Context: jest.fn(),
        updateCoordinationParameters: jest.fn()
      })),
      updateCharacterContext: jest.fn(),
      getModel: jest.fn(() => ({
        updateSystemPrompt: jest.fn()
      })),
      isDualBrainMode: jest.fn(() => true)
    };
  }
});