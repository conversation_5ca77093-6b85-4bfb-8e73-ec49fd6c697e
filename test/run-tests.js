#!/usr/bin/env node

/**
 * LangGraph Agent Test Runner
 * Provides convenient commands for running different test suites
 */

import { spawn } from 'child_process';
import { resolve, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// ANSI color codes for output formatting
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    bold: '\x1b[1m'
};

/**
 * Log colored output
 * @param {string} message - Message to log
 * @param {string} color - Color to use
 */
function log(message, color = 'white') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * Run vitest with specified options
 * @param {Array<string>} args - Arguments to pass to vitest
 * @returns {Promise<number>} Exit code
 */
function runVitest(args = []) {
    return new Promise((promiseResolve) => {
        const vitestBin = resolve(__dirname, '../node_modules/.bin/vitest');
        const configPath = resolve(__dirname, 'vitest.config.js');

        const vitestArgs = [
            '--config', configPath,
            ...args
        ];

        log(`Running: vitest ${vitestArgs.join(' ')}`, 'cyan');

        const child = spawn('node', [vitestBin, ...vitestArgs], {
            stdio: 'inherit',
            cwd: resolve(__dirname, '..')
        });

        child.on('close', (code) => {
            promiseResolve(code || 0);
        });

        child.on('error', (error) => {
            log(`Error running tests: ${error.message}`, 'red');
            promiseResolve(1);
        });
    });
}

/**
 * Display usage information
 */
function showUsage() {
    log('LangGraph Agent Test Runner', 'bold');
    log('');
    log('Usage:', 'yellow');
    log('  node run-tests.js [command] [options]', 'white');
    log('');
    log('Commands:', 'yellow');
    log('  quick         Quick tests without real API calls (default)', 'white');
    log('  unit          Unit tests for individual components (enhanced)', 'white');
    log('  media         Comprehensive media tests (96+ tests)', 'white');
    log('  streaming     Streaming and performance tests', 'white');
    log('  dualBrain     Dual brain architecture tests', 'white');
    log('  aliyunModels  Aliyun model integration tests', 'white');
    log('  aliyunRealApi Aliyun real API tests (requires VITE_DASHSCOPE_API_KEY)', 'white');
    log('  websocketVAD  WebSocket VAD and audio tests', 'white');
    log('  animation     Animation system tests', 'white');
    log('  memory        LangGraph memory system tests', 'white');
    log('  agentAudio    Agent audio processing tests', 'white');
    log('  app           Application component tests', 'white');
    log('  integration   Integration tests with real API', 'white');
    log('  server        Server and infrastructure tests (enhanced)', 'white');
    log('  modules       Module integration tests', 'white');
    log('  realApi       Real API tests only (requires vLLM)', 'white');
    log('  all           Run all tests', 'white');
    log('  watch         Run tests in watch mode', 'white');
    log('  coverage      Run tests with coverage report', 'white');
    log('');
    log('Options:', 'yellow');
    log('  --verbose     Verbose output', 'white');
    log('  --reporter    Specify reporter (default, verbose, json)', 'white');
    log('  --bail        Stop on first failure', 'white');
    log('  --no-coverage Disable coverage collection', 'white');
    log('');
    log('Examples:', 'yellow');
    log('  node run-tests.js quick', 'white');
    log('  node run-tests.js media --coverage', 'white');
    log('  node run-tests.js agentAudio', 'white');
    log('  node run-tests.js app --watch', 'white');
    log('  node run-tests.js realApi --coverage', 'white');
    log('  node run-tests.js all --verbose --coverage', 'white');
    log('');
    log('Environment Variables:', 'yellow');
    log('  VITE_VLLM_ENDPOINT     - vLLM API endpoint (default: http://10.120.16.6:20095)', 'white');
    log('  VITE_DASHSCOPE_API_KEY - Aliyun DashScope API key (required for aliyunRealApi tests)', 'white');
}

/**
 * Parse command line arguments
 * @returns {Object} Parsed arguments
 */
function parseArgs() {
    const args = process.argv.slice(2);
    const command = args[0] || 'quick';
    const options = args.slice(1);

    return { command, options };
}

// Test file mappings for organized testing
const TEST_CATEGORIES = {
    quick: [
        'test/src/agent/tools/real-tools.test.js',
        'test/src/agent/input.test.js',
        'test/src/agent/prompt.test.js'
    ],
    unit: [
        // === CORE AGENT TESTS ===
        'test/src/agent/tools/real-tools.test.js',
        'test/src/agent/core/initialization.test.js',
        'test/src/agent/core/tools.test.js',
        'test/src/agent/core/multimodal.test.js',
        'test/src/agent/core/index.test.js',
        
        // === AUDIO & MEDIA TESTS ===
        'test/src/agent/audio/buffer-overflow-fix.test.js',
        'test/src/media/core/CameraManager.test.js',
        'test/src/media/capture/MediaCaptureManager.test.js',
        'test/src/media/modality/audioAnalysis.test.js',
        
        // === APPLICATION TESTS ===
        'test/app/viewer/photoCapture.test.js'
    ],
    media: [
        // === COMPREHENSIVE MEDIA VALIDATION ===
        'test/src/media/capture/MediaCaptureManager.test.js',  // 56 tests
        'test/src/media/core/CameraManager.test.js',           // 22 tests
        'test/src/media/modality/audioAnalysis.test.js',
        'test/src/media/modality/audio.test.js',
        'test/src/media/modality/video.test.js',
        'test/src/media/modality/index.test.js',
        'test/src/media/utils/mediaUtils.consolidated.test.js',
        'test/src/media/utils/vadUtils.test.js',
        'test/app/viewer/photoCapture.test.js'                 // 18 tests
    ],
    app: [
        'test/app/viewer/photoCapture.test.js'
    ],
    agentAudio: [
        'test/src/agent/audio/buffer-overflow-fix.test.js'
    ],
    streaming: [
        // === STREAMING & PERFORMANCE TESTS ===
        'test/src/agent/streaming/StreamingManager.test.js',
        'test/src/agent/streaming/langchain-streaming.test.js',
        'test/src/agent/models/aliyun/streaming-optimization.test.js'
    ],
    integration: [
        'test/integration/multimodal-llm.test.js',
        'test/integration/talkingavatar/tts-animation-integration.test.js',
        'test/integration/talkingavatar/voice-cloning-preservation.test.js',
        'test/src/agent/integration/real-api.test.js',
        'test/src/agent/integration/langgraph-talkingavatar.test.js'
    ],
    realApi: [
        'test/src/agent/integration/real-api.test.js',
        'test/src/agent/streaming/langchain-streaming.test.js',

    ],
    server: [
        // === SERVER & INFRASTRUCTURE TESTS ===
        'test/src/server/middleware/proxy.test.ts',
        'test/src/services/algorithms/algorithms.test.js',
        'test/src/services/algorithms/sglang-llm.test.js',
        'test/src/services/llm/service.test.js',
        'test/src/services/llm/authentication.test.js'
    ],
    modules: [
        // === MODULE INTEGRATION TESTS ===
        'test/src/modules/talkinghead/src/seed-file-fallback.test.js',
        'test/src/modules/talkinghead/src/ui-voice-deletion.test.js'
    ],
    memory: [
        'test/src/agent/memory/memory.test.js'
    ],
    
    // === DUAL BRAIN ARCHITECTURE TESTS (ENHANCED) ===
    dualBrain: [
        'test/src/agent/arch/dualbrain/dualbrain-coordinator-enhanced.test.js',
        'test/src/agent/arch/dualbrain/modern-trigger-system-validation.test.js',
        'test/src/agent/arch/dualbrain/real-api-integration.test.js',
        'test/src/agent/arch/dualbrain/dual-brain-integration.test.js',
        'test/src/agent/models/base/ContextualBridge.test.js',
        'test/src/agent/models/base/DualBrainChatModel.test.js'
    ],
    
    aliyunModels: [
        'test/src/agent/models/aliyun/AliyunConfig.test.js',
        'test/src/agent/models/aliyun/AliyunHttpChatModel.test.js',
        'test/src/agent/models/aliyun/AliyunModelFactory.test.js',
        'test/src/agent/models/aliyun/core/langchain-v3-compliance.test.js'
    ],
    
    // REAL API TESTS (ENHANCED WITH MODERN TRIGGER SYSTEM)
    aliyunRealApi: [
        'test/src/agent/models/aliyun/AliyunConfig.test.js',
        'test/src/agent/models/aliyun/AliyunHttpChatModel.test.js',
        'test/src/agent/models/aliyun/AliyunHttpChatModel.advanced.test.js',
        'test/src/agent/models/aliyun/core/aliyun-api-validation.test.js',
        'test/src/agent/models/aliyun/core/langchain-v3-compliance.test.js',
        'test/src/agent/models/aliyun/http/aliyun-real-api-test.js',
        'test/src/agent/arch/dualbrain/real-api-integration.test.js' // Modern trigger system with real API
    ],
    
    websocketVAD: [
        'test/src/agent/models/aliyun/websocket-comprehensive.test.js',
        'test/src/agent/models/aliyun/vad/aliyun-vad-simulator.test.js',
        'test/src/agent/models/aliyun/websocket/aliyun-vad-detection.test.js',
        'test/src/agent/models/aliyun/websocket/aliyun-proxy-vad.test.js'
    ],
    
    animation: [
        'test/src/animation/animation-looping-fix.test.js',
        'test/src/animation/looping-verification.test.js',
        'test/src/animation/TalkingAnimation.test.js'
    ]
};

/**
 * Main test runner function
 */
async function main() {
    const { command, options } = parseArgs();

    if (command === 'help' || command === '--help' || command === '-h') {
        showUsage();
        return 0;
    }

    log('🧪 LangGraph Agent Test Suite', 'bold');
    log('═══════════════════════════════════', 'blue');

    let vitestArgs = [];
    let exitCode = 0;

    try {
        switch (command) {
            case 'quick':
                log('Running quick tests (no real API)...', 'green');
                vitestArgs = ['run', ...TEST_CATEGORIES.quick, ...options];
                break;

            case 'unit':
                log('Running unit tests...', 'green');
                vitestArgs = ['run', ...TEST_CATEGORIES.unit, ...options];
                break;

            case 'media':
                log('Running media processing and modality tests...', 'green');
                vitestArgs = ['run', ...TEST_CATEGORIES.media, ...options];
                break;

            case 'agentAudio':
                log('Running agent audio processing tests...', 'green');
                vitestArgs = ['run', ...TEST_CATEGORIES.agentAudio, ...options];
                break;

            case 'streaming':
                log('Running streaming tests...', 'green');
                vitestArgs = ['run', ...TEST_CATEGORIES.streaming, ...options];
                break;

            case 'integration':
                log('Running integration tests with real API...', 'green');
                vitestArgs = ['run', ...TEST_CATEGORIES.integration, ...options];
                break;

            case 'realApi':
                log('Running real API tests only...', 'green');
                vitestArgs = ['run', ...TEST_CATEGORIES.realApi, ...options];
                break;

            case 'all':
                log('Running all tests...', 'green');
                vitestArgs = ['run', ...options];
                break;

            case 'watch':
                log('Running tests in watch mode...', 'green');
                vitestArgs = ['watch', ...options];
                break;

            case 'coverage':
                log('Running tests with coverage...', 'green');
                vitestArgs = ['run', '--coverage', ...options];
                break;

            case 'memory':
                log('Running LangGraph memory system tests...', 'green');
                vitestArgs = ['run', ...TEST_CATEGORIES.memory, ...options];
                break;
                
            case 'dualBrain':
                log('Running enhanced dual brain architecture tests...', 'green');
                log('  ⚡ Modern trigger system validation', 'cyan');
                log('  🔄 Enhanced context flow testing', 'cyan');
                log('  🛡️ User activation controls', 'cyan');
                log('  📊 Performance optimization validation', 'cyan');
                vitestArgs = ['run', ...TEST_CATEGORIES.dualBrain, ...options];
                break;
                
            case 'aliyunModels':
                log('Running Aliyun model integration tests...', 'green');
                vitestArgs = ['run', ...TEST_CATEGORIES.aliyunModels, ...options];
                break;
                
            case 'aliyunRealApi':
                log('Running Aliyun real API tests with modern trigger system...', 'green');
                log('  🔑 Dashscope API integration', 'cyan');
                log('  ⚡ Modern trigger system with real API', 'cyan');
                log('  🧠 Dual brain decision making validation', 'cyan');
                log('  📈 Performance benchmarking', 'cyan');
                if (!process.env.VITE_DASHSCOPE_API_KEY) {
                    log('⚠️  Warning: VITE_DASHSCOPE_API_KEY not found - some tests may skip', 'yellow');
                    log('   Set API key: VITE_DASHSCOPE_API_KEY=sk-your-key node run-tests.js aliyunRealApi', 'cyan');
                } else {
                    log(`✅ Using API key: VITE_DASHSCOPE_API_KEY=sk-***${process.env.VITE_DASHSCOPE_API_KEY.slice(-4)}`, 'green');
                }
                vitestArgs = ['run', ...TEST_CATEGORIES.aliyunRealApi, ...options];
                break;
                
            case 'websocketVAD':
                log('Running WebSocket VAD and audio tests...', 'green');
                vitestArgs = ['run', ...TEST_CATEGORIES.websocketVAD, ...options];
                break;
                
            case 'animation':
                log('Running animation system tests...', 'green');
                vitestArgs = ['run', ...TEST_CATEGORIES.animation, ...options];
                break;

            default:
                log(`Unknown command: ${command}`, 'red');
                showUsage();
                return 1;
        }

        // Add common options
        if (!vitestArgs.includes('--reporter') && !options.includes('--reporter')) {
            vitestArgs.push('--reporter=verbose');
        }

        log('');
        exitCode = await runVitest(vitestArgs);

        log('');
        if (exitCode === 0) {
            log('✅ Tests completed successfully!', 'green');
        } else {
            log('❌ Tests failed!', 'red');
        }

    } catch (error) {
        log(`Error: ${error.message}`, 'red');
        exitCode = 1;
    }

    return exitCode;
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    main().then(exitCode => {
        process.exit(exitCode);
    }).catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}

export { main, runVitest };