/**
 * Production Integration Validation Tests
 * Real-world scenario testing for WebSocket connection system
 * 
 * MISSION: Test actual integration scenarios that would occur in production
 * 
 * Test Scenarios:
 * 1. Full application startup sequence
 * 2. MediaCoordinator + WebSocket coordination
 * 3. Error recovery under load
 * 4. Session lifecycle management
 * 5. Resource cleanup validation
 * 6. Performance under realistic conditions
 */

const { expect } = require('chai');
const sinon = require('sinon');
const { performance } = require('perf_hooks');

// Import real modules for integration testing
const ConnectionManager = require('../../src/agent/services/connection/ConnectionManager.js').default;
const AliyunWebSocketChatModel = require('../../src/agent/models/aliyun/AliyunWebSocketChatModel.js').AliyunWebSocketChatModel;
const { MediaCoordinator } = require('../../app/viewer/services/mediaCoordinator.ts');
const { createLogger } = require('../../src/utils/simple-logger.js');

// Enhanced Mock WebSocket for production-like behavior
class ProductionWebSocket {
    static CONNECTING = 0;
    static OPEN = 1;
    static CLOSING = 2;
    static CLOSED = 3;

    constructor(url, protocols) {
        this.url = url;
        this.protocols = protocols;
        this.readyState = ProductionWebSocket.CONNECTING;
        this.bufferedAmount = 0;

        this.onopen = null;
        this.onclose = null;
        this.onmessage = null;
        this.onerror = null;

        this._eventListeners = {};
        this._messageQueue = [];
        this._connectionLatency = Math.random() * 200 + 50; // 50-250ms realistic latency
        this._isStable = false;

        this._simulateRealisticConnection();
    }

    _simulateRealisticConnection() {
        // Simulate realistic connection establishment
        setTimeout(() => {
            this.readyState = ProductionWebSocket.OPEN;
            this._triggerEvent('open');

            // Simulate connection stabilization period
            setTimeout(() => {
                this._isStable = true;
            }, 100 + Math.random() * 100); // 100-200ms stabilization

        }, this._connectionLatency);
    }

    addEventListener(event, callback) {
        if (!this._eventListeners[event]) {
            this._eventListeners[event] = [];
        }
        this._eventListeners[event].push(callback);
    }

    removeEventListener(event, callback) {
        if (this._eventListeners[event]) {
            const index = this._eventListeners[event].indexOf(callback);
            if (index > -1) {
                this._eventListeners[event].splice(index, 1);
            }
        }
    }

    send(data) {
        if (this.readyState !== ProductionWebSocket.OPEN) {
            throw new Error('WebSocket is not open');
        }

        if (!this._isStable) {
            // Queue messages until connection is stable
            this._messageQueue.push(data);
            return;
        }

        // Process queued messages
        while (this._messageQueue.length > 0) {
            this._processMessage(this._messageQueue.shift());
        }

        // Process current message
        this._processMessage(data);
    }

    _processMessage(data) {
        try {
            const parsedData = typeof data === 'string' ? JSON.parse(data) : data;

            // Simulate realistic server response times
            const responseDelay = Math.random() * 50 + 10; // 10-60ms

            setTimeout(() => {
                switch (parsedData.type) {
                    case 'session.update':
                        this._simulateSessionCreated();
                        break;
                    case 'validation.ping':
                        this._simulateValidationResponse();
                        break;
                    case 'quality.ping':
                        this._simulateQualityPong(parsedData.id);
                        break;
                    case 'conversation.item.create':
                        this._simulateConversationResponse();
                        break;
                    case 'response.create':
                        this._simulateResponseGeneration();
                        break;
                    default:
                        // Generic acknowledgment
                        this._sendMessage({
                            type: 'ack',
                            id: parsedData.event_id || 'unknown'
                        });
                }
            }, responseDelay);
        } catch (error) {
            console.warn('Error processing message:', error);
        }
    }

    close(code = 1000, reason = '') {
        this.readyState = ProductionWebSocket.CLOSING;
        setTimeout(() => {
            this.readyState = ProductionWebSocket.CLOSED;
            this._triggerEvent('close', { code, reason, wasClean: code === 1000 });
        }, 10);
    }

    _triggerEvent(eventName, data = null) {
        const handler = this[`on${eventName}`];
        if (handler) {
            handler(data || { type: eventName });
        }

        if (this._eventListeners[eventName]) {
            this._eventListeners[eventName].forEach(callback => {
                callback(data || { type: eventName });
            });
        }
    }

    _sendMessage(message) {
        setTimeout(() => {
            this._triggerEvent('message', {
                data: JSON.stringify(message)
            });
        }, 5);
    }

    _simulateSessionCreated() {
        // Simulate realistic server validation timing (observed ~102ms)
        setTimeout(() => {
            this._sendMessage({
                type: 'session.created',
                session: {
                    id: `session_${Date.now()}`,
                    model: 'qwen2-audio-instruct',
                    modalities: ['text', 'audio'],
                    status: 'active'
                },
                event_id: `evt_${Date.now()}`
            });
        }, 102);
    }

    _simulateValidationResponse() {
        this._sendMessage({
            type: 'session.created',
            timestamp: Date.now(),
            status: 'validated'
        });
    }

    _simulateQualityPong(pingId) {
        this._sendMessage({
            type: 'quality.pong',
            id: pingId,
            timestamp: Date.now(),
            server_time: Date.now()
        });
    }

    _simulateConversationResponse() {
        this._sendMessage({
            type: 'conversation.item.created',
            item: {
                id: `item_${Date.now()}`,
                type: 'message',
                status: 'completed'
            }
        });
    }

    _simulateResponseGeneration() {
        // Simulate response generation with multiple chunks
        const chunks = [
            { type: 'response.text.delta', text: 'Hello, ' },
            { type: 'response.text.delta', text: 'how can I ' },
            { type: 'response.text.delta', text: 'help you today?' },
            { type: 'response.text.done', text: 'Hello, how can I help you today?' }
        ];

        chunks.forEach((chunk, index) => {
            setTimeout(() => {
                this._sendMessage(chunk);
            }, index * 100);
        });

        setTimeout(() => {
            this._sendMessage({
                type: 'response.done',
                response: {
                    id: `resp_${Date.now()}`,
                    status: 'completed'
                }
            });
        }, chunks.length * 100 + 50);
    }
}

// Set up global WebSocket mock
global.WebSocket = ProductionWebSocket;

describe('Production Integration Validation', function () {
    this.timeout(60000); // 60 second timeout for integration tests

    let logger;
    let cleanup = [];

    beforeEach(function () {
        logger = createLogger('ProductionIntegrationTest');
        cleanup = [];
    });

    afterEach(async function () {
        // Clean up all resources
        for (const cleanupFn of cleanup.reverse()) {
            try {
                await cleanupFn();
            } catch (error) {
                console.warn('Cleanup error:', error.message);
            }
        }
        cleanup = [];

        // Reset singletons
        if (ConnectionManager._instance) {
            try {
                await ConnectionManager._instance.dispose();
            } catch (error) {
                // Ignore cleanup errors
            }
            ConnectionManager._instance = null;
        }
    });

    describe('1. Full Application Startup Sequence', function () {
        it('should complete full startup within reasonable time', async function () {
            const startupMetrics = {
                startTime: performance.now(),
                connectionTime: 0,
                sessionTime: 0,
                mediaTime: 0,
                totalTime: 0
            };

            // Step 1: Initialize connection manager
            const connectionStart = performance.now();
            const connectionManager = await ConnectionManager.getInstance();
            cleanup.push(() => connectionManager.dispose());

            // Step 2: Create WebSocket connection
            const wsConfig = {
                url: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime?api_key=test&model=qwen2-audio-instruct',
                options: {
                    headers: {
                        'Authorization': 'Bearer test-api-key',
                        'user-agent': 'hologram-software/1.0.0'
                    }
                },
                initializeSession: async () => {
                    return new Promise(resolve => {
                        setTimeout(() => resolve(true), 150); // Realistic session init time
                    });
                }
            };

            const connected = await connectionManager.connect(wsConfig);
            startupMetrics.connectionTime = performance.now() - connectionStart;

            expect(connected).to.be.true;
            expect(startupMetrics.connectionTime).to.be.below(5000, 'Connection should complete within 5s');

            // Step 3: Initialize Aliyun model
            const sessionStart = performance.now();
            const aliyunModel = new AliyunWebSocketChatModel({
                apiKey: 'test-api-key',
                model: 'qwen2-audio-instruct',
                modalities: ['text', 'audio']
            });
            cleanup.push(() => aliyunModel.cleanup());

            const sessionReady = await aliyunModel.waitForRealtimeReady(8000);
            startupMetrics.sessionTime = performance.now() - sessionStart;

            expect(sessionReady).to.be.true;
            expect(startupMetrics.sessionTime).to.be.below(8000, 'Session should be ready within 8s');

            // Step 4: Initialize MediaCoordinator
            const mediaStart = performance.now();
            const mockAgentService = {
                getConnectionManager: () => connectionManager,
                connectInputCoordinator: sinon.stub().resolves(true)
            };

            const mediaCoordinator = new MediaCoordinator({
                enableInputCoordination: true,
                requireUserConsent: false,
                mediaConfig: {
                    connection: {
                        baseTimeout: 2000,
                        maxTimeout: 5000,
                        qualityThreshold: 70
                    }
                }
            });
            cleanup.push(() => mediaCoordinator.dispose());

            const mediaReady = await mediaCoordinator.initializeInputCoordination(mockAgentService, false);
            startupMetrics.mediaTime = performance.now() - mediaStart;

            expect(mediaReady).to.be.true;
            expect(startupMetrics.mediaTime).to.be.below(3000, 'Media coordinator should initialize within 3s');

            // Calculate total startup time
            startupMetrics.totalTime = performance.now() - startupMetrics.startTime;

            logger.info('Application Startup Metrics:', {
                connectionTime: `${startupMetrics.connectionTime.toFixed(0)}ms`,
                sessionTime: `${startupMetrics.sessionTime.toFixed(0)}ms`,
                mediaTime: `${startupMetrics.mediaTime.toFixed(0)}ms`,
                totalTime: `${startupMetrics.totalTime.toFixed(0)}ms`
            });

            // Production readiness assertions
            expect(startupMetrics.totalTime).to.be.below(15000, 'Total startup should complete within 15s');
            expect(startupMetrics.connectionTime).to.be.below(5000, 'Connection phase should be <5s');
            expect(startupMetrics.sessionTime).to.be.below(8000, 'Session phase should be <8s');
            expect(startupMetrics.mediaTime).to.be.below(3000, 'Media phase should be <3s');
        });

        it('should handle startup failures gracefully', async function () {
            // Mock WebSocket that fails during startup
            class FailingStartupWebSocket extends ProductionWebSocket {
                constructor(url, protocols) {
                    super(url, protocols);
                    setTimeout(() => {
                        this.readyState = ProductionWebSocket.CLOSED;
                        this._triggerEvent('error', new Error('Startup connection failed'));
                        this._triggerEvent('close', { code: 1006, reason: 'Network error' });
                    }, 100);
                }
            }

            const originalWebSocket = global.WebSocket;
            global.WebSocket = FailingStartupWebSocket;

            try {
                const connectionManager = await ConnectionManager.getInstance({
                    maxReconnectAttempts: 2,
                    baseTimeout: 1000
                });
                cleanup.push(() => connectionManager.dispose());

                const wsConfig = {
                    url: 'wss://mock-server.com/api-ws/v1/realtime',
                    options: { headers: { 'Authorization': 'Bearer test-key' } }
                };

                const startTime = performance.now();

                try {
                    await connectionManager.connect(wsConfig);
                    expect.fail('Should have thrown connection error');
                } catch (error) {
                    const failureTime = performance.now() - startTime;
                    expect(error.message).to.include('connection');
                    expect(failureTime).to.be.below(5000, 'Should fail quickly, not hang');

                    // Should be in proper error state, not crashed
                    expect(connectionManager.getConnectionState()).to.equal('ERROR');
                }

            } finally {
                global.WebSocket = originalWebSocket;
            }
        });
    });

    describe('2. MediaCoordinator + WebSocket Coordination', function () {
        it('should coordinate MediaCoordinator with WebSocket lifecycle', async function () {
            const connectionManager = await ConnectionManager.getInstance();
            cleanup.push(() => connectionManager.dispose());

            const coordinationEvents = [];

            // Track coordination events
            connectionManager.on('stateChange', (newState, oldState) => {
                coordinationEvents.push({ type: 'connection', from: oldState, to: newState, time: Date.now() });
            });

            const wsConfig = {
                url: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime',
                options: { headers: { 'Authorization': 'Bearer test-key' } },
                initializeSession: async () => true
            };

            // Connect WebSocket
            await connectionManager.connect(wsConfig);

            // Initialize MediaCoordinator after connection is ready
            const mockAgentService = {
                getConnectionManager: () => connectionManager,
                connectInputCoordinator: async (coordinator) => {
                    coordinationEvents.push({ type: 'media', event: 'coordinator_connected', time: Date.now() });
                    return true;
                }
            };

            const mediaCoordinator = new MediaCoordinator({
                enableInputCoordination: true,
                requireUserConsent: false,
                connectionConfig: {
                    minQualityScore: 60,
                    adaptiveTimeout: true
                }
            });
            cleanup.push(() => mediaCoordinator.dispose());

            const mediaReady = await mediaCoordinator.initializeInputCoordination(mockAgentService, false);
            expect(mediaReady).to.be.true;

            // Verify coordination sequence
            const connectionEvents = coordinationEvents.filter(e => e.type === 'connection');
            const mediaEvents = coordinationEvents.filter(e => e.type === 'media');

            expect(connectionEvents.length).to.be.at.least(3, 'Should have multiple connection state changes');
            expect(mediaEvents.length).to.be.at.least(1, 'Should have media coordination events');

            // Check that media initialization happened after connection was established
            const lastConnectionTime = Math.max(...connectionEvents.map(e => e.time));
            const firstMediaTime = Math.min(...mediaEvents.map(e => e.time));

            expect(firstMediaTime).to.be.at.least(lastConnectionTime,
                'Media coordination should happen after connection is established');

            // Verify adaptive timeout was used
            const timeoutMetrics = mediaCoordinator.getAdaptiveTimeoutMetrics();
            expect(timeoutMetrics.current).to.be.at.most(5000, 'Should use adaptive timeout ≤5s');
            expect(timeoutMetrics.successRate).to.be.above(0, 'Should track success rate');

            logger.info('Coordination sequence completed successfully', {
                connectionEvents: connectionEvents.length,
                mediaEvents: mediaEvents.length,
                adaptiveTimeout: timeoutMetrics.current,
                successRate: timeoutMetrics.successRate
            });
        });

        it('should handle WebSocket connection drops during media operations', async function () {
            const connectionManager = await ConnectionManager.getInstance();
            cleanup.push(() => connectionManager.dispose());

            // Connect and initialize media coordinator
            const wsConfig = {
                url: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime',
                options: { headers: { 'Authorization': 'Bearer test-key' } },
                initializeSession: async () => true
            };

            await connectionManager.connect(wsConfig);

            const mockAgentService = {
                getConnectionManager: () => connectionManager,
                connectInputCoordinator: sinon.stub().resolves(true)
            };

            const mediaCoordinator = new MediaCoordinator({
                enableInputCoordination: true,
                requireUserConsent: false
            });
            cleanup.push(() => mediaCoordinator.dispose());

            await mediaCoordinator.initializeInputCoordination(mockAgentService, false);

            // Simulate connection drop during operation
            const socket = connectionManager._socket;
            const connectionDropTime = Date.now();

            let stateChangeDetected = false;
            mediaCoordinator.connectionManager?.on?.('stateChange', (newState) => {
                if (newState === 'ERROR' || newState === 'DISCONNECTED') {
                    stateChangeDetected = true;
                }
            });

            // Drop connection
            socket.close(1006, 'Network error during operation');

            // Wait for error handling
            await new Promise(resolve => setTimeout(resolve, 200));

            // Verify graceful handling
            const inputCoordinator = mediaCoordinator.getInputCoordinator();
            expect(inputCoordinator).to.not.be.null; // Should still exist

            logger.info('Connection drop handled gracefully', {
                connectionDropDetected: stateChangeDetected,
                inputCoordinatorStillExists: inputCoordinator !== null,
                responseTime: Date.now() - connectionDropTime
            });
        });
    });

    describe('3. Error Recovery Under Load', function () {
        it('should maintain stability under concurrent operations', async function () {
            const connectionManager = await ConnectionManager.getInstance();
            cleanup.push(() => connectionManager.dispose());

            const wsConfig = {
                url: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime',
                options: { headers: { 'Authorization': 'Bearer test-key' } },
                initializeSession: async () => true
            };

            await connectionManager.connect(wsConfig);

            // Create multiple MediaCoordinators simulating concurrent users
            const coordinators = [];
            const mockAgentService = {
                getConnectionManager: () => connectionManager,
                connectInputCoordinator: sinon.stub().resolves(true)
            };

            for (let i = 0; i < 5; i++) {
                const coordinator = new MediaCoordinator({
                    enableInputCoordination: true,
                    requireUserConsent: false
                });
                coordinators.push(coordinator);
                cleanup.push(() => coordinator.dispose());
            }

            // Initialize all coordinators concurrently
            const startTime = performance.now();
            const initPromises = coordinators.map(coordinator =>
                coordinator.initializeInputCoordination(mockAgentService, false)
            );

            const results = await Promise.allSettled(initPromises);
            const loadTime = performance.now() - startTime;

            // Analyze results
            const successful = results.filter(r => r.status === 'fulfilled' && r.value === true);
            const failed = results.filter(r => r.status === 'rejected' || r.value === false);

            expect(successful.length).to.be.at.least(3, 'At least 3/5 coordinators should initialize successfully under load');
            expect(loadTime).to.be.below(10000, 'Concurrent initialization should complete within 10s');

            // Test message sending under load
            const messagePromises = [];
            for (let i = 0; i < 20; i++) {
                messagePromises.push(
                    new Promise(resolve => {
                        setTimeout(() => {
                            const sent = connectionManager.send({ type: 'test.message', id: i });
                            resolve(sent);
                        }, i * 10);
                    })
                );
            }

            const messageResults = await Promise.all(messagePromises);
            const successfulMessages = messageResults.filter(sent => sent === true);

            expect(successfulMessages.length).to.be.at.least(15, 'Should send most messages successfully under load');

            logger.info('Load test completed', {
                coordinators: coordinators.length,
                successfulInits: successful.length,
                failedInits: failed.length,
                loadTime: `${loadTime.toFixed(0)}ms`,
                successfulMessages: successfulMessages.length,
                totalMessages: messageResults.length
            });
        });

        it('should recover from multiple rapid errors', async function () {
            const connectionManager = await ConnectionManager.getInstance();
            cleanup.push(() => connectionManager.dispose());

            const errorCount = [];
            const recoveryTimes = [];

            connectionManager.on('error', () => {
                errorCount.push(Date.now());
            });

            connectionManager.on('recovered', (data) => {
                recoveryTimes.push(Date.now());
            });

            const wsConfig = {
                url: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime',
                options: { headers: { 'Authorization': 'Bearer test-key' } },
                initializeSession: async () => true
            };

            await connectionManager.connect(wsConfig);

            const socket = connectionManager._socket;

            // Simulate rapid errors
            for (let i = 0; i < 3; i++) {
                setTimeout(() => {
                    socket._triggerEvent('error', new Error(`Rapid error ${i + 1}`));
                }, i * 100);
            }

            // Wait for error handling and potential recovery
            await new Promise(resolve => setTimeout(resolve, 2000));

            // System should not be in a crashed state
            const currentState = connectionManager.getConnectionState();
            expect(['ERROR', 'RECONNECTING', 'DISCONNECTED']).to.include(currentState);

            logger.info('Rapid error recovery test completed', {
                errorsReceived: errorCount.length,
                recoveryAttempts: recoveryTimes.length,
                finalState: currentState
            });
        });
    });

    describe('4. Session Lifecycle Management', function () {
        it('should manage complete session lifecycle correctly', async function () {
            const lifecycleEvents = [];

            // Initialize Aliyun model with session tracking
            const aliyunModel = new AliyunWebSocketChatModel({
                apiKey: 'test-api-key',
                model: 'qwen2-audio-instruct',
                modalities: ['text', 'audio']
            });
            cleanup.push(() => aliyunModel.cleanup());

            // Track session events
            aliyunModel.on('sessionReady', (data) => {
                lifecycleEvents.push({ event: 'sessionReady', data, time: Date.now() });
            });

            aliyunModel.on('error', (error) => {
                lifecycleEvents.push({ event: 'error', error: error.message, time: Date.now() });
            });

            // Connect and initialize session
            const connected = await aliyunModel.connect();
            expect(connected).to.be.true;

            // Wait for session to be ready
            const sessionReady = await aliyunModel.waitForRealtimeReady(8000);
            expect(sessionReady).to.be.true;

            // Test session functionality
            const testMessage = 'Hello, this is a test message for session validation.';
            const response = await aliyunModel.invoke(testMessage, { responseTimeout: 10000 });

            expect(response).to.not.be.null;
            expect(response.content).to.be.a('string');

            // Clean disconnection
            await aliyunModel.disconnect();

            // Analyze lifecycle
            const sessionReadyEvents = lifecycleEvents.filter(e => e.event === 'sessionReady');
            const errorEvents = lifecycleEvents.filter(e => e.event === 'error');

            expect(sessionReadyEvents.length).to.be.at.least(1, 'Should have session ready event');
            expect(errorEvents.length).to.equal(0, 'Should not have errors during normal lifecycle');

            logger.info('Session lifecycle completed successfully', {
                events: lifecycleEvents.length,
                sessionReadyEvents: sessionReadyEvents.length,
                errorEvents: errorEvents.length,
                sessionId: aliyunModel.realtimeSessionId
            });
        });

        it('should handle session timeout and recreation', async function () {
            // Mock WebSocket that simulates session timeout
            class SessionTimeoutWebSocket extends ProductionWebSocket {
                _simulateSessionCreated() {
                    // Don't send session.created to simulate timeout
                    setTimeout(() => {
                        this._sendMessage({
                            type: 'session.timeout',
                            reason: 'Session creation timeout'
                        });
                    }, 1000);
                }
            }

            const originalWebSocket = global.WebSocket;
            global.WebSocket = SessionTimeoutWebSocket;

            try {
                const aliyunModel = new AliyunWebSocketChatModel({
                    apiKey: 'test-api-key',
                    model: 'qwen2-audio-instruct'
                });
                cleanup.push(() => aliyunModel.cleanup());

                const sessionEvents = [];
                aliyunModel.on('error', (error) => {
                    sessionEvents.push({ event: 'error', message: error.message });
                });

                // This should timeout gracefully
                const connected = await aliyunModel.connect();
                expect(connected).to.be.true; // Connection succeeds

                const sessionReady = await aliyunModel.waitForRealtimeReady(3000);
                expect(sessionReady).to.be.false; // Session creation fails

                // Should handle timeout gracefully without crashing
                const hasSessionTimeoutError = sessionEvents.some(e =>
                    e.message && e.message.includes('timeout')
                );

                logger.info('Session timeout handled gracefully', {
                    sessionTimeoutDetected: hasSessionTimeoutError,
                    totalSessionEvents: sessionEvents.length
                });

            } finally {
                global.WebSocket = originalWebSocket;
            }
        });
    });

    describe('5. Resource Cleanup Validation', function () {
        it('should clean up all resources properly on disposal', async function () {
            const resourceTracking = {
                connectionManagers: 0,
                mediaCoordinators: 0,
                aliyunModels: 0,
                eventListeners: 0,
                timers: 0
            };

            // Track resource creation
            const originalConnectionManager = ConnectionManager.getInstance;
            ConnectionManager.getInstance = async (...args) => {
                resourceTracking.connectionManagers++;
                return originalConnectionManager.apply(ConnectionManager, args);
            };

            // Create multiple components
            const connectionManager = await ConnectionManager.getInstance();
            const mediaCoordinator = new MediaCoordinator({
                enableInputCoordination: true,
                requireUserConsent: false
            });
            const aliyunModel = new AliyunWebSocketChatModel({
                apiKey: 'test-api-key',
                model: 'qwen2-audio-instruct'
            });

            resourceTracking.mediaCoordinators++;
            resourceTracking.aliyunModels++;

            // Add some event listeners
            const testListener = () => { };
            connectionManager.on('stateChange', testListener);
            connectionManager.on('error', testListener);
            resourceTracking.eventListeners += 2;

            // Connect everything
            const wsConfig = {
                url: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime',
                options: { headers: { 'Authorization': 'Bearer test-key' } },
                initializeSession: async () => true
            };

            await connectionManager.connect(wsConfig);
            await aliyunModel.connect();

            const mockAgentService = {
                getConnectionManager: () => connectionManager,
                connectInputCoordinator: sinon.stub().resolves(true)
            };

            await mediaCoordinator.initializeInputCoordination(mockAgentService, false);

            // Now clean up everything
            const cleanupStartTime = performance.now();

            await mediaCoordinator.dispose();
            await aliyunModel.cleanup();
            await connectionManager.dispose();

            const cleanupTime = performance.now() - cleanupStartTime;

            // Verify cleanup
            expect(cleanupTime).to.be.below(2000, 'Cleanup should complete within 2s');

            // Check that singletons are reset
            expect(ConnectionManager._instance).to.be.null;

            // Verify no active timers or listeners are left
            // (This is hard to test comprehensively, but we can check basic state)
            expect(connectionManager.getConnectionState()).to.equal('DISCONNECTED');

            logger.info('Resource cleanup validation completed', {
                cleanupTime: `${cleanupTime.toFixed(0)}ms`,
                resourcesCreated: resourceTracking,
                singletonReset: ConnectionManager._instance === null
            });

            // Restore original method
            ConnectionManager.getInstance = originalConnectionManager;
        });

        it('should handle forced cleanup gracefully', async function () {
            const connectionManager = await ConnectionManager.getInstance();

            const wsConfig = {
                url: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime',
                options: { headers: { 'Authorization': 'Bearer test-key' } },
                initializeSession: async () => true
            };

            await connectionManager.connect(wsConfig);

            // Simulate forced cleanup (like browser tab close)
            const cleanupErrors = [];

            // Mock cleanup that might throw
            const originalCleanup = connectionManager._cleanup;
            connectionManager._cleanup = function () {
                try {
                    if (Math.random() > 0.5) {
                        throw new Error('Simulated cleanup error');
                    }
                    return originalCleanup.call(this);
                } catch (error) {
                    cleanupErrors.push(error);
                }
            };

            // Force cleanup
            try {
                await connectionManager.dispose();
            } catch (error) {
                cleanupErrors.push(error);
            }

            // Should complete without hanging, even with errors
            expect(cleanupErrors.length).to.be.at.most(2, 'Should handle cleanup errors gracefully');

            logger.info('Forced cleanup handled gracefully', {
                cleanupErrors: cleanupErrors.length
            });
        });
    });

    describe('6. Performance Under Realistic Conditions', function () {
        it('should maintain performance with realistic message throughput', async function () {
            const connectionManager = await ConnectionManager.getInstance();
            cleanup.push(() => connectionManager.dispose());

            const wsConfig = {
                url: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime',
                options: { headers: { 'Authorization': 'Bearer test-key' } },
                initializeSession: async () => true
            };

            await connectionManager.connect(wsConfig);

            const performanceMetrics = {
                messagesSent: 0,
                messagesSuccessful: 0,
                avgResponseTime: 0,
                peakResponseTime: 0,
                minResponseTime: Infinity,
                responseTimes: []
            };

            // Test realistic message throughput (1-2 messages per second)
            const testDuration = 10000; // 10 seconds
            const messageInterval = 500; // 500ms between messages
            const startTime = Date.now();

            const sendMessage = async () => {
                const messageStart = performance.now();

                try {
                    const success = connectionManager.send({
                        type: 'test.performance',
                        id: performanceMetrics.messagesSent,
                        timestamp: Date.now()
                    });

                    performanceMetrics.messagesSent++;

                    if (success) {
                        performanceMetrics.messagesSuccessful++;
                        const responseTime = performance.now() - messageStart;
                        performanceMetrics.responseTimes.push(responseTime);

                        performanceMetrics.peakResponseTime = Math.max(performanceMetrics.peakResponseTime, responseTime);
                        performanceMetrics.minResponseTime = Math.min(performanceMetrics.minResponseTime, responseTime);
                    }
                } catch (error) {
                    console.warn('Message send error:', error.message);
                }
            };

            // Send messages at regular intervals
            const messageTimer = setInterval(sendMessage, messageInterval);

            // Run for test duration
            await new Promise(resolve => setTimeout(resolve, testDuration));
            clearInterval(messageTimer);

            // Calculate metrics
            if (performanceMetrics.responseTimes.length > 0) {
                performanceMetrics.avgResponseTime = performanceMetrics.responseTimes.reduce((sum, time) => sum + time, 0) / performanceMetrics.responseTimes.length;
            }

            const successRate = (performanceMetrics.messagesSuccessful / performanceMetrics.messagesSent) * 100;
            const throughput = performanceMetrics.messagesSent / (testDuration / 1000);

            // Performance assertions
            expect(successRate).to.be.at.least(95, `Success rate ${successRate.toFixed(1)}% should be ≥95%`);
            expect(performanceMetrics.avgResponseTime).to.be.below(100, `Average response time ${performanceMetrics.avgResponseTime.toFixed(1)}ms should be <100ms`);
            expect(performanceMetrics.peakResponseTime).to.be.below(500, `Peak response time ${performanceMetrics.peakResponseTime.toFixed(1)}ms should be <500ms`);
            expect(throughput).to.be.at.least(1.5, `Throughput ${throughput.toFixed(1)} msg/s should be ≥1.5 msg/s`);

            logger.info('Performance test completed', {
                duration: `${testDuration}ms`,
                messagesSent: performanceMetrics.messagesSent,
                messagesSuccessful: performanceMetrics.messagesSuccessful,
                successRate: `${successRate.toFixed(1)}%`,
                avgResponseTime: `${performanceMetrics.avgResponseTime.toFixed(1)}ms`,
                peakResponseTime: `${performanceMetrics.peakResponseTime.toFixed(1)}ms`,
                minResponseTime: `${performanceMetrics.minResponseTime.toFixed(1)}ms`,
                throughput: `${throughput.toFixed(1)} msg/s`
            });
        });

        it('should handle memory usage efficiently over time', async function () {
            if (typeof global.gc !== 'function') {
                this.skip('Memory test requires --expose-gc flag');
                return;
            }

            const connectionManager = await ConnectionManager.getInstance();
            cleanup.push(() => connectionManager.dispose());

            const wsConfig = {
                url: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime',
                options: { headers: { 'Authorization': 'Bearer test-key' } },
                initializeSession: async () => true
            };

            await connectionManager.connect(wsConfig);

            // Measure initial memory usage
            global.gc();
            const initialMemory = process.memoryUsage();

            // Perform memory-intensive operations
            for (let i = 0; i < 1000; i++) {
                connectionManager.send({
                    type: 'memory.test',
                    id: i,
                    data: 'x'.repeat(1000) // 1KB of data per message
                });

                if (i % 100 === 0) {
                    await new Promise(resolve => setTimeout(resolve, 10));
                }
            }

            // Wait for operations to complete
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Force garbage collection and measure final memory
            global.gc();
            const finalMemory = process.memoryUsage();

            const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
            const memoryIncreasePercent = (memoryIncrease / initialMemory.heapUsed) * 100;

            logger.info('Memory usage test completed', {
                initialHeapUsed: `${(initialMemory.heapUsed / 1024 / 1024).toFixed(1)}MB`,
                finalHeapUsed: `${(finalMemory.heapUsed / 1024 / 1024).toFixed(1)}MB`,
                memoryIncrease: `${(memoryIncrease / 1024 / 1024).toFixed(1)}MB`,
                memoryIncreasePercent: `${memoryIncreasePercent.toFixed(1)}%`
            });

            // Memory should not increase excessively
            expect(memoryIncreasePercent).to.be.below(50,
                `Memory increase ${memoryIncreasePercent.toFixed(1)}% should be <50%`);
        });
    });
});