/**
 * WebSocket Connection Reliability Validation Tests
 * Production Readiness Assessment for Hologram Software
 * 
 * MISSION: Validate WebSocket connection reliability and real-world stability
 * 
 * Critical Test Scenarios:
 * 1. Initial connection establishment (should work >95% of time)
 * 2. Server validation period handling (150ms wait after stabilization)
 * 3. MediaCoordinator integration (no more 10s timeouts)
 * 4. Error cascade prevention (safe event emission working)
 * 5. State transition integrity (ERROR→ERROR loops fixed)
 * 6. Recovery mechanism efficiency (faster reconnection)
 * 
 * Production Readiness Criteria:
 * - No more "setImmediate is not defined" errors
 * - No more "Timeout waiting for WebSocket connection" in MediaCoordinator
 * - No more invalid state transition errors
 * - Reliable connection establishment within 5s
 * - Graceful error handling without cascade failures
 */

const { expect } = require('chai');
const sinon = require('sinon');
const { createLogger } = require('../../src/utils/simple-logger.js');

// Import the classes we need to test
const ConnectionManager = require('../../src/agent/services/connection/ConnectionManager.js').default;
const AliyunWebSocketChatModel = require('../../src/agent/models/aliyun/AliyunWebSocketChatModel.js').AliyunWebSocketChatModel;
const { MediaCoordinator } = require('../../app/viewer/services/mediaCoordinator.ts');

// Mock WebSocket for testing
class MockWebSocket {
    static CONNECTING = 0;
    static OPEN = 1;
    static CLOSING = 2;
    static CLOSED = 3;

    constructor(url, protocols) {
        this.url = url;
        this.protocols = protocols;
        this.readyState = MockWebSocket.CONNECTING;
        this.bufferedAmount = 0;

        // Event handlers
        this.onopen = null;
        this.onclose = null;
        this.onmessage = null;
        this.onerror = null;

        // Mock behavior
        this._eventListeners = {};

        // Simulate connection establishment after brief delay
        setTimeout(() => {
            this.readyState = MockWebSocket.OPEN;
            this._triggerEvent('open');
        }, 50);
    }

    addEventListener(event, callback) {
        if (!this._eventListeners[event]) {
            this._eventListeners[event] = [];
        }
        this._eventListeners[event].push(callback);
    }

    removeEventListener(event, callback) {
        if (this._eventListeners[event]) {
            const index = this._eventListeners[event].indexOf(callback);
            if (index > -1) {
                this._eventListeners[event].splice(index, 1);
            }
        }
    }

    send(data) {
        if (this.readyState !== MockWebSocket.OPEN) {
            throw new Error('WebSocket is not open');
        }

        // Simulate server responses
        setTimeout(() => {
            const parsedData = typeof data === 'string' ? JSON.parse(data) : data;

            if (parsedData.type === 'session.update') {
                this._simulateSessionCreated();
            } else if (parsedData.type === 'validation.ping') {
                this._simulateValidationPong(parsedData.id);
            } else if (parsedData.type === 'quality.ping') {
                this._simulateQualityPong(parsedData.id);
            }
        }, 10);
    }

    close(code = 1000, reason = '') {
        this.readyState = MockWebSocket.CLOSING;
        setTimeout(() => {
            this.readyState = MockWebSocket.CLOSED;
            this._triggerEvent('close', { code, reason, wasClean: code === 1000 });
        }, 10);
    }

    _triggerEvent(eventName, data = null) {
        // Trigger handler if set
        const handler = this[`on${eventName}`];
        if (handler) {
            handler(data || { type: eventName });
        }

        // Trigger event listeners
        if (this._eventListeners[eventName]) {
            this._eventListeners[eventName].forEach(callback => {
                callback(data || { type: eventName });
            });
        }
    }

    _simulateSessionCreated() {
        setTimeout(() => {
            this._triggerEvent('message', {
                data: JSON.stringify({
                    type: 'session.created',
                    session: {
                        id: 'mock-session-id',
                        status: 'created'
                    }
                })
            });
        }, 102); // Simulate server validation timing
    }

    _simulateValidationPong(id) {
        setTimeout(() => {
            this._triggerEvent('message', {
                data: JSON.stringify({
                    type: 'session.created',
                    id: id,
                    timestamp: Date.now()
                })
            });
        }, 50);
    }

    _simulateQualityPong(pingId) {
        setTimeout(() => {
            this._triggerEvent('message', {
                data: JSON.stringify({
                    type: 'quality.pong',
                    id: pingId,
                    timestamp: Date.now()
                })
            });
        }, 30);
    }
}

// Set up global WebSocket mock
global.WebSocket = MockWebSocket;

describe('WebSocket Connection Reliability Validation', function () {
    this.timeout(30000); // 30 second timeout for comprehensive tests

    let logger;
    let connectionManager;
    let mockAgentService;

    beforeEach(async function () {
        logger = createLogger('WebSocketReliabilityTest');

        // Reset singleton
        if (ConnectionManager._instance) {
            ConnectionManager._instance = null;
        }

        // Create mock agent service
        mockAgentService = {
            getConnectionManager: () => connectionManager,
            connectionManager: null,
            connectInputCoordinator: sinon.stub().resolves(true)
        };
    });

    afterEach(async function () {
        if (connectionManager) {
            try {
                await connectionManager.dispose();
            } catch (error) {
                // Ignore cleanup errors
            }
        }
    });

    describe('1. Initial Connection Establishment Reliability (>95% Success Rate)', function () {
        it('should establish connection consistently within 5 seconds', async function () {
            const results = [];
            const attempts = 20; // Test 20 connections for reliability

            for (let i = 0; i < attempts; i++) {
                const startTime = Date.now();

                try {
                    // Reset singleton for each attempt
                    ConnectionManager._instance = null;
                    connectionManager = await ConnectionManager.getInstance();
                    mockAgentService.connectionManager = connectionManager;

                    const wsConfig = {
                        url: 'wss://mock-server.com/api-ws/v1/realtime',
                        options: { headers: { 'Authorization': 'Bearer test-key' } },
                        initializeSession: async () => {
                            // Mock session initialization
                            return true;
                        }
                    };

                    const connected = await connectionManager.connect(wsConfig);
                    const connectionTime = Date.now() - startTime;

                    results.push({
                        attempt: i + 1,
                        success: connected,
                        duration: connectionTime,
                        withinTimeout: connectionTime < 5000
                    });

                    if (connected) {
                        await connectionManager.disconnect(1000, 'Test complete');
                    }

                } catch (error) {
                    results.push({
                        attempt: i + 1,
                        success: false,
                        duration: Date.now() - startTime,
                        error: error.message,
                        withinTimeout: false
                    });
                }

                // Brief pause between attempts
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            // Analyze results
            const successful = results.filter(r => r.success);
            const successRate = (successful.length / attempts) * 100;
            const avgConnectionTime = successful.reduce((sum, r) => sum + r.duration, 0) / successful.length;
            const withinTimeoutRate = results.filter(r => r.withinTimeout).length / attempts * 100;

            logger.info('Connection Establishment Reliability Results:', {
                totalAttempts: attempts,
                successful: successful.length,
                successRate: `${successRate.toFixed(1)}%`,
                avgConnectionTime: `${avgConnectionTime.toFixed(0)}ms`,
                withinTimeoutRate: `${withinTimeoutRate.toFixed(1)}%`,
                failures: results.filter(r => !r.success).map(r => ({ attempt: r.attempt, error: r.error }))
            });

            // Production readiness assertions
            expect(successRate).to.be.at.least(95, `Success rate ${successRate.toFixed(1)}% should be ≥95%`);
            expect(avgConnectionTime).to.be.below(5000, `Average connection time ${avgConnectionTime.toFixed(0)}ms should be <5s`);
            expect(withinTimeoutRate).to.be.at.least(90, `Timeout compliance ${withinTimeoutRate.toFixed(1)}% should be ≥90%`);
        });

        it('should handle connection failures gracefully without errors', async function () {
            const errorsSeen = [];

            // Mock WebSocket that fails to connect
            class FailingWebSocket extends MockWebSocket {
                constructor(url, protocols) {
                    super(url, protocols);
                    setTimeout(() => {
                        this.readyState = MockWebSocket.CLOSED;
                        this._triggerEvent('error', new Error('Connection failed'));
                        this._triggerEvent('close', { code: 1006, reason: 'Connection failed' });
                    }, 100);
                }
            }

            const originalWebSocket = global.WebSocket;
            global.WebSocket = FailingWebSocket;

            try {
                connectionManager = await ConnectionManager.getInstance();

                const wsConfig = {
                    url: 'wss://mock-server.com/api-ws/v1/realtime',
                    options: { headers: { 'Authorization': 'Bearer test-key' } }
                };

                // Capture any errors
                const originalError = logger.error;
                logger.error = (...args) => {
                    errorsSeen.push(args);
                    originalError.apply(logger, args);
                };

                const connected = await connectionManager.connect(wsConfig);

                expect(connected).to.be.false;
                expect(errorsSeen.length).to.be.at.least(1, 'Should log connection errors');

                // Check that we don't see specific production issues
                const errorMessages = errorsSeen.flat().join(' ');
                expect(errorMessages).to.not.include('setImmediate is not defined');
                expect(errorMessages).to.not.include('invalid state transition');

            } finally {
                global.WebSocket = originalWebSocket;
            }
        });
    });

    describe('2. Server Validation Period Handling (150ms Wait)', function () {
        it('should wait appropriate time for server validation after stabilization', async function () {
            connectionManager = await ConnectionManager.getInstance();

            const wsConfig = {
                url: 'wss://mock-server.com/api-ws/v1/realtime',
                options: { headers: { 'Authorization': 'Bearer test-key' } },
                initializeSession: async () => {
                    return new Promise(resolve => {
                        // Simulate session initialization
                        setTimeout(() => resolve(true), 102); // Server validation timing
                    });
                }
            };

            const startTime = Date.now();
            const connected = await connectionManager.connect(wsConfig);
            const totalTime = Date.now() - startTime;

            expect(connected).to.be.true;
            expect(totalTime).to.be.at.least(150, 'Should wait at least 150ms for server validation');
            expect(totalTime).to.be.below(3000, 'Should not take longer than 3s total');

            // Verify the connection went through proper state transitions
            const healthStatus = connectionManager.getEnhancedHealthStatus
                ? connectionManager.getEnhancedHealthStatus()
                : { state: connectionManager.getConnectionState() };

            expect(['READY', 'STABILIZED']).to.include(healthStatus.state);
        });

        it('should handle server validation timeout gracefully', async function () {
            // Mock WebSocket that never sends session.created
            class SlowValidationWebSocket extends MockWebSocket {
                _simulateSessionCreated() {
                    // Don't send session.created - simulate server validation timeout
                }
            }

            const originalWebSocket = global.WebSocket;
            global.WebSocket = SlowValidationWebSocket;

            try {
                connectionManager = await ConnectionManager.getInstance({
                    validationTimeout: 500 // Short timeout for testing
                });

                const wsConfig = {
                    url: 'wss://mock-server.com/api-ws/v1/realtime',
                    options: { headers: { 'Authorization': 'Bearer test-key' } },
                    initializeSession: async () => true
                };

                const startTime = Date.now();
                const connected = await connectionManager.connect(wsConfig);
                const totalTime = Date.now() - startTime;

                // Should fail due to validation timeout, but gracefully
                expect(connected).to.be.false;
                expect(totalTime).to.be.at.least(500, 'Should wait for validation timeout');
                expect(totalTime).to.be.below(2000, 'Should fail quickly on validation timeout');

            } finally {
                global.WebSocket = originalWebSocket;
            }
        });
    });

    describe('3. MediaCoordinator Integration (No 10s Timeouts)', function () {
        it('should initialize MediaCoordinator without long timeouts', async function () {
            connectionManager = await ConnectionManager.getInstance();
            mockAgentService.connectionManager = connectionManager;

            // Connect WebSocket first
            const wsConfig = {
                url: 'wss://mock-server.com/api-ws/v1/realtime',
                options: { headers: { 'Authorization': 'Bearer test-key' } },
                initializeSession: async () => true
            };

            await connectionManager.connect(wsConfig);

            // Create MediaCoordinator and test initialization
            const mediaCoordinator = new MediaCoordinator({
                enableInputCoordination: true,
                requireUserConsent: false,
                autoStartAudioCapture: false
            });

            const startTime = Date.now();
            const initialized = await mediaCoordinator.initializeInputCoordination(mockAgentService, false);
            const initTime = Date.now() - startTime;

            expect(initialized).to.be.true;
            expect(initTime).to.be.below(5000, `MediaCoordinator initialization took ${initTime}ms, should be <5s`);

            await mediaCoordinator.dispose();
        });

        it('should use MediaCoordinator for input coordination', async function () {
            connectionManager = await ConnectionManager.getInstance();
            mockAgentService.connectionManager = connectionManager;

            // Connect WebSocket first
            const wsConfig = {
                url: 'wss://mock-server.com/api-ws/v1/realtime',
                options: { headers: { 'Authorization': 'Bearer test-key' } },
                initializeSession: async () => true
            };

            await connectionManager.connect(wsConfig);

            // Create MediaCoordinator with basic config
            const mediaCoordinator = new MediaCoordinator({
                enableInputCoordination: true,
                requireUserConsent: false
            });

            const startTime = Date.now();
            const initialized = await mediaCoordinator.initializeInputCoordination(mockAgentService, false);
            const initTime = Date.now() - startTime;

            expect(initialized).to.be.true;
            expect(initTime).to.be.below(10000, `MediaCoordinator initialization took ${initTime}ms, should be <10s`);

            // Verify basic functionality
            expect(mediaCoordinator.getInputCoordinator()).to.not.be.null;

            await mediaCoordinator.dispose();
        });
    });

    describe('4. Error Cascade Prevention (Safe Event Emission)', function () {
        it('should prevent ERROR→ERROR state transition loops', async function () {
            connectionManager = await ConnectionManager.getInstance();

            const stateTransitions = [];
            connectionManager.on('stateChange', (newState, oldState) => {
                stateTransitions.push({ from: oldState, to: newState, timestamp: Date.now() });
            });

            // Mock WebSocket that triggers errors
            class ErrorProneWebSocket extends MockWebSocket {
                constructor(url, protocols) {
                    super(url, protocols);
                    setTimeout(() => {
                        this._triggerEvent('error', new Error('First error'));
                    }, 100);

                    setTimeout(() => {
                        this._triggerEvent('error', new Error('Second error'));
                    }, 200);
                }
            }

            const originalWebSocket = global.WebSocket;
            global.WebSocket = ErrorProneWebSocket;

            try {
                const wsConfig = {
                    url: 'wss://mock-server.com/api-ws/v1/realtime',
                    options: { headers: { 'Authorization': 'Bearer test-key' } }
                };

                await connectionManager.connect(wsConfig);

                // Wait for error handling to complete
                await new Promise(resolve => setTimeout(resolve, 500));

                // Check for ERROR→ERROR transitions
                const errorToErrorTransitions = stateTransitions.filter(t =>
                    t.from === 'ERROR' && t.to === 'ERROR'
                );

                expect(errorToErrorTransitions.length).to.equal(0,
                    `Found ${errorToErrorTransitions.length} ERROR→ERROR transitions, should be 0`);

                // Verify no rapid-fire state changes (indicating cascade)
                const rapidTransitions = [];
                for (let i = 1; i < stateTransitions.length; i++) {
                    const timeDiff = stateTransitions[i].timestamp - stateTransitions[i - 1].timestamp;
                    if (timeDiff < 10) { // Less than 10ms between transitions
                        rapidTransitions.push({
                            transition: `${stateTransitions[i - 1].to}→${stateTransitions[i].to}`,
                            timeDiff
                        });
                    }
                }

                expect(rapidTransitions.length).to.be.below(3,
                    'Should not have excessive rapid state transitions indicating cascading errors');

            } finally {
                global.WebSocket = originalWebSocket;
            }
        });

        it('should emit events safely without throwing exceptions', async function () {
            connectionManager = await ConnectionManager.getInstance();

            const eventsSeen = [];
            const errorsThrown = [];

            // Add event listeners that might throw
            connectionManager.on('message', () => {
                eventsSeen.push('message');
                // Simulate handler that throws
                if (eventsSeen.length === 3) {
                    throw new Error('Handler error');
                }
            });

            connectionManager.on('error', (error) => {
                eventsSeen.push('error');
            });

            // Capture any uncaught exceptions
            const originalUncaught = process.listeners('uncaughtException');
            process.removeAllListeners('uncaughtException');
            process.on('uncaughtException', (error) => {
                errorsThrown.push(error);
            });

            try {
                const wsConfig = {
                    url: 'wss://mock-server.com/api-ws/v1/realtime',
                    options: { headers: { 'Authorization': 'Bearer test-key' } },
                    initializeSession: async () => true
                };

                await connectionManager.connect(wsConfig);

                // Send messages to trigger events
                for (let i = 0; i < 5; i++) {
                    connectionManager.send({ type: 'test.message', id: i });
                    await new Promise(resolve => setTimeout(resolve, 10));
                }

                await new Promise(resolve => setTimeout(resolve, 200));

                // Should have received events despite handler throwing
                expect(eventsSeen.length).to.be.at.least(5, 'Should receive events even with throwing handlers');

                // Should not have uncaught exceptions
                expect(errorsThrown.length).to.equal(0, 'Should not have uncaught exceptions from event handlers');

            } finally {
                // Restore original uncaught exception handlers
                process.removeAllListeners('uncaughtException');
                originalUncaught.forEach(handler => process.on('uncaughtException', handler));
            }
        });
    });

    describe('5. State Transition Integrity', function () {
        it('should follow valid state transition paths', async function () {
            connectionManager = await ConnectionManager.getInstance();

            const stateTransitions = [];
            connectionManager.on('stateChange', (newState, oldState) => {
                stateTransitions.push({ from: oldState, to: newState });
            });

            const wsConfig = {
                url: 'wss://mock-server.com/api-ws/v1/realtime',
                options: { headers: { 'Authorization': 'Bearer test-key' } },
                initializeSession: async () => true
            };

            await connectionManager.connect(wsConfig);
            await new Promise(resolve => setTimeout(resolve, 200));

            // Verify all transitions are valid
            const validTransitions = {
                'DISCONNECTED': ['CONNECTING'],
                'CONNECTING': ['CONNECTED', 'ERROR', 'DISCONNECTED'],
                'CONNECTED': ['STABILIZING', 'ERROR', 'CLOSING'],
                'STABILIZING': ['STABILIZED', 'ERROR', 'CLOSING'],
                'STABILIZED': ['VALIDATING', 'ERROR', 'CLOSING', 'READY'],
                'VALIDATING': ['READY', 'ERROR', 'CLOSING'],
                'READY': ['ERROR', 'CLOSING', 'RECONNECTING'],
                'ERROR': ['RECONNECTING', 'DISCONNECTED'],
                'RECONNECTING': ['CONNECTING', 'DISCONNECTED'],
                'CLOSING': ['DISCONNECTED']
            };

            const invalidTransitions = [];
            stateTransitions.forEach(transition => {
                const validNext = validTransitions[transition.from] || [];
                if (!validNext.includes(transition.to)) {
                    invalidTransitions.push(transition);
                }
            });

            expect(invalidTransitions.length).to.equal(0,
                `Found invalid state transitions: ${JSON.stringify(invalidTransitions)}`);

            // Check for expected progression
            const states = stateTransitions.map(t => t.to);
            expect(states).to.include('CONNECTED', 'Should reach CONNECTED state');
            expect(states).to.include.oneOf(['STABILIZED', 'READY'], 'Should reach STABILIZED or READY state');
        });
    });

    describe('6. Recovery Mechanism Efficiency', function () {
        it('should recover from connection drops efficiently', async function () {
            connectionManager = await ConnectionManager.getInstance();

            const wsConfig = {
                url: 'wss://mock-server.com/api-ws/v1/realtime',
                options: { headers: { 'Authorization': 'Bearer test-key' } },
                initializeSession: async () => true
            };

            // Initial connection
            const initialConnected = await connectionManager.connect(wsConfig);
            expect(initialConnected).to.be.true;

            // Simulate connection drop
            const socket = connectionManager._socket;
            if (socket) {
                socket.close(1006, 'Connection dropped');
            }

            await new Promise(resolve => setTimeout(resolve, 100));

            // Check recovery behavior
            const recoveryStartTime = Date.now();

            // Listen for recovery completion
            const recoveryPromise = new Promise(resolve => {
                const checkRecovery = () => {
                    if (connectionManager.isReady && connectionManager.isReady()) {
                        resolve(Date.now() - recoveryStartTime);
                    } else if (Date.now() - recoveryStartTime > 10000) {
                        resolve(null); // Timeout
                    } else {
                        setTimeout(checkRecovery, 100);
                    }
                };
                setTimeout(checkRecovery, 100);
            });

            const recoveryTime = await recoveryPromise;

            if (recoveryTime !== null) {
                expect(recoveryTime).to.be.below(5000,
                    `Recovery took ${recoveryTime}ms, should be <5s for efficient recovery`);

                logger.info('Recovery completed successfully', {
                    recoveryTime: `${recoveryTime}ms`
                });
            } else {
                logger.warn('Recovery did not complete within timeout - may be expected if recovery is not automatic');
            }
        });

        it('should use exponential backoff for retry attempts', async function () {
            // Mock WebSocket that fails multiple times before succeeding
            let connectionAttempts = 0;
            class RetryWebSocket extends MockWebSocket {
                constructor(url, protocols) {
                    super(url, protocols);
                    connectionAttempts++;

                    if (connectionAttempts <= 3) {
                        // Fail first 3 attempts
                        setTimeout(() => {
                            this.readyState = MockWebSocket.CLOSED;
                            this._triggerEvent('error', new Error(`Connection attempt ${connectionAttempts} failed`));
                            this._triggerEvent('close', { code: 1006, reason: 'Retry test' });
                        }, 50);
                    } else {
                        // Succeed on 4th attempt
                        setTimeout(() => {
                            this.readyState = MockWebSocket.OPEN;
                            this._triggerEvent('open');
                        }, 50);
                    }
                }
            }

            const originalWebSocket = global.WebSocket;
            global.WebSocket = RetryWebSocket;

            try {
                connectionManager = await ConnectionManager.getInstance({
                    baseConnectionTimeout: 500,
                    maxConnectionTimeout: 2000
                });

                const retryTimes = [];
                connectionManager.on('stateChange', (newState, oldState) => {
                    if (newState === 'RECONNECTING') {
                        retryTimes.push(Date.now());
                    }
                });

                const wsConfig = {
                    url: 'wss://mock-server.com/api-ws/v1/realtime',
                    options: { headers: { 'Authorization': 'Bearer test-key' } }
                };

                const startTime = Date.now();
                const connected = await connectionManager.connect(wsConfig);
                const totalTime = Date.now() - startTime;

                expect(connected).to.be.true;
                expect(connectionAttempts).to.be.at.least(2, 'Should have attempted multiple connections');

                // Check that retry delays increased (exponential backoff)
                if (retryTimes.length >= 2) {
                    const firstDelay = retryTimes[1] - retryTimes[0];
                    const secondDelay = retryTimes.length > 2 ? retryTimes[2] - retryTimes[1] : firstDelay * 2;

                    expect(secondDelay).to.be.at.least(firstDelay,
                        'Second retry delay should be >= first delay (exponential backoff)');
                }

                logger.info('Retry mechanism test completed', {
                    totalAttempts: connectionAttempts,
                    totalTime: `${totalTime}ms`,
                    retryDelays: retryTimes.map((time, i) =>
                        i > 0 ? `${time - retryTimes[i - 1]}ms` : '0ms'
                    )
                });

            } finally {
                global.WebSocket = originalWebSocket;
            }
        });
    });

    describe('7. Production Error Scenarios', function () {
        it('should not throw "setImmediate is not defined" errors', async function () {
            const originalSetImmediate = global.setImmediate;
            delete global.setImmediate; // Remove setImmediate to simulate browser environment

            try {
                connectionManager = await ConnectionManager.getInstance();

                const wsConfig = {
                    url: 'wss://mock-server.com/api-ws/v1/realtime',
                    options: { headers: { 'Authorization': 'Bearer test-key' } },
                    initializeSession: async () => true
                };

                // This should not throw "setImmediate is not defined"
                const connected = await connectionManager.connect(wsConfig);
                expect(connected).to.be.true;

                // Test message sending without setImmediate
                const sent = connectionManager.send({ type: 'test.message' });
                expect(sent).to.be.true;

            } finally {
                if (originalSetImmediate) {
                    global.setImmediate = originalSetImmediate;
                }
            }
        });

        it('should handle malformed WebSocket messages gracefully', async function () {
            connectionManager = await ConnectionManager.getInstance();

            const wsConfig = {
                url: 'wss://mock-server.com/api-ws/v1/realtime',
                options: { headers: { 'Authorization': 'Bearer test-key' } },
                initializeSession: async () => true
            };

            await connectionManager.connect(wsConfig);

            const socket = connectionManager._socket;
            const errorsEmitted = [];

            connectionManager.on('messageError', (error) => {
                errorsEmitted.push(error);
            });

            // Send malformed messages that should be handled gracefully
            const malformedMessages = [
                'invalid json{',
                null,
                undefined,
                { circular: {} },
                '',
                '{"incomplete": '
            ];

            malformedMessages.forEach((msg, index) => {
                try {
                    if (typeof msg === 'object' && msg !== null && msg !== undefined) {
                        msg.circular.self = msg.circular; // Create circular reference
                    }

                    socket._triggerEvent('message', { data: typeof msg === 'string' ? msg : JSON.stringify(msg) });
                } catch (error) {
                    // Expected for circular references
                }
            });

            await new Promise(resolve => setTimeout(resolve, 100));

            // Should handle malformed messages without crashing
            expect(errorsEmitted.length).to.be.at.most(malformedMessages.length,
                'Should emit reasonable number of message errors');

            // Connection should still be functional
            expect(connectionManager.getConnectionState()).to.not.equal('ERROR');
        });
    });
});

describe('Production Readiness Assessment', function () {
    let assessmentResults = {
        connectionReliability: 0,
        timeoutOptimization: 0,
        errorHandling: 0,
        stateIntegrity: 0,
        recoveryEfficiency: 0,
        overallScore: 0,
        readyForProduction: false
    };

    after(function () {
        // Calculate overall production readiness score
        const scores = Object.values(assessmentResults).filter(v => typeof v === 'number' && v > 0);
        const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;

        assessmentResults.overallScore = Math.round(avgScore);
        assessmentResults.readyForProduction = avgScore >= 85; // 85% threshold for production readiness

        console.log('\n🎯 PRODUCTION READINESS ASSESSMENT RESULTS:');
        console.log('===============================================');
        console.log(`📊 Connection Reliability: ${assessmentResults.connectionReliability}%`);
        console.log(`⚡ Timeout Optimization: ${assessmentResults.timeoutOptimization}%`);
        console.log(`🛡️ Error Handling: ${assessmentResults.errorHandling}%`);
        console.log(`🔄 State Integrity: ${assessmentResults.stateIntegrity}%`);
        console.log(`🔧 Recovery Efficiency: ${assessmentResults.recoveryEfficiency}%`);
        console.log(`===============================================`);
        console.log(`🎯 OVERALL SCORE: ${assessmentResults.overallScore}%`);
        console.log(`🚀 PRODUCTION READY: ${assessmentResults.readyForProduction ? '✅ YES' : '❌ NO'}`);

        if (assessmentResults.readyForProduction) {
            console.log('\n✅ SYSTEM VALIDATED FOR PRODUCTION DEPLOYMENT');
            console.log('Key improvements verified:');
            console.log('  - Connection establishment >95% reliable');
            console.log('  - Adaptive timeouts eliminate 10s waits');
            console.log('  - Error cascades prevented');
            console.log('  - State transitions validated');
            console.log('  - Recovery mechanisms efficient');
        } else {
            console.log('\n❌ SYSTEM NOT READY FOR PRODUCTION');
            console.log('Areas requiring attention:');
            if (assessmentResults.connectionReliability < 85) console.log('  - Connection reliability needs improvement');
            if (assessmentResults.timeoutOptimization < 85) console.log('  - Timeout optimization needs improvement');
            if (assessmentResults.errorHandling < 85) console.log('  - Error handling needs improvement');
            if (assessmentResults.stateIntegrity < 85) console.log('  - State integrity needs improvement');
            if (assessmentResults.recoveryEfficiency < 85) console.log('  - Recovery efficiency needs improvement');
        }
    });
});