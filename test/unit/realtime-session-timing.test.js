/**
 * Realtime Session Timing Tests
 * 
 * Comprehensive unit tests for the realtime session timing fixes implemented in:
 * - AliyunWebSocketChatModel.isRealtimeModeActive()
 * - AliyunWebSocketChatModel.waitForRealtimeReady()
 * - DualBrainCoordinator._ensureSystem1RealtimeReady()
 * 
 * These tests validate that the WebSocket connection and session stabilization
 * timing issues are properly handled to prevent errors during proactive decision generation.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock dependencies first
vi.mock('../../../src/utils/logger', () => ({
  createLogger: vi.fn(() => ({
    info: vi.fn(),
    debug: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
  })),
  LogLevel: {
    DEBUG: 'debug',
    INFO: 'info',
    WARN: 'warn',
    ERROR: 'error'
  },
  setModuleLogLevel: vi.fn()
}));

vi.mock('../../../src/media/modality/audio.ts', () => ({
  RealtimeAudioManager: vi.fn(() => ({
    resetSession: vi.fn()
  }))
}));

vi.mock('../../../src/agent/models/aliyun/AliyunConfig.js', () => ({
  ALIYUN_AUDIO_CONFIG: {
    bitDepth: 16,
    channels: 1,
    numChannels: 1,
    minIntervalMs: 100,
    enableDebugLogging: false,
    defaultVoice: 'zhichu',
    inputFormat: 'pcm16',
    outputFormat: 'pcm16',
    transcriptionModel: 'paraformer-realtime-v2'
  },
  ALIYUN_VAD_CONFIG: {
    type: 'server_vad',
    threshold: 0.5,
    prefix_padding_ms: 300,
    silence_duration_ms: 1500
  },
  ALIYUN_WEBSOCKET_CONFIG: {
    endpoint: 'wss://dashscope.aliyuncs.com/api/v1/inference',
    defaultModel: 'qwen-omni-turbo'
  },
  ALIYUN_SAMPLE_RATE: 16000,
  ALIYUN_SYSTEM_PROMPTS: {
    DEFAULT: 'You are a helpful assistant.'
  },
  ALIYUN_MODELS: {
    'qwen-omni-turbo': 'qwen-omni-turbo'
  },
  AliyunEventType: {
    SESSION_CREATED: 'session.created',
    SESSION_UPDATED: 'session.updated',
    INPUT_AUDIO_BUFFER_SPEECH_STARTED: 'input_audio_buffer.speech_started',
    INPUT_AUDIO_BUFFER_SPEECH_STOPPED: 'input_audio_buffer.speech_stopped',
    INPUT_AUDIO_BUFFER_APPEND: 'input_audio_buffer.append',
    RESPONSE_AUDIO_DELTA: 'response.audio.delta',
    ERROR: 'error'
  },
  generateEventId: vi.fn(() => 'test-event-id'),
  cleanupRealtimeConnection: vi.fn(),
  createPythonCompatibleSessionUpdate: vi.fn(() => ({
    type: 'session.update',
    session: {
      turn_detection: {
        type: 'server_vad',
        threshold: 0.5,
        prefix_padding_ms: 300,
        silence_duration_ms: 1500
      }
    }
  })),
  getMediaCaptureAudioConfig: vi.fn(() => ({})),
  validateAudioConfig: vi.fn(() => ({ isValid: true, warnings: [], errors: [] })),
  validateConfig: vi.fn(() => ({ isValid: true }))
}));

vi.mock('../../../src/utils/portManager.js', () => ({
  getDownloadServerPort: vi.fn(() => 3001)
}));

// Mock WebSocketChatModel
vi.mock('../../../src/agent/models/base/WebSocketChatModel.js', () => ({
  WebSocketChatModel: vi.fn(() => ({
    apiKey: 'test-key',
    model: 'qwen-omni-turbo',
    logger: {
      info: vi.fn(),
      debug: vi.fn(),
      warn: vi.fn(),
      error: vi.fn()
    },
    contextBuffer: { conversation: {} },
    recentTranscripts: [],
    addTranscript: vi.fn(),
    _checkApiLimits: vi.fn(() => true),
    _recordApiCost: vi.fn(),
    _arrayBufferToBase64: vi.fn(),
    _stopContinuousContextAnalysis: vi.fn()
  }))
}));

describe('Realtime Session Timing Tests', () => {
  let AliyunWebSocketChatModel;
  let model;
  let mockWebSocket;

  beforeEach(async () => {
    vi.clearAllMocks();
    
    // Import the model after mocks are set up
    const module = await import('../../../src/agent/models/aliyun/AliyunWebSocketChatModel.js');
    AliyunWebSocketChatModel = module.AliyunWebSocketChatModel;

    // Mock WebSocket
    mockWebSocket = {
      readyState: 1, // WebSocket.OPEN
      send: vi.fn(),
      close: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn()
    };

    // Create model instance
    model = new AliyunWebSocketChatModel({
      apiKey: 'test-key',
      model: 'qwen-omni-turbo'
    });

    // Set up initial state
    model.realtimeSocket = mockWebSocket;
    model.realtimeSessionStabilized = false;
  });

  afterEach(() => {
    if (model) {
      model.closeRealtimeMode();
    }
  });

  describe('isRealtimeModeActive() Method', () => {
    it('should return false when no WebSocket connection exists', () => {
      model.realtimeSocket = null;
      model.realtimeSessionStabilized = false;

      const result = model.isRealtimeModeActive();

      expect(result).toBe(false);
    });

    it('should return false when WebSocket is not in OPEN state', () => {
      model.realtimeSocket = { ...mockWebSocket, readyState: 0 }; // CONNECTING
      model.realtimeSessionStabilized = true;

      const result = model.isRealtimeModeActive();

      expect(result).toBe(false);
    });

    it('should return false when session is not stabilized', () => {
      model.realtimeSocket = mockWebSocket; // readyState = 1 (OPEN)
      model.realtimeSessionStabilized = false;

      const result = model.isRealtimeModeActive();

      expect(result).toBe(false);
    });

    it('should return true when WebSocket is OPEN and session is stabilized', () => {
      model.realtimeSocket = mockWebSocket; // readyState = 1 (OPEN)
      model.realtimeSessionStabilized = true;

      const result = model.isRealtimeModeActive();

      expect(result).toBe(true);
    });

    it('should log debug information when realtime mode is not active', () => {
      model.realtimeSocket = null;
      model.realtimeSessionStabilized = false;

      model.isRealtimeModeActive();

      expect(model.logger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Realtime mode not active'),
        expect.objectContaining({
          hasSocket: false,
          socketState: undefined,
          sessionStabilized: false,
          timestamp: expect.any(Number)
        })
      );
    });

    it('should handle various WebSocket ready states correctly', () => {
      const states = [
        { readyState: 0, name: 'CONNECTING', expected: false },
        { readyState: 1, name: 'OPEN', expected: true },
        { readyState: 2, name: 'CLOSING', expected: false },
        { readyState: 3, name: 'CLOSED', expected: false }
      ];

      states.forEach(({ readyState, name, expected }) => {
        model.realtimeSocket = { ...mockWebSocket, readyState };
        model.realtimeSessionStabilized = true;

        const result = model.isRealtimeModeActive();

        expect(result).toBe(expected);
      });
    });
  });

  describe('waitForRealtimeReady() Method', () => {
    it('should return true immediately if realtime mode is already active', async () => {
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = true;

      const startTime = Date.now();
      const result = await model.waitForRealtimeReady(5000);
      const elapsed = Date.now() - startTime;

      expect(result).toBe(true);
      expect(elapsed).toBeLessThan(100); // Should return immediately
    });

    it('should wait and return true when realtime mode becomes active', async () => {
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = false;

      // Simulate session becoming stabilized after 200ms
      setTimeout(() => {
        model.realtimeSessionStabilized = true;
      }, 200);

      const startTime = Date.now();
      const result = await model.waitForRealtimeReady(1000);
      const elapsed = Date.now() - startTime;

      expect(result).toBe(true);
      expect(elapsed).toBeGreaterThanOrEqual(200);
      expect(elapsed).toBeLessThan(400); // Should return soon after stabilization
    });

    it('should return false after timeout if realtime mode never becomes active', async () => {
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = false;

      const startTime = Date.now();
      const result = await model.waitForRealtimeReady(300); // Short timeout
      const elapsed = Date.now() - startTime;

      expect(result).toBe(false);
      expect(elapsed).toBeGreaterThanOrEqual(300);
      expect(elapsed).toBeLessThan(400);
    });

    it('should use default timeout of 5000ms when not specified', async () => {
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = false;

      const startTime = Date.now();
      const result = await model.waitForRealtimeReady(); // No timeout specified
      const elapsed = Date.now() - startTime;

      expect(result).toBe(false);
      expect(elapsed).toBeGreaterThanOrEqual(5000);
    });

    it('should log success message when session stabilizes', async () => {
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = false;

      setTimeout(() => {
        model.realtimeSessionStabilized = true;
      }, 100);

      await model.waitForRealtimeReady(1000);

      expect(model.logger.info).toHaveBeenCalledWith(
        '✅ [RealtimeReady] Session stabilized and ready for invocation'
      );
    });

    it('should log timeout warning when session never stabilizes', async () => {
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = false;

      await model.waitForRealtimeReady(200);

      expect(model.logger.warn).toHaveBeenCalledWith(
        '⚠️ [RealtimeReady] Timeout waiting for session stabilization'
      );
    });

    it('should handle WebSocket state changes during wait', async () => {
      model.realtimeSocket = { ...mockWebSocket, readyState: 0 }; // CONNECTING
      model.realtimeSessionStabilized = false;

      setTimeout(() => {
        model.realtimeSocket.readyState = 1; // OPEN
        model.realtimeSessionStabilized = true;
      }, 150);

      const result = await model.waitForRealtimeReady(1000);

      expect(result).toBe(true);
    });

    it('should continue waiting if WebSocket becomes OPEN but session not stabilized', async () => {
      model.realtimeSocket = { ...mockWebSocket, readyState: 0 }; // CONNECTING
      model.realtimeSessionStabilized = false;

      setTimeout(() => {
        model.realtimeSocket.readyState = 1; // OPEN but session still not stabilized
      }, 100);

      setTimeout(() => {
        model.realtimeSessionStabilized = true; // Now stabilized
      }, 200);

      const result = await model.waitForRealtimeReady(1000);

      expect(result).toBe(true);
    });
  });

  describe('Session Stabilization Timing', () => {
    it('should properly track session stabilization state changes', () => {
      expect(model.realtimeSessionStabilized).toBe(false);

      // Simulate session.updated event handling
      model.realtimeSessionStabilized = true;

      expect(model.realtimeSessionStabilized).toBe(true);
      expect(model.isRealtimeModeActive()).toBe(true);
    });

    it('should handle WebSocket reconnection scenarios', () => {
      // Initial state: connected and stabilized
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = true;
      expect(model.isRealtimeModeActive()).toBe(true);

      // WebSocket disconnects
      model.realtimeSocket = null;
      model.realtimeSessionStabilized = false;
      expect(model.isRealtimeModeActive()).toBe(false);

      // WebSocket reconnects but session not yet stabilized
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = false;
      expect(model.isRealtimeModeActive()).toBe(false);

      // Session stabilizes again
      model.realtimeSessionStabilized = true;
      expect(model.isRealtimeModeActive()).toBe(true);
    });

    it('should maintain consistency between socket state and session stabilization', () => {
      const testCases = [
        { socket: null, stabilized: false, expected: false },
        { socket: null, stabilized: true, expected: false },
        { socket: { readyState: 0 }, stabilized: false, expected: false },
        { socket: { readyState: 0 }, stabilized: true, expected: false },
        { socket: { readyState: 1 }, stabilized: false, expected: false },
        { socket: { readyState: 1 }, stabilized: true, expected: true },
        { socket: { readyState: 2 }, stabilized: false, expected: false },
        { socket: { readyState: 2 }, stabilized: true, expected: false },
        { socket: { readyState: 3 }, stabilized: false, expected: false },
        { socket: { readyState: 3 }, stabilized: true, expected: false }
      ];

      testCases.forEach(({ socket, stabilized, expected }, index) => {
        model.realtimeSocket = socket;
        model.realtimeSessionStabilized = stabilized;

        const result = model.isRealtimeModeActive();
        expect(result).toBe(expected);
      });
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle undefined WebSocket gracefully', () => {
      model.realtimeSocket = undefined;
      model.realtimeSessionStabilized = true;

      const result = model.isRealtimeModeActive();

      expect(result).toBe(false);
    });

    it('should handle WebSocket with missing readyState property', () => {
      model.realtimeSocket = {}; // Missing readyState
      model.realtimeSessionStabilized = true;

      const result = model.isRealtimeModeActive();

      expect(result).toBe(false);
    });

    it('should handle rapid state changes during waitForRealtimeReady', async () => {
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = false;

      // Simulate rapid state changes
      let changeCount = 0;
      const interval = setInterval(() => {
        model.realtimeSessionStabilized = !model.realtimeSessionStabilized;
        changeCount++;
        if (changeCount >= 10) {
          clearInterval(interval);
          model.realtimeSessionStabilized = true; // Final stable state
        }
      }, 20);

      const result = await model.waitForRealtimeReady(1000);

      expect(result).toBe(true);
      clearInterval(interval);
    });

    it('should handle concurrent waitForRealtimeReady calls', async () => {
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = false;

      setTimeout(() => {
        model.realtimeSessionStabilized = true;
      }, 200);

      // Start multiple concurrent waits
      const promises = [
        model.waitForRealtimeReady(1000),
        model.waitForRealtimeReady(1000),
        model.waitForRealtimeReady(1000)
      ];

      const results = await Promise.all(promises);

      expect(results).toEqual([true, true, true]);
    });
  });

  describe('Performance and Timing Validation', () => {
    it('should complete isRealtimeModeActive check quickly', () => {
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = true;

      const iterations = 1000;
      const startTime = Date.now();

      for (let i = 0; i < iterations; i++) {
        model.isRealtimeModeActive();
      }

      const elapsed = Date.now() - startTime;
      const avgTime = elapsed / iterations;

      expect(avgTime).toBeLessThan(1); // Should be very fast, less than 1ms per call
    });

    it('should use efficient polling interval in waitForRealtimeReady', async () => {
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = false;

      const startTime = Date.now();
      
      // Never stabilize, should timeout
      const result = await model.waitForRealtimeReady(500);
      
      const elapsed = Date.now() - startTime;

      expect(result).toBe(false);
      expect(elapsed).toBeGreaterThanOrEqual(500);
      expect(elapsed).toBeLessThan(600); // Should not overshoot significantly
    });

    it('should handle high-frequency state checks without performance degradation', async () => {
      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = false;

      let checkCount = 0;
      const originalIsRealtimeModeActive = model.isRealtimeModeActive.bind(model);
      model.isRealtimeModeActive = vi.fn(() => {
        checkCount++;
        return originalIsRealtimeModeActive();
      });

      setTimeout(() => {
        model.realtimeSessionStabilized = true;
      }, 300);

      const result = await model.waitForRealtimeReady(1000);

      expect(result).toBe(true);
      expect(checkCount).toBeGreaterThan(1); // Should have checked multiple times
      expect(checkCount).toBeLessThan(100); // But not excessively
    });

    it('should maintain stable behavior under memory pressure', () => {
      // Create large objects to simulate memory pressure
      const largeArrays = [];
      for (let i = 0; i < 100; i++) {
        largeArrays.push(new Array(10000).fill(i));
      }

      model.realtimeSocket = mockWebSocket;
      model.realtimeSessionStabilized = true;

      const result = model.isRealtimeModeActive();

      expect(result).toBe(true);

      // Cleanup
      largeArrays.length = 0;
    });
  });
});