/**
 * DualBrainCoordinator Timing Tests
 * 
 * Unit tests for the DualBrainCoordinator._ensureSystem1RealtimeReady() method
 * and related timing validation that prevents WebSocket invocation errors
 * during proactive decision generation.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock logger
vi.mock('../../../src/utils/logger.js', () => ({
  createLogger: vi.fn(() => ({
    info: vi.fn(),
    debug: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
  }))
}));

describe('DualBrainCoordinator Timing Tests', () => {
  let coordinator;
  let mockAgentService;
  let mockSystem1Model;
  let mockSystem2Model;

  beforeEach(async () => {
    vi.clearAllMocks();

    // Create mock models
    mockSystem1Model = {
      constructor: { name: 'AliyunWebSocketChatModel' },
      isRealtimeModeActive: vi.fn(() => false),
      waitForRealtimeReady: vi.fn(() => Promise.resolve(true)),
      apiMode: 'websocket'
    };

    mockSystem2Model = {
      constructor: { name: 'AliyunHttpChatModel' },
      apiMode: 'http'
    };

    // Create mock agent service
    mockAgentService = {
      getModel: vi.fn((type) => {
        if (type === 'system1') return mockSystem1Model;
        if (type === 'system2') return mockSystem2Model;
        return null;
      }),
      isDualBrainMode: vi.fn(() => true),
      generateResponse: vi.fn(() => Promise.resolve('Mock response')),
      options: {
        agentConfig: {
          enableDualBrain: true
        }
      }
    };

    // Import the coordinator factory function after mocks are set up
    const module = await import('../../src/agent/arch/dualbrain/DualBrainCoordinator.js');
    const createDualBrainCoordinator = module.createDualBrainCoordinator;

    // Create coordinator instance using factory function
    coordinator = createDualBrainCoordinator(
      { agentService: mockAgentService },
      {
        system2AnalysisInterval: 1000,
        decisionCooldown: 2000,
        enableProactiveDecisions: true
      }
    );

    // Mark as initialized
    coordinator.isInitialized = true;
  });

  afterEach(async () => {
    if (coordinator && coordinator.isActive) {
      await coordinator.stopDualBrainSystems();
    }
  });

  describe('_ensureSystem1RealtimeReady() Method', () => {
    it('should return early if System 1 model is not available', async () => {
      mockAgentService.getModel.mockReturnValue(null);

      await coordinator._ensureSystem1RealtimeReady();

      expect(coordinator.logger.warn).toHaveBeenCalledWith(
        '⚠️ System 1 model not available - dual brain functionality will be limited'
      );
    });

    it('should skip readiness check for non-realtime models', async () => {
      mockSystem1Model.isRealtimeModeActive = undefined;

      await coordinator._ensureSystem1RealtimeReady();

      expect(coordinator.logger.debug).toHaveBeenCalledWith(
        'ℹ️ System 1 model does not support realtime mode - skipping readiness check'
      );
    });

    it('should return immediately if realtime mode is already active', async () => {
      mockSystem1Model.isRealtimeModeActive.mockReturnValue(true);

      const startTime = Date.now();
      await coordinator._ensureSystem1RealtimeReady();
      const elapsed = Date.now() - startTime;

      expect(elapsed).toBeLessThan(100);
      expect(coordinator.logger.info).toHaveBeenCalledWith(
        '✅ System 1 realtime mode already active and ready'
      );
    });

    it('should wait for realtime mode using waitForRealtimeReady method', async () => {
      mockSystem1Model.isRealtimeModeActive.mockReturnValue(false);
      mockSystem1Model.waitForRealtimeReady.mockResolvedValue(true);

      await coordinator._ensureSystem1RealtimeReady();

      expect(mockSystem1Model.waitForRealtimeReady).toHaveBeenCalledWith(10000);
      expect(coordinator.logger.info).toHaveBeenCalledWith(
        '✅ System 1 realtime mode is now active and ready'
      );
    });

    it('should handle timeout from waitForRealtimeReady gracefully', async () => {
      mockSystem1Model.isRealtimeModeActive.mockReturnValue(false);
      mockSystem1Model.waitForRealtimeReady.mockResolvedValue(false);

      await coordinator._ensureSystem1RealtimeReady();

      expect(coordinator.logger.warn).toHaveBeenCalledWith(
        '⚠️ Timeout waiting for System 1 realtime mode to become active'
      );
      expect(coordinator.logger.warn).toHaveBeenCalledWith(
        '⚠️ Dual brain periodic analysis may fail until realtime mode is properly initialized'
      );
    });

    it('should fall back to polling if waitForRealtimeReady is not available', async () => {
      mockSystem1Model.isRealtimeModeActive.mockReturnValue(false);
      mockSystem1Model.waitForRealtimeReady = undefined;

      // Mock the polling behavior - first few calls return false, then true
      let callCount = 0;
      mockSystem1Model.isRealtimeModeActive.mockImplementation(() => {
        callCount++;
        return callCount > 3; // Becomes active after 3 polls
      });

      const startTime = Date.now();
      await coordinator._ensureSystem1RealtimeReady();
      const elapsed = Date.now() - startTime;

      expect(elapsed).toBeGreaterThan(1500); // Should have polled multiple times
      expect(coordinator.logger.info).toHaveBeenCalledWith(
        '✅ System 1 realtime mode is now active and ready'
      );
    });

    it('should timeout during fallback polling if realtime mode never becomes active', async () => {
      mockSystem1Model.isRealtimeModeActive.mockReturnValue(false);
      mockSystem1Model.waitForRealtimeReady = undefined;

      const startTime = Date.now();
      await coordinator._ensureSystem1RealtimeReady();
      const elapsed = Date.now() - startTime;

      expect(elapsed).toBeGreaterThanOrEqual(10000); // Should wait full timeout
      expect(coordinator.logger.warn).toHaveBeenCalledWith(
        '⚠️ Timeout waiting for System 1 realtime mode to become active'
      );
    });

    it('should handle errors during readiness check gracefully', async () => {
      mockSystem1Model.isRealtimeModeActive.mockImplementation(() => {
        throw new Error('WebSocket error');
      });

      await coordinator._ensureSystem1RealtimeReady();

      expect(coordinator.logger.error).toHaveBeenCalledWith(
        '❌ Error ensuring System 1 realtime readiness:',
        expect.any(Error)
      );
      expect(coordinator.logger.warn).toHaveBeenCalledWith(
        '⚠️ Continuing with dual brain startup - periodic analysis may fail initially'
      );
    });

    it('should log model type for debugging', async () => {
      mockSystem1Model.isRealtimeModeActive.mockReturnValue(false);
      mockSystem1Model.waitForRealtimeReady.mockResolvedValue(true);

      await coordinator._ensureSystem1RealtimeReady();

      expect(coordinator.logger.debug).toHaveBeenCalledWith(
        '🔍 System 1 model type:',
        'AliyunWebSocketChatModel'
      );
    });

    it('should properly log realtime mode status', async () => {
      mockSystem1Model.isRealtimeModeActive.mockReturnValue(false);
      mockSystem1Model.waitForRealtimeReady.mockResolvedValue(true);

      await coordinator._ensureSystem1RealtimeReady();

      expect(coordinator.logger.debug).toHaveBeenCalledWith(
        '🔍 System 1 realtime mode status:',
        {
          isActive: false,
          modelType: 'AliyunWebSocketChatModel'
        }
      );
    });
  });

  describe('startDualBrainSystems() Integration', () => {
    it('should call _ensureSystem1RealtimeReady before starting periodic analysis', async () => {
      mockSystem1Model.isRealtimeModeActive.mockReturnValue(true);
      
      const ensureReadySpy = vi.spyOn(coordinator, '_ensureSystem1RealtimeReady');
      const startPeriodicSpy = vi.spyOn(coordinator, '_startPeriodicAnalysis');

      await coordinator.startDualBrainSystems();

      expect(ensureReadySpy).toHaveBeenCalled();
      expect(startPeriodicSpy).toHaveBeenCalled();
      
      // Ensure _ensureSystem1RealtimeReady was called before _startPeriodicAnalysis
      const ensureCall = ensureReadySpy.mock.invocationCallOrder[0];
      const startCall = startPeriodicSpy.mock.invocationCallOrder[0];
      expect(ensureCall).toBeLessThan(startCall);
    });

    it('should handle readiness check failure gracefully during startup', async () => {
      mockSystem1Model.isRealtimeModeActive.mockReturnValue(false);
      mockSystem1Model.waitForRealtimeReady.mockRejectedValue(new Error('Connection failed'));

      const result = await coordinator.startDualBrainSystems();

      expect(result).toBe(true); // Should still start successfully
      expect(coordinator.isActive).toBe(true);
      expect(coordinator.logger.error).toHaveBeenCalledWith(
        '❌ Error ensuring System 1 realtime readiness:',
        expect.any(Error)
      );
    });

    it('should not start if not initialized', async () => {
      coordinator.isInitialized = false;

      const result = await coordinator.startDualBrainSystems();

      expect(result).toBe(false);
      expect(coordinator.logger.error).toHaveBeenCalledWith(
        '❌ Cannot start - dual brain not initialized'
      );
    });

    it('should not start if already active', async () => {
      coordinator.isActive = true;

      const result = await coordinator.startDualBrainSystems();

      expect(result).toBe(true);
      expect(coordinator.logger.warn).toHaveBeenCalledWith(
        '⚠️ Dual brain systems already active'
      );
    });
  });

  describe('System 1 Invocation Protection', () => {
    it('should check realtime readiness before invoking System 1', async () => {
      mockSystem1Model.isRealtimeModeActive.mockReturnValue(false);

      await expect(coordinator._invokeSystem1('test input')).rejects.toThrow(
        'System 1 realtime mode not active - cannot invoke WebSocket model'
      );

      expect(coordinator.logger.warn).toHaveBeenCalledWith(
        '⚠️ System 1 realtime mode not active - cannot invoke WebSocket model'
      );
    });

    it('should successfully invoke System 1 when realtime mode is active', async () => {
      mockSystem1Model.isRealtimeModeActive.mockReturnValue(true);

      const result = await coordinator._invokeSystem1('test input');

      expect(result).toBe('Mock response');
      expect(mockAgentService.generateResponse).toHaveBeenCalledWith(
        'test input',
        expect.objectContaining({
          modelOverride: mockSystem1Model,
          priority: 'speed'
        })
      );
    });

    it('should handle System 1 models without realtime support', async () => {
      mockSystem1Model.isRealtimeModeActive = undefined;

      const result = await coordinator._invokeSystem1('test input');

      expect(result).toBe('Mock response');
      expect(mockAgentService.generateResponse).toHaveBeenCalled();
    });

    it('should provide detailed debug information on readiness check failure', async () => {
      mockSystem1Model.isRealtimeModeActive.mockReturnValue(false);

      await expect(coordinator._invokeSystem1('test input')).rejects.toThrow();

      expect(coordinator.logger.debug).toHaveBeenCalledWith(
        '🔍 System 1 readiness check:',
        {
          hasModel: true,
          modelType: 'AliyunWebSocketChatModel',
          hasRealtimeCheck: true,
          isRealtimeActive: false
        }
      );
    });
  });

  describe('Proactive Decision Timing', () => {
    it('should ensure realtime readiness before generating proactive decisions', async () => {
      mockSystem1Model.isRealtimeModeActive.mockReturnValue(true);
      coordinator.isActive = true;
      coordinator.lastDecisionTime = 0; // No cooldown

      const result = await coordinator.generateProactiveDecision();

      expect(result.shouldAct).toBeDefined();
      expect(mockAgentService.generateResponse).toHaveBeenCalled();
    });

    it('should handle proactive decision generation when System 1 is not ready', async () => {
      mockSystem1Model.isRealtimeModeActive.mockReturnValue(false);
      coordinator.isActive = true;
      coordinator.lastDecisionTime = 0;

      // Mock System 2 response that might trigger System 1 usage
      mockAgentService.generateResponse.mockResolvedValue(JSON.stringify({
        shouldAct: true,
        shouldSpeak: true,
        confidence: 0.8,
        reason: 'Test reason'
      }));

      const result = await coordinator.generateProactiveDecision();

      expect(result.shouldAct).toBe(true);
    });

    it('should respect decision cooldown to prevent timing issues', async () => {
      coordinator.isActive = true;
      coordinator.lastDecisionTime = Date.now(); // Just made a decision

      const result = await coordinator.generateProactiveDecision();

      expect(result.shouldAct).toBe(false);
      expect(result.reason).toBe('decision_cooldown');
      expect(result.cooldownRemaining).toBeGreaterThan(0);
    });
  });

  describe('Performance and Resource Timing', () => {
    it('should complete readiness check quickly when already active', async () => {
      mockSystem1Model.isRealtimeModeActive.mockReturnValue(true);

      const startTime = Date.now();
      await coordinator._ensureSystem1RealtimeReady();
      const elapsed = Date.now() - startTime;

      expect(elapsed).toBeLessThan(50); // Should be very fast
    });

    it('should handle concurrent readiness checks without race conditions', async () => {
      mockSystem1Model.isRealtimeModeActive.mockReturnValue(false);
      mockSystem1Model.waitForRealtimeReady.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve(true), 100))
      );

      const promises = [
        coordinator._ensureSystem1RealtimeReady(),
        coordinator._ensureSystem1RealtimeReady(),
        coordinator._ensureSystem1RealtimeReady()
      ];

      const results = await Promise.allSettled(promises);

      expect(results.every(result => result.status === 'fulfilled')).toBe(true);
    });

    it('should handle memory pressure during readiness checks', async () => {
      // Create memory pressure
      const largeObjects = [];
      for (let i = 0; i < 1000; i++) {
        largeObjects.push(new Array(1000).fill(i));
      }

      mockSystem1Model.isRealtimeModeActive.mockReturnValue(true);

      const result = await coordinator._ensureSystem1RealtimeReady();

      expect(result).toBeUndefined(); // Should complete without issues

      // Cleanup
      largeObjects.length = 0;
    });

    it('should maintain stable timing under high frequency checks', async () => {
      mockSystem1Model.isRealtimeModeActive.mockReturnValue(true);

      const timings = [];
      const iterations = 50;

      for (let i = 0; i < iterations; i++) {
        const startTime = Date.now();
        await coordinator._ensureSystem1RealtimeReady();
        const elapsed = Date.now() - startTime;
        timings.push(elapsed);
      }

      const avgTiming = timings.reduce((sum, time) => sum + time, 0) / timings.length;
      const maxTiming = Math.max(...timings);

      expect(avgTiming).toBeLessThan(10); // Average should be very fast
      expect(maxTiming).toBeLessThan(50); // Even worst case should be reasonable
    });
  });

  describe('Error Recovery and Resilience', () => {
    it('should recover from temporary WebSocket errors during readiness check', async () => {
      let errorCount = 0;
      mockSystem1Model.isRealtimeModeActive.mockImplementation(() => {
        errorCount++;
        if (errorCount <= 3) {
          throw new Error('Temporary WebSocket error');
        }
        return true;
      });

      await coordinator._ensureSystem1RealtimeReady();

      expect(coordinator.logger.error).toHaveBeenCalled();
      expect(coordinator.logger.warn).toHaveBeenCalledWith(
        '⚠️ Continuing with dual brain startup - periodic analysis may fail initially'
      );
    });

    it('should handle null or undefined model gracefully', async () => {
      mockAgentService.getModel.mockReturnValue(null);

      await coordinator._ensureSystem1RealtimeReady();

      expect(coordinator.logger.warn).toHaveBeenCalledWith(
        '⚠️ System 1 model not available - dual brain functionality will be limited'
      );
    });

    it('should handle models with partially implemented interfaces', async () => {
      mockSystem1Model.isRealtimeModeActive = null; // Not a function

      await coordinator._ensureSystem1RealtimeReady();

      expect(coordinator.logger.debug).toHaveBeenCalledWith(
        'ℹ️ System 1 model does not support realtime mode - skipping readiness check'
      );
    });
  });
});