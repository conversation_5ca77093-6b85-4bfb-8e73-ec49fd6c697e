/**
 * Session Modality Configuration Tests
 * 
 * Tests for session-level modality management including:
 * - Aliyun session.update event generation
 * - Modality state management throughout request lifecycle
 * - VAD configuration and audio stream processing
 * - Error handling for modality switching
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { 
  createAliyunModalityUpdate,
  determineSystem1Modalities,
  DUAL_BRAIN_CONFIG 
} from '@/agent/config/LangGraphConfig.js';

// Mock logger
vi.mock('@/utils/logger.ts', () => ({
  createLogger: () => ({
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    setLogLevel: vi.fn()
  })
}));

describe('Session Modality Configuration', () => {
  let sessionConfig;
  let modalityManager;

  beforeEach(() => {
    // Mock session configuration manager
    sessionConfig = {
      currentModalities: ['text'],
      pendingUpdate: null,
      updateQueue: [],
      isUpdating: false,
      
      updateModalities: vi.fn(async (newModalities) => {
        sessionConfig.currentModalities = newModalities.modalities;
        return { success: true, event_id: 'test_event_123' };
      }),
      
      queueUpdate: vi.fn((update) => {
        sessionConfig.updateQueue.push(update);
      }),
      
      getCurrentConfig: vi.fn(() => ({
        modalities: sessionConfig.currentModalities,
        voice: sessionConfig.currentModalities.includes('audio') ? 'Chelsie' : null
      }))
    };

    // Mock modality manager
    modalityManager = {
      determineModalityChange: vi.fn((currentModalities, toolDecision) => {
        const needsAudio = toolDecision.toolsRequired?.some(
          tool => tool.type === 'speech' || tool.name === 'control_avatar_speech'
        );
        
        if (needsAudio && !currentModalities.includes('audio')) {
          return {
            shouldChange: true,
            newModalities: ['text', 'audio'],
            reason: 'speaking_tool_activated'
          };
        }
        
        return {
          shouldChange: false,
          newModalities: currentModalities,
          reason: 'no_change_needed'
        };
      }),
      
      generateSessionUpdate: vi.fn((modalities) => {
        return createAliyunModalityUpdate(modalities);
      })
    };
  });

  describe('1. Aliyun Session Update Event Generation', () => {
    it('should generate valid session.update events for text-only modality', () => {
      const textModalities = determineSystem1Modalities(false);
      const sessionUpdate = createAliyunModalityUpdate(textModalities);

      expect(sessionUpdate).toMatchObject({
        type: 'session.update',
        event_id: expect.stringMatching(/^event_\d+_[a-z0-9]+$/),
        session: {
          modalities: ['text'],
          input_audio_format: 'pcm16',
          output_audio_format: 'pcm16',
          input_audio_transcription: {
            model: 'gummy-realtime-v1'
          },
          turn_detection: {
            type: 'server_vad',
            threshold: 0.1,
            prefix_padding_ms: 500,
            silence_duration_ms: 900
          }
        }
      });

      // Should not include voice for text-only
      expect(sessionUpdate.session.voice).toBeUndefined();
    });

    it('should generate valid session.update events for text+audio modality', () => {
      const audioModalities = determineSystem1Modalities(true);
      const sessionUpdate = createAliyunModalityUpdate(audioModalities);

      expect(sessionUpdate).toMatchObject({
        type: 'session.update',
        event_id: expect.stringMatching(/^event_\d+_[a-z0-9]+$/),
        session: {
          modalities: ['text', 'audio'],
          voice: 'Chelsie',
          input_audio_format: 'pcm16',
          output_audio_format: 'pcm16'
        }
      });

      // Should include voice for audio-enabled sessions
      expect(sessionUpdate.session.voice).toBe('Chelsie');
    });

    it('should support custom session options in updates', () => {
      const audioModalities = determineSystem1Modalities(true);
      const customOptions = {
        voice: 'CustomVoice',
        output_audio_format: 'mp3',
        custom_parameter: 'test_value'
      };

      const sessionUpdate = createAliyunModalityUpdate(audioModalities, customOptions);

      expect(sessionUpdate.session.voice).toBe('CustomVoice');
      expect(sessionUpdate.session.output_audio_format).toBe('mp3');
      expect(sessionUpdate.session.custom_parameter).toBe('test_value');
    });

    it('should generate unique event IDs for each update', () => {
      const modalities = determineSystem1Modalities(true);
      
      const update1 = createAliyunModalityUpdate(modalities);
      const update2 = createAliyunModalityUpdate(modalities);
      const update3 = createAliyunModalityUpdate(modalities);

      expect(update1.event_id).not.toBe(update2.event_id);
      expect(update2.event_id).not.toBe(update3.event_id);
      expect(update1.event_id).not.toBe(update3.event_id);

      // All should match the expected format
      const eventIdPattern = /^event_\d+_[a-z0-9]+$/;
      expect(update1.event_id).toMatch(eventIdPattern);
      expect(update2.event_id).toMatch(eventIdPattern);
      expect(update3.event_id).toMatch(eventIdPattern);
    });
  });

  describe('2. Modality State Management', () => {
    it('should track current modality state correctly', () => {
      // Start with text-only
      expect(sessionConfig.currentModalities).toEqual(['text']);

      // Update to text+audio
      sessionConfig.updateModalities({
        modalities: ['text', 'audio'],
        voice: 'Chelsie'
      });

      expect(sessionConfig.currentModalities).toEqual(['text', 'audio']);
    });

    it('should determine when modality changes are needed', () => {
      const currentModalities = ['text'];
      
      // Tool decision that requires speaking
      const speakingToolDecision = {
        shouldAct: true,
        toolsRequired: [
          {
            type: 'speech',
            name: 'control_avatar_speech',
            reasoning: 'User needs verbal response'
          }
        ]
      };

      const changeResult = modalityManager.determineModalityChange(
        currentModalities, 
        speakingToolDecision
      );

      expect(changeResult.shouldChange).toBe(true);
      expect(changeResult.newModalities).toEqual(['text', 'audio']);
      expect(changeResult.reason).toBe('speaking_tool_activated');
    });

    it('should not change modalities when not needed', () => {
      const currentModalities = ['text', 'audio'];
      
      // Tool decision that doesn't require speaking
      const nonSpeakingDecision = {
        shouldAct: true,
        toolsRequired: [
          {
            type: 'animation',
            name: 'select_animation',
            reasoning: 'Show visual feedback'
          }
        ]
      };

      const changeResult = modalityManager.determineModalityChange(
        currentModalities, 
        nonSpeakingDecision
      );

      expect(changeResult.shouldChange).toBe(false);
      expect(changeResult.newModalities).toEqual(['text', 'audio']);
      expect(changeResult.reason).toBe('no_change_needed');
    });

    it('should queue modality updates during active sessions', () => {
      sessionConfig.isUpdating = true;

      const modalityUpdate = {
        modalities: ['text', 'audio'],
        voice: 'Chelsie',
        reason: 'speaking_activated'
      };

      sessionConfig.queueUpdate(modalityUpdate);

      expect(sessionConfig.updateQueue).toHaveLength(1);
      expect(sessionConfig.updateQueue[0]).toEqual(modalityUpdate);
    });
  });

  describe('3. VAD Configuration and Audio Processing', () => {
    it('should configure VAD settings properly for audio sessions', () => {
      const audioModalities = determineSystem1Modalities(true);
      const sessionUpdate = createAliyunModalityUpdate(audioModalities);

      const vadConfig = sessionUpdate.session.turn_detection;

      expect(vadConfig).toMatchObject({
        type: 'server_vad',
        threshold: 0.1,
        prefix_padding_ms: 500,
        silence_duration_ms: 900
      });
    });

    it('should handle custom VAD configurations', () => {
      const audioModalities = determineSystem1Modalities(true);
      const customVadConfig = {
        turn_detection: {
          type: 'server_vad',
          threshold: 0.05,
          prefix_padding_ms: 300,
          silence_duration_ms: 1200,
          continuous_vad: true
        }
      };

      const sessionUpdate = createAliyunModalityUpdate(audioModalities, customVadConfig);

      expect(sessionUpdate.session.turn_detection).toMatchObject({
        type: 'server_vad',
        threshold: 0.05,
        prefix_padding_ms: 300,
        silence_duration_ms: 1200,
        continuous_vad: true
      });
    });

    it('should validate audio format configurations', () => {
      const audioModalities = determineSystem1Modalities(true);
      
      // Test with various audio formats
      const formats = ['pcm16', 'mp3', 'wav', 'opus'];
      
      formats.forEach(format => {
        const sessionUpdate = createAliyunModalityUpdate(audioModalities, {
          input_audio_format: format,
          output_audio_format: format
        });

        expect(sessionUpdate.session.input_audio_format).toBe(format);
        expect(sessionUpdate.session.output_audio_format).toBe(format);
      });
    });

    it('should process audio stream configuration for real-time processing', () => {
      const streamConfig = {
        input_audio_format: 'pcm16',
        sample_rate: 16000,
        channels: 1,
        bit_depth: 16,
        frame_duration_ms: 20,
        buffer_size: 1024
      };

      const audioModalities = determineSystem1Modalities(true);
      const sessionUpdate = createAliyunModalityUpdate(audioModalities, streamConfig);

      expect(sessionUpdate.session.input_audio_format).toBe('pcm16');
      expect(sessionUpdate.session.sample_rate).toBe(16000);
      expect(sessionUpdate.session.channels).toBe(1);
      expect(sessionUpdate.session.bit_depth).toBe(16);
    });
  });

  describe('4. Request Lifecycle Modality Management', () => {
    it('should manage modalities throughout complete request lifecycle', async () => {
      const lifecycle = {
        initial: sessionConfig.getCurrentConfig(),
        afterSystem1: null,
        afterSystem2Decision: null,
        afterToolExecution: null,
        final: null
      };

      // 1. Initial state (text-only)
      expect(lifecycle.initial.modalities).toEqual(['text']);

      // 2. After System 1 analysis (still text-only)
      lifecycle.afterSystem1 = sessionConfig.getCurrentConfig();
      expect(lifecycle.afterSystem1.modalities).toEqual(['text']);

      // 3. After System 2 decides to speak
      const speakingDecision = {
        shouldAct: true,
        toolsRequired: [{ type: 'speech', name: 'control_avatar_speech' }]
      };

      const modalityChange = modalityManager.determineModalityChange(
        lifecycle.afterSystem1.modalities,
        speakingDecision
      );

      if (modalityChange.shouldChange) {
        await sessionConfig.updateModalities({
          modalities: modalityChange.newModalities,
          voice: 'Chelsie'
        });
      }

      lifecycle.afterSystem2Decision = sessionConfig.getCurrentConfig();
      expect(lifecycle.afterSystem2Decision.modalities).toEqual(['text', 'audio']);

      // 4. After tool execution (maintain audio for response)
      lifecycle.afterToolExecution = sessionConfig.getCurrentConfig();
      expect(lifecycle.afterToolExecution.modalities).toEqual(['text', 'audio']);

      // 5. Final state (could return to text-only or stay audio)
      lifecycle.final = sessionConfig.getCurrentConfig();
      expect(lifecycle.final.modalities).toContain('text');
      
      // Verify complete lifecycle tracking
      expect(lifecycle.initial).toBeDefined();
      expect(lifecycle.afterSystem1).toBeDefined();
      expect(lifecycle.afterSystem2Decision).toBeDefined();
      expect(lifecycle.afterToolExecution).toBeDefined();
      expect(lifecycle.final).toBeDefined();
    });

    it('should handle concurrent modality update requests', async () => {
      const concurrentUpdates = [
        { modalities: ['text', 'audio'], voice: 'Chelsie', priority: 1 },
        { modalities: ['text'], voice: null, priority: 2 },
        { modalities: ['text', 'audio'], voice: 'Serena', priority: 3 }
      ];

      // Queue all updates
      concurrentUpdates.forEach(update => {
        sessionConfig.queueUpdate(update);
      });

      expect(sessionConfig.updateQueue).toHaveLength(3);

      // Process updates in priority order
      sessionConfig.updateQueue.sort((a, b) => a.priority - b.priority);
      
      // Apply the highest priority update
      const highestPriority = sessionConfig.updateQueue[0];
      await sessionConfig.updateModalities(highestPriority);

      expect(sessionConfig.currentModalities).toEqual(['text', 'audio']);
    });
  });

  describe('5. Error Handling and Edge Cases', () => {
    it('should handle malformed modality configurations gracefully', () => {
      const malformedConfigs = [
        null,
        undefined,
        { modalities: null },
        { modalities: [] },
        { modalities: 'invalid' },
        {}
      ];

      malformedConfigs.forEach(config => {
        expect(() => {
          // Should not throw when given malformed config
          const sessionUpdate = createAliyunModalityUpdate(
            config || { modalities: ['text'], voice: null }
          );
          expect(sessionUpdate.type).toBe('session.update');
        }).not.toThrow();
      });
    });

    it('should provide fallback configurations for failed updates', () => {
      const fallbackConfig = {
        modalities: ['text'],
        voice: null,
        reason: 'fallback_due_to_error'
      };

      // Mock update failure
      sessionConfig.updateModalities.mockRejectedValueOnce(
        new Error('WebSocket connection failed')
      );

      // Should fall back to safe configuration
      const safeConfig = sessionConfig.getCurrentConfig();
      expect(safeConfig.modalities).toContain('text');
    });

    it('should validate session update event structure', () => {
      const textModalities = determineSystem1Modalities(false);
      const sessionUpdate = createAliyunModalityUpdate(textModalities);

      // Required fields
      expect(sessionUpdate.type).toBe('session.update');
      expect(sessionUpdate.event_id).toBeDefined();
      expect(sessionUpdate.session).toBeDefined();
      expect(sessionUpdate.session.modalities).toBeDefined();
      expect(Array.isArray(sessionUpdate.session.modalities)).toBe(true);

      // Audio configuration
      expect(sessionUpdate.session.input_audio_format).toBeDefined();
      expect(sessionUpdate.session.output_audio_format).toBeDefined();
      expect(sessionUpdate.session.turn_detection).toBeDefined();
      expect(sessionUpdate.session.turn_detection.type).toBe('server_vad');
    });

    it('should handle WebSocket connection state during modality updates', () => {
      const connectionStates = ['connecting', 'open', 'closing', 'closed'];
      
      connectionStates.forEach(state => {
        const updateResult = modalityManager.generateSessionUpdate({
          modalities: ['text', 'audio'],
          voice: 'Chelsie',
          connectionState: state
        });

        expect(updateResult.type).toBe('session.update');
        expect(updateResult.session.modalities).toEqual(['text', 'audio']);
        
        // Updates should be generated regardless of connection state
        // (they may be queued for when connection is available)
      });
    });

    it('should handle rapid modality switching scenarios', () => {
      const rapidSwitches = [
        { modalities: ['text'], timestamp: 1000 },
        { modalities: ['text', 'audio'], timestamp: 1050 },
        { modalities: ['text'], timestamp: 1100 },
        { modalities: ['text', 'audio'], timestamp: 1150 }
      ];

      rapidSwitches.forEach(switchConfig => {
        const sessionUpdate = createAliyunModalityUpdate(switchConfig);
        expect(sessionUpdate.type).toBe('session.update');
        expect(sessionUpdate.session.modalities).toEqual(switchConfig.modalities);
      });

      // Should handle rapid switches without errors
      expect(rapidSwitches).toHaveLength(4);
    });
  });

  describe('6. Integration with Dual Brain Configuration', () => {
    it('should use configuration from DUAL_BRAIN_CONFIG', () => {
      const config = DUAL_BRAIN_CONFIG.modalityControl;
      
      expect(config.defaultModalities).toEqual(['text']);
      expect(config.speakingModalities).toEqual(['text', 'audio']);
      expect(config.defaultVoice).toBe('Chelsie');

      // Test that functions use this configuration
      const textModalities = determineSystem1Modalities(false);
      const audioModalities = determineSystem1Modalities(true);

      expect(textModalities.modalities).toEqual(config.defaultModalities);
      expect(audioModalities.modalities).toEqual(config.speakingModalities);
      expect(audioModalities.voice).toBe(config.defaultVoice);
    });

    it('should integrate with dual brain routing configuration', () => {
      const system1Config = DUAL_BRAIN_CONFIG.routing.system1;
      const system2Config = DUAL_BRAIN_CONFIG.routing.system2;

      expect(system1Config.defaultModalities).toEqual(['text']);
      expect(system1Config.audioModalities).toEqual(['text', 'audio']);
      expect(system1Config.nativeAudioCapability).toBe(true);

      expect(system2Config.controlsSystem1Audio).toBe(true);
      expect(system2Config.canTriggerAudioResponse).toBe(true);
    });
  });
});