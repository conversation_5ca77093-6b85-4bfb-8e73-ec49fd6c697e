/**
 * Two-Phase Modality Processing Test
 * 
 * Tests the revised dual brain system with proper two-phase processing:
 * Phase 1: Audio input → System 1 text description (modalities: ["text"])
 * Phase 2: System 2 decision making → Tool calling for audio output
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import { SystemInvoker } from '../../src/agent/arch/dualbrain/services/SystemInvoker.js';
import { DualBrainCoordinator } from '../../src/agent/arch/dualbrain/DualBrainCoordinator.js';
import { DUAL_BRAIN_CONFIG, getRoutingConfig, getTwoPhaseConfig } from '../../src/agent/config/DualBrainConfig.js';

describe('Two-Phase Modality Processing', () => {
  let systemInvoker;
  let dualBrainCoordinator;
  let mockAgentService;
  let mockSystem1Model;
  let mockSystem2Model;

  beforeEach(() => {
    // Mock System 1 model (WebSocket-based)
    mockSystem1Model = {
      invoke: vi.fn().mockResolvedValue({
        data: 'User is speaking into microphone. Scene shows active camera feed. Intent appears to be conversational engagement.'
      })
    };

    // Mock System 2 model (HTTP-based with tool calling)
    mockSystem2Model = {
      invoke: vi.fn().mockResolvedValue({
        data: {
          content: 'Based on the scene analysis, I should respond to engage the user.',
          tool_calls: [{
            name: 'avatar_speak',
            args: { text: 'Hello! I can see you\'re ready to chat. How can I help you today?' }
          }]
        }
      })
    };

    // Mock agent service
    mockAgentService = {
      generateResponse: vi.fn().mockResolvedValue({
        content: 'System 2 decision made',
        tool_calls: [{
          name: 'avatar_speak',
          args: { text: 'Hello! How can I help?' }
        }]
      }),
      getModel: vi.fn((type) => {
        return type === 'system1' ? mockSystem1Model : mockSystem2Model;
      }),
      isDualBrainMode: vi.fn().mockReturnValue(true),
      tools: []
    };

    // Mock decision processor
    const mockDecisionProcessor = {
      processDecision: vi.fn().mockResolvedValue(true)
    };

    // Create SystemInvoker
    systemInvoker = new SystemInvoker();
    systemInvoker.initialize({
      system1: mockSystem1Model,
      system2: mockSystem2Model
    });
    systemInvoker.setAgentService(mockAgentService);
    systemInvoker.setDecisionProcessor(mockDecisionProcessor);

    // Create DualBrainCoordinator
    dualBrainCoordinator = new DualBrainCoordinator(mockAgentService, {
      services: {
        systemInvoker: systemInvoker,
        decisionProcessor: mockDecisionProcessor
      }
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  test('Consolidated configuration is loaded correctly', () => {
    // Test consolidated DUAL_BRAIN_CONFIG
    expect(DUAL_BRAIN_CONFIG).toBeDefined();
    expect(DUAL_BRAIN_CONFIG.twoPhaseProcessing.enabled).toBe(true);
    expect(DUAL_BRAIN_CONFIG.systems.system1.modalities.analysis).toEqual(['text']);
    expect(DUAL_BRAIN_CONFIG.systems.system1.modalities.response).toEqual(['text', 'audio']);

    // Test routing config function
    const audioRouting = getRoutingConfig('audio');
    expect(audioRouting.targetSystem).toBe('system1');
    expect(audioRouting.reason).toBe('audio_input_routing');
    expect(audioRouting.modalityOverride).toEqual(['text']);

    // Test two-phase config functions
    const phase1Config = getTwoPhaseConfig('phase1');
    expect(phase1Config.modalities).toEqual(['text']);
    expect(phase1Config.skipAudioOutput).toBe(true);

    const phase2Config = getTwoPhaseConfig('phase2');
    expect(phase2Config.enableToolCalling).toBe(true);
    expect(phase2Config.enableSpeaking).toBe(true);
  });

  test('Audio input triggers two-phase processing', async () => {
    // Test audio input routing
    const routingResult = dualBrainCoordinator._routeToAppropriateSystem(
      'Audio stream input', 
      { inputType: 'audio', hasAudioInput: true }
    );

    expect(routingResult).toEqual({
      targetSystem: 'system1',
      reason: 'audio_input_routing',
      capabilities: ['audioInput', 'textOutput'],
      modalityOverride: ['text'],
      forAnalysis: true,
      useRealtime: true
    });
  });

  test('SystemInvoker handles two-phase processing correctly', async () => {
    const input = 'Audio stream from microphone';
    const context = {
      routing: {
        reason: 'audio_input_routing',
        forAnalysis: true,
        capabilities: ['audioInput', 'textOutput'],
        modalityOverride: ['text']
      }
    };

    const result = await systemInvoker.invokeSupervisor(input, context);

    // Verify two-phase processing occurred
    expect(result.routing.targetSystem).toBe('two_phase_processing');
    expect(result.routing.reason).toBe('audio_input_two_phase');
    expect(result.routing.phases).toEqual({
      phase1: 'system1_text_analysis',
      phase2: 'system2_decision_making'
    });

    // Verify Phase 1: System 1 was called for text analysis
    expect(mockSystem1Model.invoke).toHaveBeenCalledWith({
      input: expect.stringContaining('CONTEXT: Analyze the current scene'),
      context: expect.objectContaining({
        modalityOverride: ['text'],
        skipAudioOutput: true,
        purpose: 'scene_analysis'
      }),
      metadata: expect.objectContaining({
        system: 'system1',
        modalityConfiguration: expect.objectContaining({
          textOnly: true,
          audioEnabled: false
        })
      })
    });

    // Verify Phase 2: System 2 was called for decision making
    expect(mockAgentService.generateResponse).toHaveBeenCalledWith(
      expect.stringContaining('SCENE ANALYSIS:'),
      expect.objectContaining({
        enableToolCalling: true,
        enableSpeaking: true,
        purpose: 'speaking_decision'
      })
    );

    // Verify both phase results are included
    expect(result.phase1Result).toBeDefined();
    expect(result.phase2Result).toBeDefined();
  });

  test('Phase 1 generates text-only description', async () => {
    const textDescription = await systemInvoker._generateSceneDescription(
      'Audio input detected',
      {
        routing: {
          capabilities: ['audioInput', 'videoInput']
        }
      }
    );

    expect(mockSystem1Model.invoke).toHaveBeenCalledWith({
      input: expect.stringContaining('Generate a concise text description'),
      context: expect.objectContaining({
        modalityOverride: ['text'],
        skipAudioOutput: true,
        purpose: 'scene_analysis'
      }),
      metadata: expect.objectContaining({
        modalityConfiguration: expect.objectContaining({
          textOnly: true
        })
      })
    });

    expect(textDescription.data).toBe(
      'User is speaking into microphone. Scene shows active camera feed. Intent appears to be conversational engagement.'
    );
  });

  test('Phase 2 processes scene analysis for tool calling', async () => {
    const sceneDescription = 'User is actively speaking and looking at camera';
    const originalContext = {
      routing: { reason: 'audio_input_routing' },
      originalInput: 'audio stream'
    };

    const decision = await systemInvoker._processSceneAnalysisForSpeaking(
      sceneDescription,
      originalContext
    );

    expect(mockAgentService.generateResponse).toHaveBeenCalledWith(
      expect.stringContaining(`SCENE ANALYSIS: ${sceneDescription}`),
      expect.objectContaining({
        enableToolCalling: true,
        enableSpeaking: true,
        sceneAnalysis: sceneDescription,
        purpose: 'speaking_decision'
      })
    );

    expect(decision.tool_calls).toBeDefined();
    expect(decision.tool_calls[0].name).toBe('avatar_speak');
  });

  test('Non-audio input bypasses two-phase processing', async () => {
    const input = 'Regular text input';
    const context = {
      routing: {
        targetSystem: 'system2',
        reason: 'complex_query',
        capabilities: ['tools', 'thinking']
      }
    };

    const result = await systemInvoker.invokeSupervisor(input, context);

    // Should NOT trigger two-phase processing
    expect(result.routing.targetSystem).toBe('system2');
    expect(result.routing.reason).not.toBe('audio_input_two_phase');
    expect(result.phase1Result).toBeUndefined();
    expect(result.phase2Result).toBeUndefined();

    // Should call System 2 directly
    expect(mockAgentService.generateResponse).toHaveBeenCalledTimes(1);
    expect(mockSystem1Model.invoke).not.toHaveBeenCalled();
  });

  test('Modality override is properly passed to System 1', async () => {
    const request = {
      input: 'Test input',
      context: {
        modalityOverride: ['text'],
        skipAudioOutput: true
      },
      options: {
        modalityOverride: ['text']
      }
    };

    const preparedRequest = systemInvoker._prepareSystem1Request(request, 'test-id');

    expect(preparedRequest.context.modalityOverride).toEqual(['text']);
    expect(preparedRequest.context.skipAudioOutput).toBe(true);
    expect(preparedRequest.metadata.modalityConfiguration).toEqual({
      requested: ['text'],
      textOnly: true,
      audioEnabled: false
    });
  });
});

describe('Dual Brain Coordinator Audio Routing', () => {
  let coordinator;
  let mockAgentService;

  beforeEach(() => {
    mockAgentService = {
      isDualBrainMode: () => true,
      getModel: () => ({}),
      generateResponse: vi.fn()
    };

    coordinator = new DualBrainCoordinator(mockAgentService);
  });

  test('Audio input routes to two-phase processing', () => {
    const audioInputOptions = [
      { inputType: 'audio' },
      { hasAudioInput: true },
      { modality: 'audio' },
      { inputType: 'audio', hasAudioInput: true, modality: 'audio' }
    ];

    audioInputOptions.forEach(options => {
      const result = coordinator._routeToAppropriateSystem('test input', options);
      
      expect(result.targetSystem).toBe('system1');
      expect(result.reason).toBe('audio_input_routing');
      expect(result.capabilities).toEqual(['audioInput', 'textOutput']);
      expect(result.modalityOverride).toEqual(['text']);
      expect(result.forAnalysis).toBe(true);
      expect(result.useRealtime).toBe(true);
    });
  });

  test('Text input uses normal routing', () => {
    const result = coordinator._routeToAppropriateSystem(
      'Simple text query',
      { inputType: 'text' }
    );

    // Should not trigger audio-specific routing
    expect(result.reason).not.toBe('audio_input_routing');
    expect(result.forAnalysis).toBeUndefined();
    expect(result.modalityOverride).toBeUndefined();
  });
});