/**
 * Modality Routing Tests - New Architecture
 * 
 * Tests for the dual brain modality system focusing on:
 * - System 1 text description output for scene analysis
 * - System 2 tool calling with audio modality decisions
 * - Modality switching between ["text"] and ["text","audio"]
 * - Audio input → text description → decision flow
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { DualBrainCoordinator } from '@/agent/arch/dualbrain/DualBrainCoordinator.js';
import { SystemInvoker } from '@/agent/arch/dualbrain/services/SystemInvoker.js';
import { 
  determineSystem1Modalities, 
  createAliyunModalityUpdate,
  determineSystemRouting
} from '@/agent/config/LangGraphConfig.js';

// Mock logger
vi.mock('@/utils/logger.ts', () => ({
  createLogger: () => ({
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    setLogLevel: vi.fn()
  }),
  LogLevelValues: { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3 },
  LogLevel: { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3 }
}));

describe('Dual Brain Modality Routing - Current Architecture', () => {
  let coordinator;
  let systemInvoker;
  let mockAgentService;
  let mockDecisionProcessor;

  beforeEach(() => {
    // Create mock agent service with dual brain models
    mockAgentService = {
      isDualBrainMode: vi.fn(() => true),
      getModel: vi.fn().mockImplementation((type) => {
        if (type === 'system1') {
          return {
            constructor: { name: 'AliyunWebSocketChatModel' },
            apiMode: 'websocket',
            generateTextDescription: vi.fn().mockResolvedValue({
              content: 'User appears to be asking about AI capabilities',
              confidence: 0.8,
              sceneAnalysis: 'Single person speaking to camera'
            }),
            setModalities: vi.fn(),
            getCurrentModalities: vi.fn(() => ['text'])
          };
        } else if (type === 'system2') {
          return {
            constructor: { name: 'AliyunHttpChatModel' },
            apiMode: 'http',
            invoke: vi.fn().mockResolvedValue({
              content: 'I can help you with that!',
              tool_calls: [
                {
                  function: {
                    name: 'control_avatar_speech',
                    arguments: JSON.stringify({
                      action: 'speak',
                      text: 'I can help you with AI questions',
                      voice: 'Chelsie'
                    })
                  }
                }
              ]
            })
          };
        }
        return null;
      }),
      generateResponse: vi.fn().mockResolvedValue({
        content: 'I can help you with that!',
        tool_calls: [
          {
            function: {
              name: 'control_avatar_speech',
              arguments: JSON.stringify({
                action: 'speak',
                text: 'I can help you with AI questions'
              })
            }
          }
        ]
      }),
      tools: [
        { name: 'control_avatar_speech', func: vi.fn() },
        { name: 'select_animation', func: vi.fn() }
      ]
    };

    // Create mock decision processor
    mockDecisionProcessor = {
      processDecision: vi.fn().mockResolvedValue({
        executed: true,
        toolsExecuted: ['control_avatar_speech'],
        modalityChanged: true,
        newModalities: ['text', 'audio']
      })
    };

    // Initialize SystemInvoker with enhanced integration
    systemInvoker = new SystemInvoker({
      validateInputs: false,
      validateOutputs: false
    });

    systemInvoker.configureModels({
      system1: mockAgentService.getModel('system1'),
      system2: mockAgentService.getModel('system2')
    });

    systemInvoker.setAgentService(mockAgentService);
    systemInvoker.setDecisionProcessor(mockDecisionProcessor);

    // Initialize coordinator with services
    coordinator = new DualBrainCoordinator(mockAgentService, {
      services: {
        systemInvoker,
        decisionProcessor: mockDecisionProcessor
      }
    });
  });

  afterEach(() => {
    if (coordinator) {
      coordinator.dispose();
    }
  });

  describe('1. Audio Input → System 1 Text Description', () => {
    it('should route audio input to System 1 for text description generation', async () => {
      const audioInput = {
        type: 'audio',
        format: 'pcm16',
        sampleRate: 16000,
        data: new ArrayBuffer(1024)
      };

      const routing = coordinator._routeToAppropriateSystem('audio input', {
        inputType: 'audio',
        hasAudioInput: true,
        modality: 'audio'
      });

      expect(routing.targetSystem).toBe('system1');
      expect(routing.reason).toBe('audio_input_routing');
      expect(routing.capabilities).toContain('audioInput');
      expect(routing.capabilities).toContain('realtime');
      expect(routing.useRealtime).toBe(true);
    });

    it('should generate text descriptions from System 1 for audio/video scenes', async () => {
      const system1Model = mockAgentService.getModel('system1');
      
      const result = await system1Model.generateTextDescription({
        audioInput: 'User speaking about AI',
        videoContext: 'Person at desk with computer'
      });

      expect(result.content).toContain('User appears to be asking about AI');
      expect(result.confidence).toBeGreaterThan(0.5);
      expect(result.sceneAnalysis).toBeDefined();
      expect(system1Model.generateTextDescription).toHaveBeenCalledWith({
        audioInput: 'User speaking about AI',
        videoContext: 'Person at desk with computer'
      });
    });

    it('should maintain text-only modality for System 1 scene descriptions', async () => {
      const system1Model = mockAgentService.getModel('system1');
      
      // System 1 should default to text-only for generating descriptions
      const modalities = determineSystem1Modalities(false);
      
      expect(modalities.modalities).toEqual(['text']);
      expect(modalities.voice).toBeNull();
      expect(modalities.reason).toBe('Default text-only for analysis');
      
      // Verify System 1 model is configured for text-only output
      expect(system1Model.getCurrentModalities()).toEqual(['text']);
    });
  });

  describe('2. System 1 → System 2 Context Handoff', () => {
    it('should pass System 1 text descriptions to System 2 for decision making', async () => {
      const system1Description = {
        content: 'User is asking about machine learning concepts',
        sceneAnalysis: 'Engaged user looking at screen',
        confidence: 0.9
      };

      const request = {
        input: system1Description.content,
        context: {
          system1Analysis: system1Description,
          enableToolCalling: true,
          capabilities: ['tools', 'speaking', 'thinking']
        }
      };

      const result = await systemInvoker.invokeSystem2(request);

      expect(result.status).toBe('success');
      expect(mockAgentService.generateResponse).toHaveBeenCalledWith(
        system1Description.content,
        expect.objectContaining({
          useSystem2: true,
          enableTools: true,
          dualBrainContext: expect.objectContaining({
            isSystem2: true,
            enableToolCalling: true
          })
        })
      );
    });

    it('should include rich context in System 1 → System 2 handoff', async () => {
      const enrichedContext = {
        system1Output: 'User discussing AI topics',
        audioActivity: true,
        environmentalState: 'engaged_conversation',
        userIntent: 'seeking_information',
        sceneConfidence: 0.85
      };

      coordinator.state.lastSystem1Response = {
        timestamp: Date.now(),
        content: enrichedContext.system1Output,
        analysis: enrichedContext
      };

      const contextDescription = coordinator._buildSystem1ContextualDescription(enrichedContext);

      expect(contextDescription).toContain('Audio Environment: Active user audio detected');
      expect(contextDescription).toContain('Environmental State: engaged_conversation');
      expect(contextDescription).toContain('System 1 Analysis: User discussing AI topics');
    });
  });

  describe('3. System 2 Tool Calling Decisions', () => {
    it('should route complex queries to System 2 with tool calling capabilities', () => {
      const routing = determineSystemRouting(
        'Can you explain machine learning and show me some examples?',
        { requiresReasoning: true, enableToolCalling: true }
      );

      expect(routing.targetSystem).toBe('system2');
      expect(routing.reason).toContain('Tool calling required');
      expect(routing.capabilities).toContain('tools');
      expect(routing.capabilities).toContain('thinking');
      expect(routing.capabilities).toContain('complexReasoning');
    });

    it('should make tool calling decisions through System 2', async () => {
      const request = {
        input: 'Can you speak to me about AI?',
        context: {
          enableToolCalling: true,
          capabilities: ['tools', 'speaking']
        }
      };

      const result = await systemInvoker.invokeSystem2(request);

      expect(result.status).toBe('success');
      expect(result.data.tool_calls).toHaveLength(1);
      expect(result.data.tool_calls[0].function.name).toBe('control_avatar_speech');
      
      // Verify DecisionProcessor was called to handle the tool execution
      expect(mockDecisionProcessor.processDecision).toHaveBeenCalledWith(
        expect.objectContaining({
          tool_calls: expect.arrayContaining([
            expect.objectContaining({
              function: expect.objectContaining({
                name: 'control_avatar_speech'
              })
            })
          ])
        }),
        expect.objectContaining({
          source: 'system2_agent_service'
        })
      );
    });

    it('should include speaking decisions in System 2 tool calling', async () => {
      const system2Model = mockAgentService.getModel('system2');
      
      system2Model.invoke.mockResolvedValue({
        content: 'I should speak to engage the user',
        tool_calls: [
          {
            function: {
              name: 'control_avatar_speech',
              arguments: JSON.stringify({
                action: 'speak',
                text: 'Hello! I can help you learn about AI',
                voice: 'Chelsie',
                reasoning: 'User appears interested and ready to listen'
              })
            }
          }
        ]
      });

      const request = {
        input: 'Tell me about artificial intelligence',
        context: { enableToolCalling: true }
      };

      const result = await systemInvoker.invokeSystem2(request);

      expect(result.data.tool_calls[0].function.name).toBe('control_avatar_speech');
      
      const args = JSON.parse(result.data.tool_calls[0].function.arguments);
      expect(args.action).toBe('speak');
      expect(args.text).toBeDefined();
      expect(args.voice).toBe('Chelsie');
      expect(args.reasoning).toBeDefined();
    });
  });

  describe('4. Modality Switching Logic', () => {
    it('should default to text-only modality for analysis', () => {
      const modalities = determineSystem1Modalities(false);
      
      expect(modalities.modalities).toEqual(['text']);
      expect(modalities.voice).toBeNull();
      expect(modalities.reason).toBe('Default text-only for analysis');
    });

    it('should switch to text+audio when speaking tools are activated', () => {
      const modalities = determineSystem1Modalities(true);
      
      expect(modalities.modalities).toEqual(['text', 'audio']);
      expect(modalities.voice).toBe('Chelsie');
      expect(modalities.reason).toBe('Speaking tool activated');
    });

    it('should generate proper Aliyun session.update events for modality changes', () => {
      const speakingModalities = determineSystem1Modalities(true);
      const sessionUpdate = createAliyunModalityUpdate(speakingModalities);

      expect(sessionUpdate.type).toBe('session.update');
      expect(sessionUpdate.event_id).toMatch(/^event_\d+_[a-z0-9]+$/);
      expect(sessionUpdate.session.modalities).toEqual(['text', 'audio']);
      expect(sessionUpdate.session.voice).toBe('Chelsie');
      expect(sessionUpdate.session.input_audio_format).toBe('pcm16');
      expect(sessionUpdate.session.output_audio_format).toBe('pcm16');
      expect(sessionUpdate.session.turn_detection.type).toBe('server_vad');
    });

    it('should coordinate modality changes through DecisionProcessor', async () => {
      const decision = {
        shouldAct: true,
        toolsRequired: [
          {
            type: 'speech',
            name: 'control_avatar_speech'
          }
        ]
      };

      const result = await mockDecisionProcessor.processDecision(decision, {
        source: 'system2_modality_test'
      });

      expect(result.executed).toBe(true);
      expect(result.modalityChanged).toBe(true);
      expect(result.newModalities).toEqual(['text', 'audio']);
      expect(result.toolsExecuted).toContain('control_avatar_speech');
    });
  });

  describe('5. Complete Audio Decision Pipeline', () => {
    it('should process audio input through full decision pipeline', async () => {
      // 1. Audio input arrives and routes to System 1
      const audioInput = 'User asking about machine learning';
      const routing = coordinator._routeToAppropriateSystem(audioInput, {
        inputType: 'audio',
        hasAudioInput: true
      });

      expect(routing.targetSystem).toBe('system1');

      // 2. System 1 generates text description
      const system1Model = mockAgentService.getModel('system1');
      const textDescription = await system1Model.generateTextDescription({
        audioInput
      });

      expect(textDescription.content).toContain('User appears to be asking about AI');

      // 3. System 2 analyzes and decides to speak
      const system2Request = {
        input: textDescription.content,
        context: { enableToolCalling: true }
      };

      const system2Result = await systemInvoker.invokeSystem2(system2Request);

      expect(system2Result.data.tool_calls).toHaveLength(1);
      expect(system2Result.data.tool_calls[0].function.name).toBe('control_avatar_speech');

      // 4. DecisionProcessor handles tool execution and modality switching
      expect(mockDecisionProcessor.processDecision).toHaveBeenCalled();

      // 5. Verify complete pipeline flow
      const pipelineResult = {
        audioInput,
        system1Analysis: textDescription,
        system2Decision: system2Result.data,
        modalitySwitch: true,
        finalModalities: ['text', 'audio']
      };

      expect(pipelineResult.audioInput).toBeDefined();
      expect(pipelineResult.system1Analysis.content).toBeDefined();
      expect(pipelineResult.system2Decision.tool_calls).toHaveLength(1);
      expect(pipelineResult.modalitySwitch).toBe(true);
      expect(pipelineResult.finalModalities).toEqual(['text', 'audio']);
    });

    it('should handle audio/video stream processing triggers', async () => {
      const streamTrigger = {
        type: 'audio_stream',
        vadDetected: true,
        speechStarted: true,
        timestamp: Date.now()
      };

      // Simulate VAD trigger processing
      coordinator.recordUserInteraction('audio');
      
      // Should reset quiet period and update activity
      const noActivityStatus = coordinator.getNoActivityStatus();
      expect(noActivityStatus.isQuietPeriod).toBe(false);
      expect(noActivityStatus.timeSinceLastInput).toBeLessThan(1000);

      // Should route subsequent processing to System 1
      const routing = coordinator._routeToAppropriateSystem('stream audio', {
        inputType: 'audio',
        vadTrigger: true
      });

      expect(routing.targetSystem).toBe('system1');
      expect(routing.capabilities).toContain('audioInput');
    });
  });

  describe('6. Session Modality Configuration', () => {
    it('should manage session modality state throughout request lifecycle', async () => {
      let currentModalities = ['text']; // Start with text-only

      // Process request that triggers speaking
      const request = await coordinator.processMultiAgentRequest(
        'Can you tell me about AI?',
        { complexity: 'medium' }
      );

      // Should route to System 2 and potentially change modalities
      expect(mockAgentService.generateResponse).toHaveBeenCalled();

      // If speaking tool was called, modalities should change
      if (mockDecisionProcessor.processDecision.mock.calls.length > 0) {
        const processorResult = await mockDecisionProcessor.processDecision.mock.results[0].value;
        if (processorResult.modalityChanged) {
          currentModalities = processorResult.newModalities;
        }
      }

      // Verify final state
      expect(currentModalities).toBeDefined();
      expect(Array.isArray(currentModalities)).toBe(true);
    });

    it('should handle modality configuration errors gracefully', () => {
      // Test with invalid modality configuration
      const invalidModalities = { modalities: null };
      
      expect(() => {
        createAliyunModalityUpdate(invalidModalities);
      }).not.toThrow();

      // Should fall back to default configuration
      const fallbackUpdate = createAliyunModalityUpdate({
        modalities: ['text'],
        voice: null
      });

      expect(fallbackUpdate.session.modalities).toEqual(['text']);
      expect(fallbackUpdate.session.voice).toBeUndefined();
    });
  });
});

describe('Integration with Current Architecture', () => {
  it('should integrate with LangGraphConfig modality functions', () => {
    // Test LangGraphConfig integration
    const textOnlyModalities = determineSystem1Modalities(false);
    const audioModalities = determineSystem1Modalities(true);

    expect(textOnlyModalities.modalities).toEqual(['text']);
    expect(audioModalities.modalities).toEqual(['text', 'audio']);

    // Test session update generation
    const sessionUpdate = createAliyunModalityUpdate(audioModalities);
    expect(sessionUpdate.type).toBe('session.update');
    expect(sessionUpdate.session.modalities).toEqual(['text', 'audio']);
  });

  it('should work with existing dual brain coordinator patterns', async () => {
    // This test ensures new modality tests work with existing coordinator structure
    const mockAgentService = {
      isDualBrainMode: () => true,
      getModel: (type) => ({
        constructor: { name: `Mock${type}Model` }
      })
    };

    const coordinator = new DualBrainCoordinator(mockAgentService);
    await coordinator.initialize();

    expect(coordinator.isInitialized).toBe(true);
    
    const status = coordinator.getStatus();
    expect(status.isInitialized).toBe(true);
  });
});