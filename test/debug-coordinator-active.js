#!/usr/bin/env node

/**
 * Debug script to check coordinator activation
 */

import { DualBrainCoordinator } from '../src/agent/arch/dualbrain/DualBrainCoordinator.js';
import { createLogger } from '../src/utils/logger.js';
import { createMockAgentService } from './src/utils/test-helpers.js';

const logger = createLogger('DebugCoordinatorActive');

async function debugCoordinatorActivation() {
  console.log('🔧 Creating mock agent service...');
  
  const mockAgentService = createMockAgentService({
    enableDualBrain: true,
    realtimeCapable: true
  });

  console.log('🧠 Creating DualBrainCoordinator...');
  const coordinator = new DualBrainCoordinator(mockAgentService, {
    decisionCooldown: 1000,
    system2AnalysisInterval: 5000,
    deadlockPrevention: true,
    enhancedContextFlow: true
  });

  console.log('📍 Before initialization:');
  console.log('  - isActive:', coordinator.isActive);

  console.log('🚀 Initializing coordinator...');
  const initResult = await coordinator.initialize();
  
  console.log('📍 After initialization:');
  console.log('  - initResult:', initResult);
  console.log('  - isActive:', coordinator.isActive);
  console.log('  - coordinationMode:', coordinator.coordinationMode);

  console.log('🧪 Testing decision generation...');
  try {
    const decision1 = await coordinator.generateProactiveDecision({ test: 'first' });
    console.log('  - decision1:', decision1);

    const decision2 = await coordinator.generateProactiveDecision({ test: 'second' });
    console.log('  - decision2:', decision2);
  } catch (error) {
    console.error('❌ Decision generation error:', error);
  }

  coordinator.dispose();
}

debugCoordinatorActivation().catch(console.error);