# Hologram Software Agent Tests

Organized test suite for the simplified LangChain.js-based agent system with real API integration.

## 🎯 Test Organization

### Test Categories

| Category | Description | Files | API Required |
|----------|-------------|-------|--------------|
| **quick** | Fast tests without real API calls | `tools/real-tools.test.js`, `input.test.js`, `prompt.test.js`, voice cloning tests | ❌ |
| **unit** | Unit tests for individual components | Core, tools, multimodal, TalkingAvatar tests | ❌ |
| **streaming** | LangChain.js streaming tests | `stream/langchain-streaming.test.js`, `streamprocessor-integration.test.js`, TTS animation | ✅ |
| **integration** | Full integration with real API | `integration/real-api.test.js`, `integration/langgraph-talkingavatar.test.js`, core integration | ✅ |
| **realApi** | Real API tests only | Integration + streaming tests | ✅ |
| **voiceCloning** | Voice cloning and TTS tests | Voice preservation, SparkTTS fallback, seed file handling | ❌ |
| **animation** | Animation integration tests | TTS-animation integration, animation system tests | ❌ |
| **all** | Complete test suite | All test files | ✅ |

## 🚀 Running Tests

### Agent WebSocket & Multimodal Tests

```bash
# Run all agent WebSocket and multimodal tests
node test/src/agent/websocket-multimodal.test.js

# Run specific test types
node test/src/agent/websocket-multimodal.test.js direct      # Direct Aliyun connection
node test/src/agent/websocket-multimodal.test.js proxy       # Proxy connection  
node test/src/agent/websocket-multimodal.test.js multimodal  # Environment & utilities
```

### Using npm scripts (Recommended)

```bash
# Quick tests (default) - no real API required
npm test

# Unit tests
npm run test:unit

# Streaming tests
npm run test:streaming

# Integration tests with real API
npm run test:integration

# Real API tests only
npm run test:real-api

# Voice cloning and TTS tests
npm run test:voice-cloning

# Animation integration tests
npm run test:animation

# All tests
npm run test:all

# Watch mode
npm run test:watch

# Coverage report
npm run test:coverage
### Using test runner directly
```bash
# Quick tests
node test/run-tests.js quick

# Real API tests with coverage
node test/run-tests.js realApi --coverage

# Streaming tests in watch mode
node test/run-tests.js streaming --watch

# Voice cloning tests with verbose output
node test/run-tests.js voiceCloning --verbose

# Animation tests with coverage
node test/run-tests.js animation --coverage

# All tests with verbose output
node test/run-tests.js all --verbose --coverage
```

## 🔧 Configuration

### Environment Variables

```bash
# vLLM API endpoint (required for real API tests)
export VITE_VLLM_ENDPOINT="http://***********:20095"

# ASR endpoint (for integration tests)
export VITE_ASR_ENDPOINT="http://***********:20100"

# Log level for tests
export LOG_LEVEL="warn"
```

### Test Configuration

- **Timeout**: 15 seconds for real API calls
- **Retries**: 2 retries for flaky real API tests
- **Coverage**: 85% threshold for critical components
- **Parallel**: Up to 4 threads for faster execution

## 📁 Test Structure

```
test/
├── agent/
│   ├── core/                    # Core agent service tests
│   │   ├── initialization.test.js
│   │   ├── tools.test.js
│   │   └── multimodal.test.js
│   ├── stream/                  # Streaming tests
│   │   └── langchain-streaming.test.js
│   ├── integration/             # Integration tests
│   │   ├── real-api.test.js
│   │   └── langgraph-talkingavatar.test.js
│   ├── tools/                   # Tool tests
│   │   ├── real-tools.test.js
│   │   └── tts/                 # TTS service tests
│   │       ├── sparktts-service.test.js
│   │       ├── tts-functional.test.js
│   │       ├── tts-integration.test.js
│   │       └── tts-tools.test.js
│   ├── input.test.js
│   └── prompt.test.js
├── media/                       # Media processing tests
├── modules/                     # Module-specific tests
│   └── talkinghead/
│       └── src/                 # TalkingHead module source tests
│           ├── sparktts-enhanced-fallback.test.js
│           ├── seed-file-fallback.test.js
│           └── README.md
├── talkingavatar/              # TalkingAvatar class tests
│   ├── voice-cloning-preservation.test.js
│   └── tts-animation-integration.test.js
├── utils/                      # Utility function tests
├── animation/                  # Animation system tests
├── config/                     # Configuration tests
├── server/                     # Server component tests
├── setup/                      # Test setup and utilities
├── vitest.config.js           # Vitest configuration
├── run-tests.js               # Organized test runner
└── README.md                   # This file
```

## 🧪 Test Types

### 1. Quick Tests (`npm test`)

- **Purpose**: Fast feedback during development
- **No API required**: Uses mocks and fixtures
- **Duration**: < 30 seconds
- **Use case**: Pre-commit checks, rapid iteration

### 2. Unit Tests (`npm run test:unit`)

- **Purpose**: Test individual components in isolation
- **Includes**: Core services, tools, utilities
- **Duration**: < 1 minute
- **Use case**: Component development and debugging

### 3. Streaming Tests (`npm run test:streaming`)

- **Purpose**: Test LangChain.js native streaming
- **Requires**: Real vLLM API
- **Tests**: Stream processing, backpressure, tool calling
- **Duration**: 1-2 minutes
- **Use case**: Streaming feature development

### 4. Integration Tests (`npm run test:integration`)

- **Purpose**: End-to-end testing with real APIs
- **Requires**: vLLM API, ASR service
- **Tests**: Full agent workflow, TalkingAvatar integration
- **Duration**: 2-5 minutes
- **Use case**: Release validation, CI/CD

### 5. Real API Tests (`npm run test:real-api`)

- **Purpose**: API-dependent tests only
- **Requires**: vLLM API
- **Tests**: API connectivity, streaming, tool execution
- **Duration**: 1-3 minutes
- **Use case**: API validation, deployment checks

## 🔌 WebSocket & Multimodal Test Suite

**Consolidated Test Script:** `test/src/agent/websocket-multimodal.test.js`

This comprehensive test suite validates WebSocket connectivity and multimodal functionality for the agent system.

### Quick Start

```bash
# Run all WebSocket and multimodal tests
node test/src/agent/websocket-multimodal.test.js

# Run specific test categories
node test/src/agent/websocket-multimodal.test.js direct      # Test direct Aliyun connection
node test/src/agent/websocket-multimodal.test.js proxy       # Test proxy connection
node test/src/agent/websocket-multimodal.test.js multimodal  # Test environment & utilities
node test/src/agent/websocket-multimodal.test.js flow        # Test multimodal flow
```

### Test Results Summary

- ✅ **Direct Connection: SUCCESS** - Aliyun Qwen-Omni API connectivity working
- ✅ **Proxy Connection: SUCCESS** - Local proxy server working correctly  
- ✅ **Multimodal: SUCCESS** - Environment and project structure validated
- ✅ **Multimodal Flow: SUCCESS** - Agent multimodal capabilities validated
- ✅ **Connection Robustness: SUCCESS** - Error handling and race condition fixes working

✨ **All WebSocket connection issues have been resolved!**

### 🔧 Recent Fixes Applied

#### WebSocket Connection Robustness (v2.1)
1. **Blob Message Handling**: Fixed `SyntaxError: Unexpected token 'o', "[object Blob]" is not valid JSON`
   - Added support for String, Blob, and ArrayBuffer message formats
   - Automatic conversion to text using proper async methods

2. **Null Reference Protection**: Fixed `TypeError: Cannot read properties of null (reading 'send')`
   - Added null checking to all WebSocket send operations
   - Graceful handling when connection is not ready

3. **Race Condition Prevention**: Fixed "Received client message but no active upstream connection"
   - Implemented message queuing when upstream is connecting
   - Proper connection state management with `isUpstreamReady` flag
   - Messages are queued and sent once upstream is fully established

4. **Improved Error Handling**: Enhanced connection retry and recovery logic
   - Better logging with proper levels (using `this.logger.debug` instead of `console.log`)
   - Exponential backoff with max retry limits
   - Proper cleanup of message queues on disconnect

#### Test Coverage Expansion
- Added comprehensive connection robustness tests
- Validates all error handling scenarios
- Tests message queuing and state management
- Verifies null protection and Blob handling

### Quick Verification

To test the WebSocket connection fixes:

```bash
# Test all connections (recommended)
node test/src/agent/websocket-multimodal.test.js all

# Test individual components
node test/src/agent/websocket-multimodal.test.js direct    # Direct Aliyun connection
node test/src/agent/websocket-multimodal.test.js proxy     # Proxy connection 
node test/src/agent/websocket-multimodal.test.js multimodal # Environment check
```

**Note**: The consolidated test file replaces the previous `unified-websocket-tests.js` to eliminate test redundancy.

---

## 🔍 Key Features

### Real API Integration

- Tests use actual `fetchVllmApi` calls instead of mocks
- Automatic API availability detection
- Graceful fallback when APIs are unavailable
- Real checkpoint validation

### Simplified Streaming

- Tests LangChain.js native streaming patterns
- No complex custom backpressure testing
- Focus on LangChain.js built-in capabilities
- Real stream processing validation

### Organized Structure

- Clear separation of test types
- Easy selective test execution
- Focused test categories
- Minimal test maintenance

### Performance Optimized

- Parallel execution where possible
- Retry logic for flaky API tests
- Efficient timeout configuration
- Coverage-focused testing

## 🎤 Voice Cloning Tests

### Overview

The voice cloning system includes comprehensive tests for handling seed files with missing reference audio by using fallback mechanisms.

### Test Files

- **`talkingavatar/voice-cloning-preservation.test.js`** - Tests TalkingAvatar voice preservation logic
- **`modules/talkinghead/src/sparktts-enhanced-fallback.test.js`** - Tests SparkTTS service-level fallback
- **`modules/talkinghead/src/seed-file-fallback.test.js`** - Tests UI-level seed file handling
- **`agent/tools/tts/sparktts-service.test.js`** - Tests core SparkTTS service functionality

### Key Features Tested

- ✅ Voice cloning preservation when switching voices
- ✅ Fallback to regular voices when cloned audio missing
- ✅ Enhanced `getRoles()` integration for voice validation
- ✅ Proper error handling and user notifications
- ✅ Gender-based default voice selection
- ✅ Seed file management and favorite voice handling

### Running Voice Tests

```bash
# Run all voice-related tests
npx vitest run test/talkingavatar/ test/modules/talkinghead/src/ test/agent/tools/tts/

# Run specific voice cloning tests
npx vitest run test/talkingavatar/voice-cloning-preservation.test.js
npx vitest run test/modules/talkinghead/src/sparktts-enhanced-fallback.test.js
npx vitest run test/modules/talkinghead/src/seed-file-fallback.test.js
```

## 🚨 Troubleshooting

### API Connection Issues

```bash
# Check vLLM API availability
curl http://***********:20095/v1/models

# Run tests without real API
npm test  # Uses quick tests by default
```

### Test Failures

```bash
# Run with verbose output
node test/run-tests.js realApi --verbose

# Run specific test file
npx vitest run test/src/agent/streaming/langchain-streaming.test.js
```

### Coverage Issues

```bash
# Generate detailed coverage report
npm run test:coverage

# View coverage in browser
open test/coverage/index.html
```

## 📊 Coverage Targets

| Component | Target | Description |
|-----------|--------|-------------|
| `src/agent/core.js` | 90% | Core agent service |
| `src/agent/streaming/` | 85% | Streaming components |
| `src/agent/tools/` | 85% | Tool implementations |
| `src/utils/apiProxy.ts` | 80% | API utilities |
| **Overall** | 85% | Total coverage |

## 🔄 CI/CD Integration

### GitHub Actions Example

```yaml
- name: Run Quick Tests
  run: npm test

- name: Run Integration Tests
  run: npm run test:integration
  env:
    VITE_VLLM_ENDPOINT: ${{ secrets.VLLM_ENDPOINT }}

- name: Generate Coverage
  run: npm run test:coverage
```

### Local Development

```bash
# Pre-commit hook
npm test && npm run test:unit

# Pre-push hook
npm run test:integration
```

This organized test structure provides comprehensive coverage while maintaining fast feedback loops and clear separation of concerns.
