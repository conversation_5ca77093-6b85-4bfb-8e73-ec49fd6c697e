/**
 * Test Utilities and Helper Functions
 * 
 * Provides mock implementations and testing utilities for E2E tests
 */

/**
 * Creates a mock audio blob for testing voice transcription
 * @param {string} mockTranscript - The text that this audio should transcribe to
 * @param {object} options - Audio simulation options
 * @returns {Blob} Mock audio blob
 */
export function createMockAudio(mockTranscript, options = {}) {
  const {
    duration = 3,
    quality = 'medium',
    sampleRate = 44100,
    channels = 1
  } = options;

  // Create a mock audio buffer with basic properties
  const mockAudioData = new ArrayBuffer(duration * sampleRate * channels * 2);
  const mockBlob = new Blob([mockAudioData], { type: 'audio/wav' });
  
  // Add metadata for testing
  mockBlob._mockMetadata = {
    transcript: mockTranscript,
    duration,
    quality,
    confidence: quality === 'high' ? 0.95 : quality === 'medium' ? 0.8 : 0.6
  };

  return mockBlob;
}

/**
 * Creates a mock character database with test data
 * @returns {object} Mock character database
 */
export function createMockCharacterDatabase() {
  return {
    // One Piece characters
    'monkey_d_luffy': {
      id: 'monkey_d_luffy',
      name: 'Monkey D. Luffy',
      series: 'One Piece',
      description: 'Captain of the Straw Hat Pirates with rubber powers',
      personality: {
        formality: 0.2,
        enthusiasm: 0.9,
        empathy: 0.8,
        creativity: 0.7,
        directness: 0.8
      },
      voiceStyle: 'energetic_casual',
      systemPrompt: 'You are Monkey D. Luffy, the rubber pirate captain who loves adventure and meat!',
      tags: ['pirate', 'rubber', 'captain', 'straw hat'],
      popularity: 0.95
    },
    'roronoa_zoro': {
      id: 'roronoa_zoro',
      name: 'Roronoa Zoro',
      series: 'One Piece',
      description: 'Three-sword style swordsman and first mate of the Straw Hat Pirates',
      personality: {
        formality: 0.4,
        enthusiasm: 0.6,
        empathy: 0.5,
        creativity: 0.6,
        directness: 0.9
      },
      voiceStyle: 'serious_determined',
      systemPrompt: 'You are Roronoa Zoro, the world\'s greatest swordsman-in-training.',
      tags: ['swordsman', 'three sword', 'pirate', 'first mate'],
      popularity: 0.88
    },

    // One-Punch Man characters
    'saitama_one_punch': {
      id: 'saitama_one_punch',
      name: 'Saitama',
      series: 'One-Punch Man',
      description: 'Hero who can defeat any enemy with one punch',
      personality: {
        formality: 0.3,
        enthusiasm: 0.4,
        empathy: 0.6,
        creativity: 0.3,
        directness: 0.9
      },
      voiceStyle: 'deadpan_casual',
      systemPrompt: 'You are Saitama, the bald caped hero who defeats enemies with one punch.',
      tags: ['hero', 'bald', 'one punch', 'strong'],
      popularity: 0.92
    },
    'genos': {
      id: 'genos',
      name: 'Genos',
      series: 'One-Punch Man',
      description: 'Cyborg hero and disciple of Saitama',
      personality: {
        formality: 0.7,
        enthusiasm: 0.8,
        empathy: 0.7,
        creativity: 0.6,
        directness: 0.7
      },
      voiceStyle: 'formal_respectful',
      systemPrompt: 'You are Genos, the cyborg hero and devoted disciple of Saitama-sensei.',
      tags: ['cyborg', 'disciple', 'hero', 'respectful'],
      popularity: 0.85
    }
  };
}

/**
 * Mock voice input simulation
 * @param {string} phrase - The phrase to simulate
 * @param {object} options - Voice simulation options
 */
export function mockVoiceInput(phrase, options = {}) {
  const {
    accent = 'neutral',
    speed = 'normal',
    clarity = 'high'
  } = options;

  return {
    transcript: phrase,
    confidence: clarity === 'high' ? 0.95 : clarity === 'medium' ? 0.8 : 0.6,
    alternatives: [
      { transcript: phrase, confidence: 0.95 },
      { transcript: phrase.toLowerCase(), confidence: 0.9 }
    ],
    metadata: {
      accent,
      speed,
      clarity,
      processingTime: Math.random() * 1000 + 500 // 500-1500ms
    }
  };
}

/**
 * Performance timer utility
 */
export function performanceTimer() {
  const startTime = performance.now();
  
  return {
    end: () => performance.now() - startTime,
    elapsed: () => performance.now() - startTime
  };
}

/**
 * Mock agent service for testing
 */
export function createMockAgentService(options = {}) {
  const mockModels = {
    system1: {
      updateSystemPrompt: jest.fn(),
      invoke: jest.fn(),
      isRealtimeModeActive: jest.fn(() => true),
      generateResponse: jest.fn()
    },
    system2: {
      updateSystemPrompt: jest.fn(),
      invoke: jest.fn(),
      generateResponse: jest.fn()
    }
  };

  return {
    getDualBrainCoordinator: jest.fn(() => ({
      updateSystem1Context: jest.fn(),
      updateSystem2Context: jest.fn(),
      updateCoordinationParameters: jest.fn(),
      generateProactiveDecision: jest.fn(() => ({
        shouldAct: false,
        confidence: 0.7,
        reason: 'test_decision'
      }))
    })),
    updateCharacterContext: jest.fn(),
    getModel: jest.fn((type) => mockModels[type] || mockModels.system1),
    isDualBrainMode: jest.fn(() => true),
    initialize: jest.fn(() => Promise.resolve()),
    generateResponse: jest.fn(() => 'Mock response'),
    initializeRealtimeMode: jest.fn(() => Promise.resolve(true)),
    ...options
  };
}

/**
 * Mock DOM elements for testing
 */
export function createMockDOMElements() {
  // Mock search input
  const searchInput = document.createElement('input');
  searchInput.id = 'character-search';
  searchInput.type = 'text';
  searchInput.placeholder = 'Search for a character...';

  // Mock results container
  const resultsContainer = document.createElement('div');
  resultsContainer.id = 'search-results';
  resultsContainer.className = 'search-results';

  // Mock character preview
  const characterPreview = document.createElement('div');
  characterPreview.id = 'character-preview';
  characterPreview.className = 'character-preview';

  // Mock voice button
  const voiceButton = document.createElement('button');
  voiceButton.id = 'voice-search';
  voiceButton.textContent = '🎤 Voice Search';

  return {
    searchInput,
    resultsContainer,
    characterPreview,
    voiceButton
  };
}

/**
 * Simulate user interactions
 */
export class UserInteractionSimulator {
  constructor(container) {
    this.container = container;
  }

  async typeInSearchBox(text, inputElement) {
    // Simulate typing with realistic timing
    for (let i = 0; i < text.length; i++) {
      inputElement.value = text.substring(0, i + 1);
      inputElement.dispatchEvent(new Event('input', { bubbles: true }));
      await this.delay(50 + Math.random() * 100); // 50-150ms per character
    }
    
    inputElement.dispatchEvent(new Event('change', { bubbles: true }));
  }

  async clickElement(element) {
    element.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
    await this.delay(50);
    element.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));
    element.dispatchEvent(new MouseEvent('click', { bubbles: true }));
  }

  async holdVoiceButton(duration = 3000) {
    const voiceButton = this.container.querySelector('#voice-search');
    if (!voiceButton) return;

    voiceButton.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
    await this.delay(duration);
    voiceButton.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));
  }

  async selectCharacterFromResults(characterName) {
    const resultElements = this.container.querySelectorAll('.character-result');
    const targetElement = Array.from(resultElements).find(
      el => el.textContent.includes(characterName)
    );
    
    if (targetElement) {
      await this.clickElement(targetElement);
      return true;
    }
    return false;
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Test data generators
 */
export class TestDataGenerator {
  static generateRandomCharacter() {
    const names = ['Goku', 'Vegeta', 'Naruto', 'Sasuke', 'Ichigo'];
    const series = ['Dragon Ball', 'Naruto', 'Bleach'];
    
    return {
      id: `test_character_${Math.random().toString(36).substr(2, 9)}`,
      name: names[Math.floor(Math.random() * names.length)],
      series: series[Math.floor(Math.random() * series.length)],
      personality: {
        formality: Math.random(),
        enthusiasm: Math.random(),
        empathy: Math.random(),
        creativity: Math.random(),
        directness: Math.random()
      },
      voiceStyle: 'test_voice',
      systemPrompt: 'Test character prompt',
      tags: ['test', 'generated'],
      popularity: Math.random()
    };
  }

  static generateTestScenarios(count = 10) {
    const scenarios = [];
    for (let i = 0; i < count; i++) {
      scenarios.push({
        searchTerm: `test${i}`,
        expectedResults: Math.floor(Math.random() * 5),
        errorRate: Math.random() * 0.1, // 0-10% error rate
        responseTime: Math.random() * 1000 + 100 // 100-1100ms
      });
    }
    return scenarios;
  }
}

/**
 * Network condition simulator
 */
export class NetworkSimulator {
  static setOffline() {
    Object.defineProperty(navigator, 'onLine', { value: false, writable: true });
  }

  static setOnline() {
    Object.defineProperty(navigator, 'onLine', { value: true, writable: true });
  }

  static simulateSlowConnection() {
    // Mock slow network responses
    const originalFetch = global.fetch;
    global.fetch = (...args) => {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve(originalFetch(...args));
        }, 2000 + Math.random() * 3000); // 2-5 second delay
      });
    };
  }

  static restoreNormalConnection() {
    // Restore original fetch if it was mocked
    if (global.fetch._originalFetch) {
      global.fetch = global.fetch._originalFetch;
    }
  }
}

/**
 * Memory usage tracker
 */
export class MemoryTracker {
  constructor() {
    this.snapshots = [];
  }

  takeSnapshot(label) {
    const snapshot = {
      label,
      timestamp: Date.now(),
      memory: this.getMemoryUsage()
    };
    this.snapshots.push(snapshot);
    return snapshot;
  }

  getMemoryUsage() {
    if (typeof performance !== 'undefined' && performance.memory) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      };
    }
    return { used: 0, total: 0, limit: 0 };
  }

  getMemoryDelta(fromLabel, toLabel) {
    const fromSnapshot = this.snapshots.find(s => s.label === fromLabel);
    const toSnapshot = this.snapshots.find(s => s.label === toLabel);
    
    if (!fromSnapshot || !toSnapshot) {
      return null;
    }

    return {
      usedDelta: toSnapshot.memory.used - fromSnapshot.memory.used,
      totalDelta: toSnapshot.memory.total - fromSnapshot.memory.total,
      timeDelta: toSnapshot.timestamp - fromSnapshot.timestamp
    };
  }

  generateReport() {
    return {
      snapshots: this.snapshots,
      maxMemoryUsed: Math.max(...this.snapshots.map(s => s.memory.used)),
      memoryGrowth: this.snapshots.length > 1 ? 
        this.snapshots[this.snapshots.length - 1].memory.used - this.snapshots[0].memory.used : 0
    };
  }
}