/**
 * End-to-End Testing Helper Functions
 * 
 * Specialized helpers for E2E workflow validation and UI testing
 */

import { UserInteractionSimulator, createMockDOMElements } from './test-helpers.js';

/**
 * Performance timer with detailed metrics
 */
export function performanceTimer() {
  const startTime = performance.now();
  const startMemory = getMemorySnapshot();

  return {
    end: () => {
      const endTime = performance.now();
      const endMemory = getMemorySnapshot();
      
      return {
        duration: endTime - startTime,
        memoryDelta: endMemory.used - startMemory.used,
        timestamp: endTime
      };
    },
    elapsed: () => performance.now() - startTime
  };
}

/**
 * Validates complete UI workflow from start to finish
 * @param {Array} steps - Array of workflow steps to validate
 * @param {Object} context - Test context and expected values
 * @returns {Object} Validation results
 */
export async function validateUIFlow(steps, context) {
  const { searchTerm, expectedCharacter, container } = context;
  const results = {
    success: false,
    completedSteps: [],
    failedSteps: [],
    totalTime: 0,
    stepTimings: {}
  };

  const overallTimer = performanceTimer();

  try {
    // Setup DOM elements for testing
    const elements = setupUIElements(container);
    const simulator = new UserInteractionSimulator(container);

    for (const step of steps) {
      const stepTimer = performanceTimer();
      console.log(`🔄 Executing UI step: ${step}`);

      try {
        switch (step) {
          case 'character_search_input':
            await executeSearchInput(simulator, elements, searchTerm);
            break;

          case 'search_results_display':
            await validateSearchResults(elements, expectedCharacter);
            break;

          case 'character_selection':
            await executeCharacterSelection(simulator, elements, expectedCharacter.name);
            break;

          case 'personality_preview':
            await validatePersonalityPreview(elements, expectedCharacter);
            break;

          case 'confirm_selection':
            await executeConfirmSelection(simulator, elements);
            break;

          case 'role_play_ready':
            await validateRolePlayReady(elements, expectedCharacter);
            break;

          default:
            throw new Error(`Unknown UI step: ${step}`);
        }

        const stepResult = stepTimer.end();
        results.completedSteps.push(step);
        results.stepTimings[step] = stepResult.duration;
        console.log(`✅ UI step completed: ${step} (${stepResult.duration.toFixed(2)}ms)`);

      } catch (error) {
        console.error(`❌ UI step failed: ${step}`, error);
        results.failedSteps.push({ step, error: error.message });
        break; // Stop on first failure
      }
    }

    const overallResult = overallTimer.end();
    results.totalTime = overallResult.duration;
    results.success = results.failedSteps.length === 0;

    console.log(`🎯 UI workflow validation ${results.success ? 'SUCCESS' : 'FAILED'}`);
    console.log(`📊 Total time: ${results.totalTime.toFixed(2)}ms`);

  } catch (error) {
    console.error('❌ UI workflow validation error:', error);
    results.success = false;
    results.error = error.message;
  }

  return results;
}

/**
 * Setup UI elements for testing
 */
function setupUIElements(container) {
  const elements = createMockDOMElements();
  
  // Add elements to container
  container.appendChild(elements.searchInput);
  container.appendChild(elements.resultsContainer);
  container.appendChild(elements.characterPreview);
  container.appendChild(elements.voiceButton);

  // Add additional test elements
  const confirmButton = document.createElement('button');
  confirmButton.id = 'confirm-character';
  confirmButton.textContent = 'Confirm Character';
  confirmButton.style.display = 'none';
  container.appendChild(confirmButton);

  const statusIndicator = document.createElement('div');
  statusIndicator.id = 'role-play-status';
  statusIndicator.className = 'status-indicator';
  container.appendChild(statusIndicator);

  return {
    ...elements,
    confirmButton,
    statusIndicator
  };
}

/**
 * Execute search input step
 */
async function executeSearchInput(simulator, elements, searchTerm) {
  await simulator.typeInSearchBox(searchTerm, elements.searchInput);
  
  // Simulate search API call delay
  await simulator.delay(200);
  
  // Verify input was entered correctly
  if (elements.searchInput.value !== searchTerm) {
    throw new Error(`Search input mismatch: expected "${searchTerm}", got "${elements.searchInput.value}"`);
  }
}

/**
 * Validate search results display
 */
async function validateSearchResults(elements, expectedCharacter) {
  // Simulate search results rendering
  const resultElement = document.createElement('div');
  resultElement.className = 'character-result';
  resultElement.dataset.characterId = expectedCharacter.id;
  resultElement.innerHTML = `
    <div class="character-name">${expectedCharacter.name}</div>
    <div class="character-description">${expectedCharacter.description}</div>
    <div class="character-series">${expectedCharacter.series || 'Unknown Series'}</div>
  `;
  elements.resultsContainer.appendChild(resultElement);

  // Verify results are displayed
  const results = elements.resultsContainer.querySelectorAll('.character-result');
  if (results.length === 0) {
    throw new Error('No search results displayed');
  }

  // Verify expected character is in results
  const expectedResult = Array.from(results).find(
    result => result.dataset.characterId === expectedCharacter.id
  );
  
  if (!expectedResult) {
    throw new Error(`Expected character "${expectedCharacter.name}" not found in results`);
  }
}

/**
 * Execute character selection
 */
async function executeCharacterSelection(simulator, elements, characterName) {
  const characterResult = elements.resultsContainer.querySelector(
    `.character-result .character-name:contains("${characterName}")`
  ) || elements.resultsContainer.querySelector('.character-result');

  if (!characterResult) {
    throw new Error(`Character result for "${characterName}" not found`);
  }

  await simulator.clickElement(characterResult.closest('.character-result'));

  // Verify selection state
  const selectedResult = elements.resultsContainer.querySelector('.character-result.selected');
  if (!selectedResult) {
    // Add selected class for testing
    characterResult.closest('.character-result').classList.add('selected');
  }
}

/**
 * Validate personality preview
 */
async function validatePersonalityPreview(elements, expectedCharacter) {
  // Simulate personality preview rendering
  elements.characterPreview.innerHTML = `
    <div class="character-avatar">
      <img src="${expectedCharacter.avatar || '/default-avatar.png'}" alt="${expectedCharacter.name}" />
    </div>
    <div class="character-details">
      <h3>${expectedCharacter.name}</h3>
      <p class="description">${expectedCharacter.description}</p>
      <div class="personality-traits">
        <div class="trait">Formality: ${Math.round(expectedCharacter.personality.formality * 100)}%</div>
        <div class="trait">Enthusiasm: ${Math.round(expectedCharacter.personality.enthusiasm * 100)}%</div>
        <div class="trait">Empathy: ${Math.round(expectedCharacter.personality.empathy * 100)}%</div>
        <div class="trait">Creativity: ${Math.round(expectedCharacter.personality.creativity * 100)}%</div>
        <div class="trait">Directness: ${Math.round(expectedCharacter.personality.directness * 100)}%</div>
      </div>
      <div class="voice-style">Voice Style: ${expectedCharacter.voiceStyle}</div>
    </div>
  `;
  elements.characterPreview.style.display = 'block';

  // Show confirm button
  const confirmButton = document.getElementById('confirm-character');
  if (confirmButton) {
    confirmButton.style.display = 'block';
  }

  // Verify preview content
  const nameElement = elements.characterPreview.querySelector('h3');
  if (!nameElement || nameElement.textContent !== expectedCharacter.name) {
    throw new Error(`Personality preview name mismatch: expected "${expectedCharacter.name}"`);
  }

  const traits = elements.characterPreview.querySelectorAll('.trait');
  if (traits.length !== 5) {
    throw new Error(`Expected 5 personality traits, found ${traits.length}`);
  }
}

/**
 * Execute confirm selection
 */
async function executeConfirmSelection(simulator, elements) {
  const confirmButton = document.getElementById('confirm-character');
  if (!confirmButton) {
    throw new Error('Confirm button not found');
  }

  if (confirmButton.style.display === 'none') {
    throw new Error('Confirm button not visible');
  }

  await simulator.clickElement(confirmButton);

  // Simulate confirmation processing
  await simulator.delay(500);
}

/**
 * Validate role play ready state
 */
async function validateRolePlayReady(elements, expectedCharacter) {
  const statusIndicator = document.getElementById('role-play-status');
  if (!statusIndicator) {
    throw new Error('Role play status indicator not found');
  }

  // Simulate role play ready state
  statusIndicator.innerHTML = `
    <div class="status-message">✅ Role play ready!</div>
    <div class="active-character">Playing as: ${expectedCharacter.name}</div>
    <div class="character-mood">Mood: ${generateMoodFromPersonality(expectedCharacter.personality)}</div>
  `;
  statusIndicator.className = 'status-indicator ready';

  // Verify ready state
  const statusMessage = statusIndicator.querySelector('.status-message');
  if (!statusMessage || !statusMessage.textContent.includes('ready')) {
    throw new Error('Role play ready state not properly indicated');
  }

  const activeCharacter = statusIndicator.querySelector('.active-character');
  if (!activeCharacter || !activeCharacter.textContent.includes(expectedCharacter.name)) {
    throw new Error(`Active character mismatch: expected "${expectedCharacter.name}"`);
  }
}

/**
 * Generate mood from personality traits
 */
function generateMoodFromPersonality(personality) {
  if (personality.enthusiasm > 0.8) return 'Excited';
  if (personality.empathy > 0.8) return 'Caring';
  if (personality.directness > 0.8) return 'Focused';
  if (personality.creativity > 0.8) return 'Imaginative';
  if (personality.formality > 0.8) return 'Professional';
  return 'Balanced';
}

/**
 * Get memory snapshot for performance tracking
 */
function getMemorySnapshot() {
  if (typeof performance !== 'undefined' && performance.memory) {
    return {
      used: performance.memory.usedJSHeapSize,
      total: performance.memory.totalJSHeapSize,
      limit: performance.memory.jsHeapSizeLimit
    };
  }
  return { used: 0, total: 0, limit: 0 };
}

/**
 * Voice input testing utilities
 */
export class VoiceInputTester {
  constructor(container) {
    this.container = container;
    this.simulator = new UserInteractionSimulator(container);
  }

  async testVoiceSearch(phrase, expectedCharacter) {
    const timer = performanceTimer();

    try {
      // Simulate starting voice recording
      const voiceButton = this.container.querySelector('#voice-search');
      if (!voiceButton) {
        throw new Error('Voice search button not found');
      }

      // Start recording simulation
      await this.simulator.clickElement(voiceButton);
      voiceButton.classList.add('recording');
      voiceButton.textContent = '🔴 Recording...';

      // Simulate speaking duration
      await this.simulator.delay(3000);

      // Stop recording
      await this.simulator.clickElement(voiceButton);
      voiceButton.classList.remove('recording');
      voiceButton.textContent = '⏳ Processing...';

      // Simulate transcription processing
      await this.simulator.delay(1000);

      // Mock transcription result
      const transcriptionResult = {
        success: true,
        transcript: phrase,
        confidence: 0.95
      };

      // Extract character name and trigger search
      const characterName = this.extractCharacterName(phrase);
      if (characterName && characterName.toLowerCase().includes(expectedCharacter.name.toLowerCase())) {
        // Simulate successful search
        voiceButton.textContent = '✅ Character found!';
        
        const result = timer.end();
        return {
          success: true,
          transcription: transcriptionResult,
          characterFound: true,
          processingTime: result.duration
        };
      } else {
        voiceButton.textContent = '❌ Character not found';
        
        const result = timer.end();
        return {
          success: false,
          transcription: transcriptionResult,
          characterFound: false,
          error: 'Character not recognized in voice input',
          processingTime: result.duration
        };
      }

    } catch (error) {
      const result = timer.end();
      return {
        success: false,
        error: error.message,
        processingTime: result.duration
      };
    }
  }

  extractCharacterName(transcript) {
    const commonCharacterNames = ['Luffy', 'Saitama', 'Naruto', 'Goku', 'Zoro', 'Genos'];
    return commonCharacterNames.find(name => 
      transcript.toLowerCase().includes(name.toLowerCase())
    );
  }
}

/**
 * UI Responsiveness Tester
 */
export class ResponsivenessTester {
  constructor(container) {
    this.container = container;
  }

  async testResponsiveLayout(screenSizes) {
    const results = [];

    for (const size of screenSizes) {
      const testResult = await this.testSingleScreenSize(size);
      results.push({ ...testResult, screenSize: size });
    }

    return {
      allPassed: results.every(r => r.passed),
      results,
      summary: this.generateResponsiveSummary(results)
    };
  }

  async testSingleScreenSize(size) {
    // Simulate screen resize
    Object.defineProperty(window, 'innerWidth', { value: size.width, writable: true });
    Object.defineProperty(window, 'innerHeight', { value: size.height, writable: true });
    window.dispatchEvent(new Event('resize'));

    // Give layout time to adjust
    await new Promise(resolve => setTimeout(resolve, 100));

    const checks = {
      searchInputVisible: this.isElementVisible('#character-search'),
      resultsContainerVisible: this.isElementVisible('#search-results'),
      voiceButtonAccessible: this.isElementAccessible('#voice-search'),
      previewAreaUsable: this.isElementUsable('#character-preview'),
      navigationWorking: this.testNavigation(),
      textReadable: this.testTextReadability(),
      buttonsClickable: this.testButtonClickability()
    };

    const passed = Object.values(checks).every(check => check === true);

    return {
      passed,
      checks,
      screenDimensions: `${size.width}x${size.height}`,
      deviceType: this.categorizeDevice(size)
    };
  }

  isElementVisible(selector) {
    const element = this.container.querySelector(selector);
    if (!element) return false;

    const rect = element.getBoundingClientRect();
    return rect.width > 0 && rect.height > 0 && 
           rect.top < window.innerHeight && rect.bottom > 0;
  }

  isElementAccessible(selector) {
    const element = this.container.querySelector(selector);
    if (!element) return false;

    const rect = element.getBoundingClientRect();
    // Check if element is large enough for touch interaction (44px minimum)
    return rect.width >= 44 && rect.height >= 44;
  }

  isElementUsable(selector) {
    const element = this.container.querySelector(selector);
    if (!element) return false;

    // Check if element is not overlapped by other elements
    const rect = element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const elementAtCenter = document.elementFromPoint(centerX, centerY);
    return element.contains(elementAtCenter) || elementAtCenter === element;
  }

  testNavigation() {
    // Test that navigation elements are properly sized and positioned
    const navigationElements = this.container.querySelectorAll('button, input, select');
    return Array.from(navigationElements).every(el => {
      const rect = el.getBoundingClientRect();
      return rect.width >= 32 && rect.height >= 32; // Minimum touch target size
    });
  }

  testTextReadability() {
    // Test that text is readable (not too small)
    const textElements = this.container.querySelectorAll('p, span, div, label');
    return Array.from(textElements).every(el => {
      const style = window.getComputedStyle(el);
      const fontSize = parseInt(style.fontSize);
      return fontSize >= 14; // Minimum readable font size
    });
  }

  testButtonClickability() {
    // Test that buttons have proper spacing and aren't too close together
    const buttons = this.container.querySelectorAll('button');
    if (buttons.length < 2) return true;

    for (let i = 0; i < buttons.length - 1; i++) {
      const rect1 = buttons[i].getBoundingClientRect();
      const rect2 = buttons[i + 1].getBoundingClientRect();
      
      // Calculate distance between button centers
      const dx = (rect2.left + rect2.width / 2) - (rect1.left + rect1.width / 2);
      const dy = (rect2.top + rect2.height / 2) - (rect1.top + rect1.height / 2);
      const distance = Math.sqrt(dx * dx + dy * dy);
      
      if (distance < 56) { // Minimum 56px between button centers
        return false;
      }
    }

    return true;
  }

  categorizeDevice(size) {
    if (size.width <= 480) return 'mobile';
    if (size.width <= 768) return 'tablet-portrait';
    if (size.width <= 1024) return 'tablet-landscape';
    return 'desktop';
  }

  generateResponsiveSummary(results) {
    const passed = results.filter(r => r.passed).length;
    const total = results.length;
    
    return {
      successRate: `${passed}/${total} (${Math.round(passed / total * 100)}%)`,
      failedDeviceTypes: results.filter(r => !r.passed).map(r => r.deviceType),
      commonIssues: this.identifyCommonIssues(results)
    };
  }

  identifyCommonIssues(results) {
    const issues = [];
    const failedResults = results.filter(r => !r.passed);

    failedResults.forEach(result => {
      Object.entries(result.checks).forEach(([check, passed]) => {
        if (!passed) {
          const existingIssue = issues.find(i => i.issue === check);
          if (existingIssue) {
            existingIssue.count++;
            existingIssue.devices.push(result.deviceType);
          } else {
            issues.push({
              issue: check,
              count: 1,
              devices: [result.deviceType]
            });
          }
        }
      });
    });

    return issues.sort((a, b) => b.count - a.count);
  }
}

/**
 * CSS Selector helper that works with contains text
 */
function addContainsSelectorSupport() {
  // Add :contains selector support for testing
  if (!CSS.supports('selector(:contains("text"))')) {
    document.querySelectorAll = function(selector) {
      if (selector.includes(':contains(')) {
        const containsMatch = selector.match(/:contains\("([^"]+)"\)/);
        if (containsMatch) {
          const baseSelector = selector.replace(/:contains\("[^"]+"\)/, '');
          const searchText = containsMatch[1];
          const elements = document.querySelectorAll(baseSelector);
          
          return Array.from(elements).filter(el => 
            el.textContent.includes(searchText)
          );
        }
      }
      
      return Document.prototype.querySelectorAll.call(this, selector);
    };
  }
}

// Initialize CSS selector support
addContainsSelectorSupport();