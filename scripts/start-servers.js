#!/usr/bin/env node
/**
 * Server Startup Script
 * Helps users start all required servers for the Hologram application
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🚀 Starting Hologram Servers...\n');

// Function to start a server in a new process
function startServer(name, command, args = [], options = {}) {
  console.log(`📋 Starting ${name}...`);
  console.log(`   Command: ${command} ${args.join(' ')}\n`);

  const process = spawn(command, args, {
    cwd: projectRoot,
    stdio: 'pipe',
    shell: true,
    ...options
  });

  // Handle stdout
  process.stdout.on('data', (data) => {
    const output = data.toString().trim();
    if (output) {
      console.log(`[${name}] ${output}`);
    }
  });

  // Handle stderr
  process.stderr.on('data', (data) => {
    const output = data.toString().trim();
    if (output) {
      console.error(`[${name} ERROR] ${output}`);
    }
  });

  // Handle process exit
  process.on('close', (code) => {
    if (code === 0) {
      console.log(`✅ ${name} exited successfully`);
    } else {
      console.error(`❌ ${name} exited with code ${code}`);
    }
  });

  // Handle process error
  process.on('error', (error) => {
    console.error(`❌ Failed to start ${name}: ${error.message}`);
  });

  return process;
}

// Check if we should start in development mode
const isDev = process.argv.includes('--dev') || process.argv.includes('-d');
const serverCommand = isDev ? 'server:dev' : 'server';
const wsServerCommand = isDev ? 'ws-server:dev' : 'ws-server';

console.log(`🔧 Mode: ${isDev ? 'Development' : 'Production'}\n`);

// Start the download server (port 2994 by default)
console.log('1️⃣ Starting Download Server (handles API proxies, models, assets)...');
const downloadServer = startServer(
  'Download Server',
  'npm',
  ['run', serverCommand]
);

// Wait a bit for the download server to start
setTimeout(() => {
  console.log('\n2️⃣ Starting WebSocket Server (handles real-time communication)...');
  const wsServer = startServer(
    'WebSocket Server',
    'npm',
    ['run', wsServerCommand]
  );
}, 3000);

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down servers...');

  // Kill all child processes
  downloadServer.kill('SIGTERM');

  setTimeout(() => {
    console.log('✅ All servers shut down');
    process.exit(0);
  }, 2000);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down servers...');
  downloadServer.kill('SIGTERM');

  setTimeout(() => {
    process.exit(0);
  }, 2000);
});

console.log('ℹ️  Server Status:');
console.log('   • Download Server: http://localhost:2994 (or fallback port)');
console.log('   • WebSocket Server: Check console for port information');
console.log('\n⚡ Press Ctrl+C to stop all servers\n');