#!/usr/bin/env node

/**
 * Download MediaPipe WASM files to the correct location
 * This script ensures the WASM files are available for the server to serve
 */

import https from 'https';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const WASM_FILES = [
    'vision_wasm_internal.wasm',
    'vision_wasm_internal.js'
];

const CDN_BASE_URL = 'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@latest/wasm';
const OUTPUT_DIR = path.join(__dirname, '../public/models/mediapipe/wasm');

/**
 * Download a file from URL to local path
 */
function downloadFile(url, outputPath) {
    return new Promise((resolve, reject) => {
        console.log(`📦 Downloading ${path.basename(outputPath)}...`);

        const file = fs.createWriteStream(outputPath);

        https.get(url, (response) => {
            if (response.statusCode !== 200) {
                reject(new Error(`Failed to download ${url}: ${response.statusCode}`));
                return;
            }

            response.pipe(file);

            file.on('finish', () => {
                file.close();
                console.log(`✅ Downloaded ${path.basename(outputPath)}`);
                resolve();
            });

            file.on('error', (err) => {
                fs.unlink(outputPath, () => { }); // Delete incomplete file
                reject(err);
            });
        }).on('error', (err) => {
            reject(err);
        });
    });
}

/**
 * Main download function
 */
async function downloadWasmFiles() {
    try {
        // Ensure output directory exists
        if (!fs.existsSync(OUTPUT_DIR)) {
            fs.mkdirSync(OUTPUT_DIR, { recursive: true });
            console.log(`📁 Created directory: ${OUTPUT_DIR}`);
        }

        console.log('🚀 Starting MediaPipe WASM download...');

        // Download all WASM files
        for (const wasmFile of WASM_FILES) {
            const url = `${CDN_BASE_URL}/${wasmFile}`;
            const outputPath = path.join(OUTPUT_DIR, wasmFile);

            // Skip if file already exists
            if (fs.existsSync(outputPath)) {
                console.log(`⏭️  Skipping ${wasmFile} (already exists)`);
                continue;
            }

            await downloadFile(url, outputPath);
        }

        console.log('✅ All MediaPipe WASM files downloaded successfully!');
        console.log(`📂 Files saved to: ${OUTPUT_DIR}`);

        // List downloaded files
        const files = fs.readdirSync(OUTPUT_DIR);
        console.log('📋 Downloaded files:');
        files.forEach(file => {
            const filePath = path.join(OUTPUT_DIR, file);
            const stats = fs.statSync(filePath);
            console.log(`   - ${file} (${(stats.size / 1024).toFixed(1)} KB)`);
        });

    } catch (error) {
        console.error('❌ Download failed:', error.message);
        process.exit(1);
    }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    downloadWasmFiles();
}

export { downloadWasmFiles };
