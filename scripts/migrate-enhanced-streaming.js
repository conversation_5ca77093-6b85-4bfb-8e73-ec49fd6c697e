#!/usr/bin/env node

/**
 * Migration Script for Enhanced Streaming Architecture
 * 
 * This script helps migrate from the old streaming architecture to the new
 * unified StreamingManager approach with enhanced capabilities.
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

const MIGRATION_PATTERNS = [
    {
        // Update LLMStreamProcessor direct imports to use StreamingManager
        pattern: /import\s+{\s*LLMStreamProcessor\s*}\s+from\s+['"].*?LLMStreamProcessor.*?['"];?/g,
        replacement: `import { StreamingManager } from '@/agent/streaming';`,
        description: 'Replace LLMStreamProcessor imports with StreamingManager'
    },
    {
        // Update StreamingAudioPlayer direct imports to use StreamingManager
        pattern: /import\s+{\s*StreamingAudioPlayer\s*}\s+from\s+['"].*?StreamingAudioPlayer.*?['"];?/g,
        replacement: `import { StreamingManager } from '@/agent/streaming';`,
        description: 'Replace StreamingAudioPlayer imports with StreamingManager'
    },
    {
        // Update ConnectionCoordinationService direct imports
        pattern: /import\s+{\s*ConnectionCoordinationService\s*}\s+from\s+['"].*?ConnectionCoordinationService.*?['"];?/g,
        replacement: `import { StreamingManager } from '@/agent/streaming';`,
        description: 'Replace ConnectionCoordinationService imports with StreamingManager (provider coordination is now built-in)'
    },
    {
        // Update new LLMStreamProcessor() instantiation
        pattern: /new\s+LLMStreamProcessor\s*\(/g,
        replacement: `new StreamingManager(`,
        description: 'Replace LLMStreamProcessor instantiation with StreamingManager'
    },
    {
        // Update new StreamingAudioPlayer() instantiation
        pattern: /new\s+StreamingAudioPlayer\s*\(/g,
        replacement: `// Audio playing is now handled internally by StreamingManager\n// new StreamingManager(`,
        description: 'Replace StreamingAudioPlayer instantiation (now handled internally)'
    },
    {
        // Update new ConnectionCoordinationService() instantiation
        pattern: /new\s+ConnectionCoordinationService\s*\(/g,
        replacement: `// Provider coordination is now built into StreamingManager\n// new StreamingManager(`,
        description: 'Replace ConnectionCoordinationService instantiation (now built-in)'
    }
];

function migrateFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    let newContent = content;
    let hasChanges = false;

    MIGRATION_PATTERNS.forEach(({ pattern, replacement, description }) => {
        if (pattern.test(content)) {
            console.log(`  ✓ ${description}`);
            newContent = newContent.replace(pattern, replacement);
            hasChanges = true;
        }
    });

    if (hasChanges) {
        fs.writeFileSync(filePath, newContent);
        return true;
    }
    return false;
}

function runMigration() {
    console.log('🔄 Starting Enhanced Streaming Architecture Migration...\n');

    // Find all JS/TS files in the project
    const files = glob.sync('**/*.{js,ts,jsx,tsx}', {
        ignore: [
            'node_modules/**',
            'dist/**',
            'build/**',
            'coverage/**',
            '.git/**',
            'src/agent/streaming/**', // Skip the streaming directory itself
            'test/**' // Skip test files for now
        ]
    });

    let totalFiles = 0;
    let migratedFiles = 0;

    files.forEach(file => {
        console.log(`📁 Processing: ${file}`);
        totalFiles++;
        
        try {
            if (migrateFile(file)) {
                migratedFiles++;
                console.log(`  ✅ File migrated successfully\n`);
            } else {
                console.log(`  ⏭️  No changes needed\n`);
            }
        } catch (error) {
            console.log(`  ❌ Error migrating file: ${error.message}\n`);
        }
    });

    console.log('📊 Migration Summary:');
    console.log(`   Total files processed: ${totalFiles}`);
    console.log(`   Files migrated: ${migratedFiles}`);
    console.log(`   Files unchanged: ${totalFiles - migratedFiles}`);

    if (migratedFiles > 0) {
        console.log('\n📝 Next Steps:');
        console.log('1. Update your code to use StreamingManager API');
        console.log('2. Test the migrated functionality');
        console.log('3. Remove any manual audio/provider coordination code');
        console.log('4. See src/agent/streaming/README.md for usage examples');
    }

    console.log('\n✨ Migration completed!');
}

if (require.main === module) {
    runMigration();
}

module.exports = { runMigration, migrateFile, MIGRATION_PATTERNS };
