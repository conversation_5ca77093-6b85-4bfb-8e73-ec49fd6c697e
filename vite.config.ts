import { defineConfig, loadEnv } from 'vite'
import type { ViteDevServer, Connect } from 'vite'
import path from 'path'
import fs from 'fs'
import wasm from "vite-plugin-wasm"
import topLevelAwait from "vite-plugin-top-level-await"
import { APPS } from './app/index.js'

interface App {
  name: string;
  entry: string;
}

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // Load ALL environment variables from .env file with empty prefix
  // This loads variables from .env, .env.local, .env.[mode], and .env.[mode].local
  const env = loadEnv(mode, process.cwd(), '');

  // Log loaded environment variables for debugging (excluding sensitive ones)
  console.log('Loaded environment variables:', Object.keys(env)
    .filter(key => key.startsWith('VITE_'))
    .map(key => `${key}: ${key.includes('KEY') || key.includes('API') || key.includes('SECRET') ? '[REDACTED]' : env[key]}`))

  const appName = process.env.APP_NAME || 'viewer'
  const app = (Object.values(APPS) as App[]).find((a) => a.name === appName)

  if (!app) {
    throw new Error(`App "${appName}" not found in registry`)
  }

  console.log('App entry path:', path.resolve(__dirname, app.entry))

  return {
    // Define environment variables for the client
    define: {
      // Make environment variables available to the client
      // This ensures that import.meta.env has all the variables from .env
      ...Object.fromEntries(
        Object.entries(env).filter(([key]) => key.startsWith('VITE_')).map(([key, val]) => [
          `import.meta.env.${key}`,
          JSON.stringify(val)
        ])
      )
    },
    plugins: [
      wasm(),
      topLevelAwait()
    ],
    worker: {
      plugins: () => [wasm()],
      format: 'es'
    },
    root: '.',
    publicDir: 'public',
    build: {
      target: 'esnext',
      outDir: `dist/${app.name}`,
      assetsDir: 'assets',
      rollupOptions: {
        input: path.resolve(__dirname, app.entry),
        external: [
          'mem0ai',
          '@xenova/transformers',
          // Node.js built-in modules - externalize for browser compatibility (except async_hooks which we polyfill)
          'fs',
          'node:fs',
          'node:fs/promises',
          'path',
          'node:path',
          'os',
          'node:os',
          'child_process',
          'node:child_process',
          'util',
          'node:util',
          // Server-side packages that should not be bundled in browser
          'node-fetch',
          'fetch-blob'
          // NOTE: async_hooks and node:async_hooks are NOT external - we polyfill them via alias
        ] // Exclude optional dependencies and Node.js modules from bundle to prevent build errors
      }
    },
    server: {
      port: 3002,
      open: app.entry,
      fs: {
        // Allow serving files from one level up to the project root
        allow: ['..']
      },
      // Configure static file serving
      middlewares: [
        {
          name: 'serve-static',
          configureServer(server: ViteDevServer) {
            server.middlewares.use((req: Connect.IncomingMessage, res: any, next: Connect.NextFunction) => {
              // Ensure /assets requests are handled properly
              if (req.url?.startsWith('/assets/')) {
                req.url = `/public${req.url}`;
              }
              next();
            });
          }
        }
      ]
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@apps': path.resolve(__dirname, './app'),
        // Browser compatibility aliases for Node.js modules
        'async_hooks': path.resolve(__dirname, './src/agent/memory/async-hooks-polyfill.js'),
        'node:async_hooks': path.resolve(__dirname, './src/agent/memory/async-hooks-polyfill.js')
      },
      extensions: ['.ts', '.js', '.tsx', '.jsx']
    },
    optimizeDeps: {
      include: [
        'draco3d',
        '@dimforge/rapier3d-compat',
        '@babylonjs/core',
        '@babylonjs/loaders',
        // LangChain core dependencies for better Vite optimization
        '@langchain/core/output_parsers',
        '@langchain/core/prompts',
        'langchain/vectorstores/memory',
        'langchain/retrievers/multi_query'
      ],
      exclude: [
        '@dimforge/rapier3d',
        '@babylonjs/havok',
        // Exclude problematic LangChain modules that should be loaded dynamically
        '@langchain/openai',
        '@langchain/community/embeddings/hf_transformers'
      ]
    }
  }
})