#!/usr/bin/env node

/**
 * Debug Tool Calling Functionality
 * 
 * This script tests the complete tool calling pipeline to identify
 * why System 2 is not executing tools that should trigger audio output.
 */

import { createLogger, LogLevel } from './src/utils/logger.ts';
import { AgentService } from './src/agent/core.js';
import { getCompleteAgentConfig } from './src/agent/config/index.js';

const logger = createLogger('ToolCallingDebugger', LogLevel.DEBUG);

async function debugToolCalling() {
    try {
        logger.info('🔍 Starting Tool Calling Debug Session...');

        // Initialize agent service with dual brain mode
        const config = getCompleteAgentConfig();
        const agentService = new AgentService(config);
        
        logger.info('📊 Configuration Analysis:', {
            dualBrainMode: config.mode === 'dual_brain',
            system1Model: config.model.system1,
            system2Model: config.model.system2,
            toolsEnabled: !!config.tools?.enabled
        });

        // Initialize the service
        await agentService.initialize();

        // Test 1: Check if tools are properly loaded
        logger.info('\n🧪 TEST 1: Tool Loading Verification');
        const tools = agentService.tools || [];
        logger.info(`📋 Available tools: ${tools.length}`, {
            toolNames: tools.map(t => t.name || 'unnamed'),
            hasAudioTools: tools.some(t => t.name?.includes('audio') || t.name?.includes('speak')),
            firstTool: tools[0] ? {
                name: tools[0].name,
                hasInvoke: !!tools[0].invoke,
                hasFunc: !!tools[0].func
            } : null
        });

        // Test 2: Check System 2 model tool binding
        logger.info('\n🧪 TEST 2: System 2 Model Tool Binding');
        const system2Model = agentService.getModel('system2');
        if (system2Model) {
            logger.info('🔧 System 2 Model Analysis:', {
                modelType: system2Model.constructor.name,
                hasBoundTools: !!(system2Model.boundTools && system2Model.boundTools.length > 0),
                boundToolsCount: system2Model.boundTools?.length || 0,
                boundToolNames: system2Model.boundTools?.map(t => t.name) || [],
                hasBindToolsMethod: typeof system2Model.bindTools === 'function'
            });

            // Test tool binding
            if (tools.length > 0 && typeof system2Model.bindTools === 'function') {
                try {
                    const boundModel = system2Model.bindTools(tools);
                    logger.info('✅ Tool binding test successful:', {
                        originalToolsCount: system2Model.boundTools?.length || 0,
                        newBoundToolsCount: boundModel.boundTools?.length || 0,
                        toolNames: boundModel.boundTools?.map(t => t.name) || []
                    });
                } catch (error) {
                    logger.error('❌ Tool binding failed:', error.message);
                }
            }
        } else {
            logger.error('❌ System 2 model not found');
        }

        // Test 3: Test actual tool calling scenario
        logger.info('\n🧪 TEST 3: Tool Calling Scenario Test');
        
        // Create a prompt that should trigger tool usage
        const testPrompt = "I want to hear some audio. Please play a sound or speak something out loud.";
        
        logger.info(`📝 Testing prompt: "${testPrompt}"`);
        
        try {
            const response = await agentService.generateResponse(testPrompt, {
                enableToolCalling: true,
                targetSystem: 'system2'
            });

            logger.info('📋 Response Analysis:', {
                hasResponse: !!response,
                responseType: typeof response,
                hasToolCalls: !!(response?.tool_calls?.length > 0 || response?.toolCalls?.length > 0),
                toolCallCount: response?.tool_calls?.length || response?.toolCalls?.length || 0,
                hasContent: !!response?.content,
                contentLength: response?.content?.length || 0,
                responseKeys: response ? Object.keys(response) : []
            });

            if (response?.tool_calls?.length > 0 || response?.toolCalls?.length > 0) {
                const toolCalls = response.tool_calls || response.toolCalls || [];
                logger.info('🎯 Tool Calls Detected:', {
                    count: toolCalls.length,
                    calls: toolCalls.map(call => ({
                        id: call.id,
                        name: call.name || call.function?.name,
                        args: call.args || call.function?.arguments
                    }))
                });
            } else {
                logger.warn('⚠️ No tool calls detected in response');
                logger.debug('🔍 Raw response structure:', response);
            }

        } catch (error) {
            logger.error('❌ Tool calling test failed:', error.message);
            logger.debug('Error details:', error);
        }

        // Test 4: Check agent service tool execution pipeline
        logger.info('\n🧪 TEST 4: Agent Service Tool Pipeline');
        const isDualBrain = agentService.isDualBrainMode();
        logger.info('🧠 Dual Brain Mode Analysis:', {
            isDualBrainMode: isDualBrain,
            hasSystem1: !!agentService.getModel('system1'),
            hasSystem2: !!agentService.getModel('system2'),
            hasDualBrainCoordinator: !!agentService.dualBrainCoordinator
        });

        // Test 5: Check LangGraph configuration
        logger.info('\n🧪 TEST 5: LangGraph Configuration Check');
        if (agentService.graph) {
            logger.info('📊 LangGraph Analysis:', {
                hasGraph: !!agentService.graph,
                graphType: agentService.graph.constructor.name,
                hasInvoke: typeof agentService.graph.invoke === 'function'
            });
        } else {
            logger.warn('⚠️ No LangGraph instance found');
        }

        // Test 6: Manual tool execution test
        logger.info('\n🧪 TEST 6: Manual Tool Execution Test');
        if (tools.length > 0) {
            const testTool = tools.find(t => t.name?.includes('speak') || t.name?.includes('audio')) || tools[0];
            if (testTool) {
                logger.info(`🔧 Testing tool: ${testTool.name}`);
                try {
                    const toolResult = await testTool.invoke({ 
                        text: "Test audio output" 
                    });
                    logger.info('✅ Manual tool execution successful:', toolResult);
                } catch (error) {
                    logger.error('❌ Manual tool execution failed:', error.message);
                }
            }
        }

        logger.info('\n✅ Debug session completed');

    } catch (error) {
        logger.error('❌ Debug session failed:', error);
        process.exit(1);
    }
}

// Run the debug session
debugToolCalling().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
});