# Architecture Diagrams

This directory contains all C4 Model architecture diagrams for the Hologram Software system.

## 📁 Directory Structure

### 🏗️ **System Architecture**
- **[spec.c4](./spec.c4)** - C4 Model specification and element definitions
- **[containers/](./containers/)** - Container-level architecture (C4 Level 2)
  - `system.c4` - Main system containers overview
  - `applications.c4` - Application containers and their relationships

### 🔧 **Components**
- **[components/](./components/)** - Component-level diagrams (C4 Level 3)
  - `agent.c4` - AI agent system components  
  - `media.c4` - Media processing components
  - `ui.c4` - User interface components
  - `viewer.c4` - 3D viewer components

### 🔄 **Process Flows**
- **[flows/](./flows/)** - Detailed process and data flows
  - `http-request-flow.c4` - HTTP request processing flow
  - `llm-api-processing-flow.c4` - LLM API processing workflow
  - `media-streaming.c4` - Media streaming architecture
  - `websocket-proxy-flow.c4` - WebSocket proxy flow
  - `error-recovery-flow.c4` - Error handling and recovery

### 🚀 **Deployment**
- **[deployment/](./deployment/)** - Deployment architecture
  - `development.c4` - Development environment setup

### 🔧 **System Infrastructure**
- **[system/](./system/)** - System-level infrastructure diagrams

## 🎯 Updated Architecture Highlights

### System Capabilities Verification ✅

#### System 1 (WebSocket) - **Real-time Brain**
- ❌ **Tool Calling**: Not supported by Qwen-Omni realtime
- ✅ **Audio Streaming**: Native real-time audio processing  
- ✅ **Text Streaming**: Real-time text response streaming
- ⚠️ **JSON Output**: Text-based JSON only, no structured output

#### System 2 (HTTP) - **Reasoning Brain**  
- ✅ **Tool Calling**: Full LangGraph tool integration
- ✅ **Structured Output**: Complete JSON and structured responses
- ✅ **Complex Reasoning**: Advanced reasoning capabilities
- ✅ **Memory Management**: Persistent conversation memory

### Dual-Brain Coordination ✅
- **Context Bridge**: AliyunModelFactory coordinates between systems
- **Intelligent Routing**: Tasks routed based on verified capabilities
- **Proactive Analysis**: ContextualAnalysisService monitors engagement

## 🔄 Building Architecture Diagrams

```bash
# Generate all architecture diagrams
cd docs/arch
./build-architecture.sh

# View generated diagrams
open generated/index.html
```

## 📊 Architecture Validation

The architecture has been verified against actual system capabilities:

- **Tool Calling Limitations**: System 1 cannot execute tools (verified in code)
- **Streaming Integration**: Both systems properly integrate with StreamingManager
- **Context Coordination**: Dual-brain architecture correctly compensates for individual system limitations

## 🔗 Related Documentation

- **[Main Architecture](../ARCHITECTURE.md)** - Complete architecture overview
- **[Production Readiness](../PRODUCTION_READINESS_ASSESSMENT.md)** - Deployment readiness
- **[Streaming Guide](../STREAMING_ARCHITECTURE_GUIDE.md)** - Streaming implementation details

---

**Last Updated**: Current Session - Architecture verification complete  
**Validation Status**: All diagrams reflect verified system capabilities