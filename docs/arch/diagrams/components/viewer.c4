/**
 * Viewer Application Components - Level 3 (C4 Model)
 * Detailed view of the 3D viewer application components
 * Enhanced with FILE_PATH_ENHANCEMENT_GUIDE.md patterns
 * 
 * Based on actual app/viewer/ analysis ✅ VERIFIED:
 * - viewer.js (61KB, 1576 lines) - <PERSON>IN VIEWER CLASS
 * - ui.js (100KB, 2681 lines) - MASSIVE UI MANAGEMENT SYSTEM
 * - gestures.js (54KB, 1434 lines) - GESTURE CONTROL SYSTEM  
 * - talkingavatar.js (103KB, 2613 lines) - AVATAR INTERACTION SYSTEM
 * - photoCapture.js (21KB, 587 lines) - PHOTO CAPTURE SYSTEM
 */

model {
  // ===== CORE VIEWER SYSTEM =====
  
  viewerCore = component 'Viewer Core (MAIN)' {
    description 'Main 3D viewer application with Three.js rendering ✅ VERIFIED (61KB, 1576 lines)'
    technology 'Three.js + WebXR + OrbitControls + Agent integration + Environment rendering'
    
    link ../../../app/viewer/viewer.js 'Viewer Core Source Implementation'
    
    metadata {
      filePath 'app/viewer/viewer.js'
      fileSize '61KB (1576 lines)'
      mainClass 'Viewer extends Base'
      coreFunctions 'initViewer(), loadDefaultAssets(), setupUI(), _sendCameraState()'
      renderingEngine 'Three.js with WebXR support'
      baseClass 'Extends app/base.js for core functionality'
      integrations 'Agent-based architecture (replaced Gradio), TalkingAvatar, GestureController'
      testCoverage 'Production-ready 3D viewer with mobile support'
    }
  }
  
  uiManagementSystem = component 'UI Management System (MASSIVE)' {
    description 'Comprehensive UI management and control system ✅ VERIFIED (100KB, 2681 lines)'
    technology 'Dynamic UI generation + Component management + Event handling + Audio transcription'
    
    link ../../../app/viewer/ui.js 'UI Management Source Implementation'
    
    metadata {
      filePath 'app/viewer/ui.js'
      fileSize '100KB (2681 lines)'
      mainClass 'UISettings'
      coreFunctions 'initialize(), setupNotifications(), createProgressUI(), setupGestureControls()'
      uiComponents 'MeshSelector, CameraViewer, VRButton, Progress indicators'
      systemCapabilities 'Notification system, Progress tracking, Audio transcription workflows'
      dynamicFeatures 'Gesture UI indicators, Zoom controls, Debug overlays'
    }
  }
  
  gestureControlSystem = component 'Gesture Control System' {
    description 'Advanced gesture recognition and control system ✅ VERIFIED (54KB, 1434 lines)'
    technology 'MediaPipe + Pose detection + Gesture recognition + Camera controls'
    
    link ../../../app/viewer/gestures.js 'Gesture Control Source Implementation'
    
    metadata {
      filePath 'app/viewer/gestures.js'
      fileSize '54KB (1434 lines)'
      mainClass 'GestureController'
      coreFunctions 'initialize(), startGestureRecognition(), handleGestureInput(), processLandmarks()'
      recognitionTypes 'Hand gestures, Pose detection, Camera control gestures'
      mediaIntegration 'MediaPipe pose detection, Canvas visualization'
      controlCapabilities 'Camera orbit, Zoom, Model manipulation'
    }
  }
  
  talkingAvatarSystem = component 'Talking Avatar System (MASSIVE)' {
    description 'Comprehensive avatar interaction and conversation system ✅ VERIFIED (103KB, 2613 lines)'
    technology 'LangGraph agent integration + TTS + Animation + Realtime streaming + Dual-brain architecture'
    
    link ../../../app/viewer/talkingavatar.js 'Talking Avatar Source Implementation'
    
    metadata {
      filePath 'app/viewer/talkingavatar.js'
      fileSize '103KB (2613 lines)'
      mainClass 'TalkingAvatar'
      coreFunctions 'initialize(), initializeAgent(), startConversation(), handleRealtimeAudio()'
      agentIntegration 'LangGraphAgentService with ReactAgent workflows'
      audioCapabilities 'MediaCaptureManager integration, Realtime streaming, TTS playback'
      conversationFeatures 'Context awareness, Memory persistence, Tool execution'
      architecturalPattern 'Dual-brain architecture (System 1: fast responses, System 2: deliberate reasoning)'
    }
  }
  
  photoCaptureSystem = component 'Photo Capture System' {
    description 'Professional photo capture and processing system ✅ VERIFIED (21KB, 587 lines)'
    technology 'Canvas rendering + Image processing + Download management'
    
    link ../../../app/viewer/photoCapture.js 'Photo Capture Source Implementation'
    
    metadata {
      filePath 'app/viewer/photoCapture.js'
      fileSize '21KB (587 lines)'
      mainClass 'PhotoCapture'
      coreFunctions 'capturePhoto(), processImage(), downloadImage(), setupUI()'
      captureFeatures 'High-resolution capture, Multiple formats, Background removal'
      imageProcessing 'Canvas-based rendering, Quality optimization, Format conversion'
    }
  }
  
  // ===== CONFIGURATION SYSTEM =====
  
  viewerConfiguration = component 'Viewer Configuration' {
    description 'Centralized configuration management for viewer components'
    technology 'Modular configuration + Asset management + UI settings'
    
    link ../../../app/viewer/viewerConfig.js 'Configuration Source Implementation'
    
    metadata {
      filePath 'app/viewer/viewerConfig.js'
      fileSize '7.3KB (250 lines)'
      configSections 'SCENE_CONFIG, ANIMATION_CONFIG, ASSETS, UI_CONFIG, CONTROLS_CONFIG, ENVIRONMENT_CONFIG'
      coreFunctions 'Configuration export and management'
      configTypes 'Scene settings, Animation parameters, Asset paths, UI properties'
    }
  }
  
  // ===== INTEGRATION COMPONENTS =====
  
  poseDetection = component 'Pose Detection' {
    description 'MediaPipe-based pose and gesture detection'
    technology 'MediaPipe + Canvas visualization + Real-time processing'
    
    link ../../../src/recognition/poseDetector.js 'Pose Detection Source Implementation'
    
    metadata {
      filePath 'src/recognition/poseDetector.js'
      detectionModes 'Hand tracking, Pose estimation, Gesture recognition'
      visualizationSupport 'Canvas2DVisualizer integration'
    }
  }
  
  avatarMeshDetection = component 'Avatar Mesh Detection' {
    description 'Automatic avatar mesh detection and analysis'
    technology 'TypeScript + Three.js mesh analysis'
    
    link ../../../src/viewer/detection/AvatarMeshDetector.ts 'Avatar Mesh Detection Source'
    
    metadata {
      filePath 'src/viewer/detection/AvatarMeshDetector.ts'
      coreFunctions 'detectAvatarMesh()'
      capabilities 'Mesh analysis, Avatar identification'
    }
  }
  
  // ===== UI COMPONENT INTEGRATIONS =====
  
  meshSelector = component 'Mesh Selector' {
    description 'Interactive mesh selection and management UI'
    technology 'Dynamic UI + File management + Three.js integration'
    
    link ../../../src/ui/components/MeshSelector.js 'Mesh Selector Source'
    
    metadata {
      filePath 'src/ui/components/MeshSelector.js'
      uiFeatures 'File selection, Delete functionality, Dynamic loading'
      integration 'viewerConfig.js UI_CONFIG properties'
    }
  }
  
  // ===== EFFECTS AND VISUALIZATION =====
  
  modelEffects = component 'Model Effects' {
    description 'Visual effects for 3D models and scenes'
    technology 'Three.js effects + Animation systems'
    
    link ../../../src/effects/modelEffects.js 'Model Effects Source'
    
    metadata {
      filePath 'src/effects/modelEffects.js'
      effectTypes 'FloatingEffect, TrackballEffect'
      integration 'viewer.js effects system'
    }
  }
  
  // Note: cameraViewer and visualizers are defined in components/ui.c4 to avoid duplication
  
  // ===== COMPONENT RELATIONSHIPS =====
  
  // Core viewer relationships
  viewerCore -> uiManagementSystem 'UI component initialization and management'
  viewerCore -> gestureControlSystem 'Gesture recognition and camera controls'
  viewerCore -> talkingAvatarSystem 'Avatar conversation and interaction'
  viewerCore -> photoCaptureSystem 'Photo capture functionality'
  viewerCore -> viewerConfiguration 'Configuration and settings management'
  
  // UI system relationships
  uiManagementSystem -> meshSelector 'Mesh selection UI management'
  uiManagementSystem -[external]-> cameraViewer 'Camera viewer UI management (defined in ui.c4)'
  uiManagementSystem -> modelEffects 'Visual effects integration'
  
  // Gesture system relationships
  gestureControlSystem -> poseDetection 'Pose and gesture detection'
  gestureControlSystem -[external]-> visualizers 'Gesture visualization (defined in ui.c4)'
  
  // Talking avatar system relationships
  talkingAvatarSystem -> avatarMeshDetection 'Avatar mesh identification'
  talkingAvatarSystem -[external]-> agentCore 'LangGraph agent integration'
  talkingAvatarSystem -[external]-> mediaCaptureManager 'Media capture integration'
  
  // Configuration relationships
  viewerConfiguration -> viewerCore 'Core viewer settings'
  viewerConfiguration -> uiManagementSystem 'UI configuration properties'
  
  // External integrations
  viewerCore -[external]-> baseApplication 'Base application functionality'
}

