/**
 * UI Layer Components - Level 3 (C4 Model)
 * Detailed view of user interface components
 * 
 * Based on actual src/ui/ analysis:
 * - components/ (UI component library)
 * - modules/ (specialized UI modules)  
 * - settings.js, settingsPanel.js (configuration UI)
 * - styles/ (CSS and styling system)
 */

model {
  // ===== UI LAYER COMPONENTS =====
  
  // ===== CORE UI COMPONENTS =====
  
  settingsManager = component 'Settings Manager' {
    description 'Application settings and configuration management'
    technology 'JavaScript + Local Storage + Configuration API'
    
    metadata {
      filePath 'src/ui/settings.js'
      fileSize '2.5KB (87 lines)'
      coreFunctions 'loadSettings(), saveSettings(), validateSettings(), getDefaultSettings()'
      storageType 'LocalStorage + Configuration objects'
    }
  }
  
  settingsPanel = component 'Settings Panel' {
    description 'Interactive settings UI panel with controls and validation'
    technology 'DOM manipulation + Event handling + Form validation'
    
    metadata {
      filePath 'src/ui/settingsPanel.js'
      fileSize '6.3KB (199 lines)'
      coreFunctions 'createPanel(), renderControls(), handleInput(), validateForm()'
      uiFeatures 'Form controls, validation, real-time updates'
    }
  }
  
  // ===== COMPONENT LIBRARY =====
  
  componentLibrary = component 'Component Library' {
    description 'Reusable UI components and widgets'
    technology 'Web Components + Custom Elements + CSS Modules'
    
    metadata {
      filePath 'src/ui/components/'
      components 'Button.js, CameraCapture.js, cameraViewer.js, visualizers/'
      coreFunctions 'Component creation, lifecycle management, event handling'
    }
  }
  
  buttonComponent = component 'Button Component' {
    description 'Interactive button with state management'
    technology 'Custom button implementation + State tracking'
    
    metadata {
      filePath 'src/ui/components/Button.js'
      coreFunctions 'createButton(), handleClick(), updateState(), setDisabled()'
      features 'State management, event handling, styling'
    }
  }
  
  cameraCapture = component 'Camera Capture Component' {  
    description 'Camera capture UI with preview and controls'
    technology 'MediaStream UI + Canvas preview + Control interface'
    
    metadata {
      filePath 'src/ui/components/CameraCapture.js'
      coreFunctions 'initializeCamera(), showPreview(), captureFrame(), stopCapture()'
      features 'Camera preview, capture controls, frame extraction'
    }
  }
  
  cameraViewer = component 'Camera Viewer' {
    description 'Real-time camera viewing and display component'
    technology 'Video element + MediaStream display + Controls'
    
    metadata {
      filePath 'src/ui/components/cameraViewer.js'
      coreFunctions 'displayStream(), updateViewer(), handleResize(), toggleControls()'
      features 'Real-time display, responsive sizing, control overlay'
    }
  }
  
  // ===== VISUALIZATION COMPONENTS =====
  
  visualizers = component 'Visualizers' {
    description 'Data visualization and debugging components'
    technology 'Canvas rendering + WebGL + Data visualization'
    
    metadata {
      filePath 'src/ui/components/visualizers/'
      components 'Various visualization components for debugging and display'
      coreFunctions 'renderVisualization(), updateData(), handleInteraction()'
    }
  }
  
  // ===== SPECIALIZED MODULES =====
  
  uiModules = component 'UI Modules' {
    description 'Specialized UI modules for specific functionality'
    technology 'Modular UI architecture + Specialized interfaces'
    
    metadata {
      filePath 'src/ui/modules/'
      modules 'Text2mesh/ and other specialized UI modules'
      coreFunctions 'Module initialization, specialized UI handling'
    }
  }
  
  text2meshModule = component 'Text2Mesh Module' {
    description 'Text to 3D mesh conversion UI module'
    technology '3D text rendering + Mesh generation + UI controls'
    
    metadata {
      filePath 'src/ui/modules/Text2mesh/'
      files 'JavaScript and CSS files for 3D text interface'
      coreFunctions 'textToMesh(), render3DText(), updateMeshUI()'
    }
  }
  
  // ===== STYLING SYSTEM =====
  
  stylingSystem = component 'Styling System' {
    description 'CSS styling and theming system'
    technology 'CSS modules + Responsive design + Theme management'
    
    metadata {
      filePath 'src/ui/styles/'  
      mainFile 'main.css'
      features 'Responsive design, theming, component styling'
    }
  }
  
  // ===== COMPONENT RELATIONSHIPS =====
  
  // Settings system
  settingsManager -> settingsPanel 'Provides settings data and validation'
  settingsPanel -> settingsManager 'Updates settings based on user input'
  
  // Component library relationships
  componentLibrary -> buttonComponent 'Provides button implementation'
  componentLibrary -> cameraCapture 'Provides camera UI components'
  componentLibrary -> cameraViewer 'Provides camera display components'
  
  // Visualization system
  componentLibrary -> visualizers 'Provides visualization components'
  visualizers -> stylingSystem 'Uses styling for visual components'
  
  // Module system
  uiModules -> text2meshModule 'Specialized 3D text interface module'
  text2meshModule -> stylingSystem 'Uses CSS for 3D text UI'
  
  // Styling coordination
  settingsPanel -> stylingSystem 'Uses styles for settings interface'
  componentLibrary -> stylingSystem 'Uses styles for all components'
  
  // External integrations (to other containers)
  cameraCapture -[external]-> mediaCaptureManager 'Triggers camera capture operations'
  settingsManager -[external]-> agentContainer 'Loads and saves configuration'
}

