/**
 * Development Deployment View - Level 4 (C4 Model)
 * Shows deployment architecture for development environment
 * 
 * Based on actual development setup and server configuration
 */

model {
  // ===== DEVELOPMENT DEPLOYMENT =====
  
  // ===== DEVELOPMENT MACHINE =====
  
  developerMachine = server 'Developer Machine' {
    description 'Local development environment (macOS/Linux/Windows)'
    technology 'Node.js 18+, npm/yarn, Git, VS Code'
    
    metadata {
      os 'macOS 24.4.0 (user example), Linux, Windows'
      nodeVersion 'Node.js 18+'
      packageManager 'npm/yarn'
      ide 'VS Code with extensions'
    }
  }
  
  // ===== LOCAL SERVICES =====
  
  viteDevServer = server 'Vite Development Server' {
    description 'Fast development server with HMR'
    technology 'Vite + Hot Module Replacement + TypeScript'
    
    metadata {
      configFile 'vite.config.ts'
      port '5173 (default)'
      features 'HMR, TypeScript compilation, asset bundling'
    }
  }
  
  nodeServer = server 'Node.js Application Server' {
    description 'Backend API server for development'
    technology 'Express.js + middleware stack'
    
    metadata {
      entryPoint 'server.js'
      middleware 'CORS, logging, error handling'
      routes 'src/server/routes/'
    }
  }
  
  nginxProxy = server 'Nginx Development Proxy' {
    description 'Optional local proxy for testing'
    technology 'Nginx reverse proxy'
    
    metadata {
      configFile 'nginx.conf'
      purpose 'Local load balancing and SSL testing'
    }
  }
  
  // ===== CONTAINERIZED SERVICES =====
  
  dockerServices = server 'Docker Development Services' {
    description 'Containerized supporting services'
    technology 'Docker Compose + Container orchestration'
    
    metadata {
      services 'Database, Redis, external service mocks'
      configFile 'docker-compose.yml'
      purpose 'Consistent development environment'
    }
  }
  
  // ===== EXTERNAL DEVELOPMENT SERVICES =====
  
  cloudLLMDev = cloud 'Cloud LLM Services (Dev)' {
    description 'Development/testing endpoints for AI services'
    technology 'OpenAI, Anthropic, Aliyun development APIs'
    
    metadata {
      purpose 'AI model testing with development quotas'
      configuration 'Development API keys and endpoints'
    }
  }
  
  mediaServicesDev = cloud 'Media Services (Dev)' {
    description 'Development endpoints for speech/vision services'
    technology 'Development TTS/STT, computer vision APIs'
    
    metadata {
      purpose 'Media processing testing'
      configuration 'Development service credentials'
    }
  }
  
  // ===== LOCAL STORAGE =====
  
  localFileSystem = database 'Local File System' {
    description 'Local development files and assets'
    technology 'File system storage'
    
    metadata {
      paths 'src/, public/, docs/, examples/'
      assets 'Development media files, cached models'
    }
  }
  
  tempStorage = database 'Temporary Storage' {
    description 'Development temporary files and logs'
    technology 'tmp/ directory, log files'
    
    metadata {
      purpose 'Debug logs, temporary media files, cache'
      cleanup 'Automatic cleanup on restart'
    }
  }
  
  // ===== DEPLOYMENT RELATIONSHIPS =====
  
  // Developer workflow
  developerMachine -> viteDevServer 'Runs development server'
  developerMachine -> nodeServer 'Runs backend API server'
  developerMachine -> dockerServices 'Manages containerized services'
  
  // Development server communication
  viteDevServer -> nodeServer 'API proxy for backend calls'
  nodeServer -> dockerServices 'Database and service connections'
  
  // Optional proxy setup
  nginxProxy -> viteDevServer 'Proxies frontend requests'
  nginxProxy -> nodeServer 'Proxies API requests'
  
  // External service integration
  nodeServer -> cloudLLMDev 'Development API calls'
  nodeServer -> mediaServicesDev 'Media processing requests'
  
  // Storage access
  viteDevServer -> localFileSystem 'Serves static assets'
  nodeServer -> localFileSystem 'Reads configuration and assets'
  nodeServer -> tempStorage 'Writes logs and temporary files'
  
  // ===== DEVELOPMENT WORKFLOW =====
  
  gitRepository = system 'Git Repository' {
    description 'Version control and source code management'
    technology 'Git + GitHub/GitLab'
    
    metadata {
      purpose 'Source code versioning, collaboration'
      branches 'main, development, feature branches'
    }
  }
  
  packageRegistry = system 'Package Registry' {
    description 'npm package registry'
    technology 'npm registry + package.json'
    
    metadata {
      purpose 'Dependency management'
      lockFile 'package-lock.json'
    }
  }
  
  // Development workflow relationships
  developerMachine -> gitRepository 'Source code management'
  developerMachine -> packageRegistry 'Package installation and updates'
  
  // ===== DEVELOPMENT FEATURES =====
  
  hotReload = component 'Hot Module Replacement' {
    description 'Real-time code updates without page refresh'
    technology 'Vite HMR + WebSocket updates'
  }
  
  debugTools = component 'Development Debug Tools' {
    description 'Debug logging, performance monitoring, error tracking'
    technology 'Console logging + Dev tools + Performance monitor'
  }
  
  testRunner = component 'Test Runner' {
    description 'Automated testing during development'
    technology 'Vitest + test runners'
  }
  
  // Feature relationships
  viteDevServer -> hotReload 'Provides real-time updates'
  nodeServer -> debugTools 'Development logging and monitoring'
  developerMachine -> testRunner 'Runs automated tests'
}

