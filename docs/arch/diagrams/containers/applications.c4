/**
 * Application Containers - Level 2 (C4 Model)
 * High-level view of application containers and their responsibilities
 * Enhanced with FILE_PATH_ENHANCEMENT_GUIDE.md patterns
 * 
 * Based on actual app/ analysis ✅ VERIFIED:
 * - app/viewer/ - 3D VIEWER APPLICATION (450KB+ total)
 * - app/base.js - SHARED BASE FUNCTIONALITY
 * - app/punchme/ - PUNCH DETECTION APPLICATION
 * - app/mobile/ - MOBILE INTERFACE
 */

model {
  // ===== APPLICATION CONTAINERS =====
  
  viewerApplication = container 'Viewer Application (MAIN)' {
    description 'Comprehensive 3D viewer with avatar interaction, gesture controls, and agent integration ✅ VERIFIED (450KB+ total)'
    technology 'Three.js + WebXR + LangGraph Agents + MediaPipe + Canvas API'
    
    link ../../../app/viewer/ 'Viewer Application Directory'
    
    metadata {
      filePath 'app/viewer/'
      totalSize '450KB+ (multiple large components)'
      mainComponents 'viewer.js (61KB), ui.js (100KB), talkingavatar.js (103KB), gestures.js (54KB)'
      capabilities '3D rendering, Avatar conversation, Gesture recognition, Photo capture, Agent integration'
      architecturalPattern 'Component-based with agent integration'
      testCoverage 'Production-ready with mobile support'
    }
  }
  
  baseApplication = container 'Base Application Framework' {
    description 'Shared base functionality for all applications'
    technology 'JavaScript + Three.js core + Environment setup'
    
    link ../../../app/base.js 'Base Application Source'
    
    metadata {
      filePath 'app/base.js'
      coreFunctions 'init_base(), setupEnvironment(), core Three.js setup'
      sharedFeatures 'Scene initialization, Renderer setup, Common utilities'
      inheritedBy 'Viewer, PunchMe applications'
    }
  }
  
  punchmeApplication = container 'PunchMe Application' {
    description 'Punch detection and combat training application'
    technology 'MediaPipe + Combat detection + 3D visualization + Animation'
    
    link ../../../app/punchme/ 'PunchMe Application Directory'
    
    metadata {
      filePath 'app/punchme/'
      components 'characters/, combat/, core/, input/, ui/, utils/, visualizer/'
      capabilities 'Punch detection, Combat training, Character animation, Performance tracking'
      specialFeatures 'Real-time pose detection, Combat physics, Training analytics'
    }
  }
  
  mobileInterface = container 'Mobile Interface' {
    description 'Mobile-optimized interface for remote control'
    technology 'Mobile-responsive HTML + Remote control APIs'
    
    link ../../../app/mobile/ 'Mobile Interface Directory'
    
    metadata {
      filePath 'app/mobile/'
      mainFile 'index.html'
      capabilities 'Remote viewer control, Mobile-optimized UI'
      integration 'Connects to viewer application for remote control'
    }
  }
  
  // ===== VIEWER APPLICATION INTERNAL COMPONENTS =====
  // (Detailed components are defined in components/viewer.c4)
  // Note: Individual components like viewerCore, uiManagement, talkingAvatar, etc.
  // are defined in their dedicated component files to avoid duplication
  
  // ===== PUNCHME APPLICATION INTERNAL COMPONENTS =====
  // Note: Individual components are defined in their dedicated component files
  
  // ===== APPLICATION RELATIONSHIPS =====
  
  // Base inheritance
  viewerApplication -> baseApplication 'Inherits base functionality'
  punchmeApplication -> baseApplication 'Inherits base functionality'
  
  // Mobile interface connection
  mobileInterface -> viewerApplication 'Remote control and monitoring'
  
  // External system integrations (reference external containers)
  viewerApplication -[external]-> agentContainer 'LangGraph agent integration'
  viewerApplication -[external]-> mediaContainer 'Media processing integration'
  punchmeApplication -[external]-> recognitionContainer 'Pose and gesture recognition'
}

