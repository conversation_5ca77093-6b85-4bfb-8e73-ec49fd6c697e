/**
 * HTTP Request Flow - Express.js Middleware and Routing Flow
 * Shows how HTTP requests flow through the Express.js server architecture
 * 
 * Based on implemented server architecture:
 * - Express.js middleware chain with CORS and body parsing
 * - Unified routing system with static file serving
 * - LLM API endpoint with multimodal processing
 * - Error handling and logging throughout
 */

model {
  // External Actors (HTTP flow specific)
  httpFlowClientApp = actor 'Client Application' {
    description 'Frontend applications (viewer, mobile, web)'
    technology 'Browser, React, Vue, Mobile App'
  }
  
  // Core Systems (HTTP flow specific)
  httpFlowDownloadServer = system 'Unified Express Server' {
    description 'Consolidated Express.js HTTP server with unified routing'
    technology 'Express.js, Node.js, HTTP, WebSocket Proxy'
  }
  
  // External Systems (Reference from containers/system.c4)
  // cloudLLM is defined at system level
  
  // Server Components (from server.ts architecture)
  expressApp = component 'Express Application' {
    description 'Main Express.js app with middleware chain'
    technology 'Express.js v4.x'
    
    link ../../../src/server/server.ts 'Main Server Implementation'
    
    metadata {
      filePath 'src/server/server.ts'
      fileSize '15KB (327 lines)'
      functionality 'CORS, body parsing, static files, routing'
      middlewareChain '7 middleware layers'
    }
  }
  
  corsMiddleware = component 'CORS Middleware' {
    description 'Cross-Origin Resource Sharing handler'
    technology 'Custom CORS implementation with origin validation'
    
    metadata {
      allowedOrigins '["http://localhost:3002", "http://localhost:2994"]'
      functionality 'Dynamic origin validation, OPTIONS handling'
    }
  }
  
  bodyParsingMiddleware = component 'Body Parsing Middleware' {
    description 'Request body parsing with multipart support'
    technology 'Express built-in + custom multipart handling'
    
    metadata {
      maxSize '50MB for audio/video data'
      supportedTypes 'JSON, URL-encoded, multipart/form-data'
    }
  }
  
  staticFileMiddleware = component 'Static File Middleware' {
    description 'Serves static assets and models'
    technology 'Express static middleware'
    
    metadata {
      routes '["/assets", "/models", "/models/mediapipe", "/models/tts"]'
      directories '4 static directories served'
    }
  }
  
  unifiedRoutes = component 'Unified Routes' {
    description 'Central routing system for all API endpoints'
    technology 'Express Router with modular route definitions'
    
    link ../../../src/server/routes/index.ts 'Route Registration'
    
    metadata {
      filePath 'src/server/routes/index.ts'
      endpoints 'LLM, assets, file management, health checks'
    }
  }
  
  httpLlmRoute = component 'LLM API Route' {
    description 'Multimodal LLM request processing endpoint'
    technology 'Express Router with validation and error handling'
    
    link ../../../src/server/routes/llm.ts 'LLM Processing Implementation'
    
    metadata {
      filePath 'src/server/routes/llm.ts'
      fileSize '13KB (297 lines)'
      functionality 'Multimodal processing, tool calling, audio support'
      validation 'Provider, model, messages validation'
    }
  }
  
  checkpointRoute = component 'Checkpoint Management Route' {
    description 'ML model download and management endpoint'
    technology 'Express Router with multer for file uploads'
    
    metadata {
      functionality 'Model download, upload, caching, lifecycle management'
      supportedModels 'MediaPipe, Sherpa-ONNX, HuggingFace, Custom'
    }
  }
  
  // Model Components
  aliyunModel = component 'Aliyun Chat Model' {
    description 'LangChain-compatible Aliyun Qwen model interface'
    technology 'LangChain BaseChatModel + Aliyun DashScope API'
    
    link ../../../src/agent/models/AliyunBailianChatModel.js 'Model Implementation'
    
    metadata {
      supportedModalities '["text", "audio", "image"]'
      toolCalling 'OpenAI-compatible function calling'
    }
  }
  
  // Error Handling
  errorHandler = component 'Error Handler' {
    description 'Centralized error handling and logging'
    technology 'Express error middleware + custom error formatting'
    
    metadata {
      errorTypes 'Validation, authentication, rate limiting, server errors'
      responseFormat 'Structured JSON with error details and troubleshooting'
    }
  }
  
  // ===== HTTP REQUEST FLOW RELATIONSHIPS =====
  
  // Incoming Request Flow
  httpFlowClientApp -[external]-> expressApp 'HTTP Request (GET/POST/PUT/DELETE)'
  
  // Middleware Chain (Sequential Processing)
  expressApp -[sync]-> corsMiddleware 'CORS validation'
  corsMiddleware -[sync]-> bodyParsingMiddleware 'Body parsing'
  bodyParsingMiddleware -[sync]-> staticFileMiddleware 'Static file check'
  staticFileMiddleware -[sync]-> unifiedRoutes 'Route matching'
  
  // Route Processing
  unifiedRoutes -[internal]-> httpLlmRoute 'LLM API requests (/llm)'
  unifiedRoutes -[internal]-> checkpointRoute 'Model management (/checkpoints)'
  
  // LLM Processing Pipeline
  httpLlmRoute -[sync]-> aliyunModel 'Model invocation'
  aliyunModel -[external]-> cloudLLM 'API calls'
  cloudLLM -[external]-> aliyunModel 'AI responses'
  aliyunModel -[sync]-> httpLlmRoute 'Processed response'
  
  // Error Handling Flow
  corsMiddleware -[internal]-> errorHandler 'CORS errors'
  bodyParsingMiddleware -[internal]-> errorHandler 'Parsing errors'
  httpLlmRoute -[internal]-> errorHandler 'Processing errors'
  checkpointRoute -[internal]-> errorHandler 'File operation errors'
  
  // Response Flow
  httpLlmRoute -[sync]-> expressApp 'JSON response'
  checkpointRoute -[sync]-> expressApp 'File/JSON response'
  staticFileMiddleware -[sync]-> expressApp 'Static file response'
  errorHandler -[sync]-> expressApp 'Error response'
  expressApp -[external]-> httpFlowClientApp 'HTTP Response'
}

views {
  view http_request_processing_flow {
    title 'HTTP Request Processing Flow - Express.js Middleware Chain'
    description 'Complete request lifecycle through Express.js server architecture'
    
    include httpFlowClientApp, httpFlowDownloadServer, cloudLLM
    include expressApp, corsMiddleware, bodyParsingMiddleware, staticFileMiddleware
    include unifiedRoutes, httpLlmRoute, checkpointRoute, aliyunModel, errorHandler
    
    // Focus on the sequential middleware chain and error handling paths
  }
  
  view llm_request_specific_flow {
    title 'LLM Request Specific Flow - Multimodal Processing'
    description 'Detailed flow for LLM API requests with multimodal support'
    
    include httpFlowClientApp, httpLlmRoute, aliyunModel, cloudLLM, errorHandler
    
    // Highlight LLM-specific processing including validation and tool calling
  }
}