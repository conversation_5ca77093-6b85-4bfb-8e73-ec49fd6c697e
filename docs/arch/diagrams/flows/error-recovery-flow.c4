/**
 * Error Recovery Flow - 1011 Error Handling and Connection Failure Recovery
 * Shows comprehensive error detection, recovery strategies, and system resilience
 * 
 * Based on implemented error recovery:
 * - 1011 Internal Server Error prevention and handling
 * - Connection failure recovery with exponential backoff
 * - Session stabilization and rate limiting
 * - Circuit breaker patterns and graceful degradation
 */

model {
  // External Actors
  errorRecoveryClientApp = actor 'Client Application' {
    description 'Frontend with realtime streaming connections'
    technology 'Browser, Mobile App, WebSocket clients'
  }
  
  // External Systems
  errorRecoveryAliyunDashScope = system 'Aliyun DashScope' {
    description 'External AI service with potential connection issues'
    technology 'WebSocket API, HTTP API, Rate limiting'
  }
  
  networkInfrastructure = system 'Network Infrastructure' {
    description 'Internet connectivity and network conditions'
    technology 'ISP, CDN, DNS, Firewalls'
  }
  
  // Core System
  errorRecoveryDownloadServer = system 'Unified Express Server' {
    description 'Consolidated server with comprehensive error recovery capabilities'
    technology 'Node.js, Express.js, WebSocket Proxy, Error Recovery'
  }
  
  // Error Detection Components
  connectionMonitor = component 'Connection Monitor' {
    description 'Monitors connection health and detects failures'
    technology 'WebSocket state tracking + health checks'
    
    metadata {
      monitoringTypes 'Connection state, message flow, response times'
      detectionLatency 'Sub-100ms error detection'
      healthMetrics 'Ping/pong, message acknowledgment, timeout tracking'
    }
  }
  
  errorClassifier = component 'Error Classifier' {
    description 'Classifies errors and determines recovery strategies'
    technology 'Error code analysis + pattern matching'
    
    metadata {
      errorTypes '1011 (Internal Server), 1006 (Abnormal Closure), 1000 (Normal)'
      recoveryStrategies 'Immediate, delayed, exponential backoff, circuit break'
      patternRecognition 'API rate limiting, network issues, authentication failures'
    }
  }
  
  // 1011 Error Specific Components (from AliyunRealtimeProxy.ts)
  sessionStabilizer = component 'Session Stabilizer' {
    description 'Prevents 1011 errors through session.update timing control'
    technology 'Delayed message processing + session lifecycle management'
    
    link ../../../src/server/middleware/AliyunRealtimeProxy.ts 'Session Stabilization Logic'
    
    metadata {
      delayStrategy 'Client: 500ms, Queued: 1000ms stabilization delays'
      preventionMechanism 'session.update sent only after upstream stabilization'
      upstreamReadiness 'Connection state validation before session operations'
      queueProcessing 'Special handling for queued session.update messages'
    }
  }
  
  rateLimitController = component 'Rate Limit Controller' {
    description 'Controls message rate to prevent API overload'
    technology 'Time-based throttling + adaptive rate control'
    
    link ../../../src/agent/models/aliyun/AliyunConfig.js 'Rate Limiting Configuration'
    
    metadata {
      rateLimit '200ms intervals (5 messages/second)'
      adaptiveLogic 'Python-compatible timing for stability'
      backpressure 'Queue management during rate limiting'
      errorPrevention '1011 error prevention through controlled message flow'
    }
  }
  
  // Recovery Mechanisms
  reconnectionEngine = component 'Reconnection Engine' {
    description 'Handles automatic reconnection with intelligent backoff'
    technology 'Exponential backoff + connection state management'
    
    metadata {
      backoffStrategy 'Exponential: 1s, 2s, 4s, 8s, max 30s'
      maxRetries '10 attempts before circuit breaking'
      statePreservation 'Message queue and session state during reconnection'
      connectionPooling 'Shared upstream connections for multiple clients'
    }
  }
  
  circuitBreaker = component 'Circuit Breaker' {
    description 'Prevents cascade failures with failure threshold management'
    technology 'Circuit breaker pattern + health-based switching'
    
    metadata {
      failureThreshold '5 consecutive failures trigger circuit break'
      recoveryTimeout '30 seconds before attempting recovery'
      healthCheck 'Periodic connectivity testing during circuit break'
      gracefulDegradation 'Fallback responses during circuit break state'
    }
  }
  
  stateRecoveryManager = component 'State Recovery Manager' {
    description 'Manages state preservation and restoration during recovery'
    technology 'State serialization + message queue persistence'
    
    metadata {
      stateTypes 'Connection state, message queue, session configuration'
      persistenceStrategy 'In-memory queuing with optional disk persistence'
      recoveryValidation 'State integrity checks after reconnection'
    }
  }
  
  // Error-Specific Recovery Components
  error1011Handler = component '1011 Error Handler' {
    description 'Specialized handler for Aliyun 1011 Internal Server Errors'
    technology 'Immediate reconnection + session reset'
    
    metadata {
      triggerCondition 'WebSocket close code 1011'
      recoveryAction 'Immediate reconnection without exponential delay'
      stateReset 'Connection state reset for fresh session establishment'
      preventionIntegration 'Works with session stabilizer for prevention'
    }
  }
  
  error1006Handler = component '1006 Error Handler' {
    description 'Handles abnormal connection closures'
    technology 'Network diagnostics + progressive recovery'
    
    metadata {
      triggerCondition 'WebSocket close code 1006 (abnormal closure)'
      diagnostics 'Network connectivity checks and API key validation'
      recoveryStrategy 'Standard exponential backoff with network validation'
    }
  }
  
  authErrorHandler = component 'Auth Error Handler' {
    description 'Handles authentication and authorization failures'
    technology 'API key validation + credential refresh'
    
    metadata {
      triggerCondition 'HTTP 401, 403, or WebSocket auth rejection'
      validationSteps 'API key format, environment variable checks'
      recoveryAction 'Credential refresh or user notification'
    }
  }
  
  // Monitoring and Alerting
  errorMetricsCollector = component 'Error Metrics Collector' {
    description 'Collects and analyzes error patterns for system improvement'
    technology 'Time-series metrics + pattern analysis'
    
    metadata {
      metricsTypes 'Error frequency, recovery success rate, downtime duration'
      patternAnalysis 'Identifies recurring error patterns and root causes'
      alertingThresholds 'Configurable thresholds for error rate alerts'
    }
  }
  
  recoveryLogger = component 'Recovery Logger' {
    description 'Comprehensive logging for error recovery debugging'
    technology 'Structured logging + recovery timeline tracking'
    
    metadata {
      logLevels 'DEBUG, INFO, WARN, ERROR with contextual information'
      recoveryTimeline 'Complete recovery process tracking from error to resolution'
      diagnosticData 'Connection state, error context, recovery attempts'
    }
  }
  
  // Configuration
  errorRecoveryConfig = component 'Error Recovery Config' {
    description 'Centralized configuration for error recovery behavior'
    technology 'Environment-based configuration + runtime adjustments'
    
    link ../../../src/agent/models/aliyun/AliyunConfig.js 'Error Recovery Configuration'
    
    metadata {
      configurableParameters 'Retry attempts, backoff intervals, circuit breaker thresholds'
      environmentOverrides 'Development vs production recovery behavior'
      dynamicAdjustment 'Runtime configuration updates based on error patterns'
    }
  }
  
  // ===== ERROR RECOVERY FLOW RELATIONSHIPS =====
  
  // Error Detection Flow
  errorRecoveryClientApp -[external]-> connectionMonitor 'Connection activity'
  errorRecoveryAliyunDashScope -[external]-> connectionMonitor 'Connection status updates'
  networkInfrastructure -[external]-> connectionMonitor 'Network condition signals'
  connectionMonitor -[internal]-> errorClassifier 'Error detection events'
  
  // 1011 Error Prevention Flow
  errorRecoveryClientApp -[async]-> sessionStabilizer 'session.update messages'
  sessionStabilizer -[internal]-> rateLimitController 'Throttled message processing'
  rateLimitController -[internal]-> errorRecoveryAliyunDashScope 'Rate-limited session updates'
  
  // Error Classification and Routing
  errorClassifier -[internal]-> error1011Handler '1011 Internal Server Error'
  errorClassifier -[internal]-> error1006Handler '1006 Abnormal Closure'
  errorClassifier -[internal]-> authErrorHandler 'Authentication failures'
  
  // Recovery Execution Flow
  error1011Handler -[internal]-> reconnectionEngine 'Immediate reconnection'
  error1006Handler -[internal]-> reconnectionEngine 'Standard backoff reconnection'
  authErrorHandler -[internal]-> errorRecoveryConfig 'Credential validation'
  
  // Connection Recovery
  reconnectionEngine -[internal]-> stateRecoveryManager 'Preserve connection state'
  reconnectionEngine -[external]-> errorRecoveryAliyunDashScope 'Reconnection attempts'
  errorRecoveryAliyunDashScope -[external]-> reconnectionEngine 'Connection responses'
  
  // Circuit Breaker Integration
  reconnectionEngine -[internal]-> circuitBreaker 'Failure count tracking'
  circuitBreaker -[internal]-> reconnectionEngine 'Circuit state decisions'
  circuitBreaker -[external]-> errorRecoveryClientApp 'Graceful degradation responses'
  
  // State Recovery
  stateRecoveryManager -[internal]-> connectionMonitor 'Restored connection state'
  stateRecoveryManager -[internal]-> errorRecoveryClientApp 'State restoration confirmation'
  
  // Monitoring and Learning
  errorClassifier -[internal]-> errorMetricsCollector 'Error pattern data'
  reconnectionEngine -[internal]-> recoveryLogger 'Recovery process logging'
  circuitBreaker -[internal]-> errorMetricsCollector 'Circuit breaker events'
  
  // Configuration Feedback
  errorMetricsCollector -[internal]-> errorRecoveryConfig 'Performance-based adjustments'
  recoveryLogger -[internal]-> errorRecoveryConfig 'Log-based optimization'
}

views {
  view error_detection_and_classification {
    title 'Error Detection and Classification - Automated Error Recognition'
    description 'Real-time error detection with intelligent classification and routing'
    
    include errorRecoveryClientApp, connectionMonitor, errorClassifier
    include error1011Handler, error1006Handler, authErrorHandler
    include errorRecoveryAliyunDashScope, networkInfrastructure
    
    // Focus on error detection and initial classification
  }
  
  view one_zero_one_one_prevention_flow {
    title '1011 Error Prevention Flow - Session Stabilization Strategy'
    description 'Proactive prevention of 1011 Internal Server Errors through timing control'
    
    include errorRecoveryClientApp, sessionStabilizer, rateLimitController
    include errorRecoveryAliyunDashScope, errorRecoveryConfig
    
    // Highlight the specific 1011 error prevention mechanisms
  }
  
  view connection_recovery_flow {
    title 'Connection Recovery Flow - Reconnection and State Restoration'
    description 'Automated recovery with exponential backoff and state preservation'
    
    include reconnectionEngine, stateRecoveryManager, circuitBreaker
    include errorRecoveryAliyunDashScope, errorRecoveryClientApp
    
    // Focus on the recovery execution and state management
  }
  
  view comprehensive_error_resilience {
    title 'Comprehensive Error Resilience - End-to-End Recovery System'
    description 'Complete error handling ecosystem with monitoring and adaptation'
    
    include connectionMonitor, errorClassifier, reconnectionEngine, circuitBreaker
    include errorMetricsCollector, recoveryLogger, errorRecoveryConfig
    
    // Show the complete error resilience architecture
  }
}