/**
 * LLM API Processing Flow - Multimodal Request Processing Pipeline
 * Shows the complete LLM API request processing with multimodal support
 * 
 * Based on implemented LLM processing:
 * - Multimodal input processing (text, audio, image)
 * - LangChain message format conversion
 * - Too<PERSON> calling with OpenAI-compatible format
 * - Aliyun DashScope integration with error handling
 * 
 * === COMPREHENSIVE TEST VALIDATION STATUS ===
 * - LLM Service Tests: ✅ service.test.js, authentication.test.js (production ready)
 * - Error Handling: ✅ Enhanced TypeScript error service validation
 * - Model Integration: ✅ Aliyun model factory and chat model tests
 * - Request Processing: ⚠️ End-to-end LLM API flow tests missing from runner
 * - Multimodal Processing: ✅ Validated through media and agent integration tests
 * - Tool Calling: ✅ Real tools integration validated (25 tests passed)
 * - Production Status: ✅ Core LLM processing ready, comprehensive E2E testing recommended
 */

model {
  // External Actors
  llmProcessingClientApp = actor 'Client Application' {
    description 'Frontend applications making LLM requests'
    technology 'React, Vue, Mobile, API clients'
  }
  
  // External Systems
  llmProcessingAliyunDashScope = system 'Aliyun DashScope' {
    description 'Aliyun multimodal AI service'
    technology 'Qwen-Omni models, HTTP API, Tool calling'
  }
  
  openAIAPI = system 'OpenAI API' {
    description 'OpenAI language models (future support)'
    technology 'GPT models, function calling'
  }
  
  anthropicAPI = system 'Anthropic API' {
    description 'Anthropic Claude models (future support)'
    technology 'Claude models, tool use'
  }
  
  // Core System
  llmProcessingDownloadServer = system 'Unified Express Server' {
    description 'Consolidated HTTP server with unified LLM API processing'
    technology 'Express.js, Node.js, Unified Routing'
  }
  
  // Core LLM Processing Components (from llm.ts)
  llmRoute = component 'LLM API Route' {
    description 'Main LLM request processing endpoint with validation'
    technology 'Express Router + comprehensive validation + error handling'
    
    link ../../../src/server/routes/llm.ts 'LLM Route Implementation'
    
    metadata {
      filePath 'src/server/routes/llm.ts'
      fileSize '13KB (297 lines)'
      endpoint 'POST /llm'
      validation 'Provider, model, messages, modalities, tools'
      errorHandling 'Aliyun-specific error mapping and troubleshouting'
      
      // === TEST COVERAGE STATUS ===
      test_status '⚠️ No dedicated LLM route tests in current test runner'
      related_tests 'LLM service tests (service.test.js, authentication.test.js)'
      coverage_gap 'End-to-end LLM API route testing missing'
      validation_scope 'Component-level LLM services validated, route-level integration needed'
      production_concerns 'Core functionality validated, API route testing recommended'
    }
  }
  
  requestValidator = component 'Request Validator' {
    description 'Validates incoming LLM requests for required parameters'
    technology 'Parameter validation + message content verification'
    
    metadata {
      requiredParams '["provider", "model", "messages"]'
      messageValidation 'Content length, array format, modality support'
      toolValidation 'OpenAI format compatibility, tool choice validation'
    }
  }
  
  inputSanitizer = component 'Input Sanitizer' {
    description 'Sanitizes sensitive data from logs and processing'
    technology 'Audio data redaction + nested content sanitization'
    
    metadata {
      redactionTypes 'input_audio, nested content arrays, large payloads'
      privacyProtection 'Audio data marked as [REDACTED] in logs'
    }
  }
  
  messageConverter = component 'Message Converter' {
    description 'Converts API messages to LangChain format'
    technology 'LangChain message types + multimodal content extraction'
    
    metadata {
      messageTypes 'SystemMessage, AIMessage, HumanMessage'
      multimodalSupport 'Text, image_url, audio_url, input_audio'
      contentExtraction 'Separates text from media content'
    }
  }
  
  multimodalProcessor = component 'Multimodal Processor' {
    description 'Processes mixed content types (text, audio, image)'
    technology 'Content type detection + modality-specific processing'
    
    metadata {
      supportedTypes '["text", "audio", "image"]'
      audioFormats 'WAV, MP3, base64 encoded audio'
      imageFormats 'JPEG, PNG, base64 encoded images'
      contentValidation 'Size limits, format verification'
    }
  }
  
  toolConverter = component 'Tool Converter' {
    description 'Converts custom tool format to OpenAI-compatible format'
    technology 'Schema transformation + function definition mapping'
    
    metadata {
      inputFormat 'Custom {name, schema, description} format'
      outputFormat 'OpenAI {type: "function", function: {...}} format'
      toolChoice 'auto, required, specific function selection'
    }
  }
  
  // Model Components
  aliyunModelFactory = component 'Aliyun Model Factory' {
    description 'Creates and configures Aliyun chat model instances'
    technology 'AliyunBailianChatModel + configuration management'
    
    metadata {
      modelSupport 'qwen-omni-turbo, qwen-omni-turbo-realtime'
      apiModeDetection 'Automatic HTTP/WebSocket mode selection'
      configurations 'Audio config, modalities, tool binding'
    }
  }
  
  aliyunChatModel = component 'Aliyun Chat Model' {
    description 'LangChain-compatible Aliyun model interface'
    technology 'BaseChatModel extension + Aliyun API integration'
    
    link ../../../src/agent/models/AliyunBailianChatModel.js 'Model Implementation'
    
    metadata {
      langchainCompliance 'v0.3 compatible message handling'
      toolCalling 'Function calling with proper tool_calls extraction'
      streamingSupport 'Token-level streaming responses'
      multimodalSupport 'Text, audio, image processing'
    }
  }
  
  responseProcessor = component 'Response Processor' {
    description 'Processes and formats model responses'
    technology 'Response transformation + metadata extraction'
    
    metadata {
      responseFormat 'Unified {content, audio, tool_calls, metadata} format'
      toolCallExtraction 'Proper tool_calls forwarding for autonomous communication'
      audioHandling 'Audio response processing and validation'
      usageTracking 'Token usage and performance metrics'
    }
  }
  
  // Error Handling (Enhanced TypeScript Implementation)
  llmErrorService = component 'LLM Error Service' {
    description 'Centralized error handling with comprehensive type safety'
    technology 'TypeScript error interfaces + optional chaining + pattern matching'
    
    link ../../../src/services/llm/errors.ts 'Enhanced Error Service Implementation'
    
    metadata {
      typeSystem 'LLMErrorInterface with comprehensive error classification'
      errorMapping 'Safe optional chaining for provider-specific error mapping'
      errorTypes 'authentication, rate_limit, network, validation, provider, internal'
      retryLogic 'Exponential backoff with configurable retry strategies'
      contextualErrors 'Request ID tracking and error context preservation'
      
      // === ERROR HANDLING TEST VALIDATION ===
      test_validation '✅ Enhanced TypeScript error service comprehensive testing'
      test_files 'service.test.js validates error handling patterns'
      authentication_tests '✅ authentication.test.js validates API key handling'
      error_classification '✅ Type-safe error classification validated'
      production_ready '✅ Error handling production ready with comprehensive coverage'
    }
  }
  
  validationService = component 'Validation Service' {
    description 'Enhanced validation with warnings and errors support'
    technology 'TypeScript ValidationResult + comprehensive parameter validation'
    
    link ../../../src/services/llm/validation.ts 'Validation Service Implementation'
    
    metadata {
      validationResult 'isValid, errors[], warnings[] structure'
      toolValidation 'Enhanced LLMTool with function property support'
      parameterChecks 'Provider, model, messages, modalities validation'
      warningSystem 'Non-blocking warnings for optimization suggestions'
    }
  }
  
  errorMapper = component 'Error Mapper' {
    description 'Maps provider-specific errors to standard responses'
    technology 'Error pattern matching + troubleshooting guidance'
    
    metadata {
      aliyunErrors 'Empty messages, API key issues, rate limiting'
      httpCodes '400 (validation), 401 (auth), 429 (rate limit), 500 (server)'
      troubleshooting 'Provider-specific error resolution guidance'
    }
  }
  
  // Configuration
  modelConfig = component 'Model Configuration' {
    description 'Centralized model configuration and provider settings'
    technology 'Environment variables + default configurations'
    
    metadata {
      providers '["aliyun", "openai", "anthropic"] (aliyun implemented)'
      apiKeys 'VITE_DASHSCOPE_API_KEY validation'
      defaultSettings 'Voice: Chelsie, Format: WAV, Language: English'
    }
  }
  
  // ===== LLM API PROCESSING FLOW RELATIONSHIPS =====
  
  // Request Processing Flow (Enhanced with TypeScript Services)
  llmProcessingClientApp -[external]-> llmRoute 'LLM request (POST /llm)'
  llmRoute -[internal]-> inputSanitizer 'Sanitize sensitive data'
  inputSanitizer -[internal]-> validationService 'Enhanced validation with warnings'
  validationService -[internal]-> requestValidator 'Validate parameters'
  requestValidator -[internal]-> messageConverter 'Convert to LangChain format'
  
  // Multimodal Processing
  messageConverter -[internal]-> multimodalProcessor 'Extract multimodal content'
  multimodalProcessor -[internal]-> toolConverter 'Process tool definitions'
  toolConverter -[data]-> modelConfig 'Retrieve provider configuration'
  
  // Model Initialization
  modelConfig -[internal]-> aliyunModelFactory 'Create model instance'
  aliyunModelFactory -[internal]-> aliyunChatModel 'Configure Aliyun model'
  
  // Tool Processing
  toolConverter -[internal]-> aliyunChatModel 'Bind tools to model'
  
  // Model Invocation
  aliyunChatModel -[external]-> llmProcessingAliyunDashScope 'API request with multimodal data'
  llmProcessingAliyunDashScope -[external]-> aliyunChatModel 'Model response'
  
  // Response Processing
  aliyunChatModel -[internal]-> responseProcessor 'Process model response'
  responseProcessor -[internal]-> llmRoute 'Format unified response'
  llmRoute -[external]-> llmProcessingClientApp 'JSON response'
  
  // Error Handling Flow (Enhanced TypeScript Integration)
  validationService -[internal]-> llmErrorService 'Validation errors with context'
  requestValidator -[internal]-> llmErrorService 'Parameter validation errors'
  aliyunChatModel -[internal]-> llmErrorService 'API errors with provider context'
  responseProcessor -[internal]-> llmErrorService 'Processing errors'
  llmErrorService -[internal]-> errorMapper 'Classified and contextualized errors'
  errorMapper -[internal]-> llmRoute 'Standardized error response'
  
  // Future Provider Support
  modelConfig -[dotted]-> openAIAPI 'Future OpenAI integration'
  modelConfig -[dotted]-> anthropicAPI 'Future Anthropic integration'
  
  // Monitoring and Logging
  inputSanitizer -[internal]-> llmRoute 'Sanitized request logging'
  responseProcessor -[internal]-> llmRoute 'Response metadata logging'
  errorMapper -[internal]-> llmRoute 'Error diagnostics logging'
}

views {
  view request_processing_pipeline {
    title 'LLM Request Processing Pipeline - Validation to Model Invocation'
    description 'Complete request processing from validation through model execution'
    
    include llmProcessingClientApp, llmRoute, requestValidator, inputSanitizer
    include messageConverter, multimodalProcessor, toolConverter
    include aliyunModelFactory, aliyunChatModel, llmProcessingAliyunDashScope
    
    // Focus on the sequential processing pipeline
  }
  
  view multimodal_processing_flow {
    title 'Multimodal Processing Flow - Text, Audio, Image Handling'
    description 'Specialized processing for different content modalities'
    
    include messageConverter, multimodalProcessor, aliyunChatModel
    include responseProcessor, llmProcessingAliyunDashScope
    
    // Highlight multimodal content processing capabilities
  }
  
  view tool_calling_flow {
    title 'Tool Calling Flow - Function Definition and Execution'
    description 'Tool processing from definition through response extraction'
    
    include toolConverter, aliyunChatModel, responseProcessor
    include llmProcessingAliyunDashScope
    
    // Focus on tool calling and function execution capabilities
  }
  
  view error_handling_flow {
    title 'Error Handling Flow - Enhanced TypeScript Error Management'
    description 'Comprehensive error handling with type-safe validation and provider-specific mapping'
    
    include validationService, llmErrorService, requestValidator, aliyunChatModel, errorMapper
    include llmRoute, llmProcessingClientApp
    
    // Highlight enhanced TypeScript error detection, classification, and response strategies
  }
}