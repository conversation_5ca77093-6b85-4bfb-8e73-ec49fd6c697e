/**
 * Media Streaming Flow - Process Flow View
 * Shows the simplified streaming architecture after MediaInputBridge removal
 * 
 * Based on implemented changes:
 * - MediaInputBridge.ts DELETED
 * - MediaCaptureManager.setStreamingManager() ADDED  
 * - Direct LangGraph integration IMPLEMENTED
 * 
 * === TEST VALIDATION STATUS ===
 * - MediaCaptureManager: ✅ 56 tests passed (100% success)
 * - CameraManager: ✅ 22 tests passed (100% success)
 * - Audio Processing: ✅ 11 buffer overflow tests + modality tests passed
 * - Streaming Integration: ⚠️ StreamingManager tests exist but not in test runner
 * - Last Validated: 2025-08-04
 * - Production Ready: ✅ Core media streaming functionality validated
 */

model {
  
  // Flow-specific actors
  streamingUser = actor 'Streaming User' {
    description 'Person interacting via voice, gesture, video in the streaming context'
  }
  
  // Device layer
  deviceMedia = device 'Device Media Interface' {
    description 'User device interface for audio/video capture and output'
    technology 'WebRTC + MediaDevices API + Audio/Video processing'
  }
  
  // Cloud LLM service
  cloudLLM = cloud 'Cloud LLM Service' {
    description 'External AI model service for processing'
    technology 'OpenAI/Aliyun/Claude API + HTTP/WebSocket'
  }

  // Flow-specific components not defined elsewhere
  audioProcessor = component 'Audio Processing System' {
    description 'Comprehensive audio processing with consolidated analysis capabilities'
    technology 'Audio format conversion + Analysis utilities + Streaming processors'
    
    metadata {
      test_validation '✅ Audio processing validated through comprehensive test suite'
      test_coverage 'buffer-overflow-fix.test.js (11 tests), audioAnalysis.test.js, audio.test.js'
      streaming_tests '⚠️ StreamingManager.test.js exists but not included in test runner'
      production_status '✅ Core audio processing production ready'
    }
  }
  
  videoProcessor = component 'Video Processing System' {
    description 'Comprehensive video processing and frame extraction utilities'
    technology 'WebGL processing + Canvas manipulation + Frame extraction'
  }
  
  streamingManager = component 'Streaming Engine' {
    description 'LangGraph streaming engine with performance coordination'
    technology 'LangGraph Stream API + Performance optimization + Session management'
    
    metadata {
      test_status '⚠️ Streaming tests exist but not in run-tests.js categories'
      test_files 'StreamingManager.test.js, langchain-streaming.test.js'
      coverage_gap 'Streaming tests need to be added to test runner categories'
      validation_needed 'Comprehensive streaming validation required for production'
      performance_target 'Sub-600ms response time with adaptive optimization'
    }
  }
}

