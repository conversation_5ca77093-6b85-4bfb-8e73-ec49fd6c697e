/**
 * WebSocket Proxy Flow - AliyunRealtimeProxy Connection Handling
 * Shows the complete WebSocket proxy architecture for realtime communication
 * 
 * Based on implemented proxy architecture:
 * - BaseWebSocketProxy with provider-specific extensions
 * - AliyunRealtimeProxy with 1011 error recovery
 * - Session stabilization and VAD event handling
 * - Rate limiting and connection management
 */

model {
  // External Actors
  wsProxyClientApp = actor 'Client Application' {
    description 'Frontend with realtime audio/video streaming'
    technology 'Browser WebSocket, MediaDevices API'
  }
  
  // External Systems
  wsProxyAliyunDashScope = system 'Aliyun DashScope' {
    description 'Aliyun realtime multimodal API service'
    technology 'WebSocket API, Qwen-Omni models'
  }
  
  // Server Infrastructure
  httpServer = system 'Unified Express Server' {
    description 'Consolidated Node.js server hosting WebSocket proxy and HTTP endpoints'
    technology 'Node.js HTTP, Express.js, WebSocket Proxy'
  }
  
  // Core Proxy Components (from AliyunRealtimeProxy.ts and BaseWebSocketProxy.ts)
  aliyunProxy = component 'Aliyun Realtime Proxy' {
    description 'Provider-specific WebSocket proxy with 1011 error recovery'
    technology 'BaseWebSocketProxy extension + Aliyun-specific handling'
    
    link ../../../src/server/middleware/AliyunRealtimeProxy.ts 'Proxy Implementation'
    
    metadata {
      filePath 'src/server/middleware/AliyunRealtimeProxy.ts'
      fileSize '8KB (240 lines)'
      errorRecovery '1011 Internal Server Error handling'
      sessionDelay 'Stabilization delays for session.update'
      vadSupport 'Voice Activity Detection event processing'
    }
  }
  
  baseProxy = component 'Base WebSocket Proxy' {
    description 'Universal WebSocket proxy foundation with connection management'
    technology 'WebSocket handling, connection pooling, retry logic'
    
    link ../../../src/server/middleware/BaseWebSocketProxy.ts 'Base Implementation'
    
    metadata {
      filePath 'src/server/middleware/BaseWebSocketProxy.ts'
      functionality 'Connection management, message routing, error recovery'
      retryLogic 'Exponential backoff with circuit breaker pattern'
    }
  }
  
  connectionManager = component 'Connection Manager' {
    description 'Manages client connections and upstream routing'
    technology 'WebSocket connection pooling + state management'
    
    metadata {
      connectionLimit 'Multi-client support with shared upstream'
      stateTracking 'Connection state, message queuing, health monitoring'
    }
  }
  
  messageRouter = component 'Message Router' {
    description 'Routes messages between clients and upstream with special handling'
    technology 'JSON message parsing + protocol translation'
    
    metadata {
      specialHandling 'session.update delays, VAD events, error recovery'
      queueManagement 'Message queuing during connection establishment'
    }
  }
  
  wsProxySessionManager = component 'Session Manager' {
    description 'Handles Aliyun session lifecycle and stabilization'
    technology 'Session.update processing + timing coordination'
    
    metadata {
      sessionDelays 'Client: 500ms, Queued: 1000ms stabilization'
      errorPrevention '1011 error prevention through delayed session.update'
    }
  }
  
  rateLimiter = component 'Rate Limiter' {
    description 'Controls message rate to prevent API errors'
    technology 'Time-based throttling + adaptive rate control'
    
    metadata {
      rateLimit '200ms intervals (5 messages/second)'
      adaptiveLogic 'Python-compatible timing for stability'
    }
  }
  
  vadProcessor = component 'VAD Event Processor' {
    description 'Processes Voice Activity Detection events'
    technology 'Real-time audio event handling + speech boundary detection'
    
    metadata {
      eventTypes 'speech_started, speech_stopped, voice_activity_detected'
      realTimeProcessing 'Sub-100ms event forwarding for responsive UX'
    }
  }
  
  errorRecovery = component 'Error Recovery Handler' {
    description 'Handles connection failures and automatic recovery'
    technology 'Circuit breaker pattern + exponential backoff retry'
    
    metadata {
      errorCodes '1011 (immediate retry), 1006 (network issues), auth failures'
      recoveryStrategy 'State reset, connection retry, graceful degradation'
    }
  }
  
  // Configuration
  aliyunConfig = component 'Aliyun Configuration' {
    description 'Centralized configuration for Aliyun integration'
    technology 'Environment variables + default configurations'
    
    link ../../../src/agent/models/aliyun/AliyunConfig.js 'Configuration Management'
    
    metadata {
      apiKeyValidation 'DASHSCOPE_API_KEY validation and fallbacks'
      endpointConfig 'wss://dashscope.aliyuncs.com/api/v1/realtime'
      timeoutSettings 'Handshake, ping, session update timeouts'
    }
  }
  
  // ===== WEBSOCKET PROXY FLOW RELATIONSHIPS =====
  
  // Initial Connection Flow
  wsProxyClientApp -[external]-> httpServer 'WebSocket handshake (ws://localhost:2994/ws)'
  httpServer -[internal]-> aliyunProxy 'Proxy attachment'
  aliyunProxy -[internal]-> baseProxy 'Provider-specific initialization'
  baseProxy -[internal]-> connectionManager 'Connection registration'
  
  // Upstream Connection Establishment
  connectionManager -[internal]-> aliyunConfig 'Configuration lookup'
  aliyunConfig -[data]-> connectionManager 'Endpoint, headers, validation'
  connectionManager -[external]-> wsProxyAliyunDashScope 'Upstream WebSocket connection'
  wsProxyAliyunDashScope -[external]-> connectionManager 'Connection established'
  
  // Message Flow (Client to Upstream)
  wsProxyClientApp -[async]-> messageRouter 'WebSocket messages'
  messageRouter -[internal]-> wsProxySessionManager 'Session.update handling'
  messageRouter -[internal]-> rateLimiter 'Rate limit check'
  rateLimiter -[internal]-> wsProxyAliyunDashScope 'Throttled message delivery'
  
  // Message Flow (Upstream to Client)
  wsProxyAliyunDashScope -[async]-> vadProcessor 'VAD events + responses'
  vadProcessor -[internal]-> messageRouter 'Processed events'
  messageRouter -[async]-> wsProxyClientApp 'Real-time responses'
  
  // Session Management
  wsProxySessionManager -[internal]-> wsProxyAliyunDashScope 'Delayed session.update'
  wsProxySessionManager -[data]-> aliyunConfig 'Session configuration'
  
  // Error Handling Flow
  wsProxyAliyunDashScope -[internal]-> errorRecovery '1011/1006 errors'
  errorRecovery -[internal]-> connectionManager 'Connection state reset'
  connectionManager -[internal]-> wsProxyAliyunDashScope 'Reconnection attempt'
  
  // Monitoring and Health
  connectionManager -[internal]-> baseProxy 'Connection health'
  rateLimiter -[internal]-> baseProxy 'Rate limiting stats'
  errorRecovery -[internal]-> baseProxy 'Error statistics'
}

views {
  view websocket_connection_flow {
    title 'WebSocket Connection Flow - Proxy Setup and Upstream Connection'
    description 'Complete WebSocket proxy connection establishment and configuration'
    
    include wsProxyClientApp, httpServer, aliyunProxy, baseProxy, connectionManager
    include aliyunConfig, wsProxyAliyunDashScope
    
    // Focus on connection establishment and configuration flow
  }
  
  view realtime_message_flow {
    title 'Realtime Message Flow - Bidirectional Communication'
    description 'Message routing with VAD processing and session management'
    
    include wsProxyClientApp, messageRouter, wsProxySessionManager, rateLimiter
    include vadProcessor, wsProxyAliyunDashScope
    
    // Highlight real-time message processing and special event handling
  }
  
  view error_recovery_flow {
    title 'Error Recovery Flow - 1011 Error Handling and Connection Recovery'
    description 'Error detection, recovery strategies, and connection resilience'
    
    include wsProxyAliyunDashScope, errorRecovery, connectionManager, wsProxySessionManager
    include aliyunConfig
    
    // Focus on error handling patterns and recovery mechanisms
  }
}