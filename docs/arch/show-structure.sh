#!/bin/bash

# 🌳 Hologram Software - Multi-File Architecture Structure
# Updated for the new modular LikeC4 architecture

echo "🏗️ Hologram Software - Multi-File Architecture Structure"
echo "========================================================"
echo ""
echo "📂 docs/arch/ - Architecture Documentation:"
echo ""

# Show the organized structure
echo "📁 Architecture Files (LikeC4):"
echo "├── 📋 specs.c4                # Element types & relationships"
echo "├── 📂 models/                 # System models (mirror codebase)"
echo "│   ├── 📺 media.c4            # src/media/ - Media processing"
echo "│   ├── 🤖 agent.c4            # src/agent/ - AI agent systems"
echo "│   ├── 📱 applications.c4     # app/ - Frontend applications"
echo "│   ├── 🔧 infrastructure.c4   # Supporting systems"
echo "│   └── 🔗 integration.c4      # Cross-system connections"
echo "├── 📂 views/                  # Architectural perspectives"
echo "│   ├── 🔭 overview.c4         # High-level views"
echo "│   └── 🌊 flows.c4            # Data & process flows"
echo "└── 📂 .vscode/                # IDE configuration"
echo "    └── mcp.json               # MCP server config"
echo ""

echo "🎯 Key Benefits of Multi-File Architecture:"
echo "===========================================" 
echo "✅ **Modular**: Each file mirrors actual codebase structure"
echo "✅ **Maintainable**: Easy to update specific system components"
echo "✅ **Collaborative**: Multiple developers can work on different files"
echo "✅ **Organized**: Clear separation of concerns and responsibilities"
echo "✅ **Scalable**: Easy to add new systems without cluttering"
echo ""

echo "🚀 Quick Commands (run from docs/arch/):"
echo "========================================="
echo "• npm run arch:dev      → Live diagrams at localhost:5173"
echo "• npm run mcp:info      → MCP setup instructions"
echo "• npm run arch:build    → Generate all diagram formats"
echo "• npm run help          → Show this structure guide"
echo ""

echo "📊 Architecture Views Available:"
echo "================================"
echo "🔭 Complete Architecture    - Full system overview"
echo "📱 Application Layer        - Frontend applications"
echo "📺 Media Processing         - Input pipeline"
echo "🤖 Agent Intelligence       - AI systems"
echo "🔧 Infrastructure          - Supporting systems"
echo "🌊 Data Flow               - End-to-end processing"
echo "🧠 Dual-Brain Flow         - AI coordination"
echo "🔧 Tool Execution          - Dynamic tool system"
echo ""

echo "📁 Generated Files Status:"
echo "=========================="

if [ -d "generated" ]; then
    generated_count=$(find generated -type f | wc -l)
    echo "✅ generated/ folder exists (${generated_count} files)"
else
    echo "❌ generated/ folder missing (run: npm run arch:build)"
fi

if [ -d "images" ]; then
    images_count=$(find images -type f | wc -l)
    echo "✅ images/ folder exists (${images_count} files)"
else
    echo "❌ images/ folder missing (run: npm run arch:export:png)"
fi

echo ""
echo "🤖 MCP Integration Status:"
echo "=========================="

if [ -f ".vscode/mcp.json" ]; then
    echo "✅ MCP configuration ready for AI tools"
    echo "📍 MCP Server: Use VSCode Extension (recommended)"
    echo "ℹ️  Alternative: Manual setup via extension"
else
    echo "❌ MCP configuration missing"
    echo "💡 Run: npm run mcp:info for setup instructions"
fi

echo ""
echo "🌐 Live Server Status:"
echo "======================"

if pgrep -f "likec4 start" > /dev/null; then
    echo "✅ LikeC4 dev server is running"
    echo "📍 Access at: http://localhost:5173"
else
    echo "❌ LikeC4 dev server not running"
    echo "💡 Start with: npm run arch:dev"
fi

echo ""
echo "🎯 Next Steps:"
echo "=============="
echo "1. 🌐 Visit http://localhost:5173 to explore interactive diagrams"
echo "2. 🤖 Install LikeC4 VSCode Extension for MCP integration"
echo "3. 📝 Edit model files to reflect code changes" 
echo "4. 🔄 Architecture auto-updates with live reload"
echo "5. ⚠️  Remember: Run commands from docs/arch/ directory!"
echo ""
echo "📖 Full Documentation: PROJECT_OVERVIEW.md"