#!/bin/bash

# 🏗️ Hologram Software - Architecture Documentation Builder
# This script generates all architecture diagrams from LikeC4 models

set -e

echo "🎨 Building Hologram Software Architecture Documentation..."
echo "📍 Working directory: $(pwd)"

# Check if we're in the right directory
if [ ! -f "hologram.c4" ]; then
    echo "❌ hologram.c4 not found. Please run this script from docs/arch/ directory"
    exit 1
fi

# Check if LikeC4 CLI is available
if ! command -v likec4 &> /dev/null; then
    echo "📦 LikeC4 CLI not found. Installing..."
    npm run install-cli
fi

# Create output directories
mkdir -p generated images static

echo "🧹 Cleaning previous builds..."
npm run clean
mkdir -p generated images static

echo "📊 Building interactive diagrams..."
npm run arch:build

echo "🖼️ Exporting all diagram formats..."
npm run arch:export:all

echo "🌐 Starting preview server (background)..."
echo "📍 Preview available at: http://localhost:5173"
npm run arch:preview &

echo "✅ Architecture documentation built successfully!"
echo ""
echo "📁 Outputs:"
echo "  - Interactive: ./generated/"
echo "  - PNG/SVG Images: ./images/"
echo "  - Mermaid: ./static/"
echo ""
echo "🚀 To preview: npm run arch:preview"
echo "🔄 To start dev server: npm run arch:dev"

# List generated files
echo ""
echo "📋 Generated files:"
if [ -d "./generated" ]; then
    echo "  Interactive diagrams:"
    ls -la ./generated/ | head -10
fi

if [ -d "./images" ]; then
    echo "  Image files:"
    ls -la ./images/ | head -10
fi

if [ -d "./static" ]; then
    echo "  Static exports:"
    ls -la ./static/ | head -10
fi