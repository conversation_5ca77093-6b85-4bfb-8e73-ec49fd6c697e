/**
 * Hologram Software - Main Architecture Specification
 * Consolidated dual-brain architecture with verified capabilities
 * This is the main specification file - other files should extend this
 */

specification {
  element actor
  element system  
  element container
  element component
  element service
  element database
  element queue
  element application
  element module
  element server
  element cloud
  element device
  element coordinator
  
  relationship async
  relationship sync
  relationship uses
  relationship includes
  relationship dotted
  relationship external
  relationship internal
  relationship data
  relationship control
  relationship manages
}

model {
  hologramSoftware = system 'Hologram Software' {
    description 'Enterprise-grade AI agent system with dual-brain architecture'
  }
  
  // Note: user actor is defined in dual-brain-system.c4 as the authoritative definition
  
  // user -> hologramSoftware 'Interacts with AI agent' // Note: user defined in dual-brain-system.c4
}