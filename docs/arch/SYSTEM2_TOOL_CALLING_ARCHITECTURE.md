# System 2 Tool Calling Architecture - Solution Design

## 🚨 Problem Analysis

**Issue**: System 2 (Reasoning Brain) generates responses but doesn't call available speaking and animation tools, despite being configured with tool calling capabilities.

**Root Cause**: The dual-brain architecture correctly routes complex queries to System 2, but the tool calling flow is disconnected between:
1. **SystemInvoker service** - Handles model invocation
2. **Core agent service** - Manages tool binding with `bindTools()`
3. **Tool execution flow** - Missing proper integration

## 🏗️ Current Architecture Analysis

### System 1 vs System 2 Tool Calling

```mermaid
graph TD
    A[User Input] --> B[DualBrainCoordinator]
    B --> C{Route Decision}
    
    C -->|Simple Query| D[System 1 - WebSocket]
    C -->|Complex Query| E[System 2 - HTTP]
    
    D --> F[NO TOOLS]
    D --> G[Audio Output Only]
    
    E --> H[SystemInvoker.invokeSystem2]
    H --> I[AliyunHttpChatModel]
    I --> J[LLM Response Generated]
    J --> K[❌ TOOLS NOT CALLED]
    
    style K fill:#ff9999
    style F fill:#cccccc
```

### Current Tool Binding in Core.js

```javascript
// ✅ CORRECT: Core.js binds tools to System 2
const modelForTools = (this.isDualBrainMode() && this.models?.system2) 
    ? this.models.system2 : this.model;
const llmWithTools = modelForTools.bindTools(this.tools, {
    tool_choice: this._getToolChoiceForTools()
});
```

### Missing Integration Points

1. **SystemInvoker** → **Core Agent Service** integration gap
2. **Tool execution context** not passed through dual-brain flow
3. **LangGraph agent workflow** bypassed in System 2 invocation

## 🎯 Solution Architecture

### Enhanced System 2 Tool Calling Flow

```mermaid
graph TD
    A[User Input] --> B[DualBrainCoordinator.processMultiAgentRequest]
    B --> C[SystemInvoker.invokeSupervisor]
    C --> D[Route to System 2]
    D --> E[SystemInvoker.invokeSystem2]
    
    E --> F[✅ NEW: Check enableToolCalling]
    F -->|true| G[✅ NEW: Use Core Agent Service]
    F -->|false| H[Direct Model Invocation]
    
    G --> I[AgentService.processInput with tools]
    I --> J[LangGraph ReactAgent with bindTools]
    J --> K[Tool Execution]
    K --> L[Speaking/Animation Output]
    
    H --> M[Text Response Only]
    
    style G fill:#99ff99
    style I fill:#99ff99
    style J fill:#99ff99
    style K fill:#99ff99
```

### Tool Calling Decision Matrix

| Input Characteristics   | System   | Tool Calling | Reasoning            |
| ----------------------- | -------- | ------------ | -------------------- |
| Simple query < 50 chars | System 1 | ❌ No tools   | Fast response only   |
| Complex reasoning       | System 2 | ✅ Full tools | Analysis + Actions   |
| Proactive decisions     | System 2 | ✅ Full tools | Speaking + Animation |
| Questions with analysis | System 2 | ✅ Full tools | Research + Response  |

## 🔧 Implementation Strategy

### Phase 1: SystemInvoker Enhancement

**File**: `/src/agent/arch/dualbrain/services/SystemInvoker.js`

```javascript
// Enhanced _invokeSystem2Model method
async _invokeSystem2Model(request, options = {}) {
    if (!this.system2Model) {
        throw new Error('System 2 model not initialized');
    }

    // ✅ NEW: Check if tool calling is enabled and agent service is available
    if (options.enableToolCalling && this.agentService) {
        logger.debug('🔧 System 2 using agent service for tool calling');
        
        // Use full agent service workflow with tool binding
        return await this._invokeWithAgentService(request, options);
    }

    // Fallback to direct model invocation
    return await this._invokeDirectModel(request, options);
}

// ✅ NEW: Agent service integration for tool calling
async _invokeWithAgentService(request, options) {
    const messageContent = Array.isArray(request) ? request[0].content : request;
    
    // Process through agent service with full tool calling support
    const result = await this.agentService.processInput(messageContent, {
        useSystem2: true,
        enableTools: true,
        context: options.context || {},
        streaming: false // System 2 uses HTTP, not streaming
    });

    return {
        data: result,
        metadata: {
            toolCallsExecuted: result.toolCalls?.length || 0,
            method: 'agent_service_with_tools'
        }
    };
}
```

### Phase 1.5: Centralized Tool Management

All tool registration, binding, formatting, and execution are now centralized in `src/agent/services/ToolManagementService.js`.

- Use `toolManagementService.bindToolsToModel(model, tools, options)` for binding
- Use `toolManagementService.formatToolsForOpenAI(tools)` for OpenAI-compatible formatting
- Use `toolManagementService.executeToolCall(toolCallData, services, options)` for execution

This removes scattered `bindTools()` calls and ad-hoc tool formatting. Core and System 2 paths delegate through the service.

### Phase 2: Enhanced Tool Binding Configuration

**File**: `/src/agent/arch/dualbrain/DualBrainCoordinator.js`

```javascript
// Enhanced initialization with agent service reference
async initialize() {
    // ... existing initialization code ...

    // ✅ NEW: Configure SystemInvoker with agent service reference
    if (this.services?.systemInvoker) {
        this.services.systemInvoker.setAgentService(this.agentService);
        logger.info('🔗 SystemInvoker configured with agent service for tool calling');
    }

    // ✅ NEW: Verify tool availability for System 2
    const toolsAvailable = this.agentService?.tools?.length || 0;
    logger.info(`🔧 System 2 tool calling ready: ${toolsAvailable} tools available`);
}

// Enhanced routing with tool calling context
_routeToAppropriateSystem(input, options = {}) {
    const routing = this._analyzeInputComplexity(input, options);
    
    if (routing.targetSystem === 'system2') {
        // ✅ NEW: Always enable tool calling for System 2
        routing.capabilities = routing.capabilities || [];
        if (!routing.capabilities.includes('tools')) {
            routing.capabilities.push('tools');
        }
        if (!routing.capabilities.includes('speaking')) {
            routing.capabilities.push('speaking');
        }
    }
    
    return routing;
}
```

### Phase 3: Tool Integration Verification

**Speaking Tools Integration**:
- ✅ `avatarSpeechControlTool` - Controls avatar speech behavior
- ✅ `agentTTSTool` - Text-to-speech conversion
- ✅ `voiceProfileTool` - Voice cloning management

**Animation Tools Integration**:
- ✅ `selectAnimationTool` - LangGraph semantic animation search
- ✅ `listAnimationsTool` - Available animations catalog  
- ✅ `recommendAnimationsTool` - Contextual recommendations
- ✅ `stopAnimationTool` - Animation control
- ✅ `randomTalkingAnimationTool` - Conversational animations

### Phase 4: Response Processing Enhancement

**File**: `/src/agent/arch/dualbrain/services/DecisionProcessor.js`

```javascript
// Enhanced decision processing for tool calls
async _parseAiResponse(aiResponse, decisionId) {
    const decisions = [];

    // ✅ ENHANCED: Better tool call detection and processing
    if (aiResponse.toolCalls && aiResponse.toolCalls.length > 0) {
        logger.debug(`🔧 Processing ${aiResponse.toolCalls.length} tool calls from System 2`);
        
        for (const toolCall of aiResponse.toolCalls) {
            decisions.push({
                id: `${decisionId}_tool_${decisions.length}`,
                type: DecisionType.TOOL_EXECUTION,
                priority: this._getToolCallPriority(toolCall),
                toolCall: toolCall,
                timestamp: Date.now(),
                metadata: {
                    toolName: toolCall.function?.name || toolCall.name,
                    hasArguments: !!(toolCall.function?.arguments || toolCall.arguments),
                    source: 'system2_reasoning'
                }
            });
        }
    }

    // ✅ NEW: Handle direct text responses that should trigger speaking
    if (aiResponse.content && !decisions.length) {
        logger.debug('🎤 System 2 text response detected, adding speaking decision');
        
        decisions.push({
            id: `${decisionId}_speak`,
            type: DecisionType.TOOL_EXECUTION,
            priority: DecisionPriority.HIGH,
            toolCall: {
                function: {
                    name: 'control_avatar_speech',
                    arguments: JSON.stringify({
                        action: 'speak',
                        text: aiResponse.content,
                        voice: 'Serena',
                        options: { streaming: true }
                    })
                }
            },
            timestamp: Date.now(),
            metadata: {
                toolName: 'control_avatar_speech',
                autoGenerated: true,
                source: 'system2_text_response'
            }
        });
    }

    return decisions;
}

// ✅ NEW: Smart tool call priority assignment
_getToolCallPriority(toolCall) {
    const toolName = toolCall.function?.name || toolCall.name;
    
    const priorityMap = {
        'control_avatar_speech': DecisionPriority.HIGH,
        'speak_response': DecisionPriority.HIGH,
        'select_animation': DecisionPriority.MEDIUM,
        'random_talking_animation': DecisionPriority.MEDIUM,
        'stop_animation': DecisionPriority.HIGH,
        'play_audio': DecisionPriority.HIGH
    };
    
    return priorityMap[toolName] || DecisionPriority.MEDIUM;
}
```

## 🧪 Testing Strategy

### Integration Test Cases

1. **System 2 Tool Calling Verification**
   ```javascript
   // Test: Complex query triggers System 2 with tools
   const result = await coordinator.processMultiAgentRequest(
       "I'm feeling happy, can you dance for me?", 
       { requiresReasoning: true }
   );
   
   expect(result.system).toBe('system2');
   expect(result.toolCalls).toHaveLength(2); // animation + speaking
   expect(result.toolCalls[0].function.name).toBe('select_animation');
   expect(result.toolCalls[1].function.name).toBe('control_avatar_speech');
   ```

2. **No-Activity Proactive Behavior**
   ```javascript
   // Test: Proactive decision triggers speaking
   const proactiveResult = await coordinator.generateProactiveDecision(
       'quiet_period', { timeSinceLastInput: 10000 }
   );
   
   expect(proactiveResult.decisions).toContain(
       expect.objectContaining({
           type: 'TOOL_EXECUTION',
           toolCall: expect.objectContaining({
               function: expect.objectContaining({
                   name: 'control_avatar_speech'
               })
           })
       })
   );
   ```

3. **Tool Execution Verification**
   ```javascript
   // Test: Actual tool execution
   const mockAvatarController = { stateManager: { startSpeaking: jest.fn() } };
   const speakingResult = await avatarSpeechControlTool.func(
       { action: 'speak', text: 'Hello!', voice: 'Serena' },
       { avatarController: mockAvatarController }
   );
   
   expect(speakingResult.success).toBe(true);
   expect(mockAvatarController.stateManager.startSpeaking).toHaveBeenCalled();
   ```

## 🚀 Performance Considerations

### Optimization Strategies

1. **Tool Call Batching**: Group multiple tool calls (animation + speaking)
2. **Context Preservation**: Maintain conversation context across tool calls
3. **Error Recovery**: Graceful fallback when tools fail
4. **Memory Management**: Efficient tool result caching

### Monitoring Metrics

- **Tool Call Success Rate**: % of successful tool executions
- **System 2 Response Time**: Including tool execution
- **Tool Call Distribution**: Most used tools for optimization
- **Error Patterns**: Common tool call failures

## 📋 Implementation Checklist

- [x] ✅ **Architecture Analysis Complete**
- [x] ✅ **Root Cause Identified** 
- [ ] 🔄 **SystemInvoker Enhancement** (In Progress)
- [ ] 📋 **DualBrainCoordinator Updates**
- [ ] 📋 **DecisionProcessor Improvements**
- [ ] 📋 **Integration Testing**
- [ ] 📋 **Performance Validation**
- [ ] 📋 **Documentation Updates**

## 🎯 Expected Outcomes

After implementation:

1. **System 2 Tool Calling**: ✅ Complex queries will trigger both reasoning AND tool execution
2. **Proactive Behavior**: ✅ No-activity periods will generate speaking + animation responses  
3. **Seamless Integration**: ✅ Tools work naturally within dual-brain conversation flow
4. **Performance Maintained**: ✅ Sub-600ms System 1, enhanced System 2 with tools

## 🔗 Related Documentation

- [DUAL_BRAIN_ARCHITECTURE_README.md](./DUAL_BRAIN_ARCHITECTURE_README.md) - Main architecture
- [Speaking Tools](../src/agent/tools/avatar/speaking.js) - Speech capabilities
- [Animation Tools](../src/agent/tools/avatar/animation.js) - Animation system
- [SystemInvoker Service](../src/agent/arch/dualbrain/services/SystemInvoker.js) - Model invocation

---

**Implementation Priority**: 🔴 **CRITICAL** - Core functionality for avatar interaction
**Estimated Effort**: 4-6 hours development + 2 hours testing
**Risk Level**: 🟡 **MEDIUM** - Touches core dual-brain flow but well-isolated changes