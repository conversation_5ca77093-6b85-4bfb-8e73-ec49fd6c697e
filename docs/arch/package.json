{"name": "hologram-architecture-docs", "version": "1.0.0", "description": "Hologram Software - Architecture Documentation with LikeC4", "type": "module", "private": true, "scripts": {"arch:dev": "likec4 start .", "arch:build": "likec4 build . -o ./generated/", "arch:export:png": "likec4 export png . -o ./images/", "arch:export:svg": "likec4 export svg . -o ./images/", "arch:export:mermaid": "likec4 export mermaid . -o ./static/", "arch:export:all": "npm run arch:export:png && npm run arch:export:svg && npm run arch:export:mermaid", "arch:preview": "likec4 serve ./generated/", "arch:all": "npm run arch:build && npm run arch:export:all", "clean": "rm -rf ./generated ./images ./static", "install-cli": "npm install -g likec4@latest", "show-structure": "./show-structure.sh", "help": "./show-structure.sh", "mcp:info": "echo \"⚠️  MCP Server: Use VSCode Extension with built-in MCP server\"", "dev:all": "concurrently \"npm run arch:dev\" \"npm run mcp:info\""}, "devDependencies": {"likec4": "^1.34.2", "concurrently": "^8.2.2"}, "keywords": ["architecture", "documentation", "likec4", "diagrams", "hologram-software"], "author": "Hologram Software Team", "license": "MIT"}