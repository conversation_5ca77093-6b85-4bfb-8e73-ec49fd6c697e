# Character Search System - Integration Specifications

## Component Integration Details

### 1. CharacterSearchTool Implementation

**File**: `/src/agent/tools/character/CharacterSearch.js`

#### Tool Structure
```javascript
import { BaseToolNode } from '../base/BaseToolNode.js';
import { tool } from '@langchain/core/tools';
import { z } from 'zod';

// Character Search Schema
const CharacterSearchSchema = z.object({
  query: z.string().describe('Character name or description to search for'),
  source: z.enum(['One Piece', 'One-Punch Man', 'Naruto', 'Attack on Titan', 'any']).optional().describe('Anime source to filter by'),
  includePersonality: z.boolean().default(true).describe('Whether to analyze personality traits'),
  maxResults: z.number().min(1).max(20).default(10).describe('Maximum number of results to return')
});

// Character Analysis Schema  
const CharacterAnalysisSchema = z.object({
  characterData: z.object({
    name: z.string(),
    source: z.string(),
    description: z.string(),
    abilities: z.array(z.string()).optional(),
    imageUrl: z.string().optional()
  }).describe('Character data to analyze'),
  analysisType: z.enum(['personality', 'background', 'abilities', 'relationships']).default('personality').describe('Type of analysis to perform')
});
```

#### Tool Implementations
```javascript
// Web Search Tool for Characters
export const characterWebSearchTool = tool(
  async ({ query, source, maxResults }) => {
    const searchQuery = source && source !== 'any' 
      ? `${query} ${source} anime character` 
      : `${query} anime character`;
      
    // Use existing WebSearch tool
    const results = await webSearchTool.invoke({
      query: searchQuery,
      allowDomains: ['fandom.com', 'wiki.com', 'myanimelist.net'],
      maxResults
    });
    
    return {
      success: true,
      results: results.map(r => ({
        title: r.title,
        description: r.description,
        url: r.url,
        source: extractAnimeSource(r.title, r.description)
      }))
    };
  },
  {
    name: 'character_web_search',
    description: 'Search the web for anime character information',
    schema: CharacterSearchSchema
  }
);

// Character Information Extraction Tool
export const characterInfoTool = tool(
  async ({ characterData, analysisType }) => {
    // Extract structured character data from web results
    const extractedData = {
      id: generateCharacterId(characterData.name, characterData.source),
      name: characterData.name,
      source: characterData.source,
      description: characterData.description,
      avatar: selectCharacterEmoji(characterData),
      animeData: {
        abilities: characterData.abilities || [],
        firstAppearance: extractFirstAppearance(characterData.description),
        affiliations: extractAffiliations(characterData.description),
        imageUrl: characterData.imageUrl
      }
    };
    
    return {
      success: true,
      character: extractedData,
      analysisType
    };
  },
  {
    name: 'character_info_extraction',
    description: 'Extract structured character information',
    schema: CharacterAnalysisSchema
  }
);

// Character Tool Collection
export const characterToolCollection = [
  characterWebSearchTool,
  characterInfoTool
];
```

### 2. CharacterService Extensions

**File**: `/app/viewer/services/CharacterService.js` (Extensions)

#### New Methods
```javascript
export class CharacterService {
  constructor(options = {}) {
    // Existing constructor...
    
    // Add anime character storage
    this.animeCharacters = new Map();
    this.characterSources = new Set(['One Piece', 'One-Punch Man', 'Naruto', 'Attack on Titan']);
    this.searchIndex = new Map(); // For fast character search
  }

  /**
   * Search for anime characters
   */
  async searchCharacters(query, filters = {}) {
    const results = [];
    const normalizedQuery = query.toLowerCase();
    
    // Search through stored anime characters
    for (const [id, character] of this.animeCharacters.entries()) {
      if (this.matchesSearchCriteria(character, normalizedQuery, filters)) {
        results.push({
          ...character,
          relevanceScore: this.calculateRelevanceScore(character, normalizedQuery)
        });
      }
    }
    
    // Sort by relevance
    results.sort((a, b) => b.relevanceScore - a.relevanceScore);
    
    return results.slice(0, filters.maxResults || 20);
  }

  /**
   * Add anime character from search results
   */
  async addAnimeCharacter(characterData, source = 'web_search') {
    try {
      // Validate character structure
      if (!this.validateAnimeCharacterStructure(characterData)) {
        throw new Error('Invalid anime character structure');
      }

      // Generate personality traits from description if not provided
      if (!characterData.personality) {
        characterData.personality = await this.generatePersonalityFromDescription(characterData);
      }

      // Store the character
      this.animeCharacters.set(characterData.id, {
        ...characterData,
        addedAt: Date.now(),
        source,
        usageCount: 0
      });

      // Update search index
      this.updateSearchIndex(characterData);

      // Persist data
      if (this.options.enablePersistence) {
        this.saveData();
      }

      console.log('[CharacterService] Added anime character:', characterData.name);
      return true;

    } catch (error) {
      console.error('[CharacterService] Failed to add anime character:', error);
      return false;
    }
  }

  /**
   * Get characters by anime source
   */
  getCharactersBySource(source) {
    return Array.from(this.animeCharacters.values())
      .filter(char => char.source === source || char.animeData?.source === source);
  }

  /**
   * Generate character summary using System 2
   */
  async summarizeCharacter(characterId) {
    const character = this.animeCharacters.get(characterId) || 
                      this.characterPresets.find(c => c.id === characterId);
    
    if (!character) {
      throw new Error(`Character not found: ${characterId}`);
    }

    // Use DualBrainCoordinator for System 2 analysis
    if (this.options.agentCoordinator) {
      const dualBrainCoordinator = this.options.agentCoordinator
        .getAgentService()?.getDualBrainCoordinator?.();
      
      if (dualBrainCoordinator) {
        const summary = await dualBrainCoordinator.analyzeCharacterPersonality(character);
        return summary;
      }
    }

    // Fallback to local analysis
    return this.generateLocalCharacterSummary(character);
  }

  /**
   * Generate personality traits from character description
   */
  async generatePersonalityFromDescription(characterData) {
    const description = characterData.description || '';
    const abilities = characterData.animeData?.abilities || [];
    
    // Basic personality mapping based on keywords
    const personalityKeywords = {
      formality: {
        high: ['formal', 'respectful', 'honor', 'discipline', 'proper'],
        low: ['casual', 'carefree', 'relaxed', 'informal', 'laid-back']
      },
      enthusiasm: {
        high: ['energetic', 'excited', 'passionate', 'determined', 'motivated'],
        low: ['calm', 'stoic', 'reserved', 'quiet', 'composed']
      },
      empathy: {
        high: ['caring', 'kind', 'protective', 'loyal', 'compassionate'],
        low: ['cold', 'ruthless', 'independent', 'aloof', 'distant']
      },
      creativity: {
        high: ['creative', 'innovative', 'unique', 'imaginative', 'artistic'],
        low: ['traditional', 'methodical', 'straightforward', 'conventional']
      },
      directness: {
        high: ['direct', 'blunt', 'honest', 'straightforward', 'frank'],
        low: ['diplomatic', 'subtle', 'tactful', 'indirect', 'mysterious']
      }
    };

    const personality = {};
    const text = (description + ' ' + abilities.join(' ')).toLowerCase();

    for (const [trait, keywords] of Object.entries(personalityKeywords)) {
      let score = 0.5; // Default neutral
      let matchCount = 0;

      for (const keyword of keywords.high) {
        if (text.includes(keyword)) {
          score += 0.1;
          matchCount++;
        }
      }

      for (const keyword of keywords.low) {
        if (text.includes(keyword)) {
          score -= 0.1;
          matchCount++;
        }
      }

      // Normalize score and add some character-specific adjustments
      personality[trait] = Math.max(0.1, Math.min(0.9, score));
    }

    return personality;
  }

  /**
   * Update search index for fast character lookup
   */
  updateSearchIndex(character) {
    const searchTerms = [
      character.name.toLowerCase(),
      character.source?.toLowerCase(),
      character.animeData?.source?.toLowerCase(),
      ...(character.animeData?.abilities || []).map(a => a.toLowerCase()),
      ...(character.animeData?.affiliations || []).map(a => a.toLowerCase())
    ].filter(Boolean);

    for (const term of searchTerms) {
      if (!this.searchIndex.has(term)) {
        this.searchIndex.set(term, new Set());
      }
      this.searchIndex.get(term).add(character.id);
    }
  }

  /**
   * Check if character matches search criteria
   */
  matchesSearchCriteria(character, query, filters) {
    // Text matching
    const searchableText = [
      character.name,
      character.description,
      character.source,
      character.animeData?.source,
      ...(character.animeData?.abilities || []),
      ...(character.animeData?.affiliations || [])
    ].join(' ').toLowerCase();

    const textMatch = searchableText.includes(query);

    // Filter matching
    let filterMatch = true;
    
    if (filters.source && filters.source !== 'any') {
      filterMatch = filterMatch && (
        character.source === filters.source ||
        character.animeData?.source === filters.source
      );
    }

    return textMatch && filterMatch;
  }

  /**
   * Calculate relevance score for search results
   */
  calculateRelevanceScore(character, query) {
    let score = 0;
    
    // Exact name match gets highest score
    if (character.name.toLowerCase().includes(query)) {
      score += 10;
    }
    
    // Source match gets medium score
    if (character.source?.toLowerCase().includes(query) ||
        character.animeData?.source?.toLowerCase().includes(query)) {
      score += 5;
    }
    
    // Description match gets lower score
    if (character.description?.toLowerCase().includes(query)) {
      score += 3;
    }
    
    // Abilities/affiliations match gets lowest score
    const abilities = character.animeData?.abilities || [];
    const affiliations = character.animeData?.affiliations || [];
    
    for (const ability of abilities) {
      if (ability.toLowerCase().includes(query)) {
        score += 1;
      }
    }
    
    for (const affiliation of affiliations) {
      if (affiliation.toLowerCase().includes(query)) {
        score += 1;
      }
    }
    
    // Usage count bonus
    score += (character.usageCount || 0) * 0.1;
    
    return score;
  }

  /**
   * Validate anime character structure
   */
  validateAnimeCharacterStructure(character) {
    const required = ['id', 'name', 'description'];
    
    for (const field of required) {
      if (!character[field]) {
        console.error(`[CharacterService] Missing required field: ${field}`);
        return false;
      }
    }
    
    // Validate anime data if present
    if (character.animeData) {
      const animeRequired = ['source'];
      for (const field of animeRequired) {
        if (!character.animeData[field]) {
          console.error(`[CharacterService] Missing anime data field: ${field}`);
          return false;
        }
      }
    }
    
    return true;
  }

  // Override existing saveData to include anime characters
  saveData() {
    if (!this.options.enablePersistence) return;

    try {
      const data = {
        currentCharacter: this.currentCharacter,
        characterHistory: this.characterHistory,
        personalityProfiles: Array.from(this.personalityProfiles.entries()),
        consistencyMetrics: this.consistencyMetrics,
        animeCharacters: Array.from(this.animeCharacters.entries()), // Add anime characters
        searchIndex: Array.from(this.searchIndex.entries()).map(([key, set]) => [key, Array.from(set)]), // Save search index
        lastSaved: Date.now()
      };

      localStorage.setItem(this.options.storageKey, JSON.stringify(data));
    } catch (error) {
      console.warn('[CharacterService] Failed to save data:', error);
    }
  }

  // Override existing loadStoredData to include anime characters
  loadStoredData() {
    if (!this.options.enablePersistence) return;

    try {
      const stored = localStorage.getItem(this.options.storageKey);
      if (stored) {
        const data = JSON.parse(stored);
        
        // Load existing data
        if (data.currentCharacter) {
          this.currentCharacter = data.currentCharacter;
        }
        
        if (data.characterHistory) {
          this.characterHistory = data.characterHistory;
        }
        
        if (data.personalityProfiles) {
          this.personalityProfiles = new Map(data.personalityProfiles);
        }
        
        if (data.consistencyMetrics) {
          this.consistencyMetrics = data.consistencyMetrics;
        }

        // Load anime characters
        if (data.animeCharacters) {
          this.animeCharacters = new Map(data.animeCharacters);
        }

        // Load search index
        if (data.searchIndex) {
          this.searchIndex = new Map(data.searchIndex.map(([key, arr]) => [key, new Set(arr)]));
        }

        console.log('[CharacterService] Loaded stored character data with anime characters');
      }
    } catch (error) {
      console.warn('[CharacterService] Failed to load stored data:', error);
    }
  }
}
```

### 3. RolePlayingPanel UI Extensions

**File**: `/app/viewer/components/RolePlayingPanel.js` (Extensions)

#### New UI Components
```javascript
export class RolePlayingPanel {
  constructor(options = {}) {
    // Existing constructor...
    
    // Add search state
    this.searchResults = [];
    this.searchQuery = '';
    this.selectedSource = 'any';
    this.isSearching = false;
    this.voiceSearchActive = false;
  }

  /**
   * Create character search interface
   */
  createCharacterSearchInterface() {
    const content = this.panel.querySelector('.rp-content');
    
    const searchSection = document.createElement('div');
    searchSection.className = 'rp-section search-section';
    searchSection.innerHTML = `
      <h3 class="rp-section-title">Search Characters</h3>
      <div class="character-search-container">
        <div class="search-input-group">
          <input type="text" class="character-search-input" placeholder="Search anime characters...">
          <button class="voice-search-btn" title="Voice Search">
            <span class="voice-icon">🎤</span>
          </button>
          <button class="search-btn" title="Search">
            <span class="search-icon">🔍</span>
          </button>
        </div>
        <div class="search-filters">
          <select class="source-filter" id="source-filter">
            <option value="any">Any Source</option>
            <option value="One Piece">One Piece</option>
            <option value="One-Punch Man">One-Punch Man</option>
            <option value="Naruto">Naruto</option>
            <option value="Attack on Titan">Attack on Titan</option>
          </select>
          <button class="advanced-search-toggle">Advanced</button>
        </div>
        <div class="search-status">
          <span class="search-status-text">Enter a character name to search</span>
        </div>
      </div>
    `;

    // Insert search section before character grid
    const characterSection = content.querySelector('.rp-section');
    content.insertBefore(searchSection, characterSection);
    
    // Create search results container
    this.createSearchResultsContainer();
  }

  /**
   * Create search results display
   */
  createSearchResultsContainer() {
    const content = this.panel.querySelector('.rp-content');
    
    this.searchResultsContainer = document.createElement('div');
    this.searchResultsContainer.className = 'search-results-container hidden';
    this.searchResultsContainer.innerHTML = `
      <div class="search-results-header">
        <h4>Search Results</h4>
        <button class="close-search-results">×</button>
      </div>
      <div class="search-results-grid"></div>
      <div class="search-pagination">
        <button class="load-more-btn hidden">Load More</button>
      </div>
    `;

    // Insert after search section
    const searchSection = content.querySelector('.search-section');
    searchSection.insertAdjacentElement('afterend', this.searchResultsContainer);
  }

  /**
   * Perform character search
   */
  async performCharacterSearch(query, filters = {}) {
    if (!query.trim()) return;

    try {
      this.setSearchStatus('Searching...', true);
      this.searchQuery = query;
      this.selectedSource = filters.source || 'any';

      // Search local characters first
      const localResults = await this.options.characterService?.searchCharacters(query, filters) || [];
      
      // If we have enough local results, use them
      if (localResults.length >= 3) {
        this.displaySearchResults(localResults);
        this.setSearchStatus(`Found ${localResults.length} characters`, false);
        return;
      }

      // Search web for new characters
      const webResults = await this.searchWebForCharacters(query, filters);
      
      // Combine and display results
      const allResults = [...localResults, ...webResults];
      this.displaySearchResults(allResults);
      
      this.setSearchStatus(`Found ${allResults.length} characters`, false);

    } catch (error) {
      console.error('[RolePlayingPanel] Search failed:', error);
      this.setSearchStatus('Search failed. Please try again.', false);
    }
  }

  /**
   * Search web for characters using tools
   */
  async searchWebForCharacters(query, filters = {}) {
    try {
      // Use character search tool through agent coordinator
      if (!this.options.agentCoordinator) {
        return [];
      }

      const searchResult = await this.options.agentCoordinator.executeToolCall('character_web_search', {
        query,
        source: filters.source || 'any',
        maxResults: filters.maxResults || 10
      });

      if (!searchResult.success) {
        return [];
      }

      // Process search results and add to character service
      const characters = [];
      for (const result of searchResult.results) {
        try {
          // Extract character info
          const characterInfo = await this.options.agentCoordinator.executeToolCall('character_info_extraction', {
            characterData: {
              name: this.extractCharacterName(result.title),
              source: result.source,
              description: result.description,
              imageUrl: this.extractImageUrl(result)
            }
          });

          if (characterInfo.success) {
            const character = characterInfo.character;
            
            // Add to character service
            await this.options.characterService?.addAnimeCharacter(character, 'web_search');
            
            characters.push(character);
          }
        } catch (error) {
          console.warn('[RolePlayingPanel] Failed to process search result:', error);
        }
      }

      return characters;

    } catch (error) {
      console.error('[RolePlayingPanel] Web search failed:', error);
      return [];
    }
  }

  /**
   * Display search results
   */
  displaySearchResults(results) {
    const resultsGrid = this.searchResultsContainer.querySelector('.search-results-grid');
    resultsGrid.innerHTML = '';

    if (results.length === 0) {
      resultsGrid.innerHTML = `
        <div class="no-results">
          <p>No characters found for "${this.searchQuery}"</p>
          <p>Try a different search term or source filter</p>
        </div>
      `;
    } else {
      results.forEach(character => {
        const resultCard = this.createSearchResultCard(character);
        resultsGrid.appendChild(resultCard);
      });
    }

    // Show results container
    this.searchResultsContainer.classList.remove('hidden');
    this.searchResults = results;
  }

  /**
   * Create search result card
   */
  createSearchResultCard(character) {
    const card = document.createElement('div');
    card.className = 'search-result-card';
    card.setAttribute('data-character-id', character.id);
    
    const relevanceScore = character.relevanceScore || 0;
    const source = character.animeData?.source || character.source || 'Unknown';
    
    card.innerHTML = `
      <div class="result-card-header">
        <div class="character-avatar">${character.avatar || '👤'}</div>
        <div class="character-basic-info">
          <div class="character-name">${character.name}</div>
          <div class="character-source">${source}</div>
        </div>
        <div class="relevance-score">${Math.round(relevanceScore)}</div>
      </div>
      <div class="character-description">
        ${this.truncateDescription(character.description, 100)}
      </div>
      <div class="character-abilities">
        ${(character.animeData?.abilities || []).slice(0, 3).map(ability => 
          `<span class="ability-tag">${ability}</span>`
        ).join('')}
      </div>
      <div class="result-card-actions">
        <button class="preview-character-btn" data-character-id="${character.id}">
          Preview
        </button>
        <button class="select-character-btn" data-character-id="${character.id}">
          Select
        </button>
      </div>
    `;

    return card;
  }

  /**
   * Activate voice search
   */
  activateVoiceSearch() {
    if (!this.options.agentCoordinator) {
      alert('Voice search requires agent coordinator');
      return;
    }

    this.voiceSearchActive = true;
    this.setSearchStatus('Listening... Speak your character search', true);
    
    // Update voice button state
    const voiceBtn = this.panel.querySelector('.voice-search-btn');
    voiceBtn.classList.add('active');
    voiceBtn.querySelector('.voice-icon').textContent = '🔴';

    // Use System 1 for voice recognition
    this.processVoiceSearch().catch(error => {
      console.error('[RolePlayingPanel] Voice search failed:', error);
      this.setSearchStatus('Voice search failed', false);
      this.deactivateVoiceSearch();
    });
  }

  /**
   * Process voice search using System 1
   */
  async processVoiceSearch() {
    try {
      // Get DualBrainCoordinator
      const dualBrainCoordinator = this.options.agentCoordinator
        ?.getAgentService()?.getDualBrainCoordinator?.();
      
      if (!dualBrainCoordinator) {
        throw new Error('DualBrainCoordinator not available');
      }

      // Simulate voice input (in real implementation, would capture audio)
      const simulatedVoiceInput = await this.captureVoiceInput();
      
      // Process voice with System 1
      const searchQuery = await dualBrainCoordinator.processVoiceCharacterSearch(simulatedVoiceInput);
      
      if (searchQuery && searchQuery.trim()) {
        // Update search input
        const searchInput = this.panel.querySelector('.character-search-input');
        searchInput.value = searchQuery;
        
        // Perform search
        await this.performCharacterSearch(searchQuery, {
          source: this.selectedSource
        });
      }

    } finally {
      this.deactivateVoiceSearch();
    }
  }

  /**
   * Deactivate voice search
   */
  deactivateVoiceSearch() {
    this.voiceSearchActive = false;
    
    const voiceBtn = this.panel.querySelector('.voice-search-btn');
    voiceBtn.classList.remove('active');
    voiceBtn.querySelector('.voice-icon').textContent = '🎤';
    
    if (!this.isSearching) {
      this.setSearchStatus('Enter a character name to search', false);
    }
  }

  /**
   * Display character summary modal
   */
  async displayCharacterSummary(characterId) {
    try {
      const character = this.searchResults.find(c => c.id === characterId) ||
                       this.characterPresets.find(c => c.id === characterId);
      
      if (!character) return;

      // Get AI summary
      const summary = await this.options.characterService?.summarizeCharacter(characterId);
      
      // Create modal
      const modal = this.createCharacterSummaryModal(character, summary);
      document.body.appendChild(modal);
      
      // Show modal
      setTimeout(() => modal.classList.add('visible'), 10);

    } catch (error) {
      console.error('[RolePlayingPanel] Failed to display character summary:', error);
    }
  }

  /**
   * Create character summary modal
   */
  createCharacterSummaryModal(character, summary) {
    const modal = document.createElement('div');
    modal.className = 'character-summary-modal';
    modal.innerHTML = `
      <div class="modal-backdrop"></div>
      <div class="modal-content">
        <div class="modal-header">
          <h3>${character.name}</h3>
          <button class="close-modal-btn">×</button>
        </div>
        <div class="modal-body">
          <div class="character-overview">
            <div class="character-avatar-large">${character.avatar || '👤'}</div>
            <div class="character-details">
              <div class="character-source">${character.animeData?.source || character.source}</div>
              <div class="character-description">${character.description}</div>
            </div>
          </div>
          <div class="character-abilities-section">
            <h4>Abilities</h4>
            <div class="abilities-list">
              ${(character.animeData?.abilities || []).map(ability => 
                `<span class="ability-tag">${ability}</span>`
              ).join('')}
            </div>
          </div>
          <div class="ai-summary-section">
            <h4>AI Analysis</h4>
            <div class="ai-summary">${summary?.content || 'Analysis not available'}</div>
          </div>
          <div class="personality-preview">
            <h4>Personality Traits</h4>
            ${this.renderPersonalityPreview(character.personality)}
          </div>
        </div>
        <div class="modal-actions">
          <button class="select-character-from-modal" data-character-id="${character.id}">
            Select This Character
          </button>
        </div>
      </div>
    `;

    // Add event listeners
    modal.addEventListener('click', (e) => {
      if (e.target.matches('.close-modal-btn, .modal-backdrop')) {
        this.closeModal(modal);
      }
      
      if (e.target.matches('.select-character-from-modal')) {
        const characterId = e.target.getAttribute('data-character-id');
        this.selectSearchResult(characterId);
        this.closeModal(modal);
      }
    });

    return modal;
  }

  // ... Additional helper methods for search functionality
}
```

This comprehensive integration specification provides detailed implementation guidance for each component while maintaining compatibility with the existing system architecture. The character search system will seamlessly integrate with the current DualBrain coordination and CharacterService patterns.