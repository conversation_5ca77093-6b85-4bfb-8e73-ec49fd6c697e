<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1400" height="1000" fill="#f8fafc"/>
  
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#1a202c">
    Hologram Software - Dual-Brain Architecture Flow
  </text>
  <text x="700" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#718096">
    Critical Functions and Message Flow Analysis
  </text>
  
  <!-- System 1: WebSocket Brain Section - FIRST POSITION -->
  <rect x="50" y="100" width="280" height="160" rx="10" fill="#e6fffa" stroke="#38b2ac" stroke-width="2"/>
  <text x="190" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#234e52">
    System 1: WebSocket Brain
  </text>
  <text x="65" y="145" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">• Continuous Audio/Video Input</text>
  <text x="65" y="160" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">• Real-time Context Extraction</text>
  <text x="65" y="175" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">• VAD Detection</text>
  <text x="65" y="190" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">• Multimodal Processing</text>
  
  <!-- Key Function: AliyunRealtimeClient -->
  <rect x="65" y="205" width="250" height="35" rx="5" fill="#4fd1c7" stroke="#319795" stroke-width="1"/>
  <text x="190" y="223" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#234e52">
    AliyunRealtimeClient.processAudioChunk()
  </text>
  <text x="190" y="237" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#234e52">
    Rate Limited: 200ms intervals
  </text>
  
  <!-- Context Bridge (Critical Component) - SECOND POSITION -->
  <rect x="360" y="100" width="280" height="160" rx="10" fill="#fed7d7" stroke="#fc8181" stroke-width="3"/>
  <text x="500" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#822727">
    Context Bridge (Critical)
  </text>
  <text x="375" y="145" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">• Dual-Brain Coordination</text>
  <text x="375" y="160" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">• Context Sharing</text>
  <text x="375" y="175" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">• Multimodal Context Bridge</text>
  <text x="375" y="190" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">• Real-time Context Updates</text>
  
  <!-- Key Function: Context Bridge -->
  <rect x="375" y="205" width="250" height="35" rx="5" fill="#feb2b2" stroke="#e53e3e" stroke-width="1"/>
  <text x="500" y="223" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#822727">
    ContextBridge.updateContext()
  </text>
  <text x="500" y="237" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#822727">
    Max Context Age: 10s
  </text>
  
  <!-- System 2: HTTP Brain Section - THIRD POSITION -->
  <rect x="670" y="100" width="280" height="160" rx="10" fill="#fef5e7" stroke="#ed8936" stroke-width="2"/>
  <text x="810" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#744210">
    System 2: HTTP Brain
  </text>
  <text x="685" y="145" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">• Complex Reasoning</text>
  <text x="685" y="160" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">• Tool Calling</text>
  <text x="685" y="175" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">• LangGraph ReactAgent</text>
  <text x="685" y="190" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">• Proactive Decision Making</text>
  
  <!-- Key Function: LangGraphAgentService -->
  <rect x="685" y="205" width="250" height="35" rx="5" fill="#fbb863" stroke="#dd6b20" stroke-width="1"/>
  <text x="810" y="223" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#744210">
    LangGraphAgentService.processMessage()
  </text>
  <text x="810" y="237" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#744210">
    Sub-600ms Response Target
  </text>
  
  <!-- Proactive Analysis Engine - FOURTH POSITION -->
  <rect x="980" y="100" width="280" height="160" rx="10" fill="#e6ffed" stroke="#48bb78" stroke-width="2"/>
  <text x="1120" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#22543d">
    Proactive Analysis Engine
  </text>
  <text x="995" y="145" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">• Every 2s Analysis</text>
  <text x="995" y="160" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">• Engagement Monitoring</text>
  <text x="995" y="175" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">• Contextual Triggers</text>
  <text x="995" y="190" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">• Speaking Decisions</text>
  
  <!-- Key Function: ContextualAnalysisService -->
  <rect x="995" y="205" width="250" height="35" rx="5" fill="#9ae6b4" stroke="#38a169" stroke-width="1"/>
  <text x="1120" y="218" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#22543d">
    ContextualAnalysisService
  </text>
  <text x="1120" y="232" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#22543d">
    _makeProactiveSpeakingDecision()
  </text>
  
  <!-- Flow Arrows and Messages -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4a5568"/>
    </marker>
    <marker id="critical-arrow" markerWidth="12" markerHeight="9" refX="11" refY="4.5" orient="auto">
      <polygon points="0 0, 12 4.5, 0 9" fill="#e53e3e"/>
    </marker>
  </defs>
  
  <!-- Flow: System 1 → Context Bridge -->
  <path d="M 330 180 L 360 180" fill="none" stroke="#e53e3e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="345" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#e53e3e">
    Multimodal Context Stream
  </text>
  
  <!-- Flow: Context Bridge → System 2 -->
  <path d="M 640 180 L 670 180" fill="none" stroke="#e53e3e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="655" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#e53e3e">
    Enriched Context Feed
  </text>
  
  <!-- Flow: System 2 → Proactive Engine -->
  <path d="M 950 180 L 980 180" fill="none" stroke="#e53e3e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="965" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#e53e3e">
    Context Updates
  </text>
  
  <!-- Tool Execution Layer -->
  <rect x="150" y="300" width="1100" height="120" rx="10" fill="#f7fafc" stroke="#a0aec0" stroke-width="2"/>
  <text x="700" y="325" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2d3748">
    Tool Execution Layer
  </text>
  
  <!-- Tool Manager -->
  <rect x="180" y="340" width="200" height="65" rx="5" fill="#e2e8f0" stroke="#718096" stroke-width="1"/>
  <text x="280" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2d3748">
    ToolManager
  </text>
  <text x="190" y="375" font-family="Arial, sans-serif" font-size="9" fill="#4a5568">• Tool Registration</text>
  <text x="190" y="387" font-family="Arial, sans-serif" font-size="9" fill="#4a5568">• executeToolCall()</text>
  <text x="190" y="399" font-family="Arial, sans-serif" font-size="9" fill="#4a5568">• Service Injection</text>
  
  <!-- Avatar Tools -->
  <rect x="400" y="340" width="200" height="65" rx="5" fill="#e2e8f0" stroke="#718096" stroke-width="1"/>
  <text x="500" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2d3748">
    Avatar Tools
  </text>
  <text x="410" y="375" font-family="Arial, sans-serif" font-size="9" fill="#4a5568">• control_avatar_speech</text>
  <text x="410" y="387" font-family="Arial, sans-serif" font-size="9" fill="#4a5568">• speak_response</text>
  <text x="410" y="399" font-family="Arial, sans-serif" font-size="9" fill="#4a5568">• select_animation</text>
  
  <!-- Service Layer -->
  <rect x="620" y="340" width="200" height="65" rx="5" fill="#e2e8f0" stroke="#718096" stroke-width="1"/>
  <text x="720" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2d3748">
    Service Layer
  </text>
  <text x="630" y="375" font-family="Arial, sans-serif" font-size="9" fill="#4a5568">• AgentSpeakingService</text>
  <text x="630" y="387" font-family="Arial, sans-serif" font-size="9" fill="#4a5568">• AnimationRetriever</text>
  <text x="630" y="399" font-family="Arial, sans-serif" font-size="9" fill="#4a5568">• ConnectionCoordination</text>
  
  <!-- Output Layer -->
  <rect x="840" y="340" width="200" height="65" rx="5" fill="#e2e8f0" stroke="#718096" stroke-width="1"/>
  <text x="940" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2d3748">
    Output Layer
  </text>
  <text x="850" y="375" font-family="Arial, sans-serif" font-size="9" fill="#4a5568">• TTS Generation</text>
  <text x="850" y="387" font-family="Arial, sans-serif" font-size="9" fill="#4a5568">• Avatar Animation</text>
  <text x="850" y="399" font-family="Arial, sans-serif" font-size="9" fill="#4a5568">• Audio Output</text>
  
  <!-- Tool Flow from System 2 -->
  <path d="M 810 260 L 280 340" fill="none" stroke="#4a5568" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="545" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#4a5568">Tool Decision</text>
  
  <!-- Curved Flow from Proactive Analysis Engine to Tool Execution -->
  <path d="M 1120 260 Q 1200 280 700 340" fill="none" stroke="#48bb78" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="910" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#48bb78">
    Decision Trigger
  </text>
  
  <!-- Horizontal Tool Flow Arrows -->
  <path d="M 380 372 L 400 372" fill="none" stroke="#4a5568" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 600 372 L 620 372" fill="none" stroke="#4a5568" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 820 372 L 840 372" fill="none" stroke="#4a5568" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Decision Flow Section -->
  <rect x="50" y="460" width="1300" height="100" rx="10" fill="#fffaf0" stroke="#f6ad55" stroke-width="2"/>
  <text x="700" y="485" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#c05621">
    Proactive Decision Flow (Every 2 seconds)
  </text>
  
  <!-- Decision Steps -->
  <rect x="80" y="505" width="140" height="50" rx="5" fill="#fed7c7" stroke="#f6ad55" stroke-width="1"/>
  <text x="150" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#c05621">
    Context Analysis
  </text>
  <text x="90" y="538" font-family="Arial, sans-serif" font-size="8" fill="#7b341e">• Engagement Level</text>
  <text x="90" y="548" font-family="Arial, sans-serif" font-size="8" fill="#7b341e">• Silence Duration</text>
  
  <rect x="250" y="505" width="140" height="50" rx="5" fill="#fed7c7" stroke="#f6ad55" stroke-width="1"/>
  <text x="320" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#c05621">
    Trigger Evaluation
  </text>
  <text x="260" y="538" font-family="Arial, sans-serif" font-size="8" fill="#7b341e">• Multimodal Cues</text>
  <text x="260" y="548" font-family="Arial, sans-serif" font-size="8" fill="#7b341e">• User Disengagement</text>
  
  <rect x="420" y="505" width="140" height="50" rx="5" fill="#fed7c7" stroke="#f6ad55" stroke-width="1"/>
  <text x="490" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#c05621">
    Decision Matrix
  </text>
  <text x="430" y="538" font-family="Arial, sans-serif" font-size="8" fill="#7b341e">• Confidence Score</text>
  <text x="430" y="548" font-family="Arial, sans-serif" font-size="8" fill="#7b341e">• Urgency Level</text>
  
  <rect x="590" y="505" width="140" height="50" rx="5" fill="#fed7c7" stroke="#f6ad55" stroke-width="1"/>
  <text x="660" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#c05621">
    Tool Selection
  </text>
  <text x="600" y="538" font-family="Arial, sans-serif" font-size="8" fill="#7b341e">• LLM Choice</text>
  <text x="600" y="548" font-family="Arial, sans-serif" font-size="8" fill="#7b341e">• Context-Aware</text>
  
  <rect x="760" y="505" width="140" height="50" rx="5" fill="#fed7c7" stroke="#f6ad55" stroke-width="1"/>
  <text x="830" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#c05621">
    Response Gen
  </text>
  <text x="770" y="538" font-family="Arial, sans-serif" font-size="8" fill="#7b341e">• Sub-600ms</text>
  <text x="770" y="548" font-family="Arial, sans-serif" font-size="8" fill="#7b341e">• Context-Rich</text>
  
  <rect x="930" y="505" width="140" height="50" rx="5" fill="#fed7c7" stroke="#f6ad55" stroke-width="1"/>
  <text x="1000" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#c05621">
    Avatar Output
  </text>
  <text x="940" y="538" font-family="Arial, sans-serif" font-size="8" fill="#7b341e">• TTS + Animation</text>
  <text x="940" y="548" font-family="Arial, sans-serif" font-size="8" fill="#7b341e">• Multimodal</text>
  
  <!-- Decision Flow Arrows -->
  <path d="M 220 530 L 250 530" fill="none" stroke="#f6ad55" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 390 530 L 420 530" fill="none" stroke="#f6ad55" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 560 530 L 590 530" fill="none" stroke="#f6ad55" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 730 530 L 760 530" fill="none" stroke="#f6ad55" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 900 530 L 930 530" fill="none" stroke="#f6ad55" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Proactive Engine to Decision Flow (curved path avoiding blocks) -->
  <path d="M 1120 260 Q 1200 350 660 505" fill="none" stroke="#f6ad55" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="890" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#f6ad55">
    Proactive Decision
  </text>
  
  <!-- Configuration and Performance Layer -->
  <rect x="50" y="600" width="600" height="80" rx="10" fill="#f0fff4" stroke="#48bb78" stroke-width="2"/>
  <text x="350" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#22543d">
    Configuration and Performance Layer
  </text>
  
  <rect x="70" y="640" width="120" height="35" rx="5" fill="#c6f6d5" stroke="#38a169" stroke-width="1"/>
  <text x="130" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#22543d">
    AliyunConfig.js
  </text>
  <text x="80" y="668" font-family="Arial, sans-serif" font-size="8" fill="#22543d">Single Source Truth</text>
  
  <rect x="210" y="640" width="120" height="35" rx="5" fill="#c6f6d5" stroke="#38a169" stroke-width="1"/>
  <text x="270" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#22543d">
    ModelFactory
  </text>
  <text x="220" y="668" font-family="Arial, sans-serif" font-size="8" fill="#22543d">Intelligent Routing</text>
  
  <rect x="350" y="640" width="120" height="35" rx="5" fill="#c6f6d5" stroke="#38a169" stroke-width="1"/>
  <text x="410" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#22543d">
    CircuitBreaker
  </text>
  <text x="360" y="668" font-family="Arial, sans-serif" font-size="8" fill="#22543d">Service Protection</text>
  
  <rect x="490" y="640" width="140" height="35" rx="5" fill="#c6f6d5" stroke="#38a169" stroke-width="1"/>
  <text x="560" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#22543d">
    PerformanceTracker
  </text>
  <text x="500" y="668" font-family="Arial, sans-serif" font-size="8" fill="#22543d">Real-time Metrics</text>
  
  <!-- Error Handling and Recovery System -->
  <rect x="700" y="600" width="650" height="80" rx="10" fill="#fef5e7" stroke="#ed8936" stroke-width="2"/>
  <text x="1025" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#744210">
    Error Handling and Recovery System
  </text>
  
  <rect x="720" y="640" width="120" height="35" rx="5" fill="#fbb863" stroke="#dd6b20" stroke-width="1"/>
  <text x="780" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#744210">
    Connection Recovery
  </text>
  <text x="730" y="668" font-family="Arial, sans-serif" font-size="8" fill="#744210">Auto Reconnect</text>
  
  <rect x="860" y="640" width="120" height="35" rx="5" fill="#fbb863" stroke="#dd6b20" stroke-width="1"/>
  <text x="920" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#744210">
    Rate Limiting
  </text>
  <text x="870" y="668" font-family="Arial, sans-serif" font-size="8" fill="#744210">200ms Intervals</text>
  
  <rect x="1000" y="640" width="120" height="35" rx="5" fill="#fbb863" stroke="#dd6b20" stroke-width="1"/>
  <text x="1060" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#744210">
    Graceful Degradation
  </text>
  <text x="1010" y="668" font-family="Arial, sans-serif" font-size="8" fill="#744210">Fallback Responses</text>
  
  <rect x="1140" y="640" width="120" height="35" rx="5" fill="#fbb863" stroke="#dd6b20" stroke-width="1"/>
  <text x="1200" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#744210">
    Validation Layer
  </text>
  <text x="1150" y="668" font-family="Arial, sans-serif" font-size="8" fill="#744210">Config Validation</text>
  
  <!-- Key Performance Metrics -->
  <rect x="50" y="720" width="1300" height="80" rx="10" fill="#e6fffa" stroke="#319795" stroke-width="2"/>
  <text x="700" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#234e52">
    Key Performance Metrics and Critical Thresholds
  </text>
  
  <text x="100" y="765" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#234e52">
    • Sub-600ms Response Time
  </text>
  <text x="350" y="765" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#234e52">
    • 200ms Audio Rate Limiting
  </text>
  <text x="580" y="765" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#234e52">
    • 16kHz PCM16 Audio
  </text>
  <text x="800" y="765" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#234e52">
    • 2s Proactive Analysis
  </text>
  <text x="1050" y="765" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#234e52">
    • 85%+ Cache Hit Rate
  </text>
  
  <text x="100" y="785" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#234e52">
    • 10s Max Context Age
  </text>
  <text x="350" y="785" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#234e52">
    • 5-Failure Circuit Breaker
  </text>
  <text x="580" y="785" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#234e52">
    • 5min TTL Cache Duration
  </text>
  <text x="800" y="785" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#234e52">
    • 30s Recovery Timeout
  </text>
  <text x="1050" y="785" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#234e52">
    • LangGraph v0.3 Compliance
  </text>
  
  <!-- Legend -->
  <rect x="50" y="830" width="1300" height="50" rx="10" fill="#f7fafc" stroke="#cbd5e0" stroke-width="1"/>
  <text x="700" y="850" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2d3748">
    Legend and Critical Path Indicators
  </text>
  
  <rect x="80" y="860" width="15" height="15" fill="#e53e3e"/>
  <text x="105" y="872" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">Critical Path (Context Bridge Flow)</text>
  
  <rect x="300" y="860" width="15" height="15" fill="#4fd1c7"/>
  <text x="325" y="872" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">System 1 (WebSocket Brain)</text>
  
  <rect x="500" y="860" width="15" height="15" fill="#fbb863"/>
  <text x="525" y="872" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">System 2 (HTTP Brain)</text>
  
  <rect x="700" y="860" width="15" height="15" fill="#9ae6b4"/>
  <text x="725" y="872" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">Proactive Analysis Engine</text>
  
  <rect x="900" y="860" width="15" height="15" fill="#fed7c7"/>
  <text x="925" y="872" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">Decision Flow (2s Cycle)</text>
  
  <rect x="1100" y="860" width="15" height="15" fill="#e2e8f0"/>
  <text x="1125" y="872" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">Supporting Infrastructure</text>
</svg>