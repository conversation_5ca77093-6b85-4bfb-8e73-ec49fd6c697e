// Client-side endpoints configuration
// This file provides a centralized place for all API endpoints
// that can be used in both server and client contexts

// Import the centralized environment variable utility
import { getEnvVar, isBrowser } from '@/config/env';

// Get the base URL for proxies when in browser mode
const getProxyBaseUrl = () => {
    if (!isBrowser) return '';

    // In browser mode, use the current origin for proxy endpoints
    const origin = window.location.origin;
    const downloadServerPort = getEnvVar('VITE_DOWNLOAD_SERVER_PORT', '2994');

    // Always use the download server port for proxy endpoints
    // This ensures we're connecting to the correct server
    const downloadServerUrl = origin.replace(/:\d+/, `:${downloadServerPort}`);

    console.log(`[Config] Using download server at ${downloadServerUrl} for proxies`);
    return downloadServerUrl;
};

// Test server connectivity and provide fallback
let proxyServerStatus: 'unknown' | 'healthy' | 'unhealthy' = 'unknown';

async function testProxyServerConnectivity(): Promise<boolean> {
    if (!isBrowser) return true;

    const testUrl = `${getProxyBaseUrl()}/proxy-status`;
    try {
        const response = await fetch(testUrl, {
            method: 'GET',
            signal: AbortSignal.timeout(2000) // 2 second timeout
        });
        const isHealthy = response.ok;
        proxyServerStatus = isHealthy ? 'healthy' : 'unhealthy';

        if (!isHealthy) {
            console.warn(`🚨 Download server unhealthy at ${testUrl} (status: ${response.status})`);
            console.warn('💡 Please start the download server with: npm run server');
        }

        return isHealthy;
    } catch (error) {
        proxyServerStatus = 'unhealthy';
        console.warn(`🚨 Download server connection failed: ${testUrl}`);
        console.warn('💡 Please start the download server with: npm run server');
        console.warn('📋 Connection error:', (error as Error).message);
        return false;
    }
}

// Test connectivity on module load (non-blocking)
if (isBrowser) {
    testProxyServerConnectivity().catch(() => {
        // Silently fail - we've already logged the warnings
    });
}

export { testProxyServerConnectivity, proxyServerStatus };

// Define all API endpoints with fallbacks
export const endpoints = {
    // Letta API endpoint - use proxy in browser mode
    letta: isBrowser
        ? `${getProxyBaseUrl()}/letta-proxy/`
        : getEnvVar('VITE_LETTA_API_ENDPOINT', 'http://***********:20096/'),

    // vLLM API endpoint - use proxy in browser mode
    vllm: isBrowser
        ? `${getProxyBaseUrl()}/vllm-proxy/`
        : getEnvVar('VITE_LLM_API_ENDPOINT', 'http://***********:20095/'),

    // SGLang API endpoint - use proxy in browser mode
    sglang: isBrowser
        ? `${getProxyBaseUrl()}/sglang-proxy/`
        : getEnvVar('VITE_SGLANG_QWEN_API_ENDPOINT', 'http://***********:20097/'),

    // Ollama API endpoint
    ollama: getEnvVar('VITE_OLLAMA_API_ENDPOINT', 'http://***********:20054/'),

    // Speech recognition endpoint
    speechRecog: getEnvVar('VITE_ASR_ENDPOINT', 'http://***********:20100/'),

    // Spark TTS endpoint
    sparkTTS: getEnvVar('VITE_TTS_ENDPOINT', 'http://***********:20099/'),

    // AnyTo3D endpoint
    anyTo3D: getEnvVar('VITE_GRADIO_Anyto3D_ENDPOINT', 'http://*************:20210/'),

    // Tripo-Doll endpoint
    tripoDoll: getEnvVar('VITE_TRIPO_DOLL_ENDPOINT', 'http://***********:9123/'),

    // TextTo3D endpoint
    textTo3D: getEnvVar('VITE_GRADIO_TEXTTO3D_ENDPOINT', 'http://************:7860/'),

    // Mem0 API endpoint - use proxy in browser mode
    mem0: isBrowser
        ? `${getProxyBaseUrl()}/memo-proxy`
        : getEnvVar('VITE_MEM0_API_ENDPOINT', 'https://api.mem0.ai'),
};

// Export default LLM provider
export const llmProvider = getEnvVar('VITE_LLM_PROVIDER', 'letta-vllm');

// Export default Ollama model
export const ollamaDefaultModel = getEnvVar('VITE_OLLAMA_DEFAULT_MODEL', 'qwen2.5:72b');

// Log loaded endpoints for debugging
console.log('[Config] Loaded API endpoints:', {
    letta: endpoints.letta,
    vllm: endpoints.vllm,
    sglang: endpoints.sglang,
    ollama: endpoints.ollama,
    speechRecog: endpoints.speechRecog,
    sparkTTS: endpoints.sparkTTS,
    anyTo3D: endpoints.anyTo3D,
    tripoDoll: endpoints.tripoDoll,
    textTo3D: endpoints.textTo3D,
    mem0: endpoints.mem0,
    llmProvider
});
