/**
 * Centralized environment variable handling
 * This file provides a unified way to access environment variables
 * across both server and client environments
 */

import { isNodeEnvironment, isBrowserEnvironment } from '@/media/utils/environment';

// Environment detection
const isServer = isNodeEnvironment();
const isBrowser = isBrowserEnvironment();



/**
 * Get an environment variable from the appropriate source based on environment
 * @param key The environment variable key (with or without VITE_ prefix)
 * @param defaultValue The default value to use if the environment variable is not defined or empty
 * @param logWarning Whether to log a warning if the environment variable is not defined
 * @returns The environment variable value or the default value
 */
export const getEnvVar = (key: string, defaultValue: string, logWarning = true): string => {
    // Add VITE_ prefix if not already present
    const fullKey = key.startsWith('VITE_') ? key : `VITE_${key}`;

    let envValue: string | undefined;

    if (isServer) {
        // Server-side: use process.env
        envValue = process.env[fullKey];
        if (!envValue && logWarning) {
            console.warn(`[Config] Environment variable ${fullKey} not found in process.env`);
        }
    } else if (isBrowser) {
        // Client-side: use import.meta.env
        try {
            // Try to access the environment variable directly
            // This should work because Vite replaces import.meta.env.* with the actual values at build time
            envValue = (import.meta.env as Record<string, any>)[fullKey];

            // If the value is undefined but we can see it in the list of keys, try again
            if (envValue === undefined && Object.keys(import.meta.env).includes(fullKey)) {
                // Try accessing it with bracket notation
                envValue = (import.meta.env as any)[fullKey];

                // If still undefined, try with direct property access
                if (envValue === undefined) {
                    // @ts-ignore - This is a workaround for Vite's environment variable handling
                    envValue = import.meta.env[fullKey];
                }
            }
        } catch (error) {
            console.error(`[Config] Error accessing import.meta.env[${fullKey}]:`, error);
        }
    } else {
        // Unknown environment
        console.warn(`[Config] Unknown environment, cannot access ${fullKey}`);
    }

    // Check if the environment variable is defined and not empty
    const isValidValue = envValue !== undefined && envValue !== null && envValue !== '';

    // Log if the environment variable is not defined or empty
    if (!isValidValue && logWarning) {
        console.warn(`[Config] Using default value for ${fullKey}: ${defaultValue}`);
    }

    // TypeScript needs this cast to ensure we're returning a string
    return isValidValue ? (envValue as string) : defaultValue;
};

/**
 * Get an environment variable as a number
 * @param key The environment variable key
 * @param defaultValue The default value to use if the environment variable is not defined or empty
 * @param logWarning Whether to log a warning if the environment variable is not defined
 * @returns The environment variable value as a number or the default value
 */
export const getEnvVarAsNumber = (key: string, defaultValue: number, logWarning = true): number => {
    const strValue = getEnvVar(key, defaultValue.toString(), logWarning);
    const numValue = Number(strValue);

    if (isNaN(numValue)) {
        console.warn(`[Config] Environment variable ${key} is not a valid number: ${strValue}. Using default: ${defaultValue}`);
        return defaultValue;
    }

    return numValue;
};

/**
 * Get an environment variable as a boolean
 * @param key The environment variable key
 * @param defaultValue The default value to use if the environment variable is not defined or empty
 * @param logWarning Whether to log a warning if the environment variable is not defined
 * @returns The environment variable value as a boolean or the default value
 */
export const getEnvVarAsBoolean = (key: string, defaultValue: boolean, logWarning = true): boolean => {
    const strValue = getEnvVar(key, defaultValue.toString(), logWarning);

    // Check for common boolean string values
    if (strValue.toLowerCase() === 'true' || strValue === '1') {
        return true;
    }

    if (strValue.toLowerCase() === 'false' || strValue === '0') {
        return false;
    }

    console.warn(`[Config] Environment variable ${key} is not a valid boolean: ${strValue}. Using default: ${defaultValue}`);
    return defaultValue;
};

/**
 * Log all environment variables for debugging
 * Redacts sensitive values (keys containing 'KEY', 'API', or 'SECRET')
 */
export const logEnvironmentVariables = (): void => {
    try {
        if (isBrowser) {
            // Get all environment variables that start with VITE_
            const envVars = Object.keys(import.meta.env)
                .filter(key => key.startsWith('VITE_'))
                .map(key => {
                    const value = (import.meta.env as any)[key];
                    // Redact sensitive values
                    const displayValue = key.includes('KEY') || key.includes('API') || key.includes('SECRET')
                        ? '[REDACTED]'
                        : value;
                    // Check if the value is empty
                    const isEmpty = value === undefined || value === null || value === '';
                    // Return the key-value pair with a warning if empty
                    return `${key}: ${displayValue}${isEmpty ? ' [EMPTY]' : ''}`;
                });

            if (envVars.length > 0) {
                console.log('[Config] Environment variables:', envVars);

                // Log a summary of how many variables were found
                console.log(`[Config] Found ${envVars.length} environment variables with VITE_ prefix`);

                // Check for empty values
                const emptyVars = envVars.filter(v => v.includes('[EMPTY]'));
                if (emptyVars.length > 0) {
                    console.warn(`[Config] Warning: ${emptyVars.length} environment variables are empty`);
                }
            } else {
                console.warn('[Config] No VITE_ environment variables found in import.meta.env');
                console.warn('[Config] This may indicate an issue with environment variable loading');
            }
        } else if (isServer) {
            // Get all environment variables that start with VITE_
            const envVars = Object.keys(process.env)
                .filter(key => key.startsWith('VITE_'))
                .map(key => {
                    const value = process.env[key];
                    // Redact sensitive values
                    const displayValue = key.includes('KEY') || key.includes('API') || key.includes('SECRET')
                        ? '[REDACTED]'
                        : value;
                    // Check if the value is empty
                    const isEmpty = value === undefined || value === null || value === '';
                    // Return the key-value pair with a warning if empty
                    return `${key}: ${displayValue}${isEmpty ? ' [EMPTY]' : ''}`;
                });

            if (envVars.length > 0) {
                console.log('[Config] Environment variables:', envVars);
                console.log(`[Config] Found ${envVars.length} environment variables with VITE_ prefix`);
            } else {
                console.warn('[Config] No VITE_ environment variables found in process.env');
            }
        }
    } catch (error) {
        console.error('[Config] Error logging environment variables:', error);
    }
};

/**
 * Get the full asset path by appending the base path
 * @param path The asset path to append to the base path
 * @returns The full asset path
 */
export const getAssetPath = (path: string): string => {
    const basePath = getEnvVar('VITE_ASSETS_PATH', '/assets');
    return `${basePath}${path}`;
};

/**
 * API Configuration - Dual Brain System Settings
 * These settings control API usage limits and testing modes for both WebSocket and HTTP models
 */

// API Rate Limiting Configuration
export const API_MAX_ATTEMPTS = getEnvVarAsNumber('API_MAX_ATTEMPTS', 5);         // Increased for dual brain system operation
export const API_TEST_MODE = getEnvVarAsBoolean('API_TEST_MODE', true);         // Enabled to allow full dual brain pipeline
export const API_RATE_LIMIT_ENABLED = getEnvVarAsBoolean('API_RATE_LIMIT_ENABLED', false); // Disabled for testing dual brain audio flow

/**
 * Get API configuration for models
 * Applies to both WebSocket and HTTP models when in test mode
 * @returns API configuration object
 */
export const getApiConfig = () => ({
    maxAttempts: API_MAX_ATTEMPTS,
    testMode: API_TEST_MODE,
    rateLimitEnabled: API_RATE_LIMIT_ENABLED,
    // Additional test mode restrictions
    ...(API_TEST_MODE && {
        // When in test mode, restrict API usage
        maxRequestsPerMinute: 10,
        maxConcurrentRequests: 2,
        enableRequestLogging: true
    })
});

// Export environment detection utilities for convenience
export { isServer, isBrowser };
