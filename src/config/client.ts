// Client-side config
// Import centralized environment utilities
import { getEnvVar, getEnvVarAsBoolean } from './env';
import { endpoints } from './endpoints';
import { createLogger } from '@/utils/logger';

// Dedicated logger for Config (client)
const logger = createLogger('Config');

// Environment logging is now handled by centralized env.ts

// Create a reactive config object
const config = {
    // Gradio endpoints
    gradioEndpoint: getEnvVar('VITE_GRADIO_Anyto3D_ENDPOINT', 'http://127.0.0.1:7860'),
    gradioASREndpoint: getEnvVar('VITE_ASR_ENDPOINT', 'http://127.0.0.1:7861'),

    // API endpoints - import from centralized endpoints config
    endpoints: {
        ...endpoints,
        downloadServer: `http://${getEnvVar('VITE_SERVER_HOST', 'localhost')}:${getEnvVar('VITE_DOWNLOAD_SERVER_PORT', '2994')}`
    },

    // LLM configuration
    llmConfig: {
        provider: getEnvVar('VITE_LLM_PROVIDER', 'vllm'),
        ollamaModel: getEnvVar('VITE_OLLAMA_DEFAULT_MODEL', 'qwen2.5:72b'),
        vllmModel: getEnvVar('VITE_LLM_MODEL', 'Qwen2.5-Omni-7B'),
        lettaPreferredModel: getEnvVar('VITE_LETTA_PREFERRED_MODEL', 'vllm/Qwen/Qwen2.5-Omni-7B'),
        lettaFallbackModel: getEnvVar('VITE_LETTA_FALLBACK_MODEL', 'letta/letta-free'),
        lettaEmbedding: getEnvVar('VITE_LETTA_EMBEDDING', 'letta/letta-free'),

        // Aliyun Bailian configuration
        aliyunApiKey: getEnvVar('VITE_DASHSCOPE_API_KEY', ''),
        aliyunModel: getEnvVar('VITE_ALIYUN_MODEL', 'qwen-omni-turbo-realtime'),
        aliyunUseProxy: getEnvVarAsBoolean('VITE_ALIYUN_USE_PROXY', true), // Browser environments should use proxy by default
        modelProvider: getEnvVar('VITE_MODEL_PROVIDER', 'vllm') // 'vllm' or 'aliyun'
    },

    // Download server configuration
    downloadConfig: {
        _port: parseInt(getEnvVar('VITE_DOWNLOAD_SERVER_PORT', '2994')),
        get port() {
            return this._port;
        },
        set port(value: number) {
            this._port = value;
        },
        assetsDir: getEnvVar('VITE_DOWNLOAD_ASSETS_DIR', 'public/assets'),
        supportedMeshExtensions: getEnvVar('VITE_SUPPORTED_MESH_EXTENSIONS', '.glb,.fbx,.obj,.gltf').split(',')
    },

    // Server configuration
    host: getEnvVar('VITE_SERVER_HOST', 'localhost'),
    port: parseInt(getEnvVar('VITE_SERVER_PORT', '3006')),

    // Model configuration
    modelConfig: {
        baseUrl: getEnvVar('VITE_ASSETS_PATH', '/models'), // Changed to '/models' for consistency
        cacheDuration: parseInt(getEnvVar('VITE_MODEL_CACHE_DURATION', '86400000')), // 24 hours
        mediapipe: {
            holistic: {
                modelPath: 'models/mediapipe/holistic_landmarker.task',
                fallbackPath: 'https://storage.googleapis.com/mediapipe-models/holistic_landmarker/holistic_landmarker/float16/1/holistic_landmarker.task'
            },
            pose: {
                modelPath: 'models/mediapipe/pose_landmarker_lite.task',
                fallbackPath: 'https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_lite/float16/1/pose_landmarker_lite.task'
            },
            hand: {
                modelPath: 'models/mediapipe/hand_landmarker.task',
                fallbackPath: 'https://storage.googleapis.com/mediapipe-models/hand_landmarker/hand_landmarker/float16/1/hand_landmarker.task'
            }
        }
    },

    // Debug configuration
    debug: getEnvVar('VITE_DEBUG_MODE', 'false') === 'true',
    appEnv: getEnvVar('VITE_APP_ENV', 'development'),

    // Test mode configuration
    testMode: {
        enabled: getEnvVarAsBoolean('VITE_TEST_MODE', false) ||
            getEnvVarAsBoolean('TEST_REAL_API', false) ||
            (typeof global !== 'undefined' && (global as any).__TEST__) ||
            process.env.NODE_ENV === 'test',
        restrictApiUsage: getEnvVarAsBoolean('VITE_RESTRICT_API_IN_TEST', true),
        mockEndpoints: getEnvVarAsBoolean('VITE_MOCK_ENDPOINTS_IN_TEST', true),
        maxTestApiCalls: parseInt(getEnvVar('VITE_MAX_TEST_API_CALLS', '50')),
        testApiCallCount: 0
    }
};

export { config };
export type Config = typeof config;