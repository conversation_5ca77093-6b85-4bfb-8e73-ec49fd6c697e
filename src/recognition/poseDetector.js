import { Vector3 } from '@babylonjs/core';
import { config } from '../config/client.js';

// Import the new MediaPipe module for unified MediaPipe handling
import MediaPipeModule, { DetectionMode } from '../modules/mediapipe/index.js';
import { MODEL_CONFIGS } from '../config/models.js';
import { createLogger, LogLevel } from '@/utils/logger';

// Import consolidated media infrastructure
import { MediaCaptureManager } from '../media/capture/MediaCaptureManager.ts';
import { DEFAULT_VIDEO_CONFIG } from '../media/modality/video.ts';

const logger = createLogger('PoseDetector', LogLevel.INFO);

class PoseDetector {
    constructor(options = {}) {
        this.detector = null;  // 替换 poseLandmarker
        this.debugCanvas = null;
        this.fpsText = null;
        this.lastFrameTime = Date.now();
        this.isLandmarkMode = false;
        this._isInitialized = false;  // 重命名为私有属性
        this.debug = false;

        // Separate detectors for pose and hands
        this.poseDetector = null;
        this.handDetector = null;

        // Set detection mode from options or default to pose-only
        if (options.detectorType === 'holistic') {
            this.detectionMode = DetectionMode.HOLISTIC;
        } else {
            // Parse detection mode from options
            if (options.detectionMode) {
                this.detectionMode = options.detectionMode;
            } else if (options.useHands === true && options.usePose === false) {
                this.detectionMode = DetectionMode.HANDS_ONLY;
            } else if (options.useHands === true && (options.usePose === true || options.usePose === undefined)) {
                this.detectionMode = DetectionMode.POSE_AND_HANDS;
            } else {
                // Default to pose-only detection if not specified
                this.detectionMode = DetectionMode.POSE_ONLY;
            }
        }

        // For backward compatibility
        this.useHands = this.detectionMode === DetectionMode.HANDS_ONLY ||
            this.detectionMode === DetectionMode.POSE_AND_HANDS;
        this.usePose = this.detectionMode === DetectionMode.POSE_ONLY ||
            this.detectionMode === DetectionMode.POSE_AND_HANDS;

        console.log(`[PoseDetector] Initialized with detection mode: ${this.detectionMode}`);

        // Use consolidated video configuration instead of hardcoded values
        this.videoConfig = {
            ...DEFAULT_VIDEO_CONFIG,
            ...options.videoConfig
        };

        // Initialize MediaCaptureManager for camera access
        this.mediaCaptureManager = new MediaCaptureManager({
            video: {
                width: { ideal: 640 },
                height: { ideal: 480 },
                frameRate: { ideal: 30 },
                facingMode: 'user'
            },
            audio: false, // PoseDetector doesn't need audio
            onCaptureStart: () => logger.debug('Video capture started'),
            onCaptureStop: () => logger.debug('Video capture stopped'),
            onCaptureError: (error) => logger.error('Video capture error:', error)
        });

        // Define unified model configurations from shared config
        this.modelConfigs = {
            HOLISTIC: {
                modelPath: MODEL_CONFIGS.mediapipe?.holistic?.modelPath || 'mediapipe/holistic_landmarker.task',
                fallbackPath: MODEL_CONFIGS.mediapipe?.holistic?.fallbackPath || 'https://storage.googleapis.com/mediapipe-models/holistic_landmarker/holistic_landmarker/float16/latest/holistic_landmarker.task',
                options: {
                    runningMode: "VIDEO",
                    minFaceDetectionConfidence: 0.5,
                    minFacePresenceConfidence: 0.5,
                    minFaceSuppressionThreshold: 0.3,
                    outputFaceBlendshapes: false,
                    minPoseDetectionConfidence: 0.5,
                    minPoseSuppressionThreshold: 0.3,
                    minPosePresenceConfidence: 0.5,
                    outputPoseSegmentationMasks: false,
                    minHandLandmarksConfidence: 0.5,
                }
            },
            POSE: {
                modelPath: MODEL_CONFIGS.mediapipe?.pose?.modelPath || 'mediapipe/pose_landmarker_lite.task',
                fallbackPath: MODEL_CONFIGS.mediapipe?.pose?.fallbackPath || 'https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_lite/float16/1/pose_landmarker_lite.task',
                options: {
                    runningMode: "VIDEO",
                    numPoses: 1,
                    minPoseDetectionConfidence: 0.5,
                    minPosePresenceConfidence: 0.5,
                    minTrackingConfidence: 0.5,
                    outputSegmentationMasks: false,
                }
            },
            HAND: {
                modelPath: MODEL_CONFIGS.mediapipe?.hand?.modelPath || 'mediapipe/hand_landmarker.task',
                fallbackPath: MODEL_CONFIGS.mediapipe?.hand?.fallbackPath || 'https://storage.googleapis.com/mediapipe-models/hand_landmarker/hand_landmarker/float16/1/hand_landmarker.task',
                options: {
                    runningMode: "VIDEO",
                    numHands: 2,
                    minHandDetectionConfidence: 0.5,
                    minHandPresenceConfidence: 0.8,
                    minTrackingConfidence: 0.5
                },
            }
        };

        // MediaPipe module handles all model loading and caching
        this.mediaPipeModule = new MediaPipeModule({
            detectionMode: this.detectionMode, // Use the detected mode
            uiManager: options.uiManager,
            debug: options.debug || false
        });

        // Add hand pose tracking
        this.handPoses = {
            left: null,
            right: null
        };

        this.landmarks = null;
        this.worldLandmarks = null;
        this.boneLengths = new Map();

        // Add resolution setting

        this.mediapipeLoaded = false;
        this.initPromise = null;

        // Keep only basic WebAssembly check
        this.hasWasmSupport = this.checkWasmSupport();

        // Legacy parameter kept for compatibility
        this.detectorType = options.detectorType ||
            (this.detectionMode === DetectionMode.HOLISTIC ? 'holistic' : 'separate');

        this.holisticDetector = null;
        this._initializationPromise = null;
        this.uiManager = options.uiManager;  // Store uiManager reference

        // Initialize gesture detector only if needed
        this.useGestureDetection = options.useGestureDetection !== false &&
            (this.detectionMode === DetectionMode.HANDS_ONLY ||
                this.detectionMode === DetectionMode.POSE_AND_HANDS ||
                this.detectionMode === DetectionMode.HOLISTIC);


        // Set up visualizer reference for debug mode
        this.visualizer = null;

        // Add gesture callbacks collection
        this.gestureCallbacks = new Set();

        // Add debug visualization settings
        this.debugVisualization = {
            enabled: false,
            canvas: null,
            context: null,
            drawHandLandmarks: true,
            drawConnections: true,
            colors: {
                landmarks: 'rgba(0, 255, 0, 0.8)',
                connections: 'rgba(255, 255, 255, 0.5)',
                leftHand: 'rgba(0, 255, 0, 0.8)',
                rightHand: 'rgba(255, 0, 0, 0.8)'
            }
        };
    }

    async initializeModelCache() {
        try {
            const request = this.modelStorage.open('ModelCache', 1);

            request.onerror = () => {
                console.error('[PoseDetector] Failed to open model cache database');
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                if (!db.objectStoreNames.contains('models')) {
                    db.createObjectStore('models', { keyPath: 'name' });
                }
            };

            request.onsuccess = () => {
                console.log('[PoseDetector] Model cache initialized');
            };
        } catch (error) {
            console.error('[PoseDetector] Model cache initialization failed:', error);
        }
    }

    async saveModelToPublic(modelName, modelBuffer) {
        try {
            if (this.app?.uiManager) {
                this.app.uiManager.showLoadingPanel();
                this.app.uiManager.updateLoadingProgress('Preparing upload...');
            }

            // Clean up the model directory path
            const modelDir = dirname(modelName).replace(/^\/+|\/+$/g, '');
            console.log('[PoseDetector] Preparing upload:', {
                modelName,
                modelDir,
                bufferSize: modelBuffer.byteLength
            });

            // Create directory first
            try {
                const baseUrl = config.modelConfig.baseUrl;
                const createDirResponse = await fetch(`${baseUrl}/${modelDir}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ createDir: true })
                });

                if (!createDirResponse.ok) {
                    throw new Error(`Failed to create directory: ${await createDirResponse.text()}`);
                }

                this.app?.uiManager?.updateLoadingProgress('Directory created...');
            } catch (error) {
                console.error('[PoseDetector] Directory creation failed:', error);
                this.app?.uiManager?.hideLoadingPanel();
                return null;
            }

            // Prepare and send upload
            const formData = new FormData();
            const blob = new Blob([modelBuffer], { type: 'application/octet-stream' });
            const filename = basename(modelName);

            // Important: append path first, before the file
            formData.append('path', modelDir);
            formData.append('model', blob, filename);

            const baseUrl = config.modelConfig.baseUrl;
            const uploadUrl = `${baseUrl}/upload`;
            this.app?.uiManager?.updateLoadingProgress('Uploading model...');

            console.log('[PoseDetector] Starting upload:', {
                url: uploadUrl,
                filename,
                modelDir,
                blobSize: blob.size,
                formDataEntries: Array.from(formData.entries()).map(([key]) => key)
            });

            try {
                const response = await fetch(uploadUrl, {
                    method: 'POST',
                    body: formData
                });

                console.log('[PoseDetector] Upload response status:', response.status);
                const responseText = await response.text();
                console.log('[PoseDetector] Upload response:', responseText);

                if (!response.ok) {
                    throw new Error(`Upload failed: ${response.status} ${response.statusText}\n${responseText}`);
                }

                const result = JSON.parse(responseText);
                console.log('[PoseDetector] Upload successful:', result);
                this.app?.uiManager?.hideLoadingPanel();
                return result;

            } catch (error) {
                console.error('[PoseDetector] Upload error:', error);
                this.app?.uiManager?.hideLoadingPanel();
                throw error;
            }
        } catch (error) {
            console.error('[PoseDetector] Save model error:', error);
            this.app?.uiManager?.hideLoadingPanel();
            return null;
        }
    }

    convertToHolisticFormat(results) {
        return {
            poseLandmarks: results.landmarks,
            poseWorldLandmarks: results.worldLandmarks,
            leftHandLandmarks: results.handPoses?.left?.worldLandmarks || null,
            rightHandLandmarks: results.handPoses?.right?.worldLandmarks || null,
            // Add any additional properties needed by MediapipePoseCalculator
        };
    }

    // 修改为 getter 方法
    isInitialized() {
        return this.mediaPipeModule.isReady();
    }

    async initialize(canvas = null) {
        logger.info('🚀 Initializing PoseDetector...');
        try {
            // Initialize MediaCaptureManager first for camera access
            const mediaInitialized = await this.mediaCaptureManager.initialize('video');
            if (mediaInitialized) {
                logger.info('✅ MediaCaptureManager initialized successfully');
            } else {
                logger.warn('⚠️ MediaCaptureManager initialization failed, continuing without camera');
            }

            await this.mediaPipeModule.initialize(canvas);
            this._isInitialized = true;
            logger.info('✅ PoseDetector initialization complete');
            return true;
        } catch (error) {
            logger.error('❌ PoseDetector initialization failed:', error);
            this._isInitialized = false;
            throw error;
        }
    }

    /**
     * Start camera stream using MediaCaptureManager
     * @returns {Promise<MediaStream|null>} The media stream or null if failed
     */
    async startCamera() {
        try {
            const started = await this.mediaCaptureManager.startCapture('video');
            if (started) {
                const mediaStream = this.mediaCaptureManager.getMediaStream();
                logger.info('📹 Camera started successfully');
                return mediaStream;
            }
            return null;
        } catch (error) {
            logger.error('❌ Failed to start camera:', error);
            return null;
        }
    }

    /**
     * Stop camera stream
     */
    stopCamera() {
        try {
            this.mediaCaptureManager.stopCapture();
            logger.info('📹 Camera stopped');
        } catch (error) {
            logger.error('❌ Failed to stop camera:', error);
        }
    }

    /**
     * Get video element from MediaCaptureManager
     * @returns {HTMLVideoElement|null}
     */
    getVideoElement() {
        return this.mediaCaptureManager.getVideoElement();
    }

    /**
     * Get media stream from MediaCaptureManager
     * @returns {MediaStream|null}
     */
    getMediaStream() {
        return this.mediaCaptureManager.getMediaStream();
    }

    async initializeMediaPipe() {
        // Delegate to MediaPipe module
        return await this.mediaPipeModule.initializeWasm();
    }

    async _getVideoDimensionsWithTimeout(videoElement, timeout = 5000) {
        if (!videoElement) {
            console.warn('[PoseDetector] No video element provided, using defaults');
            return {
                width: this.videoConfig.width || 640,
                height: this.videoConfig.height || 480
            };
        }

        // If video already has dimensions, use them
        if (videoElement.videoWidth && videoElement.videoHeight) {
            return {
                width: Math.max(videoElement.videoWidth, 640),
                height: Math.max(videoElement.videoHeight, 480)
            };
        }

        // Wait for video metadata with timeout
        try {
            const dimensions = await Promise.race([
                new Promise((resolve) => {
                    const handleMetadata = () => {
                        resolve({
                            width: Math.max(videoElement.videoWidth, 640),
                            height: Math.max(videoElement.videoHeight, 480)
                        });
                    };
                    videoElement.addEventListener('loadedmetadata', handleMetadata, { once: true });
                }),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Video metadata load timeout')), timeout)
                )
            ]);
            console.log('[PoseDetector] Got video dimensions:', dimensions);
            return dimensions;
        } catch (error) {
            console.warn('[PoseDetector] Failed to get video dimensions:', error);
            // Return default dimensions if timeout
            return {
                width: 640,
                height: 480
            };
        }
    }

    /**
     * Check WebAssembly support
     * @returns {boolean} Whether WASM is supported
     */
    checkWasmSupport() {
        try {
            if (typeof WebAssembly === 'object' && typeof WebAssembly.instantiate === 'function') {
                const module = new WebAssembly.Module(Uint8Array.of(0x0, 0x61, 0x73, 0x6d, 0x01, 0x00, 0x00, 0x00));
                if (module instanceof WebAssembly.Module) {
                    const instance = new WebAssembly.Instance(module);
                    return instance instanceof WebAssembly.Instance;
                }
            }
        } catch (e) {
            logger.debug('WASM support check failed:', e);
            return false;
        }
        return false;
    }

    /**
     * Check GPU support for MediaPipe
     * @returns {Promise<boolean>} Whether GPU acceleration is available
     */
    async checkGPUSupport() {
        try {
            // Check for WebGL support (GPU acceleration)
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
            if (gl) {
                logger.info('🚀 GPU acceleration available');
                return true;
            }
        } catch (error) {
            logger.debug('GPU check failed:', error);
        }

        logger.info('🔧 Using CPU processing');
        return false;
    }

    async reset() {
        logger.info('🔄 Resetting PoseDetector...');
        try {
            await this.mediaPipeModule.reset();
            this._isInitialized = false;
            this.landmarks = null;
            this.worldLandmarks = null;
            this.handPoses = { left: null, right: null };
            logger.info('✅ PoseDetector reset complete');
        } catch (error) {
            logger.error('❌ Reset failed:', error);
        }
    }

    setupDebugVisualization() {
        // ... debug canvas setup ...
    }

    // Remove drawDebugView method as visualization should be handled by DebugVisualizer

    async start(videoElement) {
        try {
            if (!this.detector) {
                await this.initialize(videoElement);
            }
        } catch (error) {
            console.error("Failed to start pose detection:", error);
        }
    }

    async detectPose(video, timestamp) {
        // Add input validation
        if (!video || video.readyState !== 4 || video.videoWidth === 0 || video.videoHeight === 0) {
            logger.debug('Video not ready:', {
                readyState: video?.readyState,
                width: video?.videoWidth,
                height: video?.videoHeight
            });
            return null;
        }

        if (!this.isInitialized()) {
            logger.warn('Not initialized, initializing now...');
            try {
                await this.initialize();
            } catch (error) {
                logger.error('Failed to initialize:', error);
                throw new Error('PoseDetector not initialized');
            }
        }

        try {
            const currentTimestamp = timestamp || performance.now();

            // Use the consolidated MediaPipe module for detection
            const results = await this.mediaPipeModule.detect(video, currentTimestamp);

            if (results) {
                // Update internal state for backward compatibility
                this.landmarks = results.landmarks;
                this.worldLandmarks = results.worldLandmarks;
                this.handPoses = results.handPoses || {};

                return results;
            }

            return null;
        } catch (error) {
            logger.error('Detection failed:', error);
            return null;
        }
    }

    // Add new method to combine pose and hand results
    combinePoseAndHandResults(poseResults, handResults, timestamp) {
        // If useHands is false or no handResults, provide empty hand poses
        const handPoses = handResults ? this.processHandResults(handResults) : {};

        return {
            landmarks: poseResults.landmarks,
            worldLandmarks: poseResults.worldLandmarks,
            handPoses: handPoses,
            timestamp: timestamp
        };
    }

    processHandResults(handResults) {
        if (!handResults?.handedness || !handResults?.landmarks) return {};

        const poses = {};
        handResults.handedness.forEach((hand, index) => {
            const side = hand[0].categoryName.toLowerCase();
            const landmarks = handResults.landmarks[index].map(lm => ({
                x: lm.x,
                y: lm.y,
                z: lm.z || 0,
                visibility: lm.visibility || 1
            }));

            poses[side] = {
                landmarks: landmarks,
                worldLandmarks: handResults.worldLandmarks?.[index],
            };
        });
        return poses;
    }

    convertHolisticResults(results) {
        if (!results) return {
            landmarks: null,
            worldLandmarks: null,
            handPoses: {
                left: null,
                right: null
            }
        };

        return {
            landmarks: results.poseLandmarks || null,
            worldLandmarks: results.poseWorldLandmarks || null,
            handPoses: {
                left: results.leftHandLandmarks ? {
                    landmarks: results.leftHandLandmarks,
                    worldLandmarks: results.leftHandWorldLandmarks
                } : null,
                right: results.rightHandLandmarks ? {
                    landmarks: results.rightHandLandmarks,
                    worldLandmarks: results.rightHandWorldLandmarks
                } : null
            }
        };
    }

    analyzeHandPose(landmarks) {
        if (!landmarks || landmarks.length === 0) return null;

        // Calculate finger curls
        const fingerCurls = this.calculateFingerCurls(landmarks);

        // Determine hand pose based on finger curls
        const pose = this.determineHandPose(fingerCurls);

        return {
            pose,
            fingerCurls,
            confidence: landmarks[0].visibility || 0
        };
    }

    calculateFingerCurls(landmarks) {
        // MediaPipe hand landmarks indices
        const fingerBases = [1, 5, 9, 13, 17]; // thumb, index, middle, ring, pinky
        const fingerTips = [4, 8, 12, 16, 20];

        return fingerBases.map((base, i) => {
            const tip = fingerTips[i];
            const mid = base + 2;

            // Calculate angles between finger segments
            const curl = this.calculateFingerCurl(
                landmarks[base],
                landmarks[mid],
                landmarks[tip]
            );
            return curl;
        });
    }

    calculateFingerCurl(base, mid, tip) {
        // Convert landmarks to vectors using BabylonJS Vector3
        const v1 = new Vector3(
            mid.x - base.x,
            mid.y - base.y,
            (mid.z || 0) - (base.z || 0)
        );
        const v2 = new Vector3(
            tip.x - mid.x,
            tip.y - mid.y,
            (tip.z || 0) - (mid.z || 0)
        );

        // Calculate angle between vectors using BabylonJS methods
        const dotProduct = Vector3.Dot(v1, v2);
        const length1 = v1.length();
        const length2 = v2.length();

        if (length1 === 0 || length2 === 0) return 0;
        const angle = Math.acos(dotProduct / (length1 * length2));
        return angle;
    }

    determineHandPose(fingerCurls) {
        // Threshold for considering a finger curled
        const curlThreshold = 0.7;

        // Check if all fingers are curled (fist)
        const isFist = fingerCurls.every(curl => curl > curlThreshold);

        // Check if all fingers are extended
        const isOpen = fingerCurls.every(curl => curl < curlThreshold);

        if (isFist) return 'fist';
        if (isOpen) return 'open';
        return 'other';
    }

    convertTo3DSkeleton(landmarks, worldLandmarks) {
        if (!landmarks || !worldLandmarks) return null;

        // Initialize bone lengths on first detection
        if (this.boneLengths.size === 0) {
            this.calculateBoneLengths(worldLandmarks);
        }

        const skeleton = {
            joints: {},
            bones: {}
        };

        // Convert landmarks to joint positions
        this.keypointPairs.forEach(([joint, index]) => {
            if (worldLandmarks[index]) {
                const position = new Vector3(
                    worldLandmarks[index].x,
                    worldLandmarks[index].y,
                    worldLandmarks[index].z
                );
                skeleton.joints[joint] = {
                    position,
                    confidence: landmarks[index].visibility || 0
                };
            }
        });

        // Calculate bone rotations using inverse kinematics
        this.calculateBoneRotations(skeleton);

        return skeleton;
    }

    calculateBoneLengths(worldLandmarks) {
        this.bonePairs.forEach(([start, end, boneName]) => {
            const startPoint = worldLandmarks[start];
            const endPoint = worldLandmarks[end];
            if (startPoint && endPoint) {
                const length = new Vector3(
                    endPoint.x - startPoint.x,
                    endPoint.y - startPoint.y,
                    endPoint.z - startPoint.z
                ).length();
                this.boneLengths.set(boneName, length);
            }
        });
    }


    enableDebug(enabled = true) {
        this.debug = enabled;
        if (this.debugVisualizer) {
            this.debugVisualizer.setEnabled(enabled);
        }
        logger.info(`🐛 Debug mode ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Set visualizer for debug drawing
     * Expected by the Viewer and gesture controls
     */
    setVisualizer(visualizer) {
        this.debugVisualizer = visualizer;
        logger.debug('🎨 Debug visualizer set');
    }

    /**
     * Set debug mode
     * Expected by gesture controls
     */
    setDebugMode(enabled) {
        this.enableDebug(enabled);
    }

    /**
     * Get current debug visualizer
     */
    getVisualizer() {
        return this.debugVisualizer;
    }

    /**
     * Check if debug mode is enabled
     */
    isDebugEnabled() {
        return this.debug;
    }

    /**
     * Save model to cache using MediaPipe module's caching system
     * Expected by some legacy code
     */
    async saveToCache(path, buffer) {
        if (this.mediaPipeModule && this.mediaPipeModule.cacheManager) {
            return await this.mediaPipeModule.cacheManager.saveFile(path, buffer);
        }
        // Fallback to ModelDownloader if MediaPipe module not available
        return await this.modelDownloader?.saveModelToPublic(path, buffer);
    }

    /**
     * Dispose and clean up all resources including MediaCaptureManager
     */
    dispose() {
        try {
            logger.info('🧹 Disposing PoseDetector resources...');

            // Dispose MediaCaptureManager
            if (this.mediaCaptureManager) {
                this.mediaCaptureManager.dispose();
                this.mediaCaptureManager = null;
            }

            // Reset MediaPipe module
            if (this.mediaPipeModule) {
                this.mediaPipeModule.reset();
            }

            // Clear internal state
            this._isInitialized = false;
            this.landmarks = null;
            this.worldLandmarks = null;
            this.handPoses = { left: null, right: null };
            this.boneLengths.clear();

            logger.info('✅ PoseDetector disposed successfully');
        } catch (error) {
            logger.error('❌ Error during PoseDetector disposal:', error);
        }
    }
}

// Export the DetectionMode enum so it can be used by consumers
export { DetectionMode };
export default PoseDetector;