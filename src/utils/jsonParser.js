/**
 * Robust JSON Parser Utility
 * 
 * Provides reliable JSON parsing for LLM outputs, tool calls, and other potentially malformed JSON.
 * Uses jsonrepair library to fix common JSON issues before parsing.
 * Includes streaming support for large JSON documents.
 * 
 * Based on the jsonrepair library: https://github.com/josdejong/jsonrepair
 * 
 * Features:
 * - Multiple extraction strategies (direct, codeblock, thinking, etc.)
 * - Automatic JSON repair using jsonrepair
 * - Streaming support for large documents
 * - Schema validation
 * - Graceful fallback handling
 */

import { jsonrepair } from 'jsonrepair';
import { jsonrepairTransform } from 'jsonrepair/stream';
import { createLogger } from './logger.ts';

const logger = createLogger('JSONParser');

/**
 * Parse JSON with robust error handling and repair capabilities
 * @param {string|object} input - Input to parse (string or object)
 * @param {Object} options - Parsing options
 * @returns {Object} Parsed result with success flag and data/error
 */
export function parseRobustJSON(input, options = {}) {
  const {
    allowFallback = true,
    extractionStrategies = ['direct', 'codeblock', 'thinking', 'lastjson'],
    logLevel = 'warn',
    repairOptions = {},
    validateSchema = null
  } = options;

  try {
    // Handle non-string inputs
    if (typeof input === 'object') {
      return { success: true, data: input, method: 'passthrough' };
    }

    if (typeof input !== 'string' || !input.trim()) {
      throw new Error('Input must be a non-empty string');
    }

    const content = input.trim();

    // Try multiple extraction strategies
    for (const strategy of extractionStrategies) {
      const extracted = extractJSONByStrategy(content, strategy);
      if (extracted.success) {
        try {
          // Try direct parsing first
          let parsed;
          try {
            parsed = JSON.parse(extracted.json);
          } catch (directError) {
            // Use jsonrepair if direct parsing fails
            logger.debug('Direct JSON parse failed, attempting repair...', {
              strategy,
              error: directError.message,
              jsonPreview: extracted.json.substring(0, 100)
            });

            const repairedJSON = jsonrepair(extracted.json);
            parsed = JSON.parse(repairedJSON);

            logger.debug('JSON successfully repaired and parsed', {
              strategy,
              originalLength: extracted.json.length,
              repairedLength: repairedJSON.length
            });
          }

          // Validate schema if provided
          if (validateSchema && !validateSchema(parsed)) {
            logger.warn('Parsed JSON failed schema validation', { strategy });
            continue;
          }

          if (logLevel === 'debug') {
            logger.debug('✅ JSON parsed successfully', {
              strategy,
              hasRepair: extracted.json !== input,
              fields: Object.keys(parsed)
            });
          }

          return {
            success: true,
            data: parsed,
            method: strategy,
            wasRepaired: extracted.json !== input,
            originalInput: content.substring(0, 200) + (content.length > 200 ? '...' : '')
          };

        } catch (parseError) {
          logger.debug(`Strategy ${strategy} failed:`, parseError.message);
          continue;
        }
      }
    }

    // If all strategies failed and fallback is allowed
    if (allowFallback) {
      return createFallbackResult(content, 'all_strategies_failed');
    }

    throw new Error('No valid JSON found using any extraction strategy');

  } catch (error) {
    logger[logLevel]('JSON parsing failed:', {
      error: error.message,
      inputLength: typeof input === 'string' ? input.length : 'not_string',
      inputPreview: typeof input === 'string' ? input.substring(0, 100) : 'not_string'
    });

    if (allowFallback) {
      return createFallbackResult(input, error.message);
    }

    return {
      success: false,
      error: error.message,
      method: 'failed',
      originalInput: typeof input === 'string' ? input.substring(0, 200) : 'not_string'
    };
  }
}

/**
 * Extract JSON using different strategies
 * @param {string} content - Content to extract from
 * @param {string} strategy - Extraction strategy
 * @returns {Object} Extraction result
 */
function extractJSONByStrategy(content, strategy) {
  switch (strategy) {
    case 'direct':
      // Direct JSON extraction (first complete JSON object)
      const directMatch = content.match(/\{[\s\S]*\}/);
      if (directMatch) {
        return { success: true, json: directMatch[0] };
      }
      break;

    case 'codeblock':
      // JSON within code blocks (```json or ```)
      const codeBlockMatch = content.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
      if (codeBlockMatch) {
        return { success: true, json: codeBlockMatch[1] };
      }
      break;

    case 'thinking':
      // JSON after thinking tags
      const thinkingMatch = content.match(/<\/thinking>\s*(\{[\s\S]*?\})/);
      if (thinkingMatch) {
        return { success: true, json: thinkingMatch[1] };
      }
      break;

    case 'lastjson':
      // Last JSON object in content
      const allMatches = [...content.matchAll(/\{[\s\S]*?\}/g)];
      if (allMatches.length > 0) {
        return { success: true, json: allMatches[allMatches.length - 1][0] };
      }
      break;

    case 'multiline':
      // Handle multiline JSON with better regex
      const multilineMatch = content.match(/\{(?:[^{}]|{[^{}]*})*\}/);
      if (multilineMatch) {
        return { success: true, json: multilineMatch[0] };
      }
      break;

    case 'lenient':
      // Try to extract anything that looks like JSON, even incomplete
      const lenientMatch = content.match(/\{[^}]*\}/);
      if (lenientMatch) {
        try {
          // Use jsonrepair to try to complete incomplete JSON
          const repaired = jsonrepair(lenientMatch[0]);
          return { success: true, json: repaired };
        } catch {
          // If repair fails, return original
          return { success: true, json: lenientMatch[0] };
        }
      }
      break;
  }

  return { success: false };
}

/**
 * Create a fallback result when parsing fails
 * @param {string} input - Original input
 * @param {string} reason - Reason for fallback
 * @returns {Object} Fallback result
 */
function createFallbackResult(input, reason) {
  return {
    success: false,
    error: `JSON parsing failed: ${reason}`,
    data: {
      success: false,
      error: `JSON parsing failed: ${reason}`,
      fallback: true,
      originalContent: typeof input === 'string' ? input.substring(0, 500) : 'not_string'
    },
    method: 'fallback',
    wasRepaired: false,
    originalInput: typeof input === 'string' ? input.substring(0, 200) : 'not_string'
  };
}

/**
 * Parse LLM decision output specifically
 * @param {string|object} response - LLM response
 * @param {Object} options - Parsing options
 * @returns {Object} Parsed decision
 */
export function parseLLMDecision(response, options = {}) {
  const defaultSchema = (obj) => {
    // Validate that it has basic decision structure
    return obj && (
      typeof obj.shouldAct === 'boolean' ||
      typeof obj.confidence === 'number' ||
      typeof obj.reason === 'string'
    );
  };

  const parseOptions = {
    validateSchema: defaultSchema,
    extractionStrategies: ['thinking', 'codeblock', 'direct', 'lastjson'],
    logLevel: 'debug',
    ...options
  };

  const result = parseRobustJSON(response, parseOptions);

  if (result.success && !result.data.fallback) {
    // Enhance the decision with defaults
    const decision = result.data;
    return {
      ...result,
      data: {
        shouldAct: !!decision.shouldAct,
        shouldSpeak: !!decision.shouldSpeak,
        confidence: Math.min(1, Math.max(0, decision.confidence || 0)),
        reason: decision.reason || 'analysis_completed',
        urgency: decision.urgency || 'low',
        suggestedAction: decision.suggestedAction || null,
        thinkingProcess: decision.thinkingProcess || 'not_available',
        toolsRequired: Array.isArray(decision.toolsRequired) ? decision.toolsRequired : [],
        // Metadata
        timestamp: Date.now(),
        enhanced: true,
        parsingMethod: result.method,
        wasRepaired: result.wasRepaired
      }
    };
  }

  // Return fallback decision
  return {
    success: true,
    data: {
      shouldAct: false,
      shouldSpeak: false,
      confidence: 0,
      reason: 'parsing_failed_using_fallback',
      urgency: 'low',
      suggestedAction: null,
      thinkingProcess: 'parsing_failed_no_thinking_content',
      toolsRequired: [],
      timestamp: Date.now(),
      enhanced: false,
      parsingMethod: 'fallback',
      wasRepaired: false,
      parseError: result.data?.error || 'Unknown parsing error'
    },
    method: 'fallback'
  };
}

/**
 * Parse tool call output
 * @param {string|object} response - Tool response
 * @param {Object} options - Parsing options
 * @returns {Object} Parsed tool result
 */
export function parseToolOutput(response, options = {}) {
  const parseOptions = {
    extractionStrategies: ['direct', 'codeblock', 'lastjson'],
    logLevel: 'warn',
    ...options
  };

  return parseRobustJSON(response, parseOptions);
}

/**
 * Repair and validate JSON string
 * @param {string} jsonString - JSON string to repair
 * @returns {Object} Repair result
 */
export function repairJSON(jsonString) {
  try {
    // First try direct parsing
    const direct = JSON.parse(jsonString);
    return { success: true, data: direct, wasRepaired: false, repaired: jsonString };
  } catch {
    try {
      // Use jsonrepair
      const repaired = jsonrepair(jsonString);
      const parsed = JSON.parse(repaired);
      return { success: true, data: parsed, wasRepaired: true, repaired };
    } catch (error) {
      return { success: false, error: error.message, original: jsonString };
    }
  }
}

/**
 * Create a streaming JSON repair transformer
 * Useful for processing large JSON documents from streaming sources
 * @param {Object} options - Streaming options
 * @returns {Transform} Transform stream for repairing JSON
 */
export function createJSONRepairStream(options = {}) {
  const {
    chunkSize = 65536,
    bufferSize = 65536
  } = options;

  logger.debug('Creating JSON repair stream transform', { chunkSize, bufferSize });

  return jsonrepairTransform({ chunkSize, bufferSize });
}

/**
 * Parse streaming JSON with repair capabilities
 * @param {ReadableStream} inputStream - Input stream containing JSON
 * @param {Object} options - Parsing options
 * @returns {Promise<Object>} Parsed result
 */
export async function parseStreamingJSON(inputStream, options = {}) {
  const {
    chunkSize = 65536,
    bufferSize = 65536,
    extractionStrategies = ['direct', 'codeblock', 'thinking', 'lastjson'],
    validateSchema = null,
    logLevel = 'warn'
  } = options;

  try {
    logger.debug('Starting streaming JSON parse', {
      chunkSize,
      bufferSize,
      strategies: extractionStrategies
    });

    // For browser environments, use ReadableStream
    if (typeof window !== 'undefined' && inputStream instanceof ReadableStream) {
      return await parseStreamingJSONBrowser(inputStream, options);
    }

    // For Node.js environments, use Node streams
    if (typeof require !== 'undefined') {
      return await parseStreamingJSONNode(inputStream, options);
    }

    throw new Error('Streaming JSON parsing not supported in this environment');

  } catch (error) {
    logger[logLevel]('Streaming JSON parse failed:', error.message);
    return {
      success: false,
      error: error.message,
      method: 'streaming_failed'
    };
  }
}

/**
 * Parse streaming JSON in browser environment
 * @private
 */
async function parseStreamingJSONBrowser(inputStream, options) {
  const chunks = [];
  const reader = inputStream.getReader();

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      chunks.push(new TextDecoder().decode(value));
    }

    const content = chunks.join('');
    logger.debug('Collected streaming content', {
      totalLength: content.length,
      chunks: chunks.length
    });

    return parseRobustJSON(content, options);

  } finally {
    reader.releaseLock();
  }
}

/**
 * Parse streaming JSON in Node.js environment
 * @private
 */
async function parseStreamingJSONNode(inputStream, options) {
  return new Promise((resolve, reject) => {
    const chunks = [];

    // Create repair transform
    const repairTransform = createJSONRepairStream({
      chunkSize: options.chunkSize,
      bufferSize: options.bufferSize
    });

    inputStream
      .pipe(repairTransform)
      .on('data', (chunk) => {
        chunks.push(chunk.toString());
      })
      .on('end', () => {
        try {
          const content = chunks.join('');
          logger.debug('Streaming repair completed', {
            totalLength: content.length,
            chunks: chunks.length
          });

          const parsed = parseRobustJSON(content, options);
          resolve(parsed);
        } catch (error) {
          reject(error);
        }
      })
      .on('error', (error) => {
        logger.error('Streaming repair error:', error.message);
        reject(error);
      });
  });
}

/**
 * Batch process multiple JSON strings with repair
 * @param {Array<string>} jsonStrings - Array of JSON strings to repair
 * @param {Object} options - Processing options
 * @returns {Array<Object>} Array of repair results
 */
export function batchRepairJSON(jsonStrings, options = {}) {
  const {
    continueOnError = true,
    logLevel = 'warn'
  } = options;

  logger.debug('Starting batch JSON repair', {
    count: jsonStrings.length,
    continueOnError
  });

  const results = [];
  let successCount = 0;
  let errorCount = 0;

  for (let i = 0; i < jsonStrings.length; i++) {
    try {
      const result = repairJSON(jsonStrings[i]);
      results.push({
        index: i,
        ...result
      });

      if (result.success) successCount++;
      else errorCount++;

    } catch (error) {
      errorCount++;
      const errorResult = {
        index: i,
        success: false,
        error: error.message,
        original: jsonStrings[i]
      };

      results.push(errorResult);

      if (!continueOnError) {
        logger[logLevel]('Batch repair stopped due to error:', error.message);
        break;
      }
    }
  }

  logger.debug('Batch JSON repair completed', {
    total: jsonStrings.length,
    processed: results.length,
    successful: successCount,
    errors: errorCount
  });

  return {
    success: errorCount === 0,
    results,
    summary: {
      total: jsonStrings.length,
      processed: results.length,
      successful: successCount,
      errors: errorCount,
      successRate: (successCount / results.length * 100).toFixed(1) + '%'
    }
  };
}

export default {
  parseRobustJSON,
  parseLLMDecision,
  parseToolOutput,
  repairJSON,
  createJSONRepairStream,
  parseStreamingJSON,
  batchRepairJSON
};