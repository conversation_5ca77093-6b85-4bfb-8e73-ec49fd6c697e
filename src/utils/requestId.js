/**
 * Request ID Generation Utility
 * 
 * Provides consistent request ID generation for tracing and logging
 */

let requestCounter = 0;

/**
 * Generate a unique request ID for tracing
 */
export function generateRequestId() {
  const timestamp = Date.now().toString(36);
  const counter = (++requestCounter).toString(36).padStart(4, '0');
  const random = Math.random().toString(36).substr(2, 8);

  return `req_${timestamp}_${counter}_${random}`;
}

/**
 * Extract timestamp from request ID (if possible)
 */
export function extractTimestampFromRequestId(requestId) {
  try {
    const parts = requestId.split('_');
    if (parts.length >= 2 && parts[0] === 'req') {
      return parseInt(parts[1], 36);
    }
  } catch {
    // Ignore parsing errors
  }
  return null;
}

/**
 * Check if a string looks like a valid request ID
 */
export function isValidRequestId(str) {
  return /^req_[a-z0-9]+_[a-z0-9]{4}_[a-z0-9]{8}$/.test(str);
}