import { config } from '../config/client.ts';
import { ASSETS } from '../../app/viewer/viewerConfig.js';
import { getDownloadServerPort, buildDownloadServerUrl } from './portManager.js';
import { blobToBase64 } from '../media/modality/audio.js';

// In-memory cache for quick access
const modelCache = new Map();

// Memory for storing seeds of generated items
const seedMemory = new Map();

/**
 * Join path segments in a browser-compatible way, ensuring proper base path
 * @param {...string} segments - Path segments to join
 * @returns {string} Joined path
 */
function joinPaths(...segments) {
    if (!segments || segments.length === 0) return '';

    // Check if first segment starts with a slash
    const hasLeadingSlash = segments[0]?.startsWith('/');

    // Filter and process segments
    const processedSegments = segments
        .filter(segment => segment != null)
        .map(segment => String(segment).replace(/^\/+|\/+$/g, ''))
        .filter(Boolean);

    // Add leading slash back if it was present in first segment
    return hasLeadingSlash ? '/' + processedSegments.join('/') : processedSegments.join('/');
}

/**
 * Store seed for a generated item
 * @param {string} cacheKey - The cache key of the item
 * @param {number|string|object} seed - The seed used for generation
 */
export async function storeSeed(cacheKey, seed) {
    try {
        // Store in memory
        seedMemory.set(cacheKey, seed);

        // Check if this is a key without hash suffix (old format)
        const parts = cacheKey.split('_');
        const hasHash = parts.length >= 2;

        if (!hasHash && /[\u4e00-\u9fff]/.test(cacheKey)) {
            console.log(`[Cache] Key without hash suffix detected in storeSeed: ${cacheKey}`);

            // Look for a matching key with hash suffix in memory
            let foundHashKey = null;
            for (const key of seedMemory.keys()) {
                if (key.startsWith(cacheKey + '_')) {
                    console.log(`[Cache] Found matching key with hash suffix in memory: ${key}`);
                    foundHashKey = key;
                    break;
                }
            }

            // If found a matching key with hash suffix, use that instead
            if (foundHashKey) {
                console.log(`[Cache] Using matching key with hash suffix: ${foundHashKey}`);
                cacheKey = foundHashKey;
            }
        }

        // Use a consistent naming convention for all seed files
        // Just use the cacheKey directly with .json extension
        const filename = `${cacheKey}.json`;

        // Store in file system - use the seedPath from SAVE_OPTIONS
        const seedPath = joinPaths(ASSETS.SAVE_OPTIONS.seedPath, filename);

        // Convert to string if it's not already
        const seedString = typeof seed === 'string' ? seed : JSON.stringify(seed);

        // Get the download server URL
        const port = getDownloadServerPort();
        const downloadServerUrl = `http://${config.host}:${port}`;

        // Extract the path without the leading /assets prefix for the upload
        // The server expects paths relative to the assets directory
        let relativePath = ASSETS.SAVE_OPTIONS.seedPath.replace(/^\/assets\//, '');

        // Make sure the path is in the format expected by the server
        // The server expects paths relative to the public directory
        // So for seeds, we want to use 'assets/seeds'
        if (ASSETS.SAVE_OPTIONS.seedPath.includes('seeds')) {
            // Extract just the 'seeds' part if it's in the path
            if (ASSETS.SAVE_OPTIONS.seedPath.includes('/seeds')) {
                relativePath = 'assets/seeds';
            } else {
                relativePath = 'assets/' + ASSETS.SAVE_OPTIONS.seedPath;
            }
        } else {
            // Fallback to assets/seeds if the path doesn't contain 'seeds'
            relativePath = 'assets/seeds';
        }
        console.log('[Cache] Using seed path for upload:', relativePath);

        // Create FormData for direct upload instead of using data URL
        const formData = new FormData();
        formData.append('path', relativePath);

        // Use the raw filename without URL encoding for the name parameter
        // This ensures the file is saved with the correct name including Chinese characters
        formData.append('name', filename);

        // Create a text blob from the seed string
        const seedBlob = new Blob([seedString], { type: 'text/plain' });
        formData.append('file', seedBlob, filename);

        // Upload directly using fetch
        console.log(`[Cache] Uploading seed file to ${downloadServerUrl}/upload with path=${relativePath}, name=${filename}`);

        try {
            console.log(`[Cache] Uploading seed file to ${downloadServerUrl}/upload with FormData:`, {
                path: relativePath,
                filename: filename,
                seedDataSize: seedString.length
            });

            const response = await fetch(`${downloadServerUrl}/upload`, {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`[Cache] Upload response error (${response.status}):`, errorText);
                throw new Error(`Failed to upload seed file: ${response.statusText}`);
            }

            const result = await response.json();
            console.log('[Cache] Successfully stored seed file:', {
                path: seedPath,
                result: result
            });
            return result;
        } catch (uploadError) {
            console.error('[Cache] Upload failed:', uploadError);

            // Log more details about the error
            if (uploadError.cause) {
                console.error('[Cache] Error cause:', uploadError.cause);
            }

            // Check if this is a network error
            if (uploadError.message.includes('Failed to fetch') ||
                uploadError.message.includes('NetworkError')) {
                console.error('[Cache] Network error detected. Check server connection.');
            }

            // Check if this is a CORS error
            if (uploadError.message.includes('CORS') ||
                uploadError.message.includes('cross-origin')) {
                console.error('[Cache] CORS error detected. Check server CORS configuration.');
            }

            // If the upload fails, we'll try an alternative method
            // This is a workaround for the case where the upload endpoint doesn't handle Chinese characters properly
            console.log('[Cache] Using memory-only storage for seed file with Chinese characters');

            // Return a success result even though we only stored in memory
            // This allows the application to continue working
            return {
                success: true,
                path: seedPath,
                note: 'Stored in memory only due to upload issues with Chinese characters'
            };
        }
    } catch (error) {
        console.error('[Cache] Failed to store seed file:', error);
        // Store in memory anyway so we have something
        return { success: false, error: error.message };
    }
}

/**
 * Get stored seed for an item
 * @param {string} cacheKey - The cache key of the item
 * @returns {Promise<number|null>} The stored seed or null if not found
 */
export async function getStoredSeed(cacheKey) {
    try {
        // Check memory first
        const memorySeed = seedMemory.get(cacheKey);
        if (memorySeed !== undefined) {
            return memorySeed;
        }

        // Special case for 'default' seed - return a default configuration
        if (cacheKey === 'default') {
            console.log('[Cache] Creating default seed configuration');
            const defaultSeed = {
                roleName: "default",
                clonedAudio: null,
                favorite: false,
                timestamp: Date.now(),
                meshName: "default"
            };

            // Store in memory for future use
            seedMemory.set(cacheKey, defaultSeed);
            return defaultSeed;
        }

        // Check if this is a seed file with Chinese characters
        const hasChinese = /[\u4e00-\u9fff]/.test(cacheKey);
        if (hasChinese) {
            console.log(`[Cache] Seed key contains Chinese characters: ${cacheKey}`);

            // Extract the mesh name from the cache key
            const parts = cacheKey.split('_');
            let meshName;
            let hasHash = false;

            if (parts.length >= 3) {
                // For names with multiple parts (like doll_熊辉_hash)
                // Take all parts except the last one (which is the hash)
                meshName = parts.slice(0, -1).join('_');
                hasHash = true;
            } else if (parts.length === 2) {
                // For simple names with hash (like name_hash)
                meshName = parts[0];
                hasHash = true;
            } else {
                // This might be a key without hash suffix (old format)
                meshName = cacheKey;
                hasHash = false;
            }

            console.log(`[Cache] Extracted mesh name from cache key: ${meshName}, hasHash: ${hasHash}`);

            // If this is a key without hash suffix (old format), try to find a matching key with hash suffix
            if (!hasHash) {
                console.log(`[Cache] Key without hash suffix detected: ${cacheKey}`);

                // Look for a matching key with hash suffix in memory
                for (const [key, value] of seedMemory.entries()) {
                    if (key.startsWith(cacheKey + '_')) {
                        console.log(`[Cache] Found matching key with hash suffix in memory: ${key}`);
                        return value;
                    }
                }

                // If not found in memory, create a default seed object
                console.log(`[Cache] No matching key with hash suffix found, creating default seed`);
            }

        }

        // For other seeds, try to fetch from the server
        // Get the download server URL
        const port = getDownloadServerPort();
        const downloadServerUrl = `http://${config.host}:${port}`;

        // Use a consistent naming convention for all seed files
        // Just use the cacheKey directly with .json extension
        const filename = `${cacheKey}.json`;

        // Check file system - use the seedPath from SAVE_OPTIONS
        const seedPath = joinPaths(ASSETS.SAVE_OPTIONS.seedPath, filename);

        // Clean up the path to prevent doubled assets prefix
        let cleanPath = seedPath;
        if (cleanPath.startsWith('/assets/')) {
            cleanPath = cleanPath.slice(8); // Remove "/assets/"
        } else if (cleanPath.startsWith('assets/')) {
            cleanPath = cleanPath.slice(7); // Remove "assets/"
        }

        // Create a URL object to handle encoding properly
        const url = new URL(`${downloadServerUrl}/assets/${cleanPath}`);
        console.log('[Cache] Fetching seed from:', url.toString());

        try {
            const response = await fetch(url.toString(), {
                headers: { 'Accept': 'application/json' }
            });
            if (response.ok) {
                const seedText = await response.text();

                // Try to parse as JSON first (for complex seed data like voice config)
                try {
                    // Try to parse the text directly as JSON
                    let seedJson;

                    try {
                        // First try to parse directly as JSON
                        seedJson = JSON.parse(seedText);
                        console.log('[Cache] Successfully parsed seed as JSON');
                    } catch (jsonError) {
                        // If direct parsing fails, check if it's base64 encoded (legacy format)
                        if (/^[A-Za-z0-9+/=]+$/.test(seedText)) {
                            try {
                                // Try to decode base64
                                const decodedText = atob(seedText);
                                seedJson = JSON.parse(decodedText);
                                console.log('[Cache] Successfully decoded legacy base64 seed');
                            } catch (base64Error) {
                                // If base64 decoding fails, rethrow the original error
                                throw jsonError;
                            }
                        } else {
                            // Not base64, rethrow the original error
                            throw jsonError;
                        }
                    }
                    // Cache in memory
                    seedMemory.set(cacheKey, seedJson);
                    console.log('[Cache] Loaded JSON seed from file:', seedJson);
                    return seedJson;
                } catch (e) {
                    // If not JSON, try as integer (for backward compatibility)
                    const seed = parseInt(seedText, 10);
                    if (!isNaN(seed)) {
                        // Cache in memory
                        seedMemory.set(cacheKey, seed);
                        console.log('[Cache] Loaded numeric seed from file:', seed);
                        return seed;
                    }

                    // If neither JSON nor integer, return the raw text
                    seedMemory.set(cacheKey, seedText);
                    console.log('[Cache] Loaded text seed from file:', seedText);
                    return seedText;
                }
            }
        } catch (error) {
            console.log('[Cache] Seed file not found in standard path:', error.message);
        }

        // If we get here, we couldn't find the seed file in either location
        return null;
    } catch (error) {
        console.error('[Cache] Failed to load seed file:', error);
        return null;
    }
}

/**
 * Clear seed memory and optionally delete seed files
 * @param {boolean} deleteFiles - Whether to also delete seed files
 */
export async function clearSeedMemory(deleteFiles = false) {
    seedMemory.clear();
    if (deleteFiles) {
        try {
            const seedsPath = ASSETS.SAVE_OPTIONS.seedPath;
            // Note: You might want to implement a way to delete the seed files here
            console.log('[Cache] Seed files would be deleted from:', seedsPath);
        } catch (error) {
            console.error('[Cache] Failed to delete seed files:', error);
        }
    }
}

/**
 * Get model from cache or local storage
 * @param {string} source - Source type (text, image, doll)
 * @param {any} input - Input data
 * @param {number|null} seed - Seed value
 * @returns {Promise<{meshResult: any, imageResult: any, videoResult: any} | null>}
 */
export async function getCachedModel(source, input, seed = null) {
    try {
        if (!ASSETS?.SAVE_OPTIONS) {
            console.warn('[Cache] SAVE_OPTIONS not configured');
            return null;
        }
        const { meshPath, imagePath, videoPath, formats = {} } = ASSETS.SAVE_OPTIONS;
        // Generate cache key
        const cacheKey = await generateCacheKey(source, input, seed);
        console.log('[Cache] Generated cache key:', cacheKey);

        // Get file extensions
        const meshExt = formats.mesh || '.glb';
        const imageExt = formats.image || '.png';
        const videoExt = formats.video || '.mp4';
        const options = ASSETS.SAVE_OPTIONS;

        // Generate local paths
        const localPaths = {
            mesh: joinPaths(options.meshPath, `${cacheKey}${meshExt}`),
            image: joinPaths(options.imagePath, `${cacheKey}${imageExt}`),
            video: joinPaths(options.videoPath, `${cacheKey}${videoExt}`)
        };

        console.log('[Cache] Generated local paths:', localPaths);


        // Check if we have a stored seed for this input
        const storedSeed = await getStoredSeed(cacheKey);

        if (storedSeed !== null && storedSeed !== seed) {
            console.log('[Cache] Found stored seed:', storedSeed);
            // Try to get cache with stored seed
            const storedCacheKey = await generateCacheKey(source, input, storedSeed);
            const storedResult = {
                meshResult: { url: joinPaths(options.meshPath, `${storedCacheKey}${meshExt}`) },
                imageResult: { url: joinPaths(options.imagePath, `${storedCacheKey}${imageExt}`) },
                videoResult: { url: joinPaths(options.videoPath, `${storedCacheKey}${videoExt}`) }
            };
            if (storedResult) {
                console.log('[Cache] Found model with stored seed');
                return storedResult;
            }
        }

        // Check in-memory cache first
        if (modelCache.has(cacheKey)) {
            console.log('[Cache] Found model in memory cache');
            return modelCache.get(cacheKey);
        }


        // Validate paths
        if (!meshPath || !imagePath || !videoPath) {
            console.warn('[Cache] Missing required paths in SAVE_OPTIONS');
            return null;
        }

        if (!localPaths.mesh || !localPaths.image || !localPaths.video) {
            console.warn('[Cache] Failed to generate valid paths');
            return null;
        }

        // Check if files exist using fetch HEAD request
        const exists = await Promise.all([
            fileExists(localPaths.mesh),
            fileExists(localPaths.image),
            fileExists(localPaths.video)
        ]);

        if (exists[0]) { // At least mesh file exists
            console.log('[Cache] Found model in local storage');
            const result = {
                meshResult: { url: localPaths.mesh },
                imageResult: exists[1] ? { url: localPaths.image } : null,
                videoResult: exists[2] ? { url: localPaths.video } : null
            };

            // Cache in memory for future use
            modelCache.set(cacheKey, result);
            return result;
        }

        console.log('[Cache] Model not found in cache');
        return null;
    } catch (error) {
        console.error('[Cache] Error accessing cache:', error);
        return null;
    }
}

/**
 * Save model to cache and local storage
 * @param {string} source - Source type
 * @param {any} input - Input data
 * @param {{meshResult: any, imageResult: any, videoResult: any}} data - Model data
 * @param {number|null} seed - Seed value
 */
export async function cacheModel(source, input, data, seed = null) {
    try {
        // Make sure to await the key generation
        const key = await generateCacheKey(source, input, seed);
        console.log('[Cache] Cache key:', key);

        // Cache seed file
        await storeSeed(key, seed);

        // Cache mesh file if present
        if (data.meshResult?.url) {
            // Make sure url is resolved if it's a Promise
            const url = data.meshResult.url instanceof Promise
                ? await data.meshResult.url
                : data.meshResult.url;

            // Now extract the extension from the resolved URL
            const fileExtension = extractFileExtension(url);
            const meshPath = `/assets/meshes/${key}${fileExtension}`;

            console.log('[Cache] Mesh file path:', meshPath);
            await downloadFile(url, meshPath);
            data.meshResult.localPath = meshPath;
        }

        // Apply similar Promise resolution for image and video URLs
        if (data.imageResult?.url) {
            const imageUrl = data.imageResult.url instanceof Promise
                ? await data.imageResult.url
                : data.imageResult.url;

            const imagePath = `/assets/images/${key}.png`;
            console.log('[Cache] Image file path:', imagePath);
            await downloadFile(imageUrl, imagePath);
            data.imageResult.localPath = imagePath;
        }

        if (data.videoResult?.url) {
            const videoUrl = data.videoResult.url instanceof Promise
                ? await data.videoResult.url
                : data.videoResult.url;

            const videoPath = `/assets/videos/${key}.mp4`;
            console.log('[Cache] Video file path:', videoPath);
            await downloadFile(videoUrl, videoPath);
            data.videoResult.localPath = videoPath;
        }

        console.log('[Cache] Model cached successfully');
    } catch (error) {
        console.error('[Cache] Error caching model:', error);
    }
}
/**
 * Extract file extension from URL or filename
 * @param {string} url - The URL or filename
 * @returns {string} - The extracted extension including dot (e.g., ".glb")
 */
function extractFileExtension(url) {
    const urlParts = url.split('?')[0].split('#')[0]; // Remove query params and hash
    const pathParts = urlParts.split('/');
    const filename = pathParts[pathParts.length - 1];

    const extensionMatch = filename.match(/\.[a-zA-Z0-9]+$/);
    return extensionMatch ? extensionMatch[0] : ".glb"; // Default to .glb if no match
}
/**
 * Clear all cached models
 */
export function clearCache() {
    modelCache.clear();
}

// Helper functions
/**
 * Generate a cache key for an item
 * @param {string} source - Source type (text, image, voice, etc.)
 * @param {any} input - Input data (text, file, etc.)
 * @param {number|null} seed - Optional seed value
 * @returns {Promise<string>} The generated cache key
 *
 * NOTE: As of the 2023-09-15 update, all sources including 'voice' use the format {name}_{hash}
 * Previously, 'voice' and 'doll' sources used just the name without a hash, but this was changed
 * to ensure consistent naming conventions across all file types.
 * The MeshSelector component includes migration code to handle old-style doll metadata files.
 * The cache.js file includes migration code to handle old-style voice seed files without hash suffix.
 */
export async function generateCacheKey(source, input, seed = null) {
    // Get the text to hash based on input type
    let textToHash;
    if (source === 'text' || typeof input === 'string') {
        textToHash = input;
    } else if (input && input.name) {
        // Handle file objects with name and size
        textToHash = input.name + (input.size || '');
    } else {
        // Handle other input types (like simple strings for voice names)
        textToHash = String(input);
    }

    // Get display text based on input type
    let displayText;
    if (source === 'text' || typeof input === 'string') {
        displayText = input;
    } else if (input && input.name) {
        // Remove extension for files
        displayText = input.name.replace(/\.[^/.]+$/, '');
    } else {
        // Handle other input types
        displayText = String(input);
    }

    // Sanitize the display text
    const sanitizedText = displayText
        .toLowerCase()
        .trim()
        .replace(/[^a-z0-9\u4e00-\u9fff]+/g, '_') // Replace any non-alphanumeric/Chinese chars with underscore
        .replace(/^_+|_+$/g, '');     // Remove leading/trailing underscores

    // Create cache key
    let cacheKey;

    // Generate hash including seed if provided
    const hash = hashString(textToHash + (seed !== null ? seed.toString() : ''));

    // For all sources including voice and doll, include the hash to maintain consistent naming convention
    // This ensures that all seed files use the same naming convention with hash suffix
    cacheKey = `${sanitizedText}_${hash}`;
    console.log(`[Cache] Using name with hash for ${source} source: ${cacheKey}`);

    // Store seed in memory and file if provided
    if (seed !== null) {
        await storeSeed(cacheKey, seed);
    }

    return cacheKey;
}

function hashString(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
}

/**
 * Check if a file exists in the public directory
 * @param {string} path - Path relative to public directory
 * @returns {Promise<boolean>}
 */
export async function fileExists(path) {
    try {
        // Get the download server URL
        const port = getDownloadServerPort();
        const downloadServerUrl = `http://${config.host}:${port}`;
        console.log('[Cache] Download server URL:', downloadServerUrl);

        // Clean up the path to prevent doubled assets prefix
        let cleanPath = path;

        // Remove any leading "/assets/" if it exists
        if (cleanPath.startsWith('/assets/')) {
            cleanPath = cleanPath.slice(8); // Remove "/assets/"
        } else if (cleanPath.startsWith('assets/')) {
            cleanPath = cleanPath.slice(7); // Remove "assets/"
        }

        // Check if this is a seed file path
        const isSeedFile = cleanPath.startsWith('seeds/') || cleanPath.includes('/seeds/');

        // For seed files, we need to handle them differently
        if (isSeedFile) {
            // Extract the filename from the path
            const pathParts = cleanPath.split('/');
            const filename = pathParts[pathParts.length - 1];

            // Try to check if the seed file exists in the assets/seeds directory
            try {
                // Create a URL object to handle the encoding properly
                const url = new URL(`${downloadServerUrl}/assets/${cleanPath}`);
                console.log('[Cache] Checking seed file exists at:', url.toString());

                const response = await fetch(url.toString(), {
                    method: 'HEAD',
                    headers: { 'Accept': 'application/json' }
                });

                if (response.ok) {
                    console.log(`[Cache] Seed file exists: ${filename}`);
                    return true;
                } else {
                    console.log(`[Cache] Seed file does not exist: ${filename}`);
                    return false;
                }
            } catch (error) {
                console.log(`[Cache] Error checking seed file: ${error.message}`);
                return false;
            }
        } else {
            // For regular files, we'll use a direct approach with proper encoding
            // Create a URL object to handle the encoding properly
            const url = new URL(`${downloadServerUrl}/assets/${cleanPath}`);
            console.log('[Cache] Checking file exists at:', url.toString());

            try {
                const response = await fetch(url.toString(), {
                    method: 'HEAD',
                    headers: { 'Accept': 'application/json' }
                });

                return response.ok;
            } catch (error) {
                console.log('[Cache] File not found:', error.message);
                return false;
            }
        }
    } catch (error) {
        console.error('[Cache] Error checking file exists:', error);
        return false;
    }
}

/**
 * Download a file from a URL and save it to the specified path using the download server
 * @param {string} url - Source URL
 * @param {string} destPath - Destination path (e.g. /assets/models/file.glb)
 * @param {Object} [options={}] - Additional options for download
 * @param {string} [options.pose] - Pose parameter for Ready Player Me models ('T' or 'A')
 * @returns {Promise<{success: boolean, path?: string, error?: string}>}
 */
export async function downloadFile(url, destPath, options = {}) {
    try {
        // Get the download server URL
        const port = getDownloadServerPort();
        const downloadServerUrl = `http://${config.host}:${port}`;
        console.log('[Cache] Download server URL:', downloadServerUrl);

        // Clean up the destination path to avoid doubled 'assets' prefix
        let cleanDestPath = destPath;

        // Remove leading /assets/ if present
        if (destPath.startsWith('/assets/')) {
            cleanDestPath = destPath.slice(7); // Remove /assets/ prefix
        } else if (destPath.startsWith('/')) {
            cleanDestPath = destPath.slice(1); // Remove leading slash
        }

        // Remove any doubled 'assets/' in the path
        cleanDestPath = cleanDestPath.replace(/^assets\/assets\//, 'assets/');

        console.log('[Cache] Clean destination path:', cleanDestPath);

        // Decode the URL once and avoid double-encoding
        const decodedUrl = decodeURIComponent(url);

        // Log a truncated version of the URL to avoid cluttering logs with data URLs
        if (decodedUrl.startsWith('data:')) {
            const urlType = decodedUrl.split(',')[0];
            console.log('[Cache] Decoded URL type:', urlType, '(data URL content truncated)');
        } else {
            console.log('[Cache] Decoded URL:', decodedUrl);
        }

        // Process the URL without additional encoding
        let processedUrl = decodedUrl;

        // Add parameters for Ready Player Me models if needed
        if (processedUrl.includes('readyplayer.me')) {
            // Get RPM configuration from viewerConfig
            const rpmConfig = ASSETS.RPM_CONFIG || {};

            // Create parameter object with correct format (not using URLSearchParams)
            const params = [];

            // Add pose parameter (use options, or default from config, or fallback to 'T')
            const pose = options.pose || rpmConfig.defaultPose || 'T';
            params.push(`pose=${pose}`);

            // Add other RPM parameters from config if available
            // Keep the original format with commas and plus signs as literal characters
            if (rpmConfig.morphTargets) {
                params.push(`morphTargets=${rpmConfig.morphTargets}`);
            }
            if (rpmConfig.textureSizeLimit) {
                params.push(`textureSizeLimit=${rpmConfig.textureSizeLimit}`);
            }
            if (rpmConfig.textureFormat) {
                params.push(`textureFormat=${rpmConfig.textureFormat}`);
            }

            // Append parameters to URL with proper formatting
            const paramsString = params.join('&');
            processedUrl = processedUrl.includes('?')
                ? `${processedUrl}&${paramsString}`
                : `${processedUrl}?${paramsString}`;

            console.log('[Cache] RPM URL with parameters:', processedUrl);
        } else if (decodedUrl.includes('file=')) {
            const [baseUrl, filePath] = decodedUrl.split('file=');
            processedUrl = `${baseUrl}file=${filePath}`;
        }

        // Log a truncated version of the processed URL to avoid cluttering logs with data URLs
        if (processedUrl.startsWith('data:')) {
            const urlType = processedUrl.split(',')[0];
            console.log('[Cache] Processed URL type:', urlType, '(data URL content truncated)');
        } else {
            console.log('[Cache] Processed URL:', processedUrl);
        }

        // Encode components separately to avoid double-encoding
        const requestUrl = `${downloadServerUrl}/download?url=${encodeURIComponent(processedUrl)}&destPath=${encodeURIComponent(cleanDestPath)}`;

        // Log a truncated version of the request URL to avoid cluttering logs with data URLs
        if (processedUrl.startsWith('data:')) {
            console.log('[Cache] Making request to download server with data URL (content truncated) and destPath:', cleanDestPath);
        } else {
            console.log('[Cache] Making request to:', requestUrl);
        }

        // Send request to download server with timeout
        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

        try {
            const response = await fetch(requestUrl, {
                signal: controller.signal,
                headers: {
                    'Accept': 'application/json'
                }
            });
            clearTimeout(timeout);

            console.log('[Cache] Response status:', response.status);
            console.log('[Cache] Response headers:', Object.fromEntries(response.headers.entries()));

            if (!response.ok) {
                let errorMessage;
                try {
                    const text = await response.text();
                    try {
                        const data = JSON.parse(text);
                        errorMessage = data.error || `HTTP ${response.status} ${response.statusText}`;
                        console.error('[Cache] Server error details:', data);
                    } catch {
                        errorMessage = text || `HTTP ${response.status} ${response.statusText}`;
                    }
                } catch (e) {
                    errorMessage = `HTTP ${response.status} ${response.statusText}`;
                }
                throw new Error(`Download server error: ${errorMessage}`);
            }

            let result;
            try {
                result = await response.json();
            } catch (e) {
                console.error('[Cache] Failed to parse JSON response:', e);
                throw new Error('Invalid response from server');
            }

            console.log('[Cache] Download completed successfully:', result);
            return result;
        } finally {
            clearTimeout(timeout);
        }
    } catch (error) {
        console.error('[Cache] Download failed:', error);
        if (error.name === 'AbortError') {
            return {
                success: false,
                error: 'Download request timed out after 30 seconds'
            };
        }
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Fetch a file from the local assets directory
 * @param {string} path - Path to the file relative to assets directory
 * @returns {Promise<{success: boolean, content?: string, error?: string}>}
 */
export async function fetchFile(path) {
    try {
        // Get the download server URL
        const port = getDownloadServerPort();
        const downloadServerUrl = `http://${config.host}:${port}`;

        // Clean up the path to prevent doubled assets prefix
        let cleanPath = path;

        // Remove any leading "/assets/" if it exists
        if (cleanPath.startsWith('/assets/')) {
            cleanPath = cleanPath.slice(8); // Remove "/assets/"
        } else if (cleanPath.startsWith('assets/')) {
            cleanPath = cleanPath.slice(7); // Remove "assets/"
        }

        // Check if this is a seed file path
        const isSeedFile = cleanPath.startsWith('seeds/') || cleanPath.includes('/seeds/');

        if (isSeedFile) {
            // Extract the filename from the path
            const pathParts = cleanPath.split('/');
            const filename = pathParts[pathParts.length - 1];

            // For seed files, we need to read directly from the filesystem
            // since we can't access them through the download server
            console.log(`[Cache] Reading seed file directly from filesystem: ${filename}`);

            try {
                // First try to fetch the seed file from the assets/seeds directory
                try {
                    // Create a URL object to handle the encoding properly
                    const url = new URL(`${downloadServerUrl}/assets/${cleanPath}`);
                    console.log('[Cache] Trying to fetch seed file from:', url.toString());

                    const response = await fetch(url.toString(), {
                        headers: { 'Accept': 'application/json' }
                    });

                    if (response.ok) {
                        const content = await response.text();
                        console.log(`[Cache] Successfully fetched seed file: ${filename}`);
                        return {
                            success: true,
                            content: content
                        };
                    } else {
                        console.log(`[Cache] Seed file not found at ${url.toString()}, using default data`);
                    }
                } catch (fetchError) {
                    console.log(`[Cache] Error fetching seed file: ${fetchError.message}, using default data`);
                }

                // If we couldn't fetch the file, create a default seed object
                console.log(`[Cache] Using default seed data for: ${filename}`);

                // Extract the mesh name from the filename (remove the hash and extension)
                // The filename format is expected to be meshName_hash.json
                // For Chinese names, it might be prefix_chineseName_hash.json
                const parts = filename.replace('.json', '').split('_');
                let meshName;

                if (parts.length >= 3) {
                    // For names with multiple parts (like doll_熊辉_hash)
                    // Take all parts except the last one (which is the hash)
                    meshName = parts.slice(0, -1).join('_');
                } else if (parts.length === 2) {
                    // For simple names with hash (like name_hash)
                    meshName = parts[0];
                } else {
                    // Fallback for unexpected formats
                    meshName = parts.join('_');
                }

                console.log(`[Cache] Extracted mesh name from filename: ${meshName}`);

                // Create a default seed object
                const defaultSeed = {
                    roleName: "陈鲁豫", // Default role name for Chinese characters
                    clonedAudio: null,
                    favorite: false,
                    timestamp: Date.now(),
                    meshName: meshName
                };

                // Don't save the seed file without hash suffix
                // This prevents creating duplicate seed files when previewing voices
                console.log(`[Cache] Using default seed data in memory only for: ${filename}`);

                return {
                    success: true,
                    content: JSON.stringify(defaultSeed)
                };
            } catch (error) {
                console.log('[Cache] Error reading seed file:', error.message);
                throw error;
            }
        } else {
            // For regular files, use the URL object to handle encoding properly
            const url = new URL(`${downloadServerUrl}/assets/${cleanPath}`);
            console.log('[Cache] Fetching file from:', url.toString());

            const response = await fetch(url.toString(), {
                headers: { 'Accept': 'application/json' }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status} ${response.statusText}`);
            }

            const content = await response.text();
            return {
                success: true,
                content: content
            };
        }
    } catch (error) {
        console.error('[Cache] Error fetching file:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Upload an audio file to the server
 * @param {Blob|File} audioFile - The audio file to upload
 * @param {string} destinationPath - Destination path (e.g. /assets/audio/file.wav)
 * @returns {Promise<{success: boolean, path?: string, error?: string}>}
 */
export async function uploadAudioFile(audioFile, destinationPath) {
    try {
        if (!audioFile) {
            throw new Error('No audio file provided');
        }

        // Clean up the destination path
        const cleanDestPath = destinationPath.replace(/^\/+|\/+$/g, '');
        const fileName = cleanDestPath.split('/').pop() || 'audio.wav';

        // Ensure the directory exists by extracting the directory path
        const dirPath = cleanDestPath.split('/').slice(0, -1).join('/');

        // Create FormData
        const formData = new FormData();
        formData.append('path', dirPath || 'audio');
        formData.append('name', fileName);

        // Check if we need to convert the audio file
        let fileToUpload = audioFile;

        // If the file is larger than 20MB, log a warning
        if (audioFile.size > 20 * 1024 * 1024) {
            console.warn('[Cache] Large audio file detected:', {
                size: audioFile.size,
                sizeInMB: (audioFile.size / (1024 * 1024)).toFixed(2) + 'MB'
            });
        }

        // Add the file to the form data
        formData.append('file', fileToUpload, fileName);

        // Get the download server URL
        const port = getDownloadServerPort();
        const downloadServerUrl = `http://${config.host}:${port}`;

        console.log('[Cache] Uploading audio file:', {
            fileName,
            path: cleanDestPath,
            size: fileToUpload.size,
            type: fileToUpload.type
        });

        // Make the upload request with timeout and retry logic
        let retries = 0;
        const maxRetries = 3;
        let lastError = null;

        while (retries < maxRetries) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout

                const response = await fetch(`${downloadServerUrl}/upload`, {
                    method: 'POST',
                    body: formData,
                    signal: controller.signal,
                    // Don't set Content-Type header - let the browser set it with the boundary
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    let errorMessage;
                    try {
                        const errorData = await response.json();
                        errorMessage = errorData.error || response.statusText;
                        console.error('[Cache] Upload server error details:', errorData);
                    } catch (e) {
                        errorMessage = response.statusText;
                        console.error('[Cache] Failed to parse error response:', e);
                    }
                    throw new Error(`Upload server error: ${errorMessage}`);
                }

                const result = await response.json();
                console.log('[Cache] Upload successful:', result);
                return {
                    ...result,
                    success: true
                };
            } catch (error) {
                lastError = error;
                retries++;

                if (error.name === 'AbortError') {
                    console.warn(`[Cache] Upload timed out (attempt ${retries}/${maxRetries})`);
                } else {
                    console.warn(`[Cache] Upload failed (attempt ${retries}/${maxRetries}):`, error.message);
                }

                if (retries < maxRetries) {
                    // Wait before retrying (exponential backoff)
                    const delay = Math.pow(2, retries) * 1000;
                    console.log(`[Cache] Retrying in ${delay}ms...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }

        // If we get here, all retries failed
        throw lastError || new Error('Upload failed after multiple attempts');
    } catch (error) {
        console.error('[Cache] Upload failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// blobToBase64 function imported from media/modality/audio.js
// No local implementation needed - using the canonical version
export { blobToBase64 };