/**
 * Message formatting utilities for LangChain and Aliyun Bailian
 * Handles conversion between different message formats and multimodal content
 */

import { processMultimodal } from '@/media';
import { createLogger } from '@/utils/logger';

const logger = createLogger('MessageFormatters');

/**
 * Convert LangChain messages to Aliyun Bailian format
 * @param {Array} messages - LangChain messages
 * @param {boolean} isOmniModel - Whether the model supports multimodal content
 * @returns {Array} Formatted messages for Aliyun API
 */
export function convertLangChainToAliyun(messages, isOmniModel = true) {
    return messages.map(message => {
        const role = message._getType() === 'human' ? 'user' :
            message._getType() === 'ai' ? 'assistant' :
                message._getType() === 'system' ? 'system' : 'user';

        const content = message.content;

        // Handle multimodal content for omni models
        if (Array.isArray(content) && isOmniModel) {
            return {
                role,
                content: formatMultimodalContent(content)
            };
        }

        return {
            role,
            content: typeof content === 'string' ? content : JSON.stringify(content)
        };
    });
}

/**
 * Format multimodal content for Aliyun omni models
 * @param {Array} content - Multimodal content array
 * @returns {Array} Formatted content for Aliyun API
 */
export function formatMultimodalContent(content) {
    try {
        // Use processMultimodal from @/media for standardization
        const processedContent = processMultimodal(content, {
            validateInput: true,
            optimizeForAliyun: true
        });

        return processedContent.map(item => {
            switch (item.type) {
                case 'text':
                    return {
                        type: 'text',
                        text: item.text
                    };

                case 'image_url':
                    return {
                        type: 'image_url',
                        image_url: {
                            url: item.image_url?.url || item.image_url,
                            min_pixels: item.min_pixels || 28 * 28 * 4,
                            max_pixels: item.max_pixels || 28 * 28 * 8192
                        }
                    };

                case 'video':
                    return {
                        type: 'video',
                        video: Array.isArray(item.video) ? item.video : [item.video]
                    };

                case 'audio':
                    return {
                        type: 'audio',
                        audio: item.audio
                    };

                default:
                    return item;
            }
        });
    } catch (error) {
        logger.warn('Error processing multimodal content, using fallback:', error);

        // Fallback to direct mapping
        return content.map(item => ({
            type: item.type,
            [item.type === 'text' ? 'text' : item.type]: item[item.type === 'text' ? 'text' : item.type]
        }));
    }
}

/**
 * Format tools for LangGraph compatibility
 * @param {Array} tools - Array of tool definitions
 * @returns {Array} Formatted tools for Aliyun API
 */
export function formatToolsForAliyun(tools) {
    if (!Array.isArray(tools) || tools.length === 0) {
        return [];
    }

    return tools.map(tool => {
        // If already in correct format
        if (typeof tool === 'object' && tool.function) {
            return tool;
        }

        // Convert from LangGraph tool format
        if (tool.name && tool.description) {
            return {
                type: "function",
                function: {
                    name: tool.name,
                    description: tool.description,
                    parameters: tool.schema || {
                        type: "object",
                        properties: {},
                        required: []
                    }
                }
            };
        }

        logger.warn('Unknown tool format:', tool);
        return tool;
    });
}

/**
 * Create request body for Aliyun API
 * @param {Object} params - Request parameters
 * @returns {Object} Formatted request body
 */
export function createAliyunRequestBody({
    model,
    messages,
    temperature,
    maxTokens,
    tools = null,
    streaming = false,
    ...options
}) {
    const requestBody = {
        model,
        messages,
        temperature,
        max_tokens: maxTokens,
        stream: streaming
    };

    // Add streaming options
    if (streaming) {
        requestBody.stream_options = { include_usage: true };
    }

    // Add tools if provided
    if (tools && tools.length > 0) {
        requestBody.tools = formatToolsForAliyun(tools);
        requestBody.tool_choice = "auto";
    }

    // Add any additional options
    Object.assign(requestBody, options);

    return requestBody;
} 