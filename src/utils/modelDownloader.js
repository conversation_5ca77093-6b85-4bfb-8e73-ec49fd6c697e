import { config } from '../config/client.js';
import { MODEL_CONFIGS } from '../config/models.js';
import { getDownloadServerUrl, buildDownloadServerUrl } from './portManager.js';

// Utility function for path handling
function dirname(path) {
    return path.split('/').slice(0, -1).join('/');
}

function basename(path) {
    return path.split('/').pop();
}

class ModelDownloader {
    constructor(options = {}) {
        this.uiManager = options.uiManager;
        this.modelCache = new Map();
        this.modelStorage = window.indexedDB;
        this.initializeModelCache();
        this._isLoadingModel = false;
    }

    async initializeModelCache() {
        try {
            const request = this.modelStorage.open('ModelCache', 1);

            request.onerror = () => {
                console.error('[ModelDownloader] Failed to open model cache database');
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                if (!db.objectStoreNames.contains('models')) {
                    db.createObjectStore('models', { keyPath: 'name' });
                }
            };

            request.onsuccess = () => {
                console.log('[ModelDownloader] Model cache initialized');
            };
        } catch (error) {
            console.error('[ModelDownloader] Model cache initialization failed:', error);
        }
    }

    async ensureModelExists(modelConfig) {
        if (this._isLoadingModel) {
            console.log('[ModelDownloader] Another model load in progress, waiting...');
            await new Promise(resolve => {
                const checkLoading = setInterval(() => {
                    if (!this._isLoadingModel) {
                        clearInterval(checkLoading);
                        resolve();
                    }
                }, 100);
            });
        }

        this._isLoadingModel = true;
        if (this.uiManager) {
            this.uiManager.showLoadingPanel('Checking model availability...');
        }

        try {
            // Build proper URL using port manager
            const baseUrl = config.modelConfig.baseUrl;
            const cleanModelPath = modelConfig.modelPath.replace(baseUrl, '').replace(/^\/+/, '');

            // First, try to check if file exists in public directory using relative paths
            // Avoid duplicating "models" in the path
            const modelsDirPath = cleanModelPath.startsWith('models/') ? cleanModelPath : `models/${cleanModelPath}`;
            const publicPaths = [
                `/${modelsDirPath}`,          // Standard models directory  
                `/${cleanModelPath}`,         // Direct path
                `/public/${modelsDirPath}`,   // Alternative public path with models
            ];

            console.log(`[ModelDownloader] Checking for local model: ${cleanModelPath}`);

            // Try to fetch from download server first (using correct port from portManager)
            for (const publicPath of publicPaths) {
                try {
                    const publicUrl = buildDownloadServerUrl(publicPath);
                    console.log(`[ModelDownloader] Checking public path: ${publicUrl}`);
                    const publicResponse = await fetch(publicUrl, { method: 'HEAD' });
                    if (publicResponse.ok) {
                        console.log(`[ModelDownloader] Model found in public directory: ${publicUrl}`);
                        return publicUrl;
                    }
                } catch (error) {
                    // Continue to next path
                    console.debug(`[ModelDownloader] Public path not found: ${publicPath}`);
                }
            }

            // If not found in public, try download server
            const localUrl = buildDownloadServerUrl(`/${modelsDirPath}`);
            console.log(`[ModelDownloader] Checking download server at: ${localUrl}`);

            try {
                const localResponse = await fetch(localUrl, { method: 'HEAD' });
                if (localResponse.ok) {
                    console.log('[ModelDownloader] Model found on download server');
                    return localUrl;
                }
            } catch (error) {
                console.log('[ModelDownloader] Download server not available, will download from remote...');
                this.uiManager?.updateLoadingProgress('Downloading model...');
            }

            // Download from remote
            const remoteResponse = await fetch(modelConfig.fallbackPath);
            if (!remoteResponse.ok) {
                throw new Error(`Remote fetch failed: ${remoteResponse.status}`);
            }

            const modelBuffer = await remoteResponse.arrayBuffer();
            this.uiManager?.updateLoadingProgress('Saving model locally...');

            // Save downloaded model to the correct location
            const modelSavePath = `models/${cleanModelPath}`;
            await this.saveModelToPublic(modelSavePath, modelBuffer);
            console.log('[ModelDownloader] Model downloaded and saved successfully');

            // Return the public URL path for the saved model  
            return window.location.origin + `/models/${cleanModelPath}`;
        } catch (error) {
            console.error('[ModelDownloader] Model check/download failed:', error);
            throw error;
        } finally {
            this.uiManager?.hideLoadingPanel();
            this._isLoadingModel = false;
        }
    }

    async saveModelToPublic(modelPath, modelBuffer) {
        try {
            // Clean up the model directory path and ensure it's under assets
            const rawModelDir = dirname(modelPath).replace(/^\/+|\/+$/g, '');
            const modelDir = rawModelDir.startsWith('assets/') ? rawModelDir : `assets/${rawModelDir}`;
            console.log('[ModelDownloader] Preparing upload:', {
                modelPath,
                rawModelDir,
                modelDir,
                bufferSize: modelBuffer.byteLength
            });

            // Create directory first using the proper endpoint from createDirHandler.ts
            try {
                // Use the create-directory endpoint with query parameter as expected by createGeneralDirHandler
                const createDirUrl = buildDownloadServerUrl(`/create-dir?dir=${encodeURIComponent(modelDir)}`);
                const createDirResponse = await fetch(createDirUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!createDirResponse.ok) {
                    throw new Error(`Failed to create directory: ${await createDirResponse.text()}`);
                }

                this.uiManager?.updateLoadingProgress('Directory created...');
            } catch (error) {
                console.error('[ModelDownloader] Directory creation failed:', error);
                throw error;
            }

            // Prepare and send upload
            const formData = new FormData();
            const blob = new Blob([modelBuffer], { type: 'application/octet-stream' });
            const filename = basename(modelPath);

            formData.append('path', modelDir);
            formData.append('file', blob, filename);

            const uploadUrl = buildDownloadServerUrl('/upload');
            this.uiManager?.updateLoadingProgress('Uploading model...');

            const response = await fetch(uploadUrl, {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            console.log('[ModelDownloader] Upload successful:', result);
            return result;

        } catch (error) {
            console.error('[ModelDownloader] Save model error:', error);
            throw error;
        }
    }
}

export default ModelDownloader; 