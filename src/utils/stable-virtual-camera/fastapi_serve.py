import os
import time
import glob
import uuid
import shutil
import gc
import asyncio
from concurrent.futures import ThreadPoolExecutor
from typing import List, Optional, Tuple, Union, Dict, Any
from pathlib import Path

import numpy as np
from fastapi import FastAPI, HTTPException, BackgroundTasks, UploadFile, File, Form
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from pydantic import BaseModel
import aiofiles
from einops import rearrange
import torch

# Import the actual demo function
from demo import main as demo_main

# Import quilt generation components
try:
    from traj_tool.quilt_generator import QuiltImageGenerator, SevaQuiltGenerator
    from traj_tool.lookingglass_config import get_device_config, list_available_devices
    from PIL import Image
    QUILT_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Quilt generation components not available: {e}")
    QUILT_AVAILABLE = False

# Try to import dust3r components for preprocessing
try:
    from seva.modules.preprocessor import Dust3rPipeline
    from seva.eval import transform_img_and_K
    from seva.geometry import normalize_scene, get_default_intrinsics
    import torch
    import imageio.v3 as iio
    DUST3R_AVAILABLE = True
    
    # Initialize dust3r pipeline (use CPU by default to avoid GPU conflicts)
    device = "cuda" if torch.cuda.is_available() else "cpu"
    DUST3R = Dust3rPipeline(device=device)
    print(f"Dust3r initialized on {device}")
except ImportError as e:
    print(f"Warning: Dust3r components not available: {e}")
    DUST3R_AVAILABLE = False
    DUST3R = None


app = FastAPI(
    title="Stable Virtual Camera API",
    description="API to generate trajectory videos using img2trajvid_s-prob and img2trajvid models.",
    version="1.0.0"
)

# Create directories for uploads and outputs
UPLOAD_DIR = "uploads"
OUTPUT_DIR = "generated_videos"
WORK_DIR = "work_dirs/api"

for directory in [UPLOAD_DIR, OUTPUT_DIR, WORK_DIR]:
    os.makedirs(directory, exist_ok=True)

# Mount the 'generated_videos' directory to serve files under the '/videos' path
app.mount("/videos", StaticFiles(directory=OUTPUT_DIR), name="videos")

# Global storage for job status
job_status: Dict[str, Dict[str, Any]] = {}


class JobStatus(BaseModel):
    job_id: str
    status: str  # "pending", "running", "completed", "failed"
    message: str
    created_at: str
    completed_at: Optional[str] = None
    output_path: Optional[str] = None
    error: Optional[str] = None


def generate_trajectory_video(
    job_id: str,
    data_path: str,
    task: str,
    **kwargs
) -> None:
    """
    Wrapper function that calls the actual demo main function and updates job status.
    """
    try:
        job_status[job_id]["status"] = "running"
        job_status[job_id]["message"] = f"Processing {task} task..."
        
        print(f"--- Starting Video Generation for job {job_id} ---")
        print(f"Task: {task}, Data Path: {data_path}")
        print(f"Arguments: {kwargs}")
        
        # Set up save directory for this job
        save_subdir = f"job_{job_id}"
        
        # Convert cfg to the expected format
        if isinstance(kwargs.get("cfg"), list):
            kwargs["cfg"] = ",".join(map(str, kwargs["cfg"]))
        elif isinstance(kwargs.get("cfg"), str) and "," in kwargs["cfg"]:
            # Convert string like "4.0,2.0" to float for the first value (primary cfg)
            try:
                cfg_values = kwargs["cfg"].split(",")
                kwargs["cfg"] = float(cfg_values[0])  # Use first value as primary cfg
            except (ValueError, TypeError):
                kwargs["cfg"] = 2.0  # Default fallback
        
        # Convert T parameter to int if it's a string
        if "T" in kwargs and isinstance(kwargs["T"], str):
            try:
                kwargs["T"] = int(kwargs["T"])
            except ValueError:
                # If T contains commas, split and take first value
                if "," in kwargs["T"]:
                    kwargs["T"] = int(kwargs["T"].split(",")[0])
                else:
                    kwargs["T"] = 21  # Default fallback
        
        # Convert camera_scale to float if it's a string
        if "camera_scale" in kwargs:
            try:
                kwargs["camera_scale"] = float(kwargs["camera_scale"])
            except (ValueError, TypeError):
                kwargs["camera_scale"] = 2.0  # Default fallback
        
        # Convert other numeric parameters to proper types
        numeric_params = ['H', 'W', 'T', 'seed', 'num_steps', 'video_save_fps', 'L_short']
        for param in numeric_params:
            if param in kwargs:
                try:
                    if param == 'video_save_fps':
                        kwargs[param] = float(kwargs[param])
                    else:
                        kwargs[param] = int(kwargs[param])
                except (ValueError, TypeError):
                    pass  # Keep original value if conversion fails
        
        # Call the actual demo main function
        demo_main(
            data_path=data_path,
            task=task,
            save_subdir=save_subdir,
            **kwargs
        )
        
        # Find the generated output
        work_dir = f"work_dirs/demo/{task}/{save_subdir}"
        output_files = []
        
        # Look for generated videos and images
        for ext in ["*.mp4", "*.avi", "*.mov"]:
            output_files.extend(glob.glob(os.path.join(work_dir, "**", ext), recursive=True))
        
        if output_files:
            # Copy to output directory for serving
            output_filename = f"{task}_{job_id}.mp4"
            output_path = os.path.join(OUTPUT_DIR, output_filename)
            shutil.copy2(output_files[0], output_path)
            
            job_status[job_id]["status"] = "completed"
            job_status[job_id]["message"] = "Video generation completed successfully"
            job_status[job_id]["output_path"] = f"/videos/{output_filename}"
        else:
            # No video found, but check for images and create a simple response
            job_status[job_id]["status"] = "completed"
            job_status[job_id]["message"] = "Processing completed - check work directory for outputs"
            job_status[job_id]["output_path"] = work_dir
            
        job_status[job_id]["completed_at"] = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"--- Finished Video Generation for job {job_id} ---")
        
    except Exception as e:
        job_status[job_id]["status"] = "failed"
        job_status[job_id]["message"] = f"Error during processing: {str(e)}"
        job_status[job_id]["error"] = str(e)
        job_status[job_id]["completed_at"] = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"--- Error in job {job_id}: {e} ---")


@app.post("/generate/single-image", response_model=Dict[str, str])
async def generate_single_image_video(
    data_path: str = Form(..., description="Path to the single image file"),
    traj_prior: str = Form("orbit", description="Trajectory prior type"),
    cfg: str = Form("4.0,2.0", description="Configuration values"),
    camera_scale: float = Form(2.0, description="Camera scale factor"),
    num_targets: int = Form(111, description="Number of target frames"),
    guider: str = Form("1,2", description="Guider configuration"),
    use_traj_prior: bool = Form(True, description="Use trajectory prior"),
    chunk_strategy: str = Form("interp", description="Chunk strategy"),
    replace_or_include_input: bool = Form(True, description="Include input in output"),
    seed: int = Form(23, description="Random seed"),
    H: int = Form(576, description="Height of output images"),
    W: int = Form(576, description="Width of output images"),
    L_short: int = Form(576, description="Resize shortest side"),
    T: Union[int, str] = Form(21, description="Number of frames"),
    num_steps: int = Form(50, description="Number of denoising steps"),
    video_save_fps: float = Form(30.0, description="Video save FPS"),
    zoom_factor: Optional[float] = Form(None, description="Zoom factor for zoom/dolly-zoom trajectories"),
    background_tasks: BackgroundTasks = None
):
    """
    Generate trajectory video from a single image using img2trajvid_s-prob task.
    
    Supports various trajectory priors:
    - orbit: Circular camera motion around the scene
    - pan-left, pan-right, pan-up, pan-down: Panning motions
    - dolly-in, dolly-out: Forward/backward camera motion
    - spiral: Spiral camera motion
    """
    job_id = str(uuid.uuid4())
    
    # Initialize job status
    job_status[job_id] = {
        "job_id": job_id,
        "status": "pending",
        "message": f"Single image video generation queued for trajectory: {traj_prior}",
        "created_at": time.strftime("%Y-%m-%d %H:%M:%S"),
        "completed_at": None,
        "output_path": None,
        "error": None
    }
    
    # Prepare arguments for demo function
    kwargs = {
        "traj_prior": traj_prior,
        "cfg": cfg,
        "camera_scale": camera_scale,
        "num_targets": num_targets,
        "guider": guider,
        "use_traj_prior": use_traj_prior,
        "chunk_strategy": chunk_strategy,
        "replace_or_include_input": replace_or_include_input,
        "seed": seed,
        "H": H,
        "W": W,
        "L_short": L_short,
        "T": T,
        "num_steps": num_steps,
        "video_save_fps": video_save_fps
    }
    
    # Add zoom_factor if provided (required for zoom/dolly-zoom trajectories)
    if zoom_factor is not None:
        kwargs["zoom_factor"] = zoom_factor
    
    # Add the background task
    background_tasks.add_task(
        generate_trajectory_video,
        job_id=job_id,
        data_path=data_path,
        task="img2trajvid_s-prob",
        **kwargs
    )
    
    return {
        "job_id": job_id,
        "message": f"Single image video generation started for trajectory: {traj_prior}",
        "status_url": f"/status/{job_id}"
    }


@app.post("/generate/multi-image", response_model=Dict[str, str])
async def generate_multi_image_video(
    data_path: str = Form(..., description="Path to folder with multiple images (parsable by ReconfusionParser)"),
    cfg: str = Form("3.0,2.0", description="Configuration values for multi-image task"),
    num_inputs: Optional[int] = Form(None, description="Number of input views (auto-detected if not provided)"),
    chunk_strategy: str = Form("interp-gt", description="Chunk strategy (interp-gt for sparse-view, interp for semi-dense)"),
    chunk_strategy_first_pass: str = Form("gt-nearest", description="First pass chunk strategy"),
    use_traj_prior: bool = Form(True, description="Use trajectory prior"),
    camera_scale: float = Form(2.0, description="Camera scale factor"),
    seed: int = Form(23, description="Random seed"),
    H: int = Form(576, description="Height of output images"),
    W: int = Form(576, description="Width of output images"),
    L_short: int = Form(576, description="Resize shortest side"),
    T: Union[int, str] = Form(21, description="Number of frames"),
    num_steps: int = Form(50, description="Number of denoising steps"),
    video_save_fps: float = Form(30.0, description="Video save FPS"),
    background_tasks: BackgroundTasks = None
):
    """
    Generate trajectory video from multiple images using img2trajvid task.
    
    Supports both sparse-view (P <= 8) and semi-dense view (P > 9) regimes.
    The data_path should point to a folder parsable by ReconfusionParser.
    """
    job_id = str(uuid.uuid4())
    
    # Initialize job status
    job_status[job_id] = {
        "job_id": job_id,
        "status": "pending", 
        "message": f"Multi-image video generation queued",
        "created_at": time.strftime("%Y-%m-%d %H:%M:%S"),
        "completed_at": None,
        "output_path": None,
        "error": None
    }
    
    # Prepare arguments for demo function
    kwargs = {
        "cfg": cfg,
        "num_inputs": num_inputs,
        "chunk_strategy": chunk_strategy,
        "chunk_strategy_first_pass": chunk_strategy_first_pass,
        "use_traj_prior": use_traj_prior,
        "camera_scale": camera_scale,
        "seed": seed,
        "H": H,
        "W": W,
        "L_short": L_short,
        "T": T,
        "num_steps": num_steps,
        "video_save_fps": video_save_fps
    }
    
    # Add the background task
    background_tasks.add_task(
        generate_trajectory_video,
        job_id=job_id,
        data_path=data_path,
        task="img2trajvid",
        **kwargs
    )
    
    return {
        "job_id": job_id,
        "message": "Multi-image video generation started",
        "status_url": f"/status/{job_id}"
    }


@app.post("/generate/custom-trajectory", response_model=Dict[str, str])
async def generate_custom_trajectory_video(
    data_path: str = Form(..., description="Path to the single image file"),
    camera_poses: str = Form(..., description="JSON string of camera poses [{'position': [x,y,z], 'rotation': [rx,ry,rz], 'fov': degrees}, ...]"),
    cfg: str = Form("4.0,2.0", description="Configuration values"),
    camera_scale: float = Form(2.0, description="Camera scale factor"),
    seed: int = Form(23, description="Random seed"),
    H: int = Form(576, description="Height of output images"),
    W: int = Form(576, description="Width of output images"),
    L_short: int = Form(576, description="Resize shortest side"),
    T: Union[int, str] = Form(21, description="Number of frames"),
    num_steps: int = Form(50, description="Number of denoising steps"),
    video_save_fps: float = Form(30.0, description="Video save FPS"),
    background_tasks: BackgroundTasks = None
):
    """
    Generate trajectory video from a single image using custom camera trajectory.
    
    Camera poses should be provided as JSON string with format:
    [
        {"position": [x, y, z], "rotation": [rx, ry, rz], "fov": degrees},
        ...
    ]
    
    Where:
    - position: 3D camera position in world coordinates
    - rotation: Euler angles in degrees (pitch, yaw, roll)
    - fov: Field of view in degrees
    """
    import json
    
    job_id = str(uuid.uuid4())
    
    try:
        # Parse the camera poses
        poses_data = json.loads(camera_poses)
        if not isinstance(poses_data, list) or len(poses_data) == 0:
            raise ValueError("Camera poses must be a non-empty list")
            
        # Validate pose format
        for i, pose in enumerate(poses_data):
            if not all(key in pose for key in ['position', 'rotation', 'fov']):
                raise ValueError(f"Pose {i} missing required keys: position, rotation, fov")
                
    except (json.JSONDecodeError, ValueError) as e:
        raise HTTPException(status_code=400, detail=f"Invalid camera poses format: {str(e)}")
    
    # Initialize job status
    job_status[job_id] = {
        "job_id": job_id,
        "status": "pending",
        "message": f"Custom trajectory video generation queued ({len(poses_data)} keyframes)",
        "created_at": time.strftime("%Y-%m-%d %H:%M:%S"),
        "completed_at": None,
        "output_path": None,
        "error": None
    }
    
    # Prepare arguments for demo function
    kwargs = {
        "cfg": cfg,
        "camera_scale": camera_scale,
        "seed": seed,
        "H": H,
        "W": W,
        "L_short": L_short,
        "T": T,
        "num_steps": num_steps,
        "video_save_fps": video_save_fps,
        "custom_camera_poses": poses_data,  # Pass custom poses to be handled by demo function
        "use_traj_prior": False,  # Disable preset trajectories
        "replace_or_include_input": True,
        "chunk_strategy": "interp"
    }
    
    # Add the background task
    background_tasks.add_task(
        generate_trajectory_video,
        job_id=job_id,
        data_path=data_path,
        task="img2trajvid_s-prob",  # Use single image task but with custom trajectory
        **kwargs
    )
    
    return {
        "job_id": job_id,
        "message": f"Custom trajectory video generation started ({len(poses_data)} keyframes)",
        "status_url": f"/status/{job_id}"
    }


@app.post("/upload-single-image")
async def upload_single_image(file: UploadFile = File(...)):
    """
    Upload a single image for img2trajvid_s-prob processing.
    Returns the file path that can be used with the single-image generation endpoint.
    """
    if not file.content_type or not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    # Generate unique filename
    file_extension = os.path.splitext(file.filename)[1] if file.filename else '.jpg'
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join(UPLOAD_DIR, unique_filename)
    
    # Save the uploaded file
    async with aiofiles.open(file_path, 'wb') as f:
        content = await file.read()
        await f.write(content)
    
    return {
        "message": "File uploaded successfully",
        "file_path": file_path,
        "filename": unique_filename
    }


@app.post("/upload-images")
async def upload_multiple_images(files: List[UploadFile] = File(...)):
    """
    Upload multiple images for batch processing.
    Creates a folder and returns the folder path that can be used with multi-image generation.
    """
    if not files:
        raise HTTPException(status_code=400, detail="No files provided")
    
    # Validate all files are images
    for file in files:
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail=f"File {file.filename} must be an image")
    
    # Create unique folder for this batch
    batch_id = str(uuid.uuid4())
    batch_dir = os.path.join(UPLOAD_DIR, f"batch_{batch_id}")
    os.makedirs(batch_dir, exist_ok=True)
    
    uploaded_files = []
    
    # Save all uploaded files
    for i, file in enumerate(files):
        file_extension = os.path.splitext(file.filename)[1] if file.filename else '.jpg'
        filename = f"{i:04d}{file_extension}"
        file_path = os.path.join(batch_dir, filename)
        
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        uploaded_files.append({
            "original_filename": file.filename,
            "saved_filename": filename,
            "file_path": file_path
        })
    
    return {
        "message": f"Successfully uploaded {len(files)} files",
        "data_path": batch_dir,
        "batch_id": batch_id,
        "files": uploaded_files
    }


class PreprocessRequest(BaseModel):
    data_path: str
    extract_cameras: bool = True
    normalize_scene: bool = True


@app.post("/preprocess")
async def preprocess_images(request: PreprocessRequest):
    """
    Preprocess uploaded images to extract cameras and points using dust3r.
    Returns width, height, scene_scale and other metadata needed by the Gradio app.
    """
    if not os.path.exists(request.data_path):
        raise HTTPException(status_code=404, detail="Data path not found")
    
    # Find image files
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
        image_files.extend(glob.glob(os.path.join(request.data_path, ext)))
        image_files.extend(glob.glob(os.path.join(request.data_path, ext.upper())))
    
    if not image_files:
        raise HTTPException(status_code=400, detail="No image files found in data path")
    
    # Sort image files for consistent ordering
    image_files.sort()
    
    # Get dimensions from first image
    from PIL import Image
    first_image = Image.open(image_files[0])
    width, height = first_image.size
    
    # Preprocess with dust3r if available
    if DUST3R_AVAILABLE and DUST3R is not None:
        try:
            return await preprocess_with_dust3r(image_files, width, height)
        except Exception as e:
            print(f"Warning: Dust3r preprocessing failed: {e}")
            print("Falling back to mock data...")
    
    # Fallback to mock data generation
    return generate_mock_preprocessing_data(image_files, width, height)


async def preprocess_with_dust3r(image_files: List[str], width: int, height: int):
    """Use dust3r to preprocess images similar to demo_gr.py"""
    shorter = 576  # Standard size from demo_gr.py
    shorter = round(shorter / 64) * 64  # Must be 64 multiple
    
    num_images = len(image_files)
    
    # For both single and multiple images, use the same dust3r approach as demo_gr.py
    (
        imgs,
        input_Ks_numpy,
        input_c2ws_numpy,
        points,
        point_colors,
    ) = DUST3R.infer_cameras_and_points(image_files)
    
    if num_images == 1:
        # For single image, take only the first results
        input_imgs_numpy = [imgs[0][..., :3]]
        input_Ks_numpy = input_Ks_numpy[:1]
        input_c2ws_numpy = input_c2ws_numpy[:1]
        points = points[:1]
        point_colors = point_colors[:1]
    else:
        # Keep only the input images (remove any duplicates dust3r might create)
        if len(imgs) > num_images:
            imgs = imgs[:num_images]
            input_Ks_numpy = input_Ks_numpy[:num_images]
            input_c2ws_numpy = input_c2ws_numpy[:num_images]
            points = points[:num_images]
            point_colors = point_colors[:num_images]
        
        input_imgs_numpy = [img[..., :3] for img in imgs]
    
    # Normalize the scene (same as demo_gr.py)
    point_chunks = [p.shape[0] for p in points]
    point_indices = np.cumsum(point_chunks)[:-1]
    input_c2ws_normalized, points_normalized, _ = normalize_scene(
        input_c2ws_numpy,
        np.concatenate(points, 0),
        camera_center_method="poses",
    )
    points_normalized = np.split(points_normalized, point_indices, 0)
    
    # Scale camera and points for viewport visualization
    scene_scale = np.median(
        np.ptp(np.concatenate([input_c2ws_normalized[:, :3, 3], *points_normalized], 0), -1)
    )
    input_c2ws_numpy = input_c2ws_normalized
    input_c2ws_numpy[:, :3, 3] /= scene_scale
    points = [point / scene_scale for point in points_normalized]
    
    # Convert images to tensors and resize
    input_imgs = [
        torch.as_tensor(img / 255.0, dtype=torch.float32) for img in input_imgs_numpy
    ]
    input_Ks = torch.as_tensor(input_Ks_numpy)
    input_c2ws = torch.as_tensor(input_c2ws_numpy)
    
    new_input_imgs, new_input_Ks = [], []
    for img, K in zip(input_imgs, input_Ks):
        img = rearrange(img, "h w c -> 1 c h w")
        img, K = transform_img_and_K(img, shorter, K=K[None], size_stride=64)
        assert isinstance(K, torch.Tensor)
        K = K / K.new_tensor([img.shape[-1], img.shape[-2], 1])[:, None]
        new_input_imgs.append(img)
        new_input_Ks.append(K)
    
    input_imgs = torch.cat(new_input_imgs, 0)
    input_imgs = rearrange(input_imgs, "b c h w -> b h w c")[..., :3]
    input_Ks = torch.cat(new_input_Ks, 0)
    
    # Convert to numpy for API response
    input_imgs_numpy = (input_imgs.numpy() * 255).astype(np.uint8)
    input_Ks_numpy = input_Ks.numpy()
    
    # Convert camera matrices to the expected format for lookingglass_app.py
    camera_poses = []
    for i in range(len(input_c2ws_numpy)):
        c2w = input_c2ws_numpy[i]
        K = input_Ks_numpy[i]
        
        # Calculate FOV from intrinsics
        img_h, img_w = input_imgs_numpy[i].shape[:2]
        if img_h > img_w:
            fov = 2 * np.arctan(img_h / (2 * K[1, 1])) * 180 / np.pi
        else:
            fov = 2 * np.arctan(img_w / (2 * K[0, 0])) * 180 / np.pi
        
        camera_poses.append({
            "matrix": c2w.tolist(),
            "fov": float(fov),
            "image_path": image_files[i] if i < len(image_files) else None,
            "image_data": input_imgs_numpy[i].tolist() if i < len(input_imgs_numpy) else None
        })
    
    return {
        "width": int(input_imgs_numpy.shape[2]),
        "height": int(input_imgs_numpy.shape[1]),
        "scene_scale": float(scene_scale),
        "camera_poses": camera_poses,
        "points": [p.tolist() for p in points],
        "point_colors": [pc.tolist() for pc in point_colors],
        "num_images": len(image_files),
        # Additional data for compatibility with demo_gr.py format
        "input_imgs": input_imgs_numpy.tolist(),
        "input_Ks": input_Ks_numpy.tolist(),
        "input_c2ws": input_c2ws_numpy.tolist(),
        "input_wh": (int(input_imgs_numpy.shape[2]), int(input_imgs_numpy.shape[1]))
    }


def generate_mock_preprocessing_data(image_files: List[str], width: int, height: int):
    """Generate mock data when dust3r is not available"""
    # Enhanced camera poses and point cloud data (closer to dust3r style)
    camera_poses = []
    num_images = len(image_files)
    
    if num_images == 1:
        # Single image: place at origin with identity pose
        camera_poses.append({
            "matrix": [
                [1, 0, 0, 0],
                [0, 1, 0, 0], 
                [0, 0, 1, 0],
                [0, 0, 0, 1]
            ],
            "fov": 54.0,
            "image_path": image_files[0]
        })
        
        # Generate a few points in front of the camera for single image
        pts = np.random.randn(200, 3) * 0.8
        pts[:, 2] += 2.0  # Move points forward
        colors = np.random.rand(200, 3) * 0.8 + 0.2  # Slightly brighter colors
        points = [pts.tolist()]
        point_colors = [colors.tolist()]
        
    else:
        # Multiple images: arrange in a rough circle pattern
        for i, img_path in enumerate(image_files):
            angle = (i / num_images) * 2 * np.pi
            radius = 2.5
            height_variation = np.sin(angle * 2) * 0.3  # Small height variations
            
            # Create camera position
            pos_x = radius * np.cos(angle)
            pos_z = radius * np.sin(angle)
            pos_y = height_variation
            
            # Create rotation matrix looking toward center
            forward = np.array([-pos_x, -height_variation * 0.5, -pos_z])
            forward = forward / np.linalg.norm(forward)
            
            up = np.array([0, 1, 0])
            right = np.cross(up, forward)
            right = right / np.linalg.norm(right)
            up = np.cross(forward, right)
            
            rotation_matrix = np.column_stack([right, up, -forward])
            
            camera_matrix = np.eye(4)
            camera_matrix[:3, :3] = rotation_matrix
            camera_matrix[:3, 3] = [pos_x, pos_y, pos_z]
            
            camera_poses.append({
                "matrix": camera_matrix.tolist(),
                "fov": 54.0 + np.random.uniform(-5, 5),  # Slight FOV variation
                "image_path": img_path
            })
        
        # Generate shared point cloud for multiple images
        # Create points in the center area where cameras are looking
        center_points = np.random.randn(150, 3) * 0.6
        center_points[:, 1] *= 0.3  # Flatten vertically
        
        # Create per-camera point clouds with some shared points
        points = []
        point_colors = []
        for i in range(num_images):
            # Each camera sees the center points plus some unique points
            camera_specific_pts = np.random.randn(50, 3) * 0.4
            angle = (i / num_images) * 2 * np.pi
            # Offset camera-specific points toward camera direction
            camera_specific_pts[:, 0] += np.cos(angle) * 1.0
            camera_specific_pts[:, 2] += np.sin(angle) * 1.0
            
            # Combine center and camera-specific points
            all_pts = np.vstack([center_points, camera_specific_pts])
            
            # Generate colors (warmer for shared points, cooler for specific points)
            shared_colors = np.random.rand(len(center_points), 3) * 0.6 + 0.4
            specific_colors = np.random.rand(len(camera_specific_pts), 3) * 0.8 + 0.1
            all_colors = np.vstack([shared_colors, specific_colors])
            
            points.append(all_pts.tolist())
            point_colors.append(all_colors.tolist())
    
    return {
        "width": width,
        "height": height,
        "scene_scale": 1.0,
        "camera_poses": camera_poses,
        "points": points,
        "point_colors": point_colors,
        "num_images": len(image_files)
    }


class QuiltConfig(BaseModel):
    device_type: str = "portrait_web"
    rows: int = 6
    columns: int = 8
    width: int = 3360
    height: int = 3360


class QuiltGenerationRequest(BaseModel):
    data_path: str
    camera_poses: str  # JSON string
    quilt_config: QuiltConfig
    output_path: Optional[str] = None


@app.post("/generate-quilt")
async def generate_quilt(request: QuiltGenerationRequest):
    """
    Generate a basic quilt layout from camera trajectory.
    Creates a placeholder quilt for visualization purposes.
    """
    import json
    from PIL import Image, ImageDraw
    
    try:
        poses_data = json.loads(request.camera_poses)
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid camera poses JSON")
    
    if not os.path.exists(request.data_path):
        raise HTTPException(status_code=404, detail="Data path not found")
    
    # Create output path if not provided
    if not request.output_path:
        timestamp = int(time.time())
        request.output_path = os.path.join(OUTPUT_DIR, f"quilt_{timestamp}.png")
    
    # Ensure output directory exists
    os.makedirs(os.path.dirname(request.output_path), exist_ok=True)
    
    # Generate a basic quilt pattern
    config = request.quilt_config
    quilt_width = config.width
    quilt_height = config.height
    
    # Create a simple test pattern quilt
    quilt_image = Image.new('RGB', (quilt_width, quilt_height), color='black')
    draw = ImageDraw.Draw(quilt_image)
    
    # Calculate view dimensions
    view_width = quilt_width // config.columns
    view_height = quilt_height // config.rows
    
    # Fill each view with a different color/pattern
    for row in range(config.rows):
        for col in range(config.columns):
            x = col * view_width
            y = row * view_height
            
            # Create a simple gradient or pattern for each view
            view_index = row * config.columns + col
            hue = (view_index * 360 // (config.rows * config.columns)) % 360
            
            # Simple colored rectangle for each view
            color = tuple(int(c * 255) for c in [
                0.5 + 0.5 * np.sin(hue * np.pi / 180),
                0.5 + 0.5 * np.sin((hue + 120) * np.pi / 180),
                0.5 + 0.5 * np.sin((hue + 240) * np.pi / 180)
            ])
            
            draw.rectangle(
                [x, y, x + view_width - 1, y + view_height - 1],
                fill=color
            )
            
            # Add view number
            draw.text((x + 10, y + 10), str(view_index), fill='white')
    
    # Save the quilt image
    quilt_image.save(request.output_path)
    
    return {
        "message": "Quilt generated successfully",
        "quilt_path": request.output_path,
        "config": {
            "rows": config.rows,
            "columns": config.columns,
            "total_views": config.rows * config.columns,
            "device_type": config.device_type
        }
    }


@app.get("/generate-test-quilt")
async def generate_test_quilt(device_type: str = "portrait_web", pattern: str = "gradient"):
    """
    Generate a basic test quilt pattern.
    """
    from PIL import Image, ImageDraw
    
    # Device configurations
    device_configs = {
        "portrait_web": {"rows": 6, "columns": 8, "width": 3360, "height": 3360},
        "landscape": {"rows": 4, "columns": 8, "width": 2048, "height": 2048},
        "portrait": {"rows": 8, "columns": 6, "width": 2048, "height": 2048}
    }
    
    if device_type not in device_configs:
        device_type = "portrait_web"
    
    config = device_configs[device_type]
    
    # Create output path
    timestamp = int(time.time())
    output_path = os.path.join(OUTPUT_DIR, f"test_quilt_{device_type}_{timestamp}.png")
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # Generate test pattern
    quilt_image = Image.new('RGB', (config["width"], config["height"]), color='black')
    draw = ImageDraw.Draw(quilt_image)
    
    view_width = config["width"] // config["columns"]
    view_height = config["height"] // config["rows"]
    
    for row in range(config["rows"]):
        for col in range(config["columns"]):
            x = col * view_width
            y = row * view_height
            
            if pattern == "gradient":
                # Gradient pattern
                progress = (row * config["columns"] + col) / (config["rows"] * config["columns"])
                color = (int(255 * progress), int(255 * (1 - progress)), 128)
            else:
                # Checkerboard pattern
                if (row + col) % 2 == 0:
                    color = (255, 255, 255)
                else:
                    color = (0, 0, 0)
            
            draw.rectangle([x, y, x + view_width - 1, y + view_height - 1], fill=color)
            
            # Add view information
            view_num = row * config["columns"] + col
            draw.text((x + 5, y + 5), f"V{view_num}", fill='red')
            draw.text((x + 5, y + 20), f"R{row}C{col}", fill='red')
    
    quilt_image.save(output_path)
    
    return {
        "message": "Test quilt generated",
        "quilt_path": output_path,
        "device_type": device_type,
        "pattern": pattern,
        "config": config
    }


@app.get("/status/{job_id}", response_model=JobStatus)
async def get_job_status(job_id: str):
    """Get the status of a generation job."""
    if job_id not in job_status:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return JobStatus(**job_status[job_id])


@app.get("/jobs")
async def list_jobs():
    """List all jobs and their statuses."""
    return {"jobs": list(job_status.values())}


@app.delete("/jobs/{job_id}")
async def delete_job(job_id: str):
    """Delete a job and its associated files."""
    if job_id not in job_status:
        raise HTTPException(status_code=404, detail="Job not found")
    
    # Clean up output files
    job_info = job_status[job_id]
    if job_info.get("output_path") and job_info["output_path"].startswith("/videos/"):
        output_file = os.path.join(OUTPUT_DIR, job_info["output_path"][8:])  # Remove "/videos/" prefix
        if os.path.exists(output_file):
            os.remove(output_file)
    
    # Clean up work directory
    work_dir = f"work_dirs/demo/{job_info.get('task', '')}/job_{job_id}"
    if os.path.exists(work_dir):
        shutil.rmtree(work_dir)
    
    # Remove from status tracking
    del job_status[job_id]
    
    return {"message": f"Job {job_id} deleted successfully"}


@app.get("/download/{job_id}")
async def download_result(job_id: str):
    """Download the generated video for a completed job."""
    if job_id not in job_status:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job_info = job_status[job_id]
    
    if job_info["status"] != "completed":
        raise HTTPException(status_code=400, detail="Job is not completed yet")
    
    if not job_info.get("output_path"):
        raise HTTPException(status_code=404, detail="No output file found")
    
    if job_info["output_path"].startswith("/videos/"):
        file_path = os.path.join(OUTPUT_DIR, job_info["output_path"][8:])  # Remove "/videos/" prefix
        if os.path.exists(file_path):
            return FileResponse(
                file_path,
                media_type='video/mp4',
                filename=f"stable_virtual_camera_{job_id}.mp4"
            )
    
    raise HTTPException(status_code=404, detail="Output file not found")


# Legacy endpoint for backward compatibility
@app.post("/generate")
async def create_generation_task_legacy(
    data_path: str = Form(...),
    task: str = Form(...),
    cfg: str = Form("2.0"),
    traj_prior: Optional[str] = Form(None),
    camera_scale: Optional[float] = Form(2.0),
    num_targets: Optional[int] = Form(111),
    background_tasks: BackgroundTasks = None
):
    """
    Legacy endpoint for generating videos. 
    Use /generate/single-image or /generate/multi-image instead.
    """
    job_id = str(uuid.uuid4())
    
    # Initialize job status
    job_status[job_id] = {
        "job_id": job_id,
        "status": "pending",
        "message": f"Legacy generation queued for task: {task}",
        "created_at": time.strftime("%Y-%m-%d %H:%M:%S"),
        "completed_at": None,
        "output_path": None,
        "error": None
    }
    
    # Prepare arguments
    kwargs = {
        "cfg": cfg,
        "use_traj_prior": True,
        "chunk_strategy": "interp",
        "camera_scale": camera_scale,
    }
    
    if task == "img2trajvid_s-prob":
        kwargs.update({
            "traj_prior": traj_prior or "orbit",
            "replace_or_include_input": True,
            "num_targets": num_targets,
            "guider": "1,2"
        })
    elif task == "img2trajvid":
        kwargs.update({
            "chunk_strategy": "interp-gt",
            "chunk_strategy_first_pass": "gt-nearest"
        })
    
    # Add the background task
    background_tasks.add_task(
        generate_trajectory_video,
        job_id=job_id,
        data_path=data_path,
        task=task,
        **kwargs
    )
    
    return {
        "job_id": job_id,
        "message": f"Generation started for task: {task}",
        "status_url": f"/status/{job_id}"
    }


@app.get("/")
def read_root():
    """API documentation and available endpoints."""
    return {
        "message": "Stable Virtual Camera API",
        "version": "1.0.0",
        "description": "Generate trajectory videos from images using AI models",
        "endpoints": {
            "POST /generate/single-image": "Generate video from single image with preset trajectories",
            "POST /generate/custom-trajectory": "Generate video with custom camera trajectory design",
            "POST /generate/multi-image": "Generate video from multiple images (img2trajvid)",
            "POST /upload-single-image": "Upload single image file",
            "GET /status/{job_id}": "Get job status",
            "GET /jobs": "List all jobs",
            "DELETE /jobs/{job_id}": "Delete a job",
            "GET /download/{job_id}": "Download result",
            "GET /trajectory-priors": "List available preset trajectories with settings",
            "GET /trajectory-examples": "Get example camera poses for custom design",
            "GET /health": "Health check"
        },
        "docs": "/docs"
    }


@app.get("/trajectory-priors")
def get_trajectory_priors():
    """Get list of available trajectory priors for single image generation."""
    return {
        "trajectory_priors": [
            {
                "name": "orbit",
                "description": "Circular camera motion around the scene",
                "recommended_camera_scale": 2.0,
                "recommended_cfg": "4.0,2.0"
            },
            {
                "name": "spiral",
                "description": "Spiral camera motion with varying height",
                "recommended_camera_scale": 2.0,
                "recommended_cfg": "3.0,2.0"
            },
            {
                "name": "lemniscate",
                "description": "Figure-8 motion pattern",
                "recommended_camera_scale": 2.0,
                "recommended_cfg": "3.0,2.0"
            },
            {
                "name": "zoom-in",
                "description": "Zoom into the scene",
                "recommended_camera_scale": 2.0,
                "recommended_cfg": "3.0,2.0",
                "requires_zoom_factor": True,
                "zoom_factor_range": [0.1, 0.5]
            },
            {
                "name": "zoom-out",
                "description": "Zoom out from the scene",
                "recommended_camera_scale": 2.0,
                "recommended_cfg": "5.0,2.0",
                "requires_zoom_factor": True,
                "zoom_factor_range": [1.2, 3.0]
            },
            {
                "name": "dolly zoom-in",
                "description": "Dolly forward while zooming in (Vertigo effect)",
                "recommended_camera_scale": 10.0,
                "recommended_cfg": "4.0,2.0",
                "requires_zoom_factor": True,
                "zoom_factor_range": [0.1, 0.5]
            },
            {
                "name": "dolly zoom-out",
                "description": "Dolly backward while zooming out",
                "recommended_camera_scale": 10.0,
                "recommended_cfg": "4.0,2.0",
                "requires_zoom_factor": True,
                "zoom_factor_range": [1.2, 3.0]
            },
            {
                "name": "move-forward",
                "description": "Move camera forward into the scene",
                "recommended_camera_scale": 10.0,
                "recommended_cfg": "3.0,2.0"
            },
            {
                "name": "move-backward",
                "description": "Move camera backward from the scene",
                "recommended_camera_scale": 10.0,
                "recommended_cfg": "4.0,2.0"
            },
            {
                "name": "move-up",
                "description": "Move camera upward",
                "recommended_camera_scale": 10.0,
                "recommended_cfg": "4.0,2.0"
            },
            {
                "name": "move-down",
                "description": "Move camera downward",
                "recommended_camera_scale": 10.0,
                "recommended_cfg": "5.0,2.0"
            },
            {
                "name": "move-left",
                "description": "Move camera to the left",
                "recommended_camera_scale": 10.0,
                "recommended_cfg": "4.0,2.0"
            },
            {
                "name": "move-right",
                "description": "Move camera to the right",
                "recommended_camera_scale": 10.0,
                "recommended_cfg": "4.0,2.0"
            },
            {
                "name": "pan-left",
                "description": "Pan camera to the left",
                "recommended_camera_scale": 10.0,
                "recommended_cfg": "4.0,2.0"
            },
            {
                "name": "pan-right", 
                "description": "Pan camera to the right",
                "recommended_camera_scale": 10.0,
                "recommended_cfg": "4.0,2.0"
            },
            {
                "name": "pan-up",
                "description": "Pan camera upward",
                "recommended_camera_scale": 10.0,
                "recommended_cfg": "4.0,2.0"
            },
            {
                "name": "pan-down",
                "description": "Pan camera downward", 
                "recommended_camera_scale": 10.0,
                "recommended_cfg": "4.0,2.0"
            },
            {
                "name": "dolly-in",
                "description": "Move camera forward into the scene",
                "recommended_camera_scale": 10.0,
                "recommended_cfg": "3.0,2.0"
            },
            {
                "name": "dolly-out",
                "description": "Move camera backward from the scene",
                "recommended_camera_scale": 10.0,
                "recommended_cfg": "4.0,2.0"
            }
        ]
    }


@app.get("/trajectory-examples")
def get_trajectory_examples():
    """Get example camera poses for custom trajectory design."""
    return {
        "examples": {
            "simple_orbit": {
                "description": "Simple 4-keyframe orbit around the scene",
                "poses": [
                    {"position": [2.0, 0.0, 0.0], "rotation": [0, 90, 0], "fov": 60},
                    {"position": [0.0, 0.0, 2.0], "rotation": [0, 180, 0], "fov": 60},
                    {"position": [-2.0, 0.0, 0.0], "rotation": [0, 270, 0], "fov": 60},
                    {"position": [0.0, 0.0, -2.0], "rotation": [0, 0, 0], "fov": 60}
                ]
            },
            "zoom_in": {
                "description": "Zoom in while moving forward",
                "poses": [
                    {"position": [0.0, 0.0, 5.0], "rotation": [0, 0, 0], "fov": 60},
                    {"position": [0.0, 0.0, 3.0], "rotation": [0, 0, 0], "fov": 45},
                    {"position": [0.0, 0.0, 1.0], "rotation": [0, 0, 0], "fov": 30}
                ]
            },
            "spiral_up": {
                "description": "Spiral upward motion",
                "poses": [
                    {"position": [2.0, 0.0, 0.0], "rotation": [0, 90, 0], "fov": 60},
                    {"position": [0.0, 1.0, 2.0], "rotation": [-15, 180, 0], "fov": 60},
                    {"position": [-2.0, 2.0, 0.0], "rotation": [-30, 270, 0], "fov": 60},
                    {"position": [0.0, 3.0, -2.0], "rotation": [-45, 0, 0], "fov": 60}
                ]
            },
            "dolly_zoom": {
                "description": "Dolly zoom (Vertigo effect) - move back while zooming in",
                "poses": [
                    {"position": [0.0, 0.0, 1.0], "rotation": [0, 0, 0], "fov": 30},
                    {"position": [0.0, 0.0, 2.0], "rotation": [0, 0, 0], "fov": 45},
                    {"position": [0.0, 0.0, 3.0], "rotation": [0, 0, 0], "fov": 60}
                ]
            }
        },
        "coordinate_system": {
            "description": "Camera coordinate system explanation",
            "position": "3D coordinates [x, y, z] where +Y is up, +Z is forward from camera",
            "rotation": "Euler angles [pitch, yaw, roll] in degrees",
            "fov": "Field of view in degrees (typically 30-90)"
        },
        "tips": [
            "Start with fewer keyframes (3-5) for smoother motion",
            "Ensure smooth transitions between camera poses",
            "Use consistent FOV unless you want zoom effects",
            "Test with simple trajectories first before complex ones",
            "Camera scale parameter affects the overall motion magnitude"
        ]
    }


@app.post("/generate/batch-quilts", response_model=Dict[str, str])
async def generate_batch_quilts(
    data_path: str = Form(..., description="Path to the preprocessed data"),
    quilt_units: str = Form(..., description="JSON string of quilt units with camera poses"),
    cfg: str = Form("4.0,2.0", description="Configuration values"),
    camera_scale: float = Form(2.0, description="Camera scale factor"),
    seed: int = Form(23, description="Random seed"),
    H: int = Form(576, description="Height of output images"),
    W: int = Form(576, description="Width of output images"),
    L_short: int = Form(576, description="Resize shortest side"),
    num_steps: int = Form(50, description="Number of denoising steps"),
    video_save_fps: float = Form(30.0, description="Video save FPS"),
    background_tasks: BackgroundTasks = None
):
    """
    Generate multiple quilt images in a single batch request.
    
    This endpoint processes multiple quilt units simultaneously, reducing network overhead
    and enabling better resource utilization compared to individual requests.
    
    quilt_units format:
    [
        {
            "unit_id": 0,
            "camera_poses": [{"position": [x,y,z], "rotation": [rx,ry,rz], "fov": degrees}, ...],
            "rows": 5, "columns": 8, "total_views": 40
        },
        ...
    ]
    """
    batch_job_id = str(uuid.uuid4())
    
    # Parse quilt units
    try:
        import json
        units_data = json.loads(quilt_units)
        if not isinstance(units_data, list):
            raise ValueError("quilt_units must be a list")
    except (json.JSONDecodeError, ValueError) as e:
        raise HTTPException(status_code=400, detail=f"Invalid quilt_units JSON: {e}")
    
    # Initialize batch job status
    job_status[batch_job_id] = {
        "job_id": batch_job_id,
        "status": "pending",
        "message": f"Batch quilt generation queued for {len(units_data)} units",
        "created_at": time.strftime("%Y-%m-%d %H:%M:%S"),
        "completed_at": None,
        "output_path": None,
        "error": None,
        "batch_type": "quilts",
        "total_units": len(units_data),
        "completed_units": 0,
        "unit_results": {}
    }
    
    # Add the batch processing task
    background_tasks.add_task(
        process_batch_quilts,
        batch_job_id=batch_job_id,
        data_path=data_path,
        units_data=units_data,
        cfg=cfg,
        camera_scale=camera_scale,
        seed=seed,
        H=H,
        W=W,
        L_short=L_short,
        num_steps=num_steps,
        video_save_fps=video_save_fps
    )
    
    return {
        "job_id": batch_job_id,
        "message": f"Batch quilt generation started for {len(units_data)} units",
        "status_url": f"/status/{batch_job_id}",
        "total_units": str(len(units_data))
    }


# Memory management utilities
def cleanup_gpu_memory():
    """Clean up GPU memory to prevent OOM issues"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()
    gc.collect()

# Thread pool for CPU-bound operations
_thread_pool_executor = ThreadPoolExecutor(max_workers=4)

async def process_single_unit(
    unit_data: Dict,
    batch_job_id: str,
    data_path: str,
    unit_index: int,
    total_units: int,
    **kwargs
) -> Tuple[int, Dict]:
    """Process a single quilt unit with proper error handling and memory management"""
    unit_id = unit_data.get("unit_id", unit_index)
    
    try:
        # Update status
        job_status[batch_job_id]["message"] = f"Processing unit {unit_index + 1}/{total_units}"
        job_status[batch_job_id]["completed_units"] = unit_index
        
        camera_poses = unit_data.get("camera_poses", [])
        total_views = unit_data.get("total_views", len(camera_poses))
        
        print(f"Processing unit {unit_id} ({unit_index + 1}/{total_units}) with {len(camera_poses)} camera poses")
        
        # Set up save directory for this unit
        save_subdir = f"batch_{batch_job_id}/unit_{unit_id}"
        
        # Prepare arguments for demo function
        unit_kwargs = kwargs.copy()
        unit_kwargs["T"] = min(total_views, 40)  # Apply SEVA limits
        
        # Convert parameters to proper types
        _convert_unit_parameters(unit_kwargs)
        
        # Run SEVA processing in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(
            _thread_pool_executor,
            lambda: demo_main(
                data_path=data_path,
                task="img2trajvid_s-prob",
                save_subdir=save_subdir,
                custom_camera_poses=camera_poses,
                **unit_kwargs
            )
        )
        
        # Clean up GPU memory after processing
        cleanup_gpu_memory()
        
        # Find generated samples and create quilt
        # For batch processing, frames are distributed across multiple chunks
        batch_work_dir = f"work_dirs/demo/img2trajvid_s-prob/batch_{batch_job_id}"
        unit_work_dir = os.path.join(batch_work_dir, f"unit_{unit_id}")
        
        print(f"🔍 Looking for unit frames in: {unit_work_dir}")
        print(f"🔍 Unit directory exists: {os.path.exists(unit_work_dir)}")
        
        frame_files = []
        if os.path.exists(unit_work_dir):
            # Collect frames from all chunks (0000, 0001, 0002, 0003, etc.)
            chunk_dirs = sorted([d for d in os.listdir(unit_work_dir) 
                               if os.path.isdir(os.path.join(unit_work_dir, d)) and d.isdigit()])
            print(f"🔍 Found {len(chunk_dirs)} chunk directories: {chunk_dirs}")
            
            for chunk_dir in chunk_dirs:
                chunk_path = os.path.join(unit_work_dir, chunk_dir)
                samples_dir = os.path.join(chunk_path, "samples-rgb")
                if os.path.exists(samples_dir):
                    chunk_frames = sorted(glob.glob(os.path.join(samples_dir, "*.png")))
                    frame_files.extend(chunk_frames)
                    print(f"🔍 Chunk {chunk_dir}: found {len(chunk_frames)} frames")
            
            print(f"🔍 Total frames collected: {len(frame_files)}")
            if len(frame_files) > 0:
                print(f"🔍 First few frames: {frame_files[:3]}")
        else:
            print(f"🔍 Unit directory not found, checking batch dir contents:")
            if os.path.exists(batch_work_dir):
                batch_dir_contents = os.listdir(batch_work_dir)
                print(f"🔍 Batch dir contents: {batch_dir_contents}")
        
        print(f"🔍 QUILT_AVAILABLE: {QUILT_AVAILABLE}")
        print(f"🔍 Frame files collected: {len(frame_files)}")
        
        if frame_files and QUILT_AVAILABLE:
            print(f"✅ Starting quilt generation for unit {unit_id} with {len(frame_files)} frames")
            
            # Calculate quilt dimensions based on number of camera poses
            # For smaller numbers of views, use a more appropriate layout
            num_poses = len(camera_poses)
            
            # Use device defaults but adjust if we have fewer poses
            device_rows = unit_data.get("rows", 6)
            device_cols = unit_data.get("columns", 8)
            device_total = device_rows * device_cols
            
            if num_poses <= device_total:
                # Use device layout as-is
                quilt_rows = device_rows
                quilt_cols = device_cols
            else:
                # Calculate optimal layout for the number of poses
                # Try to keep aspect ratio close to device default
                target_ratio = device_cols / device_rows
                # Find factors of num_poses that give ratio closest to target
                best_rows, best_cols = device_rows, device_cols
                best_diff = abs(device_total - num_poses)
                
                for rows in range(1, int(np.sqrt(num_poses)) + 5):
                    cols = (num_poses + rows - 1) // rows  # Ceiling division
                    if rows * cols >= num_poses:
                        diff = abs(rows * cols - num_poses)
                        ratio = cols / rows
                        ratio_diff = abs(ratio - target_ratio)
                        # Prefer layouts closer to target views and ratio
                        if diff < best_diff or (diff == best_diff and ratio_diff < abs(best_cols/best_rows - target_ratio)):
                            best_rows, best_cols = rows, cols
                            best_diff = diff
                
                quilt_rows, quilt_cols = best_rows, best_cols
            
            print(f"Using quilt layout: {quilt_rows}x{quilt_cols} = {quilt_rows * quilt_cols} for {num_poses} camera poses")
            
            quilt_path = await generate_quilt_from_frames(
                frame_files, 
                unit_id, 
                batch_job_id,
                unit_data.get("device_type", "portrait"),
                quilt_rows,
                quilt_cols,
                num_poses  # Pass the exact number of camera poses
            )
            
            if quilt_path:
                result = {
                    "success": True,
                    "output_path": quilt_path,
                    "quilt_image_path": quilt_path,
                    "frames_processed": len(camera_poses),
                    "frame_count": len(frame_files)
                }
                print(f"✅ Unit {unit_id} completed - generated quilt with {len(frame_files)} frames")
                return unit_id, result
            else:
                raise Exception("Quilt generation failed")
        else:
            error_msg = "No frames generated or quilt components not available"
            if not frame_files:
                error_msg = f"No frame files found in {unit_work_dir}"
            elif not QUILT_AVAILABLE:
                error_msg = "Quilt components not available"
            
            raise Exception(error_msg)
            
    except Exception as e:
        print(f"❌ Unit {unit_id} failed: {e}")
        return unit_id, {
            "success": False,
            "error": str(e),
            "frames_processed": 0,
            "frames_found": len(frame_files) if 'frame_files' in locals() else 0,
            "quilt_available": QUILT_AVAILABLE
        }

def _convert_unit_parameters(unit_kwargs: Dict) -> None:
    """Convert string parameters to proper types for SEVA processing"""
    # Convert cfg to the expected format
    if isinstance(unit_kwargs.get("cfg"), list):
        unit_kwargs["cfg"] = ",".join(map(str, unit_kwargs["cfg"]))
    elif isinstance(unit_kwargs.get("cfg"), str) and "," in unit_kwargs["cfg"]:
        try:
            cfg_values = unit_kwargs["cfg"].split(",")
            unit_kwargs["cfg"] = float(cfg_values[0])
        except (ValueError, TypeError):
            unit_kwargs["cfg"] = 2.0
    
    # Convert camera_scale to float
    if "camera_scale" in unit_kwargs:
        try:
            unit_kwargs["camera_scale"] = float(unit_kwargs["camera_scale"])
        except (ValueError, TypeError):
            unit_kwargs["camera_scale"] = 2.0
    
    # Convert numeric parameters
    numeric_params = ['H', 'W', 'T', 'seed', 'num_steps', 'L_short']
    for param in numeric_params:
        if param in unit_kwargs:
            try:
                unit_kwargs[param] = int(unit_kwargs[param])
            except (ValueError, TypeError):
                pass
    
    # Handle video_save_fps as float
    if 'video_save_fps' in unit_kwargs:
        try:
            unit_kwargs['video_save_fps'] = float(unit_kwargs['video_save_fps'])
        except (ValueError, TypeError):
            unit_kwargs['video_save_fps'] = 30.0

async def process_batch_quilts(
    batch_job_id: str,
    data_path: str,
    units_data: List[Dict],
    max_concurrent: int = 2,  # Limit concurrent GPU operations
    **kwargs
):
    """Process multiple quilt units in parallel with controlled concurrency"""
    try:
        job_status[batch_job_id]["status"] = "running"
        job_status[batch_job_id]["message"] = f"Processing {len(units_data)} quilt units in parallel batch..."
        
        print(f"--- Starting Parallel Batch Quilt Generation for job {batch_job_id} ---")
        print(f"Total units: {len(units_data)}, Max concurrent: {max_concurrent}")
        print(f"Arguments: {kwargs}")
        
        # Create semaphore to limit concurrent GPU operations
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_unit_with_semaphore(unit_data: Dict, unit_index: int) -> Tuple[int, Dict]:
            async with semaphore:
                return await process_single_unit(
                    unit_data, batch_job_id, data_path, unit_index, len(units_data), **kwargs
                )
        
        # Process all units concurrently with semaphore control
        tasks = [
            process_unit_with_semaphore(unit_data, i) 
            for i, unit_data in enumerate(units_data)
        ]
        
        # Gather results as they complete
        completed_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        results = {}
        for result in completed_results:
            if isinstance(result, Exception):
                print(f"❌ Unit processing failed with exception: {result}")
                continue
            
            unit_id, unit_result = result
            results[unit_id] = unit_result
        
        # Final GPU cleanup
        cleanup_gpu_memory()
        
        # Update final job status
        successful_units = len([r for r in results.values() if r["success"]])
        job_status[batch_job_id]["status"] = "completed"
        job_status[batch_job_id]["message"] = f"Batch completed: {successful_units}/{len(units_data)} units successful"
        job_status[batch_job_id]["completed_units"] = len(units_data)
        job_status[batch_job_id]["unit_results"] = results
        job_status[batch_job_id]["completed_at"] = time.strftime("%Y-%m-%d %H:%M:%S")
        
        print(f"--- Finished Batch Quilt Generation for job {batch_job_id} ---")
        print(f"Results: {successful_units}/{len(units_data)} units successful")
        
    except Exception as e:
        job_status[batch_job_id]["status"] = "failed"
        job_status[batch_job_id]["message"] = f"Batch processing failed: {str(e)}"
        job_status[batch_job_id]["error"] = str(e)
        job_status[batch_job_id]["completed_at"] = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"--- Error in batch job {batch_job_id}: {e} ---")


async def generate_quilt_from_frames(
    frame_files: List[str],
    unit_id: int,
    batch_job_id: str,
    device_type: str = "portrait",
    rows: int = 6,
    columns: int = 8
) -> Optional[str]:
    """
    Generate a Looking Glass quilt image from SEVA-generated frame files.
    
    Args:
        frame_files: List of paths to individual frame images
        unit_id: Unit identifier
        batch_job_id: Batch job identifier
        device_type: Looking Glass device type
        rows: Quilt rows (overrides device config if provided)
        columns: Quilt columns (overrides device config if provided)
        
    Returns:
        Path to generated quilt image or None if failed
    """
    if not QUILT_AVAILABLE:
        print("Quilt generation components not available")
        return None
        
    try:
        # Get device configuration
        config = get_device_config(device_type)
        
        # Override rows/columns if provided
        if rows != config.quilt.rows or columns != config.quilt.columns:
            print(f"Overriding device quilt config: {config.quilt.rows}x{config.quilt.columns} -> {rows}x{columns}")
            # Directly modify the rows and columns (total_views is computed automatically)
            config.quilt.rows = rows
            config.quilt.columns = columns
            # Note: total_views, view_width, view_height are computed properties
        
        required_views = config.quilt.total_views  # This will be computed from rows * columns
        print(f"Creating {device_type} quilt: {rows}x{columns} = {required_views} views from {len(frame_files)} frames")
        
        # SEVA generates frames in chunks where each chunk covers part of the trajectory
        # We need to select frames that correspond to the camera poses from viser
        # The frames are distributed across chunks, and we need to sample evenly
        
        # Collect all target frames (excluding input frame 000)
        all_target_frames = []
        chunk_frame_map = {}  # Map chunk_idx -> list of frame paths
        
        for frame_path in frame_files:
            # Extract chunk and frame index from path like .../0001/samples-rgb/023.png
            parts = frame_path.split('/')
            for i, part in enumerate(parts):
                if part.isdigit() and len(part) == 4:  # Chunk directory like "0001"
                    chunk_idx = int(part)
                    if i + 2 < len(parts) and parts[i + 1] == "samples-rgb":
                        frame_name = parts[i + 2]
                        if frame_name.endswith('.png'):
                            frame_idx = int(frame_name.split('.')[0])
                            if frame_idx > 0:  # Skip frame 000 (input)
                                if chunk_idx not in chunk_frame_map:
                                    chunk_frame_map[chunk_idx] = []
                                chunk_frame_map[chunk_idx].append((frame_idx, frame_path))
                    break
        
        # Sort frames within each chunk
        for chunk_idx in chunk_frame_map:
            chunk_frame_map[chunk_idx].sort(key=lambda x: x[0])  # Sort by frame index
            all_target_frames.extend([path for _, path in chunk_frame_map[chunk_idx]])
        
        print(f"Collected {len(all_target_frames)} target frames from {len(chunk_frame_map)} chunks")
        print(f"Chunk distribution: {[(k, len(v)) for k, v in chunk_frame_map.items()]}")
        
        # Sample frames to match the required number of views
        # We want to sample evenly across the generated trajectory
        if len(all_target_frames) >= required_views:
            # Sample evenly across all available frames
            indices = np.linspace(0, len(all_target_frames) - 1, required_views, dtype=int)
            frames_to_use = [all_target_frames[i] for i in indices]
        else:
            # Use all available frames and pad if necessary
            frames_to_use = all_target_frames
        
        print(f"Selected {len(frames_to_use)} frames for quilt (required: {required_views})")
        
        # Load frame images
        view_images = []
        for i, frame_path in enumerate(frames_to_use):
            try:
                img = Image.open(frame_path)
                view_images.append(img.convert('RGB'))
                if i == 0:
                    print(f"First frame size: {img.size}")
            except Exception as e:
                print(f"Error loading frame {frame_path}: {e}")
                continue
        
        # Pad with last frame if we don't have enough
        while len(view_images) < required_views:
            if view_images:
                view_images.append(view_images[-1].copy())
            else:
                # Create placeholder if no frames loaded
                placeholder = Image.new('RGB', (512, 384), color=(128, 128, 128))
                view_images.append(placeholder)
        
        # Truncate if we have too many
        if len(view_images) > required_views:
            view_images = view_images[:required_views]
        
        print(f"Using {len(view_images)} views for quilt generation")
        
        # Create quilt generator
        generator = QuiltImageGenerator(config)
        
        # Generate quilt
        quilt_image = generator.arrange_views_into_quilt(view_images)
        
        # Save quilt image
        output_filename = f"quilt_unit_{unit_id}_{batch_job_id}.png"
        output_path = os.path.join(OUTPUT_DIR, output_filename)
        quilt_image.save(output_path, 'PNG')
        
        # Clean up memory after quilt generation
        del view_images, quilt_image
        cleanup_gpu_memory()
        
        # Return web-accessible path
        web_path = f"/videos/{output_filename}"
        print(f"Saved quilt image: {output_path} (web: {web_path})")
        print(f"Quilt contains {len(view_images)} target frame views (input frames excluded)")
        
        return web_path
        
    except Exception as e:
        print(f"Error generating quilt from frames: {e}")
        import traceback
        traceback.print_exc()
        return None


@app.get("/health")
def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "active_jobs": len([j for j in job_status.values() if j["status"] in ["pending", "running"]])
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=4013)