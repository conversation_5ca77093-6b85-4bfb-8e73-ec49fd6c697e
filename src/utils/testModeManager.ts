/**
 * Test Mode Manager
 * Manages API usage restrictions when testing is enabled
 */

import { config } from '@/config/client';
import { createLogger } from '@/utils/logger';
import { testProxyServerConnectivity, proxyServerStatus } from '@/config/endpoints';

const logger = createLogger('TestModeManager');

export class TestModeManager {
  private static instance: TestModeManager;
  private apiCallCount = 0;

  public static getInstance(): TestModeManager {
    if (!TestModeManager.instance) {
      TestModeManager.instance = new TestModeManager();
    }
    return TestModeManager.instance;
  }

  /**
   * Check if we're currently in test mode
   */
  public isTestModeEnabled(): boolean {
    return config.testMode.enabled;
  }

  /**
   * Check if API usage should be restricted
   */
  public shouldRestrictApiUsage(): boolean {
    return config.testMode.enabled && config.testMode.restrictApiUsage;
  }

  /**
   * Check if we've exceeded the maximum number of test API calls
   */
  public hasExceededTestApiLimit(): boolean {
    if (!this.shouldRestrictApiUsage()) {
      return false;
    }
    return this.apiCallCount >= config.testMode.maxTestApiCalls;
  }

  /**
   * Increment the API call counter
   */
  public incrementApiCallCount(): void {
    if (this.shouldRestrictApiUsage()) {
      this.apiCallCount++;
      config.testMode.testApiCallCount = this.apiCallCount;

      logger.debug(`Test API call count: ${this.apiCallCount}/${config.testMode.maxTestApiCalls}`);

      if (this.hasExceededTestApiLimit()) {
        logger.warn(`🚨 Test API limit exceeded: ${this.apiCallCount}/${config.testMode.maxTestApiCalls}`);
      }
    }
  }

  /**
   * Get current API call count
   */
  public getApiCallCount(): number {
    return this.apiCallCount;
  }

  /**
   * Reset API call count (useful for test cleanup)
   */
  public resetApiCallCount(): void {
    this.apiCallCount = 0;
    config.testMode.testApiCallCount = 0;
    logger.debug('Test API call count reset');
  }

  /**
   * Get mock response for API calls during testing
   */
  public getMockResponse(apiType: string, endpoint: string): any {
    logger.info(`🎭 Returning mock response for ${apiType}:${endpoint} (test mode active)`);

    // Return appropriate mock responses based on API type
    switch (apiType.toLowerCase()) {
      case 'llm':
      case 'vllm':
      case 'aliyun':
        return {
          choices: [{
            message: {
              content: `Mock response from ${apiType} API for testing`,
              role: 'assistant'
            }
          }],
          model: 'test-model',
          usage: { total_tokens: 10 }
        };

      case 'tts':
      case 'sparktts':
        return {
          audio: 'mock-audio-data',
          format: 'pcm',
          sample_rate: 16000
        };

      case 'asr':
      case 'speechrecog':
        return {
          text: 'Mock transcription for testing',
          confidence: 0.95
        };

      case 'textto3d':
      case 'anyto3d':
        return {
          model_url: 'mock-3d-model.glb',
          status: 'completed'
        };

      default:
        return {
          success: true,
          data: `Mock response for ${apiType}`,
          timestamp: new Date().toISOString()
        };
    }
  }

  /**
   * Check server connectivity and provide recommendations
   */
  public async checkServerConnectivity(): Promise<{
    isHealthy: boolean;
    recommendation?: string;
  }> {
    const isHealthy = await testProxyServerConnectivity();

    if (!isHealthy) {
      return {
        isHealthy: false,
        recommendation: 'Start the download server with: npm run server'
      };
    }

    return { isHealthy: true };
  }

  /**
   * Get server health status without async call
   */
  public getServerStatus(): 'unknown' | 'healthy' | 'unhealthy' {
    return proxyServerStatus;
  }

  /**
   * Validate API call and return whether it should proceed
   */
  public validateApiCall(apiType: string, endpoint: string): {
    allowed: boolean;
    reason?: string;
    mockResponse?: any;
  } {
    if (!this.isTestModeEnabled()) {
      return { allowed: true };
    }

    // If API usage should be restricted
    if (this.shouldRestrictApiUsage()) {
      // Check if we've exceeded the limit
      if (this.hasExceededTestApiLimit()) {
        return {
          allowed: false,
          reason: `Test API limit exceeded (${this.apiCallCount}/${config.testMode.maxTestApiCalls})`,
          mockResponse: this.getMockResponse(apiType, endpoint)
        };
      }

      // If mock endpoints are enabled, return mock response
      if (config.testMode.mockEndpoints) {
        return {
          allowed: false,
          reason: 'Mock endpoints enabled for testing',
          mockResponse: this.getMockResponse(apiType, endpoint)
        };
      }
    }

    // Increment counter and allow the call
    this.incrementApiCallCount();
    return { allowed: true };
  }

  /**
   * Log test mode status
   */
  public logTestModeStatus(): void {
    if (this.isTestModeEnabled()) {
      logger.info('🧪 Test mode is ENABLED', {
        restrictApiUsage: config.testMode.restrictApiUsage,
        mockEndpoints: config.testMode.mockEndpoints,
        maxApiCalls: config.testMode.maxTestApiCalls,
        currentApiCalls: this.apiCallCount
      });
    } else {
      logger.debug('Test mode is disabled');
    }
  }
}

// Export singleton instance
export const testModeManager = TestModeManager.getInstance();