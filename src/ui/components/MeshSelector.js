import { config } from '../../config/client.ts';
import { ASSETS } from '../../../app/viewer/viewerConfig.js';
import { fileExists, fetchFile } from '@/utils/cache.js';
import { detectAvatarMesh } from '../../viewer/detection/AvatarMeshDetector.ts';
import { buildDownloadServerUrl, onPortChange } from '../../utils/portManager.js';
import { createLogger } from '@/utils/logger';

// Create a logger for this module
const logger = createLogger('MeshSelector');

export class MeshSelector {
    constructor(app, meshPath, meshFormats = ['.glb', '.fbx'], enableDelete = false) {
        this.app = app;
        this.meshPath = meshPath;
        this.meshFormats = Array.isArray(meshFormats) ? meshFormats : [meshFormats];
        // Create regex pattern from mesh formats
        const formatPattern = this.meshFormats
            .map(format => format.replace('.', '\\.')) // Escape dots
            .join('|');
        this.extensionRegex = new RegExp(`(${formatPattern})$`, 'i');
        this.enableDelete = enableDelete;
        this.container = null;
        this.select = null;
        this.loadingIndicator = null;
        this.isLoading = false;
        this.isVisible = true;
        this.meshes = [];
        this.selectedMesh = null;

        // Set up port change listener to refetch meshes when server port changes
        this.portChangeUnsubscribe = onPortChange((newPort) => {
            logger.debug(`Server port changed to ${newPort}, refetching meshes`);
            this.fetchMeshes();
        });

        this.init();
    }

    init() {
        // Create container
        this.container = document.createElement('div');
        this.container.className = 'mesh-selector';

        // Create select element
        this.select = document.createElement('select');
        this.select.className = 'mesh-select';

        // Add default option
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = 'Select a mesh...';
        this.select.appendChild(defaultOption);

        // Create loading indicator
        this.loadingIndicator = document.createElement('span');
        this.loadingIndicator.className = 'loading-indicator';
        this.loadingIndicator.textContent = 'Loading...';
        this.loadingIndicator.style.display = 'none';

        // Create button container
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'button-container';

        // Create delete button if enabled
        if (this.enableDelete) {
            this.deleteButton = document.createElement('button');
            this.deleteButton.className = 'delete-button';
            this.deleteButton.textContent = '🗑️';
            this.deleteButton.title = 'Delete selected mesh and its cache';
            this.deleteButton.onclick = () => this.handleDelete();
            buttonContainer.appendChild(this.deleteButton);
        }

        // Add Make Avatar button
        this.makeAvatarButton = document.createElement('button');
        this.makeAvatarButton.className = 'make-avatar-button';
        this.makeAvatarButton.textContent = '🧑';
        this.makeAvatarButton.title = 'Transform selected mesh to talking avatar';
        this.makeAvatarButton.onclick = () => this.handleMakeAvatar();
        buttonContainer.appendChild(this.makeAvatarButton);

        // Add event listener
        this.select.addEventListener('change', this.handleMeshChange.bind(this));

        // Add elements to container
        this.container.appendChild(this.select);
        this.container.appendChild(buttonContainer);
        this.container.appendChild(this.loadingIndicator);

        // Fetch meshes
        this.fetchMeshes();

        // Add to viewer container
        if (this.app.container) {
            this.app.container.appendChild(this.container);
        } else {
            logger.error('Viewer container not found');
        }
    }

    async handleMakeAvatar() {
        const selectedMesh = this.select.value;

        // Check if we're already in TalkingHead mode
        if (this.app.talkingAvatar && this.app.talkingAvatar.inTalkingHeadMode) {
            logger.info('Exiting TalkingHead mode');
            try {
                // This will explicitly stop the TalkingHead before exiting
                this.app.talkingAvatar.exitTalkingHeadMode();
            } catch (error) {
                logger.error('Error exiting TalkingHead mode:', error);
                // Force reset of TalkingHead mode state if there's an error
                if (this.app.talkingAvatar) {
                    this.app.talkingAvatar.inTalkingHeadMode = false;
                    this.app.talkingAvatar.talkingHead = null;
                    this.app.talkingAvatar.animator = null;
                }
            }

            // Update button state to show it can be used to transform again
            this.makeAvatarButton.disabled = false;
            this.makeAvatarButton.classList.add('available');
            this.makeAvatarButton.title = 'Transform selected mesh to talking avatar';

            // Re-enable selection dropdown
            this.select.disabled = false;

            return;
        }

        // Original code for transforming to talking avatar
        if (!selectedMesh) {
            alert('Please select a mesh first');
            return;
        }

        try {
            this.setLoading(true);

            // Check if app has transformToTalkingHead method
            if (typeof this.app.transformToTalkingHead === 'function') {
                // Pass the selected mesh ID to the transformation function
                // This allows fetching the correct avatar URL if available
                const success = await this.app.transformToTalkingHead(null, selectedMesh);

                if (success) {
                    // Reset the mesh selector as the original mesh has been transformed
                    this.select.value = '';

                    // Update UI to reflect the transformation
                    this.makeAvatarButton.disabled = false; // Keep enabled to allow exiting
                    this.makeAvatarButton.classList.add('available');
                    this.makeAvatarButton.title = 'Exit talking head mode';

                    // Show success notification through the UI if available
                    if (this.app.uiSettings) {
                        this.app.uiSettings.showNotification(
                            'Successfully transformed to talking avatar!',
                            false
                        );
                    }
                }
            } else {
                console.error('transformToTalkingHead method not available in app');
                alert('Avatar transformation feature is not available');
            }
        } catch (error) {
            console.error('Failed to transform mesh to avatar:', error);
            alert('Failed to transform mesh to avatar: ' + (error.message || 'Unknown error'));
        } finally {
            this.setLoading(false);
        }
    }

    // Add a method to update UI state after transformation
    updateAfterTransformation() {
        // Reset selection
        this.select.value = '';

        // Update button state
        if (this.makeAvatarButton) {
            this.makeAvatarButton.disabled = true;
            this.makeAvatarButton.classList.remove('available');
            this.makeAvatarButton.title = 'Select a new mesh to transform';
        }

        // Disable delete button if present
        if (this.deleteButton) {
            this.deleteButton.disabled = true;
        }
    }

    async handleDelete(meshName = null) {
        const selectedMesh = meshName || this.select.value;
        if (!selectedMesh) return;

        // Extract base name without any extension using the dynamic regex
        const baseName = selectedMesh.replace(this.extensionRegex, '');

        if (!confirm(`Are you sure you want to delete ${baseName} and all its related files (mesh, images, videos, seeds, etc.)?`)) {
            return;
        }

        try {
            this.setLoading(true);

            // Use the server's delete endpoint which now handles all associated files
            // including avatar URL seed files that share the same base name
            const serverUrl = buildDownloadServerUrl(`/delete/${baseName}`);
            const response = await fetch(serverUrl, {
                method: 'DELETE'
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || 'Failed to delete files');
            }

            // Remove from select
            const option = this.select.querySelector(`option[value="${selectedMesh}"]`);
            if (option) {
                option.remove();
            }
            this.select.value = '';

            // Clean up mesh from the scene
            if (this.app.scene) {
                this.app.deleteMesh();
            }

            console.log(`Successfully deleted ${baseName} and all related files`);
        } catch (error) {
            console.error('Failed to delete files:', error);
            alert('Failed to delete the files');
        } finally {
            this.setLoading(false);
        }
    }

    // Removed unused _sanitizeFileId method

    async fetchMeshes() {
        this.setLoading(true);
        try {
            const serverUrl = buildDownloadServerUrl(this.meshPath);
            console.log(`[MeshSelector] Fetching meshes from: ${serverUrl}`);
            const response = await fetch(serverUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const meshList = await response.json();

            // Debug: Log the raw mesh list from server
            console.log('Raw mesh list from server:', meshList);

            // Clear existing options (except default)
            while (this.select.options.length > 1) {
                this.select.remove(1);
            }

            // Removed unused debug code

            // Filter meshes by supported formats and add file stats
            const filteredMeshes = meshList.filter(mesh => {
                const isSupported = this.meshFormats.some(format => {
                    const matches = mesh.toLowerCase().endsWith(format.toLowerCase());
                    return matches;
                });
                if (!isSupported) {
                    console.log(`Mesh rejected: ${mesh} - doesn't match any format in:`, this.meshFormats);
                }
                return isSupported;
            });

            // Debug: Log filtered results
            console.log('Filtered meshes:', filteredMeshes);

            // Get file stats for sorting by modification time
            const meshesWithStats = await Promise.all(
                filteredMeshes.map(async (mesh) => {
                    try {
                        // Try to get file stats from the server
                        const statsUrl = buildDownloadServerUrl(`${this.meshPath}/${mesh}?stats=true`);
                        const statsResponse = await fetch(statsUrl, { method: 'HEAD' });

                        let modificationTime = 0;
                        if (statsResponse.ok) {
                            // Try to get modification time from Last-Modified header
                            const lastModified = statsResponse.headers.get('Last-Modified');
                            if (lastModified) {
                                modificationTime = new Date(lastModified).getTime();
                            }
                        }

                        // If we can't get modification time, use current time minus index for stable ordering
                        if (!modificationTime) {
                            modificationTime = Date.now() - filteredMeshes.indexOf(mesh) * 1000;
                        }

                        return {
                            filename: mesh,
                            modificationTime: modificationTime
                        };
                    } catch (error) {
                        console.warn(`Failed to get stats for ${mesh}:`, error);
                        // Fallback to current time minus index for stable ordering
                        return {
                            filename: mesh,
                            modificationTime: Date.now() - filteredMeshes.indexOf(mesh) * 1000
                        };
                    }
                })
            );

            // Sort by modification time (newest first)
            meshesWithStats.sort((a, b) => b.modificationTime - a.modificationTime);

            // Store the mesh list for UI synchronization
            this.meshes = meshesWithStats.map(meshStat => {
                const mesh = meshStat.filename;
                // Get base name without extension using the dynamic regex
                const baseName = mesh.replace(this.extensionRegex, '');
                return {
                    id: mesh,
                    name: baseName
                };
            });

            meshesWithStats.forEach(meshStat => {
                const mesh = meshStat.filename;
                const option = document.createElement('option');
                option.value = mesh;

                // Get base name without extension using the dynamic regex
                const baseName = mesh.replace(this.extensionRegex, '');

                // Trim the display text if too long (30 chars)
                const maxDisplayLength = 30;
                const displayText = baseName.length > maxDisplayLength
                    ? baseName.substring(0, maxDisplayLength - 3) + '...'
                    : baseName;

                option.textContent = displayText;
                option.title = baseName;  // Show full name on hover
                option.dataset.mesh = mesh;

                // Add click handler for the delete icon
                option.addEventListener('click', async (e) => {
                    if (e.offsetX > option.offsetWidth - 30) {  // Click on delete icon area
                        e.preventDefault();
                        e.stopPropagation();
                        await this.handleDelete(mesh);
                    }
                });

                this.select.appendChild(option);
            });

        } catch (error) {
            console.error('Failed to setup mesh selector:', error);
        } finally {
            this.setLoading(false);
        }
    }

    async handleMeshChange(event) {
        const meshPath = event.target.value;
        if (!meshPath) {
            this.selectedMesh = null;
            return;
        }

        // Store the selected mesh for UI synchronization
        this.selectedMesh = meshPath;

        try {
            // Check if we need to exit TalkingHead mode before loading new mesh
            if (this.app.talkingAvatar && this.app.talkingAvatar.inTalkingHeadMode) {
                console.log('[MeshSelector] New mesh selected, exiting TalkingHead mode');
                try {
                    this.app.talkingAvatar.exitTalkingHeadMode();
                } catch (error) {
                    console.error('[MeshSelector] Error exiting TalkingHead mode:', error);
                    // Continue with mesh loading even if exiting TalkingHead mode fails
                    // This ensures the user can still change meshes if there's an issue with TalkingHead
                }
            }

            // Store the mesh filename as a stable ID
            const meshFileName = meshPath.split('/').pop();
            console.log('[MeshSelector] Loading mesh with stable ID:', meshFileName);

            await this.app.loadAssets({
                meshes: {
                    selected: `${ASSETS.SAVE_OPTIONS.meshPath}/${meshPath}`
                }
            }, {
                reloadMeshes: true,
                reloadEnvironment: false
            });

            // Wait briefly for the object to be fully loaded into scene
            setTimeout(async () => {
                // Check if the app has a talkingAvatar and if this is an avatar-compatible mesh
                if (this.app.talkingAvatar) {
                    const loadedObject = this.app.objects.get('selected');
                    if (loadedObject) {
                        // Store the filename in userData for stable identification
                        if (!loadedObject.userData) loadedObject.userData = {};
                        loadedObject.userData.fileName = meshFileName;

                        const detectionResult = detectAvatarMesh(loadedObject, {
                            logDetection: true,
                            onMeshFound: (mesh, analysis) => {
                                // Update the talkingAvatar references
                                this.app.talkingAvatar.avatarMesh = mesh;
                                if (mesh.morphTargetDictionary && mesh.morphTargetInfluences) {
                                    this.app.talkingAvatar.morphTargetDictionary = mesh.morphTargetDictionary;
                                    this.app.talkingAvatar.morphTargetInfluences = mesh.morphTargetInfluences;
                                }
                            }
                        });

                        // Load metadata for avatar meshes that have a skeleton
                        if (detectionResult.found) {
                            // Try to load gender metadata for this avatar mesh if available
                            await this.loadMeshMetadata(meshFileName, loadedObject);

                            this.makeAvatarButton.disabled = false;
                            this.makeAvatarButton.classList.add('available');

                            if (detectionResult.hasMorphTargets) {
                                this.makeAvatarButton.title = `Transform ${detectionResult.meshType} to talking avatar (${detectionResult.morphTargets.length} morph targets)`;
                            } else {
                                this.makeAvatarButton.title = `Transform ${detectionResult.meshType} to talking avatar (skeleton only)`;
                            }
                        } else {
                            this.makeAvatarButton.disabled = true;
                            this.makeAvatarButton.classList.remove('available');
                            this.makeAvatarButton.title = 'This mesh does not have a skeleton for animation';
                        }
                    }
                }
            }, 200);
        } catch (error) {
            console.error('Failed to load mesh:', error);
        }
    }

    /**
     * Load metadata for a mesh file if available and update voice settings
     * @param {string} meshFileName - The filename of the mesh
     * @param {THREE.Object3D} loadedObject - The loaded mesh object
     */
    async loadMeshMetadata(meshFileName, loadedObject) {
        try {
            // Get base filename without extension
            const baseFilename = meshFileName.replace(/\.[^/.]+$/, '');

            // Determine if this is a doll mesh based on the filename
            const isDoll = baseFilename.includes('doll');

            // Import the generateCacheKey function to get the correct metadata filename
            const { generateCacheKey } = await import('@/utils/cache.js');

            // Generate a cache key for the metadata file
            // Both doll and regular meshes will now use the same naming convention with a hash
            const source = isDoll ? 'doll' : 'mesh';
            const cacheKey = await generateCacheKey(source, baseFilename, null);

            console.log(`[MeshSelector] Generated cache key: ${cacheKey} for ${baseFilename}`);

            // Use the cache key as the metadata filename
            const metadataFilename = `${cacheKey}.json`;

            console.log(`[MeshSelector] Generated metadata filename: ${metadataFilename} for ${baseFilename}`);

            // Prepare the metadata path - avoid doubled assets prefix
            const metadataPath = `${ASSETS.SAVE_OPTIONS.seedPath}/${metadataFilename}`.replace(/^\/assets\//, '');
            console.log('[MeshSelector] Looking for metadata file at:', metadataPath);

            // Check if metadata file exists
            let existsResult = await fileExists(metadataPath);

            if (existsResult) {
                // Fetch metadata
                const metadataResult = await fetchFile(metadataPath);
                if (metadataResult.success && metadataResult.content) {
                    let metadata;
                    try {
                        metadata = JSON.parse(metadataResult.content);
                        console.log('[MeshSelector] Loaded metadata for mesh:', metadata);

                        // Store metadata in the object's userData
                        loadedObject.userData.metadata = metadata;

                        // Check for Letta agent ID
                        if (metadata.lettaAgentId) {
                            console.log(`[MeshSelector] Found Letta agent ID in metadata: ${metadata.lettaAgentId}`);
                            loadedObject.userData.lettaAgentId = metadata.lettaAgentId;
                        }

                        // Set gender based on the outfitGender field
                        if (metadata.outfitGender) {
                            const gender = metadata.outfitGender === 'feminine' ? 'female' : 'male';
                            loadedObject.userData.gender = gender;
                            console.log(`[MeshSelector] Setting gender to ${gender} from metadata`);

                            // Update TalkingAvatar's voice configuration if available
                            if (this.app.talkingAvatar) {
                                this.app.talkingAvatar.voiceConfig.currentGender = gender;

                                // Update the voice with current language and new gender
                                const currentLang = this.app.talkingAvatar.voiceConfig.currentLanguage;
                                this.app.talkingAvatar.setVoice(currentLang, gender);
                                console.log(`[MeshSelector] Updated TalkingAvatar voice to ${currentLang}/${gender}`);
                            }
                        }
                    } catch (parseError) {
                        console.error('[MeshSelector] Error parsing metadata:', parseError);
                    }
                }
            } else {
                console.log(`[MeshSelector] No metadata file found for ${baseFilename}`);

                // Skip creating a seed file for the default mesh
                if (baseFilename === 'default') {
                    console.log('[MeshSelector] Skipping seed file creation for default mesh');
                    return;
                }

                // Create a default seed file when none is found
                try {
                    const { storeSeed } = await import('@/utils/cache.js');

                    // Determine if this is a doll mesh based on the filename
                    const isDoll = baseFilename.includes('doll');

                    // Create default seed data with initial values
                    const defaultSeedData = {
                        roleName: isDoll ? "陈鲁豫" : "default", // Use 陈鲁豫 for dolls, default for others
                        favorite: false,
                        timestamp: Date.now(),
                        meshName: baseFilename
                    };

                    // Use the same cache key that we generated earlier
                    // This ensures consistency between the lookup and creation

                    // Store the default seed
                    const result = await storeSeed(cacheKey, defaultSeedData);
                    if (result && result.success) {
                        console.log(`[MeshSelector] Created default seed file for ${baseFilename}:`, defaultSeedData);

                        // Store metadata in the object's userData
                        loadedObject.userData.metadata = defaultSeedData;
                    } else {
                        console.error('[MeshSelector] Failed to store default seed file:', result?.error || 'Unknown error');
                    }
                } catch (seedError) {
                    console.error('[MeshSelector] Error creating default seed file:', seedError);
                }
            }
        } catch (error) {
            console.error('[MeshSelector] Error loading mesh metadata:', error);
        }
    }

    setLoading(loading) {
        this.isLoading = loading;
        this.select.disabled = loading;
        if (this.deleteButton) this.deleteButton.disabled = loading;
        this.makeAvatarButton.disabled = loading;
        this.loadingIndicator.style.display = loading ? 'inline' : 'none';
    }

    dispose() {
        // Unsubscribe from port changes
        if (this.portChangeUnsubscribe) {
            this.portChangeUnsubscribe();
        }

        // Remove event listeners
        this.select?.removeEventListener('change', this.handleMeshChange.bind(this));
        this.deleteButton?.removeEventListener('click', this.handleDelete.bind(this));
        this.makeAvatarButton?.removeEventListener('click', this.handleMakeAvatar.bind(this));

        // Remove from DOM
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }

        // Clear references
        this.container = null;
        this.select = null;
        this.deleteButton = null;
        this.makeAvatarButton = null;
        this.loadingIndicator = null;
    }


    // Removed redundant methods onSelectChange and onMeshSelect
    // These methods were not being used and contained placeholder comments

    /**
     * Select a mesh programmatically
     * @param {string} meshId - The ID of the mesh to select
     */
    selectMesh(meshId) {
        if (!meshId || !this.select) return;

        // Find the option with the matching value
        const option = Array.from(this.select.options).find(opt => opt.value === meshId);
        if (option) {
            // Set the select value
            this.select.value = meshId;

            // Trigger the change event to load the mesh
            const event = new Event('change');
            this.select.dispatchEvent(event);

            console.log(`[MeshSelector] Selected mesh: ${meshId}`);
            return true;
        }

        console.warn(`[MeshSelector] Mesh not found: ${meshId}`);
        return false;
    }

    /**
     * Select a mesh by its object ID (e.g., 'generated', 'selected')
     * @param {string} objectId - The object ID to select
     */
    selectMeshByObjectId(objectId) {
        console.log(`[MeshSelector] Attempting to select mesh by object ID: ${objectId}`);

        // Get the object from the app's objects map
        const object = this.app.objects.get(objectId);
        if (!object) {
            console.warn(`[MeshSelector] No object found with ID: ${objectId}`);
            return false;
        }

        // Get the mesh filename from the object's userData
        const fileName = object.userData?.fileName;
        if (!fileName) {
            console.warn(`[MeshSelector] No fileName found in object userData for ID: ${objectId}`);

            // If no fileName in userData, try to find a matching mesh in the dropdown by other means
            // For generated objects, refresh the mesh list first and then try to find the newest mesh
            this.fetchMeshes().then(() => {
                setTimeout(() => {
                    // Try to select the first mesh in the list (usually the newest)
                    if (this.select && this.select.options.length > 1) {
                        // Skip the default "Select mesh" option
                        this.select.selectedIndex = 1;
                        const event = new Event('change');
                        this.select.dispatchEvent(event);
                        console.log(`[MeshSelector] Selected first available mesh as fallback`);
                    }
                }, 100);
            });
            return false;
        }

        // Remove file extension for comparison
        const baseFileName = fileName.replace(/\.[^/.]+$/, '');
        console.log(`[MeshSelector] Looking for mesh with base filename: ${baseFileName}`);

        // Find the option in the dropdown that matches this filename
        let selectedOption = null;
        for (const option of this.select.options) {
            if (option.value && option.value !== '') {
                // Remove extension from option value for comparison
                const optionBaseValue = option.value.replace(/\.[^/.]+$/, '');
                if (optionBaseValue === baseFileName) {
                    selectedOption = option;
                    break;
                }
            }
        }

        if (selectedOption) {
            // Set the select value and trigger change event
            this.select.value = selectedOption.value;
            const event = new Event('change');
            this.select.dispatchEvent(event);
            console.log(`[MeshSelector] Successfully selected mesh: ${selectedOption.value}`);
            return true;
        } else {
            console.warn(`[MeshSelector] No matching option found for filename: ${baseFileName}`);

            // Refresh mesh list and try again
            this.fetchMeshes().then(() => {
                setTimeout(() => {
                    this.selectMeshByObjectId(objectId);
                }, 500);
            });
            return false;
        }
    }

    /**
     * Update UI after loading assets - refreshes the mesh list
     */
    updateAfterLoadingAssets() {
        // Refresh the mesh list
        this.fetchMeshes();

        // If we have a selected mesh, update button states
        const selectedObject = this.app.objects.get('selected');
        if (selectedObject) {
            // Check if the mesh can be transformed to TalkingHead
            if (this.app.talkingAvatar) {
                const detectionResult = detectAvatarMesh(selectedObject, {
                    logDetection: true,
                    onMeshFound: (mesh, analysis) => {
                        // Update the talkingAvatar references
                        this.app.talkingAvatar.avatarMesh = mesh;
                        if (mesh.morphTargetDictionary && mesh.morphTargetInfluences) {
                            this.app.talkingAvatar.morphTargetDictionary = mesh.morphTargetDictionary;
                            this.app.talkingAvatar.morphTargetInfluences = mesh.morphTargetInfluences;
                        }
                    }
                });
                if (detectionResult.found) {
                    this.makeAvatarButton.disabled = false;
                    this.makeAvatarButton.classList.add('available');

                    if (detectionResult.hasMorphTargets) {
                        this.makeAvatarButton.title = `Transform ${detectionResult.meshType} to talking avatar (with morph targets)`;
                    } else {
                        this.makeAvatarButton.title = `Transform ${detectionResult.meshType} to talking avatar (skeleton only)`;
                    }
                } else {
                    this.makeAvatarButton.disabled = true;
                    this.makeAvatarButton.classList.remove('available');
                    this.makeAvatarButton.title = 'This mesh does not have a skeleton for animation';
                }
            }
        }
    }
}