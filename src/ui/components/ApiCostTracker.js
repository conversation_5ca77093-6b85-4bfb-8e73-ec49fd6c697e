/**
 * API Cost Tracker UI Component
 * Real-time display of API usage costs and call statistics
 */

import { createLogger } from '@/utils/logger';

const logger = createLogger('ApiCostTrackerUI');

export class ApiCostTrackerUI {
  constructor(options = {}) {
    this.options = {
      updateInterval: options.updateInterval || 1000, // Update every second
      position: options.position || 'top-right',
      minimized: options.minimized || false,
      showDetailed: options.showDetailed || false,
      ...options
    };

    this.costTracker = null;
    this.container = null;
    this.updateTimer = null;
    this.isInitialized = false;
    this.logger = logger;
  }

  /**
   * Initialize the cost tracker UI
   * @param {ApiCostTracker} costTracker - The cost tracker instance
   */
  async initialize(costTracker) {
    if (this.isInitialized) {
      return;
    }

    try {
      this.costTracker = costTracker;
      this._createUI();
      this._startUpdates();
      this.isInitialized = true;

      this.logger.info('✅ API Cost Tracker UI initialized');
    } catch (error) {
      this.logger.error('❌ Failed to initialize API Cost Tracker UI:', error);
    }
  }

  /**
   * Create the UI elements
   * @private
   */
  _createUI() {
    // Create main container
    this.container = document.createElement('div');
    this.container.id = 'api-cost-tracker';
    this.container.className = `api-cost-tracker ${this.options.position} ${this.options.minimized ? 'minimized' : ''}`;

    // Create header
    const header = document.createElement('div');
    header.className = 'api-cost-header';
    header.innerHTML = `
            <div class="api-cost-title">
                <span class="icon">💰</span>
                <span class="text">API Costs</span>
            </div>
            <div class="api-cost-controls">
                <button class="toggle-details" title="Toggle Details">${this.options.showDetailed ? '📊' : '📈'}</button>
                <button class="minimize-toggle" title="Minimize">${this.options.minimized ? '🔍' : '➖'}</button>
            </div>
        `;

    // Create content area
    const content = document.createElement('div');
    content.className = 'api-cost-content';
    content.innerHTML = `
            <div class="cost-summary">
                <div class="cost-item">
                    <span class="cost-label">Total Cost:</span>
                    <span class="cost-value" id="total-cost">$0.00</span>
                </div>
                <div class="cost-item">
                    <span class="cost-label">Requests:</span>
                    <span class="cost-value" id="total-requests">0</span>
                </div>
                <div class="cost-item">
                    <span class="cost-label">Avg Cost/Req:</span>
                    <span class="cost-value" id="avg-cost">$0.000</span>
                </div>
            </div>
            <div class="cost-details" style="display: ${this.options.showDetailed ? 'block' : 'none'}">
                <div class="performance-stats">
                    <div class="stat-item">
                        <span class="stat-label">Response Time:</span>
                        <span class="stat-value" id="avg-response-time">0ms</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Success Rate:</span>
                        <span class="stat-value" id="success-rate">100%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">API Limits:</span>
                        <span class="stat-value" id="api-limits">N/A</span>
                    </div>
                </div>
                <div class="recent-calls">
                    <div class="section-title">Recent Calls (Last 5 min)</div>
                    <div class="calls-list" id="recent-calls-list">
                        <div class="no-calls">No recent calls</div>
                    </div>
                </div>
                <div class="model-breakdown">
                    <div class="section-title">Model Usage</div>
                    <div class="models-list" id="models-list">
                        <div class="no-models">No models used</div>
                    </div>
                </div>
            </div>
        `;

    this.container.appendChild(header);
    this.container.appendChild(content);

    // Add event listeners
    this._setupEventListeners();

    // Add to DOM
    document.body.appendChild(this.container);

    // Add CSS styles
    this._addStyles();
  }

  /**
   * Setup event listeners for UI interactions
   * @private
   */
  _setupEventListeners() {
    const minimizeBtn = this.container.querySelector('.minimize-toggle');
    const detailsBtn = this.container.querySelector('.toggle-details');
    const header = this.container.querySelector('.api-cost-header');

    // Minimize/maximize toggle
    minimizeBtn.addEventListener('click', () => {
      this.options.minimized = !this.options.minimized;
      this.container.classList.toggle('minimized', this.options.minimized);
      minimizeBtn.textContent = this.options.minimized ? '🔍' : '➖';
    });

    // Details toggle
    detailsBtn.addEventListener('click', () => {
      this.options.showDetailed = !this.options.showDetailed;
      const details = this.container.querySelector('.cost-details');
      details.style.display = this.options.showDetailed ? 'block' : 'none';
      detailsBtn.textContent = this.options.showDetailed ? '📊' : '📈';
    });

    // Double-click header to toggle minimize
    header.addEventListener('dblclick', () => {
      minimizeBtn.click();
    });
  }

  /**
   * Start periodic updates
   * @private
   */
  _startUpdates() {
    this.updateTimer = setInterval(() => {
      this._updateDisplay();
    }, this.options.updateInterval);

    // Initial update
    this._updateDisplay();
  }

  /**
   * Update the display with current cost data
   * @private
   */
  _updateDisplay() {
    if (!this.costTracker) return;

    try {
      const report = this.costTracker.getComprehensiveReport();

      // Update summary
      document.getElementById('total-cost').textContent = `$${report.total.totalCost.toFixed(3)}`;
      document.getElementById('total-requests').textContent = report.total.requests.toString();
      document.getElementById('avg-cost').textContent = `$${report.total.averageCostPerRequest.toFixed(4)}`;

      // Update performance stats
      document.getElementById('avg-response-time').textContent = `${Math.round(report.performance.avgResponseTime)}ms`;
      document.getElementById('success-rate').textContent = report.performance.successRate;

      // Update API limits status
      this._updateApiLimitsStatus();

      // Update recent calls (last 5 minutes)
      this._updateRecentCalls(report.lastHour);

      // Update model breakdown
      this._updateModelBreakdown(report.lastHour.modelBreakdown);

      // Update container class based on cost thresholds
      this._updateCostStatus(report.total.totalCost);

    } catch (error) {
      this.logger.error('❌ Error updating cost display:', error);
    }
  }

  /**
   * Update recent calls display
   * @private
   */
  _updateRecentCalls(hourlyData) {
    const callsList = document.getElementById('recent-calls-list');

    if (hourlyData.totalRequests === 0) {
      callsList.innerHTML = '<div class="no-calls">No recent calls</div>';
      return;
    }

    const avgTime = Math.round(hourlyData.totalRequests > 0 ?
      (hourlyData.totalCost / hourlyData.totalRequests) * 1000 : 0);

    callsList.innerHTML = `
            <div class="call-summary">
                <div class="call-item">
                    <span>Requests (1h):</span>
                    <span>${hourlyData.totalRequests}</span>
                </div>
                <div class="call-item">
                    <span>Cost (1h):</span>
                    <span>$${hourlyData.totalCost.toFixed(3)}</span>
                </div>
                <div class="call-item">
                    <span>Avg Time:</span>
                    <span>${avgTime}ms</span>
                </div>
            </div>
        `;
  }

  /**
   * Update model breakdown display
   * @private
   */
  _updateModelBreakdown(modelBreakdown) {
    const modelsList = document.getElementById('models-list');

    if (!modelBreakdown || modelBreakdown.length === 0) {
      modelsList.innerHTML = '<div class="no-models">No models used</div>';
      return;
    }

    const modelsHtml = modelBreakdown
      .sort((a, b) => b.cost - a.cost)
      .slice(0, 5) // Show top 5 models
      .map(model => `
                <div class="model-item">
                    <span class="model-name">${model.name}</span>
                    <span class="model-stats">
                        <span class="model-requests">${model.requests} req</span>
                        <span class="model-cost">$${model.cost.toFixed(3)}</span>
                    </span>
                </div>
            `).join('');

    modelsList.innerHTML = modelsHtml;
  }

  /**
   * Update API limits status display
   * @private
   */
  async _updateApiLimitsStatus() {
    try {
      // Try to get API limits from the agent service model
      const agentService = this.costTracker.viewer?.talkingAvatar?.agentCoordinator?.getAgentService();
      const model = agentService?.getModel();

      if (model && typeof model.getApiLimitsStatus === 'function') {
        const limitsStatus = model.getApiLimitsStatus();
        const limitsElement = document.getElementById('api-limits');

        if (limitsStatus.enabled) {
          if (limitsStatus.testMode) {
            const remaining = limitsStatus.remainingAttempts;
            limitsElement.textContent = `${remaining}/${limitsStatus.maxAttempts} left`;

            // Update styling based on remaining attempts
            limitsElement.classList.remove('limits-ok', 'limits-warning', 'limits-critical');
            if (remaining <= 0) {
              limitsElement.classList.add('limits-critical');
            } else if (remaining <= 2) {
              limitsElement.classList.add('limits-warning');
            } else {
              limitsElement.classList.add('limits-ok');
            }
          } else {
            limitsElement.textContent = 'Enabled';
            limitsElement.classList.add('limits-ok');
          }
        } else {
          limitsElement.textContent = 'Disabled';
          limitsElement.classList.add('limits-ok');
        }
      } else {
        document.getElementById('api-limits').textContent = 'N/A';
      }
    } catch (error) {
      document.getElementById('api-limits').textContent = 'Error';
    }
  }

  /**
   * Update cost status styling based on thresholds
   * @private
   */
  _updateCostStatus(totalCost) {
    this.container.classList.remove('cost-low', 'cost-medium', 'cost-high', 'cost-critical');

    if (totalCost < 1) {
      this.container.classList.add('cost-low');
    } else if (totalCost < 5) {
      this.container.classList.add('cost-medium');
    } else if (totalCost < 20) {
      this.container.classList.add('cost-high');
    } else {
      this.container.classList.add('cost-critical');
    }
  }

  /**
   * Add CSS styles for the component
   * @private
   */
  _addStyles() {
    if (document.getElementById('api-cost-tracker-styles')) {
      return; // Styles already added
    }

    const styles = document.createElement('style');
    styles.id = 'api-cost-tracker-styles';
    styles.textContent = `
            .api-cost-tracker {
                position: fixed;
                z-index: 10000;
                background: rgba(0, 0, 0, 0.9);
                border: 1px solid #333;
                border-radius: 8px;
                color: #ffffff;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 12px;
                min-width: 280px;
                max-width: 400px;
                backdrop-filter: blur(10px);
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                transition: all 0.3s ease;
            }

            .api-cost-tracker.top-right {
                top: 20px;
                right: 20px;
            }

            .api-cost-tracker.top-left {
                top: 20px;
                left: 20px;
            }

            .api-cost-tracker.minimized {
                min-width: auto;
                width: auto;
            }

            .api-cost-tracker.minimized .api-cost-content {
                display: none;
            }

            .api-cost-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 12px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 7px 7px 0 0;
                cursor: move;
            }

            .api-cost-title {
                display: flex;
                align-items: center;
                gap: 6px;
                font-weight: bold;
            }

            .api-cost-controls {
                display: flex;
                gap: 4px;
            }

            .api-cost-controls button {
                background: none;
                border: none;
                color: #ffffff;
                cursor: pointer;
                padding: 2px 4px;
                border-radius: 3px;
                font-size: 12px;
                transition: background 0.2s;
            }

            .api-cost-controls button:hover {
                background: rgba(255, 255, 255, 0.2);
            }

            .api-cost-content {
                padding: 12px;
            }

            .cost-summary {
                display: flex;
                flex-direction: column;
                gap: 6px;
                margin-bottom: 12px;
            }

            .cost-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .cost-label {
                color: #cccccc;
            }

            .cost-value {
                font-weight: bold;
            }

            .cost-details {
                border-top: 1px solid #333;
                padding-top: 12px;
            }

            .section-title {
                font-weight: bold;
                margin-bottom: 6px;
                color: #ffffff;
                font-size: 11px;
                text-transform: uppercase;
            }

            .performance-stats {
                margin-bottom: 12px;
            }

            .stat-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 4px;
            }

            .stat-label {
                color: #cccccc;
                font-size: 11px;
            }

            .stat-value {
                font-weight: bold;
                font-size: 11px;
            }

            .recent-calls, .model-breakdown {
                margin-bottom: 12px;
            }

            .call-summary, .calls-list {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 4px;
                padding: 6px;
            }

            .call-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 3px;
                font-size: 11px;
            }

            .model-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 4px 6px;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 3px;
                margin-bottom: 3px;
            }

            .model-name {
                font-weight: bold;
                font-size: 11px;
            }

            .model-stats {
                display: flex;
                gap: 8px;
                font-size: 10px;
                color: #cccccc;
            }

            .no-calls, .no-models {
                text-align: center;
                color: #666666;
                font-style: italic;
                padding: 8px;
                font-size: 11px;
            }

            /* Cost status colors */
            .api-cost-tracker.cost-low .cost-value {
                color: #4ade80;
            }

            .api-cost-tracker.cost-medium .cost-value {
                color: #facc15;
            }

            .api-cost-tracker.cost-high .cost-value {
                color: #f97316;
            }

            .api-cost-tracker.cost-critical .cost-value {
                color: #ef4444;
            }

            .api-cost-tracker.cost-critical {
                border-color: #ef4444;
                box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
            }

            /* API limits status styling */
            #api-limits.limits-ok {
                color: #4ade80;
            }

            #api-limits.limits-warning {
                color: #facc15;
            }

            #api-limits.limits-critical {
                color: #ef4444;
                font-weight: bold;
            }
        `;

    document.head.appendChild(styles);
  }

  /**
   * Stop updates and cleanup
   */
  dispose() {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = null;
    }

    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }

    this.isInitialized = false;
    this.logger.info('🗑️ API Cost Tracker UI disposed');
  }

  /**
   * Show the tracker UI
   */
  show() {
    if (this.container) {
      this.container.style.display = 'block';
    }
  }

  /**
   * Hide the tracker UI
   */
  hide() {
    if (this.container) {
      this.container.style.display = 'none';
    }
  }

  /**
   * Toggle the tracker UI visibility
   */
  toggle() {
    if (this.container) {
      const isVisible = this.container.style.display !== 'none';
      if (isVisible) {
        this.hide();
      } else {
        this.show();
      }
    }
  }
}

export default ApiCostTrackerUI;