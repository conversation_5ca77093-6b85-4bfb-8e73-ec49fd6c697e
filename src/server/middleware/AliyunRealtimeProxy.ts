/**
 * Aliyun Realtime WebSocket Proxy - Provider-Specific Implementation
 * 
 * Extends BaseWebSocketProxy with Aliyun DashScope-specific functionality
 * including session.update delay handling, VAD event processing, and 1011 error recovery
 */

import type http from 'http';
import { BaseWebSocketProxy, ProviderConfig, ConnectionState, MessageContext } from './BaseWebSocketProxy.js';
import { getEnvVar } from '../../config/env.js';
import {
  createErrorRecoveryConfig,
  ALIYUN_WEBSOCKET_CONFIG,
  generateEventId
} from '../../agent/models/aliyun/AliyunConfig.js';

/**
 * Aliyun-specific configuration for DashScope realtime API
 */
export class AliyunRealtimeProxy {
  private proxy: BaseWebSocketProxy;

  constructor(options: { path?: string; logPrefix?: string } = {}) {
    // Use centralized AliyunConfig.js for all configuration settings
    const errorRecoveryConfig = createErrorRecoveryConfig();

    const providerConfig: ProviderConfig = {
      getUpstreamEndpoint: this.getUpstreamEndpoint.bind(this),
      getHeaders: this.getHeaders.bind(this),
      validateApiKey: this.validateApiKey.bind(this),
      handleSpecialMessage: this.handleSpecialMessage.bind(this),
      onConnectionOpen: this.onConnectionOpen.bind(this),
      onConnectionClose: this.onConnectionClose.bind(this),
      onConnectionError: this.onConnectionError.bind(this)
    };

    this.proxy = new BaseWebSocketProxy(
      {
        path: options.path || '/ws',
        maxRetries: errorRecoveryConfig.maxRetries,
        retryDelay: errorRecoveryConfig.retryDelayMs,
        // Use centralized WebSocket configuration from AliyunConfig.js
        pingInterval: ALIYUN_WEBSOCKET_CONFIG.pingInterval,
        pingTimeout: ALIYUN_WEBSOCKET_CONFIG.pingTimeout,
        handshakeTimeout: ALIYUN_WEBSOCKET_CONFIG.handshakeTimeout,
        logPrefix: options.logPrefix || ALIYUN_WEBSOCKET_CONFIG.logPrefix
      },
      providerConfig
    );
  }

  /**
   * Attach Aliyun realtime proxy to HTTP server
   */
  attach(server: http.Server): boolean {
    return this.proxy.attach(server);
  }

  /**
   * Get upstream endpoint for Aliyun DashScope
   */
  private getUpstreamEndpoint(model: string): string {
    const actualModel = model || ALIYUN_WEBSOCKET_CONFIG.defaultModel;
    return `${ALIYUN_WEBSOCKET_CONFIG.endpoint}?model=${encodeURIComponent(actualModel)}`;
  }

  /**
   * Get headers for Aliyun DashScope authentication
   */
  private getHeaders(_model: string): Record<string, string> {
    const apiValidation = this.validateApiKey();
    return {
      'Authorization': `Bearer ${apiValidation.key}`,
      'User-Agent': 'HologramSoftware-Proxy/1.0'
    };
  }

  /**
   * Validate Aliyun API key
   */
  private validateApiKey(): { valid: boolean; key?: string; error?: string } {
    const API_KEY = getEnvVar('DASHSCOPE_API_KEY', '', false) || getEnvVar('ALIYUN_API_KEY', '', false);

    if (!API_KEY) {
      return {
        valid: false,
        error: 'Environment variable VITE_DASHSCOPE_API_KEY or VITE_ALIYUN_API_KEY is not set'
      };
    }

    if (API_KEY.length < ALIYUN_WEBSOCKET_CONFIG.minApiKeyLength) {
      return {
        valid: false,
        error: `Invalid API key - too short (minimum ${ALIYUN_WEBSOCKET_CONFIG.minApiKeyLength} characters)`
      };
    }

    return { valid: true, key: API_KEY };
  }

  /**
   * Handle Aliyun-specific messages (session.update delay, VAD events)
   */
  private handleSpecialMessage(messageObj: any, messageStr: string, context: MessageContext): boolean {
    // CRITICAL 1011 FIX: Validate and properly format session.update according to official API
    if (messageObj.type === 'session.update') {
      console.log('[AliyunProxy] 🔧 Processing session.update message...');

      // Validate and fix session.update format to match official Python example
      const fixedSessionUpdate = this.validateAndFixSessionUpdate(messageObj);

      if (context.upstream && context.upstream.readyState === 1 /* WebSocket.OPEN */) {
        console.log('[AliyunProxy] ✅ Sending validated session.update immediately (required by Aliyun API)');
        try {
          context.sendToUpstream(JSON.stringify(fixedSessionUpdate));
          return true; // Handled successfully
        } catch (error) {
          console.error('[AliyunProxy] ❌ Failed to send session.update:', error);
          return false; // Let base proxy handle it
        }
      } else {
        // If upstream not ready, queue the validated message
        console.log('[AliyunProxy] ⏱️ Upstream not ready, will queue validated session.update message');
        // Update the message string with the fixed version
        context.messageQueue = context.messageQueue || [];
        context.messageQueue.push(JSON.stringify(fixedSessionUpdate));
        return true; // Mark as handled since we queued it
      }
    }

    return false; // Not handled - proceed with normal processing
  }

  /**
   * Validate and fix session.update format to match official Aliyun API specification
   */
  private validateAndFixSessionUpdate(messageObj: any): any {
    // Create session.update that exactly matches the Python official example
    const fixedUpdate = {
      type: "session.update",
      event_id: messageObj.event_id || generateEventId(),
      session: {
        modalities: ["text", "audio"],
        voice: messageObj.session?.voice || "Chelsie", // Use official example default
        input_audio_format: "pcm16",
        output_audio_format: "pcm16",
        input_audio_transcription: {
          model: "gummy-realtime-v1"
        },
        turn_detection: {
          type: "server_vad",
          threshold: 0.1,  // Match official example exactly
          prefix_padding_ms: 500,
          silence_duration_ms: 900
        }
      }
    };

    console.log('[AliyunProxy] 🔧 Fixed session.update format:', JSON.stringify(fixedUpdate, null, 2));
    return fixedUpdate;
  }

  /**
   * Handle connection open event with immediate message processing
   */
  private onConnectionOpen(upstream: any, connectionState: ConnectionState): void {
    console.log('[AliyunProxy] 🔗 Connection opened, processing queued messages immediately');

    // Process all queued messages immediately - no artificial delays
    // The Aliyun API expects session.update to be sent right after connection
    const processQueuedMessage = (queuedMessage: string) => {
      try {
        if (upstream && upstream.readyState === 1 /* WebSocket.OPEN */) {
          console.log('[AliyunProxy] 📤 Sending queued message immediately:', JSON.parse(queuedMessage).type);
          upstream.send(queuedMessage);
        }
      } catch (e) {
        console.error('[AliyunProxy] ❌ Failed to send queued message:', e);
      }
    };

    // Process all queued messages immediately
    const originalQueue = [...connectionState.messageQueue];
    connectionState.messageQueue.length = 0; // Clear the queue

    // Send all messages immediately without delays
    originalQueue.forEach(processQueuedMessage);

    console.log('[AliyunProxy] ✅ Processed', originalQueue.length, 'queued messages');
  }

  /**
   * Handle connection close with Aliyun-specific error recovery
   */
  private onConnectionClose(code: number, reason: string, connectionState: ConnectionState): void {
    console.log(`[AliyunProxy] 🔌 Connection closed - Code: ${code}, Reason: ${reason}`);

    // Handle 1011 Internal Server Error - common with Aliyun API
    if (code === 1011) {
      console.error(`[AliyunProxy] ❌ 1011 Internal Server Error detected:`, {
        code,
        reason,
        attempts: connectionState.connectionAttempts,
        clients: connectionState.clients.size,
        troubleshooting: {
          message: 'This is often caused by invalid session.update format or timing',
          action: 'The proxy will retry with corrected session.update format'
        }
      });

      // Reset connection state for immediate retry
      connectionState.isReady = false;
      connectionState.connectionInProgress = false;

      return; // Let base class handle retry with immediate reconnection
    }

    // Handle other common error codes
    if (code === 1006) {
      console.error(`[AliyunProxy] ❌ 1006 Connection closed abnormally:`, {
        code,
        reason,
        connectionAttempts: connectionState.connectionAttempts,
        troubleshooting: 'Check network connectivity and API key validity'
      });
    }

    if (code === 1002) {
      console.error(`[AliyunProxy] ❌ 1002 Protocol error:`, {
        code,
        reason,
        troubleshooting: 'Check message format and API compatibility'
      });
    }
  }

  /**
   * Handle connection errors with Aliyun-specific diagnostics
   */
  private onConnectionError(error: Error, connectionState: ConnectionState): void {
    // Add Aliyun-specific error diagnostics
    console.error('[AliyunProxy] Connection error diagnostics:', {
      message: error.message,
      clients: connectionState.clients.size,
      attempts: connectionState.connectionAttempts,
      queueSize: connectionState.messageQueue.length,
      troubleshooting: {
        apiKey: 'Verify VITE_DASHSCOPE_API_KEY or VITE_ALIYUN_API_KEY is set correctly',
        network: 'Check network connectivity to dashscope.aliyuncs.com',
        model: 'Verify model parameter is correct (qwen-omni-turbo-realtime)'
      }
    });
  }
}


/**
 * Factory function for backward compatibility
 */
export function attachAliyunRealtimeProxy(server: http.Server, path: string = '/ws'): void {
  const aliyunProxy = new AliyunRealtimeProxy({ path });
  aliyunProxy.attach(server);
}