/**
 * Checkpoint Manager Middleware
 * 
 * Generalized checkpoint downloading and inference management for ML models.
 * Supports multiple model types: MediaPipe, Sherpa-ONNX, custom models.
 */

import { Router } from 'express';
import fs from 'fs';
import path from 'path';
import axios from 'axios';
import multer from 'multer';
import { createLogger } from '@/utils/logger';

// JavaScript with JSDoc type hints for better IDE support
/**
 * @typedef {keyof typeof MODEL_TYPES} ModelType
 * @typedef {{
 *   checkpointId?: string,
 *   modelType?: string,
 *   size?: number,
 *   createdAt?: string,
 *   sourceUrl?: string,
 *   downloadedAt?: string,
 *   originalName?: string,
 *   mimeType?: string,
 *   [key: string]: any
 * }} CheckpointMetadata
 * @typedef {{
 *   id: string,
 *   path: string,
 *   size: number,
 *   lastModified: Date,
 *   metadata: CheckpointMetadata,
 *   cached?: boolean,
 *   cacheTimestamp?: number
 * }} CheckpointInfo
 * @typedef {{
 *   data: Buffer,
 *   metadata: CheckpointMetadata,
 *   path: string,
 *   timestamp: number,
 *   size: number
 * }} CacheEntry
 */

const logger = createLogger('CheckpointManager');

// Model types and their configurations
const MODEL_TYPES = {
    MEDIAPIPE: {
        extensions: ['.task', '.tflite'],
        baseDir: 'models/mediapipe',
        downloadSources: [
            'https://storage.googleapis.com/mediapipe-models',
            'https://cdn.jsdelivr.net/npm/@mediapipe'
        ]
    },
    SHERPA_ONNX: {
        extensions: ['.onnx', '.bin', '.json'],
        baseDir: 'models/sherpa',
        downloadSources: [
            'https://huggingface.co/sherpa-onnx',
            'https://github.com/k2-fsa/sherpa-onnx/releases'
        ]
    },
    HUGGINGFACE: {
        extensions: ['.bin', '.safetensors', '.onnx', '.json'],
        baseDir: 'models/huggingface',
        downloadSources: [
            'https://huggingface.co'
        ]
    },
    CUSTOM: {
        extensions: ['.bin', '.onnx', '.tflite', '.pb'],
        baseDir: 'models/custom',
        downloadSources: []
    }
};

// Cache configuration
const CACHE_CONFIG = {
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    maxSize: 500 * 1024 * 1024, // 500MB per model type
    retryAttempts: 3,
    retryDelay: 1000 // 1 second
};

/**
 * Checkpoint Manager Class
 * Handles model downloading, caching, and lifecycle management
 */
export class CheckpointManager {
    constructor(options = {}) {
        this.baseDir = options.baseDir || path.join(process.cwd(), 'public');
        this.enableCache = options.enableCache !== false;
        this.maxCacheSize = options.maxCacheSize || CACHE_CONFIG.maxSize;
        this.router = Router();

        // Initialize storage
        this.cache = new Map();
        this.downloadQueue = new Map();
        this.activeDownloads = new Set();

        this._setupRoutes();
        this._ensureDirectories();

        logger.info('🏗️ Checkpoint Manager initialized', {
            baseDir: this.baseDir,
            enableCache: this.enableCache,
            supportedTypes: Object.keys(MODEL_TYPES)
        });
    }

    /**
     * Setup Express routes for checkpoint management
     */
    _setupRoutes() {
        // Configure multer for file uploads
        const upload = multer({
            storage: multer.memoryStorage(),
            limits: {
                fileSize: 500 * 1024 * 1024 // 500MB
            }
        });

        // Download checkpoint route
        this.router.post('/download', this._handleDownload.bind(this));

        // Upload checkpoint route
        this.router.post('/upload', upload.single('checkpoint'), this._handleUpload.bind(this));

        // List checkpoints route - separate routes for optional parameter
        this.router.get('/list/:modelType', this._handleList.bind(this));
        this.router.get('/list', this._handleList.bind(this));

        // Delete checkpoint route
        this.router.delete('/:modelType/:checkpointId', this._handleDelete.bind(this));

        // Cache management routes
        this.router.post('/cache/clear', this._handleCacheClear.bind(this));
        this.router.get('/cache/stats', this._handleCacheStats.bind(this));

        // Health check route
        this.router.get('/health', this._handleHealth.bind(this));
    }

    /**
     * Ensure required directories exist
     */
    _ensureDirectories() {
        Object.values(MODEL_TYPES).forEach(config => {
            const dir = path.join(this.baseDir, config.baseDir);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                logger.debug(`📁 Created directory: ${dir}`);
            }
        });
    }

    /**
     * Download checkpoint handler
     */
    async _handleDownload(req, res) {
        try {
            const { url, modelType, checkpointId, forceDownload = false } = req.body;

            if (!url || !modelType || !checkpointId) {
                return res.status(400).json({
                    error: 'Missing required parameters',
                    required: ['url', 'modelType', 'checkpointId']
                });
            }

            if (!MODEL_TYPES[modelType]) {
                return res.status(400).json({
                    error: 'Invalid model type',
                    supportedTypes: Object.keys(MODEL_TYPES)
                });
            }

            const result = await this.downloadCheckpoint(url, modelType, checkpointId, {
                forceDownload,
                onProgress: (progress) => {
                    // Could emit progress via WebSocket if needed
                    logger.debug(`📥 Download progress for ${checkpointId}: ${progress}%`);
                }
            });

            res.json(result);
        } catch (error) {
            logger.error('❌ Download failed:', error);
            res.status(500).json({
                error: 'Download failed',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }

    /**
     * Upload checkpoint handler
     */
    async _handleUpload(req, res) {
        try {
            const { modelType, checkpointId } = req.body;
            const file = req.file;

            if (!file || !modelType || !checkpointId) {
                return res.status(400).json({
                    error: 'Missing required parameters',
                    required: ['file', 'modelType', 'checkpointId']
                });
            }

            if (!MODEL_TYPES[modelType]) {
                return res.status(400).json({
                    error: 'Invalid model type',
                    supportedTypes: Object.keys(MODEL_TYPES)
                });
            }

            const result = await this.saveCheckpoint(file.buffer, modelType, checkpointId, {
                originalName: file.originalname,
                mimeType: file.mimetype,
                metadata: req.body.metadata ? JSON.parse(req.body.metadata) : {}
            });

            res.json(result);
        } catch (error) {
            logger.error('❌ Upload failed:', error);
            res.status(500).json({
                error: 'Upload failed',
                message: error.message
            });
        }
    }

    /**
     * List checkpoints handler
     */
    async _handleList(req, res) {
        try {
            const { modelType } = req.params;
            const { includeCache = false } = req.query;

            const result = await this.listCheckpoints(modelType, {
                includeCache: includeCache === 'true'
            });

            res.json(result);
        } catch (error) {
            logger.error('❌ List failed:', error);
            res.status(500).json({
                error: 'List failed',
                message: error.message
            });
        }
    }

    /**
     * Delete checkpoint handler
     */
    async _handleDelete(req, res) {
        try {
            const { modelType, checkpointId } = req.params;
            const { deleteFromCache = false } = req.query;

            const result = await this.deleteCheckpoint(modelType, checkpointId, {
                deleteFromCache: deleteFromCache === 'true'
            });

            res.json(result);
        } catch (error) {
            logger.error('❌ Delete failed:', error);
            res.status(500).json({
                error: 'Delete failed',
                message: error.message
            });
        }
    }

    /**
     * Cache clear handler
     */
    async _handleCacheClear(req, res) {
        try {
            const { modelType } = req.body;
            const result = await this.clearCache(modelType);
            res.json(result);
        } catch (error) {
            logger.error('❌ Cache clear failed:', error);
            res.status(500).json({
                error: 'Cache clear failed',
                message: error.message
            });
        }
    }

    /**
     * Cache stats handler
     */
    async _handleCacheStats(req, res) {
        try {
            const stats = await this.getCacheStats();
            res.json(stats);
        } catch (error) {
            logger.error('❌ Cache stats failed:', error);
            res.status(500).json({
                error: 'Cache stats failed',
                message: error.message
            });
        }
    }

    /**
     * Health check handler
     */
    async _handleHealth(req, res) {
        try {
            const health = {
                status: 'healthy',
                timestamp: new Date().toISOString(),
                cache: {
                    enabled: this.enableCache,
                    size: this.cache.size,
                    memoryUsage: process.memoryUsage()
                },
                activeDownloads: this.activeDownloads.size,
                queuedDownloads: this.downloadQueue.size,
                supportedTypes: Object.keys(MODEL_TYPES)
            };

            res.json(health);
        } catch (error) {
            res.status(500).json({
                status: 'unhealthy',
                error: error.message
            });
        }
    }

    /**
     * Download checkpoint from URL
     */
    async downloadCheckpoint(url, modelType, checkpointId, options = {}) {
        const {
            forceDownload = false,
            onProgress = () => { },
            retryAttempts = CACHE_CONFIG.retryAttempts
        } = options;

        const downloadKey = `${modelType}:${checkpointId}`;

        // Check if already downloading
        if (this.activeDownloads.has(downloadKey)) {
            throw new Error(`Download already in progress for ${downloadKey}`);
        }

        // Check if already exists and not forced
        if (!forceDownload && await this.hasCheckpoint(modelType, checkpointId)) {
            logger.info(`✅ Checkpoint already exists: ${downloadKey}`);
            return {
                success: true,
                cached: true,
                path: this._getCheckpointPath(modelType, checkpointId),
                message: 'Checkpoint already available'
            };
        }

        this.activeDownloads.add(downloadKey);

        try {
            logger.info(`📥 Downloading checkpoint: ${downloadKey} from ${url}`);

            // Download with retries
            const data = await this._downloadWithRetry(url, retryAttempts, onProgress);

            // Save to disk
            const savedPath = await this.saveCheckpoint(data, modelType, checkpointId, {
                sourceUrl: url,
                downloadedAt: new Date().toISOString()
            });

            logger.info(`✅ Download complete: ${downloadKey}`);

            return {
                success: true,
                cached: false,
                path: savedPath,
                size: data.length,
                message: 'Checkpoint downloaded successfully'
            };

        } catch (error) {
            logger.error(`❌ Download failed for ${downloadKey}:`, error);
            throw error;
        } finally {
            this.activeDownloads.delete(downloadKey);
        }
    }

    /**
     * Save checkpoint to disk and cache
     */
    async saveCheckpoint(data, modelType, checkpointId, metadata = {}) {
        const config = MODEL_TYPES[modelType];
        if (!config) {
            throw new Error(`Unsupported model type: ${modelType}`);
        }

        const checkpointPath = this._getCheckpointPath(modelType, checkpointId);
        const fullPath = path.join(this.baseDir, checkpointPath);

        // Ensure directory exists
        const dir = path.dirname(fullPath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }

        // Write to disk
        fs.writeFileSync(fullPath, data);

        // Add to cache if enabled
        if (this.enableCache) {
            this.cache.set(`${modelType}:${checkpointId}`, {
                data: data,
                metadata: metadata,
                path: checkpointPath,
                timestamp: Date.now(),
                size: data.length
            });

            await this._enforceCacheLimit(modelType);
        }

        // Save metadata file
        const metadataPath = fullPath + '.meta.json';
        fs.writeFileSync(metadataPath, JSON.stringify({
            checkpointId,
            modelType,
            size: data.length,
            createdAt: new Date().toISOString(),
            ...metadata
        }, null, 2));

        logger.info(`💾 Checkpoint saved: ${checkpointPath}`);
        return checkpointPath;
    }

    /**
     * Check if checkpoint exists
     */
    async hasCheckpoint(modelType, checkpointId) {
        // Check cache first
        if (this.enableCache && this.cache.has(`${modelType}:${checkpointId}`)) {
            return true;
        }

        // Check disk
        const checkpointPath = this._getCheckpointPath(modelType, checkpointId);
        const fullPath = path.join(this.baseDir, checkpointPath);
        return fs.existsSync(fullPath);
    }

    /**
     * List available checkpoints
     */
    async listCheckpoints(modelType = null, options = {}) {
        const { includeCache = false } = options;
        const results = {};

        const typesToCheck = modelType ? [modelType] : Object.keys(MODEL_TYPES);

        for (const type of typesToCheck) {
            const config = MODEL_TYPES[type];
            const typeDir = path.join(this.baseDir, config.baseDir);
            const checkpoints = [];

            if (fs.existsSync(typeDir)) {
                const files = fs.readdirSync(typeDir, { withFileTypes: true });

                for (const file of files) {
                    if (file.isFile() && !file.name.endsWith('.meta.json')) {
                        const filePath = path.join(typeDir, file.name);
                        const metaPath = filePath + '.meta.json';

                        let metadata = {};
                        if (fs.existsSync(metaPath)) {
                            try {
                                metadata = JSON.parse(fs.readFileSync(metaPath, 'utf8'));
                            } catch (error) {
                                logger.warn(`⚠️ Failed to read metadata for ${file.name}`);
                            }
                        }

                        const stats = fs.statSync(filePath);
                        checkpoints.push({
                            id: file.name,
                            path: path.relative(this.baseDir, filePath),
                            size: stats.size,
                            lastModified: stats.mtime,
                            metadata
                        });
                    }
                }
            }

            // Add cache info if requested
            if (includeCache) {
                const cacheEntries = Array.from(this.cache.entries())
                    .filter(([key]) => key.startsWith(`${type}:`))
                    .map(([key, value]) => ({
                        id: key.split(':')[1],
                        cached: true,
                        cacheTimestamp: value.timestamp,
                        size: value.size,
                        metadata: value.metadata
                    }));

                checkpoints.forEach(checkpoint => {
                    const cacheEntry = cacheEntries.find(ce => ce.id === checkpoint.id);
                    if (cacheEntry) {
                        checkpoint.cached = true;
                        checkpoint.cacheTimestamp = cacheEntry.cacheTimestamp;
                    }
                });
            }

            results[type] = checkpoints;
        }

        return modelType ? results[modelType] : results;
    }

    /**
     * Delete checkpoint
     */
    async deleteCheckpoint(modelType, checkpointId, options = {}) {
        const { deleteFromCache = true } = options;

        const checkpointPath = this._getCheckpointPath(modelType, checkpointId);
        const fullPath = path.join(this.baseDir, checkpointPath);
        const metaPath = fullPath + '.meta.json';

        let deleted = false;

        // Delete from disk
        if (fs.existsSync(fullPath)) {
            fs.unlinkSync(fullPath);
            deleted = true;
        }

        if (fs.existsSync(metaPath)) {
            fs.unlinkSync(metaPath);
        }

        // Delete from cache
        if (deleteFromCache) {
            this.cache.delete(`${modelType}:${checkpointId}`);
        }

        logger.info(`🗑️ Checkpoint deleted: ${checkpointPath}`);

        return {
            success: true,
            deleted,
            path: checkpointPath
        };
    }

    /**
     * Clear cache
     */
    async clearCache(modelType = null) {
        let cleared = 0;

        if (modelType) {
            // Clear specific model type
            const keysToDelete = Array.from(this.cache.keys())
                .filter(key => key.startsWith(`${modelType}:`));

            keysToDelete.forEach(key => {
                this.cache.delete(key);
                cleared++;
            });
        } else {
            // Clear all cache
            cleared = this.cache.size;
            this.cache.clear();
        }

        logger.info(`🧹 Cleared ${cleared} cache entries`);

        return {
            success: true,
            cleared,
            modelType: modelType || 'all'
        };
    }

    /**
     * Get cache statistics
     */
    async getCacheStats() {
        const stats = {
            totalEntries: this.cache.size,
            totalSize: 0,
            byModelType: {},
            oldestEntry: null,
            newestEntry: null
        };

        let oldestTimestamp = Date.now();
        let newestTimestamp = 0;

        for (const [key, value] of this.cache.entries()) {
            const [modelType] = key.split(':');

            if (!stats.byModelType[modelType]) {
                stats.byModelType[modelType] = {
                    entries: 0,
                    size: 0
                };
            }

            stats.byModelType[modelType].entries++;
            stats.byModelType[modelType].size += value.size;
            stats.totalSize += value.size;

            if (value.timestamp < oldestTimestamp) {
                oldestTimestamp = value.timestamp;
                stats.oldestEntry = key;
            }

            if (value.timestamp > newestTimestamp) {
                newestTimestamp = value.timestamp;
                stats.newestEntry = key;
            }
        }

        return stats;
    }

    // Private utility methods

    /**
     * Download with retry logic
     */
    async _downloadWithRetry(url, retryAttempts, onProgress) {
        let lastError;

        for (let attempt = 1; attempt <= retryAttempts; attempt++) {
            try {
                logger.debug(`📥 Download attempt ${attempt}/${retryAttempts}: ${url}`);

                const response = await axios.get(url, {
                    responseType: 'arraybuffer',
                    timeout: 30000,
                    onDownloadProgress: (progressEvent) => {
                        if (progressEvent.total) {
                            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                            onProgress(progress);
                        }
                    }
                });

                if (response.status === 200) {
                    return response.data;
                }

                throw new Error(`HTTP ${response.status}: ${response.statusText}`);

            } catch (error) {
                lastError = error;
                logger.warn(`⚠️ Download attempt ${attempt} failed:`, error.message);

                if (attempt < retryAttempts) {
                    const delay = CACHE_CONFIG.retryDelay * attempt;
                    logger.debug(`⏳ Waiting ${delay}ms before retry...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }

        throw new Error(`Download failed after ${retryAttempts} attempts: ${lastError.message}`);
    }

    /**
     * Get checkpoint file path
     */
    _getCheckpointPath(modelType, checkpointId) {
        const config = MODEL_TYPES[modelType];
        return path.join(config.baseDir, checkpointId);
    }

    /**
     * Enforce cache size limit
     */
    async _enforceCacheLimit(modelType) {
        const cacheEntries = Array.from(this.cache.entries())
            .filter(([key]) => key.startsWith(`${modelType}:`));

        const totalSize = cacheEntries.reduce((sum, [, value]) => sum + value.size, 0);

        if (totalSize > this.maxCacheSize) {
            // Sort by timestamp (oldest first)
            cacheEntries.sort((a, b) => a[1].timestamp - b[1].timestamp);

            let removedSize = 0;
            let removed = 0;

            while (removedSize < (totalSize - this.maxCacheSize) && removed < cacheEntries.length) {
                const [key, value] = cacheEntries[removed];
                this.cache.delete(key);
                removedSize += value.size;
                removed++;
            }

            logger.info(`🧹 Evicted ${removed} entries (${removedSize} bytes) from ${modelType} cache`);
        }
    }

    /**
     * Get Express router
     */
    getRouter() {
        return this.router;
    }
}

/**
 * Create checkpoint manager middleware
 */
export function createCheckpointManager(options = {}) {
    const manager = new CheckpointManager(options);
    return manager.getRouter();
}

export default CheckpointManager;
