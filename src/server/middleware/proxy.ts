/**
 * Proxy Middleware - Refactored with Generalized Architecture
 * 
 * This file now provides a clean interface to the generalized WebSocket proxy system.
 * The heavy lifting is done by BaseWebSocketProxy and provider-specific extensions.
 * HTTP route proxying is handled by the unified route system.
 */

import type http from 'http';

// Import the new generalized proxy architecture
import { attachAliyunRealtimeProxy } from './AliyunRealtimeProxy.js';

/**
 * Attach Aliyun realtime WebSocket proxy to an existing HTTP server instance.
 * 
 * REFACTORED: This now delegates to the generalized AliyunRealtimeProxy class
 * which extends BaseWebSocketProxy for better maintainability and extensibility.
 *
 * @param server Existing HTTP/S server returned by `http.createServer()`
 * @param path   WebSocket path to mount the proxy on. Default: `/ws`.
 */
export function attachAliyunRealtimeProxyLegacy(server: http.Server, path: string = '/ws') {
  // Delegate to the new generalized architecture
  attachAliyunRealtimeProxy(server, path);
}

// Maintain backward compatibility by re-exporting the main function
export { attachAliyunRealtimeProxy };

/**
 * Legacy HTTP proxy - REMOVED
 * This functionality is now handled by routes/llm.ts at /api/llm/chat/completions
 * Keeping this comment for reference - the actual proxy logic has been consolidated
 */
