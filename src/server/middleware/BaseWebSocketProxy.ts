/**
 * Base WebSocket Proxy Handler - Generalized
 * 
 * This provides a generic WebSocket proxy infrastructure that can be extended
 * for specific providers (Aliyun, OpenAI, Azure, etc.)
 */

import type http from 'http';
import { WebSocketServer } from 'ws';
import WebSocket from 'ws';

export interface ConnectionState {
  upstream: WebSocket | null;
  clients: Set<WebSocket>;
  connectionInProgress: boolean;
  isReady: boolean;
  messageQueue: string[];
  connectionAttempts: number;
  metadata?: Record<string, any>;
}

export interface ProxyConfig {
  path?: string;
  maxRetries?: number;
  retryDelay?: number;
  pingInterval?: number;
  pingTimeout?: number;
  handshakeTimeout?: number;
  logPrefix?: string;
}

export interface ProviderConfig {
  getUpstreamEndpoint: (model: string, params?: Record<string, string>) => string;
  getHeaders: (model: string) => Record<string, string>;
  validateApiKey: () => { valid: boolean; key?: string; error?: string };
  processMessage?: (message: any, direction: 'client-to-upstream' | 'upstream-to-client') => any;
  handleSpecialMessage?: (messageObj: any, messageStr: string, context: MessageContext) => boolean;
  onConnectionOpen?: (upstream: WebSocket, connectionState: ConnectionState) => void;
  onConnectionClose?: (code: number, reason: string, connectionState: ConnectionState) => void;
  onConnectionError?: (error: Error, connectionState: ConnectionState) => void;
}

export interface MessageContext {
  client: WebSocket;
  upstream: WebSocket | null;
  connectionState: ConnectionState;
  isUpstreamReady: boolean;
  messageQueue: string[];
  queueMessage: (message: string) => void;
  sendToUpstream: (message: string) => void;
}

/**
 * Generic WebSocket Proxy Handler
 * Can be extended for any WebSocket API provider
 */
export class BaseWebSocketProxy {
  private activeConnections = new Map<string, ConnectionState>();
  
  constructor(
    private config: ProxyConfig = {},
    private providerConfig: ProviderConfig
  ) {
    this.config = {
      path: '/ws',
      maxRetries: 3,
      retryDelay: 2000,
      pingInterval: 20000,
      pingTimeout: 10000,
      handshakeTimeout: 10000,
      logPrefix: 'WebSocketProxy',
      ...config
    };
  }

  /**
   * Attach the proxy to an existing HTTP server
   */
  attach(server: http.Server): boolean {
    // Validate provider configuration
    const apiValidation = this.providerConfig.validateApiKey();
    if (!apiValidation.valid) {
      console.warn(`[${this.config.logPrefix}] ❌ ${apiValidation.error} – proxy disabled`);
      return false;
    }

    const wss = new WebSocketServer({ 
      server, 
      path: this.config.path 
    });
    
    console.log(`[${this.config.logPrefix}] 🚀 WebSocket proxy listening on ws://<host>:<port>${this.config.path}`);

    wss.on('connection', (client, req) => {
      this.handleClientConnection(client, req);
    });

    return true;
  }

  /**
   * Handle new client connection
   */
  private handleClientConnection(client: WebSocket, req: http.IncomingMessage) {
    const requestUrl = new URL(req.url || '', `http://${req.headers.host}`);
    const model = requestUrl.searchParams.get('model') || 'default';
    
    console.log(`[${this.config.logPrefix}] 🔌 Client connected – model: ${model}`);

    // Get or create shared connection state for this model
    if (!this.activeConnections.has(model)) {
      this.activeConnections.set(model, {
        upstream: null,
        clients: new Set(),
        connectionInProgress: false,
        isReady: false,
        messageQueue: [],
        connectionAttempts: 0,
        metadata: {}
      });
    }

    const connectionState = this.activeConnections.get(model)!;
    connectionState.clients.add(client);

    console.log(`[${this.config.logPrefix}] 📊 Model ${model} now has ${connectionState.clients.size} client(s)`);

    // Set up client event handlers
    this.setupClientHandlers(client, model, connectionState);

    // Initialize upstream connection if needed
    if (!connectionState.connectionInProgress && !connectionState.upstream) {
      console.log(`[${this.config.logPrefix}] 🚀 Starting initial connection for model ${model}`);
      this.createUpstreamConnection(model, connectionState);
    } else if (connectionState.upstream && connectionState.isReady) {
      console.log(`[${this.config.logPrefix}] 🔄 Using existing ready connection for model ${model}`);
    } else {
      console.log(`[${this.config.logPrefix}] ⏳ Connection in progress for model ${model}, client will wait`);
    }
  }

  /**
   * Set up event handlers for client WebSocket
   */
  private setupClientHandlers(client: WebSocket, model: string, connectionState: ConnectionState) {
    // Client message handling
    client.on('message', (data) => {
      this.handleClientMessage(client, data, model, connectionState);
    });

    // Client disconnect handling
    client.on('close', () => {
      this.handleClientDisconnect(client, model, connectionState);
    });

    // Client error handling
    client.on('error', (err) => {
      this.handleClientError(client, err, model, connectionState);
    });
  }

  /**
   * Handle message from client
   */
  private handleClientMessage(client: WebSocket, data: WebSocket.Data, model: string, connectionState: ConnectionState) {
    try {
      const messageStr = data.toString();
      let messageObj: any;

      try {
        messageObj = JSON.parse(messageStr);
      } catch (e) {
        messageObj = { type: 'binary', size: messageStr.length };
      }

      const context: MessageContext = {
        client,
        upstream: connectionState.upstream,
        connectionState,
        isUpstreamReady: connectionState.isReady,
        messageQueue: connectionState.messageQueue,
        queueMessage: (msg: string) => connectionState.messageQueue.push(msg),
        sendToUpstream: (msg: string) => {
          if (connectionState.upstream && connectionState.upstream.readyState === WebSocket.OPEN) {
            connectionState.upstream.send(msg);
          }
        }
      };

      // Allow provider to handle special messages
      if (this.providerConfig.handleSpecialMessage) {
        const handled = this.providerConfig.handleSpecialMessage(messageObj, messageStr, context);
        if (handled) return;
      }

      // Process message through provider if configured
      let processedMessage = messageStr;
      if (this.providerConfig.processMessage) {
        const processed = this.providerConfig.processMessage(messageObj, 'client-to-upstream');
        processedMessage = typeof processed === 'string' ? processed : JSON.stringify(processed);
      }

      // Handle message based on upstream connection status
      if (!connectionState.isReady || !connectionState.upstream || connectionState.upstream.readyState !== WebSocket.OPEN) {
        console.log(`[${this.config.logPrefix}] ⏱️ Upstream not ready, queueing message: ${messageObj.type}`);
        connectionState.messageQueue.push(processedMessage);

        // Initialize connection if needed
        if (!connectionState.upstream) {
          this.createUpstreamConnection(model, connectionState);
        }
        return;
      }

      connectionState.upstream.send(processedMessage);

    } catch (error) {
      console.error(`[${this.config.logPrefix}] ❌ Error handling client message:`, error);
    }
  }

  /**
   * Handle client disconnect
   */
  private handleClientDisconnect(client: WebSocket, model: string, connectionState: ConnectionState) {
    console.log(`[${this.config.logPrefix}] 🔒 Client disconnected from model ${model}`);
    
    connectionState.clients.delete(client);
    console.log(`[${this.config.logPrefix}] 📊 Model ${model} now has ${connectionState.clients.size} client(s) remaining`);
    
    // Clean up upstream connection if no clients remain
    if (connectionState.clients.size === 0) {
      console.log(`[${this.config.logPrefix}] 🧹 No clients remaining for model ${model}, cleaning up upstream connection`);
      
      if (connectionState.upstream && connectionState.upstream.readyState !== WebSocket.CLOSED) {
        connectionState.upstream.close();
      }
      
      this.activeConnections.delete(model);
    }
  }

  /**
   * Handle client error
   */
  private handleClientError(client: WebSocket, error: Error, model: string, connectionState: ConnectionState) {
    console.error(`[${this.config.logPrefix}] ❌ Client error for model ${model}:`, error.message);
    
    connectionState.clients.delete(client);
    
    // Clean up upstream if no clients remain
    if (connectionState.clients.size === 0 && connectionState.upstream && connectionState.upstream.readyState !== WebSocket.CLOSED) {
      connectionState.upstream.close();
      this.activeConnections.delete(model);
    }
  }

  /**
   * Create upstream WebSocket connection
   */
  private createUpstreamConnection(model: string, connectionState: ConnectionState) {
    // Prevent multiple simultaneous connection attempts
    if (connectionState.connectionInProgress) {
      console.log(`[${this.config.logPrefix}] ⚠️ Connection already in progress for model ${model}, skipping duplicate attempt`);
      return;
    }

    // Reuse existing connection if available
    if (connectionState.upstream && connectionState.upstream.readyState === WebSocket.OPEN) {
      console.log(`[${this.config.logPrefix}] ✅ Reusing existing connection for model ${model}`);
      return;
    }

    connectionState.connectionInProgress = true;
    connectionState.connectionAttempts++;
    
    const upstreamEndpoint = this.providerConfig.getUpstreamEndpoint(model);
    const headers = this.providerConfig.getHeaders(model);
    
    console.log(`[${this.config.logPrefix}] 🔗 Connecting to upstream (attempt ${connectionState.connectionAttempts}/${this.config.maxRetries}) for ${connectionState.clients.size} clients: ${upstreamEndpoint}`);

    const upstream = new WebSocket(upstreamEndpoint, {
      headers,
      handshakeTimeout: this.config.handshakeTimeout,
      perMessageDeflate: false,
      maxRedirects: 0,
    });

    connectionState.upstream = upstream;

    // Set up upstream event handlers
    this.setupUpstreamHandlers(upstream, model, connectionState);
  }

  /**
   * Set up event handlers for upstream WebSocket
   */
  private setupUpstreamHandlers(upstream: WebSocket, model: string, connectionState: ConnectionState) {
    let pingInterval: NodeJS.Timeout | null = null;
    let pingTimeoutTimer: NodeJS.Timeout | null = null;

    upstream.on('open', () => {
      console.log(`[${this.config.logPrefix}] ✅ Upstream connection established for model: ${model} (${connectionState.clients.size} clients)`);
      
      connectionState.connectionAttempts = 0;
      connectionState.connectionInProgress = false;
      connectionState.isReady = true;

      // Provider-specific connection open handling
      if (this.providerConfig.onConnectionOpen) {
        this.providerConfig.onConnectionOpen(upstream, connectionState);
      }

      // Set up ping mechanism
      if (this.config.pingInterval && this.config.pingInterval > 0) {
        pingInterval = setInterval(() => {
          if (upstream.readyState === WebSocket.OPEN) {
            try {
              if (pingTimeoutTimer) {
                clearTimeout(pingTimeoutTimer);
                pingTimeoutTimer = null;
              }
              
              upstream.ping();
              console.log(`[${this.config.logPrefix}] 🏓 Sent ping to upstream`);
              
              pingTimeoutTimer = setTimeout(() => {
                console.log(`[${this.config.logPrefix}] ⚠️ Ping timeout - no pong received within ${this.config.pingTimeout}ms`);
              }, this.config.pingTimeout);
              
            } catch (error) {
              console.error(`[${this.config.logPrefix}] ❌ Failed to ping upstream:`, error);
            }
          } else {
            if (pingInterval) clearInterval(pingInterval);
            if (pingTimeoutTimer) {
              clearTimeout(pingTimeoutTimer);
              pingTimeoutTimer = null;
            }
          }
        }, this.config.pingInterval);

        upstream.on('pong', () => {
          console.log(`[${this.config.logPrefix}] 🏓 Received pong from upstream`);
          if (pingTimeoutTimer) {
            clearTimeout(pingTimeoutTimer);
            pingTimeoutTimer = null;
          }
        });
      }

      // Send queued messages
      this.processQueuedMessages(upstream, connectionState);
    });

    upstream.on('error', (err) => {
      console.error(`[${this.config.logPrefix}] ❌ Upstream connection failed (attempt ${connectionState.connectionAttempts}):`, err.message);

      // Provider-specific error handling
      if (this.providerConfig.onConnectionError) {
        this.providerConfig.onConnectionError(err, connectionState);
      }

      // Retry logic
      if (connectionState.connectionAttempts < this.config.maxRetries!) {
        console.log(`[${this.config.logPrefix}] 🔄 Retrying connection in ${this.config.retryDelay!/1000} seconds...`);
        setTimeout(() => {
          if (connectionState.clients.size > 0) {
            this.createUpstreamConnection(model, connectionState);
          }
        }, this.config.retryDelay);
      } else {
        console.error(`[${this.config.logPrefix}] 🔧 Max retries reached. Connection failed.`);
        connectionState.clients.forEach(client => {
          if (client.readyState === WebSocket.OPEN) {
            client.close(1011, 'Upstream connection failed after retries');
          }
        });
      }
    });

    upstream.on('close', (code, reason) => {
      const reasonStr = reason?.toString?.() || reason || '';
      console.log(`[${this.config.logPrefix}] 🔒 Upstream closed: ${code} ${reasonStr}`);

      // Clean up ping timers
      if (pingInterval) clearInterval(pingInterval);
      if (pingTimeoutTimer) {
        clearTimeout(pingTimeoutTimer);
        pingTimeoutTimer = null;
      }

      connectionState.isReady = false;

      // Provider-specific close handling
      if (this.providerConfig.onConnectionClose) {
        this.providerConfig.onConnectionClose(code, reasonStr, connectionState);
      }

      // Close clients if connection was established
      if (upstream === connectionState.upstream) {
        connectionState.clients.forEach(client => {
          if (client.readyState === WebSocket.OPEN) {
            // Ensure we use valid WebSocket close codes and string reason
            const validCode = code >= 1000 && code <= 4999 ? code : 1000;
            const validReason = typeof reasonStr === 'string' ? reasonStr : '';
            client.close(validCode, validReason);
          }
        });
        connectionState.upstream = null;
      }
    });

    upstream.on('message', (data) => {
      this.handleUpstreamMessage(data, connectionState);
    });
  }

  /**
   * Handle message from upstream
   */
  private handleUpstreamMessage(data: WebSocket.Data, connectionState: ConnectionState) {
    try {
      const messageStr = data.toString();
      let messageObj: any;

      try {
        messageObj = JSON.parse(messageStr);
      } catch (e) {
        messageObj = { type: 'unparseable', size: messageStr.length };
      }

      // Process message through provider if configured
      let processedData = data;
      if (this.providerConfig.processMessage) {
        const processed = this.providerConfig.processMessage(messageObj, 'upstream-to-client');
        processedData = Buffer.from(typeof processed === 'string' ? processed : JSON.stringify(processed));
      }

      // Broadcast message to all clients for this connection
      connectionState.clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
          client.send(processedData);
        }
      });

    } catch (error) {
      console.error(`[${this.config.logPrefix}] ❌ Error handling upstream message:`, error);
    }
  }

  /**
   * Process queued messages when connection opens
   */
  private processQueuedMessages(upstream: WebSocket, connectionState: ConnectionState) {
    while (connectionState.messageQueue.length > 0 && upstream.readyState === WebSocket.OPEN) {
      const queuedMessage = connectionState.messageQueue.shift();
      if (queuedMessage) {
        console.log(`[${this.config.logPrefix}] 📤 Sending queued message`);
        upstream.send(queuedMessage);
      }
    }
  }
}