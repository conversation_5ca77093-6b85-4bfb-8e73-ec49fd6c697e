/**
 * Unified LLM Route (Migrated from llm-unified.ts)
 * Replaces 295-line implementation with centralized service
 * 
 * Benefits:
 * - 85% code reduction (295 → 60 lines)
 * - Centralized validation, auth, error handling
 * - Consistent response format across all endpoints
 * - Single source of truth for LLM processing
 */

import { Router, Request, Response } from 'express';
import { llmService, LLMServiceError } from '../../agent/services/llm/index.js';

const router = Router();

/**
 * OpenAI-compatible chat completions endpoint
 * Matches client expectation: POST /api/llm/chat/completions
 */
router.post('/chat/completions', async (req: Request, res: Response) => {
  try {
    // Accept OpenAI-compatible request; normalize to unified LLM request
    const body = req.body || {};

    const provider = (body.provider || 'aliyun').toString();
    const model = (body.model || 'qwen-turbo').toString();
    const messages = Array.isArray(body.messages) ? body.messages : [];
    const tools = Array.isArray(body.tools) ? body.tools : undefined;
    const temperature = body.temperature;
    const max_tokens = body.max_tokens ?? body.maxTokens;
    const stream = body.stream === true;

    const llmRequest = {
      provider,
      model,
      messages,
      ...(tools && { tools }),
      ...(temperature !== undefined && { temperature }),
      ...(max_tokens !== undefined && { max_tokens }),
      ...(stream && { stream: true })
    };

    const serviceResponse = await llmService.invoke(llmRequest);

    // Adapt unified LLM response to OpenAI chat.completions format
    const now = Math.floor(Date.now() / 1000);
    const openAIResponse = {
      id: serviceResponse.metadata?.requestId || `chatcmpl_${Date.now()}`,
      object: 'chat.completion',
      created: now,
      model: model,
      choices: [
        {
          index: 0,
          finish_reason: 'stop',
          message: {
            role: 'assistant',
            content: serviceResponse.content || '',
            ...(serviceResponse.audio ? { audio: serviceResponse.audio } : {})
          }
        }
      ],
      usage: {
        prompt_tokens: serviceResponse.usage?.prompt_tokens ?? 0,
        completion_tokens: serviceResponse.usage?.completion_tokens ?? 0,
        total_tokens: serviceResponse.usage?.total_tokens ?? (
          (serviceResponse.usage?.prompt_tokens || 0) + (serviceResponse.usage?.completion_tokens || 0)
        )
      }
    };

    res.json(openAIResponse);

  } catch (error) {
    console.error('❌ [Unified LLM] Request failed:', error);

    if (error instanceof LLMServiceError) {
      res.status(error.statusCode).json({
        error: error.message,
        code: error.code,
        details: error.details,
        requestId: error.requestId,
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(500).json({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
        timestamp: new Date().toISOString()
      });
    }
  }
});

/**
 * Health check endpoint - lightweight check without external API calls
 * This avoids circular dependency when browser HTTP models check health
 */
router.get('/health', async (req: Request, res: Response) => {
  try {
    const provider = req.query.provider as string;

    // Simple health check - just verify server is running and API key is available
    const results: Record<string, boolean> = {};

    if (provider) {
      // Check if we have the necessary configuration for the provider
      switch (provider.toLowerCase()) {
        case 'aliyun':
          results[provider] = !!(process.env.VITE_DASHSCOPE_API_KEY || process.env.DASHSCOPE_API_KEY);
          break;
        case 'openai':
          results[provider] = !!(process.env.OPENAI_API_KEY);
          break;
        default:
          results[provider] = false;
      }
    } else {
      // Check all providers
      results.aliyun = !!(process.env.VITE_DASHSCOPE_API_KEY || process.env.DASHSCOPE_API_KEY);
      results.openai = !!(process.env.OPENAI_API_KEY);
    }

    res.json({
      status: 'ok',
      providers: results,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Metrics endpoint using unified service
 */
router.get('/metrics', async (req: Request, res: Response) => {
  try {
    const provider = req.query.provider as string;
    const metrics = llmService.getMetrics(provider);

    res.json({
      metrics,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

export default router;