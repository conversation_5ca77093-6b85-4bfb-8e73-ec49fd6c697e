/**
 * LangGraph Memory Management for Hologram Avatar
 * Implements proper LangGraph memory patterns using MemorySaver and InMemoryStore
 * Based on official LangGraph documentation: https://langchain-ai.github.io/langgraphjs/concepts/memory/
 * 
 * Note: langmem.short_term (SummarizationNode, RunningSummary) is Python-only
 * This implements equivalent functionality using @langchain/langgraph JavaScript capabilities
 */

import { MemorySaver, InMemoryStore } from '@langchain/langgraph';
import { v4 as uuidv4 } from 'uuid';
import { createLogger, LogLevel } from '@/utils/logger';

const logger = createLogger('LangGraphMemory', LogLevel.DEBUG);

/**
 * LangGraph Memory Manager
 * Implements both short-term (thread-scoped) and long-term (cross-thread) memory
 */
export class LangGraphMemoryManager {
  constructor(options = {}) {
    this.options = options;

    // Short-term memory using MemorySaver (thread-scoped state persistence)
    this.checkpointer = new MemorySaver();

    // Long-term memory using InMemoryStore (cross-thread persistent storage)
    this.store = new InMemoryStore();

    this.initialized = true;

    logger.info('LangGraph Memory Manager initialized with MemorySaver and InMemoryStore');
  }

  /**
   * Get the checkpointer for short-term memory (thread-scoped state)
   * @returns {MemorySaver} The MemorySaver checkpointer instance
   */
  getCheckpointer() {
    return this.checkpointer;
  }

  /**
   * Get the store for long-term memory (cross-thread persistence)
   * @returns {InMemoryStore} The InMemoryStore instance
   */
  getStore() {
    return this.store;
  }

  /**
   * Add long-term memories (cross-thread persistence)
   * @param {string} userId - User identifier
   * @param {Array|Object} memories - Memory content to store
   * @param {string} context - Optional context (e.g., 'conversation', 'preferences')
   * @returns {Promise<string[]>} Array of memory IDs
   */
  async addMemories(userId, memories, context = 'general') {
    if (!this.initialized) {
      logger.warn('Memory manager not initialized');
      return [];
    }

    try {
      const namespace = [userId, 'memories', context];
      const memoryIds = [];

      // Ensure memories is an array
      const memoryArray = Array.isArray(memories) ? memories : [memories];

      for (const memory of memoryArray) {
        const memoryId = uuidv4();
        const memoryData = {
          content: typeof memory === 'string' ? memory : memory.content || JSON.stringify(memory),
          timestamp: Date.now(),
          context,
          userId,
          metadata: memory.metadata || {}
        };

        await this.store.put(namespace, memoryId, memoryData);
        memoryIds.push(memoryId);

        logger.debug(`Added memory ${memoryId} for user ${userId} in context ${context}`);
      }

      logger.info(`Added ${memoryIds.length} memories for user ${userId}`);
      return memoryIds;

    } catch (error) {
      logger.error('Failed to add memories:', error);
      return [];
    }
  }

  /**
   * Search long-term memories
   * @param {string} userId - User identifier
   * @param {Object} options - Search options
   * @param {string} options.context - Memory context to search in
   * @param {Object} options.filter - Additional filter criteria
   * @param {number} options.limit - Maximum number of results
   * @returns {Promise<Array>} Array of memory objects
   */
  async searchMemories(userId, options = {}) {
    if (!this.initialized) {
      logger.warn('Memory manager not initialized');
      return [];
    }

    try {
      const { context = 'general', filter = {}, limit } = options;
      const namespace = [userId, 'memories', context];

      const searchOptions = {
        filter: {
          userId,
          ...filter
        }
      };

      if (limit) {
        searchOptions.limit = limit;
      }

      const results = await this.store.search(namespace, searchOptions);

      logger.debug(`Found ${results.length} memories for user ${userId} in context ${context}`);
      return results.map(result => ({
        id: result.key,
        ...result.value,
        namespace: result.namespace,
        createdAt: result.created_at,
        updatedAt: result.updated_at
      }));

    } catch (error) {
      logger.error('Failed to search memories:', error);
      return [];
    }
  }

  /**
   * Get all memories for a user
   * @param {string} userId - User identifier
   * @param {string} context - Optional context filter
   * @returns {Promise<Array>} Array of all user memories
   */
  async getAllMemories(userId, context) {
    if (!this.initialized) {
      logger.warn('Memory manager not initialized');
      return [];
    }

    try {
      let memories = [];

      if (context) {
        // Get memories for specific context
        const namespace = [userId, 'memories', context];
        const results = await this.store.search(namespace);
        memories = results.map(result => ({
          id: result.key,
          ...result.value,
          namespace: result.namespace,
          createdAt: result.created_at,
          updatedAt: result.updated_at
        }));
      } else {
        // Get memories across all contexts
        const contexts = ['general', 'conversation', 'preferences', 'tasks'];
        for (const ctx of contexts) {
          const contextMemories = await this.searchMemories(userId, { context: ctx });
          memories = memories.concat(contextMemories);
        }
      }

      logger.info(`Retrieved ${memories.length} memories for user ${userId}`);
      return memories;

    } catch (error) {
      logger.error('Failed to get all memories:', error);
      return [];
    }
  }

  /**
   * Update a specific memory
   * @param {string} userId - User identifier
   * @param {string} memoryId - Memory ID to update
   * @param {Object} updateData - Data to update
   * @param {string} context - Memory context
   * @returns {Promise<boolean>} Success status
   */
  async updateMemory(userId, memoryId, updateData, context = 'general') {
    if (!this.initialized) {
      logger.warn('Memory manager not initialized');
      return false;
    }

    try {
      const namespace = [userId, 'memories', context];

      // Get existing memory
      const existingMemory = await this.store.get(namespace, memoryId);
      if (!existingMemory) {
        logger.warn(`Memory ${memoryId} not found for user ${userId}`);
        return false;
      }

      // Update memory
      const updatedMemory = {
        ...existingMemory.value,
        ...updateData,
        updatedAt: Date.now()
      };

      await this.store.put(namespace, memoryId, updatedMemory);

      logger.debug(`Updated memory ${memoryId} for user ${userId}`);
      return true;

    } catch (error) {
      logger.error('Failed to update memory:', error);
      return false;
    }
  }

  /**
   * Delete a specific memory
   * @param {string} userId - User identifier
   * @param {string} memoryId - Memory ID to delete
   * @param {string} context - Memory context
   * @returns {Promise<boolean>} Success status
   */
  async deleteMemory(userId, memoryId, context = 'general') {
    if (!this.initialized) {
      logger.warn('Memory manager not initialized');
      return false;
    }

    try {
      const namespace = [userId, 'memories', context];
      await this.store.delete(namespace, memoryId);

      logger.debug(`Deleted memory ${memoryId} for user ${userId}`);
      return true;

    } catch (error) {
      logger.error('Failed to delete memory:', error);
      return false;
    }
  }

  /**
   * Clear all memories for a user
   * @param {string} userId - User identifier
   * @param {string} context - Optional context to clear (if not provided, clears all contexts)
   * @returns {Promise<boolean>} Success status
   */
  async clearMemories(userId, context) {
    if (!this.initialized) {
      logger.warn('Memory manager not initialized');
      return false;
    }

    try {
      if (context) {
        // Clear specific context
        const namespace = [userId, 'memories', context];
        const memories = await this.store.search(namespace);

        for (const memory of memories) {
          await this.store.delete(namespace, memory.key);
        }

        logger.info(`Cleared ${memories.length} memories for user ${userId} in context ${context}`);
      } else {
        // Clear all contexts
        const contexts = ['general', 'conversation', 'preferences', 'tasks'];
        let totalCleared = 0;

        for (const ctx of contexts) {
          const namespace = [userId, 'memories', ctx];
          const memories = await this.store.search(namespace);

          for (const memory of memories) {
            await this.store.delete(namespace, memory.key);
          }

          totalCleared += memories.length;
        }

        logger.info(`Cleared ${totalCleared} memories for user ${userId} across all contexts`);
      }

      return true;

    } catch (error) {
      logger.error('Failed to clear memories:', error);
      return false;
    }
  }

  /**
   * Get memory statistics for a user
   * @param {string} userId - User identifier
   * @returns {Promise<Object>} Memory statistics
   */
  async getMemoryStats(userId) {
    if (!this.initialized) {
      logger.warn('Memory manager not initialized');
      return {};
    }

    try {
      const contexts = ['general', 'conversation', 'preferences', 'tasks'];
      const stats = {
        userId,
        totalMemories: 0,
        byContext: {},
        oldestMemory: null,
        newestMemory: null
      };

      let allMemories = [];

      for (const context of contexts) {
        const contextMemories = await this.searchMemories(userId, { context });
        stats.byContext[context] = contextMemories.length;
        stats.totalMemories += contextMemories.length;
        allMemories = allMemories.concat(contextMemories);
      }

      if (allMemories.length > 0) {
        // Sort by timestamp to find oldest and newest
        allMemories.sort((a, b) => a.timestamp - b.timestamp);
        stats.oldestMemory = allMemories[0];
        stats.newestMemory = allMemories[allMemories.length - 1];
      }

      return stats;

    } catch (error) {
      logger.error('Failed to get memory stats:', error);
      return {};
    }
  }

  /**
   * Format memories for LLM context
   * @param {Array} memories - Array of memory objects
   * @param {Object} options - Formatting options
   * @returns {string} Formatted memory string
   */
  formatMemoriesForLLM(memories, options = {}) {
    if (!memories || memories.length === 0) {
      return '';
    }

    const {
      includeTimestamp = true,
      includeContext = true,
      separator = '\n'
    } = options;

    return memories.map(memory => {
      let formatted = memory.content;

      if (includeContext && memory.context) {
        formatted += ` [Context: ${memory.context}]`;
      }

      if (includeTimestamp && memory.timestamp) {
        const date = new Date(memory.timestamp).toISOString();
        formatted += ` [${date}]`;
      }

      return formatted;
    }).join(separator);
  }

  /**
   * Get memory context for LangGraph prompts following official LangGraph patterns
   * Based on: https://langchain-ai.github.io/langgraphjs/how-tos/memory/add-memory/
   * @param {string} sessionId - Session identifier (used as thread_id for checkpointer)
   * @param {string} userId - User identifier (for long-term memories)
   * @param {Object} options - Configuration options
   * @returns {Promise<Object>} Memory context with conversation history and user memories
   */
  async getMemoryContext(sessionId, userId, options = {}) {
    try {
      const {
        includeConversationHistory = true,
        includeUserMemories = true,
        maxConversationHistory = 10,
        maxUserMemories = 5
      } = options;

      const context = {
        conversation_history: [],
        user_memories: [],
        memory_stats: {},
        memory_type: 'langgraph'
      };

      // Get conversation history from checkpointer (short-term memory)
      if (includeConversationHistory && sessionId) {
        try {
          const config = {
            configurable: {
              thread_id: sessionId,
              user_id: userId
            }
          };

          // List recent checkpoints for this thread
          const checkpoints = [];
          for await (const checkpoint of this.checkpointer.list(config)) {
            checkpoints.push(checkpoint);
            if (checkpoints.length >= maxConversationHistory) break;
          }

          // Extract messages from checkpoints
          context.conversation_history = checkpoints
            .reverse() // Most recent first
            .map(cp => cp.checkpoint?.channel_values?.messages || [])
            .flat()
            .slice(-maxConversationHistory);

        } catch (error) {
          logger.warn('Failed to retrieve conversation history:', error);
        }
      }

      // Get user memories from store (long-term memory)
      if (includeUserMemories && userId) {
        try {
          const namespace = ['memories', userId];

          // Search for relevant memories
          const memories = await this.store.search(namespace);

          context.user_memories = memories
            .slice(-maxUserMemories) // Get most recent memories
            .map(item => ({
              id: item.key,
              content: item.value.content || item.value.data || JSON.stringify(item.value),
              context: item.value.context || 'general',
              timestamp: item.created_at || item.value.timestamp,
              namespace: item.namespace
            }));

        } catch (error) {
          logger.warn('Failed to retrieve user memories:', error);
        }
      }

      // Generate memory statistics
      context.memory_stats = {
        userId: userId,
        sessionId: sessionId,
        conversationMessages: context.conversation_history.length,
        userMemories: context.user_memories.length,
        timestamp: new Date().toISOString()
      };

      logger.debug('Memory context retrieved:', {
        sessionId,
        userId,
        conversationHistory: context.conversation_history.length,
        userMemories: context.user_memories.length
      });

      return context;

    } catch (error) {
      logger.error('Error getting memory context:', error);
      return {
        conversation_history: [],
        user_memories: [],
        memory_stats: { error: error.message },
        memory_type: 'langgraph'
      };
    }
  }

  /**
   * Add conversation turn to memory (following LangGraph patterns)
   * @param {string} sessionId - Session identifier
   * @param {string} userId - User identifier  
   * @param {string} userMessage - User message
   * @param {string} aiResponse - AI response
   * @param {Object} metadata - Additional metadata
   * @returns {Promise<boolean>} Success status
   */
  async addConversationTurn(sessionId, userId, userMessage, aiResponse, metadata = {}) {
    try {
      // Store as user memory (long-term persistence)
      await this.addMemories(userId, [
        `User said: ${userMessage}`,
        `Assistant replied: ${aiResponse}`
      ], 'conversation', {
        sessionId,
        timestamp: Date.now(),
        ...metadata
      });

      logger.debug('Conversation turn added to memory:', {
        sessionId,
        userId,
        userMessageLength: userMessage.length,
        aiResponseLength: aiResponse.length
      });

      return true;
    } catch (error) {
      logger.error('Error adding conversation turn to memory:', error);
      return false;
    }
  }

  /**
   * Dispose of resources
   */
  dispose() {
    // MemorySaver and InMemoryStore don't require explicit cleanup for in-memory storage
    // In production, you might want to close database connections here
    this.initialized = false;
    logger.info('LangGraph Memory Manager disposed');
  }
}

// Add summarization functionality directly to the memory manager
// This replaces the need for memorySummarization.js and provides similar functionality
// to Python's langmem.short_term without requiring additional dependencies
LangGraphMemoryManager.prototype.createSummary = async function(messages, options = {}) {
  if (!messages || messages.length === 0) return null;
  
  const {
    maxTokens = 384,
    maxSummaryTokens = 128,
    model = null
  } = options;

  // Simple token estimation (4 chars per token approximation)
  const estimateTokens = (text) => Math.ceil((typeof text === 'string' ? text : JSON.stringify(text)).length / 4);
  
  const currentTokens = estimateTokens(messages);
  if (currentTokens < maxTokens) return null; // No summarization needed
  
  if (!model) {
    logger.warn('No model provided for summarization');
    return null;
  }

  try {
    const summaryPrompt = `Summarize these memory entries concisely (max ${maxSummaryTokens} tokens):
${JSON.stringify(messages)}

Focus on key insights, patterns, and important context that should be preserved.`;

    const response = await model.invoke(summaryPrompt);
    return typeof response === 'string' ? response : response.content || JSON.stringify(response);
  } catch (error) {
    logger.error('Failed to create summary:', error);
    return null;
  }
};

// Create and export singleton instance
const langGraphMemoryManager = new LangGraphMemoryManager();

export { langGraphMemoryManager };
export default LangGraphMemoryManager;
