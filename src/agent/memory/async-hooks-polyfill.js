/**
 * Browser-compatible AsyncLocalStorage polyfill
 * 
 * This polyfill provides a browser-compatible implementation of Node.js AsyncLocalStorage
 * for use with LangGraph.js in browser environments.
 * 
 * Based on community solutions from LangGraph GitHub issue #879 
 * https://github.com/langchain-ai/langgraphjs/issues/879#issuecomment-2749252465
 */

/**
 * Enhanced browser implementation of AsyncLocalStorage
 * Uses Map with Symbol keys for better isolation and async support
 */
class BrowserAsyncLocalStorage {
    constructor() {
        this.storeMap = new Map();
        this._currentContext = null;
    }

    /**
     * Run a function with a given context (supports both sync and async)
     * @param {any} store - The context to store
     * @param {Function} callback - The function to run with this context
     * @returns {any} The result of the callback
     */
    run = async (store, callback, ...args) => {
        const executionContext = Symbol('AsyncLocalStorage');
        const previousContext = this._currentContext;
        
        // Store in both map (for complex scenarios) and direct reference (for simple access)
        this.storeMap.set(executionContext, store);
        this._currentContext = store;
        
        try {
            // Handle both sync and async callbacks
            const result = callback(...args);
            return result instanceof Promise ? await result : result;
        } finally {
            this.storeMap.delete(executionContext);
            this._currentContext = previousContext;
        }
    }

    /**
     * Get the current context
     * @returns {any} The current context, or undefined if none
     */
    getStore = () => {
        // Try direct reference first (faster), then check map
        if (this._currentContext !== null) {
            return this._currentContext;
        }
        
        // Fallback to map lookup for complex scenarios
        for (const value of this.storeMap.values()) {
            return value;
        }
        return undefined;
    }

    /**
     * Disable the current context for the duration of a function call
     * @param {Function} callback - The function to run without context
     * @returns {any} The result of the callback
     */
    exit = async (callback, ...args) => {
        const previousContext = this._currentContext;
        this._currentContext = undefined;
        
        try {
            const result = callback(...args);
            return result instanceof Promise ? await result : result;
        } finally {
            this._currentContext = previousContext;
        }
    }

    /**
     * Enter the async context
     * @param {any} store - The context to enter
     */
    enterWith = (store) => {
        const executionContext = Symbol('AsyncLocalStorage');
        this.storeMap.set(executionContext, store);
        this._currentContext = store;
    }
}

/**
 * Browser polyfill for async_hooks module
 * Provides the AsyncLocalStorage class that LangGraph expects
 */
export const AsyncLocalStorage = BrowserAsyncLocalStorage;

// For CommonJS compatibility
export default {
    AsyncLocalStorage: BrowserAsyncLocalStorage
};

// Also provide named exports for maximum compatibility
export { BrowserAsyncLocalStorage };

console.log('🌐 AsyncLocalStorage browser polyfill loaded');