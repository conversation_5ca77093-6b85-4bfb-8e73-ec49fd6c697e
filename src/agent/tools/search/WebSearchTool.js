/**
 * Web Search Tool for LangGraph Integration
 * 
 * Provides web search capabilities with priority for model's native search (<PERSON>yun enable_search),
 * falling back to external tools. Special support for anime character search.
 * Integrates with the existing dual brain system and CharacterService.
 */

import { tool } from '@langchain/core/tools';
import { z } from 'zod';
import { BaseToolNode } from '../base/BaseToolNode.js';
import { createLogger } from '../../../utils/logger.js';

/**
 * Web Search Tool Class
 * Handles web search operations with model-first approach, caching and character-specific processing
 */
export class WebSearchTool {
    constructor(options = {}) {
        this.logger = createLogger('WebSearchTool');
        this.options = {
            enableCache: true,
            cacheTTL: 300000, // 5 minutes
            maxResults: 10,
            timeout: 10000, // 10 seconds
            // Model-first search settings
            preferModelSearch: true, // Prioritize model's native search (Aliyun enable_search)
            modelSearchFallback: true, // Fall back to external search if model search fails
            ...options
        };

        // Store model instance for native search capabilities
        this.chatModel = options.chatModel || null; // Should be AliyunHttpChatModel or similar

        // Cache for search results
        this.cache = new Map();
        this.cacheTimestamps = new Map();

        // Character processing patterns
        this.animeCharacterSources = [
            'fandom.com',
            'wiki.com',
            'myanimelist.net',
            'anime-planet.com'
        ];

        // Personality trait extraction patterns
        this.personalityKeywords = {
            formality: {
                high: ['formal', 'polite', 'respectful', 'proper', 'dignified', 'courteous'],
                low: ['casual', 'informal', 'relaxed', 'laid-back', 'easygoing', 'friendly']
            },
            enthusiasm: {
                high: ['energetic', 'enthusiastic', 'excited', 'passionate', 'vibrant', 'lively'],
                low: ['calm', 'quiet', 'reserved', 'composed', 'subdued', 'serene']
            },
            empathy: {
                high: ['caring', 'compassionate', 'understanding', 'kind', 'sympathetic', 'nurturing'],
                low: ['cold', 'distant', 'aloof', 'detached', 'objective', 'analytical']
            },
            creativity: {
                high: ['creative', 'imaginative', 'innovative', 'artistic', 'inventive', 'original'],
                low: ['practical', 'straightforward', 'conventional', 'logical', 'methodical', 'traditional']
            },
            directness: {
                high: ['direct', 'blunt', 'straightforward', 'honest', 'frank', 'candid'],
                low: ['diplomatic', 'tactful', 'subtle', 'gentle', 'careful', 'considerate']
            }
        };

        // Voice style mapping
        this.voiceStyleMapping = {
            'formal': ['formal', 'polite', 'respectful', 'dignified'],
            'casual': ['casual', 'friendly', 'relaxed', 'easygoing'],
            'authoritative': ['strong', 'commanding', 'confident', 'leader'],
            'expressive': ['expressive', 'animated', 'dramatic', 'theatrical'],
            'energetic': ['energetic', 'enthusiastic', 'lively', 'vibrant'],
            'balanced': ['balanced', 'moderate', 'adaptable', 'versatile']
        };

        this.logger.info('WebSearchTool initialized', {
            enableCache: this.options.enableCache,
            cacheTTL: this.options.cacheTTL,
            maxResults: this.options.maxResults
        });
    }

    /**
     * Execute web search with character processing
     */
    async execute(params) {
        const startTime = Date.now();

        try {
            this.logger.debug('Executing web search', params);

            // Validate parameters
            const validation = this.validateParams(params);
            if (!validation.valid) {
                return {
                    success: false,
                    error: validation.error,
                    executionTime: Date.now() - startTime
                };
            }

            const { query, searchType, maxResults, enableCache = this.options.enableCache } = params;

            // Check cache if enabled
            if (enableCache) {
                const cachedResult = this.getCachedResult(query);
                if (cachedResult) {
                    this.logger.debug('Returning cached result for query:', query);
                    return {
                        ...cachedResult,
                        fromCache: true,
                        executionTime: Date.now() - startTime
                    };
                }
            }

            // Perform web search
            const searchResult = await this.performWebSearch(query, maxResults);

            if (!searchResult.success) {
                return {
                    success: false,
                    error: searchResult.error,
                    executionTime: Date.now() - startTime
                };
            }

            // Process results based on search type
            let processedResult;
            if (searchType === 'anime_character') {
                processedResult = await this.processAnimeCharacterResult(searchResult, params);
            } else {
                processedResult = this.processGeneralResult(searchResult, params);
            }

            // Cache result if enabled
            if (enableCache) {
                this.cacheResult(query, processedResult);
            }

            const result = {
                ...processedResult,
                success: true,
                executionTime: Date.now() - startTime,
                fromCache: false
            };

            this.logger.debug('Web search completed', {
                query,
                searchType,
                executionTime: result.executionTime,
                resultType: searchType === 'anime_character' ? 'character' : 'general'
            });

            return result;

        } catch (error) {
            this.logger.error('Web search execution failed', error);
            return {
                success: false,
                error: error.message,
                executionTime: Date.now() - startTime
            };
        }
    }

    /**
     * Validate search parameters
     */
    validateParams(params) {
        if (!params || typeof params !== 'object') {
            return { valid: false, error: 'Parameters must be an object' };
        }

        if (!params.query || typeof params.query !== 'string' || params.query.trim().length === 0) {
            return { valid: false, error: 'query is required and must be a non-empty string' };
        }

        if (params.maxResults && (typeof params.maxResults !== 'number' || params.maxResults < 1 || params.maxResults > 50)) {
            return { valid: false, error: 'maxResults must be a number between 1 and 50' };
        }

        if (params.searchType && !['general', 'anime_character'].includes(params.searchType)) {
            return { valid: false, error: 'searchType must be either "general" or "anime_character"' };
        }

        return { valid: true };
    }

    /**
     * Perform the actual web search using WebFetch
     */
    async performWebSearch(query, maxResults = this.options.maxResults) {
        try {
            // Use the WebFetch tool provided by the system
            const webFetch = this.options.webFetch || global.webFetch;
            
            if (!webFetch) {
                throw new Error('WebFetch tool not available');
            }

            // Create search URL and prompt
            const searchPrompt = `Search for information about: ${query}. Provide comprehensive details including key facts, characteristics, and relevant information.`;

            // For anime characters, enhance the search
            const enhancedQuery = query.includes('character') || query.includes('anime') 
                ? query 
                : `${query} anime character information`;

            const result = await webFetch({
                url: `https://www.google.com/search?q=${encodeURIComponent(enhancedQuery)}`,
                prompt: searchPrompt
            });

            if (!result || typeof result !== 'object') {
                throw new Error('Invalid response from WebFetch');
            }

            return {
                success: true,
                data: result,
                query: enhancedQuery
            };

        } catch (error) {
            this.logger.error('Web search failed', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Process anime character search results
     */
    async processAnimeCharacterResult(searchResult, params) {
        const { data } = searchResult;
        const { structureForService = false, extractPersonality = false, anime } = params;

        try {
            // Extract character information from search data
            const characterInfo = this.extractCharacterInfo(data, anime);

            if (extractPersonality || structureForService) {
                characterInfo.personality = this.extractPersonalityTraits(data);
            }

            if (structureForService) {
                return {
                    character: this.structureForCharacterService(characterInfo)
                };
            }

            return {
                character: characterInfo,
                rawData: data
            };

        } catch (error) {
            this.logger.error('Failed to process anime character result', error);
            return {
                character: null,
                error: error.message,
                rawData: data
            };
        }
    }

    /**
     * Extract character information from search data
     */
    extractCharacterInfo(data, anime) {
        const content = (data.content || data.title || '').toLowerCase();
        
        // Extract character name
        let characterName = 'Unknown Character';
        if (data.title) {
            // Try to extract name from title (common pattern: "Character Name - Anime Wiki")
            const titleMatch = data.title.match(/^([^-|]+)/);
            if (titleMatch) {
                characterName = titleMatch[1].trim();
            }
        }

        // Extract anime series if not provided
        let animeSeries = anime || 'Unknown Series';
        if (!anime && data.content) {
            const seriesPatterns = [
                /one piece/i,
                /one-punch man/i,
                /naruto/i,
                /dragon ball/i,
                /attack on titan/i,
                /demon slayer/i,
                /my hero academia/i
            ];

            for (const pattern of seriesPatterns) {
                if (pattern.test(data.content)) {
                    animeSeries = data.content.match(pattern)[0];
                    break;
                }
            }
        }

        // Extract description
        let description = 'A character from anime series';
        if (data.content && data.content.length > 50) {
            // Take first meaningful sentence
            const sentences = data.content.split(/[.!?]+/);
            for (const sentence of sentences) {
                if (sentence.trim().length > 20 && sentence.toLowerCase().includes(characterName.toLowerCase())) {
                    description = sentence.trim();
                    break;
                }
            }
        }

        return {
            name: characterName,
            anime: animeSeries,
            description: description,
            source: data.url || 'web search',
            searchData: data
        };
    }

    /**
     * Extract personality traits from character data
     */
    extractPersonalityTraits(data) {
        const content = (data.content || data.title || '').toLowerCase();
        const traits = {
            formality: 0.5,
            enthusiasm: 0.5,
            empathy: 0.5,
            creativity: 0.5,
            directness: 0.5
        };

        // Analyze content for personality keywords
        for (const [trait, keywords] of Object.entries(this.personalityKeywords)) {
            let score = 0.5; // Default neutral
            let highCount = 0;
            let lowCount = 0;

            // Count high and low trait indicators
            for (const keyword of keywords.high) {
                if (content.includes(keyword)) {
                    highCount++;
                }
            }

            for (const keyword of keywords.low) {
                if (content.includes(keyword)) {
                    lowCount++;
                }
            }

            // Calculate score based on keyword frequency
            if (highCount > lowCount) {
                score = Math.min(0.9, 0.5 + (highCount - lowCount) * 0.1);
            } else if (lowCount > highCount) {
                score = Math.max(0.1, 0.5 - (lowCount - highCount) * 0.1);
            }

            traits[trait] = Math.round(score * 10) / 10; // Round to 1 decimal place
        }

        return traits;
    }

    /**
     * Structure character data for CharacterService compatibility
     */
    structureForCharacterService(characterInfo) {
        const { name, anime, description, personality } = characterInfo;

        // Generate unique ID
        const id = name.toLowerCase().replace(/[^a-z0-9]/g, '_');

        // Determine voice style based on personality
        const voiceStyle = this.determineVoiceStyle(personality);

        // Generate avatar emoji based on anime/character
        const avatar = this.generateAvatar(name, anime);

        // Create system prompt
        const systemPrompt = this.generateSystemPrompt(characterInfo);

        return {
            id,
            name,
            description,
            avatar,
            personality: personality || {
                formality: 0.5,
                enthusiasm: 0.5,
                empathy: 0.5,
                creativity: 0.5,
                directness: 0.5
            },
            voiceStyle,
            systemPrompt,
            metadata: {
                anime,
                source: 'web_search',
                searchedAt: Date.now()
            }
        };
    }

    /**
     * Determine appropriate voice style based on personality traits
     */
    determineVoiceStyle(personality) {
        if (!personality) return 'balanced';

        const { formality, enthusiasm, empathy, creativity, directness } = personality;

        // Determine dominant traits
        if (formality > 0.7) return 'professional';
        if (enthusiasm > 0.8) return 'energetic';
        if (creativity > 0.7 && enthusiasm > 0.6) return 'expressive';
        if (directness > 0.8 || (directness > 0.6 && empathy < 0.5)) return 'authoritative';
        if (formality < 0.4 && empathy > 0.6) return 'casual';
        
        return 'balanced';
    }

    /**
     * Generate avatar emoji based on character and anime
     */
    generateAvatar(name, anime) {
        const nameL = name.toLowerCase();
        const animeL = anime.toLowerCase();

        // Specific character mappings
        const characterAvatars = {
            'luffy': '👒',
            'zoro': '⚔️',
            'nami': '🧭',
            'sanji': '🍳',
            'chopper': '🦌',
            'robin': '📚',
            'franky': '🤖',
            'brook': '💀',
            'saitama': '👊',
            'genos': '🔥',
            'tatsumaki': '🌪️',
            'mumen rider': '🚲'
        };

        for (const [character, emoji] of Object.entries(characterAvatars)) {
            if (nameL.includes(character)) {
                return emoji;
            }
        }

        // Anime-based defaults
        if (animeL.includes('one piece')) return '🏴‍☠️';
        if (animeL.includes('one-punch man')) return '👊';
        if (animeL.includes('naruto')) return '🍃';
        if (animeL.includes('dragon ball')) return '🐉';

        // Generic anime character
        return '👤';
    }

    /**
     * Generate system prompt for character
     */
    generateSystemPrompt(characterInfo) {
        const { name, anime, description, personality } = characterInfo;

        let prompt = `You are ${name} from ${anime}. ${description}`;

        if (personality) {
            const traits = [];
            if (personality.formality > 0.7) traits.push('formal and respectful');
            else if (personality.formality < 0.3) traits.push('casual and friendly');

            if (personality.enthusiasm > 0.7) traits.push('energetic and enthusiastic');
            else if (personality.enthusiasm < 0.3) traits.push('calm and measured');

            if (personality.empathy > 0.7) traits.push('caring and empathetic');
            if (personality.creativity > 0.7) traits.push('creative and imaginative');
            if (personality.directness > 0.7) traits.push('direct and honest');

            if (traits.length > 0) {
                prompt += ` You are ${traits.join(', ')}.`;
            }
        }

        prompt += ' Stay true to your character while being helpful and engaging in conversations.';

        return prompt;
    }

    /**
     * Process general (non-character) search results
     */
    processGeneralResult(searchResult, params) {
        const { data } = searchResult;

        return {
            results: [{
                title: data.title || 'Search Result',
                content: data.content || 'No content available',
                url: data.url || null,
                relevance: 1.0
            }],
            query: params.query,
            rawData: data
        };
    }

    /**
     * Cache management methods
     */
    getCachedResult(query) {
        if (!this.options.enableCache) return null;

        const cached = this.cache.get(query);
        const timestamp = this.cacheTimestamps.get(query);

        if (cached && timestamp) {
            const age = Date.now() - timestamp;
            if (age < this.options.cacheTTL) {
                return cached;
            } else {
                // Cache expired
                this.cache.delete(query);
                this.cacheTimestamps.delete(query);
            }
        }

        return null;
    }

    cacheResult(query, result) {
        if (!this.options.enableCache) return;

        this.cache.set(query, result);
        this.cacheTimestamps.set(query, Date.now());

        // Cleanup old entries if cache gets too large
        if (this.cache.size > 100) {
            const oldestEntries = Array.from(this.cacheTimestamps.entries())
                .sort((a, b) => a[1] - b[1])
                .slice(0, 20);

            for (const [oldQuery] of oldestEntries) {
                this.cache.delete(oldQuery);
                this.cacheTimestamps.delete(oldQuery);
            }
        }
    }

    setCacheTTL(ttl) {
        this.options.cacheTTL = ttl;
    }

    clearCache() {
        this.cache.clear();
        this.cacheTimestamps.clear();
        this.logger.info('Search cache cleared');
    }
}

/**
 * Zod schema for web search parameters
 */
const webSearchSchema = z.object({
    query: z.string().min(1).describe('Search query string'),
    searchType: z.enum(['general', 'anime_character']).default('general').describe('Type of search to perform'),
    maxResults: z.number().min(1).max(50).default(10).describe('Maximum number of results to return'),
    anime: z.string().optional().describe('Specific anime series (for character searches)'),
    extractPersonality: z.boolean().default(false).describe('Extract personality traits from character data'),
    structureForService: z.boolean().default(false).describe('Structure result for CharacterService compatibility'),
    enableCache: z.boolean().default(true).describe('Enable result caching')
});

/**
 * Web search tool function for LangChain integration
 */
async function webSearchFunction(params) {
    const searchTool = new WebSearchTool({
        webFetch: global.webFetch || global.WebFetch
    });

    return await searchTool.execute(params);
}

/**
 * LangChain tool instance
 */
export const webSearchTool = tool(
    webSearchFunction,
    {
        name: 'web_search',
        description: 'Search the web for information. Supports general searches and specialized anime character searches with personality extraction.',
        schema: webSearchSchema
    }
);

/**
 * Create enhanced tool node for LangGraph workflows
 */
export function createWebSearchToolNode(options = {}) {
    return new BaseToolNode([webSearchTool], {
        loggerName: 'WebSearchToolNode',
        enableStatistics: true,
        includeMetadata: true,
        ...options
    });
}

/**
 * Web search tool collection for registration
 */
export const webSearchToolCollection = [webSearchTool];

export const WEB_SEARCH_TOOL_NAMES = {
    WEB_SEARCH: 'web_search'
};

export default webSearchTool;