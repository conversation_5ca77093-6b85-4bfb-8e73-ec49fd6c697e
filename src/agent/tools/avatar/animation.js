/**
 * Advanced Animation Tools for LangGraph Integration
 * Enhanced with BaseToolNode for comprehensive tool management
 * Uses modern LangChain.js/LangGraph patterns with semantic embeddings and retrieval
 * Implements LangGraph-style state management and advanced retrieval patterns
 * Based on: https://js.langchain.com/docs/integrations/text_embedding/
 */

import { z } from 'zod';
import { tool } from '@langchain/core/tools';
import { MemoryVectorStore } from 'langchain/vectorstores/memory';
import { MultiQueryRetriever } from 'langchain/retrievers/multi_query';
import { PromptTemplate } from '@langchain/core/prompts';
import { StringOutputParser } from '@langchain/core/output_parsers';
import { createLogger } from '@/utils/logger';
import { ANIMATION_REGISTRY } from '@/animation/AnimationConfig.js';
import { BaseToolNode, createEnhancedToolNode } from '../base/BaseToolNode.js';

const logger = createLogger('animation-tools');

/**
 * Simple text-based embeddings as a fallback when vector embeddings are unavailable
 * Implements basic TF-IDF style similarity for animation search
 */
class SimpleTextEmbeddings {
    constructor() {
        this.name = 'SimpleTextEmbeddings';
    }

    /**
     * Create simple text embeddings using character frequency
     */
    async embedDocuments(texts) {
        return texts.map(text => this._textToVector(text));
    }

    async embedQuery(query) {
        return this._textToVector(query);
    }

    /**
     * Convert text to simple numerical vector using character frequencies
     * @private
     */
    _textToVector(text) {
        const normalized = text.toLowerCase().replace(/[^a-z0-9\s]/g, '');
        const words = normalized.split(/\s+/).filter(word => word.length > 0);

        // Create a simple frequency vector (basic TF-IDF concept)
        const wordFreq = {};
        words.forEach(word => {
            wordFreq[word] = (wordFreq[word] || 0) + 1;
        });

        // Convert to fixed-length vector (use common animation keywords)
        const commonKeywords = [
            'dance', 'happy', 'sad', 'angry', 'wave', 'move', 'kick', 'jump',
            'martial', 'boxing', 'talk', 'speak', 'greet', 'celebrate', 'fight',
            'emotion', 'action', 'performance', 'communication', 'gesture'
        ];

        const vector = commonKeywords.map(keyword => {
            // Score based on exact match and partial matches
            let score = 0;
            if (wordFreq[keyword]) score += wordFreq[keyword] * 2;

            // Partial matching for flexibility
            for (const word of words) {
                if (word.includes(keyword) || keyword.includes(word)) {
                    score += 0.5;
                }
            }
            return score;
        });

        return vector;
    }
}

/**
 * LangGraph-Enhanced Animation Retrieval System
 * Implements advanced semantic search with multi-query retrieval and state management
 * Following modern LangGraph patterns for intelligent animation selection
 */
class LangGraphAnimationRetriever {
    constructor() {
        this.vectorStore = null;
        this.multiQueryRetriever = null;
        this.isInitialized = false;
        this.embeddings = null;
        this.llm = null;
        // LangGraph-style state management
        this.state = {
            lastQuery: null,
            lastResults: [],
            queryHistory: [],
            preferredCategories: new Set(),
            userPatterns: new Map()
        };
    }

    /**
     * Initialize LangGraph retrieval system with multi-query capabilities
     * Implements modern LangGraph patterns with advanced semantic search
     */
    async initialize(llm = null) {
        if (this.isInitialized) return this.vectorStore;

        try {
            this.llm = llm; // Store LLM for multi-query generation

            // Initialize embeddings with enhanced fallback strategy
            this.embeddings = await this._initializeAdvancedEmbeddings();

            if (!this.embeddings) {
                logger.warn('No embeddings available, using text-based fallback');
                this.isInitialized = false;
                return null;
            }

            // Prepare enhanced animation documents for LangGraph patterns
            const animationDocuments = this._prepareLangGraphDocuments();
            const metadatas = animationDocuments.map(doc => doc.metadata);
            const texts = animationDocuments.map(doc => doc.text);

            // Create vector store following LangGraph best practices
            this.vectorStore = await MemoryVectorStore.fromTexts(
                texts,
                metadatas,
                this.embeddings
            );

            // Initialize multi-query retriever if LLM is available
            if (this.llm) {
                await this._initializeMultiQueryRetriever();
            }

            this.isInitialized = true;
            logger.info(`LangGraph animation retriever initialized with ${texts.length} animations`);

            return this.vectorStore;
        } catch (error) {
            logger.error('Failed to initialize LangGraph animation retriever:', error);
            this.isInitialized = false;
            return null;
        }
    }

    /**
     * Initialize multi-query retriever for advanced semantic search
     * Following LangGraph patterns for query expansion and diversification
     * @private
     */
    async _initializeMultiQueryRetriever() {
        if (!this.llm || !this.vectorStore) return;

        try {
            // Create animation-specific query expansion prompt
            const queryExpansionPrompt = PromptTemplate.fromTemplate(
                `You are an expert animation assistant. Given a user's animation request, generate 3-5 different search queries to find the most relevant animations.

                Consider these aspects when generating queries:
                - Emotional context and mood
                - Physical movements and gestures
                - Use cases and scenarios
                - Animation styles and categories
                - Synonyms and related concepts

                Original query: {question}

                Generate diverse search queries (one per line):
                `
            );

            // Create LCEL chain for query expansion (modern LangChain pattern)
            // Replaces deprecated LLMChain per: https://python.langchain.com/docs/versions/v0_2/deprecations/
            const queryExpansionChain = queryExpansionPrompt
                .pipe(this.llm)
                .pipe(new StringOutputParser());

            // Initialize multi-query retriever with LCEL chain
            this.multiQueryRetriever = new MultiQueryRetriever({
                retriever: this.vectorStore.asRetriever({
                    k: 10, // Get more results for better diversity
                    searchType: "similarity"
                }),
                llmChain: queryExpansionChain, // LCEL chain is compatible with MultiQueryRetriever
                verbose: true,
            });

            logger.info('Multi-query retriever initialized for advanced animation search');
        } catch (error) {
            logger.warn('Failed to initialize multi-query retriever:', error.message);
            this.multiQueryRetriever = null;
        }
    }

    /**
     * Initialize embeddings with enhanced fallback strategy for LangGraph patterns
     * Prioritizes modern embedding approaches while maintaining compatibility
     * @private
     */
    async _initializeAdvancedEmbeddings() {
        // Try different embedding approaches in order of preference
        const embeddingOptions = [
            // Option 1: Try OpenAI embeddings (only in Node.js environment)
            async () => {
                try {
                    // Skip OpenAI embeddings in browser environment
                    if (typeof window !== 'undefined') {
                        logger.info('Browser environment detected - skipping OpenAI embeddings');
                        return null;
                    }

                    const { OpenAIEmbeddings } = await import('@langchain/openai');
                    return new OpenAIEmbeddings({
                        modelName: "text-embedding-3-small", // Smaller, cheaper model
                        maxRetries: 2,
                        timeout: 5000
                    });
                } catch (error) {
                    logger.warn('OpenAI embeddings not available (check API key):', error.message);
                    return null;
                }
            },

            // Option 2: Try deprecated HuggingFace embeddings (acknowledging deprecation warning)
            async () => {
                try {
                    const { HuggingFaceTransformersEmbeddings } = await import('@langchain/community/embeddings/hf_transformers');
                    logger.warn('Using deprecated HuggingFace embeddings - this may be removed in future versions');
                    return new HuggingFaceTransformersEmbeddings({
                        modelName: "Xenova/all-MiniLM-L6-v2"
                    });
                } catch (error) {
                    logger.warn('HuggingFace embeddings not available:', error.message);
                    return null;
                }
            },

            // Option 3: Simple embedding alternative (TF-IDF style)
            async () => {
                logger.info('Using simple text-based similarity as embedding fallback');
                return new SimpleTextEmbeddings();
            }
        ];

        // Try each embedding option until one works
        for (const tryEmbedding of embeddingOptions) {
            try {
                const embeddings = await tryEmbedding();
                if (embeddings) {
                    logger.info(`Successfully initialized embeddings: ${embeddings.constructor.name}`);
                    return embeddings;
                }
            } catch (error) {
                logger.warn('Embedding initialization failed:', error.message);
                continue;
            }
        }

        logger.warn('All embedding options failed, falling back to text search');
        return null;
    }

    /**
     * Prepare enhanced animation documents for LangGraph semantic search
     * Creates rich contextual embeddings with advanced metadata
     */
    _prepareLangGraphDocuments() {
        const documents = [];

        // Initialize ANIMATION_REGISTRY if needed
        if (!ANIMATION_REGISTRY.byId || Object.keys(ANIMATION_REGISTRY.byId).length === 0) {
            ANIMATION_REGISTRY.initialize();
        }

        // Convert animation registry to LangGraph-enhanced searchable documents
        for (const [animationId, animation] of Object.entries(ANIMATION_REGISTRY.byId)) {
            // Create comprehensive semantic content for LangGraph patterns
            const semanticContent = this._createLangGraphSemanticContent(animation);

            // Enhanced metadata following LangGraph best practices
            const enhancedMetadata = {
                animationId: animation.id,
                filename: animation.filename,
                category: animation.category,
                subcategory: animation.subcategory,
                description: animation.description,
                loopable: animation.loopable,
                // LangGraph enhancements
                semanticTags: this._generateSemanticTags(animation),
                emotionalProfile: this._analyzeEmotionalProfile(animation),
                usageScenarios: this._identifyUsageScenarios(animation),
                movementCharacteristics: this._analyzeMovementCharacteristics(animation),
                contextualFactors: this._analyzeContextualFactors(animation)
            };

            documents.push({
                text: semanticContent,
                metadata: enhancedMetadata
            });
        }

        return documents;
    }

    /**
     * Create comprehensive semantic content using LangGraph patterns
     * Builds rich contextual descriptions for advanced semantic search
     * @private
     */
    _createLangGraphSemanticContent(animation) {
        const contentElements = [
            // Core identification
            `Animation: ${animation.id}`,
            `Name: ${animation.name || animation.id}`,

            // Categorical information
            `Category: ${animation.category || 'general'}`,
            `Subcategory: ${animation.subcategory || 'none'}`,

            // Descriptive content
            `Description: ${animation.description || this._generateSmartDescription(animation)}`,

            // Enhanced semantic keywords
            `Keywords: ${this._generateAdvancedKeywords(animation)}`,

            // Contextual analysis
            `Usage scenarios: ${this._identifyUsageScenarios(animation).join(', ')}`,
            `Emotional context: ${this._analyzeEmotionalProfile(animation)}`,
            `Movement type: ${this._analyzeMovementCharacteristics(animation)}`,
            `Social context: ${this._analyzeSocialContext(animation)}`,

            // Related concepts for better semantic matching
            `Related concepts: ${this._generateRelatedConcepts(animation).join(', ')}`,

            // Technical properties
            `Loopable: ${animation.loopable ? 'yes' : 'no'}`,
            `Duration suitability: ${this._assessDurationSuitability(animation)}`
        ];

        return contentElements.filter(Boolean).join(' | ');
    }

    /**
     * Generate advanced semantic tags for LangGraph retrieval
     * @private
     */
    _generateSemanticTags(animation) {
        const tags = new Set();

        // Parse animation ID for semantic components
        const idParts = animation.id.toLowerCase().split(/[_-]/);
        idParts.forEach(part => {
            if (part.length > 1) tags.add(part);
        });

        // Add category-based semantic tags
        if (animation.category) {
            tags.add(animation.category);
            tags.add(`${animation.category}_animation`);
        }

        // Add emotional components
        const emotions = this._extractEmotionalComponents(animation);
        emotions.forEach(emotion => tags.add(emotion));

        // Add movement components
        const movements = this._extractMovementComponents(animation);
        movements.forEach(movement => tags.add(movement));

        // Add contextual components
        const contexts = this._extractContextualComponents(animation);
        contexts.forEach(context => tags.add(context));

        return Array.from(tags);
    }

    /**
     * Analyze emotional profile using LangGraph semantic analysis
     * @private
     */
    _analyzeEmotionalProfile(animation) {
        const id = animation.id.toLowerCase();
        const description = (animation.description || '').toLowerCase();

        const emotionalIndicators = {
            happiness: ['happy', 'joy', 'smile', 'laugh', 'cheerful', 'excited'],
            sadness: ['sad', 'cry', 'melancholy', 'somber', 'depressed'],
            anger: ['angry', 'mad', 'rage', 'furious', 'aggressive'],
            energy: ['energetic', 'dynamic', 'active', 'vigorous', 'lively'],
            calm: ['calm', 'peaceful', 'zen', 'tranquil', 'serene']
        };

        const emotions = [];
        for (const [emotion, indicators] of Object.entries(emotionalIndicators)) {
            if (indicators.some(indicator => id.includes(indicator) || description.includes(indicator))) {
                emotions.push(emotion);
            }
        }

        return emotions.length > 0 ? emotions.join(', ') : 'neutral';
    }

    /**
     * Identify usage scenarios using LangGraph context analysis
     * @private
     */
    _identifyUsageScenarios(animation) {
        const scenarios = [];
        const id = animation.id.toLowerCase();
        const category = animation.category || '';

        // Conversation scenarios
        if (id.includes('talk') || id.includes('speak') || id.includes('conversation')) {
            scenarios.push('dialogue', 'verbal_interaction', 'communication');
        }

        // Greeting scenarios
        if (id.includes('greet') || id.includes('hello') || id.includes('wave')) {
            scenarios.push('greeting', 'introduction', 'acknowledgment');
        }

        // Entertainment scenarios
        if (category === 'dance' || id.includes('dance') || id.includes('perform')) {
            scenarios.push('entertainment', 'performance', 'show');
        }

        // Emotional expression scenarios
        if (id.includes('happy') || id.includes('celebrate') || id.includes('joy')) {
            scenarios.push('celebration', 'positive_feedback', 'mood_elevation');
        }

        // Action scenarios
        if (category === 'combat' || id.includes('fight') || id.includes('kick')) {
            scenarios.push('action', 'combat', 'training');
        }

        // Idle scenarios
        if (id.includes('idle') || id.includes('wait') || id.includes('stand')) {
            scenarios.push('waiting', 'ambient', 'background');
        }

        return scenarios.length > 0 ? scenarios : ['general'];
    }

    /**
     * Analyze movement characteristics for semantic matching
     * @private
     */
    _analyzeMovementCharacteristics(animation) {
        const id = animation.id.toLowerCase();
        const characteristics = [];

        if (id.includes('dance') || id.includes('rhythm')) characteristics.push('rhythmic');
        if (id.includes('fight') || id.includes('combat')) characteristics.push('combative');
        if (id.includes('gentle') || id.includes('soft')) characteristics.push('gentle');
        if (id.includes('quick') || id.includes('fast')) characteristics.push('fast');
        if (id.includes('slow') || id.includes('calm')) characteristics.push('slow');
        if (id.includes('jump') || id.includes('leap')) characteristics.push('dynamic');
        if (id.includes('idle') || id.includes('still')) characteristics.push('static');

        return characteristics.length > 0 ? characteristics.join(', ') : 'moderate';
    }

    /**
     * Analyze contextual factors for LangGraph state management
     * @private
     */
    _analyzeContextualFactors(animation) {
        const id = animation.id.toLowerCase();

        return {
            formality: this._assessFormality(id),
            energy_level: this._assessEnergyLevel(id),
            social_appropriateness: this._assessSocialContext(animation),
            interaction_type: this._assessInteractionType(id),
            duration_preference: this._assessDurationSuitability(animation)
        };
    }

    // Helper methods for advanced semantic analysis
    _generateSmartDescription(animation) {
        const id = animation.id.replace(/[_-]/g, ' ');
        const category = animation.category || 'general';
        return `${category} animation: ${id}`;
    }

    _generateAdvancedKeywords(animation) {
        const keywords = new Set();

        // Enhanced category mapping
        const categoryKeywords = {
            'dance': ['dancing', 'movement', 'rhythm', 'music', 'choreography', 'expression', 'performance'],
            'emotional': ['feeling', 'mood', 'emotion', 'expression', 'state', 'demeanor'],
            'combat': ['fighting', 'martial arts', 'action', 'aggressive', 'defensive', 'training'],
            'communication': ['talking', 'speaking', 'social', 'conversation', 'dialogue', 'interaction'],
            'performance': ['show', 'entertainment', 'performance', 'action', 'display', 'demonstration']
        };

        if (animation.category && categoryKeywords[animation.category]) {
            categoryKeywords[animation.category].forEach(kw => keywords.add(kw));
        }

        // Extract semantic keywords from ID
        const idParts = animation.id.split(/[_-]/);
        idParts.forEach(part => {
            if (part.length > 2) {
                keywords.add(part);
                // Add synonyms for common animation terms
                const synonyms = this._getSynonyms(part);
                synonyms.forEach(syn => keywords.add(syn));
            }
        });

        return Array.from(keywords).join(' ');
    }

    _getSynonyms(word) {
        const synonymMap = {
            'happy': ['joyful', 'cheerful', 'pleased', 'content'],
            'sad': ['melancholy', 'sorrowful', 'dejected', 'downcast'],
            'dance': ['move', 'groove', 'boogie', 'perform'],
            'fight': ['combat', 'battle', 'spar', 'engage'],
            'talk': ['speak', 'communicate', 'converse', 'chat'],
            'jump': ['leap', 'bound', 'hop', 'spring'],
            'wave': ['gesture', 'signal', 'greet', 'acknowledge']
        };

        return synonymMap[word.toLowerCase()] || [];
    }

    _generateRelatedConcepts(animation) {
        const concepts = new Set();
        const id = animation.id.toLowerCase();

        if (id.includes('dance')) concepts.add('music', 'rhythm', 'expression', 'art');
        if (id.includes('fight')) concepts.add('martial arts', 'training', 'discipline', 'combat');
        if (id.includes('happy')) concepts.add('joy', 'celebration', 'positive', 'uplifting');
        if (id.includes('talk')) concepts.add('communication', 'dialogue', 'social', 'interaction');

        return Array.from(concepts);
    }

    _extractEmotionalComponents(animation) {
        const emotions = [];
        const text = `${animation.id} ${animation.description || ''}`.toLowerCase();

        const emotionMap = {
            'happy': 'happiness',
            'sad': 'sadness',
            'angry': 'anger',
            'excited': 'excitement',
            'calm': 'tranquility'
        };

        for (const [key, emotion] of Object.entries(emotionMap)) {
            if (text.includes(key)) emotions.push(emotion);
        }

        return emotions;
    }

    _extractMovementComponents(animation) {
        const movements = [];
        const text = `${animation.id} ${animation.description || ''}`.toLowerCase();

        const movementMap = {
            'dance': 'rhythmic_movement',
            'jump': 'vertical_movement',
            'wave': 'gestural_movement',
            'kick': 'striking_movement',
            'idle': 'minimal_movement'
        };

        for (const [key, movement] of Object.entries(movementMap)) {
            if (text.includes(key)) movements.push(movement);
        }

        return movements;
    }

    _extractContextualComponents(animation) {
        const contexts = [];
        const text = `${animation.id} ${animation.description || ''}`.toLowerCase();

        const contextMap = {
            'greet': 'social_interaction',
            'talk': 'verbal_communication',
            'perform': 'entertainment_context',
            'fight': 'action_context'
        };

        for (const [key, context] of Object.entries(contextMap)) {
            if (text.includes(key)) contexts.push(context);
        }

        return contexts;
    }

    _assessFormality(id) {
        const formal = ['professional', 'business', 'formal', 'official'];
        const casual = ['fun', 'silly', 'casual', 'relaxed'];

        if (formal.some(f => id.includes(f))) return 'formal';
        if (casual.some(c => id.includes(c))) return 'casual';
        return 'neutral';
    }

    _assessEnergyLevel(id) {
        const high = ['jump', 'dance', 'excited', 'active', 'energetic'];
        const low = ['idle', 'calm', 'still', 'peaceful'];

        if (high.some(h => id.includes(h))) return 'high';
        if (low.some(l => id.includes(l))) return 'low';
        return 'medium';
    }

    _analyzeSocialContext(animation) {
        const id = animation.id.toLowerCase();
        if (id.includes('greet') || id.includes('social')) return 'social';
        if (id.includes('perform') || id.includes('show')) return 'performance';
        if (id.includes('private') || id.includes('personal')) return 'private';
        return 'general';
    }

    _assessInteractionType(id) {
        if (id.includes('talk') || id.includes('speak')) return 'verbal';
        if (id.includes('gesture') || id.includes('wave')) return 'gestural';
        if (id.includes('dance') || id.includes('perform')) return 'expressive';
        return 'general';
    }

    _assessDurationSuitability(animation) {
        const id = animation.id.toLowerCase();
        if (id.includes('idle') || animation.loopable) return 'long';
        if (id.includes('quick') || id.includes('brief')) return 'short';
        return 'medium';
    }

    /**
     * Advanced LangGraph semantic search with multi-query retrieval and state management
     * Implements modern retrieval patterns for intelligent animation selection
     */
    async searchAnimations(query, options = {}) {
        await this.initialize(options.llm);

        // Update state with current query
        this._updateQueryState(query);

        if (!this.vectorStore) {
            logger.warn('Vector store unavailable, using text fallback');
            return this._fallbackTextSearch(query, options);
        }

        try {
            const {
                k = 5,
                category = null,
                threshold = 0.5,
                useMultiQuery = true,
                diversifyResults = true,
                includeStateContext = true
            } = options;

            let searchResults;

            // Try multi-query retrieval first (LangGraph pattern)
            if (useMultiQuery && this.multiQueryRetriever) {
                try {
                    searchResults = await this._performMultiQuerySearch(query, k * 2);
                    logger.info(`Multi-query search returned ${searchResults.length} results`);
                } catch (error) {
                    logger.warn('Multi-query search failed, falling back to standard search:', error.message);
                    searchResults = null;
                }
            }

            // Fallback to standard similarity search
            if (!searchResults) {
                const vectorResults = await this.vectorStore.similaritySearchWithScore(query, k * 2);
                searchResults = vectorResults.map(([doc, score]) => ({ doc, score }));
            }

            // Apply LangGraph-style filtering and enhancement
            let processedResults = this._applyLangGraphFiltering(searchResults, {
                category,
                threshold,
                diversifyResults,
                includeStateContext
            });

            // Limit final results
            processedResults = processedResults.slice(0, k);

            // Update state with results
            this._updateResultsState(processedResults);

            return processedResults.map(result => ({
                animationId: result.metadata.animationId,
                score: result.score,
                metadata: result.metadata,
                reason: result.reason || `LangGraph semantic match: ${(result.score * 100).toFixed(1)}%`,
                contextualFit: this._assessContextualFit(result.metadata, query),
                stateAlignment: this._assessStateAlignment(result.metadata)
            }));

        } catch (error) {
            logger.error('LangGraph search failed, using fallback:', error);
            return this._fallbackTextSearch(query, options);
        }
    }

    /**
     * Perform multi-query search using LangGraph retrieval patterns
     * @private
     */
    async _performMultiQuerySearch(query, k) {
        if (!this.multiQueryRetriever) return null;

        try {
            // Use multi-query retriever to get diverse results
            const documents = await this.multiQueryRetriever.getRelevantDocuments(query);

            // Convert to standardized format with scores
            return documents.slice(0, k).map((doc, index) => ({
                doc,
                score: Math.max(0.9 - (index * 0.1), 0.3), // Assign decreasing scores
                reason: 'Multi-query semantic match'
            }));
        } catch (error) {
            logger.warn('Multi-query retrieval failed:', error.message);
            return null;
        }
    }

    /**
     * Apply LangGraph-style filtering and enhancement to search results
     * @private
     */
    _applyLangGraphFiltering(results, options) {
        let filtered = results;

        // Score threshold filtering
        if (options.threshold) {
            filtered = filtered.filter(result => result.score >= options.threshold);
        }

        // Category filtering
        if (options.category) {
            filtered = filtered.filter(result =>
                result.doc.metadata.category === options.category ||
                result.metadata?.category === options.category
            );
        }

        // Diversify results by category (LangGraph pattern)
        if (options.diversifyResults) {
            filtered = this._diversifyResultsByCategory(filtered);
        }

        // Enhance with state context if enabled
        if (options.includeStateContext) {
            filtered = this._enhanceWithStateContext(filtered);
        }

        return filtered.map(result => ({
            metadata: result.doc?.metadata || result.metadata,
            score: result.score,
            reason: result.reason
        }));
    }

    /**
     * Diversify results by category to avoid redundancy (LangGraph pattern)
     * @private
     */
    _diversifyResultsByCategory(results) {
        const categoryMap = new Map();
        const diversified = [];

        // Group by category, keeping highest scoring from each
        for (const result of results) {
            const category = result.doc?.metadata?.category || result.metadata?.category || 'general';
            if (!categoryMap.has(category) || categoryMap.get(category).score < result.score) {
                categoryMap.set(category, result);
            }
        }

        // Add category leaders first
        diversified.push(...categoryMap.values());

        // Add remaining high-scoring results
        for (const result of results) {
            const animationId = result.doc?.metadata?.animationId || result.metadata?.animationId;
            if (!diversified.some(d =>
                (d.doc?.metadata?.animationId || d.metadata?.animationId) === animationId
            )) {
                diversified.push(result);
            }
        }

        return diversified.sort((a, b) => b.score - a.score);
    }

    /**
     * Enhance results with LangGraph state context
     * @private
     */
    _enhanceWithStateContext(results) {
        return results.map(result => {
            const metadata = result.doc?.metadata || result.metadata;

            // Boost score based on user patterns and preferences
            let stateBoost = 0;

            // Boost if category matches user preferences
            if (this.state.preferredCategories.has(metadata.category)) {
                stateBoost += 0.1;
            }

            // Boost if similar to recent successful queries
            if (this._isQuerySimilarToRecentSuccesses(metadata)) {
                stateBoost += 0.05;
            }

            return {
                ...result,
                score: Math.min(result.score + stateBoost, 1.0),
                stateBoost
            };
        });
    }

    /**
     * Update query state for LangGraph state management
     * @private
     */
    _updateQueryState(query) {
        this.state.lastQuery = query;
        this.state.queryHistory.push({
            query,
            timestamp: Date.now()
        });

        // Keep only recent history (last 20 queries)
        if (this.state.queryHistory.length > 20) {
            this.state.queryHistory = this.state.queryHistory.slice(-20);
        }
    }

    /**
     * Update results state for LangGraph patterns
     * @private
     */
    _updateResultsState(results) {
        this.state.lastResults = results;

        // Update preferred categories based on results
        results.forEach(result => {
            if (result.metadata?.category) {
                this.state.preferredCategories.add(result.metadata.category);
            }
        });

        // Limit preferred categories
        if (this.state.preferredCategories.size > 10) {
            const categories = Array.from(this.state.preferredCategories);
            this.state.preferredCategories = new Set(categories.slice(-10));
        }
    }

    /**
     * Assess contextual fit for LangGraph intelligence
     * @private
     */
    _assessContextualFit(metadata, query) {
        const queryLower = query.toLowerCase();
        let fitScore = 0;

        // Check usage scenarios alignment
        if (metadata.usageScenarios) {
            const scenarios = Array.isArray(metadata.usageScenarios)
                ? metadata.usageScenarios
                : [metadata.usageScenarios];

            for (const scenario of scenarios) {
                if (queryLower.includes(scenario.replace('_', ' '))) {
                    fitScore += 0.3;
                }
            }
        }

        // Check emotional profile alignment
        if (metadata.emotionalProfile && queryLower.includes(metadata.emotionalProfile)) {
            fitScore += 0.4;
        }

        // Check semantic tags alignment
        if (metadata.semanticTags) {
            const tags = Array.isArray(metadata.semanticTags)
                ? metadata.semanticTags
                : [metadata.semanticTags];

            for (const tag of tags) {
                if (queryLower.includes(tag)) {
                    fitScore += 0.1;
                }
            }
        }

        return Math.min(fitScore, 1.0);
    }

    /**
     * Assess state alignment for LangGraph patterns
     * @private
     */
    _assessStateAlignment(metadata) {
        let alignment = 0;

        // Check against preferred categories
        if (metadata.category && this.state.preferredCategories.has(metadata.category)) {
            alignment += 0.5;
        }

        // Check against recent query patterns
        if (this._matchesRecentPatterns(metadata)) {
            alignment += 0.3;
        }

        return Math.min(alignment, 1.0);
    }

    /**
     * Check if query is similar to recent successful queries
     * @private
     */
    _isQuerySimilarToRecentSuccesses(metadata) {
        // Simple heuristic - check if current metadata matches recent successful patterns
        return this.state.queryHistory.slice(-5).some(entry => {
            return entry.query.toLowerCase().includes(metadata.category || '');
        });
    }

    /**
     * Check if metadata matches recent query patterns
     * @private
     */
    _matchesRecentPatterns(metadata) {
        const recentQueries = this.state.queryHistory.slice(-3);
        return recentQueries.some(entry => {
            const query = entry.query.toLowerCase();
            return query.includes(metadata.category) ||
                query.includes(metadata.animationId?.split('_')[0] || '');
        });
    }

    /**
     * Fallback text search when vector search fails
     */
    _fallbackTextSearch(query, options = {}) {
        const normalizedQuery = query.toLowerCase();
        const results = [];

        for (const [animationId, animation] of Object.entries(ANIMATION_REGISTRY.byId)) {
            if (options.category && animation.category !== options.category) continue;

            // Simple keyword matching
            const searchFields = [
                animation.id,
                animation.name || '',
                animation.description || '',
                animation.category
            ].join(' ').toLowerCase();

            if (searchFields.includes(normalizedQuery)) {
                results.push({
                    animationId: animation.id,
                    score: 0.8,
                    metadata: {
                        animationId: animation.id,
                        filename: animation.filename,
                        category: animation.category,
                        description: animation.description
                    },
                    reason: `Text match for: ${query}`
                });
            }
        }

        return results.slice(0, options.k || 5);
    }
}

// Global LangGraph retriever instance following singleton pattern
const langGraphAnimationRetriever = new LangGraphAnimationRetriever();

/**
 * Advanced Animation Selection Tool using LangGraph patterns
 * Implements multi-query retrieval and state-aware semantic search
 */
export const selectAnimationTool = tool(
    async (input, options = {}) => {
        const { animationQuery, reasoningContext, category } = input;

        logger.info(`LangGraph animation selection requested: "${animationQuery}"`);

        try {
            // Use advanced LangGraph retriever for semantic search
            const searchResults = await langGraphAnimationRetriever.searchAnimations(animationQuery, {
                k: 5,
                category: category,
                threshold: 0.3,
                useMultiQuery: true,
                diversifyResults: true,
                llm: options.llm // Pass LLM for multi-query generation
            });

            if (searchResults.length > 0) {
                const bestMatch = searchResults[0];
                const selectedAnimationId = bestMatch.animationId;

                // Get animation details from registry
                let animation = ANIMATION_REGISTRY.getById(selectedAnimationId);

                // Try similarity check if exact match fails
                if (!animation) {
                    const similarAnimationId = _findSimilarAnimationId(selectedAnimationId, ANIMATION_REGISTRY);
                    if (similarAnimationId) {
                        animation = ANIMATION_REGISTRY.getById(similarAnimationId);
                        logger.info(`Animation ID corrected: '${selectedAnimationId}' -> '${similarAnimationId}'`);
                    }
                }

                if (animation) {
                    logger.info(`Animation selected via LangGraph search: ${selectedAnimationId} (score: ${bestMatch.score.toFixed(3)})`);

                    return {
                        success: true,
                        animationId: selectedAnimationId,
                        animationName: animation.name || selectedAnimationId,
                        description: animation.description || 'Animation triggered',
                        category: animation.category,
                        reasoning: reasoningContext || bestMatch.reason,
                        method: 'langgraph_search',
                        searchScore: bestMatch.score,
                        contextualFit: bestMatch.contextualFit,
                        stateAlignment: bestMatch.stateAlignment,
                        enhancedMetadata: {
                            semanticTags: bestMatch.metadata.semanticTags,
                            emotionalProfile: bestMatch.metadata.emotionalProfile,
                            usageScenarios: bestMatch.metadata.usageScenarios
                        },
                        alternatives: searchResults.slice(1, 3).map(result => ({
                            id: result.animationId,
                            score: result.score,
                            contextualFit: result.contextualFit
                        }))
                    };
                }
            }

            // Fallback to simple keyword matching
            const fallbackId = _fallbackAnimationSelection(animationQuery);
            if (fallbackId) {
                const animation = ANIMATION_REGISTRY.getById(fallbackId);
                if (animation) {
                    logger.info(`Animation selected via fallback: ${fallbackId}`);
                    return {
                        success: true,
                        animationId: fallbackId,
                        animationName: animation.name || fallbackId,
                        description: animation.description || 'Animation triggered',
                        category: animation.category,
                        reasoning: reasoningContext || `Keyword match for: ${animationQuery}`,
                        method: 'fallback_search'
                    };
                }
            }

            // No animation found
            logger.warn(`No animation found for query: "${animationQuery}"`);
            return {
                success: false,
                error: `No animation found matching: ${animationQuery}`,
                availableCategories: Object.keys(ANIMATION_REGISTRY.byCategory || {}),
                method: 'search_failed',
                suggestions: searchResults.slice(0, 3).map(r => r.animationId)
            };

        } catch (error) {
            logger.error('Animation selection error:', error);
            return {
                success: false,
                error: error.message,
                fallback: true
            };
        }
    },
    {
        name: 'select_animation',
        description: 'Select and trigger an animation using advanced LangGraph semantic search. Uses multi-query retrieval and state management to find the best matching animation based on user intent, mood, or specific requests.',
        schema: z.object({
            animationQuery: z.string().describe('The animation request, description, or intent (e.g., "dance", "happy movement", "martial arts kick")'),
            reasoningContext: z.string().optional().describe('Context or reasoning for this animation selection'),
            category: z.string().optional().describe('Specific animation category to search within (optional)')
        })
    }
);

/**
 * List Available Animations Tool
 * Provides information about available animations, organized by category
 */
export const listAnimationsTool = tool(
    async (input) => {
        const { category, includeDescriptions = true, limit = 20 } = input;

        try {
            let animations = [];

            if (category) {
                // Get animations for specific category
                const categoryAnimations = ANIMATION_REGISTRY.getByCategory(category);
                animations = categoryAnimations || [];
            } else {
                // Get all animations
                const allAnimations = ANIMATION_REGISTRY.getAllAnimations();
                animations = Object.values(allAnimations);
            }

            // Limit results
            if (animations.length > limit) {
                animations = animations.slice(0, limit);
            }

            // Format animations for response
            const formattedAnimations = animations.map(anim => {
                const result = {
                    id: anim.id,
                    name: anim.name || anim.id,
                    category: anim.category
                };

                if (includeDescriptions && anim.description) {
                    result.description = anim.description;
                }

                return result;
            });

            // Get category information
            const categories = Object.keys(ANIMATION_REGISTRY.byCategory || {});

            logger.info(`Listed ${formattedAnimations.length} animations${category ? ` in category: ${category}` : ''}`);

            return {
                success: true,
                animations: formattedAnimations,
                totalCount: formattedAnimations.length,
                availableCategories: categories,
                category: category || 'all',
                hasMore: animations.length === limit
            };

        } catch (error) {
            logger.error('Error listing animations:', error);
            return {
                success: false,
                error: error.message,
                animations: []
            };
        }
    },
    {
        name: 'list_animations',
        description: 'List available animations, optionally filtered by category. Useful for discovering what animations are available.',
        schema: z.object({
            category: z.string().optional().describe('Filter animations by category (e.g., "dance", "emotion", "combat")'),
            includeDescriptions: z.boolean().optional().default(true).describe('Include animation descriptions in the results'),
            limit: z.number().optional().default(20).describe('Maximum number of animations to return')
        })
    }
);

/**
 * Animation Recommendation Tool using LangChain.js conversational patterns
 * Provides contextual recommendations based on conversation memory and user state
 * Following: https://js.langchain.com/docs/modules/memory/how_to/summary_buffer
 */
export const recommendAnimationsTool = tool(
    async (input) => {
        const {
            userMood,
            conversationContext,
            userIntent,
            limit = 5
        } = input;

        try {
            // Build comprehensive search queries from context
            const searchQueries = [];

            if (userMood) {
                searchQueries.push(userMood);
            }
            if (userIntent) {
                searchQueries.push(userIntent);
            }
            if (conversationContext) {
                // Extract animation-relevant keywords from conversation
                const animationKeywords = _extractAnimationKeywords(conversationContext);
                searchQueries.push(...animationKeywords);
            }

            // Combine all context into a single search query
            const combinedQuery = searchQueries.join(' ');

            // Use LangGraph retriever for contextual recommendations
            const recommendations = await langGraphAnimationRetriever.searchAnimations(combinedQuery, {
                k: limit * 2, // Get more results for diversity
                threshold: 0.2, // Lower threshold for broader recommendations
                useMultiQuery: false, // Skip multi-query for recommendations to save tokens
                diversifyResults: true,
                includeStateContext: true
            });

            // Diversify recommendations by category
            const diversifiedRecs = _diversifyRecommendations(recommendations, limit);

            logger.info(`Generated ${diversifiedRecs.length} contextual animation recommendations`);

            return {
                success: true,
                recommendations: diversifiedRecs.map(rec => ({
                    animationId: rec.animationId,
                    score: rec.score,
                    reason: rec.reason,
                    category: rec.metadata.category,
                    description: rec.metadata.description,
                    contextualFit: rec.contextualFit,
                    stateAlignment: rec.stateAlignment,
                    semanticTags: rec.metadata.semanticTags?.slice(0, 3) || []
                })),
                context: {
                    mood: userMood,
                    userIntent: userIntent,
                    conversation: conversationContext
                },
                totalRecommendations: diversifiedRecs.length,
                searchQuery: combinedQuery
            };

        } catch (error) {
            logger.error('Error generating animation recommendations:', error);
            return {
                success: false,
                error: error.message,
                recommendations: []
            };
        }
    },
    {
        name: 'recommend_animations',
        description: 'Get contextual animation recommendations using LangGraph state management and semantic analysis. Considers user mood, conversation context, and intent with advanced retrieval patterns.',
        schema: z.object({
            userMood: z.string().optional().describe('Current user mood (e.g., "happy", "excited", "calm")'),
            conversationContext: z.string().optional().describe('Context from the conversation'),
            userIntent: z.string().optional().describe('What the user seems to want or is asking for'),
            limit: z.number().optional().default(5).describe('Maximum number of recommendations to return')
        })
    }
);

/**
 * Stop Animation Tool for LangGraph
 * Stops currently playing animations and provides state feedback
 */
export const stopAnimationTool = tool(
    async (input) => {
        const { stopType = 'current', reason } = input;

        try {
            logger.info(`Animation stop requested: ${stopType}${reason ? ` (${reason})` : ''}`);

            // Get access to the TalkingAvatar instance through the global context
            // This is a temporary approach until we have proper dependency injection
            const talkingAvatar = globalThis.talkingAvatar || window.talkingAvatar;

            if (!talkingAvatar) {
                logger.warn('TalkingAvatar instance not available for animation stopping');
                return {
                    success: false,
                    error: 'Animation system not available',
                    stopType
                };
            }

            let stopped = false;
            const stoppedAnimations = [];

            // Clear current talking animation reference
            if (talkingAvatar.currentTalkingAnimation) {
                stoppedAnimations.push({
                    id: talkingAvatar.currentTalkingAnimation.file || 'unknown',
                    type: 'talking_animation'
                });
                talkingAvatar.currentTalkingAnimation = null;
            }

            // Stop animations based on type
            switch (stopType) {
                case 'current':
                case 'all':
                    // Stop via animator if available
                    if (talkingAvatar.animator) {
                        if (typeof talkingAvatar.animator.stopAnimation === 'function') {
                            logger.info('Stopping animations via animator.stopAnimation()');
                            talkingAvatar.animator.stopAnimation();
                            stopped = true;
                        } else if (talkingAvatar.animator.mixer && typeof talkingAvatar.animator.mixer.stopAllAction === 'function') {
                            logger.info('Stopping animations via mixer.stopAllAction()');
                            talkingAvatar.animator.mixer.stopAllAction();
                            stopped = true;
                        }
                    }

                    // Also stop TalkingHead animations if available
                    if (talkingAvatar.talkingHead && typeof talkingAvatar.talkingHead.stopSpeaking === 'function') {
                        logger.info('Stopping TalkingHead animations');
                        talkingAvatar.talkingHead.stopSpeaking();
                        stopped = true;
                    }
                    break;

                case 'talking_only':
                    // Only stop talking-related animations, leave other animations running
                    if (talkingAvatar.animator && talkingAvatar.currentTalkingAnimation) {
                        // More selective stopping for talking animations only
                        stopped = true;
                    }
                    break;

                default:
                    logger.warn(`Unknown stop type: ${stopType}`);
                    return {
                        success: false,
                        error: `Unknown stop type: ${stopType}`,
                        availableStopTypes: ['current', 'all', 'talking_only']
                    };
            }

            if (stopped) {
                logger.info(`Successfully stopped animations (type: ${stopType})`);
                return {
                    success: true,
                    stopType,
                    stoppedAnimations,
                    reason: reason || 'Animation stop requested via LangGraph tool',
                    message: `Stopped ${stopType} animations successfully`
                };
            } else {
                logger.info('No animations were running to stop');
                return {
                    success: true,
                    stopType,
                    stoppedAnimations: [],
                    message: 'No animations were currently running',
                    reason: reason || 'No active animations found'
                };
            }

        } catch (error) {
            logger.error('Error stopping animations:', error);
            return {
                success: false,
                error: error.message,
                stopType
            };
        }
    },
    {
        name: 'stop_animation',
        description: 'Stop currently playing animations. Can stop all animations, current animation, or only talking-specific animations. Provides feedback on what was stopped.',
        schema: z.object({
            stopType: z.enum(['current', 'all', 'talking_only']).optional().default('current').describe('Type of stop operation: current (stop current animation), all (stop all animations), talking_only (stop only speaking animations)'),
            reason: z.string().optional().describe('Reason for stopping the animation (for logging and feedback)')
        })
    }
);

/**
 * Random Talking Animation Tool for LangGraph
 * Selects and triggers a random talking/speaking animation suitable for conversation
 */
export const randomTalkingAnimationTool = tool(
    async (input) => {
        const {
            mood = 'neutral',
            intensity = 'medium',
            preventRepeats = true
        } = input;

        try {
            logger.info(`Random talking animation requested: mood=${mood}, intensity=${intensity}`);

            // Build contextual query for talking animations
            const talkingQueries = [
                'talking animation',
                'speaking gesture',
                'communication',
                'conversation',
                mood !== 'neutral' ? mood : '',
                intensity !== 'medium' ? intensity : ''
            ].filter(Boolean);

            const combinedQuery = talkingQueries.join(' ');

            // Use LangGraph retriever to find suitable talking animations
            const talkingAnimations = await langGraphAnimationRetriever.searchAnimations(combinedQuery, {
                k: 10, // Get more options for randomization
                category: 'communication', // Focus on communication animations
                threshold: 0.1, // Lower threshold for more variety
                useMultiQuery: false, // Keep it simple for random selection
                diversifyResults: true
            });

            // Filter for talking/speaking animations specifically
            const speakingAnimations = talkingAnimations.filter(anim =>
                anim.metadata?.subcategory === 'speaking' ||
                anim.animationId?.toLowerCase().includes('talk') ||
                anim.animationId?.toLowerCase().includes('speak') ||
                anim.metadata?.semanticTags?.some(tag =>
                    tag.includes('talk') || tag.includes('speak') || tag.includes('conversation')
                )
            );

            let selectedAnimations = speakingAnimations.length > 0 ? speakingAnimations : talkingAnimations;

            // Handle repeat prevention if requested
            if (preventRepeats && globalThis.lastTalkingAnimationId) {
                selectedAnimations = selectedAnimations.filter(anim =>
                    anim.animationId !== globalThis.lastTalkingAnimationId
                );
            }

            // Select random animation from filtered results
            if (selectedAnimations.length > 0) {
                const randomIndex = Math.floor(Math.random() * selectedAnimations.length);
                const selectedAnimation = selectedAnimations[randomIndex];

                // Store for repeat prevention
                globalThis.lastTalkingAnimationId = selectedAnimation.animationId;

                // Get full animation details
                const animation = ANIMATION_REGISTRY.getById(selectedAnimation.animationId);

                if (animation) {
                    logger.info(`Random talking animation selected: ${selectedAnimation.animationId} (score: ${selectedAnimation.score.toFixed(3)})`);

                    return {
                        success: true,
                        animationId: selectedAnimation.animationId,
                        animationName: animation.name || selectedAnimation.animationId,
                        description: animation.description || 'Random talking animation for conversation',
                        category: animation.category,
                        subcategory: animation.subcategory,
                        mood: mood,
                        intensity: intensity,
                        method: 'random_talking_selection',
                        searchScore: selectedAnimation.score,
                        contextualFit: selectedAnimation.contextualFit,
                        reason: `Random talking animation selected based on mood: ${mood}, intensity: ${intensity}`,
                        alternativesCount: selectedAnimations.length - 1,
                        metadata: {
                            isRandomSelection: true,
                            isTalkingAnimation: true,
                            loopable: animation.loopable || true,
                            semanticTags: selectedAnimation.metadata?.semanticTags?.slice(0, 5) || []
                        }
                    };
                }
            }

            // Fallback to hardcoded talking animations if search fails
            const fallbackTalkingAnimations = [
                'talking',
                'sitting_talking',
                'conversation',
                'speaking_gesture'
            ];

            for (const fallbackId of fallbackTalkingAnimations) {
                const animation = ANIMATION_REGISTRY.getById(fallbackId);
                if (animation) {
                    globalThis.lastTalkingAnimationId = fallbackId;
                    logger.info(`Using fallback talking animation: ${fallbackId}`);

                    return {
                        success: true,
                        animationId: fallbackId,
                        animationName: animation.name || fallbackId,
                        description: animation.description || 'Fallback talking animation',
                        category: animation.category,
                        mood: mood,
                        intensity: intensity,
                        method: 'fallback_talking_selection',
                        reason: 'Used fallback talking animation from hardcoded list',
                        metadata: {
                            isRandomSelection: true,
                            isTalkingAnimation: true,
                            isFallback: true,
                            loopable: animation.loopable || true
                        }
                    };
                }
            }

            // No talking animations found
            logger.warn('No talking animations available for random selection');
            return {
                success: false,
                error: 'No talking animations found',
                mood: mood,
                intensity: intensity,
                availableCategories: Object.keys(ANIMATION_REGISTRY.byCategory || {}),
                suggestions: ['Try using select_animation with specific talking animation names']
            };

        } catch (error) {
            logger.error('Error selecting random talking animation:', error);
            return {
                success: false,
                error: error.message,
                mood: mood,
                intensity: intensity
            };
        }
    },
    {
        name: 'random_talking_animation',
        description: 'Select and trigger a random talking/speaking animation suitable for conversation. Uses LangGraph semantic search to find contextually appropriate animations based on mood and intensity.',
        schema: z.object({
            mood: z.string().optional().default('neutral').describe('Mood context for the talking animation (e.g., "happy", "excited", "calm", "serious")'),
            intensity: z.string().optional().default('medium').describe('Animation intensity level (e.g., "low", "medium", "high")'),
            preventRepeats: z.boolean().optional().default(true).describe('Prevent selecting the same animation consecutively')
        })
    }
);

/**
 * Find similar animation ID using string similarity (replaces server SHARED_PROMPT_COMPONENTS)
 * @private
 */
function _findSimilarAnimationId(targetId, registry) {
    if (!targetId || !registry) return null;

    const allAnimations = registry.getAllAnimations();
    const allIds = Object.keys(allAnimations);

    // Calculate similarity scores using simple string distance
    const similarities = allIds.map(id => ({
        id,
        score: _calculateStringSimilarity(targetId.toLowerCase(), id.toLowerCase())
    }));

    // Sort by similarity score
    similarities.sort((a, b) => b.score - a.score);

    // Return best match if similarity is above threshold
    const bestMatch = similarities[0];
    if (bestMatch && bestMatch.score > 0.6) {
        return bestMatch.id;
    }

    return null;
}

/**
 * Calculate string similarity using Levenshtein distance
 * @private
 */
function _calculateStringSimilarity(str1, str2) {
    const matrix = [];
    const len1 = str1.length;
    const len2 = str2.length;

    if (len1 === 0) return len2 === 0 ? 1 : 0;
    if (len2 === 0) return 0;

    for (let i = 0; i <= len2; i++) {
        matrix[i] = [i];
    }

    for (let j = 0; j <= len1; j++) {
        matrix[0][j] = j;
    }

    for (let i = 1; i <= len2; i++) {
        for (let j = 1; j <= len1; j++) {
            if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j - 1] + 1,
                    matrix[i][j - 1] + 1,
                    matrix[i - 1][j] + 1
                );
            }
        }
    }

    const maxLen = Math.max(len1, len2);
    return (maxLen - matrix[len2][len1]) / maxLen;
}

/**
 * Fallback animation selection using basic string matching
 * @private
 */
function _fallbackAnimationSelection(query) {
    const normalizedQuery = query.toLowerCase();
    const allAnimations = ANIMATION_REGISTRY.getAllAnimations();

    // Enhanced keyword mapping following LangChain.js patterns
    const keywords = {
        'dance': ['dance_silly', 'dance_wave', 'dance_tutting', 'dance_samba'],
        'dancing': ['dance_silly', 'dance_wave', 'dance_tutting'],
        'happy': ['happy_idle', 'jump_joy'],
        'excited': ['excited', 'jump_joy'],
        'sad': ['sad_idle', 'sad_idle_alt'],
        'angry': ['angry', 'angry_alt'],
        'wave': ['dance_wave', 'greeting'],
        'hello': ['greeting'],
        'hi': ['greeting'],
        'fight': ['boxing_jab', 'capoeira_kick'],
        'kick': ['capoeira_kick', 'spinning_kick'],
        'martial': ['capoeira_kick', 'boxing_jab'],
        'boxing': ['boxing_jab'],
        'celebrate': ['jump_joy', 'excited'],
        'talk': ['talking', 'sitting_talking'],
        'speak': ['talking'],
        'conversation': ['talking', 'sitting_talking'],
        'greet': ['greeting'],
        'argue': ['arguing'],
        'pray': ['praying'],
        'meditation': ['praying'],
        'hip hop': ['dance_tutting', 'dance_wave', 'dance_snake'],
        'samba': ['dance_samba'],
        'rumba': ['dance_rumba']
    };

    for (const [keyword, animationIds] of Object.entries(keywords)) {
        if (normalizedQuery.includes(keyword)) {
            // Return first available animation for this keyword
            for (const animId of animationIds) {
                if (allAnimations[animId]) {
                    return animId;
                }
            }
        }
    }

    // Default fallback
    return 'talking';
}

/**
 * Extract animation-relevant keywords from conversation context
 * Uses NLP-like approach following LangChain.js text processing patterns
 * @private
 */
function _extractAnimationKeywords(conversationText) {
    if (!conversationText) return [];

    const animationKeywords = [
        // Actions
        'dance', 'dancing', 'move', 'movement', 'action', 'gesture',
        'fight', 'kick', 'punch', 'martial', 'boxing', 'combat',
        'jump', 'celebrate', 'wave', 'greet', 'talk', 'speak',

        // Emotions  
        'happy', 'sad', 'angry', 'excited', 'calm', 'peaceful',
        'joyful', 'energetic', 'aggressive', 'gentle',

        // Styles
        'hip hop', 'samba', 'rumba', 'ballet', 'modern',
        'traditional', 'contemporary', 'classical'
    ];

    const text = conversationText.toLowerCase();
    const found = [];

    for (const keyword of animationKeywords) {
        if (text.includes(keyword)) {
            found.push(keyword);
        }
    }

    return found;
}

/**
 * Diversify animation recommendations by category
 * Implements diversity selection following LangChain.js recommendation patterns
 * @private
 */
function _diversifyRecommendations(recommendations, limit) {
    if (recommendations.length <= limit) return recommendations;

    const diversified = [];
    const usedCategories = new Set();

    // First pass: get one from each category
    for (const rec of recommendations) {
        if (diversified.length >= limit) break;

        const category = rec.metadata?.category;
        if (category && !usedCategories.has(category)) {
            diversified.push(rec);
            usedCategories.add(category);
        }
    }

    // Second pass: fill remaining slots with highest scores
    for (const rec of recommendations) {
        if (diversified.length >= limit) break;

        if (!diversified.includes(rec)) {
            diversified.push(rec);
        }
    }

    return diversified.slice(0, limit);
}

// Export all animation tools
export const animationToolCollection = [
    selectAnimationTool,
    listAnimationsTool,
    recommendAnimationsTool,
    stopAnimationTool
    // randomTalkingAnimationTool removed - redundant with selectAnimationTool
    // Use selectAnimationTool with "talking" or "speaking" query instead
];

export const ANIMATION_TOOL_NAMES = [
    'select_animation',
    'list_animations',
    'recommend_animations',
    'stop_animation'
    // 'random_talking_animation' removed - redundant with select_animation
];

/**
 * Enhanced Animation Tool Node for LangGraph Integration
 * Creates a BaseToolNode instance configured specifically for animation tools
 * Provides advanced tool management with validation, error handling, and statistics
 */
export function createAnimationToolNode(options = {}) {
    return createEnhancedToolNode(animationToolCollection, {
        loggerName: 'AnimationToolNode',
        validateInput: true,
        validateOutput: true,
        handleErrors: true,
        includeMetadata: true,
        ...options
    });
}

/**
 * Create unified tool node with both animation and speaking tools
 * Useful for comprehensive agent setups that need both capabilities
 */
export function createUnifiedToolNode(speakingTools = [], options = {}) {
    const allTools = [
        ...animationToolCollection,
        ...speakingTools
    ];
    
    return createEnhancedToolNode(allTools, {
        loggerName: 'UnifiedToolNode',
        validateInput: true,
        validateOutput: true,
        handleErrors: true,
        includeMetadata: true,
        ...options
    });
}
