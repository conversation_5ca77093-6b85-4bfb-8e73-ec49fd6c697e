/**
 * Focused Speaking Tools for Optimal LLM Performance
 * 
 * Based on research: "Simple, narrowly scoped tools are easier for models to use than complex tools"
 * Source: LangChain official documentation and Berkeley Function Calling Leaderboard
 * 
 * Clean separation of concerns:
 * - avatar_speak: Direct text-to-speech with voice control
 * - speech_control: Control speaking state (stop/status only)
 * - voice_profile_manager: Voice configuration management
 */

import { z } from 'zod';
import { tool } from '@langchain/core/tools';
import { createLogger, LogLevel } from '@/utils/logger';
import { getEnvVar } from '@/config/env.ts';

const logger = createLogger('SpeakingTools');
logger.setLogLevel(LogLevel.DEBUG);

/**
 * Unified Speaking Service
 * Handles all speech-related functionality without duplicates
 */
class SpeakingService {
    constructor(options = {}) {
        this.logger = createLogger('SpeakingService');
        this.apiKey = getEnvVar('VITE_DASHSCOPE_API_KEY', '');
        this.cosyVoiceEndpoint = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text2speech/synthesis';
        
        this.supportedVoices = {
            'Chelsie': { type: 'realtime', gender: 'female' },
            'Serena': { type: 'realtime', gender: 'female' },
            'Ethan': { type: 'realtime', gender: 'male' },
            'Cherry': { type: 'realtime', gender: 'female' }
        };
        
        this.aliyunModel = options.aliyunModel;
        this.sessionId = options.sessionId || 'speaking_session';
    }

    async speak(text, options = {}) {
        const { voice = 'Serena', streaming = true } = options;

        this.logger.info(`🎤 Speaking: ${text.substring(0, 50)}...`, { voice, streaming });

        try {
            if (this.aliyunModel?.isRealtimeModeActive()) {
                return await this._speakWithRealtimeModel(text, options);
            } else {
                return await this._speakWithCosyVoice(text, options);
            }
        } catch (error) {
            this.logger.error('Speech synthesis error:', error);
            throw error;
        }
    }

    async _speakWithRealtimeModel(text, options) {
        const { voice } = options;
        
        if (voice && this.supportedVoices[voice]?.type === 'realtime') {
            await this.aliyunModel.updateRealtimeSession({ voice });
        }
        
        const success = await this.aliyunModel.sendRealtimeText(text);
        
        if (!success) {
            throw new Error('Failed to send text to realtime model');
        }
        
        return { success: true, method: 'realtime', voice, streaming: true };
    }

    async _speakWithCosyVoice(text, options) {
        const { voice, streaming } = options;
        
        const requestPayload = {
            model: 'cosyvoice-v2',
            input: { text },
            parameters: {
                voice: this._mapVoiceToCosyVoice(voice),
                format: 'mp3',
                sample_rate: 22050,
                stream: streaming
            }
        };

        const response = await fetch(this.cosyVoiceEndpoint, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json',
                'X-DashScope-SSE': streaming ? 'enable' : undefined
            },
            body: JSON.stringify(requestPayload)
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`CosyVoice API error: ${response.status} - ${errorText}`);
        }

        // Audio processing handled by @src/media/ - not here
        return { success: true, method: 'cosyvoice', voice, streaming };
    }

    _mapVoiceToCosyVoice(voice) {
        const voiceMap = {
            'Serena': 'loongstella',
            'Ethan': 'longxiaocheng', 
            'Chelsie': 'longyue',
            'Cherry': 'longxiaobai'
        };
        return voiceMap[voice] || 'loongstella';
    }

    getAvailableVoices() {
        return {
            realtime: Object.keys(this.supportedVoices).filter(
                voice => this.supportedVoices[voice].type === 'realtime'
            )
        };
    }
}

// Shared service instance
let sharedSpeakingService = null;

function getSharedSpeakingService(options = {}) {
    if (!sharedSpeakingService) {
        sharedSpeakingService = new SpeakingService(options);
        logger.debug('Created shared speaking service instance');
    }
    return sharedSpeakingService;
}

/**
 * TOOL 1: Avatar Text-to-Speech
 * Direct text-to-speech conversion with voice and emotion control
 * Best for: Speaking any text with specific voice settings
 */
export const avatar_speak = tool(
    async (input, config) => {
        const { 
            text, 
            voice = 'Serena',
            emotion = 'neutral',
            speed = 1.0,
            volume = 0.8,
            animation = true,
            interrupt = false
        } = input;

        logger.info(`🎤 Avatar Speak: "${text?.substring(0, 50)}..."`, { voice, emotion });

        try {
            if (!text) {
                throw new Error('Text is required for speaking');
            }

            const speakingService = getSharedSpeakingService({
                aliyunModel: config?.aliyunModel,
                sessionId: config?.sessionId || 'avatar_speak'
            });

            const cleanText = text.replace(/[^\w\s.,!?;:'"()-]/g, '').trim();
            if (!cleanText) {
                throw new Error('No valid text to process');
            }

            const speakResult = await speakingService.speak(cleanText, { voice });

            // Emit speaking event
            if (typeof window !== 'undefined' && window.dispatchEvent) {
                window.dispatchEvent(new CustomEvent('avatar-speak', {
                    detail: {
                        text: cleanText,
                        voice,
                        emotion,
                        speed,
                        volume,
                        animation,
                        interrupt,
                        method: speakResult.method,
                        timestamp: Date.now()
                    }
                }));
            }

            const result = {
                success: true,
                text: cleanText,
                voice,
                emotion,
                method: speakResult.method,
                speaking: true,
                timestamp: Date.now(),
                message: `Avatar speaking: "${cleanText.substring(0, 50)}${cleanText.length > 50 ? '...' : ''}"`
            };

            logger.info(`✅ Avatar speech executed successfully`, result);
            return result;

        } catch (error) {
            logger.error('Avatar speech error:', error);
            return {
                success: false,
                error: error.message,
                speaking: false,
                timestamp: Date.now(),
                message: `Failed to speak: ${error.message}`
            };
        }
    },
    {
        name: 'avatar_speak',
        description: 'Make the avatar speak any text with voice and emotion control. Direct text-to-speech conversion.',
        schema: z.object({
            text: z.string().describe('Text to speak (required)'),
            voice: z.enum(['Serena', 'Ethan', 'Chelsie', 'Cherry']).default('Serena').describe('Voice to use'),
            emotion: z.enum(['neutral', 'happy', 'sad', 'excited', 'calm', 'thinking']).default('neutral').describe('Emotional tone'),
            speed: z.number().min(0.5).max(2.0).default(1.0).describe('Speaking speed multiplier'),
            volume: z.number().min(0.1).max(1.0).default(0.8).describe('Speaking volume'),
            animation: z.boolean().default(true).describe('Enable lip sync and gestures'),
            interrupt: z.boolean().default(false).describe('Interrupt current speech')
        })
    }
);

/**
 * TOOL 2: Speech Control
 * Control speaking state (stop speaking, check status)
 * Best for: Managing speech without generating new speech
 */
export const speech_control = tool(
    async (input, config) => {
        const { 
            action,
            reason
        } = input;

        logger.info(`🎛️ Speech Control: ${action}`, { reason });

        try {
            const result = {
                action,
                timestamp: Date.now(),
                success: true
            };

            switch (action) {
                case 'stop':
                    if (typeof window !== 'undefined' && window.dispatchEvent) {
                        window.dispatchEvent(new CustomEvent('avatar-stop-speaking', {
                            detail: { 
                                timestamp: Date.now(),
                                reason: reason || 'Speech control stop requested'
                            }
                        }));
                    }
                    result.speaking = false;
                    result.message = 'Avatar stopped speaking';
                    result.reason = reason || 'Speech stopped via control';
                    break;

                case 'status':
                    // Check if avatar is currently speaking (placeholder - would need actual status)
                    result.speaking = false; // This would be actual status check
                    result.message = 'Speech status checked';
                    break;

                default:
                    throw new Error(`Unknown action: ${action}. Use 'stop' or 'status'`);
            }

            logger.info(`✅ Speech control executed: ${action}`, result);
            return result;

        } catch (error) {
            logger.error('Speech control error:', error);
            return {
                success: false,
                action,
                error: error.message,
                timestamp: Date.now(),
                message: `Failed to ${action}: ${error.message}`
            };
        }
    },
    {
        name: 'speech_control',
        description: 'Control speech state - stop speaking or check speaking status. Does not generate new speech.',
        schema: z.object({
            action: z.enum(['stop', 'status']).describe('Control action: stop current speech or check speaking status'),
            reason: z.string().optional().describe('Reason for the control action (for logging)')
        })
    }
);

/**
 * TOOL 3: Voice Profile Management
 * Focused tool for managing voice settings and profiles
 * Best for: Voice configuration and profile management
 */
export const voice_profile_manager = tool(
    async (input, config) => {
        const { 
            action,
            profile_name,
            voice_settings,
            language = 'en'
        } = input;

        logger.info(`🎛️ Voice Profile Manager: ${action}`, { profile_name, language });

        try {
            const speakingService = getSharedSpeakingService({
                aliyunModel: config?.aliyunModel,
                sessionId: config?.sessionId || 'voice_profile_manager'
            });

            const result = {
                action,
                timestamp: Date.now(),
                success: true
            };

            switch (action) {
                case 'list_voices':
                    const voices = speakingService.getAvailableVoices();
                    result.available_voices = voices;
                    result.message = `Available voices: ${voices.realtime.join(', ')}`;
                    break;

                case 'set_profile':
                    if (!profile_name) {
                        throw new Error('profile_name is required for set_profile action');
                    }

                    const profileConfig = {
                        profile: profile_name,
                        settings: voice_settings || {},
                        language,
                        timestamp: Date.now()
                    };

                    if (typeof window !== 'undefined' && window.dispatchEvent) {
                        window.dispatchEvent(new CustomEvent('avatar-voice-profile', {
                            detail: profileConfig
                        }));
                    }

                    result.profile_name = profile_name;
                    result.settings = profileConfig.settings;
                    result.language = language;
                    result.message = `Voice profile set to: ${profile_name}`;
                    break;

                case 'get_profile':
                    // Return current profile information
                    result.current_profile = {
                        name: 'default',
                        voice: 'Serena',
                        language: 'en',
                        active: true
                    };
                    result.message = 'Current voice profile retrieved';
                    break;

                default:
                    throw new Error(`Unknown action: ${action}`);
            }

            logger.info(`✅ Voice profile management executed: ${action}`, result);
            return result;

        } catch (error) {
            logger.error('Voice profile management error:', error);
            return {
                success: false,
                action,
                error: error.message,
                timestamp: Date.now(),
                message: `Failed to ${action}: ${error.message}`
            };
        }
    },
    {
        name: 'voice_profile_manager',
        description: 'Manage voice profiles and settings. Focused on voice configuration, profile management, and voice listing.',
        schema: z.object({
            action: z.enum(['list_voices', 'set_profile', 'get_profile']).describe('Profile management action'),
            profile_name: z.string().optional().describe('Name of the voice profile (required for set_profile)'),
            voice_settings: z.object({}).optional().describe('Voice settings object'),
            language: z.string().default('en').describe('Language code')
        })
    }
);

// Export focused, separate tools for optimal LLM performance
export const speakingTools = [
    avatar_speak,
    speech_control,
    voice_profile_manager
];

// Compatibility export for tool manager
export const agentSpeakingToolCollection = speakingTools;

// Export unified tool aliases for backward compatibility
export const agentTTSTool = avatar_speak;
export const voiceProfileTool = voice_profile_manager;
export const avatarSpeechControlTool = speech_control;

export const SPEAKING_TOOL_NAMES = {
    AVATAR_SPEAK: 'avatar_speak',
    SPEECH_CONTROL: 'speech_control',
    VOICE_PROFILE_MANAGER: 'voice_profile_manager'
};

// Compatibility export for tool manager
export const AGENT_SPEAKING_TOOL_NAMES = SPEAKING_TOOL_NAMES;

// Export service with backward compatibility
export { SpeakingService, getSharedSpeakingService };

// Backward compatibility export - map SpeakingService to AgentSpeakingService
export const AgentSpeakingService = SpeakingService;

// Compatibility exports for functions
export function createSpeakingToolNode(options = {}) {
    return {
        tools: speakingTools,
        name: 'SpeakingToolNode',
        ...options
    };
}

export default speakingTools;