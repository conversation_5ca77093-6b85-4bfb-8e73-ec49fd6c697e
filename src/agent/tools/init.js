/**
 * General Tool Initialization System
 * Provides centralized tool setup that can be triggered from any app layer
 * with configuration parameters
 */

import { createLogger, LogLevel } from '@/utils/logger';
import { toolManager, autoRegisterTools } from './index.js';
import { AgentSpeakingService, getSharedSpeakingService } from './avatar/speaking.js';

const logger = createLogger('ToolInitializer');
logger.setLogLevel(LogLevel.DEBUG);

/**
 * Tool initialization configuration schema
 */
export const DEFAULT_TOOL_CONFIG = {
    // Speaking service configuration
    speaking: {
        apiKey: null, // Will use env VITE_DASHSCOPE_API_KEY if not provided
        defaultVoice: 'Serena',
        enableTranscription: true,
        streaming: true,
        enableVoiceCloning: false
    },

    // Animation system configuration
    animation: {
        enableSemanticRetrieval: true,
        cacheSize: 100,
        enableRecommendations: true
    },

    // Conversation analysis configuration
    conversation: {
        enableContextualAnalysis: true,
        enableMemoryTracking: true,
        engagementThreshold: 0.4,
        silenceThreshold: 15000
    },

    // Audio configuration
    audio: {
        sampleRate: 24000,
        format: 'pcm16',
        enableProcessing: true
    },

    // Session configuration
    session: {
        sessionId: 'default_session',
        enableStateManagement: true,
        enableMemory: true
    },

    // Service integration
    services: {
        audioPlayer: null,
        aliyunModel: null,
        stateManager: null,
        animationRegistry: null
    },

    // Tool selection
    tools: {
        enableSpeaking: true,
        enableAnimation: true,
        enableVoiceCloning: false,
        enableConversation: true,
        enableCharacterSearch: true,
        enableWebSearch: true,
        autoRegister: true
    }
};

/**
 * Global tool initialization state
 */
let initializationState = {
    initialized: false,
    config: null,
    services: {},
    tools: [],
    error: null,
    timestamp: null
};

/**
 * Initialize tool system with configuration
 * 
 * @param {Object} config - Tool initialization configuration
 * @param {Object} services - External services to integrate
 * @returns {Promise<Object>} Initialization result
 */
export async function initializeTools(config = {}, services = {}) {
    const startTime = Date.now();
    logger.info('🚀 Initializing tool system with configuration', {
        configKeys: Object.keys(config),
        serviceKeys: Object.keys(services)
    });

    try {
        // Merge with default configuration
        const mergedConfig = mergeConfig(DEFAULT_TOOL_CONFIG, config);

        // Validate configuration
        const validationResult = validateConfig(mergedConfig);
        if (!validationResult.valid) {
            throw new Error(`Configuration validation failed: ${validationResult.errors.join(', ')}`);
        }

        // Initialize services first
        const initializedServices = await initializeServices(mergedConfig, services);

        // Register tools based on configuration
        const toolRegistrationResult = await registerToolsFromConfig(mergedConfig, initializedServices);

        // Update global state
        initializationState = {
            initialized: true,
            config: mergedConfig,
            services: initializedServices,
            tools: toolRegistrationResult.tools,
            error: null,
            timestamp: Date.now()
        };

        const duration = Date.now() - startTime;
        logger.info(`✅ Tool system initialized successfully in ${duration}ms`, {
            toolCount: toolRegistrationResult.tools.length,
            serviceCount: Object.keys(initializedServices).length,
            registeredCollections: toolRegistrationResult.registered.map(r => r.collection)
        });

        return {
            success: true,
            duration,
            config: mergedConfig,
            services: initializedServices,
            tools: toolRegistrationResult.tools,
            statistics: toolManager.getStatistics(),
            message: `Tool system initialized with ${toolRegistrationResult.tools.length} tools in ${duration}ms`
        };

    } catch (error) {
        const duration = Date.now() - startTime;
        logger.error(`❌ Tool initialization failed after ${duration}ms:`, error);

        // Update global state with error
        initializationState = {
            initialized: false,
            config: null,
            services: {},
            tools: [],
            error: error.message,
            timestamp: Date.now()
        };

        return {
            success: false,
            duration,
            error: error.message,
            tools: [],
            statistics: null,
            message: `Tool initialization failed: ${error.message}`
        };
    }
}

/**
 * Get current initialization state
 * 
 * @returns {Object} Current initialization state
 */
export function getInitializationState() {
    return { ...initializationState };
}

/**
 * Check if tools are initialized
 * 
 * @returns {boolean} True if tools are initialized
 */
export function isInitialized() {
    return initializationState.initialized;
}

/**
 * Get initialized tools
 * 
 * @returns {Array} Array of initialized tools
 */
export function getInitializedTools() {
    return initializationState.tools || [];
}

/**
 * Get initialized services
 * 
 * @returns {Object} Object containing initialized services
 */
export function getInitializedServices() {
    return initializationState.services || {};
}

/**
 * Reinitialize tools with new configuration
 * 
 * @param {Object} newConfig - New configuration to apply
 * @param {Object} newServices - New services to integrate
 * @returns {Promise<Object>} Reinitialization result
 */
export async function reinitializeTools(newConfig = {}, newServices = {}) {
    logger.info('🔄 Reinitializing tool system with new configuration');

    // Clear existing tools
    toolManager.clearAllTools();

    // Merge with existing config if available
    const baseConfig = initializationState.config || DEFAULT_TOOL_CONFIG;
    const mergedConfig = mergeConfig(baseConfig, newConfig);
    const mergedServices = { ...initializationState.services, ...newServices };

    return await initializeTools(mergedConfig, mergedServices);
}

/**
 * Initialize services based on configuration
 * 
 * @private
 * @param {Object} config - Configuration object
 * @param {Object} externalServices - External services
 * @returns {Promise<Object>} Initialized services
 */
async function initializeServices(config, externalServices) {
    logger.info('🔧 Initializing services...');

    const services = { ...externalServices };

    // Initialize Speaking service if enabled
    if (config.tools.enableSpeaking) {
        try {
            // Create or get shared speaking service
            const speakingService = getSharedSpeakingService({
                audioPlayer: services.audioPlayer || externalServices.audioPlayer,
                aliyunModel: services.aliyunModel || externalServices.aliyunModel,
                sessionId: config.session.sessionId,
                enableTranscription: config.speaking.enableTranscription,
                ...config.speaking
            });

            services.speakingService = speakingService;
            logger.info('✅ Speaking service initialized');
        } catch (error) {
            logger.warn('⚠️ Failed to initialize Speaking service:', error);
        }
    }

    // Add other service initializations here as needed
    // services.animationService = ...
    // services.memoryService = ...

    return services;
}

/**
 * Register tools based on configuration
 * 
 * @private
 * @param {Object} config - Configuration object
 * @param {Object} services - Initialized services
 * @returns {Promise<Object>} Tool registration result
 */
async function registerToolsFromConfig(config, services) {
    logger.info('🛠️ Registering tools based on configuration...');

    if (config.tools.autoRegister) {
        // Use auto-registration with configuration
        return autoRegisterTools(services, {
            speaking: config.speaking,
            animation: config.animation,
            session: config.session
        });
    } else {
        // Manual registration based on enabled tools
        const result = {
            registered: [],
            failed: [],
            tools: []
        };

        // Register tools individually based on config
        if (config.tools.enableSpeaking) {
            try {
                const { registerSpeaking } = await import('./index.js');
                const speakingTools = registerSpeaking(services.speakingService, services.audioPlayer, config.speaking);
                result.registered.push({ collection: 'Speaking', count: speakingTools.length });
                result.tools.push(...speakingTools);
            } catch (error) {
                result.failed.push({ collection: 'Speaking', error: error.message });
            }
        }

        if (config.tools.enableAnimation) {
            try {
                const { registerAnimationTools } = await import('./index.js');
                const animationTools = registerAnimationTools(services.animationRegistry, config.animation);
                result.registered.push({ collection: 'Animation', count: animationTools.length });
                result.tools.push(...animationTools);
            } catch (error) {
                result.failed.push({ collection: 'Animation', error: error.message });
            }
        }

        // Conversation tools removed - functionality now handled by System 2 contextual analysis
        if (config.tools.enableConversation) {
            // Conversation tools have been consolidated into System 2 for better contextual analysis
            result.registered.push({ collection: 'Conversation', count: 0, note: 'Handled by System 2' });
        }

        if (config.tools.enableCharacterSearch) {
            // CharacterSearch tools not available - functionality may be in viewer components
            result.failed.push({ collection: 'CharacterSearch', error: 'CharacterSearchTool module not available in agent/tools' });
        }

        if (config.tools.enableWebSearch) {
            try {
                const { registerWebSearchTools } = await import('./index.js');
                const webSearchTools = registerWebSearchTools(config.webSearch || {});
                result.registered.push({ collection: 'WebSearch', count: webSearchTools.length });
                result.tools.push(...webSearchTools);
            } catch (error) {
                result.failed.push({ collection: 'WebSearch', error: error.message });
            }
        }

        return result;
    }
}

/**
 * Merge configuration objects deeply with circular reference protection
 * 
 * @private
 * @param {Object} defaultConfig - Default configuration
 * @param {Object} userConfig - User configuration
 * @param {WeakSet} visited - Set to track visited objects (prevents circular references)
 * @returns {Object} Merged configuration
 */
function mergeConfig(defaultConfig, userConfig, visited = new WeakSet()) {
    // Handle null/undefined cases
    if (!defaultConfig) defaultConfig = {};
    if (!userConfig) return { ...defaultConfig };

    // Check for circular references
    if (visited.has(userConfig)) {
        logger.warn('Circular reference detected in config, skipping merge');
        return { ...defaultConfig };
    }

    visited.add(userConfig);
    const merged = { ...defaultConfig };

    for (const [key, value] of Object.entries(userConfig)) {
        if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
            // Skip functions and DOM elements that could cause recursion
            if (typeof value === 'function' ||
                (typeof window !== 'undefined' && value instanceof HTMLElement)) {
                merged[key] = value;
            } else {
                merged[key] = mergeConfig(merged[key] || {}, value, visited);
            }
        } else {
            merged[key] = value;
        }
    }

    visited.delete(userConfig);
    return merged;
}

/**
 * Validate configuration object
 * 
 * @private
 * @param {Object} config - Configuration to validate
 * @returns {Object} Validation result
 */
function validateConfig(config) {
    const errors = [];

    // Validate required sections
    const requiredSections = ['speaking', 'animation', 'audio', 'session', 'services', 'tools'];
    for (const section of requiredSections) {
        if (!config[section] || typeof config[section] !== 'object') {
            errors.push(`Missing or invalid configuration section: ${section}`);
        }
    }

    // Validate session ID
    if (!config.session?.sessionId || typeof config.session.sessionId !== 'string') {
        errors.push('Session ID must be a non-empty string');
    }

    // Validate voice if specified
    if (config.speaking?.defaultVoice) {
        const validVoices = ['Serena', 'Ethan', 'Chelsie', 'Cherry'];
        if (!validVoices.includes(config.speaking.defaultVoice)) {
            errors.push(`Invalid default voice: ${config.speaking.defaultVoice}. Valid voices: ${validVoices.join(', ')}`);
        }
    }

    return {
        valid: errors.length === 0,
        errors
    };
}

/**
 * Create a tool configuration for avatar applications
 * 
 * @param {Object} avatarServices - Avatar-specific services
 * @param {Object} overrides - Configuration overrides
 * @returns {Object} Avatar tool configuration
 */
export function createAvatarToolConfig(avatarServices = {}, overrides = {}) {
    return mergeConfig(DEFAULT_TOOL_CONFIG, {
        session: {
            sessionId: avatarServices.sessionId || 'avatar_session',
            enableStateManagement: true,
            enableMemory: true
        },
        services: {
            audioPlayer: avatarServices.audioPlayer,
            aliyunModel: avatarServices.aliyunModel,
            stateManager: avatarServices.stateManager,
            ...avatarServices
        },
        speaking: {
            defaultVoice: 'Serena',
            enableTranscription: true,
            streaming: true,
            enableVoiceCloning: avatarServices.enableVoiceCloning || false
        },
        tools: {
            enableSpeaking: true,
            enableAnimation: true,
            enableVoiceCloning: avatarServices.enableVoiceCloning || false,
            enableConversation: true,
            autoRegister: true
        },
        ...overrides
    });
}

/**
 * Create a tool configuration for general applications
 * 
 * @param {Object} services - Application services
 * @param {Object} overrides - Configuration overrides
 * @returns {Object} General tool configuration
 */
export function createGeneralToolConfig(services = {}, overrides = {}) {
    return mergeConfig(DEFAULT_TOOL_CONFIG, {
        session: {
            sessionId: services.sessionId || 'general_session',
            enableStateManagement: false,
            enableMemory: false
        },
        services: {
            ...services
        },
        speaking: {
            defaultVoice: 'Serena',
            enableTranscription: false,
            streaming: true,
            enableVoiceCloning: false
        },
        tools: {
            enableSpeaking: true,
            enableAnimation: false,
            enableVoiceCloning: false,
            enableConversation: true,
            autoRegister: true
        },
        ...overrides
    });
}

/**
 * Initialize tools for applications with automatic configuration detection
 * 
 * @param {Object} appServices - Application-provided services
 * @param {Object} appConfig - Application-specific configuration
 * @returns {Promise<Object>} Initialization result with tools ready for use
 */
export async function initializeToolsForApp(appServices = {}, appConfig = {}) {
    logger.info('🛠️ Initializing tools for application');

    // Determine configuration type based on services
    const isAvatarApp = !!(appServices.stateManager || appServices.audioPlayer);

    const config = isAvatarApp
        ? createAvatarToolConfig(appServices, appConfig)
        : createGeneralToolConfig(appServices, appConfig);

    const result = await initializeTools(config, appServices);

    if (result.success) {
        logger.info(`✅ Tool initialization complete: ${result.tools.length} tools ready`);
    } else {
        logger.error('❌ Tool initialization failed:', result.error);
    }

    return result;
}

// Export commonly used functions
export { toolManager };
export default initializeTools;