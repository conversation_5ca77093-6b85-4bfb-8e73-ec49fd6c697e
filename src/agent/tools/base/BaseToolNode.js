/**
 * Enhanced Tool Node for LangGraph Integration
 * Follows LangGraph best practices while adding monitoring and service injection
 * Uses composition over inheritance to maintain LangGraph compatibility
 */

import { ToolNode } from '@langchain/langgraph/prebuilt';
import { createLogger } from '@/utils/logger';
import { CircuitBreaker } from '@/agent/services/infrastructure/reliability/CircuitBreaker.js';
import { CacheManager } from '@/agent/services/infrastructure/caching/CacheManager.js';
import { LogLevel } from '@/utils/logger';

// Lazy-loaded central ErrorHandler for tool errors (best-effort)
let __toolErrorHandlerPromise = null;
function __getToolErrorHandler() {
    if (!__toolErrorHandlerPromise) {
        __toolErrorHandlerPromise = import('@/agent/arch/dualbrain/services/ErrorHandler.js')
            .then(mod => {
                try {
                    if (typeof mod.createErrorHandler === 'function') return mod.createErrorHandler();
                    if (typeof mod.ErrorHandler === 'function') return new mod.ErrorHandler();
                } catch (_) { return null; }
                return null;
            })
            .catch(() => null);
    }
    return __toolErrorHandlerPromise;
}
/**
 * Enhanced Tool Node Class
 * Wraps LangGraph ToolNode with enhanced functionality while maintaining compatibility
 */
export class BaseToolNode {
    constructor(tools, options = {}) {
        // Use composition - create internal ToolNode instance
        this.toolNode = new ToolNode(tools);

        this.logger = createLogger(options.loggerName || 'BaseToolNode', LogLevel.DEBUG);
        this.tools = tools;
        this.options = {
            validateInput: false,  // Let LangGraph handle validation
            validateOutput: false, // Let LangGraph handle validation  
            handleErrors: true,    // We add error handling wrapper
            includeMetadata: true, // Add execution metadata
            enableStatistics: true, // Track tool usage stats
            ...options
        };

        // Tool execution statistics (enhanced with caching metrics)
        this.stats = {
            totalExecutions: 0,
            successfulExecutions: 0,
            failedExecutions: 0,
            cacheHits: 0, // WEEK 2-3: Track cache hits
            cacheMisses: 0, // WEEK 2-3: Track cache misses
            averageExecutionTime: 0,
            averageCacheHitTime: 0, // WEEK 2-3: Track cache hit performance
            toolUsage: new Map(),
            lastExecution: null
        };

        // WEEK 2-3: Node-level caching with TTL
        this.cacheManager = options.cacheManager || new CacheManager({
            modelInstanceTTL: options.cacheTTL || 300000, // 5 minutes default
            routingDecisionTTL: options.routingCacheTTL || 60000, // 1 minute default
            maxCacheSize: options.maxCacheSize || 50, // Smaller cache for tool nodes
            logger: this.logger
        });

        // Cache configuration
        this.cacheConfig = {
            enableCaching: options.enableCaching !== false, // Default to enabled
            cacheTTL: options.cacheTTL || 300000, // 5 minutes
            cacheKeyPrefix: options.cacheKeyPrefix || 'tool_node',
            excludeFromCache: new Set(options.excludeFromCache || []), // Tool names to exclude from caching
            ...options.cache
        };

        // WEEK 2-3: Circuit breaker for service reliability
        this.circuitBreaker = options.circuitBreaker || new CircuitBreaker({
            failureThreshold: options.failureThreshold || 5,
            recoveryTimeout: options.recoveryTimeout || 30000, // 30 seconds
            halfOpenRetries: options.halfOpenRetries || 3,
            logger: this.logger
        });

        // WEEK 2-3: Enhanced service injection configuration
        this.serviceRegistry = new Map(); // Track injected services
        this.serviceHealthChecks = new Map(); // Service health check functions
        this.serviceConfig = {
            sessionId: options.sessionId || 'default',
            audioPlayer: options.audioPlayer,
            aliyunModel: options.aliyunModel,
            avatarController: options.avatarController,
            ...options.services
        };

        // Register services with circuit breaker
        this._registerServicesWithCircuitBreaker();

        this.logger.info(`Enhanced ToolNode initialized with ${tools.length} tools`, {
            toolNames: tools.map(t => t.name),
            enableStats: this.options.enableStatistics,
            hasServices: Object.keys(this.serviceConfig).length > 1, // > 1 because sessionId is always present
            cachingEnabled: this.cacheConfig.enableCaching,
            cacheTTL: this.cacheConfig.cacheTTL,
            circuitBreakerEnabled: true
        });
    }

    /**
     * Register services with circuit breaker monitoring
     * WEEK 2-3: Service injection optimization
     * @private
     */
    _registerServicesWithCircuitBreaker() {
        for (const [serviceName, service] of Object.entries(this.serviceConfig)) {
            if (service && typeof service === 'object' && serviceName !== 'sessionId') {
                this.serviceRegistry.set(serviceName, service);

                // Set up health check if service has a health check method
                if (typeof service.healthCheck === 'function') {
                    this.serviceHealthChecks.set(serviceName, service.healthCheck.bind(service));
                }

                this.logger.debug(`Registered service with circuit breaker: ${serviceName}`);
            }
        }
    }

    /**
     * Enhanced tool execution using composition pattern
     * Wraps LangGraph ToolNode while adding monitoring, service injection, caching, and circuit breaker
     * WEEK 2-3: Added node-level caching with TTL and circuit breaker pattern
     */
    async invoke(state, config = {}) {
        const startTime = Date.now();

        try {
            // WEEK 2-3: Perform service health checks before execution
            await this._performServiceHealthChecks();

            // Enhance config with service injection for tools
            const enhancedConfig = this._createEnhancedConfig(config);

            // WEEK 2-3: Check cache before execution
            const cacheKey = await this._generateCacheKey(state, enhancedConfig);
            let fromCache = false;

            if (this.cacheConfig.enableCaching && cacheKey) {
                const cachedResult = await this.cacheManager.getModel(cacheKey);
                if (cachedResult) {
                    this.logger.debug(`Cache hit for tool execution: ${cacheKey}`);
                    this._updateStats(true, Date.now() - startTime, state, true); // Mark as cache hit
                    this._logToolExecution(state, 'cache_hit', Date.now() - startTime);

                    // Add cache metadata
                    return this._addExecutionMetadata(cachedResult, {
                        executionTime: Date.now() - startTime,
                        sessionId: enhancedConfig.sessionId || this.serviceConfig.sessionId,
                        toolsExecuted: this._extractExecutedTools(state),
                        fromCache: true
                    });
                }
            }

            // Log tool execution start
            this._logToolExecution(state, 'start');

            // WEEK 2-3: Execute with circuit breaker protection
            const result = await this.circuitBreaker.execute('tool_execution', async () => {
                return await this.toolNode.invoke(state, enhancedConfig);
            });

            // WEEK 2-3: Cache the result if caching is enabled
            if (this.cacheConfig.enableCaching && cacheKey && this._shouldCacheResult(result, state)) {
                await this.cacheManager.setModel(cacheKey, result, 'tool_execution');
                this.logger.debug(`Cached tool execution result: ${cacheKey}`);
            }

            // Track successful execution
            this._updateStats(true, Date.now() - startTime, state, fromCache);

            // Log successful completion
            this._logToolExecution(state, 'success', Date.now() - startTime);

            // Add metadata if enabled
            if (this.options.includeMetadata) {
                return this._addExecutionMetadata(result, {
                    executionTime: Date.now() - startTime,
                    sessionId: enhancedConfig.sessionId || this.serviceConfig.sessionId,
                    toolsExecuted: this._extractExecutedTools(state),
                    fromCache,
                    circuitBreakerState: this.circuitBreaker.getState('tool_execution')
                });
            }

            return result;

        } catch (error) {
            // Track failed execution
            this._updateStats(false, Date.now() - startTime, state);

            // Log error
            this._logToolExecution(state, 'error', Date.now() - startTime, error);

            // WEEK 2-3: Handle circuit breaker failures gracefully
            if (error.message.includes('Circuit breaker is OPEN')) {
                this.logger.warn('Circuit breaker is open, providing fallback response');
                return this._createCircuitBreakerFallbackResponse(error, state);
            }

            if (this.options.handleErrors) {
                return this._createErrorResponse(error, state);
            }

            throw error;
        }
    }

    /**
     * Create enhanced configuration with service injection
     * @private
     */
    _createEnhancedConfig(config) {
        return {
            ...this.serviceConfig,
            ...config,
            configurable: {
                thread_id: this.serviceConfig.sessionId,
                ...config.configurable
            },
            // Ensure tools have access to services
            services: this.serviceConfig
        };
    }

    /**
     * Log tool execution phases
     * @private
     */
    _logToolExecution(state, phase, executionTime = null, error = null) {
        if (!this.options.enableStatistics && phase !== 'error') return;

        const toolCalls = this._extractToolCalls(state);
        const logData = {
            phase,
            toolCount: toolCalls.length,
            toolNames: toolCalls.map(tc => tc.name),
            sessionId: this.serviceConfig.sessionId
        };

        if (executionTime !== null) {
            logData.executionTime = executionTime;
        }

        if (error) {
            logData.error = error.message;
            this.logger.error('Tool execution failed:', logData);
        } else {
            this.logger.debug(`Tool execution ${phase}:`, logData);
        }
    }

    /**
     * Extract tool calls from state
     * @private
     */
    _extractToolCalls(state) {
        const lastMessage = state.messages?.[state.messages.length - 1];
        return lastMessage?.tool_calls || [];
    }

    /**
     * Extract tools that were executed
     * @private
     */
    _extractExecutedTools(state) {
        return this._extractToolCalls(state).map(tc => ({
            name: tc.name,
            id: tc.id,
            args: tc.args
        }));
    }

    /**
     * Create error response following LangGraph patterns
     * @private
     */
    _createErrorResponse(error, state) {
        this.logger.error('Creating error response for tool execution:', error);

        const toolCalls = this._extractToolCalls(state);
        const errorMessages = toolCalls.map(toolCall => ({
            role: 'tool',
            content: `Tool execution failed: ${error.message}`,
            tool_call_id: toolCall.id || 'unknown',
            name: toolCall.name || 'unknown_tool'
        }));

        // If no tool calls found, create generic error message
        if (errorMessages.length === 0) {
            errorMessages.push({
                role: 'tool',
                content: `Tool execution failed: ${error.message}`,
                tool_call_id: 'error',
                name: 'error_handler'
            });
        }

        return {
            messages: errorMessages
        };
    }

    /**
     * Add execution metadata to results
     * @private
     */
    _addExecutionMetadata(result, metadata) {
        return {
            ...result,
            metadata: {
                ...(result.metadata || {}),
                toolExecution: {
                    timestamp: Date.now(),
                    nodeType: 'EnhancedToolNode',
                    ...metadata
                }
            }
        };
    }

    /**
     * Generate cache key for tool execution
     * WEEK 2-3: Intelligent cache key generation
     * @private
     */
    async _generateCacheKey(state, config) {
        try {
            const toolCalls = this._extractToolCalls(state);
            if (toolCalls.length === 0) return null;

            // Check if all tools in this execution should be cached
            const shouldCache = toolCalls.every(tc => !this.cacheConfig.excludeFromCache.has(tc.name));
            if (!shouldCache) return null;

            // Create deterministic cache key from tool calls and relevant config
            const keyData = {
                tools: toolCalls.map(tc => ({
                    name: tc.name,
                    args: this._normalizeArgs(tc.args)
                })),
                sessionId: config.sessionId || this.serviceConfig.sessionId,
                // Include relevant config that affects execution
                configHash: this._hashConfig(config)
            };

            return this.cacheManager.generateModelKey(`${this.cacheConfig.cacheKeyPrefix}_execution`, keyData);
        } catch (error) {
            this.logger.warn('Failed to generate cache key:', error);
            return null;
        }
    }

    /**
     * Normalize tool arguments for consistent caching
     * @private
     */
    _normalizeArgs(args) {
        if (!args || typeof args !== 'object') return args;

        // Sort object keys and remove non-deterministic fields
        const normalized = {};
        const keys = Object.keys(args).sort();

        for (const key of keys) {
            // Skip non-deterministic fields
            if (key.includes('timestamp') || key.includes('random') || key.includes('id')) {
                continue;
            }
            normalized[key] = args[key];
        }

        return normalized;
    }

    /**
     * Hash configuration for cache key
     * @private
     */
    _hashConfig(config) {
        // Only include config properties that affect tool execution
        const relevantConfig = {
            temperature: config.temperature,
            maxTokens: config.maxTokens,
            model: config.model
        };

        return JSON.stringify(relevantConfig);
    }

    /**
     * Determine if result should be cached
     * @private
     */
    _shouldCacheResult(result, state) {
        if (!result) return false;

        // Don't cache error results
        if (result.messages && result.messages.some(msg => msg.content && msg.content.includes('failed'))) {
            return false;
        }

        // Don't cache results with sensitive information
        const resultStr = JSON.stringify(result).toLowerCase();
        const sensitiveKeywords = ['password', 'token', 'key', 'secret', 'auth'];
        if (sensitiveKeywords.some(keyword => resultStr.includes(keyword))) {
            return false;
        }

        // Don't cache results that are too large
        const resultSize = JSON.stringify(result).length;
        if (resultSize > 10000) { // 10KB limit
            this.logger.debug('Skipping cache for large result:', resultSize);
            return false;
        }

        return true;
    }

    /**
     * Perform service health checks before execution
     * WEEK 2-3: Service reliability monitoring
     * @private
     */
    async _performServiceHealthChecks() {
        const healthCheckPromises = [];

        for (const [serviceName, healthCheckFn] of this.serviceHealthChecks.entries()) {
            healthCheckPromises.push(
                this._checkServiceHealth(serviceName, healthCheckFn)
            );
        }

        if (healthCheckPromises.length > 0) {
            await Promise.allSettled(healthCheckPromises);
        }
    }

    /**
     * Check individual service health
     * @private
     */
    async _checkServiceHealth(serviceName, healthCheckFn) {
        try {
            await this.circuitBreaker.execute(serviceName, async () => {
                const isHealthy = await healthCheckFn();
                if (!isHealthy) {
                    throw new Error(`Service ${serviceName} health check failed`);
                }
                return isHealthy;
            });
        } catch (error) {
            this.logger.warn(`Service health check failed for ${serviceName}:`, error.message);
            // Don't throw - just log and continue with circuit breaker handling
        }
    }

    /**
     * Create fallback response for circuit breaker failures
     * WEEK 2-3: Circuit breaker pattern implementation
     * @private
     */
    _createCircuitBreakerFallbackResponse(error, state) {
        this.logger.warn('Creating circuit breaker fallback response');

        const toolCalls = this._extractToolCalls(state);
        const fallbackMessages = toolCalls.map(toolCall => ({
            role: 'tool',
            content: `Service temporarily unavailable. Circuit breaker is open for ${toolCall.name}. Please try again later.`,
            tool_call_id: toolCall.id || 'circuit_breaker_fallback',
            name: toolCall.name || 'circuit_breaker_handler'
        }));

        // If no tool calls found, create generic fallback message
        if (fallbackMessages.length === 0) {
            fallbackMessages.push({
                role: 'tool',
                content: 'Services are temporarily unavailable due to circuit breaker protection. Please try again later.',
                tool_call_id: 'circuit_breaker_fallback',
                name: 'circuit_breaker_handler'
            });
        }

        return {
            messages: fallbackMessages,
            metadata: {
                circuitBreakerTriggered: true,
                error: error.message,
                timestamp: Date.now()
            }
        };
    }

    /**
     * Update execution statistics
     * @private
     */
    _updateStats(success, executionTime, state = null, fromCache = false) {
        if (!this.options.enableStatistics) return;

        this.stats.totalExecutions++;
        this.stats.lastExecution = Date.now();

        // WEEK 2-3: Track cache metrics
        if (fromCache) {
            this.stats.cacheHits++;
            // Update average cache hit time
            this.stats.averageCacheHitTime = (
                (this.stats.averageCacheHitTime * (this.stats.cacheHits - 1)) + executionTime
            ) / this.stats.cacheHits;
        } else {
            this.stats.cacheMisses++;
        }

        if (success) {
            this.stats.successfulExecutions++;
        } else {
            this.stats.failedExecutions++;
        }

        // Update average execution time (excluding cache hits for accurate performance measurement)
        if (!fromCache) {
            const missCount = this.stats.cacheMisses;
            this.stats.averageExecutionTime = (
                (this.stats.averageExecutionTime * (missCount - 1)) + executionTime
            ) / missCount;
        }

        // Track individual tool usage
        if (state) {
            const toolCalls = this._extractToolCalls(state);
            toolCalls.forEach(toolCall => {
                const toolName = toolCall.name;
                const current = this.stats.toolUsage.get(toolName) || {
                    executions: 0,
                    successes: 0,
                    failures: 0,
                    totalTime: 0,
                    averageTime: 0
                };

                current.executions++;
                current.totalTime += executionTime;
                current.averageTime = current.totalTime / current.executions;

                if (success) {
                    current.successes++;
                } else {
                    current.failures++;
                }

                this.stats.toolUsage.set(toolName, current);
            });
        }
    }

    /**
     * Get comprehensive execution statistics
     * WEEK 2-3: Enhanced with caching metrics
     */
    getStats() {
        const stats = {
            ...this.stats,
            successRate: this.stats.totalExecutions > 0
                ? (this.stats.successfulExecutions / this.stats.totalExecutions) * 100
                : 0,
            // WEEK 2-3: Cache performance metrics
            cacheHitRate: this.stats.totalExecutions > 0
                ? (this.stats.cacheHits / this.stats.totalExecutions) * 100
                : 0,
            cacheMissRate: this.stats.totalExecutions > 0
                ? (this.stats.cacheMisses / this.stats.totalExecutions) * 100
                : 0,
            cacheEfficiency: {
                totalHits: this.stats.cacheHits,
                totalMisses: this.stats.cacheMisses,
                hitRate: this.stats.totalExecutions > 0
                    ? (this.stats.cacheHits / this.stats.totalExecutions) * 100
                    : 0,
                averageHitTime: this.stats.averageCacheHitTime,
                averageMissTime: this.stats.averageExecutionTime,
                performanceGain: this.stats.averageExecutionTime > 0 && this.stats.averageCacheHitTime > 0
                    ? ((this.stats.averageExecutionTime - this.stats.averageCacheHitTime) / this.stats.averageExecutionTime) * 100
                    : 0
            },
            toolUsageStats: {}
        };

        // Convert Map to object for easier serialization
        for (const [toolName, usage] of this.stats.toolUsage.entries()) {
            stats.toolUsageStats[toolName] = {
                ...usage,
                successRate: usage.executions > 0
                    ? (usage.successes / usage.executions) * 100
                    : 0
            };
        }

        return stats;
    }

    /**
     * Get statistics for a specific tool
     */
    getToolStats(toolName) {
        const usage = this.stats.toolUsage.get(toolName);
        if (!usage) return null;

        return {
            ...usage,
            successRate: usage.executions > 0
                ? (usage.successes / usage.executions) * 100
                : 0
        };
    }

    /**
     * Reset statistics
     * WEEK 2-3: Enhanced to include cache stats
     */
    resetStats() {
        this.stats = {
            totalExecutions: 0,
            successfulExecutions: 0,
            failedExecutions: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageExecutionTime: 0,
            averageCacheHitTime: 0,
            toolUsage: new Map(),
            lastExecution: null
        };
        this.logger.info('Statistics reset');
    }

    /**
     * Clear cache
     * WEEK 2-3: Cache management methods
     */
    async clearCache() {
        await this.cacheManager.clear();
        this.logger.info('Tool node cache cleared');
    }

    /**
     * Invalidate cache entries by pattern
     * WEEK 2-3: Selective cache invalidation
     */
    async invalidateCache(pattern) {
        const invalidated = await this.cacheManager.invalidate(pattern);
        this.logger.info(`Invalidated ${invalidated} cache entries matching pattern: ${pattern}`);
        return invalidated;
    }

    /**
     * Get cache metrics
     * WEEK 2-3: Cache performance monitoring
     */
    getCacheMetrics() {
        return this.cacheManager.getMetrics();
    }

    /**
     * Update cache configuration at runtime
     * WEEK 2-3: Dynamic cache configuration
     */
    updateCacheConfig(newConfig) {
        this.cacheConfig = {
            ...this.cacheConfig,
            ...newConfig
        };
        this.logger.debug('Cache configuration updated', newConfig);
    }

    /**
     * Get circuit breaker metrics
     * WEEK 2-3: Circuit breaker monitoring
     */
    getCircuitBreakerMetrics() {
        return this.circuitBreaker.getMetrics();
    }

    /**
     * Reset circuit breaker for specific service
     * WEEK 2-3: Circuit breaker management
     */
    resetCircuitBreaker(serviceName = null) {
        this.circuitBreaker.reset(serviceName);
        this.logger.info(`Circuit breaker reset for service: ${serviceName || 'all services'}`);
    }

    /**
     * Force circuit breaker state for testing
     * WEEK 2-3: Circuit breaker testing support
     */
    forceCircuitBreakerState(serviceName, state) {
        this.circuitBreaker.forceState(serviceName, state);
        this.logger.warn(`Circuit breaker manually set to ${state} for ${serviceName}`);
    }

    /**
     * Register new service with health check
     * WEEK 2-3: Dynamic service registration
     */
    registerService(serviceName, service, healthCheckFn = null) {
        this.serviceConfig[serviceName] = service;
        this.serviceRegistry.set(serviceName, service);

        if (healthCheckFn && typeof healthCheckFn === 'function') {
            this.serviceHealthChecks.set(serviceName, healthCheckFn);
        }

        this.logger.info(`Service registered: ${serviceName}`, {
            hasHealthCheck: !!healthCheckFn
        });
    }

    /**
     * Unregister service
     * WEEK 2-3: Dynamic service management
     */
    unregisterService(serviceName) {
        delete this.serviceConfig[serviceName];
        this.serviceRegistry.delete(serviceName);
        this.serviceHealthChecks.delete(serviceName);
        this.circuitBreaker.reset(serviceName);

        this.logger.info(`Service unregistered: ${serviceName}`);
    }

    /**
     * Update service configuration at runtime
     * WEEK 2-3: Enhanced with circuit breaker re-registration
     */
    updateServices(newServices) {
        this.serviceConfig = {
            ...this.serviceConfig,
            ...newServices
        };

        // Re-register services with circuit breaker
        this._registerServicesWithCircuitBreaker();

        this.logger.debug('Service configuration updated', {
            updatedKeys: Object.keys(newServices)
        });
    }

    /**
     * Update options at runtime
     */
    updateOptions(newOptions) {
        this.options = {
            ...this.options,
            ...newOptions
        };
        this.logger.debug('Options updated', newOptions);
    }

    /**
     * Get current service configuration
     */
    getServiceConfig() {
        return { ...this.serviceConfig };
    }

    /**
     * Get available tools
     */
    getTools() {
        return this.tools.map(tool => ({
            name: tool.name,
            description: tool.description,
            hasSchema: !!tool.schema
        }));
    }
}

/**
 * Base Tool Class
 * Provides common functionality for individual tools with enhanced LangGraph integration
 */
export class BaseTool {
    constructor(name, description, schema, toolFunction, options = {}) {
        this.name = name;
        this.description = description;
        this.schema = schema;
        this.toolFunction = toolFunction;
        this.logger = createLogger(options.loggerName || `Tool:${name}`);

        this.options = {
            validateInput: true,
            validateOutput: true,
            handleErrors: true,
            logExecution: true,
            ...options
        };

        // Tool-specific statistics
        this.stats = {
            executions: 0,
            successes: 0,
            failures: 0,
            totalExecutionTime: 0,
            averageExecutionTime: 0,
            lastExecuted: null
        };
    }

    /**
     * Execute the tool with enhanced functionality
     */
    async execute(input, config = {}) {
        const startTime = Date.now();

        try {
            if (this.options.logExecution) {
                this.logger.info(`Executing tool: ${this.name}`, {
                    input: this._sanitizeInput(input),
                    hasConfig: !!config
                });
            }

            // Validate input if enabled
            if (this.options.validateInput) {
                this._validateInput(input);
            }

            // Execute the tool function
            const result = await this.toolFunction(input, config);

            // Validate output if enabled
            if (this.options.validateOutput) {
                this._validateOutput(result);
            }

            // Update statistics
            this._updateStats(true, Date.now() - startTime);

            if (this.options.logExecution) {
                this.logger.info(`Tool execution completed: ${this.name}`, {
                    success: true,
                    executionTime: Date.now() - startTime
                });
            }

            return result;

        } catch (error) {
            this._updateStats(false, Date.now() - startTime);

            if (this.options.handleErrors) {
                return this._handleError(error, input);
            }

            throw error;
        }
    }

    /**
     * Validate input according to schema
     * @private
     */
    _validateInput(input) {
        try {
            this.schema.parse(input);
        } catch (error) {
            throw new Error(`Input validation failed for tool ${this.name}: ${error.message}`);
        }
    }

    /**
     * Validate output
     * @private
     */
    _validateOutput(result) {
        if (result === undefined || result === null) {
            throw new Error(`Tool ${this.name} returned null/undefined result`);
        }
    }

    /**
     * Handle execution errors
     * @private
     */
    _handleError(error, input) {
        this.logger.error(`Tool execution error: ${this.name}`, error);

        // Delegate to central ErrorHandler asynchronously
        try {
            __getToolErrorHandler().then(handler => {
                if (handler && typeof handler.handleError === 'function') {
                    const normalized = error instanceof Error ? error : new Error(String(error));
                    handler.handleError(normalized, {
                        component: `Tool:${this.name}`,
                        operation: 'tool_execution',
                        inputPreview: this._sanitizeInput(input)
                    }).catch(() => { });
                }
            }).catch(() => { });
        } catch (_) { /* ignore */ }

        return {
            success: false,
            error: error.message,
            tool: this.name,
            input: this._sanitizeInput(input)
        };
    }

    /**
     * Sanitize input for logging (remove sensitive data)
     * @private
     */
    _sanitizeInput(input) {
        const sanitized = { ...input };

        // Remove potential sensitive fields
        const sensitiveFields = ['apiKey', 'password', 'token', 'secret', 'auth'];

        for (const field of sensitiveFields) {
            if (field in sanitized) {
                sanitized[field] = '[REDACTED]';
            }
        }

        return sanitized;
    }

    /**
     * Update tool execution statistics
     * @private
     */
    _updateStats(success, executionTime) {
        this.stats.executions++;
        this.stats.lastExecuted = new Date();
        this.stats.totalExecutionTime += executionTime;
        this.stats.averageExecutionTime = this.stats.totalExecutionTime / this.stats.executions;

        if (success) {
            this.stats.successes++;
        } else {
            this.stats.failures++;
        }
    }

    /**
     * Get tool statistics
     */
    getStats() {
        return {
            ...this.stats,
            successRate: this.stats.executions > 0
                ? (this.stats.successes / this.stats.executions) * 100
                : 0
        };
    }

    /**
     * Reset tool statistics
     */
    resetStats() {
        this.stats = {
            executions: 0,
            successes: 0,
            failures: 0,
            totalExecutionTime: 0,
            averageExecutionTime: 0,
            lastExecuted: null
        };
    }
}

/**
 * Create enhanced ToolNode for LangGraph workflows
 * Factory function following LangGraph patterns
 */
export function createEnhancedToolNode(tools, options = {}) {
    return new BaseToolNode(tools, options);
}

/**
 * Create enhanced tool with base functionality
 * Factory function for individual tools
 */
export function createEnhancedTool(name, description, schema, toolFunction, options = {}) {
    return new BaseTool(name, description, schema, toolFunction, options);
}

export default BaseToolNode;