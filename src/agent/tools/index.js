/**
 * Universal Tool Calling Interface
 * Enhanced with BaseToolNode for LangGraph integration
 * 
 * This module provides a generalized tool calling interface that can support
 * any type of tool (animation, API calls, calculations, speaking, etc.) through LangChain.
 */

import { tool } from '@langchain/core/tools';
import { z } from 'zod';

// Import specific tool collections
import {
    selectAnimationTool,
    listAnimationsTool,
    recommendAnimationsTool,
    animationToolCollection,
    ANIMATION_TOOL_NAMES,
    createAnimationToolNode,
    createUnifiedToolNode
} from './avatar/animation.js';
import { BaseToolNode, createEnhancedToolNode } from './base/BaseToolNode.js';
import {
    agentTTSTool,
    voiceProfileTool,
    agentSpeakingToolCollection,
    AGENT_SPEAKING_TOOL_NAMES,
    AgentSpeakingService,
    createSpeakingToolNode
} from './avatar/speaking.js';
import {
    webSearchTool,
    webSearchToolCollection,
    WEB_SEARCH_TOOL_NAMES
} from './search/WebSearchTool.js';
// Conversation tools removed - functionality consolidated in ContextualAnalysisService

// Voice cloning functionality is now integrated into Speaking tools

// Note: StreamProcessor functionality is now integrated directly in core.js
// Use LangGraphAgentService.processLangGraphStream() for streaming functionality

/**
 * Voice Cloning Tool Collection
 * Uses Aliyun CosyVoice-v2 for advanced voice cloning and synthesis
 */
// Voice cloning is now part of Speaking tools

/**
 * Universal Tool Manager
 * Manages registration and execution of various tool types
 */
export class ToolManager {
    constructor() {
        this.tools = new Map();
        this.toolSchemas = new Map();
        this.toolDescriptions = new Map();
        this.toolCategories = new Map(); // Track tool categories
        this.toolCollections = new Map(); // Track tool collections
    }

    /**
     * Register a new tool with the manager
     * 
     * @param {string} toolName - Unique name for the tool
     * @param {Function} toolFunction - Function to execute the tool
     * @param {Object} schema - Zod schema for tool parameters
     * @param {string} description - Description of what the tool does
     * @param {Object} options - Additional tool options
     */
    registerTool(toolName, toolFunction, schema, description, options = {}) {
        const langChainTool = tool(
            toolFunction,
            {
                name: toolName,
                description,
                schema,
                ...options
            }
        );

        this.tools.set(toolName, langChainTool);
        this.toolSchemas.set(toolName, schema);
        this.toolDescriptions.set(toolName, description);

        // Track category if provided
        if (options.category) {
            this.toolCategories.set(toolName, options.category);
        }

        console.log(`[ToolManager] Registered tool: ${toolName}${options.category ? ` (${options.category})` : ''}`);
        return langChainTool;
    }

    /**
     * Register a collection of tools
     * 
     * @param {string} collectionName - Name of the tool collection
     * @param {Array|Object} toolCollection - Array of tool instances or object with tools
     * @param {Object} options - Registration options
     * @returns {Array} Array of registered tools
     */
    registerToolCollection(collectionName, toolCollection, options = {}) {
        const registeredTools = [];
        const tools = Array.isArray(toolCollection) ? toolCollection : Object.values(toolCollection);

        console.log(`[ToolManager] Registering tool collection: ${collectionName} (${tools.length} tools)`);

        for (const tool of tools) {
            if (tool && tool.name) {
                this.tools.set(tool.name, tool);
                this.toolSchemas.set(tool.name, tool.schema);
                this.toolDescriptions.set(tool.name, tool.description);

                // Set category from collection name or options
                const category = options.category || collectionName;
                this.toolCategories.set(tool.name, category);

                registeredTools.push(tool);
                console.log(`[ToolManager] Registered ${collectionName} tool: ${tool.name}`);
            } else {
                console.warn(`[ToolManager] Invalid tool in collection ${collectionName}:`, tool);
            }
        }

        // Track the collection
        this.toolCollections.set(collectionName, registeredTools);

        console.log(`[ToolManager] Registered ${registeredTools.length} tools from ${collectionName} collection`);
        return registeredTools;
    }

    /**
     * Register a LangChain tool instance directly
     * 
     * @param {Object} langChainTool - LangChain tool instance
     * @param {Object} options - Registration options
     * @returns {Object} The registered tool
     */
    registerLangChainTool(langChainTool, options = {}) {
        if (!langChainTool || !langChainTool.name) {
            throw new Error('Invalid LangChain tool: must have a name');
        }

        this.tools.set(langChainTool.name, langChainTool);
        this.toolSchemas.set(langChainTool.name, langChainTool.schema);
        this.toolDescriptions.set(langChainTool.name, langChainTool.description);

        if (options.category) {
            this.toolCategories.set(langChainTool.name, options.category);
        }

        console.log(`[ToolManager] Registered LangChain tool: ${langChainTool.name}`);
        return langChainTool;
    }

    /**
     * Check if a tool exists
     * 
     * @param {string} toolName - Name of the tool to check
     * @returns {boolean} True if tool exists
     */
    hasTool(toolName) {
        return this.tools.has(toolName);
    }

    /**
     * Get a tool by name
     * 
     * @param {string} toolName - Name of the tool to retrieve
     * @returns {Object|null} LangChain tool instance or null
     */
    getTool(toolName) {
        return this.tools.get(toolName) || null;
    }

    /**
     * Get all registered tools
     * 
     * @returns {Array} Array of LangChain tool instances
     */
    getAllTools() {
        return Array.from(this.tools.values());
    }

    /**
     * Get tool names
     * 
     * @returns {Array} Array of tool names
     */
    getToolNames() {
        return Array.from(this.tools.keys());
    }

    /**
     * Get tools by category
     * 
     * @param {string} category - Tool category to filter by
     * @returns {Array} Array of tools in the specified category
     */
    getToolsByCategory(category) {
        return Array.from(this.tools.entries())
            .filter(([name, tool]) => this.toolCategories.get(name) === category)
            .map(([name, tool]) => tool);
    }

    /**
     * Get tools from a specific collection
     * 
     * @param {string} collectionName - Name of the collection
     * @returns {Array} Array of tools from the collection
     */
    getToolsFromCollection(collectionName) {
        return this.toolCollections.get(collectionName) || [];
    }

    /**
     * Get all collection names
     * 
     * @returns {Array} Array of collection names
     */
    getCollectionNames() {
        return Array.from(this.toolCollections.keys());
    }

    /**
     * Execute a tool by name
     * 
     * @param {string} toolName - Name of the tool to execute
     * @param {Object} args - Arguments to pass to the tool
     * @returns {Promise<Object>} Tool execution result
     */
    async executeTool(toolName, args) {
        const tool = this.getTool(toolName);
        if (!tool) {
            throw new Error(`Tool "${toolName}" not found`);
        }

        try {
            return await tool.invoke(args);
        } catch (error) {
            console.error(`[ToolManager] Error executing tool "${toolName}":`, error);
            throw error;
        }
    }

    /**
     * Get available tool names and descriptions
     * 
     * @returns {Object} Object mapping tool names to descriptions
     */
    getToolDescriptions() {
        const descriptions = {};
        for (const [name, description] of this.toolDescriptions.entries()) {
            descriptions[name] = description;
        }
        return descriptions;
    }

    /**
     * Generate tool calling prompt instructions
     * 
     * @param {Array<string>} enabledTools - List of tool names to include in prompt
     * @returns {string} Formatted tool instructions
     */
    generateToolInstructions(enabledTools = null) {
        const toolsToInclude = enabledTools ?
            enabledTools.filter(name => this.tools.has(name)) :
            Array.from(this.tools.keys());

        if (toolsToInclude.length === 0) {
            return '';
        }

        const toolDescriptions = toolsToInclude.map(name => {
            const description = this.toolDescriptions.get(name);
            const schema = this.toolSchemas.get(name);
            const properties = schema._def?.schema?._def?.shape || {};

            const params = Object.keys(properties).map(key => {
                const prop = properties[key];
                const type = prop._def?.typeName || 'unknown';
                const desc = prop._def?.description || '';
                return `  - ${key} (${type}): ${desc}`;
            }).join('\n');

            return `${name}: ${description}\nParameters:\n${params}`;
        }).join('\n\n');

        return `AVAILABLE TOOLS:
${toolDescriptions}

TOOL CALLING RULES:
1. Only call tools when explicitly requested or when they would enhance the response
2. Use exact tool names and parameter formats as specified
3. Provide reasoning for tool selection in your response
4. Handle tool errors gracefully with fallback responses`;
    }

    /**
     * Clear all registered tools
     */
    clearAllTools() {
        this.tools.clear();
        this.toolSchemas.clear();
        this.toolDescriptions.clear();
        this.toolCategories.clear();
        this.toolCollections.clear();
        console.log('[ToolManager] All tools cleared');
    }

    /**
     * Clear tools from a specific collection
     * 
     * @param {string} collectionName - Name of the collection to clear
     * @returns {boolean} True if collection existed and was cleared
     */
    clearCollection(collectionName) {
        const tools = this.toolCollections.get(collectionName);
        if (!tools) {
            return false;
        }

        // Remove each tool in the collection
        for (const tool of tools) {
            this.tools.delete(tool.name);
            this.toolSchemas.delete(tool.name);
            this.toolDescriptions.delete(tool.name);
            this.toolCategories.delete(tool.name);
        }

        this.toolCollections.delete(collectionName);
        console.log(`[ToolManager] Cleared collection: ${collectionName} (${tools.length} tools)`);
        return true;
    }

    /**
     * Remove a specific tool
     * 
     * @param {string} toolName - Name of the tool to remove
     * @returns {boolean} True if tool was removed, false if not found
     */
    removeTool(toolName) {
        const existed = this.tools.has(toolName);
        this.tools.delete(toolName);
        this.toolSchemas.delete(toolName);
        this.toolDescriptions.delete(toolName);
        this.toolCategories.delete(toolName);

        // Remove from collections that contain this tool
        for (const [collectionName, tools] of this.toolCollections.entries()) {
            const index = tools.findIndex(tool => tool.name === toolName);
            if (index !== -1) {
                tools.splice(index, 1);
            }
        }

        if (existed) {
            console.log(`[ToolManager] Removed tool: ${toolName}`);
        }

        return existed;
    }

    /**
     * Get tool statistics
     * 
     * @returns {Object} Statistics about registered tools
     */
    getStatistics() {
        const categories = {};
        for (const category of this.toolCategories.values()) {
            categories[category] = (categories[category] || 0) + 1;
        }

        return {
            totalTools: this.tools.size,
            totalCollections: this.toolCollections.size,
            categoriesCount: categories,
            collections: Array.from(this.toolCollections.entries()).map(([name, tools]) => ({
                name,
                toolCount: tools.length
            }))
        };
    }
}

// Export singleton instance
export const toolManager = new ToolManager();

/**
 * Tool Registration Helpers
 */

// Legacy compatibility: re-export specific tool collections
export {
    selectAnimationTool,
    listAnimationsTool,
    recommendAnimationsTool,
    animationToolCollection,
    ANIMATION_TOOL_NAMES,
    createAnimationToolNode,
    createUnifiedToolNode
} from './avatar/animation.js';

// Export search tools
export {
    webSearchTool,
    webSearchToolCollection,
    WEB_SEARCH_TOOL_NAMES
} from './search/WebSearchTool.js';

// Conversation tools removed - functionality consolidated in ContextualAnalysisService

// Export base tool functionality
export { BaseToolNode, createEnhancedToolNode } from './base/BaseToolNode.js';
export {
    agentTTSTool,
    voiceProfileTool,
    agentSpeakingToolCollection,
    AGENT_SPEAKING_TOOL_NAMES,
    AgentSpeakingService,
    createSpeakingToolNode
} from './avatar/speaking.js';

/**
 * Universal tool collection registration function
 * 
 * @param {string} collectionName - Name of the tool collection
 * @param {Array|Object} toolCollection - Tool collection to register
 * @param {Object} options - Registration options
 * @returns {Array} Array of registered tools
 */
export function registerToolCollection(collectionName, toolCollection, options = {}) {
    return toolManager.registerToolCollection(collectionName, toolCollection, options);
}

/**
 * Register Speaking tools with the tool manager
 * @param {Object} speakingService - The Speaking service instance
 * @param {Object} options - Configuration options for Speaking tools
 * @returns {Array} Array of registered Speaking tools
 */
export function registerSpeaking(speakingService = null, audioPlayer = null, options = {}) {
    const results = [];

    console.log('[ToolManager] Registering agent Speaking tools (self-sufficient, create own service instances)');

    // Register unified agent Speaking tool collection (tools create their own service instances)
    try {
        const agentSpeakingTools = toolManager.registerToolCollection('AgentSpeaking', agentSpeakingToolCollection, {
            category: 'agent_speaking',
            speakingService, // Optional - tools can create their own service
            audioPlayer,
            ...options
        });
        results.push(...agentSpeakingTools);
        console.log(`[ToolManager] Registered ${agentSpeakingTools.length} agent Speaking tools (self-sufficient)`);
    } catch (error) {
        console.error('[ToolManager] Failed to register Agent Speaking tools:', error);
    }

    return results;
}

/**
 * Register animation tools with the tool manager (self-sufficient)
 * @param {Object} animationRegistry - Optional animation registry (tools load their own if not provided)
 * @param {Object} options - Configuration options for animation tools
 * @returns {Array} Array of registered animation tools
 */
export function registerAnimationTools(animationRegistry = null, options = {}) {
    return toolManager.registerToolCollection('Animation', animationToolCollection, {
        category: 'animation',
        animationRegistry, // Optional - tools load their own registry if needed
        ...options
    });
}


/**
 * Register web search tools with the tool manager
 * @param {Object} options - Configuration options for web search tools
 * @returns {Array} Array of registered web search tools
 */
export function registerWebSearchTools(options = {}) {
    const results = [];

    console.log('[ToolManager] Registering web search tools');

    try {
        // Register web search tool collection
        const webSearchTools = toolManager.registerToolCollection('WebSearch', webSearchToolCollection, {
            category: 'web_search',
            ...options
        });
        results.push(...webSearchTools);
        console.log(`[ToolManager] Registered ${webSearchTools.length} web search tools`);
    } catch (error) {
        console.error('[ToolManager] Failed to register web search tools:', error);
    }

    return results;
}



/**
 * Register voice cloning tools with the global tool manager
 * 
 * @param {Object} options - Registration options
 * @returns {Array} Array of registered voice cloning tools
 */
// Voice cloning tools are now integrated into TTS tools

/**
 * Auto-register tool collections based on configuration parameters
 * 
 * @param {Object} services - Service instances needed for tools
 * @param {Object} options - Registration options
 * @param {Object} config - Selective registration configuration
 * @param {boolean} config.speaking - Enable speaking/TTS tools
 * @param {boolean} config.animation - Enable animation tools
 * @param {boolean} config.conversation - Enable conversation analysis tools
 * @param {boolean} config.all - Register all available tools (overrides individual flags)
 * @param {Array<string>} config.collections - Specific collection names to register
 * @param {Array<string>} config.exclude - Collection names to exclude
 * @returns {Object} Registration results with tools array
 */
export function autoRegisterTools(services = {}, options = {}, config = {}) {
    // Dynamic tool registration based on available collections and configuration
    const {
        enabledTools = {},
        collections = ['Speaking', 'Animation', 'Conversation'],
        all = false,
        exclude = [],
        filters = {}
    } = config;

    console.log('[ToolManager] Auto-registering tool collections with dynamic config:', config);

    const results = {
        registered: [],
        failed: [],
        tools: [], // Add tools array for agent consumption
        statistics: null,
        availableCollections: [], // Track what collections were found
        skippedCollections: []    // Track what collections were skipped
    };

    // Map of available tool collections with existence checks
    const availableCollections = new Map([
        ['Speaking', {
            collection: agentSpeakingToolCollection,
            registerFn: () => registerSpeaking(null, null, options.speaking),
            enabled: enabledTools.speaking !== false
        }],
        ['Animation', {
            collection: animationToolCollection,
            registerFn: () => registerAnimationTools(null, options.animation),
            enabled: enabledTools.animation !== false
        }],
        ['WebSearch', {
            collection: webSearchToolCollection,
            registerFn: () => registerWebSearchTools(options.webSearch || {}),
            enabled: enabledTools.webSearch !== false
        }],
        // Conversation tools removed - functionality consolidated in ContextualAnalysisService
        // ['Conversation', {
        //     collection: contextualConversationToolCollection,
        //     registerFn: () => {
        //         toolManager.registerToolCollection(
        //             contextualConversationToolCollection.name,
        //             contextualConversationToolCollection.tools,
        //             {
        //                 category: 'conversation',
        //                 description: contextualConversationToolCollection.description
        //             }
        //         );
        //         return contextualConversationToolCollection.tools;
        //     },
        //     enabled: enabledTools.conversation !== false
        // }]
    ]);

    // Helper to check if collection should be registered
    const shouldRegister = (collectionName, collectionInfo) => {
        if (exclude.includes(collectionName)) {
            results.skippedCollections.push({ name: collectionName, reason: 'excluded' });
            return false;
        }
        if (!collectionInfo.enabled && !all) {
            results.skippedCollections.push({ name: collectionName, reason: 'disabled in config' });
            return false;
        }
        if (all) return true;
        if (collections && Array.isArray(collections)) {
            const included = collections.includes(collectionName);
            if (!included) {
                results.skippedCollections.push({ name: collectionName, reason: 'not in collections list' });
            }
            return included;
        }
        return true; // Default behavior
    };

    // Dynamically register available tool collections
    for (const [collectionName, collectionInfo] of availableCollections) {
        if (!collectionInfo.collection) {
            console.warn(`[ToolManager] Collection ${collectionName} not available (missing import)`);
            results.failed.push({ collection: collectionName, error: 'Collection not available' });
            continue;
        }

        results.availableCollections.push(collectionName);

        if (shouldRegister(collectionName, collectionInfo)) {
            try {
                console.log(`[ToolManager] Registering ${collectionName} tools...`);
                const registeredTools = collectionInfo.registerFn();

                if (registeredTools && registeredTools.length > 0) {
                    results.registered.push({ collection: collectionName, count: registeredTools.length });
                    results.tools.push(...registeredTools);
                    console.log(`[ToolManager] Successfully registered ${registeredTools.length} ${collectionName} tools:`,
                        registeredTools.map(t => t.name || 'unnamed'));
                } else {
                    console.warn(`[ToolManager] No tools returned from ${collectionName} registration`);
                    results.failed.push({ collection: collectionName, error: 'No tools returned' });
                }
            } catch (error) {
                console.error(`[ToolManager] Failed to register ${collectionName} tools:`, error);
                results.failed.push({ collection: collectionName, error: error.message });
            }
        }
    }

    // Check for any additional collections that might be dynamically available
    // This allows for future extensibility without hardcoding collection names
    const existingCollections = toolManager.getCollectionNames();
    for (const existingCollection of existingCollections) {
        if (!availableCollections.has(existingCollection) && !results.tools.some(tool =>
            toolManager.toolCategories.get(tool.name) === existingCollection.toLowerCase())) {
            console.log(`[ToolManager] Found existing collection not in config: ${existingCollection}`);
            if (all && !exclude.includes(existingCollection)) {
                const existingTools = toolManager.getToolsFromCollection(existingCollection);
                if (existingTools.length > 0) {
                    results.tools.push(...existingTools);
                    results.registered.push({ collection: existingCollection, count: existingTools.length });
                    console.log(`[ToolManager] Added ${existingTools.length} tools from existing collection: ${existingCollection}`);
                }
            }
        }
    }

    // Register Voice Cloning Tools (Aliyun CosyVoice-v2)
    // Voice cloning is now integrated into TTS tools

    // Get all tools from the manager as fallback
    const allManagerTools = toolManager.getAllTools();
    if (results.tools.length === 0 && allManagerTools.length > 0) {
        console.log('[ToolManager] Using all tools from manager as fallback');
        results.tools = allManagerTools;
    }

    results.statistics = toolManager.getStatistics();

    console.log(`[ToolManager] Auto-registration complete:`, {
        registered: results.registered.length,
        failed: results.failed.length,
        totalTools: results.tools.length,
        availableCollections: results.availableCollections,
        skippedCollections: results.skippedCollections,
        toolNames: results.tools.map(t => t.name || 'unnamed')
    });

    return results;
}

/**
 * Tool Execution Helpers
 */

/**
 * Execute a tool call with service integration
 * @param {Object} toolCallData - Accumulated tool call data from LangChain
 * @param {Object} services - Available services for tool execution
 * @param {Object} options - Execution options
 * @returns {Promise<Object>} - Tool execution result
 */
export async function executeToolCall(toolCallData, services = {}, options = {}) {
    const startTime = Date.now();

    try {
        console.log(`[ToolManager] Executing tool: ${toolCallData.name} with args:`, toolCallData.args);

        // Check if tool exists
        if (!toolManager.hasTool(toolCallData.name)) {
            throw new Error(`Tool not found: ${toolCallData.name}`);
        }

        const tool = toolManager.getTool(toolCallData.name);

        // Parse arguments if they're a string
        let args = toolCallData.args;
        if (typeof args === 'string') {
            try {
                args = JSON.parse(args);
            } catch (parseError) {
                console.warn(`Failed to parse tool args as JSON: ${parseError.message}`);
                throw new Error(`Invalid tool arguments: ${parseError.message}`);
            }
        }

        // Minimal config for LangGraph tool execution (tools are self-sufficient)
        const toolConfig = {
            // Tools handle their own setup - minimal config needed
            ...services
        };

        // Invoke the LangChain tool with both args and config (LangGraph style)
        const result = await tool.invoke(args, toolConfig);

        const executionTime = Date.now() - startTime;
        console.log(`[ToolManager] Tool ${toolCallData.name} executed successfully in ${executionTime}ms`);

        return result;
    } catch (error) {
        const executionTime = Date.now() - startTime;
        console.error(`[ToolManager] Error executing tool ${toolCallData.name} after ${executionTime}ms:`, error);
        throw error;
    }
}

/**
 * Check if a tool is a Speaking-related tool
 * @param {string} toolName - Name of the tool
 * @returns {boolean} - True if it's a Speaking tool
 */
export function isSpeakingTool(toolName) {
    return Object.values(AGENT_SPEAKING_TOOL_NAMES).includes(toolName);
}

// Deprecated function removed - use isSpeakingTool instead

/**
 * Check if a tool is an animation-related tool
 * @param {string} toolName - Name of the tool
 * @returns {boolean} - True if it's an animation tool
 */
export function isAnimationTool(toolName) {
    return ANIMATION_TOOL_NAMES.includes(toolName);
}

/**
 * Check if a tool is a voice cloning-related tool
 * @param {string} toolName - Name of the tool
 * @returns {boolean} - True if it's a voice cloning tool (now part of TTS tools)
 */
export function isVoiceCloningTool(toolName) {
    // Voice cloning is now integrated into TTS tools
    return toolName === 'voice_profile_manager' || toolName === 'speak_response';
}

/**
 * Check if a tool is an agent Speaking tool (replaces old TTS processor check)
 * @param {string} toolName - Name of the tool
 * @returns {boolean} - True if it's an agent Speaking tool
 */
export function isAgentSpeakingTool(toolName) {
    return Object.values(AGENT_SPEAKING_TOOL_NAMES).includes(toolName);
}

/**
 * Check if a tool is a character search tool
 * @param {string} toolName - Name of the tool
 * @returns {boolean} - True if it's a character search tool
 */
export function isCharacterSearchTool(toolName) {
    // CharacterSearchTool module not available
    return false;
}

/**
 * Check if a tool is a web search tool
 * @param {string} toolName - Name of the tool
 * @returns {boolean} - True if it's a web search tool
 */
export function isWebSearchTool(toolName) {
    return Object.values(WEB_SEARCH_TOOL_NAMES).includes(toolName);
}

// Deprecated functions removed - use isAgentSpeakingTool instead

/**
 * Create configuration-based tool registration setup
 * 
 * @param {Object} baseConfig - Base configuration object
 * @returns {Object} Tool registration configuration with helper methods
 */
export function createToolRegistrationConfig(baseConfig = {}) {
    return {
        // Default configuration
        speaking: baseConfig.speaking !== false,
        animation: baseConfig.animation !== false,
        conversation: baseConfig.conversation !== false,
        all: baseConfig.all || false,
        collections: baseConfig.collections || null,
        exclude: baseConfig.exclude || [],

        // Helper methods
        withSpeaking(enabled = true) {
            this.speaking = enabled;
            return this;
        },

        withAnimation(enabled = true) {
            this.animation = enabled;
            return this;
        },

        withConversation(enabled = true) {
            this.conversation = enabled;
            return this;
        },

        withAll(enabled = true) {
            this.all = enabled;
            return this;
        },

        withCollections(collections) {
            this.collections = Array.isArray(collections) ? collections : [collections];
            return this;
        },

        withExclusions(exclude) {
            this.exclude = Array.isArray(exclude) ? exclude : [exclude];
            return this;
        },

        // Get final configuration for autoRegisterTools
        getConfig() {
            return {
                speaking: this.speaking,
                animation: this.animation,
                conversation: this.conversation,
                all: this.all,
                collections: this.collections,
                exclude: this.exclude
            };
        }
    };
}

// ToolManagementService removed - functionality consolidated in this file

/**
 * Get tool execution statistics
 * @param {Object} services - Available services
 * @returns {Object} - Tool usage statistics
 */
export function getToolStats(services = {}) {
    const allTools = toolManager.getAllTools();
    const categories = {};

    // Group tools by category
    for (const tool of allTools) {
        const category = toolManager.toolCategories.get(tool.name) || 'uncategorized';
        if (!categories[category]) {
            categories[category] = [];
        }
        categories[category].push(tool);
    }

    const collections = toolManager.getCollectionNames();

    return {
        totalTools: allTools.length,
        categories: Object.keys(categories).reduce((acc, cat) => {
            acc[cat] = categories[cat].length;
            return acc;
        }, {}),
        collections: collections.length,
        collectionNames: collections,
        availableServices: Object.keys(services).filter(key => services[key] !== null)
    };
}




