/**
 * Agent Initialization Service
 * Extracted from core.js to handle all initialization logic
 * Reduces monolithic core.js size and improves separation of concerns
 */

import { createLogger, LogLevel } from '../utils/logger.js';
// Centralized tool functionality is consolidated in tools/index.js
import { autoRegisterTools, toolManager } from './tools/index.js';
import { ModelFactory } from './models/ModelFactory.js';
import { StreamingManager } from './streaming/StreamingManager.js';
import { ProviderConfigManager } from './config/ProviderConfig.js';

export class AgentInitializationService {
    constructor(options = {}) {
        this.logger = createLogger('AgentInitializationService', LogLevel.DEBUG);
        this.options = options;

        // Service instances
        this.toolManagementService = null;
        this.modelFactory = null;
        this.streamingManager = null;
        this.providerConfigManager = null;

        // Initialization state
        this.initializationSteps = new Map();
        this.initialized = false;
    }

    /**
     * Initialize all agent services in the correct order
     * @param {Object} coreService - Reference to the main core service
     * @returns {Promise<Object>} - Initialized services
     */
    async initializeAllServices(coreService) {
        this.logger.info('Starting comprehensive agent service initialization...');

        try {
            // Step 1: Initialize provider configuration
            await this._initializeProviderConfig(coreService);

            // Step 2: Initialize model factory and models
            await this._initializeModels(coreService);

            // Step 3: Initialize tool management
            await this._initializeToolManagement(coreService);

            // Step 4: Initialize streaming manager
            await this._initializeStreamingManager(coreService);

            // Step 5: Initialize memory management
            await this._initializeMemoryManager(coreService);

            // Step 6: Initialize coordination services
            await this._initializeCoordinationServices(coreService);

            // Step 7: Initialize dual brain system (if enabled)
            if (this.options.enableDualBrain !== false) {
                await this._initializeDualBrainSystem(coreService);
            }

            // Step 8: Initialize realtime interface
            this._initializeRealtimeInterface(coreService);

            // Step 9: Create the main agent
            await this._createAgent(coreService);

            this.initialized = true;
            this.logger.info('✅ All agent services initialized successfully');

            return this._getInitializedServices();

        } catch (error) {
            this.logger.error('❌ Agent service initialization failed:', error);
            throw error;
        }
    }

    /**
     * Initialize provider configuration
     * @private
     */
    async _initializeProviderConfig(coreService) {
        this.logger.debug('🔧 Initializing provider configuration...');

        this.providerConfigManager = new ProviderConfigManager();

        // Auto-detect provider if not specified
        const detectedProvider = this.providerConfigManager.autoDetectProvider(this.options);
        const provider = this.options.provider || this.options.modelProvider || detectedProvider;

        // Create provider configuration
        const providerConfig = this.providerConfigManager.getProviderConfig(provider, this.options);

        // Store configuration in core service
        coreService.providerConfig = providerConfig;
        coreService.provider = provider;

        this.logger.info(`✅ Provider configuration initialized: ${provider}`, {
            hasAuth: !!providerConfig.apiKey,
            hasEndpoint: !!providerConfig.httpEndpoint,
            streamingEnabled: providerConfig.features.streaming,
            realtimeEnabled: providerConfig.features.realtime
        });

        this._markStepCompleted('providerConfig');
    }

    /**
     * Initialize models using the consolidated model factory
     * @private
     */
    async _initializeModels(coreService) {
        this.logger.debug('🤖 Initializing models...');

        // Create model factory with infrastructure services
        this.modelFactory = new ModelFactory({
            provider: coreService.provider,
            infrastructureManager: coreService.infrastructureManager,
            ...coreService.providerConfig
        });

        // Create primary model for the agent
        coreService.model = await this.modelFactory.createModelForTask('general', {
            provider: coreService.provider,
            ...this.options
        });

        // Store factory reference
        coreService.modelFactory = this.modelFactory;

        this.logger.info(`✅ Models initialized: ${coreService.model.constructor.name}`);
        this._markStepCompleted('models');
    }

    /**
     * Initialize tool management service
     * @private
     */
    async _initializeToolManagement(coreService) {
        this.logger.debug('🔧 Initializing tool management...');

        // Centralized tool registration via ToolManager (tools/index.js)
        const registration = autoRegisterTools(coreService.services || {}, this.options.toolOptions || {}, {
            collections: this.options.enabledToolCollections || ['Speaking', 'Animation', 'WebSearch'],
            all: false,
            exclude: []
        });

        // Expose tools to core
        coreService.tools = registration.tools || toolManager.getAllTools();
        coreService.toolManagementService = {
            // Minimal adapter to satisfy existing calls
            bindToolsToModel(model, tools, options = {}) {
                if (model && typeof model.bindTools === 'function') {
                    return model.bindTools(tools, options);
                }
                return model;
            },
            getAllTools() { return coreService.tools; }
        };

        this.logger.info(`✅ Tool management initialized with ${coreService.tools.length} tools`);
        this._markStepCompleted('toolManagement');
    }

    /**
     * Initialize streaming manager
     * @private
     */
    async _initializeStreamingManager(coreService) {
        this.logger.debug('🌊 Initializing streaming manager...');

        // Create streaming manager with performance optimization
        this.streamingManager = new StreamingManager({
            preferredMode: 'messages',
            enablePerformanceOptimization: true,
            targetLatency: this.options.targetLatency || 600,
            adaptiveThrottling: true,
            infrastructureManager: coreService.infrastructureManager
        });

        // Initialize performance coordination if infrastructure is available
        if (coreService.infrastructureManager) {
            await this.streamingManager.initializePerformanceCoordination(coreService.infrastructureManager);
        }

        coreService.streamingManager = this.streamingManager;

        this.logger.info('✅ Streaming manager initialized with performance optimization');
        this._markStepCompleted('streamingManager');
    }

    /**
     * Initialize memory management
     * @private
     */
    async _initializeMemoryManager(coreService) {
        this.logger.debug('🧠 Initializing memory management...');

        // Memory management is handled by the infrastructure manager
        // This step ensures memory services are available
        if (coreService.infrastructureManager) {
            const memoryServices = await coreService.infrastructureManager.createServicesForComponent('MemoryManager', {
                cacheManager: { maxCacheSize: 1000, defaultTTL: 300000 }
            });

            coreService.memoryServices = memoryServices;
            this.logger.info('✅ Memory management initialized');
        } else {
            this.logger.warn('⚠️ Memory management skipped - no infrastructure manager');
        }

        this._markStepCompleted('memoryManager');
    }

    /**
     * Initialize coordination services
     * @private
     */
    async _initializeCoordinationServices(coreService) {
        this.logger.debug('🤝 Initializing coordination services...');

        // Basic coordination services setup
        if (coreService.infrastructureManager) {
            const coordinationServices = await coreService.infrastructureManager.createServicesForComponent('CoordinationServices', {
                performanceCoordinator: {
                    targetLatency: this.options.targetLatency || 600,
                    enableAdaptiveOptimization: true
                }
            });

            coreService.coordinationServices = coordinationServices;
            this.logger.info('✅ Coordination services initialized');
        } else {
            this.logger.warn('⚠️ Coordination services skipped - no infrastructure manager');
        }

        this._markStepCompleted('coordinationServices');
    }

    /**
     * Initialize dual brain system
     * @private
     */
    async _initializeDualBrainSystem(coreService) {
        this.logger.debug('🧠🧠 Initializing dual brain system...');

        try {
            // Initialize dual brain models if needed
            await this._initializeDualBrainModels(coreService);

            // Initialize dual brain coordinator
            await this._initializeDualBrainCoordinator(coreService);

            this.logger.info('✅ Dual brain system initialized');
        } catch (error) {
            this.logger.error('❌ Dual brain initialization failed:', error);
            if (this.options.requireDualBrain) {
                throw error;
            } else {
                this.logger.warn('⚠️ Continuing without dual brain system');
            }
        }

        this._markStepCompleted('dualBrainSystem');
    }

    /**
     * Initialize dual brain models
     * @private
     */
    async _initializeDualBrainModels(coreService) {
        this.logger.info('🧠 Initializing dual brain models...');

        // Initialize models structure if not exists
        if (!coreService.models) {
            coreService.models = {
                system1: null,              // Fast Brain (WebSocket/Realtime) 
                system2: null               // Thinking Brain (HTTP)
            };
        }

        // Create HTTP model for System 2 (thinking brain) - ALWAYS HTTP for tools
        coreService.models.system2 = await this.modelFactory.createModelForTask('autonomous_tools', {
            provider: coreService.provider,
            modelType: 'http',
            requiresTools: true,
            complexity: 'high',
            priority: 'accuracy'
        });

        // Create WebSocket model for System 1 (realtime brain) if supported
        if (coreService.providerConfig.features.realtime) {
            coreService.models.system1 = await this.modelFactory.createModelForTask('realtime_processing', {
                provider: coreService.provider,
                modelType: 'websocket',
                realtime: true,
                priority: 'speed'
            });
        } else {
            // Fallback: use primary model for System 1
            coreService.models.system1 = coreService.model;
        }

        // Store legacy references for backward compatibility
        coreService.httpModel = coreService.models.system2;
        if (coreService.models.system1 !== coreService.model) {
            coreService.websocketModel = coreService.models.system1;
        }

        this.logger.info('🧠 Dual brain models initialized', {
            system1Available: !!coreService.models.system1,
            system2Available: !!coreService.models.system2,
            system1Type: coreService.models.system1?.constructor.name,
            system2Type: coreService.models.system2?.constructor.name
        });
    }

    /**
     * Initialize dual brain coordinator
     * @private
     */
    async _initializeDualBrainCoordinator(coreService) {
        this.logger.info('🧠 Creating DualBrainCoordinator internally...');

        try {
            // Dynamic import to avoid circular dependencies (use factory function like core.js)
            const { createDualBrainCoordinator } = await import('./arch/dualbrain/DualBrainCoordinator.js');

            // Create coordinator with agent service and options (matching core.js pattern)
            const coordinator = createDualBrainCoordinator(
                coreService,
                {
                    ...coreService.options?.agentConfig,
                    infrastructureManager: coreService.infrastructureManager,
                    memoryManager: coreService.memoryManager,
                    userId: coreService.options?.userId || 'default_user'
                }
            );

            // Initialize the coordinator
            const initialized = await coordinator.initialize();
            if (!initialized) {
                throw new Error('Failed to initialize DualBrainCoordinator');
            }

            // Set the coordinator using the existing setDualBrainCoordinator method
            if (typeof coreService.setDualBrainCoordinator === 'function') {
                coreService.setDualBrainCoordinator(coordinator);
            } else {
                coreService.dualBrainCoordinator = coordinator;
            }

            this.logger.info('✅ DualBrainCoordinator created and initialized internally', {
                hasCoordinator: !!coordinator,
                coordinatorInitialized: coordinator?.isInitialized
            });

        } catch (error) {
            this.logger.error('❌ Failed to initialize DualBrainCoordinator internally:', error);
            // Don't throw - dual brain is optional functionality like in core.js
        }
    }

    /**
     * Initialize realtime interface
     * @private
     */
    _initializeRealtimeInterface(coreService) {
        this.logger.debug('🎙️ Initializing realtime interface...');

        // Setup realtime interface if WebSocket model is available
        if (coreService.websocketModel) {
            coreService.realtimeInterface = {
                isActive: false,
                model: coreService.websocketModel,
                capabilities: ['voice_input', 'voice_output', 'realtime_processing']
            };

            this.logger.info('✅ Realtime interface initialized');
        } else {
            this.logger.debug('ℹ️ Realtime interface skipped - no WebSocket model');
        }

        this._markStepCompleted('realtimeInterface');
    }

    /**
     * Create the main agent using LangGraph
     * @private
     */
    async _createAgent(coreService) {
        this.logger.debug('🤖 Creating LangGraph agent...');

        try {
            // Dynamic import to avoid circular dependencies
            const { createReactAgent } = await import('@langchain/langgraph/prebuilt');
            // Import LangGraph tool validation utilities (dynamic to avoid cycles)
            const { validateToolsForLangGraph, getToolChoiceStrategy } = await import('./config/LangGraphConfig.js');

            // Validate and sanitize tools to prevent undefined entries breaking ReactAgent
            const allTools = Array.isArray(coreService.tools) ? coreService.tools : [];
            const validatedTools = validateToolsForLangGraph(allTools, {
                skipInvalidTools: true,
                requireDescription: true,
                requireSchema: true,
                requireInvokeMethod: true
            });

            // Bind tools to model when available; otherwise, degrade gracefully
            let llmForAgent = coreService.model;
            if (validatedTools.length > 0) {
                const toolChoice = getToolChoiceStrategy(validatedTools, { toolChoiceStrategy: this.options?.toolChoiceStrategy });

                if (coreService.toolManagementService && typeof coreService.toolManagementService.bindToolsToModel === 'function') {
                    llmForAgent = coreService.toolManagementService.bindToolsToModel(coreService.model, validatedTools, { tool_choice: toolChoice });
                    this.logger.debug('🔧 Used ToolManagementService to bind validated tools to model');
                } else if (coreService.model && typeof coreService.model.bindTools === 'function') {
                    llmForAgent = coreService.model.bindTools(validatedTools, { tool_choice: toolChoice });
                    this.logger.debug('🔧 Used model.bindTools() to bind validated tools');
                } else {
                    this.logger.warn('⚠️ Model does not support tool binding; proceeding without bound tools');
                }

                // Ensure System 2 (HTTP) uses the bound model for tool-calling paths if dual brain is present
                if (coreService.models && coreService.models.system2) {
                    coreService.models.system2 = llmForAgent;
                    this.logger.debug('🧠 System 2 model updated to tool-bound instance');
                }
            } else {
                this.logger.warn('⚠️ No valid tools after validation — creating ReactAgent without tools');
            }

            // Create the agent with sanitized tools and (if available) a tool-bound model
            coreService.agent = createReactAgent({
                llm: llmForAgent,
                tools: validatedTools,
                messageModifier: this.options.systemPrompt || "You are a helpful AI assistant.",
                checkpointSaver: coreService.memoryServices?.checkpointSaver
            });

            this.logger.info('✅ LangGraph ReactAgent created successfully');
        } catch (error) {
            this.logger.error('❌ Failed to create LangGraph agent:', error);
            throw error;
        }

        this._markStepCompleted('agent');
    }

    /**
     * Mark initialization step as completed
     * @private
     */
    _markStepCompleted(stepName) {
        this.initializationSteps.set(stepName, {
            completed: true,
            timestamp: Date.now()
        });
    }

    /**
     * Get all initialized services
     * @private
     */
    _getInitializedServices() {
        return {
            toolManagementService: this.toolManagementService,
            modelFactory: this.modelFactory,
            streamingManager: this.streamingManager,
            providerConfigManager: this.providerConfigManager,
            initializationSteps: Object.fromEntries(this.initializationSteps)
        };
    }

    /**
     * Get initialization status
     * @returns {Object} - Initialization status
     */
    getInitializationStatus() {
        return {
            initialized: this.initialized,
            completedSteps: Array.from(this.initializationSteps.keys()),
            totalSteps: 9, // Update this if you add more steps
            completionPercentage: (this.initializationSteps.size / 9) * 100
        };
    }

    /**
     * Shutdown initialization service and cleanup
     */
    async shutdown() {
        try {
            if (this.toolManagementService) {
                await this.toolManagementService.shutdown();
            }

            if (this.modelFactory) {
                await this.modelFactory.shutdown();
            }

            if (this.streamingManager) {
                await this.streamingManager.shutdown();
            }

            this.logger.info('✅ Agent initialization service shutdown complete');
        } catch (error) {
            this.logger.error('❌ Error during initialization service shutdown:', error);
        }
    }
}

export default AgentInitializationService;

/**
 * initializeAgent
 * Thin convenience wrapper used by core to initialize all agent services
 * via AgentInitializationService while keeping the core entry small.
 *
 * @param {Object} coreService - Core agent service instance (mutated with initialized services)
 * @param {Object} options - Initialization options
 * @returns {Promise<{ initService: AgentInitializationService, services: Object }>} Initialized service refs
 */
export async function initializeAgent(coreService, options = {}) {
    const initService = new AgentInitializationService(options);
    const services = await initService.initializeAllServices(coreService);
    // Expose for diagnostics/shutdown
    coreService.initializationService = initService;
    return { initService, services };
}