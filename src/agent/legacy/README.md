# Legacy StateGraph Node Methods

This directory contains legacy files that were originally designed for custom StateGraph workflows but are not currently used in the main codebase.

## Directory Structure

```
src/agent/legacy/
├── README.md                 # This file
└── state/                    # Legacy state management files
    ├── README.md            # State-specific documentation
    ├── definitions.js       # Consolidated StateGraph definitions & node methods
    ├── workflow.js          # StateGraph workflow factories
    ├── reducers.js          # State transition reducers
    └── index.js             # Central export point
```

## Current Architecture

The main agent service (`src/agent/core.js`) uses **ReactAgent** from LangGraph's prebuilt components, which is the recommended approach for tool-calling agents. ReactAgent internally handles the state graph workflow automatically.

## Legacy Components

The file `state/definitions.js` contains:

### Part 1: State Definitions
- **State Constants**: `AVATAR_STATES`, `AGENT_STATES` 
- **State Annotations**: `AvatarStateAnnotation`, `AgentStateAnnotation`
- **Transition Rules**: `StateTransitionRules` for valid state changes
- **Validation Utilities**: `StateValidation` helper functions

### Part 2: Node Factory Methods  
- **Node Factory**: `createLegacyStateGraphNodes()` function
- **Bound Node Implementations**: Factory functions for creating node implementations bound to agent service instances

### When to Use

These legacy components should only be used if you need to:

1. **Create a custom StateGraph workflow** instead of using ReactAgent
2. **Build complex multi-agent systems** that require fine-grained control over state transitions
3. **Implement specialized agent behaviors** that cannot be achieved with ReactAgent

### Migration Path

If you need StateGraph functionality, consider:

1. **First**: Check if `state/workflow.js` has the workflow you need
2. **Second**: Use the consolidated `state/definitions.js` for state definitions and node methods
3. **Third**: Create new custom node implementations

## Example Usage

```javascript
import { StateGraph, START, END } from "@langchain/langgraph/web";
import { 
    createLegacyStateGraphNodes, 
    AvatarStateAnnotation,
    AVATAR_STATES
} from './legacy/state/definitions.js';

// Create custom StateGraph workflow
const workflow = new StateGraph(AvatarStateAnnotation);
const nodes = createLegacyStateGraphNodes(agentService);

// Add nodes to workflow
workflow.addNode("processInput", nodes.processInputNode);
workflow.addNode("loadMemory", nodes.loadMemoryNode);
workflow.addNode("generateResponse", nodes.generateResponseNode);
workflow.addNode("updateAgent", nodes.updateAgentNode);
workflow.addNode("handleError", nodes.handleErrorNode);

// Define edges
workflow.addEdge(START, "processInput");
workflow.addEdge("processInput", "loadMemory");
workflow.addEdge("loadMemory", "generateResponse");
// ... more edges

const compiledWorkflow = workflow.compile();
```

## Related Files

- `src/agent/core.js` - Main agent service using ReactAgent
- `src/agent/state/workflow.js` - Active StateGraph workflow implementations
- `src/agent/state/node.js` - Node factory functions
- `src/agent/state/AgentStateDefinitions.js` - State annotations

## References

- [LangGraph StateGraph Documentation](https://langchain-ai.github.io/langgraph/concepts/low_level/)
- [LangGraph ReactAgent Documentation](https://langchain-ai.github.io/langgraph/concepts/multi_agent/)
- [LangGraph Multi-Agent Patterns](https://langchain-ai.github.io/langgraph/concepts/multi_agent/)
