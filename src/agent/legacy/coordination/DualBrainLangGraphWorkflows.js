/**
 * Dual-Brain LangGraph Workflows
 * 
 * LangGraph-based workflow patterns for coordinating communication
 * between System 2 (Thinking Brain) and System 1 (Fast Brain).
 * 
 * Implements StateGraph patterns for robust inter-system coordination.
 */

import { StateGraph, END, START } from "@langchain/langgraph";
import { createLogger, LogLevel } from '@/utils/logger';
import { 
    CommunicationMessageBuilder, 
    System2ContextPackager, 
    System1MessageParser 
} from './DualBrainCommunicationInterface.js';

const logger = createLogger('DualBrainLangGraphWorkflows', LogLevel.DEBUG);

/**
 * State schema for dual-brain communication workflows
 */
export const DualBrainState = {
    // Input data
    userInput: { value: null, reducer: (existing, update) => update || existing },
    inputType: { value: 'text', reducer: (existing, update) => update || existing },
    sessionContext: { value: {}, reducer: (existing, update) => ({ ...existing, ...update }) },
    
    // System 1 processing
    system1Context: { value: null, reducer: (existing, update) => update || existing },
    system1Response: { value: null, reducer: (existing, update) => update || existing },
    multimodalData: { value: null, reducer: (existing, update) => update || existing },
    
    // System 2 processing
    contextualAnalysis: { value: null, reducer: (existing, update) => update || existing },
    speakingDecision: { value: null, reducer: (existing, update) => update || existing },
    enhancedContext: { value: null, reducer: (existing, update) => update || existing },
    
    // Communication
    communicationMessage: { value: null, reducer: (existing, update) => update || existing },
    system1Instructions: { value: null, reducer: (existing, update) => update || existing },
    
    // Final output
    finalResponse: { value: null, reducer: (existing, update) => update || existing },
    metadata: { value: {}, reducer: (existing, update) => ({ ...existing, ...update }) }
};

/**
 * System 2 → System 1 Communication Workflow
 * Handles the flow of contextual insights from System 2 to System 1
 */
export class System2ToSystem1Workflow {
    constructor(system2, system1, contextPackager) {
        this.system2 = system2;
        this.system1 = system1;
        this.contextPackager = contextPackager;
        this.logger = logger;
    }

    /**
     * Create the LangGraph workflow for System 2 → System 1 communication
     */
    createWorkflow() {
        const workflow = new StateGraph(DualBrainState);

        // Define workflow nodes
        workflow.addNode("analyze_context", this.analyzeContext.bind(this));
        workflow.addNode("make_speaking_decision", this.makeSpeakingDecision.bind(this));
        workflow.addNode("package_communication", this.packageCommunication.bind(this));
        workflow.addNode("send_to_system1", this.sendToSystem1.bind(this));
        workflow.addNode("validate_delivery", this.validateDelivery.bind(this));

        // Define workflow edges
        workflow.addEdge(START, "analyze_context");
        workflow.addEdge("analyze_context", "make_speaking_decision");
        workflow.addEdge("make_speaking_decision", "package_communication");
        workflow.addEdge("package_communication", "send_to_system1");
        workflow.addEdge("send_to_system1", "validate_delivery");
        workflow.addEdge("validate_delivery", END);

        return workflow.compile();
    }

    /**
     * Analyze contextual information using System 2
     */
    async analyzeContext(state) {
        const startTime = Date.now();
        
        try {
            this.logger.debug('System 2: Analyzing contextual information');

            // Use System 2's contextual analysis capabilities
            const analysis = await this.system2.analyzeContext({
                userInput: state.userInput,
                sessionContext: state.sessionContext,
                system1Context: state.system1Context,
                multimodalData: state.multimodalData
            });

            const processingTime = Date.now() - startTime;

            return {
                ...state,
                contextualAnalysis: analysis,
                metadata: {
                    ...state.metadata,
                    analysisTime: processingTime,
                    analysisTimestamp: Date.now()
                }
            };

        } catch (error) {
            this.logger.error('Error in System 2 contextual analysis:', error);
            
            // Fallback analysis
            return {
                ...state,
                contextualAnalysis: {
                    confidence: 0.3,
                    userEngagement: 0.5,
                    conversationFlow: 'active',
                    environmentalContext: { canSpeak: true },
                    error: error.message
                },
                metadata: {
                    ...state.metadata,
                    analysisError: true,
                    analysisTime: Date.now() - startTime
                }
            };
        }
    }

    /**
     * Make speaking decision based on contextual analysis
     */
    async makeSpeakingDecision(state) {
        try {
            this.logger.debug('System 2: Making speaking decision');

            const { contextualAnalysis } = state;
            
            // Analyze whether System 1 should speak
            const speakingDecision = {
                shouldSpeak: this._shouldSystemSpeak(contextualAnalysis),
                confidence: contextualAnalysis.confidence || 0.5,
                urgency: this._determineUrgency(contextualAnalysis),
                timing: this._calculateOptimalTiming(contextualAnalysis),
                voiceParameters: this._determineVoiceParameters(contextualAnalysis),
                contentGuidance: this._generateContentGuidance(contextualAnalysis)
            };

            this.logger.info('Speaking decision made:', {
                shouldSpeak: speakingDecision.shouldSpeak,
                confidence: speakingDecision.confidence,
                urgency: speakingDecision.urgency
            });

            return {
                ...state,
                speakingDecision
            };

        } catch (error) {
            this.logger.error('Error making speaking decision:', error);
            
            // Fallback decision
            return {
                ...state,
                speakingDecision: {
                    shouldSpeak: true,
                    confidence: 0.5,
                    urgency: 'medium',
                    timing: 0,
                    voiceParameters: { tone: 'neutral' },
                    contentGuidance: { responseType: 'answer' }
                }
            };
        }
    }

    /**
     * Package communication message for System 1
     */
    async packageCommunication(state) {
        try {
            this.logger.debug('Packaging communication for System 1');

            const { contextualAnalysis, speakingDecision } = state;

            // Use the context packager to create structured message
            const communicationMessage = this.contextPackager.packageSpeakingDecision(
                speakingDecision,
                {
                    user: contextualAnalysis.userContext,
                    environment: contextualAnalysis.environmentalContext,
                    conversation: contextualAnalysis.conversationContext
                }
            );

            // Also create system instructions
            const instructions = this.contextPackager.packageInstructions({
                action: speakingDecision.shouldSpeak ? 'respond' : 'wait',
                priority: speakingDecision.urgency,
                params: {
                    responseType: speakingDecision.contentGuidance?.responseType,
                    voiceParams: speakingDecision.voiceParameters
                },
                tools: speakingDecision.contentGuidance?.shouldUseTools ? ['speak_response'] : []
            });

            return {
                ...state,
                communicationMessage,
                system1Instructions: instructions
            };

        } catch (error) {
            this.logger.error('Error packaging communication:', error);
            
            // Fallback message
            return {
                ...state,
                communicationMessage: '{"id":"fallback","type":"decision","speak":{"should":true}}',
                system1Instructions: '{"id":"fallback","type":"instruction","cmd":{"action":"respond"}}'
            };
        }
    }

    /**
     * Send packaged message to System 1
     */
    async sendToSystem1(state) {
        try {
            this.logger.debug('Sending communication to System 1');

            const { communicationMessage, system1Instructions } = state;

            // Send to System 1 via its communication interface
            const deliveryResult = await this.system1.receiveContextualMessage({
                message: communicationMessage,
                instructions: system1Instructions,
                timestamp: Date.now()
            });

            return {
                ...state,
                metadata: {
                    ...state.metadata,
                    deliveryResult,
                    deliveryTimestamp: Date.now()
                }
            };

        } catch (error) {
            this.logger.error('Error sending to System 1:', error);
            
            return {
                ...state,
                metadata: {
                    ...state.metadata,
                    deliveryError: error.message,
                    deliveryTimestamp: Date.now()
                }
            };
        }
    }

    /**
     * Validate message delivery and System 1 understanding
     */
    async validateDelivery(state) {
        try {
            const { metadata } = state;
            
            if (metadata.deliveryError) {
                this.logger.warn('Communication delivery failed:', metadata.deliveryError);
                
                return {
                    ...state,
                    finalResponse: {
                        success: false,
                        error: metadata.deliveryError,
                        fallbackApplied: true
                    }
                };
            }

            this.logger.info('Communication successfully delivered to System 1');
            
            return {
                ...state,
                finalResponse: {
                    success: true,
                    deliveryTime: metadata.deliveryTimestamp - metadata.analysisTimestamp,
                    analysisTime: metadata.analysisTime
                }
            };

        } catch (error) {
            this.logger.error('Error validating delivery:', error);
            
            return {
                ...state,
                finalResponse: {
                    success: false,
                    error: error.message
                }
            };
        }
    }

    // Private helper methods for decision making

    _shouldSystemSpeak(analysis) {
        // Determine if System 1 should speak based on analysis
        if (analysis.urgency === 'high') return true;
        if (analysis.userEngagement < 0.3) return true;
        if (analysis.conversationFlow === 'ending') return false;
        if (analysis.environmentalContext?.interruptionRisk === 'high') return false;
        
        return analysis.confidence > 0.4;
    }

    _determineUrgency(analysis) {
        if (analysis.confidence > 0.8) return 'high';
        if (analysis.userEngagement < 0.2) return 'high';
        if (analysis.confidence < 0.3) return 'low';
        
        return 'medium';
    }

    _calculateOptimalTiming(analysis) {
        // Calculate optimal delay before speaking (in milliseconds)
        if (analysis.urgency === 'high') return 0;
        if (analysis.environmentalContext?.backgroundNoise === 'high') return 1000;
        
        return 500; // Default 500ms delay
    }

    _determineVoiceParameters(analysis) {
        const params = {
            tone: 'neutral',
            speed: 1.0,
            emotion: 'neutral',
            volume: 0.8
        };

        if (analysis.userContext?.mood === 'excited') {
            params.tone = 'enthusiastic';
            params.speed = 1.1;
            params.emotion = 'happy';
        } else if (analysis.userContext?.mood === 'sad') {
            params.tone = 'gentle';
            params.speed = 0.9;
            params.emotion = 'empathetic';
        }

        return params;
    }

    _generateContentGuidance(analysis) {
        return {
            responseType: this._determineResponseType(analysis),
            keyPoints: analysis.suggestedTopics || [],
            maxDuration: 30000, // 30 seconds max
            shouldUseTools: true
        };
    }

    _determineResponseType(analysis) {
        if (analysis.lastUserIntent === 'question') return 'answer';
        if (analysis.userEngagement < 0.3) return 'engagement';
        if (analysis.conversationFlow === 'starting') return 'greeting';
        
        return 'answer';
    }
}

/**
 * System 1 → System 2 Feedback Workflow
 * Handles feedback from System 1 to System 2 for learning and adaptation
 */
export class System1ToSystem2FeedbackWorkflow {
    constructor(system1, system2) {
        this.system1 = system1;
        this.system2 = system2;
        this.logger = logger;
    }

    /**
     * Create feedback workflow
     */
    createWorkflow() {
        const workflow = new StateGraph({
            ...DualBrainState,
            feedbackData: { value: null, reducer: (existing, update) => update || existing },
            learningUpdate: { value: null, reducer: (existing, update) => update || existing }
        });

        workflow.addNode("collect_feedback", this.collectFeedback.bind(this));
        workflow.addNode("analyze_performance", this.analyzePerformance.bind(this));
        workflow.addNode("update_system2", this.updateSystem2.bind(this));

        workflow.addEdge(START, "collect_feedback");
        workflow.addEdge("collect_feedback", "analyze_performance");
        workflow.addEdge("analyze_performance", "update_system2");
        workflow.addEdge("update_system2", END);

        return workflow.compile();
    }

    /**
     * Collect feedback from System 1
     */
    async collectFeedback(state) {
        try {
            // Collect performance metrics from System 1
            const feedback = await this.system1.getPerformanceMetrics();
            
            return {
                ...state,
                feedbackData: feedback
            };

        } catch (error) {
            this.logger.error('Error collecting feedback:', error);
            return { ...state, feedbackData: { error: error.message } };
        }
    }

    /**
     * Analyze System 1 performance
     */
    async analyzePerformance(state) {
        try {
            const { feedbackData } = state;
            
            // Analyze how well the communication worked
            const analysis = {
                communicationEffectiveness: this._evaluateEffectiveness(feedbackData),
                responseAccuracy: this._evaluateAccuracy(feedbackData),
                timingOptimization: this._evaluateTiming(feedbackData),
                suggestions: this._generateImprovements(feedbackData)
            };

            return {
                ...state,
                learningUpdate: analysis
            };

        } catch (error) {
            this.logger.error('Error analyzing performance:', error);
            return { ...state, learningUpdate: { error: error.message } };
        }
    }

    /**
     * Update System 2 with learning insights
     */
    async updateSystem2(state) {
        try {
            const { learningUpdate } = state;
            
            // Send learning updates to System 2
            await this.system2.updateFromFeedback(learningUpdate);
            
            return {
                ...state,
                finalResponse: {
                    success: true,
                    learningApplied: true,
                    improvements: learningUpdate.suggestions
                }
            };

        } catch (error) {
            this.logger.error('Error updating System 2:', error);
            return {
                ...state,
                finalResponse: {
                    success: false,
                    error: error.message
                }
            };
        }
    }

    // Private analysis methods

    _evaluateEffectiveness(feedback) {
        // Evaluate how effective the communication was
        return feedback.userEngagementImprovement || 0;
    }

    _evaluateAccuracy(feedback) {
        // Evaluate response accuracy
        return feedback.responseRelevance || 0.5;
    }

    _evaluateTiming(feedback) {
        // Evaluate timing effectiveness
        return feedback.timingScore || 0.5;
    }

    _generateImprovements(feedback) {
        const suggestions = [];
        
        if (feedback.responseTime > 2000) {
            suggestions.push('Reduce response generation time');
        }
        
        if (feedback.userEngagement < 0.4) {
            suggestions.push('Improve engagement detection');
        }
        
        return suggestions;
    }
}

/**
 * Integrated Dual-Brain Coordination Workflow
 * Orchestrates the complete dual-brain communication cycle
 */
export class IntegratedDualBrainWorkflow {
    constructor(system1, system2, contextPackager, messageParser) {
        this.system1 = system1;
        this.system2 = system2;
        this.contextPackager = contextPackager;
        this.messageParser = messageParser;
        this.logger = logger;
        
        // Initialize sub-workflows
        this.system2ToSystem1 = new System2ToSystem1Workflow(system2, system1, contextPackager);
        this.system1ToSystem2 = new System1ToSystem2FeedbackWorkflow(system1, system2);
    }

    /**
     * Create the integrated workflow
     */
    createWorkflow() {
        const workflow = new StateGraph({
            ...DualBrainState,
            coordinationPhase: { value: 'input', reducer: (existing, update) => update || existing },
            system1Active: { value: false, reducer: (existing, update) => update ?? existing },
            system2Active: { value: false, reducer: (existing, update) => update ?? existing }
        });

        // Coordination nodes
        workflow.addNode("route_input", this.routeInput.bind(this));
        workflow.addNode("activate_system1", this.activateSystem1.bind(this));
        workflow.addNode("activate_system2", this.activateSystem2.bind(this));
        workflow.addNode("coordinate_response", this.coordinateResponse.bind(this));
        workflow.addNode("collect_feedback", this.collectFeedback.bind(this));

        // Routing logic
        workflow.addEdge(START, "route_input");
        
        workflow.addConditionalEdges(
            "route_input",
            this.routingDecision.bind(this),
            {
                "system1_first": "activate_system1",
                "system2_first": "activate_system2",
                "both": "activate_system1"
            }
        );

        workflow.addEdge("activate_system1", "activate_system2");
        workflow.addEdge("activate_system2", "coordinate_response");
        workflow.addEdge("coordinate_response", "collect_feedback");
        workflow.addEdge("collect_feedback", END);

        return workflow.compile();
    }

    /**
     * Route input to appropriate system
     */
    async routeInput(state) {
        const { inputType, userInput } = state;
        
        this.logger.debug('Routing input through dual-brain system:', { inputType });

        return {
            ...state,
            coordinationPhase: 'routing',
            metadata: {
                ...state.metadata,
                routingTimestamp: Date.now()
            }
        };
    }

    /**
     * Activate System 1 (Fast Brain)
     */
    async activateSystem1(state) {
        try {
            this.logger.debug('Activating System 1 for fast processing');

            // System 1 processes input quickly
            const system1Response = await this.system1.processInput({
                input: state.userInput,
                type: state.inputType,
                context: state.sessionContext
            });

            return {
                ...state,
                system1Response,
                system1Context: system1Response.context,
                multimodalData: system1Response.multimodal,
                system1Active: true,
                coordinationPhase: 'system1_complete'
            };

        } catch (error) {
            this.logger.error('Error activating System 1:', error);
            return {
                ...state,
                system1Active: false,
                metadata: {
                    ...state.metadata,
                    system1Error: error.message
                }
            };
        }
    }

    /**
     * Activate System 2 (Thinking Brain)
     */
    async activateSystem2(state) {
        try {
            this.logger.debug('Activating System 2 for contextual analysis');

            // Use System 2 → System 1 workflow for communication
            const s2tos1Workflow = this.system2ToSystem1.createWorkflow();
            
            const result = await s2tos1Workflow.invoke({
                ...state,
                system1Context: state.system1Context,
                multimodalData: state.multimodalData
            });

            return {
                ...state,
                enhancedContext: result.contextualAnalysis,
                communicationMessage: result.communicationMessage,
                system2Active: true,
                coordinationPhase: 'system2_complete'
            };

        } catch (error) {
            this.logger.error('Error activating System 2:', error);
            return {
                ...state,
                system2Active: false,
                metadata: {
                    ...state.metadata,
                    system2Error: error.message
                }
            };
        }
    }

    /**
     * Coordinate final response between systems
     */
    async coordinateResponse(state) {
        try {
            this.logger.debug('Coordinating final response');

            const { system1Response, communicationMessage, enhancedContext } = state;

            // Parse System 2's communication for System 1
            const parsedMessage = this.messageParser.parseMessage(communicationMessage);
            const actionableInsights = this.messageParser.extractActionableInsights(parsedMessage);

            // Coordinate final response
            const finalResponse = await this.system1.generateFinalResponse({
                initialResponse: system1Response,
                contextualInsights: actionableInsights,
                enhancedContext: enhancedContext
            });

            return {
                ...state,
                finalResponse,
                coordinationPhase: 'complete'
            };

        } catch (error) {
            this.logger.error('Error coordinating response:', error);
            return {
                ...state,
                finalResponse: state.system1Response || { text: 'How can I help you?' },
                coordinationPhase: 'error'
            };
        }
    }

    /**
     * Collect feedback for learning
     */
    async collectFeedback(state) {
        try {
            // Use feedback workflow to collect and process feedback
            const feedbackWorkflow = this.system1ToSystem2.createWorkflow();
            
            const feedbackResult = await feedbackWorkflow.invoke({
                ...state,
                inputType: 'feedback'
            });

            return {
                ...state,
                metadata: {
                    ...state.metadata,
                    feedbackCollected: true,
                    learningApplied: feedbackResult.finalResponse?.learningApplied || false
                }
            };

        } catch (error) {
            this.logger.error('Error collecting feedback:', error);
            return state;
        }
    }

    /**
     * Routing decision logic
     */
    routingDecision(state) {
        const { inputType, sessionContext } = state;

        // Real-time input goes to System 1 first
        if (inputType === 'voice' || inputType === 'audio' || inputType === 'video') {
            return 'system1_first';
        }

        // Complex reasoning goes to System 2 first
        if (sessionContext?.requiresReasoning || sessionContext?.isProactive) {
            return 'system2_first';
        }

        // Default: activate both systems (System 1 first)
        return 'both';
    }
}

export default {
    DualBrainState,
    System2ToSystem1Workflow,
    System1ToSystem2FeedbackWorkflow,
    IntegratedDualBrainWorkflow
};