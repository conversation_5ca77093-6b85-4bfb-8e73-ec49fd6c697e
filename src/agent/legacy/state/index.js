/**
 * Avatar State Management Core
 * Shared state definitions, annotations, and reducer functions for use across the application
 */

export { AvatarStateAnnotation, AVATAR_STATES, StateTransitionRules, AgentStateAnnotation, AGENT_STATES } from './definitions.js';
export { AvatarStateReducers } from './reducers.js';
// BaseStateManager removed - replaced with SimpleUIStateManager for essential UI coordination
export { createStateWorkflow, createAgentWorkflow } from './workflow.js';
