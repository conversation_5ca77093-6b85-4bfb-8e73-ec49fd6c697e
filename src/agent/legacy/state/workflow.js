/**
 * State Workflow Factory
 * Factory functions for creating specialized LangGraph workflows
 */

import { StateGraph, START, END } from "@langchain/langgraph/web";
import { AvatarStateAnnotation, AVATAR_STATES } from './definitions.js';

/**
 * Create a unified state workflow for general state transitions
 * @param {Object} customNodes - Custom node implementations to override defaults
 * @param {Object} options - Workflow configuration options
 * @param {boolean} options.enableBargeIn - Enable barge-in handling for avatar workflows
 * @returns {StateGraph} Configured LangGraph workflow
 */
export function createStateWorkflow(customNodes = {}, options = {}) {
    const { enableBargeIn = false } = options;
    const workflow = new StateGraph(AvatarStateAnnotation);

    // Base node set for general state transitions
    const nodes = {
        validateTransition: defaultValidateTransition,
        processStateTransition: defaultProcessStateTransition,
        updateUI: defaultUpdateUI,
        handleError: defaultHandleError,
        ...customNodes
    };

    // Add nodes to workflow
    Object.entries(nodes).forEach(([name, nodeFunction]) => {
        workflow.addNode(name, nodeFunction);
    });

    // Basic routing for general state transitions
    const routingFunction = enableBargeIn && customNodes.handleBargeIn
        ? routeWithBargeInSupport  // Use barge-in routing if enabled and handler provided
        : routeBasicTransition;

    const routingDestinations = enableBargeIn && customNodes.handleBargeIn
        ? ['processStateTransition', 'handleBargeIn', 'handleError']
        : ['processStateTransition', 'handleError'];

    workflow
        .addEdge(START, 'validateTransition')
        .addConditionalEdges(
            'validateTransition',
            routingFunction,
            routingDestinations
        )
        .addEdge('processStateTransition', 'updateUI')
        .addEdge('updateUI', END)
        .addEdge('handleError', END);

    // Add barge-in edge if enabled
    if (enableBargeIn && customNodes.handleBargeIn) {
        workflow.addEdge('handleBargeIn', 'processStateTransition');
    }

    return workflow;
}
/**
 * Default workflow node implementations
 */

async function defaultValidateTransition(state) {
    const { transitionMetadata } = state;
    const { targetState, force = false } = transitionMetadata;

    try {
        // Validate state transition directly
        const { StateValidation } = await import('./definitions.js');
        if (!force) {
            if (!StateValidation.isValidState(targetState)) {
                throw new Error(`Invalid state: ${targetState}`);
            }
            if (!StateValidation.canTransitionTo(state.currentState, targetState)) {
                throw new Error(`Invalid transition: ${state.currentState} → ${targetState}`);
            }
        }

        return { lastError: null };
    } catch (error) {
        return { lastError: error.message };
    }
}

async function defaultProcessStateTransition(state) {
    const { transitionMetadata, currentState, stateHistory } = state;
    const { targetState, sessionId = 'default', metadata = {} } = transitionMetadata;

    const oldState = currentState;

    // Process state transition directly
    const transitionResult = {
        previousState: oldState,
        currentState: targetState,
        transitionMetadata: {
            ...metadata,
            timestamp: Date.now()
        }
    };

    // Add to history directly
    const historyEntry = {
        from: oldState,
        to: targetState,
        timestamp: Date.now(),
        metadata
    };
    const newHistory = [...stateHistory, historyEntry];

    // Limit history size
    const limitedHistory = newHistory.length > 50 ? newHistory.slice(-50) : newHistory;

    return {
        ...transitionResult,
        stateHistory: [limitedHistory[limitedHistory.length - 1]]
    };
}

async function defaultUpdateUI(state) {
    const { currentState } = state;
    // Use StateValidation directly instead of AvatarStateReducers
    const { StateValidation } = await import('./definitions.js');
    const message = StateValidation.getStatusMessage(currentState);

    return { statusMessage: message };
}

async function defaultHandleError(state) {
    const { lastError } = state;
    const { AVATAR_STATES } = await import('./definitions.js');
    return {
        lastError: typeof lastError === 'string' ? lastError : lastError?.message || 'Unknown error',
        currentState: AVATAR_STATES.ERROR,
        statusMessage: 'Error occurred',
        transitionMetadata: {
            error: true,
            originalState: state.currentState,
            timestamp: Date.now()
        }
    };
}

/**
 * Unified routing functions
 */

/**
 * Basic routing without barge-in support
 */
function routeBasicTransition(state) {
    return state.lastError ? 'handleError' : 'processStateTransition';
}

/**
 * Route with barge-in support - used when enableBargeIn is true
 * Enhanced with contextual analysis for intelligent interruption decisions
 */
function routeWithBargeInSupport(state) {
    if (state.lastError) {
        return 'handleError';
    }

    // Avatar-specific barge-in detection: VAD during speaking state
    const { transitionMetadata, currentState, processingContext } = state;
    if (transitionMetadata && transitionMetadata.targetState) {
        const { targetState, vadTriggered, reason, contextualAnalysis } = transitionMetadata;

        // Enhanced condition: VAD triggered while speaking -> check contextual analysis
        if (currentState === AVATAR_STATES.SPEAKING &&
            targetState === AVATAR_STATES.LISTENING &&
            (vadTriggered || reason === 'voice_activity')) {

            // Use contextual analysis to make intelligent barge-in decision
            if (contextualAnalysis?.shouldAllowInterruption) {
                return 'handleBargeIn';
            } else if (contextualAnalysis?.confidence && contextualAnalysis.confidence < 0.7) {
                // Low confidence VAD - might be false positive, continue speaking
                return 'processStateTransition';
            } else {
                // Default to barge-in for strong VAD signals
                return 'handleBargeIn';
            }
        }
    }

    return 'processStateTransition';
}

/**
 * Create an agent workflow for LLM conversation and tool execution
 * @param {Object} customNodes - Custom node implementations to override defaults
 * @param {Object} options - Workflow configuration options
 * @param {Object} options.toolNode - Pre-configured ToolNode for tool execution
 * @param {Object} options.checkpointer - Memory checkpointer for conversation history
 * @returns {StateGraph} Configured LangGraph workflow for agent operations
 */
export function createAgentWorkflow(customNodes = {}, options = {}) {
    const { toolNode, checkpointer } = options;

    // Use AvatarStateAnnotation directly for agent workflows
    // AvatarStateAnnotation is the proper state annotation, not a container for AgentState
    const workflow = new StateGraph(AvatarStateAnnotation);

    // Base node set for agent conversation workflows
    const nodes = {
        processInput: defaultProcessInput,
        loadMemory: defaultLoadMemory,
        contextualAnalysis: defaultContextualAnalysis,
        generateResponse: defaultGenerateResponse,
        executeTools: toolNode || defaultExecuteTools,
        updateAgent: defaultUpdateAgent,
        handleError: defaultHandleAgentError,
        ...customNodes
    };

    // Add nodes to workflow
    Object.entries(nodes).forEach(([name, nodeFunction]) => {
        workflow.addNode(name, nodeFunction);
    });

    // Agent workflow routing with tool execution and contextual analysis
    workflow
        .addEdge(START, 'processInput')
        .addEdge('processInput', 'loadMemory')
        .addEdge('loadMemory', 'contextualAnalysis')
        .addEdge('contextualAnalysis', 'generateResponse')
        .addConditionalEdges(
            'generateResponse',
            routeAfterGeneration,
            ['executeTools', 'updateAgent', 'handleError']
        )
        .addEdge('executeTools', 'updateAgent')
        .addEdge('updateAgent', END)
        .addEdge('handleError', END);

    return workflow;
}

/**
 * Default agent workflow node implementations
 */

async function defaultProcessInput(state) {
    const { messages, userInput } = state;

    // Process the input and prepare for memory loading
    return {
        processedInput: userInput || messages?.[messages.length - 1]?.content || '',
        inputProcessedAt: Date.now()
    };
}

async function defaultLoadMemory(state) {
    const { processedInput } = state;

    // Basic memory loading - can be overridden by custom implementations
    return {
        memoryLoaded: true,
        contextLoaded: !!processedInput,
        loadedAt: Date.now()
    };
}

async function defaultContextualAnalysis(state) {
    const { processedInput, memoryContext } = state;

    // Basic contextual analysis - should be overridden by actual ContextualAnalysisService
    const hasInput = !!processedInput;
    const hasMemory = !!memoryContext;

    // Simple heuristics for default implementation
    const complexity = processedInput ? processedInput.length / 100 : 0; // Rough complexity based on length
    const shouldUseSystem2 = complexity > 0.5 || (processedInput && processedInput.includes('analyze'));

    return {
        contextualAnalysis: {
            complexity,
            shouldUseSystem2,
            hasContext: hasInput && hasMemory,
            confidence: hasInput ? 0.8 : 0.3,
            shouldAllowInterruption: true, // Default to allowing interruptions
            timestamp: Date.now()
        },
        analysisComplete: true
    };
}

async function defaultGenerateResponse(state) {
    const { processedInput } = state;

    // Basic response generation - should be overridden by actual LLM implementation
    return {
        response: `Processed: ${processedInput}`,
        tool_calls: [], // No tool calls in default implementation
        responseGeneratedAt: Date.now()
    };
}

async function defaultExecuteTools(state) {
    const { tool_calls } = state;

    // Basic tool execution - should be overridden by actual ToolNode
    if (!tool_calls || tool_calls.length === 0) {
        return { toolsExecuted: false };
    }

    return {
        toolsExecuted: true,
        toolResults: tool_calls.map(call => ({
            tool_call_id: call.id,
            content: `Executed ${call.name}`,
            type: 'tool'
        }))
    };
}

async function defaultUpdateAgent(state) {
    const { response, toolResults } = state;

    // Basic agent update - combines response and tool results
    const finalResponse = toolResults && toolResults.length > 0
        ? `${response}\n\nTool results: ${toolResults.map(r => r.content).join(', ')}`
        : response;

    return {
        finalResponse,
        agentUpdated: true,
        updatedAt: Date.now()
    };
}

async function defaultHandleAgentError(state) {
    const { lastError } = state;

    return {
        lastError: typeof lastError === 'string' ? lastError : lastError?.message || 'Agent workflow error',
        errorHandled: true,
        errorAt: Date.now()
    };
}

/**
 * Agent workflow routing function
 */
function routeAfterGeneration(state) {
    if (state.lastError) {
        return 'handleError';
    }

    // Check if there are tool calls to execute
    const { tool_calls } = state;
    if (tool_calls && tool_calls.length > 0) {
        return 'executeTools';
    }

    return 'updateAgent';
}
