# Legacy State Management Files

These files contain StateGraph-based state management implementations that are not currently used in the production system.

## Files in Legacy State Directory

### `definitions.js` ✅
- **Purpose**: Consolidated StateGraph definitions and node methods
- **Part 1**: State constants, annotations, and transition rules (`AVATAR_STATES`, `AvatarStateAnnotation`, `StateTransitionRules`)
- **Part 2**: Legacy node factory methods (`createLegacyStateGraphNodes()`)
- **Part 3**: Usage examples and documentation
- **Status**: Complete consolidation of state definitions and node methods
- **Usage**: Single import point for all StateGraph-related functionality

### `workflow.js` 
- **Purpose**: Factory functions for creating specialized LangGraph workflows
- **Functions**: `createStateWorkflow()`, `createAgentWorkflow()`
- **Status**: Not imported anywhere, designed for custom StateGraph implementations
- **Usage**: Can be used for future custom StateGraph workflows instead of ReactAgent

### `reducers.js`
- **Purpose**: Pure reducer functions for state transitions and updates
- **Functions**: `AvatarStateReducers` with transition logic
- **Status**: Not imported anywhere
- **Usage**: Can be used for custom state management implementations

### `index.js`
- **Purpose**: Central export point for state management utilities
- **Status**: Re-exports functions that are not being imported
- **Usage**: Would be useful if the other state files are reactivated

## Current Active State Files

**All active state functionality has been moved to production or consolidated into `definitions.js`.**

## Future Usage

If you need to implement custom StateGraph workflows instead of using ReactAgent:

1. **Import from `definitions.js`**: Single source for state definitions and node factory methods
2. **Use `workflow.js`**: Factory functions to create custom StateGraph workflows  
3. **Use `reducers.js`**: For custom state transition logic
4. **Use `index.js`**: As a central import point for additional utilities

## Architecture Context

The current production system uses:
- **ReactAgent** (from LangGraph prebuilt) for agent workflows
- **Direct state coordination services** for UI and connection management
- **Simplified state management** without complex StateGraph workflows

These legacy files provide the foundation for **custom StateGraph implementations** if more complex workflows are needed in the future.
