/**
 * Core LangGraph Node Implementations
 * Reusable node implementations for LangGraph workflows
 * These are the actual business logic nodes used by the agent
 */

import { ChatPromptTemplate } from "@langchain/core/prompts";
import {
    getMemoryContext,
    addToMemory,
} from '../prompts/base.js';
import { fetchLLMApi } from '@/utils/apiProxy.ts';

/**
 * Core agent workflow node implementations
 * These bind to the LangGraphAgentService instance when used
 */

/**
 * Process input and convert to proper message format
 */
export function createProcessInputNode(agentService) {
    return async function processInputNode(state) {
        try {
            const { userInput, sessionId } = state;

            // Notify mode change
            await agentService._notifyExternalStateUpdate({ phase: 'processing' }, sessionId);

            return {
                agentMode: 'processing',
                isListening: false,
                isSpeaking: false,
                waitingForResponse: true,
                inputType: userInput?.type || 'text',
                timestamp: Date.now(),
                messages: [{
                    role: 'user',
                    content: userInput?.text || userInput
                }]
            };
        } catch (error) {
            return { lastError: error, agentMode: 'error' };
        }
    };
}

/**
 * Load memory context for the session
 */
export function createLoadMemoryNode(agentService) {
    return async function loadMemoryNode(state) {
        try {
            const { sessionId } = state;
            agentService.logger.debug('🔍 [NodeDebug] Loading memory for session:', sessionId);

            if (agentService.memoryManager) {
                const memoryContext = await getMemoryContext(sessionId, agentService.memoryManager);
                agentService.logger.debug('🔍 [NodeDebug] Memory loaded successfully', {
                    sessionId,
                    memoryType: memoryContext?.memory_type || 'unknown',
                    historyLength: memoryContext?.conversation_history?.length || 0
                });
                return { memoryContext };
            }

            agentService.logger.debug('🔍 [NodeDebug] No memory manager available, using empty context');
            return { memoryContext: { conversation_history: [], memory_type: 'none' } };
        } catch (error) {
            agentService.logger.warn('Memory loading failed:', error);
            return { memoryContext: { conversation_history: [], memory_type: 'none' } };
        }
    };
}

/**
 * Generate response using LLM with enhanced debugging
 */
export function createGenerateResponseNode(agentService) {
    return async function generateResponseNode(state) {
        try {
            const { messages, language, sessionId, memoryContext } = state;

            agentService.logger.debug('🔍 [NodeDebug] Generating LLM response in workflow node:', {
                messageCount: messages?.length || 0,
                language,
                sessionId,
                hasMemory: !!memoryContext
            });

            // Create system prompt using injected function
            const systemPrompt = await agentService.createSystemPrompt({
                language,
                includeToolInfo: true,
                _suppressLogging: false
            });

            agentService.logger.debug('🔍 [NodeDebug] System prompt created', {
                promptLength: systemPrompt.length
            });

            // Use ToolManagementService for unified tool binding
            const llmWithTools = agentService.toolManagementService 
                ? agentService.toolManagementService.bindToolsToModel(agentService.model, agentService.tools)
                : agentService.model.bindTools(agentService.tools);

            // CRITICAL DEBUG: Verify tool availability to LLM
            agentService.logger.info('🔍 [CRITICAL_DEBUG] Tools available to LLM:', {
                toolCount: agentService.tools?.length || 0,
                toolNames: agentService.tools?.map(t => t.name) || [],
                hasControlAvatarSpeech: agentService.tools?.some(t => t.name === 'speaking_control'),
                hasSpeakResponse: agentService.tools?.some(t => t.name === 'speaking_control'),
                toolsDetail: agentService.tools?.map(t => ({ name: t.name, hasSchema: !!t.schema, hasFunc: !!t.func }))
            });

            // Ensure memoryContext has the required structure
            const safeMemoryContext = memoryContext || { conversation_history: [], memory_type: 'none' };
            const conversationHistory = safeMemoryContext.conversation_history || [];

            agentService.logger.debug('🔍 [NodeDebug] Using conversation history:', {
                historyLength: conversationHistory.length,
                recentMessages: conversationHistory.slice(-3).map(msg => ({
                    role: msg.role || 'unknown',
                    contentLength: msg.content?.length || 0
                }))
            });

            // Get user input from messages
            const userInput = messages[messages.length - 1]?.content || messages[messages.length - 1] || "Hello";

            agentService.logger.debug('🔍 [NodeDebug] Using LLM API service for server-side processing');

            // Use LLM API service instead of direct model chain
            const llmOptions = {
                provider: 'aliyun',
                model: 'qwen-omni-turbo-realtime',
                modalities: ['text', 'audio'],
                audioConfig: { voice: 'Ethan', format: 'wav' },
                tools: agentService.tools.map(tool => ({
                    name: tool.name,
                    description: tool.description || '',
                    schema: tool.schema || {}
                })),
                tool_choice: agentService.tools.length > 0 ? agentService._getToolChoiceForTools() : 'auto',
                temperature: 0.7,
                max_tokens: 2000,
                language,
                stream: false
            };

            // Prepare messages with system prompt
            const llmMessages = [
                { role: 'system', content: systemPrompt },
                ...conversationHistory.slice(-5).map(msg => ({ role: msg.role || 'human', content: msg.content })),
                { role: 'user', content: userInput }
            ];

            agentService.logger.debug('🔍 [NodeDebug] Calling LLM API service with:', {
                messagesCount: llmMessages.length,
                toolsCount: llmOptions.tools.length,
                toolNames: llmOptions.tools.map(t => t.name),
                toolChoice: llmOptions.tool_choice,
                provider: llmOptions.provider,
                model: llmOptions.model
            });

            // Generate response using direct API call (removing redundant llmAPI wrapper)
            const payload = {
                provider: llmOptions.provider || 'aliyun',
                model: llmOptions.model || 'qwen-omni-turbo-realtime',
                messages: llmMessages, // Messages are already formatted properly
                modalities: llmOptions.modalities || ['text'],
                audioConfig: llmOptions.audioConfig || { voice: 'zhixiaoxia', format: 'wav' },
                tools: llmOptions.tools || [],
                tool_choice: llmOptions.tool_choice || 'auto',
                temperature: llmOptions.temperature || 0.7,
                max_tokens: llmOptions.max_tokens,
                stream: llmOptions.stream || false,
                ...llmOptions // Pass through any additional options
            };

            const apiResponse = await fetchLLMApi(payload);

            // Convert API response to LangChain format
            const response = {
                content: apiResponse.content || 'No response generated',
                tool_calls: apiResponse.tool_calls || [],
                additional_kwargs: {
                    audio: apiResponse.audio || null
                }
            };

            agentService.logger.debug('🔍 [NodeDebug] LLM API response converted:', {
                hasContent: !!response.content,
                hasAudio: !!response.additional_kwargs.audio,
                hasToolCalls: response.tool_calls.length > 0,
                toolCallNames: response.tool_calls.map(tc => tc.name)
            });

            // CRITICAL FIX: If no tool calls and speaking tools are available, suggest control_avatar_speech
            if (response.tool_calls.length === 0 && llmOptions.tool_choice === 'required') {
                agentService.logger.warn('🔧 ATTEMPTING FALLBACK: LLM did not generate tool calls despite tool_choice="required"');

                // Fallback: Create a control_avatar_speech tool call if content exists
                if (response.content && response.content.trim() !== 'No response generated') {
                    const fallbackToolCall = {
                        id: `fallback_${Date.now()}`,
                        name: 'control_avatar_speech',
                        args: {
                            action: 'speak',
                            text: response.content.trim(),
                            voice: 'Serena',
                            options: {
                                streaming: true,
                                priority: 'normal'
                            }
                        }
                    };

                    response.tool_calls = [fallbackToolCall];

                    agentService.logger.info('🔧 Added fallback control_avatar_speech tool call for response:', {
                        toolCallId: fallbackToolCall.id,
                        textLength: fallbackToolCall.args.text.length
                    });
                }
            }

            // Notify mode change
            agentService.callbacks.onModeChange('speaking', sessionId);

            // Update messages to include the AI response for ToolNode
            const updatedMessages = [
                ...messages,
                response // This is the AIMessage with potential tool_calls
            ];

            return {
                agentResponse: response.content,
                messages: updatedMessages,
                agentMode: 'speaking'
            };

        } catch (error) {
            agentService.logger.error('❌ [NodeDebug] Response generation failed:', error);
            agentService.callbacks.onError(error);
            return { lastError: error, agentMode: 'error' };
        }
    };
}

/**
 * Update agent state and save to memory
 */
export function createUpdateAgentNode(agentService) {
    return async function updateAgentNode(state) {
        try {
            const { sessionId, agentResponse, userInput, memoryContext } = state;

            // Save to memory if successful
            if (agentResponse && userInput && agentService.memoryManager) {
                try {
                    await addToMemory(
                        sessionId,
                        userInput?.text || userInput,
                        agentResponse,
                        agentService.memoryManager
                    );
                } catch (memoryError) {
                    agentService.logger.warn('Memory save failed:', memoryError);
                }
            }

            // Return to idle state
            agentService.callbacks.onModeChange('idle', sessionId);

            return {
                agentMode: 'idle',
                isListening: false,
                isSpeaking: false,
                waitingForResponse: false,
                processingComplete: true,
                streamingActive: false,
                lastResponse: agentResponse
            };
        } catch (error) {
            return { lastError: error, agentMode: 'error' };
        }
    };
}

/**
 * Handle errors in the workflow
 */
export function createHandleErrorNode(agentService) {
    return async function handleErrorNode(state) {
        const { lastError, sessionId } = state;

        agentService.logger.error('Handling error in workflow:', lastError);

        // Notify error
        agentService.callbacks.onError(lastError);
        await agentService._notifyExternalStateUpdate({ phase: 'error', error: lastError }, sessionId);

        return {
            agentMode: 'error',
            isStreaming: false
        };
    };
}

/**
 * Get tool choice strategy based on available tools
 */
export function createGetToolChoiceForTools(agentService) {
    return function getToolChoiceForTools() {
        const hasSpeakingTool = agentService.tools.some(tool => tool.name === 'control_avatar_speech');

        agentService.logger.debug('🎯 Determining tool choice strategy:', {
            configuredStrategy: agentService.toolChoiceStrategy,
            toolsAvailable: agentService.tools.length,
            hasSpeakingTool,
            toolNames: agentService.tools.map(t => t.name)
        });

        // For avatar applications, use 'auto' to let LLM decide contextually
        if (hasSpeakingTool) {
            agentService.logger.debug('🔧 Using "auto" tool choice for autonomous avatar communication');
            return 'auto'; // Let LLM decide when to speak vs think silently
        }

        // For other applications, follow configured strategy or default to 'auto'
        const strategy = agentService.toolChoiceStrategy === 'required' ? 'auto' : (agentService.toolChoiceStrategy || 'auto');

        agentService.logger.debug('🔧 Using tool choice strategy:', strategy);
        return strategy;
    };
}

/**
 * Route after response generation based on tool calls
 */
export function createRouteAfterGeneration(agentService) {
    return function routeAfterGeneration(state) {
        const { messages } = state;

        agentService.logger.debug('🔀 Routing after generation:', {
            messageCount: messages?.length || 0,
            hasMessages: !!(messages && messages.length > 0)
        });

        if (messages && messages.length > 0) {
            const lastMessage = messages[messages.length - 1];

            agentService.logger.debug('🔀 Examining last message for tool calls:', {
                hasToolCalls: !!(lastMessage?.tool_calls && lastMessage.tool_calls.length > 0),
                toolCallCount: lastMessage?.tool_calls?.length || 0,
                messageType: typeof lastMessage,
                hasContent: !!lastMessage?.content
            });

            // Route to tools if tool calls are present
            if (lastMessage?.tool_calls && lastMessage.tool_calls.length > 0) {
                agentService.logger.debug('🔀 Routing to tools execution');
                return 'tools';
            }
        }

        agentService.logger.debug('🔀 Routing to end (no tools to execute)');
        return '__end__';
    };
}

/**
 * Create enhanced ToolNode with service injection and monitoring
 */
export async function createServiceAwareToolNode(agentService) {
    // Use native LangGraph ToolNode directly
    const { ToolNode } = await import('@langchain/langgraph/prebuilt');
    const toolNode = new ToolNode(agentService.tools);

    // Add basic logging wrapper
    const originalInvoke = toolNode.invoke.bind(toolNode);
    toolNode.invoke = async (input, config = {}) => {
        const lastMessage = input.messages?.[input.messages.length - 1];
        if (lastMessage?.tool_calls?.length > 0) {
            agentService.logger.info(`🛠️ Executing ${lastMessage.tool_calls.length} tools:`,
                lastMessage.tool_calls.map(tc => tc.name).join(', '));

            // Notify callbacks
            for (const toolCall of lastMessage.tool_calls) {
                try {
                    agentService.callbacks.onToolCall?.({
                        toolCall,
                        sessionId: input.sessionId || 'default'
                    });
                } catch (error) {
                    agentService.logger.warn('Tool callback notification failed:', error);
                }
            }
        }

        // Enhance config with service injection
        const enhancedConfig = {
            ...config,
            configurable: {
                thread_id: 'default',
                ...config.configurable
            },
            services: agentService.services
        };

        return await originalInvoke(input, enhancedConfig);
    };

    return toolNode;
} 