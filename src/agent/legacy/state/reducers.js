/**
 * Avatar State Reducers
 * Pure reducer functions for state transitions and updates
 */

import { AVATAR_STATES, StateValidation } from './definitions.js';

/**
 * Avatar State Reducers
 * Collection of pure reducer functions for state management
 */
export const AvatarStateReducers = {
    /**
     * Reduce state transition
     */
    transitionState(currentState, targetState, metadata = {}) {
        // Validate transition if enabled
        if (metadata.validateTransition !== false) {
            if (!StateValidation.isValidState(targetState)) {
                throw new Error(`Invalid state: ${targetState}`);
            }

            if (!StateValidation.canTransitionTo(currentState, targetState) && !metadata.force) {
                throw new Error(`Invalid transition: ${currentState} → ${targetState}`);
            }
        }

        return {
            previousState: currentState,
            currentState: targetState,
            transitionMetadata: {
                ...metadata,
                timestamp: Date.now()
            }
        };
    },

    /**
     * Add state history entry
     */
    addHistoryEntry(history, fromState, toState, metadata = {}) {
        const entry = {
            from: fromState,
            to: toState,
            timestamp: Date.now(),
            metadata
        };

        return [...history, entry];
    },

    /**
     * Limit history size
     */
    limitHistory(history, maxSize = 50) {
        return history.length > maxSize ? history.slice(-maxSize) : history;
    },


    /**
     * Update status message
     */
    updateStatusMessage(state) {
        return StateValidation.getStatusMessage(state);
    },

    /**
     * Handle error state
     */
    handleError(error, currentState = AVATAR_STATES.IDLE) {
        return {
            lastError: error.message || error,
            currentState: AVATAR_STATES.ERROR,
            statusMessage: 'Error occurred',
            transitionMetadata: {
                error: true,
                originalState: currentState,
                timestamp: Date.now()
            }
        };
    },

    /**
     * Clear error state
     */
    clearError() {
        return {
            lastError: null,
            currentState: AVATAR_STATES.IDLE
        };
    },

    /**
     * Update Aliyun state
     */
    updateAliyunState(currentAliyunState, updates) {
        return {
            ...currentAliyunState,
            ...updates
        };
    },

    /**
     * Update processing context
     */
    updateProcessingContext(currentContext, updates) {
        return {
            ...currentContext,
            ...updates
        };
    },

    /**
     * Reset state to initial
     */
    resetToInitial(sessionId = 'default') {
        return {
            currentState: AVATAR_STATES.IDLE,
            previousState: null,
            bargeInActive: false,
            bargeInCooldownUntil: 0,
            lastError: null,
            statusMessage: 'Ready',
            vadActive: false,
            audioProcessing: false,
            sessionId,
            transitionMetadata: {
                reset: true,
                timestamp: Date.now()
            },
            aliyunState: {
                connected: false,
                vadEnabled: false,
                audioStreaming: false
            },
            processingContext: {}
        };
    }
};

/**
 * State Query Helpers
 * Functions to query state information
 */
export const StateQueries = {
    /**
     * Check if in specific state
     */
    isInState(state, targetState) {
        return state.currentState === targetState;
    },

    /**
     * Get state flags for backward compatibility
     */
    getStateFlags(state) {
        return {
            isListening: state.currentState === AVATAR_STATES.LISTENING,
            isSpeaking: state.currentState === AVATAR_STATES.SPEAKING,
            isProcessing: state.currentState === AVATAR_STATES.PROCESSING,
            isIdle: state.currentState === AVATAR_STATES.IDLE,
            isError: state.currentState === AVATAR_STATES.ERROR
        };
    },

    // getRecentHistory removed - replaced with LangGraph memory system

};
