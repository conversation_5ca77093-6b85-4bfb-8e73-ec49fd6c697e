/**
 * Legacy StateGraph Definitions & Node Methods
 * 
 * This file contains:
 * 1. Agent State Definitions - State constants, annotations, and transition rules
 * 2. Legacy StateGraph Node Methods - Node factories for custom StateGraph workflows
 * 
 * USAGE: For future StateGraph-based agent implementations
 * 
 * The current implementation uses createReactAgent() from LangGraph/prebuilt,
 * which internally handles the state graph workflow. These definitions and methods
 * can be used if you need to create a custom StateGraph workflow instead.
 * 
 * @see ./workflow.js for actual StateGraph workflow implementations
 * @see https://langchain-ai.github.io/langgraph/concepts/low_level/ for StateGraph docs
 */

import { Annotation } from "@langchain/langgraph/web";
import {
  createProcessInputNode,
  createLoadMemoryNode,
  createGenerateResponseNode,
  createUpdateAgentNode,
  createHandleErrorNode,
  createGetToolChoiceForTools
} from './node.js';

// ==========================================
// PART 1: STATE DEFINITIONS
// ==========================================

/**
 * Core State Constants
 * Used for both avatar and agent state management
 */
export const AVATAR_STATES = {
  IDLE: 'idle',
  LISTENING: 'listening',
  PROCESSING: 'processing',
  SPEAKING: 'speaking',
  ERROR: 'error'
};

// Export alias for backward compatibility
export const AGENT_STATES = AVATAR_STATES;

/**
 * LangGraph State Annotation for Agent State Management
 * Unified schema that works for both avatar and general agent operations
 */
export const AvatarStateAnnotation = Annotation.Root({
  // Core agent states
  currentState: Annotation({
    reducer: (x, y) => y ?? x,
    default: () => AVATAR_STATES.IDLE
  }),
  previousState: Annotation({
    reducer: (x, y) => y ?? x,
    default: () => null
  }),

  // State transition metadata
  transitionMetadata: Annotation({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({})
  }),

  // Barge-in handling for real-time interactions
  bargeInActive: Annotation({
    reducer: (x, y) => y ?? x,
    default: () => false
  }),
  bargeInCooldownUntil: Annotation({
    reducer: (x, y) => y ?? x,
    default: () => 0
  }),

  // Session context
  sessionId: Annotation({
    reducer: (x, y) => y ?? x,
    default: () => 'default'
  }),

  // State history tracking
  stateHistory: Annotation({
    reducer: (x, y) => [...x, ...y],
    default: () => []
  }),

  // UI context and status
  statusMessage: Annotation({
    reducer: (x, y) => y ?? x,
    default: () => 'Ready'
  }),

  // Error handling
  lastError: Annotation({
    reducer: (x, y) => y ?? x,
    default: () => null
  }),

  // Processing context for extended functionality
  processingContext: Annotation({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({})
  })
});

// Export alias for backward compatibility
export const AgentStateAnnotation = AvatarStateAnnotation;

/**
 * State Transition Rules
 * Defines valid state transitions for agent operations
 */
export const StateTransitionRules = {
  [AVATAR_STATES.IDLE]: [
    AVATAR_STATES.IDLE,        // Allow same-state transitions
    AVATAR_STATES.LISTENING,
    AVATAR_STATES.PROCESSING,
    AVATAR_STATES.SPEAKING,
    AVATAR_STATES.ERROR
  ],
  [AVATAR_STATES.LISTENING]: [
    AVATAR_STATES.IDLE,
    AVATAR_STATES.LISTENING,   // Allow same-state transitions
    AVATAR_STATES.PROCESSING,
    AVATAR_STATES.SPEAKING,
    AVATAR_STATES.ERROR
  ],
  [AVATAR_STATES.PROCESSING]: [
    AVATAR_STATES.IDLE,
    AVATAR_STATES.PROCESSING, // Allow same-state transitions
    AVATAR_STATES.SPEAKING,
    AVATAR_STATES.LISTENING,
    AVATAR_STATES.ERROR
  ],
  [AVATAR_STATES.SPEAKING]: [
    AVATAR_STATES.IDLE,
    AVATAR_STATES.SPEAKING,   // Allow same-state transitions
    AVATAR_STATES.LISTENING,
    AVATAR_STATES.ERROR
  ],
  [AVATAR_STATES.ERROR]: [
    AVATAR_STATES.IDLE,
    AVATAR_STATES.LISTENING,
    AVATAR_STATES.PROCESSING,
    AVATAR_STATES.SPEAKING,
    AVATAR_STATES.ERROR       // Allow same-state transitions
  ]
};

/**
 * UI-friendly status messages for each state
 */
export const STATE_STATUS_MESSAGES = {
  [AVATAR_STATES.IDLE]: 'Ready',
  [AVATAR_STATES.LISTENING]: 'Listening...',
  [AVATAR_STATES.PROCESSING]: 'Processing...',
  [AVATAR_STATES.SPEAKING]: 'Speaking...',
  [AVATAR_STATES.ERROR]: 'Error'
};

/**
 * State Validation Utilities
 * Helper functions for state management and validation
 */
export const StateValidation = {
  /**
   * Check if a state exists
   */
  isValidState(state) {
    return Object.values(AVATAR_STATES).includes(state);
  },

  /**
   * Check if transition is allowed
   */
  canTransitionTo(fromState, toState) {
    return StateTransitionRules[fromState]?.includes(toState) || false;
  },

  /**
   * Get status message for state
   */
  getStatusMessage(state) {
    return STATE_STATUS_MESSAGES[state] || 'Unknown State';
  }
};

/**
 * Default configuration for state management
 */
export const DEFAULT_STATE_CONFIG = {
  initialState: AVATAR_STATES.IDLE,
  bargeInEnabled: true,
  bargeInCooldown: 500, // ms
  maxHistorySize: 50,
  enableStateValidation: true
};

// ==========================================
// PART 2: LEGACY STATEGRAPH NODE METHODS
// ==========================================

/**
 * Legacy StateGraph Node Factory
 * Creates bound node implementations for custom StateGraph workflows
 * 
 * @param {LangGraphAgentService} agentService - The agent service instance
 * @returns {Object} Object containing bound node methods for StateGraph
 */
export function createLegacyStateGraphNodes(agentService) {
  return {
    /**
     * Process input node for StateGraph workflows
     * @returns {Function} Bound process input node
     */
    processInputNode: createProcessInputNode(agentService),

    /**
     * Load memory node for StateGraph workflows
     * @returns {Function} Bound load memory node
     */
    loadMemoryNode: createLoadMemoryNode(agentService),

    /**
     * Generate response node for StateGraph workflows
     * @returns {Function} Bound generate response node
     */
    generateResponseNode: createGenerateResponseNode(agentService),

    /**
     * Update agent node for StateGraph workflows
     * @returns {Function} Bound update agent node
     */
    updateAgentNode: createUpdateAgentNode(agentService),

    /**
     * Handle error node for StateGraph workflows
     * @returns {Function} Bound handle error node
     */
    handleErrorNode: createHandleErrorNode(agentService),

    /**
     * Get tool choice configuration for StateGraph workflows
     * @returns {Function} Tool choice configuration function
     */
    getToolChoiceForTools: () => {
      const getToolChoiceFunc = createGetToolChoiceForTools(agentService);
      return getToolChoiceFunc();
    }
  };
}

// ==========================================
// PART 3: USAGE EXAMPLES & DOCUMENTATION
// ==========================================

/**
 * Example usage for custom StateGraph workflow:
 * 
 * ```javascript
 * import { StateGraph, START, END } from "@langchain/langgraph/web";
 * import { 
 *     createLegacyStateGraphNodes, 
 *     AvatarStateAnnotation,
 *     AVATAR_STATES
 * } from './legacy/definitions.js';
 * 
 * // Create custom StateGraph workflow
 * const workflow = new StateGraph(AvatarStateAnnotation);
 * const nodes = createLegacyStateGraphNodes(agentService);
 * 
 * // Add nodes to workflow
 * workflow.addNode("processInput", nodes.processInputNode);
 * workflow.addNode("loadMemory", nodes.loadMemoryNode);
 * workflow.addNode("generateResponse", nodes.generateResponseNode);
 * workflow.addNode("updateAgent", nodes.updateAgentNode);
 * workflow.addNode("handleError", nodes.handleErrorNode);
 * 
 * // Define edges and compile
 * workflow.addEdge(START, "processInput");
 * workflow.addEdge("processInput", "loadMemory");
 * workflow.addEdge("loadMemory", "generateResponse");
 * workflow.addConditionalEdges(
 *     "generateResponse",
 *     // Route based on tool calls or completion
 *     (state) => {
 *         const lastMessage = state.messages?.[state.messages.length - 1];
 *         return lastMessage?.tool_calls?.length > 0 ? "executeTools" : "updateAgent";
 *     },
 *     ["executeTools", "updateAgent"]
 * );
 * workflow.addEdge("updateAgent", END);
 * 
 * const compiledWorkflow = workflow.compile();
 * ```
 * 
 * Current Production Architecture:
 * The main system uses ReactAgent which handles all this automatically:
 * 
 * ```javascript
 * // Current implementation (production)
 * import { createReactAgent } from "@langchain/langgraph/prebuilt";
 * 
 * const agent = createReactAgent({
 *     llm: model,
 *     tools: tools,
 *     checkpointSaver: checkpointer
 * });
 * ```
 */
