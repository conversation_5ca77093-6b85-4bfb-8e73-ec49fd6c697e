/**
 * Legacy StateGraph Node Methods
 * 
 * These node methods were originally designed for custom StateGraph workflows
 * but are not currently used since the codebase uses ReactAgent instead.
 * 
 * USAGE: For future StateGraph-based agent implementations
 * 
 * The current implementation uses createReactAgent() from LangGraph/prebuilt,
 * which internally handles the state graph workflow. These methods can be
 * used if you need to create a custom StateGraph workflow instead.
 * 
 * @see src/agent/state/workflow.js for actual StateGraph workflow implementations
 * @see https://langchain-ai.github.io/langgraph/concepts/low_level/ for StateGraph docs
 */

import {
  createProcessInputNode,
  createLoadMemoryNode,
  createGenerateResponseNode,
  createUpdateAgentNode,
  createHandleErrorNode,
  createGetToolChoiceForTools
} from './state/node.js';

/**
 * Legacy StateGraph Node Factory
 * Creates bound node implementations for custom StateGraph workflows
 * 
 * @param {LangGraphAgentService} agentService - The agent service instance
 * @returns {Object} Object containing bound node methods for StateGraph
 */
export function createLegacyStateGraphNodes(agentService) {
  return {
    /**
     * Process input node for StateGraph workflows
     * @returns {Function} Bound process input node
     */
    processInputNode: createProcessInputNode(agentService),

    /**
     * Load memory node for StateGraph workflows
     * @returns {Function} Bound load memory node
     */
    loadMemoryNode: createLoadMemoryNode(agentService),

    /**
     * Generate response node for StateGraph workflows
     * @returns {Function} Bound generate response node
     */
    generateResponseNode: createGenerateResponseNode(agentService),

    /**
     * Update agent node for StateGraph workflows
     * @returns {Function} Bound update agent node
     */
    updateAgentNode: createUpdateAgentNode(agentService),

    /**
     * Handle error node for StateGraph workflows
     * @returns {Function} Bound handle error node
     */
    handleErrorNode: createHandleErrorNode(agentService),

    /**
     * Get tool choice configuration for StateGraph workflows
     * @returns {Function} Tool choice configuration function
     */
    getToolChoiceForTools: () => {
      const getToolChoiceFunc = createGetToolChoiceForTools(agentService);
      return getToolChoiceFunc();
    }
  };
}

/**
 * Example usage for custom StateGraph workflow:
 * 
 * ```javascript
 * import { StateGraph, START, END } from "@langchain/langgraph/web";
 * import { createLegacyStateGraphNodes } from './legacy/stateGraphNodes.js';
 * 
 * // Create custom StateGraph workflow
 * const workflow = new StateGraph(YourStateAnnotation);
 * const nodes = createLegacyStateGraphNodes(agentService);
 * 
 * // Add nodes to workflow
 * workflow.addNode("processInput", nodes.processInputNode);
 * workflow.addNode("loadMemory", nodes.loadMemoryNode);
 * workflow.addNode("generateResponse", nodes.generateResponseNode);
 * workflow.addNode("updateAgent", nodes.updateAgentNode);
 * workflow.addNode("handleError", nodes.handleErrorNode);
 * 
 * // Define edges and compile
 * workflow.addEdge(START, "processInput");
 * workflow.addEdge("processInput", "loadMemory");
 * // ... define more edges
 * 
 * const compiledWorkflow = workflow.compile();
 * ```
 */
