/**
 * System Feedback Service - Dual-Brain Architecture User Feedback
 * 
 * Provides comprehensive user feedback for the dual-brain system:
 * - System 1 (WebSocket/Realtime) feedback
 * - System 2 (HTTP/Thinking) feedback  
 * - ContextualService feedback
 * - Health status feedback
 * - Permission status feedback (non-blocking)
 */

import { createLogger, LogLevel } from '@/utils/logger';
import { ConnectionState, SessionState, VADState } from '../../models/aliyun/AliyunConfig.js';

const logger = createLogger('SystemFeedbackService', LogLevel.DEBUG);

/**
 * System states for user feedback
 */
export const SystemState = {
    // Overall system states
    INITIALIZING: 'initializing',
    READY: 'ready',
    LISTENING: 'listening',
    PROCESSING: 'processing',
    RESPONDING: 'responding',
    ERROR: 'error',
    OFFLINE: 'offline',
    
    // Permission states (non-blocking)
    PERMISSIONS_NEEDED: 'permissions_needed',
    PERMISSIONS_GRANTED: 'permissions_granted',
    PERMISSIONS_DENIED: 'permissions_denied'
};

/**
 * Feedback types for different user interface elements
 */
export const FeedbackType = {
    STATUS: 'status',           // Overall system status
    VAD: 'vad',                // Voice activity detection
    TRANSCRIPTION: 'transcription', // Speech-to-text feedback
    THINKING: 'thinking',       // System 2 processing feedback
    RESPONSE: 'response',       // System response feedback
    ERROR: 'error',            // Error feedback
    PERMISSION: 'permission',   // Permission feedback (non-blocking)
    HEALTH: 'health'           // Health status feedback
};

/**
 * System Feedback Service for dual-brain architecture
 */
export class SystemFeedbackService {
    constructor(options = {}) {
        this.options = {
            enableVADFeedback: options.enableVADFeedback !== false,
            enableHealthFeedback: options.enableHealthFeedback !== false,
            enablePermissionFeedback: options.enablePermissionFeedback !== false,
            feedbackDelay: options.feedbackDelay || 100, // Debounce feedback updates
            ...options
        };

        // Current system state
        this.currentState = {
            overall: SystemState.INITIALIZING,
            system1: { state: ConnectionState.DISCONNECTED, details: null },
            system2: { state: 'idle', details: null },
            vad: { state: VADState.IDLE, details: null },
            contextual: { state: 'idle', details: null },
            permissions: {
                camera: 'unknown',
                microphone: 'unknown',
                blocking: false // Permissions never block core functionality
            },
            health: {
                overall: false,
                aliyun: false,
                websocket: false,
                lastCheck: null
            }
        };

        // Feedback callbacks by type
        this.feedbackCallbacks = {
            [FeedbackType.STATUS]: new Set(),
            [FeedbackType.VAD]: new Set(),
            [FeedbackType.TRANSCRIPTION]: new Set(),
            [FeedbackType.THINKING]: new Set(),
            [FeedbackType.RESPONSE]: new Set(),
            [FeedbackType.ERROR]: new Set(),
            [FeedbackType.PERMISSION]: new Set(),
            [FeedbackType.HEALTH]: new Set()
        };

        // Debounce feedback updates
        this.feedbackTimers = new Map();

        logger.info('✅ SystemFeedbackService initialized', {
            vadFeedback: this.options.enableVADFeedback,
            healthFeedback: this.options.enableHealthFeedback,
            permissionFeedback: this.options.enablePermissionFeedback
        });
    }

    /**
     * Register feedback callback for specific feedback type
     * @param {string} feedbackType - Type of feedback (from FeedbackType)
     * @param {Function} callback - Callback function (state, details) => void
     */
    onFeedback(feedbackType, callback) {
        if (!this.feedbackCallbacks[feedbackType]) {
            logger.warn(`Unknown feedback type: ${feedbackType}`);
            return;
        }

        this.feedbackCallbacks[feedbackType].add(callback);
        
        // Immediately send current state for this feedback type
        this._sendImmediateFeedback(feedbackType, callback);
    }

    /**
     * Remove feedback callback
     * @param {string} feedbackType - Type of feedback
     * @param {Function} callback - Callback function to remove
     */
    removeFeedback(feedbackType, callback) {
        if (this.feedbackCallbacks[feedbackType]) {
            this.feedbackCallbacks[feedbackType].delete(callback);
        }
    }

    /**
     * Send immediate feedback for new subscribers
     * @private
     */
    _sendImmediateFeedback(feedbackType, callback) {
        try {
            switch (feedbackType) {
                case FeedbackType.STATUS:
                    callback(this.currentState.overall, this.currentState);
                    break;
                case FeedbackType.VAD:
                    callback(this.currentState.vad.state, this.currentState.vad.details);
                    break;
                case FeedbackType.PERMISSION:
                    callback(this.currentState.permissions, { blocking: false });
                    break;
                case FeedbackType.HEALTH:
                    callback(this.currentState.health.overall, this.currentState.health);
                    break;
            }
        } catch (error) {
            logger.warn('Error sending immediate feedback:', error);
        }
    }

    /**
     * Debounced feedback notification
     * @private
     */
    _debouncedNotify(feedbackType, state, details) {
        // Clear existing timer
        if (this.feedbackTimers.has(feedbackType)) {
            clearTimeout(this.feedbackTimers.get(feedbackType));
        }

        // Set new timer
        const timer = setTimeout(() => {
            this._notifyCallbacks(feedbackType, state, details);
            this.feedbackTimers.delete(feedbackType);
        }, this.options.feedbackDelay);

        this.feedbackTimers.set(feedbackType, timer);
    }

    /**
     * Notify callbacks for specific feedback type
     * @private
     */
    _notifyCallbacks(feedbackType, state, details) {
        const callbacks = this.feedbackCallbacks[feedbackType];
        if (!callbacks || callbacks.size === 0) return;

        callbacks.forEach(callback => {
            try {
                callback(state, details);
            } catch (error) {
                logger.warn(`Error in ${feedbackType} feedback callback:`, error);
            }
        });
    }

    /**
     * Update System 1 (WebSocket/Realtime) state
     */
    updateSystem1State(connectionState, sessionState = null, details = {}) {
        this.currentState.system1 = {
            state: connectionState,
            sessionState,
            details: {
                timestamp: Date.now(),
                ...details
            }
        };

        // Map to overall system state
        let overallState = SystemState.READY;
        
        if (connectionState === ConnectionState.CONNECTING) {
            overallState = SystemState.INITIALIZING;
        } else if (connectionState === ConnectionState.READY) {
            overallState = SystemState.READY;
        } else if (connectionState === ConnectionState.ERROR) {
            overallState = SystemState.ERROR;
        } else if (connectionState === ConnectionState.DISCONNECTED) {
            overallState = SystemState.OFFLINE;
        }

        this._updateOverallState(overallState);

        logger.debug('📡 System 1 state updated:', {
            connection: connectionState,
            session: sessionState,
            overall: overallState
        });
    }

    /**
     * Update System 2 (HTTP/Thinking) state
     */
    updateSystem2State(state, details = {}) {
        this.currentState.system2 = {
            state,
            details: {
                timestamp: Date.now(),
                ...details
            }
        };

        // Notify thinking feedback
        if (state === 'processing') {
            this._debouncedNotify(FeedbackType.THINKING, true, details);
        } else {
            this._debouncedNotify(FeedbackType.THINKING, false, details);
        }

        logger.debug('🧠 System 2 state updated:', { state, details });
    }

    /**
     * Update VAD (Voice Activity Detection) state
     */
    updateVADState(vadState, details = {}) {
        if (!this.options.enableVADFeedback) return;

        this.currentState.vad = {
            state: vadState,
            details: {
                timestamp: Date.now(),
                ...details
            }
        };

        // Map VAD state to overall system state
        if (vadState === VADState.LISTENING || vadState === VADState.SPEECH_DETECTED) {
            this._updateOverallState(SystemState.LISTENING);
        } else if (vadState === VADState.SPEECH_ENDED) {
            this._updateOverallState(SystemState.PROCESSING);
        }

        this._debouncedNotify(FeedbackType.VAD, vadState, details);

        logger.debug('🎤 VAD state updated:', { vadState, details });
    }

    /**
     * Update ContextualService state
     */
    updateContextualState(state, details = {}) {
        this.currentState.contextual = {
            state,
            details: {
                timestamp: Date.now(),
                ...details
            }
        };

        logger.debug('🔍 Contextual state updated:', { state, details });
    }

    /**
     * Update permission state (non-blocking)
     */
    updatePermissionState(permissionType, state, details = {}) {
        if (!this.options.enablePermissionFeedback) return;

        this.currentState.permissions[permissionType] = state;
        this.currentState.permissions.blocking = false; // Always non-blocking

        this._debouncedNotify(FeedbackType.PERMISSION, this.currentState.permissions, {
            type: permissionType,
            state,
            blocking: false,
            timestamp: Date.now(),
            ...details
        });

        logger.debug(`🔐 Permission state updated:`, {
            type: permissionType,
            state,
            blocking: false
        });
    }

    /**
     * Update health status
     */
    updateHealthStatus(component, healthy, details = {}) {
        if (!this.options.enableHealthFeedback) return;

        this.currentState.health[component] = healthy;
        this.currentState.health.lastCheck = Date.now();

        // Calculate overall health (only critical components)
        this.currentState.health.overall = 
            this.currentState.health.aliyun && 
            this.currentState.health.websocket;

        this._debouncedNotify(FeedbackType.HEALTH, this.currentState.health.overall, {
            component,
            healthy,
            overall: this.currentState.health.overall,
            timestamp: Date.now(),
            ...details
        });

        logger.debug('🏥 Health status updated:', {
            component,
            healthy,
            overall: this.currentState.health.overall
        });
    }

    /**
     * Send transcription feedback
     */
    sendTranscriptionFeedback(text, isPartial = false, details = {}) {
        this._debouncedNotify(FeedbackType.TRANSCRIPTION, {
            text,
            isPartial,
            timestamp: Date.now(),
            ...details
        });

        logger.debug('📝 Transcription feedback:', { text, isPartial });
    }

    /**
     * Send response feedback
     */
    sendResponseFeedback(response, isPartial = false, details = {}) {
        this._debouncedNotify(FeedbackType.RESPONSE, {
            response,
            isPartial,
            timestamp: Date.now(),
            ...details
        });

        if (!isPartial) {
            // Response complete - back to ready state
            this._updateOverallState(SystemState.READY);
        } else {
            // Response in progress
            this._updateOverallState(SystemState.RESPONDING);
        }

        logger.debug('💬 Response feedback:', { response, isPartial });
    }

    /**
     * Send error feedback
     */
    sendErrorFeedback(error, component = 'system', details = {}) {
        const errorDetails = {
            error: error.message || error,
            component,
            timestamp: Date.now(),
            ...details
        };

        this._notifyCallbacks(FeedbackType.ERROR, error, errorDetails);

        // Update overall state to error
        this._updateOverallState(SystemState.ERROR);

        logger.error(`❌ Error feedback (${component}):`, errorDetails);
    }

    /**
     * Update overall system state
     * @private
     */
    _updateOverallState(newState) {
        if (this.currentState.overall !== newState) {
            const previousState = this.currentState.overall;
            this.currentState.overall = newState;

            this._debouncedNotify(FeedbackType.STATUS, newState, {
                previous: previousState,
                timestamp: Date.now(),
                system1: this.currentState.system1.state,
                system2: this.currentState.system2.state
            });

            logger.debug('🎯 Overall state updated:', {
                previous: previousState,
                current: newState
            });
        }
    }

    /**
     * Get current system state
     */
    getCurrentState() {
        return {
            timestamp: Date.now(),
            ...this.currentState
        };
    }

    /**
     * Get user-friendly status message
     */
    getStatusMessage() {
        const state = this.currentState.overall;
        const health = this.currentState.health.overall;

        if (!health) {
            return 'System checking connectivity...';
        }

        switch (state) {
            case SystemState.INITIALIZING:
                return 'System starting up...';
            case SystemState.READY:
                return 'Ready to listen';
            case SystemState.LISTENING:
                return 'Listening...';
            case SystemState.PROCESSING:
                return 'Processing your request...';
            case SystemState.RESPONDING:
                return 'Generating response...';
            case SystemState.ERROR:
                return 'System error - please try again';
            case SystemState.OFFLINE:
                return 'System offline - check connection';
            case SystemState.PERMISSIONS_NEEDED:
                return 'Optional permissions available';
            default:
                return 'System ready';
        }
    }

    /**
     * Check if system is ready for interaction
     */
    isSystemReady() {
        return this.currentState.overall === SystemState.READY &&
               this.currentState.health.overall;
    }

    /**
     * Check if system is processing
     */
    isSystemBusy() {
        return [
            SystemState.LISTENING,
            SystemState.PROCESSING,
            SystemState.RESPONDING
        ].includes(this.currentState.overall);
    }

    /**
     * Cleanup and dispose
     */
    dispose() {
        // Clear all timers
        for (const timer of this.feedbackTimers.values()) {
            clearTimeout(timer);
        }
        this.feedbackTimers.clear();

        // Clear all callbacks
        for (const callbackSet of Object.values(this.feedbackCallbacks)) {
            callbackSet.clear();
        }

        logger.debug('🧹 SystemFeedbackService disposed');
    }
}

export default SystemFeedbackService;