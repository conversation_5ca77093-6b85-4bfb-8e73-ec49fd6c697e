/**
 * WebSocket Health Monitoring Service
 * 
 * Monitors WebSocket connection state and provides health checks for the dual brain system.
 * Prevents race conditions by ensuring proper initialization sequencing.
 */

import { createLogger } from '../../../utils/logger.js';

export class WebSocketHealthMonitor {
  constructor(options = {}) {
    this.logger = createLogger('WebSocketHealthMonitor');
    this.options = {
      checkInterval: options.checkInterval || 5000, // 5 seconds
      healthTimeout: options.healthTimeout || 3000,  // 3 seconds
      maxRetries: options.maxRetries || 3,
      enableAutoRecovery: options.enableAutoRecovery !== false,
      ...options
    };
    
    this.models = new Map();
    this.healthStatus = new Map();
    this.monitoringActive = false;
    this.intervalId = null;
  }

  /**
   * Register a WebSocket model for monitoring
   */
  registerModel(modelId, model) {
    if (!model || typeof model.isRealtimeModeActive !== 'function') {
      this.logger.warn(`⚠️ Model ${modelId} does not support realtime mode monitoring`);
      return;
    }

    this.models.set(modelId, model);
    this.healthStatus.set(modelId, {
      isHealthy: false,
      lastCheck: null,
      consecutiveFailures: 0,
      realtimeActive: false,
      connectionState: 'unknown',
      sessionStabilized: false
    });

    this.logger.info(`✅ Registered WebSocket model for monitoring: ${modelId}`);
  }

  /**
   * Start health monitoring
   */
  startMonitoring() {
    if (this.monitoringActive) {
      this.logger.warn('⚠️ Health monitoring already active');
      return;
    }

    this.monitoringActive = true;
    this.logger.info('🔍 Starting WebSocket health monitoring...', {
      checkInterval: this.options.checkInterval,
      modelsCount: this.models.size
    });

    // Perform initial health check
    this._performHealthCheck();

    // Setup periodic monitoring
    this.intervalId = setInterval(() => {
      this._performHealthCheck();
    }, this.options.checkInterval);
  }

  /**
   * Stop health monitoring
   */
  stopMonitoring() {
    if (!this.monitoringActive) {
      return;
    }

    this.monitoringActive = false;
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    this.logger.info('🔽 Stopped WebSocket health monitoring');
  }

  /**
   * Get current health status for all models
   */
  getHealthStatus() {
    const status = {
      overallHealth: 'healthy',
      totalModels: this.models.size,
      healthyModels: 0,
      unhealthyModels: 0,
      models: {}
    };

    for (const [modelId, health] of this.healthStatus) {
      status.models[modelId] = { ...health };
      
      if (health.isHealthy) {
        status.healthyModels++;
      } else {
        status.unhealthyModels++;
      }
    }

    // Determine overall health
    if (status.unhealthyModels === 0) {
      status.overallHealth = 'healthy';
    } else if (status.healthyModels > status.unhealthyModels) {
      status.overallHealth = 'degraded';
    } else {
      status.overallHealth = 'unhealthy';
    }

    return status;
  }

  /**
   * Check if a specific model is ready for use
   */
  async isModelReady(modelId, waitForReady = false, timeout = 5000) {
    const model = this.models.get(modelId);
    if (!model) {
      this.logger.warn(`⚠️ Model ${modelId} not registered for monitoring`);
      return false;
    }

    // Check current status
    const currentStatus = this._checkModelHealth(modelId, model);
    
    if (currentStatus.isHealthy && currentStatus.realtimeActive) {
      return true;
    }

    if (!waitForReady) {
      return false;
    }

    // Wait for model to become ready
    this.logger.debug(`⏳ Waiting for model ${modelId} to become ready (max ${timeout}ms)...`);
    
    const startTime = Date.now();
    while (Date.now() - startTime < timeout) {
      const status = this._checkModelHealth(modelId, model);
      
      if (status.isHealthy && status.realtimeActive) {
        this.logger.info(`✅ Model ${modelId} became ready after ${Date.now() - startTime}ms`);
        return true;
      }
      
      // Wait a bit before checking again
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    this.logger.warn(`⚠️ Model ${modelId} did not become ready within ${timeout}ms`);
    return false;
  }

  /**
   * Get readiness summary for all models
   */
  getReadinessSummary() {
    const summary = {
      totalModels: this.models.size,
      readyModels: 0,
      notReadyModels: 0,
      details: {}
    };

    for (const [modelId] of this.models) {
      const status = this.healthStatus.get(modelId);
      const isReady = status?.isHealthy && status?.realtimeActive;
      
      summary.details[modelId] = {
        ready: isReady,
        status: status?.isHealthy ? 'healthy' : 'unhealthy',
        realtimeActive: status?.realtimeActive || false,
        connectionState: status?.connectionState || 'unknown'
      };

      if (isReady) {
        summary.readyModels++;
      } else {
        summary.notReadyModels++;
      }
    }

    return summary;
  }

  /**
   * Perform health check on all registered models
   * @private
   */
  _performHealthCheck() {
    this.logger.debug('🔍 Performing WebSocket health check...', {
      modelsCount: this.models.size
    });

    const results = [];
    
    for (const [modelId, model] of this.models) {
      const health = this._checkModelHealth(modelId, model);
      results.push({ modelId, health });
      
      // Update stored health status
      this.healthStatus.set(modelId, health);
    }

    // Log summary
    const healthyCount = results.filter(r => r.health.isHealthy).length;
    const totalCount = results.length;
    
    this.logger.debug(`🏥 Health check complete: ${healthyCount}/${totalCount} models healthy`);

    // Handle unhealthy models if auto-recovery is enabled
    if (this.options.enableAutoRecovery) {
      this._handleUnhealthyModels(results.filter(r => !r.health.isHealthy));
    }

    return results;
  }

  /**
   * Check health of a specific model
   * @private
   */
  _checkModelHealth(modelId, model) {
    const health = {
      isHealthy: false,
      lastCheck: Date.now(),
      consecutiveFailures: this.healthStatus.get(modelId)?.consecutiveFailures || 0,
      realtimeActive: false,
      connectionState: 'unknown',
      sessionStabilized: false
    };

    try {
      // Check if realtime mode is active
      if (typeof model.isRealtimeModeActive === 'function') {
        health.realtimeActive = model.isRealtimeModeActive();
      }

      // Check WebSocket connection state
      if (model.realtimeSocket) {
        const readyState = model.realtimeSocket.readyState;
        health.connectionState = this._getWebSocketState(readyState);
      }

      // Check session stabilization
      if (typeof model.realtimeSessionStabilized !== 'undefined') {
        health.sessionStabilized = model.realtimeSessionStabilized;
      }

      // Overall health assessment
      health.isHealthy = health.realtimeActive && 
                       health.connectionState === 'OPEN' && 
                       health.sessionStabilized;

      // Reset failure count if healthy
      if (health.isHealthy) {
        health.consecutiveFailures = 0;
      } else {
        health.consecutiveFailures++;
      }

    } catch (error) {
      this.logger.error(`❌ Error checking health for model ${modelId}:`, error);
      health.consecutiveFailures++;
      health.isHealthy = false;
    }

    return health;
  }

  /**
   * Handle unhealthy models with auto-recovery
   * @private
   */
  async _handleUnhealthyModels(unhealthyModels) {
    for (const { modelId, health } of unhealthyModels) {
      if (health.consecutiveFailures >= this.options.maxRetries) {
        this.logger.error(`❌ Model ${modelId} has failed ${health.consecutiveFailures} consecutive health checks`);
        
        // Attempt auto-recovery if enabled
        await this._attemptAutoRecovery(modelId);
      } else {
        this.logger.warn(`⚠️ Model ${modelId} unhealthy (${health.consecutiveFailures}/${this.options.maxRetries} failures)`);
      }
    }
  }

  /**
   * Attempt to recover an unhealthy model
   * @private
   */
  async _attemptAutoRecovery(modelId) {
    this.logger.info(`🔄 Attempting auto-recovery for model ${modelId}...`);
    
    const model = this.models.get(modelId);
    if (!model) {
      return;
    }

    try {
      // Try to reinitialize realtime mode
      if (typeof model.initializeRealtimeMode === 'function') {
        this.logger.debug(`🔧 Reinitializing realtime mode for ${modelId}...`);
        
        await model.initializeRealtimeMode({
          onSessionReady: (session) => {
            this.logger.info(`✅ Auto-recovery successful for ${modelId}:`, session.id);
          },
          onRealtimeError: (error) => {
            this.logger.error(`❌ Auto-recovery failed for ${modelId}:`, error);
          }
        });

        // Wait a moment for stabilization
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Check if recovery was successful
        const health = this._checkModelHealth(modelId, model);
        if (health.isHealthy) {
          this.logger.info(`✅ Auto-recovery successful for model ${modelId}`);
          
          // Reset failure count
          this.healthStatus.set(modelId, {
            ...health,
            consecutiveFailures: 0
          });
        } else {
          this.logger.error(`❌ Auto-recovery failed for model ${modelId}`);
        }
      }
    } catch (error) {
      this.logger.error(`❌ Auto-recovery error for model ${modelId}:`, error);
    }
  }

  /**
   * Convert WebSocket readyState to readable string
   * @private
   */
  _getWebSocketState(readyState) {
    const states = {
      0: 'CONNECTING',
      1: 'OPEN',
      2: 'CLOSING',
      3: 'CLOSED'
    };
    return states[readyState] || `UNKNOWN(${readyState})`;
  }

  /**
   * Cleanup resources
   */
  destroy() {
    this.stopMonitoring();
    this.models.clear();
    this.healthStatus.clear();
    this.logger.info('🔽 WebSocket Health Monitor destroyed');
  }
}

export default WebSocketHealthMonitor;