/**
 * Universal Rate Limiter with Queue Management
 * ARCHITECTURAL FIX: Moved from AliyunRateLimiter to be provider-agnostic
 * Supports all providers (Aliyun, OpenAI, etc.) with unified rate limiting
 */

import { createLogger, LogLevel } from '../../../utils/logger.ts';

/**
 * Provider-agnostic rate limit error
 */
export class RateLimitError extends Error {
    constructor(message, originalError = null, context = {}) {
        super(message);
        this.name = 'RateLimitError';
        this.originalError = originalError;
        this.context = context;
        this.timestamp = Date.now();
        this.retryAfter = context.retryAfter || 1000;
        this.retryable = true;
        
        // Preserve original stack trace if available
        if (originalError?.stack) {
            this.stack = originalError.stack;
        }
    }
}

/**
 * Universal rate limiter with sliding window and queue management
 * Supports any provider with configurable limits
 */
export class UniversalRateLimiter {
    constructor(options = {}) {
        this.logger = createLogger('UniversalRateLimiter', LogLevel.INFO);
        
        // Provider identification
        this.provider = options.provider || 'universal';
        this.resourceType = options.resourceType || 'default';
        
        // Rate limiting configuration
        this.requestsPerSecond = options.requestsPerSecond || 10;
        this.burstLimit = options.burstLimit || 20;
        this.windowSizeMs = options.windowSizeMs || 1000;
        this.maxQueueSize = options.maxQueueSize || 100;
        
        // Sliding window for request tracking
        this.requestHistory = [];
        
        // Request queue for handling rate-limited requests
        this.requestQueue = [];
        this.processing = false;
        
        // Circuit breaker integration
        this.circuitBreaker = options.circuitBreaker || null;
        
        // Metrics tracking
        this.metrics = {
            totalRequests: 0,
            rateLimitedRequests: 0,
            queuedRequests: 0,
            droppedRequests: 0,
            averageWaitTime: 0,
            provider: this.provider,
            resourceType: this.resourceType
        };
        
        // Cleanup interval for old requests
        this.cleanupInterval = setInterval(() => {
            this._cleanupOldRequests();
        }, this.windowSizeMs);
        
        this.logger.info(`Universal rate limiter initialized for ${this.provider}/${this.resourceType}`, {
            requestsPerSecond: this.requestsPerSecond,
            burstLimit: this.burstLimit,
            maxQueueSize: this.maxQueueSize
        });
    }
    
    /**
     * Acquire permission to make a request
     * @param {Object} context - Request context
     * @returns {Promise<boolean>} Whether request can proceed
     */
    async acquire(context = {}) {
        const now = Date.now();
        this.metrics.totalRequests++;
        
        // Clean up old requests first
        this._cleanupOldRequests();
        
        // Check if we're within rate limits
        if (this._canMakeRequest()) {
            this._recordRequest(now);
            return true;
        }
        
        // Rate limited - add to queue if space available
        if (this.requestQueue.length >= this.maxQueueSize) {
            this.metrics.droppedRequests++;
            throw new RateLimitError(
                `Request queue full for ${this.provider}/${this.resourceType} - too many concurrent requests`,
                null,
                { 
                    provider: this.provider,
                    resourceType: this.resourceType,
                    queueSize: this.requestQueue.length,
                    maxQueueSize: this.maxQueueSize,
                    retryAfter: this._getRetryDelay()
                }
            );
        }
        
        // Add to queue and wait
        return this._queueRequest(context);
    }
    
    /**
     * Check if request can be made immediately
     * @private
     */
    _canMakeRequest() {
        const now = Date.now();
        const recentRequests = this.requestHistory.filter(
            timestamp => now - timestamp < this.windowSizeMs
        );
        
        // Check against rate limit
        if (recentRequests.length >= this.requestsPerSecond) {
            return false;
        }
        
        // Check against burst limit
        const veryRecentRequests = this.requestHistory.filter(
            timestamp => now - timestamp < 100 // Last 100ms
        );
        
        return veryRecentRequests.length < Math.ceil(this.burstLimit / 10);
    }
    
    /**
     * Queue a request for later processing
     * @private
     */
    async _queueRequest(context) {
        const startTime = Date.now();
        this.metrics.queuedRequests++;
        this.metrics.rateLimitedRequests++;
        
        return new Promise((resolve, reject) => {
            const queueItem = {
                resolve,
                reject,
                context,
                startTime,
                timeout: setTimeout(() => {
                    // Remove from queue and reject on timeout
                    const index = this.requestQueue.indexOf(queueItem);
                    if (index !== -1) {
                        this.requestQueue.splice(index, 1);
                    }
                    reject(new RateLimitError(
                        `Request timeout while waiting in queue for ${this.provider}/${this.resourceType}`,
                        null,
                        { 
                            provider: this.provider,
                            resourceType: this.resourceType,
                            waitTime: Date.now() - startTime 
                        }
                    ));
                }, context.queueTimeout || 30000) // 30 second queue timeout
            };
            
            this.requestQueue.push(queueItem);
            this._processQueue();
        });
    }
    
    /**
     * Process queued requests
     * @private
     */
    async _processQueue() {
        if (this.processing || this.requestQueue.length === 0) {
            return;
        }
        
        this.processing = true;
        
        while (this.requestQueue.length > 0 && this._canMakeRequest()) {
            const queueItem = this.requestQueue.shift();
            
            if (queueItem) {
                clearTimeout(queueItem.timeout);
                
                // Record metrics
                const waitTime = Date.now() - queueItem.startTime;
                this._updateAverageWaitTime(waitTime);
                
                // Record request and resolve
                this._recordRequest(Date.now());
                queueItem.resolve(true);
            }
        }
        
        this.processing = false;
        
        // Schedule next processing cycle if queue not empty
        if (this.requestQueue.length > 0) {
            setTimeout(() => this._processQueue(), this._getRetryDelay());
        }
    }
    
    /**
     * Record a successful request
     * @private
     */
    _recordRequest(timestamp) {
        this.requestHistory.push(timestamp);
        
        // Keep history reasonable size (last 2 windows)
        const cutoff = timestamp - (this.windowSizeMs * 2);
        this.requestHistory = this.requestHistory.filter(t => t > cutoff);
    }
    
    /**
     * Clean up old requests from history
     * @private
     */
    _cleanupOldRequests() {
        const now = Date.now();
        const cutoff = now - this.windowSizeMs;
        this.requestHistory = this.requestHistory.filter(t => t > cutoff);
    }
    
    /**
     * Get retry delay based on current load
     * @private
     */
    _getRetryDelay() {
        const queueLength = this.requestQueue.length;
        const baseDelay = Math.ceil(1000 / this.requestsPerSecond);
        
        // Increase delay based on queue length
        const queueMultiplier = Math.min(queueLength / 10, 5);
        return Math.floor(baseDelay * (1 + queueMultiplier));
    }
    
    /**
     * Update average wait time metric
     * @private
     */
    _updateAverageWaitTime(newWaitTime) {
        if (this.metrics.queuedRequests === 1) {
            this.metrics.averageWaitTime = newWaitTime;
        } else {
            // Running average
            const weight = 0.1; // Weight for new value
            this.metrics.averageWaitTime = 
                (1 - weight) * this.metrics.averageWaitTime + weight * newWaitTime;
        }
    }
    
    /**
     * Get current rate limiter metrics
     */
    getMetrics() {
        return {
            ...this.metrics,
            currentQueueSize: this.requestQueue.length,
            recentRequestCount: this.requestHistory.filter(
                t => Date.now() - t < this.windowSizeMs
            ).length,
            isRateLimited: !this._canMakeRequest(),
            timestamp: Date.now()
        };
    }
    
    /**
     * Reset rate limiter state
     */
    reset() {
        this.requestHistory = [];
        
        // Reject all queued requests
        this.requestQueue.forEach(item => {
            clearTimeout(item.timeout);
            item.reject(new RateLimitError(`Rate limiter reset for ${this.provider}/${this.resourceType}`));
        });
        this.requestQueue = [];
        
        // Reset metrics
        this.metrics = {
            totalRequests: 0,
            rateLimitedRequests: 0,
            queuedRequests: 0,
            droppedRequests: 0,
            averageWaitTime: 0,
            provider: this.provider,
            resourceType: this.resourceType
        };
        
        this.logger.info(`Rate limiter reset for ${this.provider}/${this.resourceType}`);
    }
    
    /**
     * Cleanup resources
     */
    destroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
        
        this.reset();
        this.logger.info(`Rate limiter destroyed for ${this.provider}/${this.resourceType}`);
    }
}

/**
 * Universal rate limiter manager for all providers and resource types
 */
export class UniversalRateLimiterManager {
    constructor() {
        this.limiters = new Map();
        this.logger = createLogger('UniversalRateLimiterManager', LogLevel.INFO);
    }
    
    /**
     * Get or create rate limiter for provider/resource combination
     * @param {string} provider - Provider name (aliyun, openai, etc.)
     * @param {string} resourceType - Type of resource (http, websocket, audio, etc.)
     * @param {Object} config - Rate limiter configuration
     * @returns {UniversalRateLimiter} Rate limiter instance
     */
    getLimiter(provider, resourceType, config = {}) {
        const key = `${provider}:${resourceType}`;
        
        if (!this.limiters.has(key)) {
            // Provider-specific default configurations
            const providerDefaults = this._getProviderDefaults(provider);
            const resourceDefaults = this._getResourceDefaults(resourceType);
            
            const limiterConfig = {
                provider,
                resourceType,
                ...providerDefaults,
                ...resourceDefaults,
                ...config
            };
            
            const limiter = new UniversalRateLimiter(limiterConfig);
            this.limiters.set(key, limiter);
            
            this.logger.info(`Created rate limiter for ${key}:`, limiterConfig);
        }
        
        return this.limiters.get(key);
    }
    
    /**
     * Get provider-specific default configurations
     * @private
     */
    _getProviderDefaults(provider) {
        const defaults = {
            aliyun: {
                requestsPerSecond: 10,
                burstLimit: 20,
                maxQueueSize: 50
            },
            openai: {
                requestsPerSecond: 15,
                burstLimit: 30,
                maxQueueSize: 100
            },
            anthropic: {
                requestsPerSecond: 5,
                burstLimit: 10,
                maxQueueSize: 25
            },
            default: {
                requestsPerSecond: 10,
                burstLimit: 20,
                maxQueueSize: 50
            }
        };
        
        return defaults[provider] || defaults.default;
    }
    
    /**
     * Get resource-specific default configurations
     * @private
     */
    _getResourceDefaults(resourceType) {
        const defaults = {
            http: {
                requestsPerSecond: 10,
                burstLimit: 20,
                maxQueueSize: 50
            },
            audio: {
                requestsPerSecond: 5,
                burstLimit: 8,
                maxQueueSize: 30
            },
            image: {
                requestsPerSecond: 2,
                burstLimit: 4,
                maxQueueSize: 20
            },
            websocket: {
                requestsPerSecond: 20,
                burstLimit: 30,
                maxQueueSize: 100
            },
            embedding: {
                requestsPerSecond: 20,
                burstLimit: 40,
                maxQueueSize: 150
            },
            default: {
                requestsPerSecond: 10,
                burstLimit: 20,
                maxQueueSize: 50
            }
        };
        
        return defaults[resourceType] || defaults.default;
    }
    
    /**
     * Get metrics for all limiters
     */
    getAllMetrics() {
        const metrics = {};
        
        for (const [key, limiter] of this.limiters) {
            metrics[key] = limiter.getMetrics();
        }
        
        return {
            summary: {
                totalLimiters: this.limiters.size,
                timestamp: Date.now()
            },
            limiters: metrics
        };
    }
    
    /**
     * Get metrics for specific provider
     */
    getProviderMetrics(provider) {
        const metrics = {};
        
        for (const [key, limiter] of this.limiters) {
            if (key.startsWith(`${provider}:`)) {
                metrics[key] = limiter.getMetrics();
            }
        }
        
        return metrics;
    }
    
    /**
     * Reset all rate limiters for provider
     */
    resetProvider(provider) {
        let resetCount = 0;
        
        for (const [key, limiter] of this.limiters) {
            if (key.startsWith(`${provider}:`)) {
                limiter.reset();
                resetCount++;
            }
        }
        
        this.logger.info(`Reset ${resetCount} rate limiters for provider: ${provider}`);
        return resetCount;
    }
    
    /**
     * Reset all rate limiters
     */
    resetAll() {
        for (const [key, limiter] of this.limiters) {
            limiter.reset();
        }
        this.logger.info(`Reset all ${this.limiters.size} rate limiters`);
    }
    
    /**
     * Cleanup all rate limiters
     */
    destroy() {
        for (const [key, limiter] of this.limiters) {
            limiter.destroy();
        }
        this.limiters.clear();
        this.logger.info('All rate limiters destroyed');
    }
}

// Singleton instance for global use
let globalRateLimiterManager = null;

/**
 * Get global rate limiter manager instance
 */
export function getGlobalRateLimiterManager() {
    if (!globalRateLimiterManager) {
        globalRateLimiterManager = new UniversalRateLimiterManager();
    }
    return globalRateLimiterManager;
}

export default {
    UniversalRateLimiter,
    UniversalRateLimiterManager,
    RateLimitError,
    getGlobalRateLimiterManager
};