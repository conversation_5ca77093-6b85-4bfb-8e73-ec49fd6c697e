/**
 * Configuration Architecture Interfaces
 * Unified configuration system replacing the 3 separate API config locations
 */

/**
 * Main configuration interfaces
 */
export interface UnifiedConfig {
  environment: 'development' | 'test' | 'staging' | 'production';
  providers: Record<string, ProviderConfig>;
  global: GlobalConfig;
  security: GlobalSecurityConfig;
  performance: GlobalPerformanceConfig;
  logging: LoggingConfig;
  monitoring: MonitoringConfig;
}

export interface ProviderConfig {
  provider: string;
  enabled: boolean;
  authentication: AuthConfig;
  endpoints: EndpointConfig;
  models: ModelConfig;
  timeouts: TimeoutProfile;
  rateLimits: RateLimitConfig;
  security: SecurityConfig;
  performance: PerformanceConfig;
  audioConfig?: AudioConfig;
  websocketConfig?: WebSocketConfig;
}

export interface GlobalConfig {
  appName: string;
  version: string;
  deploymentId: string;
  region: string;
  debug: boolean;
  testMode: boolean;
  maxConcurrentRequests: number;
  defaultTimeout: number;
}

export interface AuthConfig {
  method: 'bearer' | 'api-key' | 'oauth' | 'custom';
  keySource: 'env' | 'vault' | 'config' | 'runtime';
  envVarNames: string[];
  encryptionEnabled: boolean;
  rotationInterval?: number;
  cacheEnabled: boolean;
  cacheTTL: number;
}

export interface EndpointConfig {
  http: HttpEndpointConfig;
  websocket?: WebSocketEndpointConfig;
  proxy?: ProxyEndpointConfig;
}

export interface HttpEndpointConfig {
  baseURL: string;
  compatibleURL?: string;
  healthEndpoint: string;
  timeout: number;
  retries: number;
  keepAlive: boolean;
}

export interface WebSocketEndpointConfig {
  url: string;
  protocols: string[];
  pingInterval: number;
  pongTimeout: number;
  maxReconnectAttempts: number;
  reconnectDelay: number;
}

export interface ProxyEndpointConfig {
  enabled: boolean;
  host: string;
  port: number;
  protocol: 'http' | 'https';
  pathPrefix: string;
  corsEnabled: boolean;
}

export interface ModelConfig {
  default: string;
  available: string[];
  fallback: string[];
  capabilities: Record<string, ModelCapabilities>;
  pricing?: Record<string, ModelPricing>;
}

export interface ModelCapabilities {
  textGeneration: boolean;
  functionCalling: boolean;
  audioProcessing: boolean;
  imageProcessing: boolean;
  streaming: boolean;
  maxTokens: number;
  supportedModalities: string[];
}

export interface ModelPricing {
  inputTokenPrice: number;
  outputTokenPrice: number;
  currency: string;
  billingUnit: string;
}

export interface AudioConfig {
  sampleRate: number;
  bitDepth: number;
  channels: number;
  format: string;
  defaultVoice: string;
  supportedVoices: string[];
  maxAudioLength: number;
  chunkSize: number;
  minIntervalMs: number;
}

export interface WebSocketConfig {
  endpoint: string;
  supportsBrowserDirectConnection: boolean;
  connectionTimeout: number;
  stabilizationTimeout: number;
  maxReconnectAttempts: number;
  useProxy: boolean;
  proxyEndpoint?: string;
}

/**
 * Global configuration interfaces
 */
export interface GlobalSecurityConfig {
  encryptionEnabled: boolean;
  defaultEncryptionAlgorithm: string;
  tokenRotationEnabled: boolean;
  auditLoggingEnabled: boolean;
  maxFailedAuthAttempts: number;
  authLockoutDuration: number;
}

export interface GlobalPerformanceConfig {
  enableMetrics: boolean;
  enableTracing: boolean;
  enableProfiling: boolean;
  metricsRetentionDays: number;
  alertingEnabled: boolean;
  defaultAlertThresholds: AlertThresholds;
}

export interface LoggingConfig {
  level: 'debug' | 'info' | 'warn' | 'error';
  format: 'json' | 'text' | 'structured';
  destinations: LogDestination[];
  enableCorrelationIds: boolean;
  enableSampling: boolean;
  samplingRate: number;
}

export interface LogDestination {
  type: 'console' | 'file' | 'remote' | 'database';
  config: Record<string, any>;
  filters?: LogFilter[];
}

export interface LogFilter {
  level?: string;
  module?: string;
  messagePattern?: string;
}

export interface MonitoringConfig {
  enabled: boolean;
  endpoint?: string;
  apiKey?: string;
  sampleRate: number;
  enableCustomMetrics: boolean;
  enableSystemMetrics: boolean;
  metricsInterval: number;
}

/**
 * Configuration manager interfaces
 */
export interface IConfigurationManager {
  getConfig(): UnifiedConfig;
  getProviderConfig(provider: string): ProviderConfig;
  updateConfig(updates: Partial<UnifiedConfig>): void;
  validateConfig(): ConfigValidationResult;
  reloadConfig(): Promise<void>;
  subscribeToChanges(callback: ConfigChangeCallback): () => void;
}

export interface ConfigValidationResult {
  isValid: boolean;
  errors: ConfigValidationError[];
  warnings: ConfigValidationWarning[];
}

export interface ConfigValidationError {
  path: string;
  message: string;
  code: string;
  severity: 'error' | 'warning';
}

export interface ConfigValidationWarning {
  path: string;
  message: string;
  suggestion?: string;
}

export type ConfigChangeCallback = (
  changes: ConfigChange[],
  oldConfig: UnifiedConfig,
  newConfig: UnifiedConfig
) => void;

export interface ConfigChange {
  path: string;
  operation: 'add' | 'update' | 'delete';
  oldValue?: any;
  newValue?: any;
}

/**
 * Configuration source interfaces
 */
export interface ConfigSource {
  name: string;
  priority: number;
  load(): Promise<Partial<UnifiedConfig>>;
  watch?(callback: (changes: Partial<UnifiedConfig>) => void): () => void;
}

export interface EnvironmentConfigSource extends ConfigSource {
  prefix: string;
  separator: string;
  allowUndefined: boolean;
}

export interface FileConfigSource extends ConfigSource {
  filePath: string;
  format: 'json' | 'yaml' | 'toml';
  watchForChanges: boolean;
}

export interface RemoteConfigSource extends ConfigSource {
  endpoint: string;
  apiKey?: string;
  pollInterval: number;
  headers?: Record<string, string>;
}

export interface VaultConfigSource extends ConfigSource {
  vaultUrl: string;
  token: string;
  path: string;
  renewToken: boolean;
}

/**
 * Configuration templates and presets
 */
export interface ConfigTemplate {
  name: string;
  description: string;
  environment: string;
  template: Partial<UnifiedConfig>;
}

export interface ConfigPreset {
  development: Partial<UnifiedConfig>;
  test: Partial<UnifiedConfig>;
  staging: Partial<UnifiedConfig>;
  production: Partial<UnifiedConfig>;
}

/**
 * Migration and versioning interfaces
 */
export interface ConfigMigration {
  fromVersion: string;
  toVersion: string;
  migrate(config: any): any;
  validate?(config: any): boolean;
}

export interface ConfigVersion {
  version: string;
  schemaVersion: string;
  compatible: string[];
  deprecated?: string[];
  removed?: string[];
}

/**
 * Runtime configuration interfaces
 */
export interface RuntimeConfigUpdate {
  path: string;
  value: any;
  temporary: boolean;
  expiresAt?: Date;
  reason?: string;
}

export interface ConfigSnapshot {
  timestamp: Date;
  version: string;
  config: UnifiedConfig;
  checksum: string;
}

export interface ConfigHistory {
  snapshots: ConfigSnapshot[];
  maxSnapshots: number;
  retentionPeriod: number;
}

/**
 * Configuration validation schemas
 */
export interface ConfigSchema {
  version: string;
  schema: any; // JSON Schema object
  examples: Record<string, any>;
  documentation: Record<string, string>;
}

export interface ValidationContext {
  environment: string;
  provider?: string;
  strict: boolean;
  ignoreUnknown: boolean;
}

/**
 * Feature flag interfaces
 */
export interface FeatureFlag {
  name: string;
  enabled: boolean;
  conditions?: FeatureFlagCondition[];
  metadata?: Record<string, any>;
}

export interface FeatureFlagCondition {
  type: 'environment' | 'user' | 'provider' | 'custom';
  operator: 'equals' | 'contains' | 'matches' | 'gt' | 'lt';
  value: any;
}

export interface FeatureFlagContext {
  environment: string;
  userId?: string;
  provider?: string;
  custom?: Record<string, any>;
}