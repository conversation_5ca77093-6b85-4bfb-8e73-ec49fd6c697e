/**
 * Unified Request Validation Service
 * 
 * Consolidates request validation logic from multiple sources
 * Provides single ground truth for request validation and formatting
 */

import { createLogger } from '@/utils/logger';
import { ALIYUN_AUDIO_CONFIG } from '@/agent/models/aliyun/AliyunConfig.js';
import type {
  LLMRequest,
  LLMMessage,
  ValidationResult,
  ValidationError,
  ValidationWarning,
  ProviderConfig,
  LLMTool
} from './types.js';

export class LLMValidationService {
  private logger: any;

  constructor() {
    this.logger = createLogger('LLMValidationService');
  }

  /**
   * Comprehensive request validation
   */
  validateRequest(request: LLMRequest, providerConfig: ProviderConfig): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // Validate required fields
    this.validateRequiredFields(request, errors);

    // Validate provider and model
    this.validateProviderAndModel(request, providerConfig, errors);

    // Validate messages
    this.validateMessages(request.messages, errors, warnings);

    // Validate modalities
    this.validateModalities(request.modalities, providerConfig, errors, warnings);

    // Validate audio configuration
    this.validateAudioConfig(request.audioConfig, errors, warnings);

    // Validate tools
    this.validateTools(request.tools, errors, warnings);

    // Validate parameters
    this.validateParameters(request, errors, warnings);

    this.logger.debug('Request validation completed', {
      errors: errors.length,
      warnings: warnings.length,
      provider: request.provider,
      model: request.model
    });

    return {
      isValid: errors.length === 0,
      errors: errors.map(err => err.message),
      warnings: warnings.map(warn => warn.message)
    };
  }

  /**
   * Validate required fields
   */
  private validateRequiredFields(request: LLMRequest, errors: ValidationError[]): void {
    if (!request.provider) {
      errors.push({
        field: 'provider',
        code: 'MISSING_REQUIRED',
        message: 'Provider is required (e.g., "aliyun", "openai")'
      });
    }

    if (!request.model) {
      errors.push({
        field: 'model',
        code: 'MISSING_REQUIRED',
        message: 'Model is required (e.g., "qwen-omni-turbo-realtime")'
      });
    }

    if (!request.messages) {
      errors.push({
        field: 'messages',
        code: 'MISSING_REQUIRED',
        message: 'Messages array is required'
      });
    }
  }

  /**
   * Validate provider and model
   */
  private validateProviderAndModel(
    request: LLMRequest,
    providerConfig: ProviderConfig,
    errors: ValidationError[]
  ): void {
    if (request.provider && request.provider !== providerConfig.name) {
      errors.push({
        field: 'provider',
        code: 'UNSUPPORTED_PROVIDER',
        message: `Provider "${request.provider}" is not supported. Supported: ${providerConfig.name}`,
        value: request.provider
      });
    }

    // Model validation could be enhanced with a registry of valid models per provider
    if (request.model && typeof request.model !== 'string') {
      errors.push({
        field: 'model',
        code: 'INVALID_TYPE',
        message: 'Model must be a string',
        value: typeof request.model
      });
    }
  }

  /**
   * Validate messages array
   */
  private validateMessages(
    messages: LLMMessage[],
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    if (!Array.isArray(messages)) {
      errors.push({
        field: 'messages',
        code: 'INVALID_TYPE',
        message: 'Messages must be an array',
        value: typeof messages
      });
      return;
    }

    if (messages.length === 0) {
      errors.push({
        field: 'messages',
        code: 'EMPTY_ARRAY',
        message: 'Messages array must contain at least one message'
      });
      return;
    }

    // Validate individual messages
    messages.forEach((message, index) => {
      this.validateMessage(message, index, errors, warnings);
    });

    // Check for valid content in at least one message
    const validMessages = messages.filter(msg => this.hasValidContent(msg));
    if (validMessages.length === 0) {
      errors.push({
        field: 'messages',
        code: 'NO_VALID_CONTENT',
        message: 'At least one message must have valid content (text, audio, or multimodal content)'
      });
    }
  }

  /**
   * Validate individual message
   */
  private validateMessage(
    message: LLMMessage,
    index: number,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    const fieldPrefix = `messages[${index}]`;

    // Validate role
    if (!message.role) {
      errors.push({
        field: `${fieldPrefix}.role`,
        code: 'MISSING_REQUIRED',
        message: 'Message role is required'
      });
    } else if (!['system', 'user', 'assistant'].includes(message.role)) {
      errors.push({
        field: `${fieldPrefix}.role`,
        code: 'INVALID_VALUE',
        message: 'Message role must be "system", "user", or "assistant"',
        value: message.role
      });
    }

    // Validate content
    if (!this.hasValidContent(message)) {
      errors.push({
        field: `${fieldPrefix}.content`,
        code: 'MISSING_CONTENT',
        message: 'Message must have content, input_audio, or valid multimodal content'
      });
    }

    // Validate multimodal content
    if (Array.isArray(message.content)) {
      message.content.forEach((part, partIndex) => {
        this.validateContentPart(part, `${fieldPrefix}.content[${partIndex}]`, errors, warnings);
      });
    }

    // Validate audio data
    if (message.input_audio) {
      this.validateAudioData(message.input_audio, `${fieldPrefix}.input_audio`, errors, warnings);
    }
  }

  /**
   * Check if message has valid content
   */
  private hasValidContent(message: LLMMessage): boolean {
    if (!message) return false;

    // Text content
    if (typeof message.content === 'string' && message.content.trim().length > 0) {
      return true;
    }

    // Multimodal content
    if (Array.isArray(message.content) && message.content.length > 0) {
      return message.content.some(part => {
        return (part.type === 'text' && part.text && part.text.trim().length > 0) ||
          (part.type === 'image_url' && part.image_url?.url) ||
          (part.type === 'audio_url' && part.audio_url?.url);
      });
    }

    // Audio content
    if (message.input_audio) {
      return true;
    }

    return false;
  }

  /**
   * Validate content part in multimodal message
   */
  private validateContentPart(
    part: any,
    fieldPath: string,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    if (!part.type) {
      errors.push({
        field: `${fieldPath}.type`,
        code: 'MISSING_REQUIRED',
        message: 'Content part type is required'
      });
      return;
    }

    switch (part.type) {
      case 'text':
        if (!part.text || typeof part.text !== 'string') {
          errors.push({
            field: `${fieldPath}.text`,
            code: 'MISSING_REQUIRED',
            message: 'Text content part must have text field'
          });
        }
        break;

      case 'image_url':
        if (!part.image_url?.url) {
          errors.push({
            field: `${fieldPath}.image_url.url`,
            code: 'MISSING_REQUIRED',
            message: 'Image content part must have image_url.url field'
          });
        } else if (!this.isValidUrl(part.image_url.url)) {
          warnings.push({
            field: `${fieldPath}.image_url.url`,
            code: 'INVALID_URL',
            message: 'Image URL may not be valid',
            value: part.image_url.url
          });
        }
        break;

      case 'audio_url':
        if (!part.audio_url?.url) {
          errors.push({
            field: `${fieldPath}.audio_url.url`,
            code: 'MISSING_REQUIRED',
            message: 'Audio content part must have audio_url.url field'
          });
        } else if (!this.isValidUrl(part.audio_url.url)) {
          warnings.push({
            field: `${fieldPath}.audio_url.url`,
            code: 'INVALID_URL',
            message: 'Audio URL may not be valid',
            value: part.audio_url.url
          });
        }
        break;

      default:
        warnings.push({
          field: `${fieldPath}.type`,
          code: 'UNKNOWN_TYPE',
          message: `Unknown content part type: ${part.type}`,
          value: part.type
        });
    }
  }

  /**
   * Validate audio data
   */
  private validateAudioData(
    audioData: any,
    fieldPath: string,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    if (typeof audioData !== 'string') {
      errors.push({
        field: fieldPath,
        code: 'INVALID_TYPE',
        message: 'Audio data must be a base64 encoded string',
        value: typeof audioData
      });
      return;
    }

    // Basic base64 validation
    if (!this.isValidBase64(audioData)) {
      errors.push({
        field: fieldPath,
        code: 'INVALID_BASE64',
        message: 'Audio data must be valid base64 encoding'
      });
    }

    // Size validation
    const sizeBytes = (audioData.length * 3) / 4; // Approximate base64 decoded size
    const maxSizeMB = 10;
    if (sizeBytes > maxSizeMB * 1024 * 1024) {
      warnings.push({
        field: fieldPath,
        code: 'LARGE_AUDIO',
        message: `Audio data is large (${Math.round(sizeBytes / 1024 / 1024)}MB). Consider chunking for better performance.`
      });
    }
  }

  /**
   * Validate modalities
   */
  private validateModalities(
    modalities: string[] | undefined,
    providerConfig: ProviderConfig,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    if (!modalities) {
      return; // Optional field
    }

    if (!Array.isArray(modalities)) {
      errors.push({
        field: 'modalities',
        code: 'INVALID_TYPE',
        message: 'Modalities must be an array of strings',
        value: typeof modalities
      });
      return;
    }

    const supportedModalities = providerConfig.supportedModalities;
    const unsupportedModalities = modalities.filter(m => !supportedModalities.includes(m));

    if (unsupportedModalities.length > 0) {
      errors.push({
        field: 'modalities',
        code: 'UNSUPPORTED_MODALITY',
        message: `Unsupported modalities: ${unsupportedModalities.join(', ')}. Supported: ${supportedModalities.join(', ')}`,
        value: unsupportedModalities
      });
    }
  }

  /**
   * Validate audio configuration
   */
  private validateAudioConfig(
    audioConfig: any,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    if (!audioConfig) {
      return; // Optional field
    }

    if (typeof audioConfig !== 'object') {
      errors.push({
        field: 'audioConfig',
        code: 'INVALID_TYPE',
        message: 'Audio config must be an object',
        value: typeof audioConfig
      });
      return;
    }

    // Validate voice
    if (audioConfig.voice && typeof audioConfig.voice !== 'string') {
      errors.push({
        field: 'audioConfig.voice',
        code: 'INVALID_TYPE',
        message: 'Voice must be a string',
        value: typeof audioConfig.voice
      });
    }

    // Validate format
    if (audioConfig.format && typeof audioConfig.format !== 'string') {
      errors.push({
        field: 'audioConfig.format',
        code: 'INVALID_TYPE',
        message: 'Audio format must be a string',
        value: typeof audioConfig.format
      });
    }

    // Provider-specific validation (Aliyun example)
    if (audioConfig.sampleRate && audioConfig.sampleRate !== ALIYUN_AUDIO_CONFIG.sampleRate) {
      warnings.push({
        field: 'audioConfig.sampleRate',
        code: 'NON_STANDARD_RATE',
        message: `Sample rate ${audioConfig.sampleRate} may not be optimal. Recommended: ${ALIYUN_AUDIO_CONFIG.sampleRate}Hz`,
        value: audioConfig.sampleRate
      });
    }
  }

  /**
   * Validate tools array
   */
  private validateTools(
    tools: LLMTool[] | undefined,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    if (!tools) {
      return; // Optional field
    }

    if (!Array.isArray(tools)) {
      errors.push({
        field: 'tools',
        code: 'INVALID_TYPE',
        message: 'Tools must be an array',
        value: typeof tools
      });
      return;
    }

    tools.forEach((tool, index) => {
      this.validateTool(tool, index, errors, warnings);
    });
  }

  /**
   * Validate individual tool
   */
  private validateTool(
    tool: LLMTool,
    index: number,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    const fieldPrefix = `tools[${index}]`;

    if (!tool.type) {
      errors.push({
        field: `${fieldPrefix}.type`,
        code: 'MISSING_REQUIRED',
        message: 'Tool type is required'
      });
    } else if (tool.type !== 'function') {
      errors.push({
        field: `${fieldPrefix}.type`,
        code: 'INVALID_VALUE',
        message: 'Tool type must be "function"',
        value: tool.type
      });
    }

    if (!tool.function) {
      errors.push({
        field: `${fieldPrefix}.function`,
        code: 'MISSING_REQUIRED',
        message: 'Tool function definition is required'
      });
      return;
    }

    if (!tool.function.name) {
      errors.push({
        field: `${fieldPrefix}.function.name`,
        code: 'MISSING_REQUIRED',
        message: 'Tool function name is required'
      });
    }

    if (!tool.function.parameters) {
      warnings.push({
        field: `${fieldPrefix}.function.parameters`,
        code: 'MISSING_RECOMMENDED',
        message: 'Tool function parameters schema is recommended for better results'
      });
    }
  }

  /**
   * Validate request parameters
   */
  private validateParameters(
    request: LLMRequest,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    // Temperature validation
    if (request.temperature !== undefined) {
      if (typeof request.temperature !== 'number') {
        errors.push({
          field: 'temperature',
          code: 'INVALID_TYPE',
          message: 'Temperature must be a number',
          value: typeof request.temperature
        });
      } else if (request.temperature < 0 || request.temperature > 2) {
        errors.push({
          field: 'temperature',
          code: 'OUT_OF_RANGE',
          message: 'Temperature must be between 0 and 2',
          value: request.temperature
        });
      }
    }

    // Max tokens validation
    if (request.max_tokens !== undefined) {
      if (typeof request.max_tokens !== 'number' || !Number.isInteger(request.max_tokens)) {
        errors.push({
          field: 'max_tokens',
          code: 'INVALID_TYPE',
          message: 'Max tokens must be a positive integer',
          value: request.max_tokens
        });
      } else if (request.max_tokens <= 0) {
        errors.push({
          field: 'max_tokens',
          code: 'OUT_OF_RANGE',
          message: 'Max tokens must be greater than 0',
          value: request.max_tokens
        });
      } else if (request.max_tokens > 4096) {
        warnings.push({
          field: 'max_tokens',
          code: 'HIGH_VALUE',
          message: 'High max_tokens value may result in slower responses',
          value: request.max_tokens
        });
      }
    }
  }

  /**
   * Utility: Check if string is valid URL
   */
  private isValidUrl(str: string): boolean {
    try {
      new URL(str);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Utility: Check if string is valid base64
   */
  private isValidBase64(str: string): boolean {
    try {
      return btoa(atob(str)) === str;
    } catch {
      return false;
    }
  }

  /**
   * Sanitize request for logging (remove sensitive data)
   */
  sanitizeRequestForLogging(request: LLMRequest): any {
    const sanitized = { ...request };

    // Redact audio data
    if (sanitized.messages) {
      sanitized.messages = sanitized.messages.map(msg => {
        const sanitizedMsg = { ...msg };
        if (sanitizedMsg.input_audio) {
          sanitizedMsg.input_audio = '[REDACTED]';
        }

        // Redact audio in multimodal content
        if (Array.isArray(sanitizedMsg.content)) {
          sanitizedMsg.content = sanitizedMsg.content.map(part => {
            if (part.type === 'audio_url' || part.input_audio) {
              return { ...part, input_audio: '[REDACTED]' };
            }
            return part;
          });
        }

        return sanitizedMsg;
      });
    }

    return sanitized;
  }
}

// Export singleton instance
export const llmValidationService = new LLMValidationService();
export default llmValidationService;