/**
 * Unified Authentication Service
 * Consolidates API key management from proxy.ts, llm.ts, and AliyunRealtimeProxy.ts
 */

import { getEnvVar } from '@/config/env.js';
import { LLMServiceError, LLMProviderConfig } from './types.js';

export class LLMAuthenticationService {
  private static instance: LLMAuthenticationService;
  private apiKeyCache = new Map<string, { key: string; timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  static getInstance(): LLMAuthenticationService {
    if (!LLMAuthenticationService.instance) {
      LLMAuthenticationService.instance = new LLMAuthenticationService();
    }
    return LLMAuthenticationService.instance;
  }

  /**
   * Get API key for provider with unified environment variable lookup
   * Consolidates all the different API key retrieval patterns
   */
  getApiKey(provider: string): string {
    const cacheKey = `${provider}_api_key`;
    const cached = this.apiKeyCache.get(cacheKey);
    
    // Return cached key if still valid
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.key;
    }

    let apiKey: string = '';

    switch (provider.toLowerCase()) {
      case 'aliyun':
        // Consolidate all Aliyun API key patterns from the existing files
        apiKey = getEnvVar('DASHSCOPE_API_KEY', '', false) || 
                 getEnvVar('ALIYUN_API_KEY', '', false) ||
                 process.env.VITE_DASHSCOPE_API_KEY ||
                 process.env.DASHSCOPE_API_KEY ||
                 '';
        break;
      
      case 'openai':
        apiKey = getEnvVar('OPENAI_API_KEY', '', false) || 
                 process.env.OPENAI_API_KEY ||
                 '';
        break;
      
      default:
        throw new LLMServiceError(
          `Unsupported provider: ${provider}`,
          'UNSUPPORTED_PROVIDER',
          400
        );
    }

    if (!apiKey || apiKey.trim() === '') {
      throw new LLMServiceError(
        `API key not configured for provider: ${provider}`,
        'MISSING_API_KEY',
        401,
        {
          provider,
          requiredEnvVars: this.getRequiredEnvVars(provider)
        }
      );
    }

    // Cache the key
    this.apiKeyCache.set(cacheKey, {
      key: apiKey,
      timestamp: Date.now()
    });

    return apiKey;
  }

  /**
   * Validate API key format for provider
   */
  validateApiKey(provider: string, apiKey: string): boolean {
    if (!apiKey || apiKey.trim() === '') {
      return false;
    }

    switch (provider.toLowerCase()) {
      case 'aliyun':
        // Aliyun API keys typically start with 'sk-' and are longer than 20 chars
        return apiKey.startsWith('sk-') && apiKey.length > 20;
      
      case 'openai':
        // OpenAI API keys start with 'sk-' and have specific length
        return apiKey.startsWith('sk-') && apiKey.length >= 40;
      
      default:
        // Generic validation - just check it's not empty
        return apiKey.length > 0;
    }
  }

  /**
   * Create provider configuration with authentication
   */
  createProviderConfig(provider: string, overrides: Partial<LLMProviderConfig> = {}): LLMProviderConfig {
    const apiKey = this.getApiKey(provider);
    
    if (!this.validateApiKey(provider, apiKey)) {
      throw new LLMServiceError(
        `Invalid API key format for provider: ${provider}`,
        'INVALID_API_KEY',
        401
      );
    }

    const defaultConfig = this.getDefaultConfig(provider);
    
    return {
      ...defaultConfig,
      ...overrides,
      apiKey
    };
  }

  /**
   * Get default configuration for provider
   * SINGLE SOURCE OF TRUTH for all Aliyun endpoint configurations
   */
  private getDefaultConfig(provider: string): Partial<LLMProviderConfig> {
    switch (provider.toLowerCase()) {
      case 'aliyun':
        return this.getAliyunEndpointConfig();
      
      case 'openai':
        return {
          endpoint: 'https://api.openai.com/v1/chat/completions',
          timeout: 30000,
          retryConfig: {
            maxRetries: 3,
            initialDelay: 1000,
            maxDelay: 10000,
            backoffFactor: 2,
            retryableErrors: ['network', 'timeout', 'rate_limit', 'server_error'],
            exponentialBackoff: true,
            baseDelayMs: 1000,
            maxDelayMs: 10000
          }
        };
      
      default:
        return {
          timeout: 30000,
          retryConfig: {
            maxRetries: 3,
            initialDelay: 1000,
            maxDelay: 10000,
            backoffFactor: 2,
            retryableErrors: ['network', 'timeout', 'rate_limit', 'server_error'],
            exponentialBackoff: true,
            baseDelayMs: 1000,
            maxDelayMs: 10000
          }
        };
    }
  }

  /**
   * CENTRALIZED Aliyun endpoint configuration
   * All Aliyun integrations should use this method
   */
  private getAliyunEndpointConfig(): Partial<LLMProviderConfig> {
    // Determine if we should use proxy or direct API
    const shouldUseProxy = this.shouldUseAliyunProxy();
    
    let endpoint: string;
    
    if (shouldUseProxy) {
      // Use local proxy server for unified handling
      try {
        const { getDownloadServerUrl } = require('../../utils/portManager.js');
        endpoint = `${getDownloadServerUrl()}/api/llm/chat/completions`;
      } catch {
        // Fallback to default proxy server
        endpoint = 'http://localhost:2994/api/llm/chat/completions';
      }
    } else {
      // Use direct Aliyun API (for server-side contexts with proper API keys)
      endpoint = 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions';
    }
    
    return {
      endpoint,
      baseURL: shouldUseProxy ? endpoint : 'https://dashscope.aliyuncs.com/compatible-mode/v1',
      timeout: 30000,
      retryConfig: {
        maxRetries: 3,
        initialDelay: 1000,
        maxDelay: 10000,
        backoffFactor: 2,
        retryableErrors: ['network', 'timeout', 'rate_limit', 'server_error'],
        exponentialBackoff: true,
        baseDelayMs: 1000,
        maxDelayMs: 10000
      }
    };
  }

  /**
   * Determine whether to use proxy or direct API based on context
   */
  private shouldUseAliyunProxy(): boolean {
    // Always use proxy for now to maintain consistency
    // This can be made configurable later if needed
    return true;
    
    // Future logic could be:
    // - Use proxy in browser contexts
    // - Use proxy when API key is not available
    // - Use direct API in server contexts with valid API keys
    // return typeof window !== 'undefined' || !this.hasValidAliyunApiKey();
  }

  /**
   * Get required environment variables for provider
   */
  private getRequiredEnvVars(provider: string): string[] {
    switch (provider.toLowerCase()) {
      case 'aliyun':
        return ['DASHSCOPE_API_KEY', 'ALIYUN_API_KEY', 'VITE_DASHSCOPE_API_KEY'];
      case 'openai':
        return ['OPENAI_API_KEY'];
      default:
        return [];
    }
  }

  /**
   * Clear API key cache for provider
   */
  clearCache(provider?: string): void {
    if (provider) {
      this.apiKeyCache.delete(`${provider}_api_key`);
    } else {
      this.apiKeyCache.clear();
    }
  }

  /**
   * Get authentication headers for provider
   */
  getAuthHeaders(provider: string): Record<string, string> {
    const apiKey = this.getApiKey(provider);
    
    switch (provider.toLowerCase()) {
      case 'aliyun':
        return {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        };
      
      case 'openai':
        return {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        };
      
      default:
        return {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        };
    }
  }
}

// Export singleton instance
export const authService = LLMAuthenticationService.getInstance();