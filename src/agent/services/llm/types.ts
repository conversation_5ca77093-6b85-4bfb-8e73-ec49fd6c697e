/**
 * Unified LLM Service Types
 * Central type definitions for all LLM service interactions
 */

export interface LLMRequest {
  provider: string;
  model: string;
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string | Array<any>;
    input_audio?: string;
  }>;
  modalities?: string[];
  audioConfig?: {
    voice?: string;
    format?: string;
  };
  tools?: Array<{
    name: string;
    description?: string;
    schema: any;
  }>;
  tool_choice?: string;
  temperature?: number;
  max_tokens?: number;
  stream?: boolean;
  [key: string]: any;
}

export interface LLMResponse {
  content: string;
  audio?: string;
  tool_calls?: Array<{
    id: string;
    type: string;
    function: {
      name: string;
      arguments: string;
    };
  }>;
  error?: string | {
    type: string;
    code: string;
    message: string;
    timestamp: string;
    requestId: string;
    retryable: boolean;
  };
  usage?: {
    prompt_tokens?: number;
    completion_tokens?: number;
    total_tokens?: number;
  };
  metadata: ResponseMetadata;
}

export interface ResponseMetadata {
  model: string;
  provider: string;
  modalities: string[];
  requestId: string;
  timestamp: string;
  messageCount?: number;
  originalMessageCount?: number;
  audioConfig?: any;
  processingTimeMs?: number;
  usage?: {
    prompt_tokens?: number;
    completion_tokens?: number;
    total_tokens?: number;
  };
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface LLMProviderConfig {
  apiKey: string;
  endpoint?: string;
  baseURL?: string;
  timeout?: number;
  retryConfig?: RetryConfig;
}

export interface ProviderConfig {
  name: string;
  endpoint: string;
  apiKeyEnvVar: string;
  defaultModel: string;
  supportedModalities: string[];
  supportedVoices: string[];
  rateLimits: {
    requestsPerSecond: number;
    tokensPerSecond: number;
    audioChunksPerSecond: number;
    imagesPerSecond: number;
    burstAllowance: number;
  };
  errorMapping?: {
    [key: string]: string;
  };
}

export interface StreamChunk {
  content?: string;
  audio?: string;
  tool_calls?: Array<{
    id: string;
    type: string;
    function: {
      name: string;
      arguments: string;
    };
  }>;
  metadata?: ResponseMetadata;
  done?: boolean;
  type?: 'error' | 'text' | 'audio' | 'tool_call' | 'usage' | 'done';
  data?: any;
}

// Retry configuration interface
export interface RetryConfig {
  maxRetries: number;
  initialDelay: number;
  maxDelay: number;
  backoffFactor: number;
  retryableErrors: string[];
  exponentialBackoff: boolean;
  baseDelayMs: number;
  maxDelayMs: number;
}

export interface LLMProvider {
  name: string;
  validateRequest(request: LLMRequest): ValidationResult;
  formatRequest(request: LLMRequest): any;
  invoke(request: LLMRequest, config: LLMProviderConfig): Promise<LLMResponse>;
  stream?(request: LLMRequest, config: LLMProviderConfig): AsyncGenerator<any, void, unknown>;
  healthCheck(config: LLMProviderConfig): Promise<boolean>;
}

export interface ServiceMetrics {
  requestCount: number;
  errorCount: number;
  totalLatency: number;
  averageLatency: number;
  retryCount: number;
  lastHealthCheck: Date;
}

export class LLMServiceError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500,
    public details?: any,
    public requestId?: string
  ) {
    super(message);
    this.name = 'LLMServiceError';
  }
}

// Alias for backward compatibility
export const LLMError = LLMServiceError;

// Additional type definitions for LLM services
export interface LLMMessage {
  role: 'system' | 'user' | 'assistant';
  content: string | Array<any>;
  input_audio?: string;
}

export interface LLMTool {
  name: string;
  description?: string;
  schema: any;
  type?: string;
  function?: {
    name: string;
    description?: string;
    parameters?: any;
  };
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
  value?: any;
}

export interface ValidationWarning {
  field: string;
  message: string;
  code: string;
  value?: any;
}

// LLM Error structure interface
export interface LLMErrorInterface {
  type: string;
  code: string;
  message: string;
  timestamp: string;
  requestId: string;
  retryable: boolean;
  details?: any;
  statusCode?: number;
  retryAfter?: number;
}