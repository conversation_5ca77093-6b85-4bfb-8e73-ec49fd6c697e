/**
 * Unified LLM Service
 * Single ground truth for all LLM interactions, eliminating duplication
 * between proxy.ts, llm.ts, and AliyunRealtimeProxy.ts
 */

// Use native fetch in browser, node-fetch in Node.js
const fetchImpl = (() => {
  if (typeof window !== 'undefined' && window.fetch) {
    // Browser environment - use native fetch
    return window.fetch.bind(window);
  } else {
    // Node.js environment - use dynamic import to prevent bundling issues
    return async (url: string | URL, init?: RequestInit) => {
      const fetch = (await import('node-fetch')).default;
      return fetch(url as any, init as any) as any;
    };
  }
})();
import {
  LLMRequest,
  LLMResponse,
  LLMProvider,
  LLMProviderConfig,
  ServiceMetrics,
  LLMServiceError,
  ValidationResult
} from './types.js';
import { generateRequestId } from './utils.js';
import { LLMValidationService } from './validation.js';
import { authService } from './authentication.js';

export class UnifiedLLMService {
  private static instance: UnifiedLLMService;
  private providers = new Map<string, LLMProvider>();
  private metrics = new Map<string, ServiceMetrics>();
  private validationService: LLMValidationService;

  static getInstance(): UnifiedLLMService {
    if (!UnifiedLLMService.instance) {
      UnifiedLLMService.instance = new UnifiedLLMService();
    }
    return UnifiedLLMService.instance;
  }

  constructor() {
    this.validationService = new LLMValidationService();
    // Register default providers
    this.registerProvider(new AliyunProvider());
  }

  /**
   * Register a new LLM provider
   */
  registerProvider(provider: LLMProvider): void {
    this.providers.set(provider.name.toLowerCase(), provider);
    this.metrics.set(provider.name.toLowerCase(), {
      requestCount: 0,
      errorCount: 0,
      totalLatency: 0,
      averageLatency: 0,
      retryCount: 0,
      lastHealthCheck: new Date()
    });
  }

  /**
   * Main invoke method - consolidates all LLM API calls
   * Replaces duplicate logic from proxy.ts and llm.ts
   */
  async invoke(request: LLMRequest): Promise<LLMResponse> {
    const startTime = Date.now();
    const requestId = this.generateRequestId();

    try {
      // Validate request
      const validation = this.validateRequest(request);
      if (!validation.isValid) {
        throw new LLMServiceError(
          'Request validation failed',
          'VALIDATION_ERROR',
          400,
          { errors: validation.errors },
          requestId
        );
      }

      // Get provider
      const provider = this.getProvider(request.provider);

      // Create provider config with authentication
      const config = authService.createProviderConfig(request.provider);

      // Increment request metrics
      this.incrementMetric(request.provider, 'requestCount');

      // Invoke provider with retry logic
      const response = await this.invokeWithRetry(provider, request, config);

      // Add metadata
      response.metadata = {
        ...response.metadata,
        requestId,
        timestamp: new Date().toISOString()
      };

      // Update latency metrics
      const latency = Date.now() - startTime;
      this.updateLatencyMetric(request.provider, latency);

      return response;

    } catch (error) {
      this.incrementMetric(request.provider, 'errorCount');

      if (error instanceof LLMServiceError) {
        error.requestId = requestId;
        throw error;
      }

      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      throw new LLMServiceError(
        errorMessage,
        'INTERNAL_ERROR',
        500,
        { originalError: errorMessage },
        requestId
      );
    }
  }

  /**
   * Stream invoke method for streaming responses
   */
  async stream(request: LLMRequest): Promise<ReadableStream> {
    const requestId = generateRequestId();
    const startTime = Date.now();

    try {
      // Get provider instance first
      const provider = this.getProvider(request.provider);

      // Validate request with default config
      const validation = await this.validationService.validateRequest(request, {
        name: request.provider,
        endpoint: '',
        apiKeyEnvVar: '',
        defaultModel: '',
        supportedModalities: [],
        supportedVoices: [],
        rateLimits: {
          requestsPerSecond: 10,
          tokensPerSecond: 1000,
          audioChunksPerSecond: 5,
          imagesPerSecond: 2,
          burstAllowance: 5
        }
      });
      if (!validation.isValid) {
        throw new LLMServiceError(
          `Invalid request: ${validation.errors.join(', ')}`,
          'INVALID_REQUEST',
          400,
          requestId
        );
      }

      // Create provider config with authentication
      const config = authService.createProviderConfig(request.provider);

      // Increment request metrics
      this.incrementMetric(request.provider, 'requestCount');

      // Store reference to service instance for use in stream controller
      const service = this;

      // Create ReadableStream for streaming response
      const stream = new ReadableStream({
        async start(controller) {
          try {
            // Check if provider supports streaming
            if (!provider.stream || typeof provider.stream !== 'function') {
              throw new Error(`Provider ${request.provider} does not support streaming`);
            }

            // Get provider config
            const config = authService.createProviderConfig(request.provider);

            // Use provider's streaming method
            const streamGenerator = provider.stream({ ...request, stream: true }, config);

            for await (const chunk of streamGenerator) {
              const streamData = JSON.stringify({
                ...chunk,
                metadata: {
                  requestId,
                  timestamp: new Date().toISOString(),
                  provider: request.provider
                }
              }) + '\n';

              controller.enqueue(new TextEncoder().encode(streamData));
            }

            controller.close();

            // Update latency metrics
            const latency = Date.now() - startTime;
            service.updateLatencyMetric(request.provider, latency);

          } catch (error) {
            service.incrementMetric(request.provider, 'errorCount');

            const errorResponse = {
              error: {
                message: (error as Error).message,
                type: 'STREAMING_ERROR',
                code: 500,
                requestId
              }
            };

            controller.enqueue(
              new TextEncoder().encode(JSON.stringify(errorResponse) + '\n')
            );
            controller.close();
          }
        }
      });

      return stream;

    } catch (error) {
      this.incrementMetric(request.provider, 'errorCount');

      if (error instanceof LLMServiceError) {
        error.requestId = requestId;
        throw error;
      }

      throw new LLMServiceError(
        `Streaming failed: ${(error as Error).message}`,
        'STREAMING_ERROR',
        500,
        requestId
      );
    }
  }

  /**
   * Health check for all providers
   */
  async healthCheck(provider?: string): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};

    if (provider) {
      const providerInstance = this.getProvider(provider);
      const config = authService.createProviderConfig(provider);
      results[provider] = await providerInstance.healthCheck(config);
    } else {
      for (const [name, providerInstance] of this.providers) {
        try {
          const config = authService.createProviderConfig(name);
          results[name] = await providerInstance.healthCheck(config);
        } catch (error) {
          results[name] = false;
        }
      }
    }

    return results;
  }

  /**
   * Get service metrics
   */
  getMetrics(provider?: string): Record<string, ServiceMetrics> {
    if (provider) {
      const metrics = this.metrics.get(provider.toLowerCase());
      return metrics ? { [provider]: metrics } : {};
    }

    return Object.fromEntries(this.metrics);
  }

  /**
   * Consolidated request validation
   * Replaces duplicate validation logic from llm.ts
   */
  private validateRequest(request: LLMRequest): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields
    if (!request.provider) {
      errors.push('Missing required parameter: provider');
    }

    if (!request.model) {
      errors.push('Missing required parameter: model');
    }

    if (!request.messages) {
      errors.push('Missing required parameter: messages');
    } else if (!Array.isArray(request.messages)) {
      errors.push('Invalid messages parameter: must be an array');
    } else if (request.messages.length === 0) {
      errors.push('Empty messages array');
    } else {
      // Validate individual messages
      const validMessages = request.messages.filter(msg => {
        return msg && (
          (typeof msg.content === 'string' && msg.content.trim().length > 0) ||
          (Array.isArray(msg.content) && msg.content.length > 0) ||
          msg.input_audio
        );
      });

      if (validMessages.length === 0) {
        errors.push('No valid messages found - all messages must have non-empty content or input_audio');
      }
    }

    // Provider-specific validation
    if (request.provider) {
      const provider = this.providers.get(request.provider.toLowerCase());
      if (!provider) {
        errors.push(`Unsupported provider: ${request.provider}`);
      } else {
        const providerValidation = provider.validateRequest(request);
        errors.push(...providerValidation.errors);
        warnings.push(...(providerValidation.warnings || []));
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Get provider instance
   */
  private getProvider(providerName: string): LLMProvider {
    const provider = this.providers.get(providerName.toLowerCase());
    if (!provider) {
      throw new LLMServiceError(
        `Provider not found: ${providerName}`,
        'PROVIDER_NOT_FOUND',
        400
      );
    }
    return provider;
  }

  /**
   * Invoke with retry logic
   */
  private async invokeWithRetry(
    provider: LLMProvider,
    request: LLMRequest,
    config: LLMProviderConfig
  ): Promise<LLMResponse> {
    const retryConfig = config.retryConfig!;
    let lastError: Error | unknown;

    for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
      try {
        return await provider.invoke(request, config);

      } catch (error) {
        lastError = error;

        // Don't retry on certain errors
        if (error instanceof LLMServiceError &&
          ['VALIDATION_ERROR', 'MISSING_API_KEY', 'INVALID_API_KEY'].includes(error.code)) {
          throw error;
        }

        // Don't retry on last attempt
        if (attempt === retryConfig.maxRetries) {
          break;
        }

        // Calculate delay
        const delay = Math.min(
          retryConfig.initialDelay * Math.pow(retryConfig.backoffFactor, attempt),
          retryConfig.maxDelay
        );

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, delay));
        this.incrementMetric(request.provider, 'retryCount');
      }
    }

    if (lastError instanceof Error) {
      throw lastError;
    } else {
      throw new Error(lastError ? String(lastError) : 'Unknown error');
    };
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Update metrics
   */
  private incrementMetric(provider: string, metric: keyof ServiceMetrics): void {
    if (!provider) {
      console.warn('[LLM Service] incrementMetric called with undefined provider');
      return;
    }
    const metrics = this.metrics.get(provider.toLowerCase());
    if (metrics && typeof metrics[metric] === 'number') {
      (metrics[metric] as number)++;
    }
  }

  private updateLatencyMetric(provider: string, latency: number): void {
    if (!provider) {
      console.warn('[LLM Service] updateLatencyMetric called with undefined provider');
      return;
    }
    const metrics = this.metrics.get(provider.toLowerCase());
    if (metrics) {
      metrics.totalLatency += latency;
      metrics.averageLatency = metrics.totalLatency / metrics.requestCount;
    }
  }
}

/**
 * Aliyun Provider Implementation
 * Consolidates logic from llm.ts and proxy.ts
 */
class AliyunProvider implements LLMProvider {
  name = 'aliyun';

  validateRequest(request: LLMRequest): ValidationResult {
    const errors: string[] = [];

    // Aliyun-specific validation
    if (request.model && !this.isValidAliyunModel(request.model)) {
      errors.push(`Invalid Aliyun model: ${request.model}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings: []
    };
  }

  formatRequest(request: LLMRequest): any {
    // Convert tools to OpenAI format if needed
    const tools = request.tools ? this.convertToolsToOpenAIFormat(request.tools) : undefined;

    return {
      model: request.model,
      messages: request.messages,
      ...(request.modalities && { modalities: request.modalities }),
      ...(request.audioConfig && { audio_config: request.audioConfig }),
      ...(tools && { tools, tool_choice: request.tool_choice || 'auto' }),
      ...(request.temperature !== undefined && { temperature: request.temperature }),
      ...(request.max_tokens !== undefined && { max_tokens: request.max_tokens }),
      ...(request.stream !== undefined && { stream: request.stream })
    };
  }

  async invoke(request: LLMRequest, config: LLMProviderConfig): Promise<LLMResponse> {
    const formattedRequest = this.formatRequest(request);
    const headers = authService.getAuthHeaders('aliyun');

    try {
      // Determine safe endpoint depending on environment
      const endpoint = (() => {
        const configured = config.endpoint || '';
        const isNodeEnv = typeof window === 'undefined';
        if (isNodeEnv && /^(http|https):\/\/(localhost|127\.0\.0\.1)/.test(configured)) {
          // In Node, avoid browser proxy endpoints that point to localhost download server
          return 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions';
        }
        return configured || 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions';
      })();

      const response = await fetchImpl(endpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(formattedRequest)
        // Note: timeout is not supported in native fetch, would need AbortController for that
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new LLMServiceError(
          `Aliyun API error: ${response.status} ${response.statusText}`,
          'API_ERROR',
          response.status,
          { responseBody: errorText }
        );
      }

      const data = await response.json() as any;

      return {
        content: data.choices?.[0]?.message?.content || '',
        audio: data.choices?.[0]?.message?.audio || null,
        usage: data.usage,
        metadata: {
          model: request.model,
          provider: 'aliyun',
          modalities: request.modalities || ['text'],
          requestId: '',  // Will be set by service
          timestamp: ''   // Will be set by service
        }
      };

    } catch (error) {
      if (error instanceof LLMServiceError) {
        throw error;
      }

      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      throw new LLMServiceError(
        `Failed to invoke Aliyun API: ${errorMessage}`,
        'NETWORK_ERROR',
        502,
        { originalError: errorMessage }
      );
    }
  }

  async* stream(request: LLMRequest, config: LLMProviderConfig): AsyncGenerator<any, void, unknown> {
    // Create a streaming request
    const streamRequest = { ...request, stream: true };
    const formattedRequest = this.formatRequest(streamRequest);
    const headers = authService.getAuthHeaders('aliyun');

    try {
      // Determine safe endpoint depending on environment
      const endpoint = (() => {
        const configured = config.endpoint || '';
        const isNodeEnv = typeof window === 'undefined';
        if (isNodeEnv && /^(http|https):\/\/(localhost|127\.0\.0\.1)/.test(configured)) {
          // In Node, avoid browser proxy endpoints that point to localhost download server
          return 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions';
        }
        return configured || 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions';
      })();

      const response = await fetchImpl(endpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(formattedRequest)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new LLMServiceError(
          `Aliyun API error: ${response.status} ${response.statusText}`,
          'API_ERROR',
          response.status,
          { responseBody: errorText }
        );
      }

      // Handle streaming response
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.trim() === '') continue;
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') return;

              try {
                const parsed = JSON.parse(data);
                yield {
                  content: parsed.choices?.[0]?.delta?.content || '',
                  audio: parsed.choices?.[0]?.delta?.audio || null,
                  metadata: {
                    model: request.model,
                    provider: 'aliyun',
                    modalities: request.modalities || ['text']
                  }
                };
              } catch (parseError) {
                console.warn('Failed to parse streaming chunk:', parseError);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

    } catch (error) {
      if (error instanceof LLMServiceError) {
        throw error;
      }

      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      throw new LLMServiceError(
        `Failed to stream from Aliyun API: ${errorMessage}`,
        'NETWORK_ERROR',
        502,
        { originalError: errorMessage }
      );
    }
  }

  async healthCheck(config: LLMProviderConfig): Promise<boolean> {
    try {
      // Use dedicated health endpoint instead of full LLM request for faster checks
      const healthEndpoint = config.baseURL ?
        `${config.baseURL}/health?provider=aliyun` :
        'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation';

      if (config.baseURL && config.baseURL.includes('localhost')) {
        // Browser/proxy context - use health endpoint
        const response = await fetchImpl(healthEndpoint, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          return false;
        }

        const result = await response.json();
        return result.status === 'ok' && result.providers?.aliyun === true;
      } else {
        // Direct API context - minimal test request
        const testRequest: LLMRequest = {
          provider: 'aliyun',
          model: 'qwen-turbo',
          messages: [{ role: 'user', content: 'ping' }]
        };

        await this.invoke(testRequest, config);
        return true;
      }

    } catch (error) {
      return false;
    }
  }

  private isValidAliyunModel(model: string): boolean {
    const validModels = [
      'qwen-turbo', 'qwen-plus', 'qwen-max',
      'qwen-omni-turbo', 'qwen-omni-turbo-realtime'
    ];
    return validModels.includes(model);
  }

  private convertToolsToOpenAIFormat(tools: any[]): any[] {
    return tools.map(tool => {
      if (tool.type === 'function' && tool.function) {
        return tool;
      }

      if (tool.name && tool.schema) {
        return {
          type: 'function',
          function: {
            name: tool.name,
            description: tool.description || '',
            parameters: tool.schema
          }
        };
      }

      return tool;
    });
  }
}

// Export singleton instance
export const unifiedLLMService = UnifiedLLMService.getInstance();