/**
 * Aliyun LLM Provider Implementation
 * 
 * Concrete implementation of LLMProvider for Aliyun DashScope
 * Consolidates logic from AliyunBailianChatModel and related components
 */

import { createLogger } from '@/utils/logger';
import { generateRequestId } from '../utils.js';
import { HumanMessage, AIMessage, SystemMessage } from '@langchain/core/messages';
import { ALIYUN_AUDIO_CONFIG, buildValidSessionConfig } from '@/agent/models/aliyun/AliyunConfig.js';
import { authService } from '../authentication.js';
import { llmValidationService } from '../validation.js';
import { llmErrorService } from '../errors.js';
import type {
  LLMProvider,
  LLMRequest,
  LLMResponse,
  LLMProviderConfig,
  ValidationResult,
  ProviderConfig,
  ResponseMetadata,
  StreamChunk,
  LLMServiceError
} from '../types.js';
import { LLMServiceError as LLMError } from '../types.js';

export class AliyunLLMProvider implements LLMProvider {
  name = 'aliyun';
  config: ProviderConfig;
  private logger: any;
  private initialized = false;

  constructor() {
    this.logger = createLogger('AliyunLLMProvider');
    this.config = this.createProviderConfig();
  }

  /**
   * Create Aliyun-specific provider configuration
   */
  private createProviderConfig(): ProviderConfig {
    return {
      name: 'aliyun',
      endpoint: 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions',
      apiKeyEnvVar: 'VITE_DASHSCOPE_API_KEY',
      defaultModel: 'qwen-omni-turbo-realtime',
      supportedModalities: ['text', 'audio', 'image'],
      supportedVoices: ['Chelsie', 'Serena', 'Ethan', 'Cherry'],
      rateLimits: {
        requestsPerSecond: 10,
        tokensPerSecond: 1000,
        audioChunksPerSecond: 5,
        imagesPerSecond: 2,
        burstAllowance: 5
      },
      errorMapping: {
        'insufficient_quota': 'rate_limit',
        'invalid_api_key': 'authentication',
        'model_not_found': 'validation',
        'content_filter': 'validation',
        'server_error': 'provider',
        'BadRequestError': 'validation',
        'UnauthorizedError': 'authentication',
        'ForbiddenError': 'authentication',
        'TooManyRequestsError': 'rate_limit',
        'InternalServerError': 'provider',
        'ServiceUnavailableError': 'provider'
      }
    };
  }

  /**
   * Initialize the provider
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    this.logger.info('Initializing Aliyun LLM Provider...');

    // Validate API key
    const apiKey = authService.getApiKey(this.name);
    const isValid = authService.validateApiKey(this.name, apiKey);
    if (!isValid) {
      throw new Error(`Aliyun provider initialization failed: Invalid API key format`);
    }

    this.initialized = true;
    this.logger.info('Aliyun LLM Provider initialized successfully');
  }

  /**
   * Shutdown the provider
   */
  async shutdown(): Promise<void> {
    this.logger.info('Shutting down Aliyun LLM Provider...');
    authService.clearCache(this.name);
    this.initialized = false;
  }

  /**
   * Validate request for Aliyun specifics
   */
  validateRequest(request: LLMRequest): ValidationResult {
    return llmValidationService.validateRequest(request, this.config);
  }

  /**
   * Format request for Aliyun API
   */
  formatRequest(request: LLMRequest): any {
    const aliyunRequest: any = {
      model: request.model,
      messages: this.formatMessages(request.messages),
      stream: request.stream || false
    };

    // Add optional parameters
    if (request.temperature !== undefined) {
      aliyunRequest.temperature = request.temperature;
    }

    if (request.max_tokens !== undefined) {
      aliyunRequest.max_tokens = request.max_tokens;
    }

    // Handle multimodal requests
    if (request.modalities?.includes('audio')) {
      aliyunRequest.modalities = request.modalities;
      aliyunRequest.audio_config = this.formatAudioConfig(request.audioConfig);
    }

    // Handle tools
    if (request.tools && request.tools.length > 0) {
      aliyunRequest.tools = request.tools;
      aliyunRequest.tool_choice = request.tool_choice || 'auto';
    }

    // Handle streaming options
    if (request.stream && request.stream_options) {
      aliyunRequest.stream_options = request.stream_options;
    }

    return aliyunRequest;
  }

  /**
   * Format messages for Aliyun API
   */
  private formatMessages(messages: any[]): any[] {
    return messages.map(msg => {
      // Handle LangChain message types
      let role = 'user';
      let content: string | any[] = '';

      if (msg.constructor?.name === 'HumanMessage' || msg._getType?.() === 'human') {
        role = 'user';
      } else if (msg.constructor?.name === 'AIMessage' || msg._getType?.() === 'ai') {
        role = 'assistant';
      } else if (msg.constructor?.name === 'SystemMessage' || msg._getType?.() === 'system') {
        role = 'system';
      } else if (msg.role) {
        role = msg.role;
      }

      // Handle content
      if (typeof msg.content === 'string') {
        content = msg.content;
      } else if (Array.isArray(msg.content)) {
        content = msg.content;
      } else {
        content = msg.content || '';
      }

      const formattedMsg: any = { role, content };

      // Preserve audio data
      if (msg.input_audio) {
        formattedMsg.input_audio = msg.input_audio;
      }

      // Handle multimodal content from extraModalities
      if (msg.extraModalities) {
        if (msg.extraModalities.images && Array.isArray(content)) {
          msg.extraModalities.images.forEach((imageUrl: string) => {
            content.push({
              type: 'image_url',
              image_url: { url: imageUrl }
            });
          });
        }

        if (msg.extraModalities.audios && Array.isArray(content)) {
          msg.extraModalities.audios.forEach((audioUrl: string) => {
            content.push({
              type: 'audio_url',
              audio_url: { url: audioUrl }
            });
          });
        }
      }

      return formattedMsg;
    });
  }

  /**
   * Format audio configuration for Aliyun
   */
  private formatAudioConfig(audioConfig: any): any {
    const defaultConfig = {
      voice: ALIYUN_AUDIO_CONFIG.defaultVoice,
      format: ALIYUN_AUDIO_CONFIG.outputFormat,
      sample_rate: ALIYUN_AUDIO_CONFIG.sampleRate,
      channels: ALIYUN_AUDIO_CONFIG.numChannels
    };

    if (!audioConfig) {
      return defaultConfig;
    }

    return {
      voice: audioConfig.voice || defaultConfig.voice,
      format: audioConfig.format || defaultConfig.format,
      sample_rate: audioConfig.sampleRate || defaultConfig.sample_rate,
      channels: audioConfig.channels || defaultConfig.channels
    };
  }

  /**
   * Main invoke method
   */
  async invoke(request: LLMRequest): Promise<LLMResponse> {
    const requestId = generateRequestId();
    const startTime = Date.now();

    this.logger.debug('Aliyun invoke called', {
      requestId,
      model: request.model,
      messagesCount: request.messages.length,
      hasAudio: request.modalities?.includes('audio'),
      hasTools: request.tools && request.tools.length > 0
    });

    try {
      // Ensure provider is initialized
      await this.initialize();

      // Validate request
      const validation = this.validateRequest(request);
      if (!validation.isValid) {
        throw new LLMError(
          validation.errors.join('; '),
          'VALIDATION_FAILED',
          400,
          { validation },
          requestId
        );
      }

      // Get authenticated API key
      const apiKey = authService.getApiKey(this.name);
      const isValid = authService.validateApiKey(this.name, apiKey);
      if (!isValid) {
        throw new LLMError(
          'Invalid API key format',
          'AUTH_FAILED',
          401,
          {},
          requestId
        );
      }

      // Format request
      const aliyunRequest = this.formatRequest(request);

      // Make API call
      const response = await this.makeApiCall(aliyunRequest, apiKey!, requestId);

      // Format response
      const metadata: ResponseMetadata = {
        model: request.model,
        provider: this.name,
        modalities: request.modalities || ['text'],
        audioConfig: request.audioConfig,
        messageCount: request.messages.length,
        originalMessageCount: request.messages.length,
        processingTimeMs: Date.now() - startTime,
        requestId,
        timestamp: new Date().toISOString()
      };

      return this.formatResponse(response, metadata);

    } catch (error) {
      const context = {
        requestId,
        provider: this.name,
        model: request.model,
        attempt: 1,
        timestamp: new Date().toISOString(),
        originalError: error
      };

      const llmError = llmErrorService.mapError(error, context, this.config);
      llmErrorService.logError(llmError);

      // Return error response instead of throwing
      return {
        content: '',  // Required field for error responses
        error: {
          type: 'provider_error',
          code: llmError.code,
          message: llmError.message,
          timestamp: new Date().toISOString(),
          requestId,
          retryable: false
        },
        metadata: {
          model: request.model,
          provider: this.name,
          modalities: request.modalities || ['text'],
          messageCount: request.messages.length,
          originalMessageCount: request.messages.length,
          processingTimeMs: Date.now() - startTime,
          requestId,
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Make actual API call to Aliyun
   */
  private async makeApiCall(request: any, apiKey: string, requestId: string): Promise<any> {
    const headers = authService.getAuthHeaders(this.name);
    headers['Content-Type'] = 'application/json';
    headers['X-Request-ID'] = requestId;

    this.logger.debug('Making Aliyun API call', {
      endpoint: this.config.endpoint,
      model: request.model,
      stream: request.stream
    });

    const response = await fetch(this.config.endpoint, {
      method: 'POST',
      headers,
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      const errorText = await response.text();
      let errorData;

      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { message: errorText };
      }

      const error = new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      (error as any).response = {
        status: response.status,
        statusText: response.statusText,
        data: errorData,
        headers: response.headers
      };
      throw error;
    }

    return await response.json();
  }

  /**
   * Format response from Aliyun API
   */
  formatResponse(response: any, metadata: ResponseMetadata): LLMResponse {
    const choice = response.choices?.[0];
    if (!choice) {
      return {
        content: '',
        metadata,
        error: {
          type: 'provider',
          code: 'NO_CHOICES',
          message: 'No response choices returned from Aliyun API',
          timestamp: new Date().toISOString(),
          requestId: metadata.requestId,
          retryable: false
        }
      };
    }

    const result: LLMResponse = {
      content: choice.message?.content || '',
      metadata: {
        ...metadata,
        usage: response.usage
      }
    };

    // Handle audio response
    if (choice.message?.audio) {
      result.audio = choice.message.audio;
    }

    // Handle tool calls
    if (choice.message?.tool_calls) {
      result.tool_calls = choice.message.tool_calls;
    }

    return result;
  }

  /**
   * Streaming implementation (if supported)
   */
  async* stream(request: LLMRequest): AsyncGenerator<StreamChunk, void, unknown> {
    // Mark request as streaming
    const streamRequest = { ...request, stream: true };

    // This would need to be implemented based on Aliyun's streaming API
    // For now, fall back to regular invoke and yield the result
    const response = await this.invoke(streamRequest);

    if (response.error) {
      yield { type: 'error', data: response.error };
      return;
    }

    if (response.content) {
      yield { type: 'text', data: response.content };
    }

    if (response.audio) {
      yield { type: 'audio', data: response.audio };
    }

    if (response.tool_calls) {
      for (const toolCall of response.tool_calls) {
        yield { type: 'tool_call', data: toolCall };
      }
    }

    if (response.metadata.usage) {
      yield { type: 'usage', data: response.metadata.usage };
    }

    yield { type: 'done', data: null };
  }

  /**
   * Map provider-specific errors
   */
  mapError(error: any): LLMServiceError {
    const context = {
      requestId: generateRequestId(),
      provider: this.name,
      model: 'unknown',
      attempt: 1,
      timestamp: new Date().toISOString(),
      originalError: error
    };

    const llmErrorInterface = llmErrorService.mapError(error, context, this.config);
    
    return new LLMError(
      llmErrorInterface.message,
      llmErrorInterface.code,
      llmErrorInterface.statusCode || 500,
      llmErrorInterface.details,
      llmErrorInterface.requestId
    );
  }

  /**
   * Determine if error should be retried
   */
  shouldRetry(error: LLMServiceError): boolean {
    // Check if error codes indicate retryable conditions
    const retryableCodes = ['rate_limit', 'network', 'provider', 'timeout', 'service_unavailable'];
    return retryableCodes.includes(error.code.toLowerCase()) && error.statusCode >= 500;
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      const apiKey = authService.getApiKey(this.name);
      const isValid = authService.validateApiKey(this.name, apiKey);
      if (!isValid) {
        return false;
      }

      // Make a minimal test request
      const testRequest = {
        model: this.config.defaultModel,
        messages: [{ role: 'user', content: 'ping' }],
        max_tokens: 1
      };

      const response = await this.makeApiCall(testRequest, apiKey, 'health-check');
      return !!response;

    } catch (error) {
      this.logger.warn('Aliyun health check failed:', error);
      return false;
    }
  }
}

// Export the provider class and a factory function
export const createAliyunProvider = () => new AliyunLLMProvider();
export default AliyunLLMProvider;