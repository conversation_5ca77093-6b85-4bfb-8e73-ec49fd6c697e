/**
 * Unified Error Handling Service
 * 
 * Consolidates error handling logic across all LLM operations
 * Provides consistent error mapping, recovery strategies, and retry logic
 */

import { createLogger } from '@/utils/logger';
import type { LLMErrorInterface, RetryConfig, ProviderConfig } from './types.js';

interface ErrorContext {
  requestId: string;
  provider: string;
  model: string;
  attempt: number;
  timestamp: string;
  originalError: any;
}

interface RetryState {
  attempt: number;
  lastAttemptTime: number;
  backoffDelay: number;
  totalElapsed: number;
}

export class LLMErrorService {
  private logger: any;
  private retryStates = new Map<string, RetryState>();

  constructor() {
    this.logger = createLogger('LLMErrorService');
  }

  /**
   * Map raw error to standardized LLMError
   */
  mapError(error: any, context: ErrorContext, providerConfig: ProviderConfig): LLMErrorInterface {
    const baseError: LLMErrorInterface = {
      type: 'internal',
      code: 'UNKNOWN_ERROR',
      message: 'An unexpected error occurred',
      timestamp: context.timestamp,
      requestId: context.requestId,
      retryable: false,
      details: {
        provider: context.provider,
        model: context.model,
        attempt: context.attempt
      }
    };

    try {
      // Handle different error types
      if (error instanceof Error) {
        return this.mapJavaScriptError(error, baseError, context, providerConfig);
      } else if (error?.response) {
        return this.mapHttpError(error, baseError, context, providerConfig);
      } else if (error?.code) {
        return this.mapNetworkError(error, baseError, context, providerConfig);
      } else if (typeof error === 'object' && error !== null) {
        return this.mapObjectError(error, baseError, context, providerConfig);
      } else {
        baseError.message = String(error);
        return baseError;
      }
    } catch (mappingError) {
      this.logger.error('Error during error mapping:', mappingError);
      baseError.details.mappingError = String(mappingError);
      return baseError;
    }
  }

  /**
   * Map JavaScript Error objects
   */
  private mapJavaScriptError(
    error: Error,
    baseError: LLMErrorInterface,
    context: ErrorContext,
    providerConfig: ProviderConfig
  ): LLMErrorInterface {
    baseError.message = error.message;
    baseError.details.stack = error.stack;

    // Map by error message patterns
    if (error.message.includes('API key')) {
      return this.createAuthenticationError(error.message, context);
    }

    if (error.message.includes('rate limit') || error.message.includes('quota')) {
      return this.createRateLimitError(error.message, context);
    }

    if (error.message.includes('network') || error.message.includes('ECONNREFUSED')) {
      return this.createNetworkError(error.message, context);
    }

    if (error.message.includes('timeout')) {
      return this.createTimeoutError(error.message, context);
    }

    if (error.message.includes('validation') || error.message.includes('invalid')) {
      return this.createValidationError(error.message, context);
    }

    // Provider-specific error mapping
    const mappedType = providerConfig.errorMapping?.[error.message] ||
      providerConfig.errorMapping?.[error.name] ||
      'internal';

    baseError.type = mappedType;
    baseError.retryable = this.isRetryableErrorType(mappedType);

    return baseError;
  }

  /**
   * Map HTTP response errors
   */
  private mapHttpError(
    error: any,
    baseError: LLMErrorInterface,
    context: ErrorContext,
    providerConfig: ProviderConfig
  ): LLMErrorInterface {
    const status = error.response?.status;
    const statusText = error.response?.statusText;
    const data = error.response?.data;

    baseError.details.httpStatus = status;
    baseError.details.statusText = statusText;
    baseError.details.responseData = data;

    switch (status) {
      case 400:
        return this.createValidationError(
          data?.message || data?.error || 'Bad request - invalid parameters',
          context
        );

      case 401:
        return this.createAuthenticationError(
          data?.message || 'Authentication failed - invalid API key',
          context
        );

      case 403:
        return this.createAuthenticationError(
          data?.message || 'Access forbidden - insufficient permissions',
          context
        );

      case 429:
        const retryAfter = error.response?.headers?.['retry-after'];
        return this.createRateLimitError(
          data?.message || 'Rate limit exceeded',
          context,
          retryAfter ? parseInt(retryAfter) : undefined
        );

      case 500:
      case 502:
      case 503:
      case 504:
        return this.createProviderError(
          data?.message || `Provider server error (${status})`,
          context,
          true // retryable
        );

      default:
        baseError.code = `HTTP_${status}`;
        baseError.message = data?.message || statusText || `HTTP error ${status}`;
        baseError.type = status >= 500 ? 'provider' : 'internal';
        baseError.retryable = status >= 500;
        return baseError;
    }
  }

  /**
   * Map network-level errors
   */
  private mapNetworkError(
    error: any,
    baseError: LLMErrorInterface,
    context: ErrorContext,
    _providerConfig: ProviderConfig
  ): LLMErrorInterface {
    const code = error.code;
    baseError.details.networkCode = code;

    switch (code) {
      case 'ECONNREFUSED':
      case 'ENOTFOUND':
      case 'EAI_AGAIN':
        return this.createNetworkError(
          'Network connection failed - check internet connectivity',
          context
        );

      case 'ETIMEDOUT':
      case 'ESOCKETTIMEDOUT':
        return this.createTimeoutError(
          'Request timed out - try again later',
          context
        );

      case 'ECONNRESET':
        return this.createNetworkError(
          'Connection reset by server',
          context
        );

      default:
        baseError.type = 'network';
        baseError.code = code;
        baseError.message = error.message || `Network error: ${code}`;
        baseError.retryable = true;
        return baseError;
    }
  }

  /**
   * Map generic object errors
   */
  private mapObjectError(
    error: any,
    baseError: LLMErrorInterface,
    context: ErrorContext,
    providerConfig: ProviderConfig
  ): LLMErrorInterface {
    // Try to extract meaningful information
    const message = error.message || error.error || error.description || 'Unknown error';
    const code = error.code || error.error_code || error.type;

    baseError.message = String(message);
    if (code) baseError.code = String(code);

    // Use provider-specific mapping if available
    if (code && providerConfig.errorMapping?.[code]) {
      baseError.type = providerConfig.errorMapping[code];
      baseError.retryable = this.isRetryableErrorType(baseError.type);
    }

    return baseError;
  }

  /**
   * Create authentication error
   */
  private createAuthenticationError(message: string, context: ErrorContext): LLMErrorInterface {
    return {
      type: 'authentication',
      code: 'AUTH_FAILED',
      message,
      timestamp: context.timestamp,
      requestId: context.requestId,
      retryable: false,
      details: {
        provider: context.provider,
        suggestion: 'Check API key configuration'
      }
    };
  }

  /**
   * Create rate limit error
   */
  private createRateLimitError(message: string, context: ErrorContext, retryAfter?: number): LLMErrorInterface {
    return {
      type: 'rate_limit',
      code: 'RATE_LIMIT_EXCEEDED',
      message,
      timestamp: context.timestamp,
      requestId: context.requestId,
      retryable: true,
      retryAfter: retryAfter || 60,
      details: {
        provider: context.provider,
        suggestion: 'Reduce request rate or upgrade plan'
      }
    };
  }

  /**
   * Create network error
   */
  private createNetworkError(message: string, context: ErrorContext): LLMErrorInterface {
    return {
      type: 'network',
      code: 'NETWORK_ERROR',
      message,
      timestamp: context.timestamp,
      requestId: context.requestId,
      retryable: true,
      details: {
        provider: context.provider,
        suggestion: 'Check network connectivity and try again'
      }
    };
  }

  /**
   * Create timeout error
   */
  private createTimeoutError(message: string, context: ErrorContext): LLMErrorInterface {
    return {
      type: 'network',
      code: 'TIMEOUT',
      message,
      timestamp: context.timestamp,
      requestId: context.requestId,
      retryable: true,
      details: {
        provider: context.provider,
        suggestion: 'Request timed out, try again or increase timeout'
      }
    };
  }

  /**
   * Create validation error
   */
  private createValidationError(message: string, context: ErrorContext): LLMErrorInterface {
    return {
      type: 'validation',
      code: 'VALIDATION_ERROR',
      message,
      timestamp: context.timestamp,
      requestId: context.requestId,
      retryable: false,
      details: {
        provider: context.provider,
        suggestion: 'Check request parameters and format'
      }
    };
  }

  /**
   * Create provider error
   */
  private createProviderError(message: string, context: ErrorContext, retryable = false): LLMErrorInterface {
    return {
      type: 'provider',
      code: 'PROVIDER_ERROR',
      message,
      timestamp: context.timestamp,
      requestId: context.requestId,
      retryable,
      details: {
        provider: context.provider,
        suggestion: retryable ? 'Provider temporary issue, try again' : 'Provider service error'
      }
    };
  }

  /**
   * Determine if error type is retryable
   */
  private isRetryableErrorType(errorType: LLMErrorInterface['type']): boolean {
    switch (errorType) {
      case 'rate_limit':
      case 'network':
      case 'provider':
        return true;
      case 'authentication':
      case 'validation':
      case 'internal':
      default:
        return false;
    }
  }

  /**
   * Determine if specific error should be retried
   */
  shouldRetry(error: LLMErrorInterface, retryConfig: RetryConfig): boolean {
    // Check if error is retryable
    if (!error.retryable) {
      return false;
    }

    // Check if error type is in retryable list
    if (!retryConfig.retryableErrors.includes(error.type)) {
      return false;
    }

    // Get retry state
    const retryState = this.getRetryState(error.requestId);

    // Check max retries
    if (retryState.attempt >= retryConfig.maxRetries) {
      this.logger.warn(`Max retries (${retryConfig.maxRetries}) exceeded for request ${error.requestId}`);
      return false;
    }

    return true;
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  calculateRetryDelay(error: LLMErrorInterface, retryConfig: RetryConfig): number {
    const retryState = this.getRetryState(error.requestId);

    let delay = retryConfig.baseDelayMs;

    // Apply exponential backoff if enabled
    if (retryConfig.exponentialBackoff) {
      delay = Math.min(
        retryConfig.baseDelayMs * Math.pow(2, retryState.attempt),
        retryConfig.maxDelayMs
      );
    }

    // Add jitter to prevent thundering herd
    const jitter = Math.random() * 0.1 * delay;
    delay += jitter;

    // Respect retry-after header if present
    if (error.retryAfter) {
      delay = Math.max(delay, error.retryAfter * 1000);
    }

    return Math.round(delay);
  }

  /**
   * Update retry state for a request
   */
  updateRetryState(requestId: string): void {
    const now = Date.now();
    const existing = this.retryStates.get(requestId);

    if (existing) {
      existing.attempt++;
      existing.backoffDelay = this.calculateRetryDelay(
        { requestId } as LLMErrorInterface,
        { exponentialBackoff: true, baseDelayMs: 1000, maxDelayMs: 30000 } as RetryConfig
      );
      existing.totalElapsed = now - (existing.lastAttemptTime - existing.totalElapsed);
      existing.lastAttemptTime = now;
    } else {
      this.retryStates.set(requestId, {
        attempt: 1,
        lastAttemptTime: now,
        backoffDelay: 0,
        totalElapsed: 0
      });
    }
  }

  /**
   * Get retry state for a request
   */
  private getRetryState(requestId: string): RetryState {
    return this.retryStates.get(requestId) || {
      attempt: 0,
      lastAttemptTime: 0,
      backoffDelay: 0,
      totalElapsed: 0
    };
  }

  /**
   * Clear retry state for a request
   */
  clearRetryState(requestId: string): void {
    this.retryStates.delete(requestId);
  }

  /**
   * Enhanced error logging with context
   */
  logError(error: LLMErrorInterface): void {
    const logData = {
      type: error.type,
      code: error.code,
      message: error.message,
      requestId: error.requestId,
      retryable: error.retryable,
      retryAfter: error.retryAfter,
      provider: error.details?.provider,
      suggestion: error.details?.suggestion
    };

    switch (error.type) {
      case 'authentication':
        this.logger.error('🔑 Authentication Error:', logData);
        break;
      case 'rate_limit':
        this.logger.warn('🚦 Rate Limit Error:', logData);
        break;
      case 'network':
        this.logger.warn('🌐 Network Error:', logData);
        break;
      case 'validation':
        this.logger.error('✅ Validation Error:', logData);
        break;
      case 'provider':
        this.logger.error('🏭 Provider Error:', logData);
        break;
      default:
        this.logger.error('❌ Unknown Error:', logData);
    }
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    activeRetries: number;
    totalRetryStates: number;
    retrysByType: Record<string, number>;
  } {
    const now = Date.now();
    const fiveMinutesAgo = now - (5 * 60 * 1000);

    let activeRetries = 0;
    const retrysByType: Record<string, number> = {};

    for (const [requestId, state] of this.retryStates.entries()) {
      if (state.lastAttemptTime > fiveMinutesAgo) {
        activeRetries++;
      }

      // This would need to be enhanced to track error types
      retrysByType[requestId] = state.attempt;
    }

    return {
      activeRetries,
      totalRetryStates: this.retryStates.size,
      retrysByType
    };
  }

  /**
   * Cleanup old retry states
   */
  cleanup(): void {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);

    for (const [requestId, state] of this.retryStates.entries()) {
      if (state.lastAttemptTime < oneHourAgo) {
        this.retryStates.delete(requestId);
      }
    }
  }
}

// Export singleton instance
export const llmErrorService = new LLMErrorService();
export default llmErrorService;