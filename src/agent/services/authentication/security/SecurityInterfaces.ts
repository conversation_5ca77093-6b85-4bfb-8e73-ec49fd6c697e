/**
 * Security Architecture Interfaces
 * Comprehensive type definitions for the new unified security system
 */

export interface SecurityConfig {
  authMethod: 'bearer' | 'header' | 'oauth' | 'custom';
  tokenStorage: 'memory' | 'encrypted' | 'vault';
  rotationInterval: number;
  encryptionEnabled: boolean;
  encryptionKey?: string;
  hashAlgorithm: 'sha256' | 'sha512' | 'argon2';
}

export interface EncryptedToken {
  encryptedValue: string;
  timestamp: number;
  expiresAt?: number;
  algorithm: string;
  keyVersion: number;
}

export interface WebSocketSecureConfig {
  url: string;                          // Clean URL without API keys
  headers: Record<string, string>;      // Secure headers with Bearer token
  protocols?: string[];                 // WebSocket protocols
  authMethod: 'header-based' | 'token-based';
  securityLevel: 'standard' | 'high' | 'maximum';
}

export interface AuthContext {
  provider: string;
  requestId: string;
  timestamp: Date;
  userAgent?: string;
  ipAddress?: string;
  sessionId?: string;
}

export interface TokenValidationResult {
  isValid: boolean;
  expiresAt?: Date;
  scopes?: string[];
  errors?: string[];
  warnings?: string[];
}

export interface SecurityAuditLog {
  timestamp: Date;
  event: 'auth_success' | 'auth_failure' | 'token_rotation' | 'security_violation';
  provider: string;
  context: AuthContext;
  details: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * Authentication service interface
 */
export interface IAuthenticationService {
  getSecureAuthHeaders(provider: string, context?: AuthContext): Record<string, string>;
  getSecureWebSocketConfig(provider: string, options?: WebSocketOptions): WebSocketSecureConfig;
  validateToken(provider: string, token: string): Promise<TokenValidationResult>;
  rotateToken(provider: string): Promise<void>;
  clearCache(provider?: string): void;
  getSecurityMetrics(): SecurityMetrics;
}

export interface WebSocketOptions {
  protocols?: string[];
  timeout?: number;
  securityLevel?: 'standard' | 'high' | 'maximum';
  validateCertificate?: boolean;
}

export interface SecurityMetrics {
  authAttempts: number;
  authSuccesses: number;
  authFailures: number;
  tokenRotations: number;
  securityViolations: number;
  lastRotation?: Date;
}

/**
 * Credential management interfaces
 */
export interface CredentialStore {
  store(key: string, value: string, metadata?: CredentialMetadata): Promise<void>;
  retrieve(key: string): Promise<string | null>;
  delete(key: string): Promise<void>;
  list(): Promise<string[]>;
  rotate(key: string): Promise<void>;
}

export interface CredentialMetadata {
  provider: string;
  createdAt: Date;
  expiresAt?: Date;
  permissions?: string[];
  tags?: Record<string, string>;
}

/**
 * Security policy interfaces
 */
export interface SecurityPolicy {
  provider: string;
  authMethod: string;
  tokenTTL: number;
  maxFailedAttempts: number;
  lockoutDuration: number;
  requiresEncryption: boolean;
  allowedOrigins?: string[];
  rateLimits?: SecurityRateLimits;
}

export interface SecurityRateLimits {
  authAttemptsPerMinute: number;
  tokenRotationsPerHour: number;
  maxConcurrentSessions: number;
}

/**
 * Encryption interfaces
 */
export interface EncryptionService {
  encrypt(data: string, keyVersion?: number): Promise<EncryptedData>;
  decrypt(encryptedData: EncryptedData): Promise<string>;
  generateKey(): Promise<string>;
  rotateKeys(): Promise<void>;
}

export interface EncryptedData {
  data: string;
  algorithm: string;
  keyVersion: number;
  iv: string;
  timestamp: Date;
}