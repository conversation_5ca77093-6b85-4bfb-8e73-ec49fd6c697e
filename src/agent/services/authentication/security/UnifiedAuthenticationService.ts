/**
 * Unified Authentication Service Implementation
 * SECURITY FIX: Eliminates API key exposure in URLs and query parameters
 * 
 * This replaces the insecure patterns found in:
 * - AliyunWebSocketChatModel.js (API keys in WebSocket URLs)
 * - Multiple proxy configurations with exposed credentials
 */

import { createHash, randomBytes, createCipher, createDecipher } from 'crypto';
import {
  IAuthenticationService,
  SecurityConfig,
  EncryptedToken,
  WebSocketSecureConfig,
  AuthContext,
  TokenValidationResult,
  SecurityAuditLog,
  SecurityMetrics,
  WebSocketOptions
} from '../interfaces/SecurityInterfaces.js';
import { getEnvVar } from '../../config/env.js';

export class UnifiedAuthenticationService implements IAuthenticationService {
  private static instance: UnifiedAuthenticationService;
  private tokenCache = new Map<string, EncryptedToken>();
  private securityConfig: SecurityConfig;
  private auditLogs: SecurityAuditLog[] = [];
  private metrics: SecurityMetrics;
  private encryptionKey: string;

  static getInstance(): UnifiedAuthenticationService {
    if (!UnifiedAuthenticationService.instance) {
      UnifiedAuthenticationService.instance = new UnifiedAuthenticationService();
    }
    return UnifiedAuthenticationService.instance;
  }

  private constructor() {
    this.securityConfig = this.loadSecurityConfig();
    this.metrics = this.initializeMetrics();
    this.encryptionKey = this.generateEncryptionKey();
  }

  /**
   * SECURITY FIX: Get secure Bearer token headers (never expose in URLs)
   * Replaces all instances of API key exposure in query parameters
   */
  getSecureAuthHeaders(provider: string, context?: AuthContext): Record<string, string> {
    try {
      const token = this.getSecureToken(provider);
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'X-Security-Version': '2.0',
        'X-Auth-Method': 'bearer',
        'X-Provider': provider
      };

      if (context) {
        headers['X-Request-ID'] = context.requestId;
        headers['X-Timestamp'] = context.timestamp.toISOString();
      }

      this.recordAuthSuccess(provider, context);
      return headers;

    } catch (error) {
      this.recordAuthFailure(provider, error as Error, context);
      throw new Error(`Failed to generate secure auth headers for ${provider}: ${(error as Error).message}`);
    }
  }

  /**
   * CRITICAL FIX: Secure WebSocket configuration without API keys in URLs
   * Replaces buildWebSocketUrl() method that exposed API keys
   */
  getSecureWebSocketConfig(provider: string, options: WebSocketOptions = {}): WebSocketSecureConfig {
    const baseUrl = this.getProviderWebSocketUrl(provider);
    const secureHeaders = this.getSecureAuthHeaders(provider);

    // SECURITY: Never include API keys in WebSocket URLs
    const secureUrl = this.buildSecureWebSocketUrl(baseUrl, provider, options);

    return {
      url: secureUrl,
      headers: secureHeaders,
      protocols: options.protocols || ['wss'],
      authMethod: 'header-based',
      securityLevel: options.securityLevel || 'high'
    };
  }

  /**
   * Validate token without exposing sensitive data
   */
  async validateToken(provider: string, token: string): Promise<TokenValidationResult> {
    try {
      const hashedToken = this.hashToken(token);
      const cached = this.tokenCache.get(`${provider}_${hashedToken}`);

      if (!cached) {
        return {
          isValid: false,
          errors: ['Token not found in cache']
        };
      }

      const isExpired = cached.expiresAt && new Date() > new Date(cached.expiresAt);
      if (isExpired) {
        this.tokenCache.delete(`${provider}_${hashedToken}`);
        return {
          isValid: false,
          errors: ['Token has expired']
        };
      }

      return {
        isValid: true,
        expiresAt: cached.expiresAt ? new Date(cached.expiresAt) : undefined
      };

    } catch (error) {
      return {
        isValid: false,
        errors: [(error as Error).message]
      };
    }
  }

  /**
   * Rotate token for enhanced security
   */
  async rotateToken(provider: string): Promise<void> {
    try {
      // Clear existing token
      const existingKeys = Array.from(this.tokenCache.keys()).filter(key => key.startsWith(provider));
      existingKeys.forEach(key => this.tokenCache.delete(key));

      // Force reload of token from environment
      this.getSecureToken(provider, true);

      this.metrics.tokenRotations++;
      this.auditLog('token_rotation', provider, {
        rotatedAt: new Date(),
        reason: 'Manual rotation'
      });

    } catch (error) {
      throw new Error(`Failed to rotate token for ${provider}: ${(error as Error).message}`);
    }
  }

  /**
   * Clear token cache
   */
  clearCache(provider?: string): void {
    if (provider) {
      const keys = Array.from(this.tokenCache.keys()).filter(key => key.startsWith(provider));
      keys.forEach(key => this.tokenCache.delete(key));
    } else {
      this.tokenCache.clear();
    }
  }

  /**
   * Get security metrics
   */
  getSecurityMetrics(): SecurityMetrics {
    return {
      ...this.metrics,
      lastRotation: this.getLastRotationDate()
    };
  }

  // ============================================
  // PRIVATE METHODS
  // ============================================

  /**
   * Get secure token with encryption
   */
  private getSecureToken(provider: string, forceReload = false): string {
    const cacheKey = `${provider}_secure_token`;
    
    if (!forceReload) {
      const cached = this.tokenCache.get(cacheKey);
      if (cached && this.isTokenValid(cached)) {
        return this.decryptToken(cached);
      }
    }

    // Load raw API key from environment
    const rawApiKey = this.loadApiKeyFromEnvironment(provider);
    
    // Validate API key format
    this.validateApiKeyFormat(provider, rawApiKey);
    
    // Encrypt and cache the token
    const encryptedToken = this.encryptToken(rawApiKey);
    this.tokenCache.set(cacheKey, encryptedToken);

    return rawApiKey;
  }

  /**
   * Load API key from environment variables
   * CONSOLIDATED: Merges logic from /services/llm/authentication.ts
   */
  private loadApiKeyFromEnvironment(provider: string): string {
    let apiKey = '';

    switch (provider.toLowerCase()) {
      case 'aliyun':
        // Consolidated from LLMAuthenticationService patterns
        apiKey = getEnvVar('DASHSCOPE_API_KEY', '', false) || 
                 getEnvVar('ALIYUN_API_KEY', '', false) ||
                 process.env.VITE_DASHSCOPE_API_KEY ||
                 process.env.DASHSCOPE_API_KEY ||
                 '';
        break;
      
      case 'openai':
        apiKey = getEnvVar('OPENAI_API_KEY', '', false) || 
                 process.env.OPENAI_API_KEY ||
                 '';
        break;

      case 'anthropic':
        apiKey = getEnvVar('ANTHROPIC_API_KEY', '', false) ||
                 process.env.ANTHROPIC_API_KEY ||
                 '';
        break;

      case 'gemini':
        apiKey = getEnvVar('GEMINI_API_KEY', '', false) ||
                 process.env.GEMINI_API_KEY ||
                 '';
        break;
      
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }

    if (!apiKey || apiKey.trim() === '') {
      const envVars = this.getRequiredEnvVars(provider);
      throw new Error(
        `API key not configured for provider: ${provider}. ` +
        `Please set one of: ${envVars.join(', ')}`
      );
    }

    return apiKey;
  }

  /**
   * Build secure WebSocket URL without API keys
   */
  private buildSecureWebSocketUrl(baseUrl: string, provider: string, options: WebSocketOptions): string {
    // Remove any existing query parameters that might contain API keys
    const url = new URL(baseUrl);
    
    // Clear all query parameters to ensure no API keys
    url.search = '';

    // Add only safe, non-sensitive parameters
    if (provider === 'aliyun') {
      // For Aliyun, we can include the model but never API keys
      url.searchParams.set('model', 'qwen-omni-turbo-realtime-latest');
    }

    // Add security validation parameter
    url.searchParams.set('auth_method', 'bearer');
    url.searchParams.set('security_level', options.securityLevel || 'high');

    return url.toString();
  }

  /**
   * Get provider-specific WebSocket URL
   */
  private getProviderWebSocketUrl(provider: string): string {
    switch (provider.toLowerCase()) {
      case 'aliyun':
        return 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime';
      default:
        throw new Error(`WebSocket URL not configured for provider: ${provider}`);
    }
  }

  /**
   * Encrypt token for secure storage
   */
  private encryptToken(token: string): EncryptedToken {
    const cipher = createCipher('aes-256-cbc', this.encryptionKey);
    let encrypted = cipher.update(token, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return {
      encryptedValue: encrypted,
      timestamp: Date.now(),
      expiresAt: Date.now() + (5 * 60 * 1000), // 5 minutes TTL
      algorithm: 'aes-256-cbc',
      keyVersion: 1
    };
  }

  /**
   * Decrypt token
   */
  private decryptToken(encryptedToken: EncryptedToken): string {
    const decipher = createDecipher(encryptedToken.algorithm, this.encryptionKey);
    let decrypted = decipher.update(encryptedToken.encryptedValue, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }

  /**
   * Hash token for caching
   */
  private hashToken(token: string): string {
    return createHash('sha256').update(token).digest('hex');
  }

  /**
   * Validate API key format
   */
  private validateApiKeyFormat(provider: string, apiKey: string): void {
    switch (provider.toLowerCase()) {
      case 'aliyun':
        if (!apiKey.startsWith('sk-') || apiKey.length < 20) {
          throw new Error('Invalid Aliyun API key format');
        }
        break;
      
      case 'openai':
        if (!apiKey.startsWith('sk-') || apiKey.length < 40) {
          throw new Error('Invalid OpenAI API key format');
        }
        break;
    }
  }

  /**
   * Check if token is still valid
   */
  private isTokenValid(token: EncryptedToken): boolean {
    if (token.expiresAt && Date.now() > token.expiresAt) {
      return false;
    }
    return true;
  }

  /**
   * Load security configuration
   */
  private loadSecurityConfig(): SecurityConfig {
    return {
      authMethod: 'bearer',
      tokenStorage: 'encrypted',
      rotationInterval: 3600000, // 1 hour
      encryptionEnabled: true,
      hashAlgorithm: 'sha256'
    };
  }

  /**
   * Initialize metrics
   */
  private initializeMetrics(): SecurityMetrics {
    return {
      authAttempts: 0,
      authSuccesses: 0,
      authFailures: 0,
      tokenRotations: 0,
      securityViolations: 0
    };
  }

  /**
   * Generate encryption key
   */
  private generateEncryptionKey(): string {
    return process.env.SECURITY_ENCRYPTION_KEY || 
           randomBytes(32).toString('hex');
  }

  /**
   * Get required environment variables for provider
   */
  private getRequiredEnvVars(provider: string): string[] {
    switch (provider.toLowerCase()) {
      case 'aliyun':
        return ['DASHSCOPE_API_KEY', 'ALIYUN_API_KEY', 'VITE_DASHSCOPE_API_KEY'];
      case 'openai':
        return ['OPENAI_API_KEY'];
      default:
        return [];
    }
  }

  /**
   * Record authentication success
   */
  private recordAuthSuccess(provider: string, context?: AuthContext): void {
    this.metrics.authAttempts++;
    this.metrics.authSuccesses++;

    if (context) {
      this.auditLog('auth_success', provider, context);
    }
  }

  /**
   * Record authentication failure
   */
  private recordAuthFailure(provider: string, error: Error, context?: AuthContext): void {
    this.metrics.authAttempts++;
    this.metrics.authFailures++;

    this.auditLog('auth_failure', provider, {
      error: error.message,
      ...context
    }, 'high');
  }

  /**
   * Add audit log entry
   */
  private auditLog(
    event: SecurityAuditLog['event'],
    provider: string,
    details: any,
    severity: SecurityAuditLog['severity'] = 'medium'
  ): void {
    const logEntry: SecurityAuditLog = {
      timestamp: new Date(),
      event,
      provider,
      context: {
        provider,
        requestId: details.requestId || 'unknown',
        timestamp: new Date()
      },
      details,
      severity
    };

    this.auditLogs.push(logEntry);

    // Keep only last 1000 entries
    if (this.auditLogs.length > 1000) {
      this.auditLogs.shift();
    }
  }

  /**
   * Get last token rotation date
   */
  private getLastRotationDate(): Date | undefined {
    const rotationLogs = this.auditLogs.filter(log => log.event === 'token_rotation');
    if (rotationLogs.length === 0) return undefined;

    return rotationLogs[rotationLogs.length - 1].timestamp;
  }
}

// Export singleton instance
export const unifiedAuthService = UnifiedAuthenticationService.getInstance();