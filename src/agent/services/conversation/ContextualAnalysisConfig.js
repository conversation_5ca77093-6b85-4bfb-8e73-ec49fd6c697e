/**
 * Contextual Analysis Configuration
 * 
 * Centralized configuration for contextual analysis parameters,
 * multimodal thresholds, and proactive decision-making settings.
 */

/**
 * Default contextual analysis configuration
 */
export const DEFAULT_CONTEXTUAL_CONFIG = {
    // Core analysis thresholds
    silenceThreshold: 15000, // 15 seconds
    engagementThreshold: 0.4, // Below 0.4 is considered low engagement
    stalledConversationThreshold: 30000, // 30 seconds
    decliningEngagementThreshold: 0.3,
    thrivingEngagementThreshold: 0.7,
    maxRecentInteractions: 10,
    recentInteractionWindow: 5 * 60 * 1000, // 5 minutes

    // Proactive decision making
    proactive: {
        continuousAnalysisFrequency: 2000, // 2 seconds
        decisionCooldown: 5000, // 5 seconds between proactive decisions
        confidenceThreshold: 0.6, // Minimum confidence for proactive speaking
        significantContextChangeThreshold: 0.3, // Threshold for immediate analysis trigger
        optimalSpeakingMomentWindow: 8000, // 8 seconds for natural pause detection
        guidanceOpportunityThreshold: 10000, // 10 seconds for guidance detection
    },

    // Multimodal analysis configuration
    audio: {
        volumeThreshold: 0.1, // Minimum volume level for active speech
        qualityThreshold: 0.6, // Minimum quality score for clear audio
        sentimentWindow: 10, // Number of audio samples for sentiment analysis
        speechRateThreshold: 2.5, // Words per second - normal range 2-4 wps
        pauseThreshold: 2000, // Milliseconds - longer pauses indicate hesitation
        freshContextWindow: 5000, // 5 seconds - fresh audio analysis window
        engagementVolumeMultiplier: 0.5, // Volume engagement calculation factor
        engagementQualityWeight: 0.2, // Quality weight in engagement calculation
        engagementSpeechRateWeight: 0.2, // Speech rate weight in engagement
        engagementPauseWeight: 0.15, // Pause weight in engagement
        engagementSentimentWeight: 0.15, // Sentiment weight in engagement
        optimalSpeechRate: 2.5, // Optimal words per second
    },

    visual: {
        faceDetectionConfidence: 0.7, // Minimum confidence for face detection
        emotionThreshold: 0.6, // Minimum confidence for emotion detection
        engagementThreshold: 0.5, // Minimum eye contact/attention score
        movementThreshold: 0.3, // Amount of movement indicating distraction
        analysisInterval: 1000, // Milliseconds between visual analysis
        freshContextWindow: 3000, // 3 seconds - fresh visual analysis window
        attentionDisengagementThreshold: 0.3, // Attention score below this triggers disengagement
        visualDistractionThreshold: 0.2, // Attention score for distraction trigger
        movementDistractionMultiplier: 2, // Movement level multiplier for distraction
        // Visual engagement calculation weights
        faceDetectionWeight: 0.25,
        emotionWeight: 0.2,
        eyeContactWeight: 0.25,
        attentionWeight: 0.2,
        movementWeight: 0.1,
        optimalMovementLevel: 0.3,
    },

    profile: {
        historyWindow: 30 * 24 * 60 * 60 * 1000, // 30 days of interaction history
        preferenceLearningRate: 0.1, // How quickly to adapt to user preferences
        sessionMemoryLimit: 100, // Maximum number of session interactions to remember
        freshContextWindow: 5000, // 5 seconds - fresh context window for confidence
    },

    // Trigger confidence thresholds
    triggers: {
        userSilence: {
            baseConfidence: 0.4,
            maxConfidence: 1.0,
            priority: 'medium'
        },
        lowEngagement: {
            baseConfidence: 0.5,
            maxConfidence: 1.0,
            priority: 'high'
        },
        facialDisengagement: {
            baseConfidence: 0.3,
            maxConfidence: 0.8,
            priority: 'medium'
        },
        audioVolumeDrop: {
            baseConfidence: 0.2,
            maxConfidence: 0.7,
            priority: 'low'
        },
        voiceSentimentNegative: {
            baseConfidence: 0.6,
            maxConfidence: 0.8,
            priority: 'high'
        },
        visualDistraction: {
            baseConfidence: 0.2,
            maxConfidence: 0.5,
            priority: 'medium'
        },
        guidanceNeeded: {
            baseConfidence: 0.6,
            maxConfidence: 0.8,
            priority: 'high'
        }
    },

    // Multimodal engagement calculation weights
    multimodalEngagement: {
        defaultWeights: {
            text: 0.3,
            audio: 0.4,
            visual: 0.3
        },
        confidenceFactors: {
            dataRecencyWeight: 0.6,
            qualityWeight: 0.4,
            maxAge: 5000 // 5 seconds max age for confidence calculation
        }
    },

    // Conversation health assessment
    conversationHealth: {
        thrivingThreshold: 0.7,
        decliningThreshold: 0.3,
        stalledThreshold: 30000, // 30 seconds
    },

    // Proactive speaking decision parameters
    speakingDecision: {
        prolongedSilenceWeight: 0.4,
        lowEngagementWeight: 0.5,
        facialDisengagementWeight: 0.3,
        audioQualityWeight: 0.2,
        guidanceOpportunityWeight: 0.6,
        conversationHealthWeight: 0.3,
        urgencyLevels: {
            low: 1,
            medium: 2,
            high: 3
        }
    }
};

/**
 * Create contextual analysis configuration with overrides
 * @param {Object} overrides - Configuration overrides
 * @returns {Object} Merged configuration
 */
export function createContextualConfig(overrides = {}) {
    return mergeConfig(DEFAULT_CONTEXTUAL_CONFIG, overrides);
}

/**
 * Deep merge configuration objects
 * @param {Object} defaultConfig - Default configuration
 * @param {Object} userConfig - User overrides
 * @returns {Object} Merged configuration
 */
function mergeConfig(defaultConfig, userConfig) {
    const result = { ...defaultConfig };

    for (const key in userConfig) {
        if (userConfig[key] && typeof userConfig[key] === 'object' && !Array.isArray(userConfig[key])) {
            result[key] = mergeConfig(defaultConfig[key] || {}, userConfig[key]);
        } else {
            result[key] = userConfig[key];
        }
    }

    return result;
}

/**
 * Validate contextual analysis configuration
 * @param {Object} config - Configuration to validate
 * @returns {Object} Validation result
 */
export function validateContextualConfig(config) {
    const errors = [];

    // Validate required numeric thresholds
    const requiredNumbers = [
        'silenceThreshold', 'engagementThreshold', 'stalledConversationThreshold'
    ];

    for (const field of requiredNumbers) {
        if (typeof config[field] !== 'number' || config[field] < 0) {
            errors.push(`${field} must be a positive number`);
        }
    }

    // Validate proactive configuration
    if (config.proactive) {
        if (typeof config.proactive.continuousAnalysisFrequency !== 'number' ||
            config.proactive.continuousAnalysisFrequency < 1000) {
            errors.push('proactive.continuousAnalysisFrequency must be at least 1000ms');
        }

        if (typeof config.proactive.confidenceThreshold !== 'number' ||
            config.proactive.confidenceThreshold < 0 || config.proactive.confidenceThreshold > 1) {
            errors.push('proactive.confidenceThreshold must be between 0 and 1');
        }
    }

    // Validate multimodal thresholds
    if (config.audio && typeof config.audio.volumeThreshold !== 'number') {
        errors.push('audio.volumeThreshold must be a number');
    }

    if (config.visual && typeof config.visual.engagementThreshold !== 'number') {
        errors.push('visual.engagementThreshold must be a number');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

export default {
    DEFAULT_CONTEXTUAL_CONFIG,
    createContextualConfig,
    validateContextualConfig
}; 