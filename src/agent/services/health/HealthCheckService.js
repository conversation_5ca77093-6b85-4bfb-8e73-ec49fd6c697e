/**
 * Health Check Service - API health monitoring excluded from rate limits
 * 
 * Provides separate health check endpoints that don't count toward API rate limits
 * and gives proper user feedback for system states.
 */

import { createLogger, LogLevel } from '@/utils/logger';

const logger = createLogger('HealthCheckService', LogLevel.DEBUG);

/**
 * Health Check Service for API monitoring
 * Separate from regular API calls to avoid rate limiting
 */
export class HealthCheckService {
    constructor(options = {}) {
        this.options = {
            healthCheckInterval: options.healthCheckInterval || 30000, // 30 seconds
            healthCheckTimeout: options.healthCheckTimeout || 5000,    // 5 seconds
            maxConsecutiveFailures: options.maxConsecutiveFailures || 3,
            enablePeriodicChecks: options.enablePeriodicChecks !== false,
            ...options
        };

        this.healthStatus = {
            aliyun: { healthy: false, lastCheck: null, consecutive_failures: 0 },
            websocket: { healthy: false, lastCheck: null, consecutive_failures: 0 },
            camera: { healthy: false, lastCheck: null, consecutive_failures: 0 },
            microphone: { healthy: false, lastCheck: null, consecutive_failures: 0 }
        };

        this.healthCheckCallbacks = new Set();
        this.periodicCheckInterval = null;
        
        // Start periodic health checks
        if (this.options.enablePeriodicChecks) {
            this.startPeriodicHealthChecks();
        }

        logger.info('✅ HealthCheckService initialized', {
            interval: this.options.healthCheckInterval,
            timeout: this.options.healthCheckTimeout
        });
    }

    /**
     * Register callback for health status changes
     * @param {Function} callback - Callback function (status, component) => void
     */
    onHealthStatusChange(callback) {
        this.healthCheckCallbacks.add(callback);
    }

    /**
     * Remove health status callback
     * @param {Function} callback - Callback function to remove
     */
    removeHealthStatusCallback(callback) {
        this.healthCheckCallbacks.delete(callback);
    }

    /**
     * Notify callbacks about health status changes
     * @private
     */
    _notifyHealthStatusChange(component, status) {
        this.healthCheckCallbacks.forEach(callback => {
            try {
                callback(status, component);
            } catch (error) {
                logger.warn('Error in health status callback:', error);
            }
        });
    }

    /**
     * Check Aliyun API health using dedicated health endpoint
     * This uses a separate endpoint that doesn't count toward rate limits
     */
    async checkAliyunHealth() {
        const startTime = Date.now();
        
        try {
            // Use a dedicated health check endpoint that doesn't count toward rate limits
            const response = await fetch('/api/health/aliyun', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Health-Check': 'true' // Special header to identify health checks
                },
                signal: AbortSignal.timeout(this.options.healthCheckTimeout)
            });

            const responseTime = Date.now() - startTime;
            const isHealthy = response.ok && responseTime < 2000; // Under 2 seconds

            this.healthStatus.aliyun = {
                healthy: isHealthy,
                lastCheck: Date.now(),
                responseTime,
                consecutive_failures: isHealthy ? 0 : this.healthStatus.aliyun.consecutive_failures + 1,
                status: response.status,
                error: isHealthy ? null : `HTTP ${response.status}`
            };

            if (!isHealthy) {
                logger.warn(`❌ Aliyun health check failed:`, {
                    status: response.status,
                    responseTime,
                    consecutive_failures: this.healthStatus.aliyun.consecutive_failures
                });
            } else {
                logger.debug(`✅ Aliyun health check passed:`, { responseTime });
            }

            this._notifyHealthStatusChange('aliyun', this.healthStatus.aliyun);
            return this.healthStatus.aliyun;

        } catch (error) {
            const responseTime = Date.now() - startTime;
            
            this.healthStatus.aliyun = {
                healthy: false,
                lastCheck: Date.now(),
                responseTime,
                consecutive_failures: this.healthStatus.aliyun.consecutive_failures + 1,
                error: error.message
            };

            logger.error(`❌ Aliyun health check error:`, {
                error: error.message,
                responseTime,
                consecutive_failures: this.healthStatus.aliyun.consecutive_failures
            });

            this._notifyHealthStatusChange('aliyun', this.healthStatus.aliyun);
            return this.healthStatus.aliyun;
        }
    }

    /**
     * Check WebSocket connectivity health
     */
    async checkWebSocketHealth() {
        const startTime = Date.now();
        
        try {
            // Test WebSocket connectivity using a lightweight ping
            const wsHealthUrl = this.options.wsHealthUrl || 'ws://localhost:2994/ws/health';
            
            const testSocket = new WebSocket(wsHealthUrl);
            
            const healthResult = await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    testSocket.close();
                    reject(new Error('WebSocket health check timeout'));
                }, this.options.healthCheckTimeout);

                testSocket.onopen = () => {
                    clearTimeout(timeout);
                    testSocket.close(1000, 'Health check complete');
                    resolve(true);
                };

                testSocket.onerror = (error) => {
                    clearTimeout(timeout);
                    reject(error);
                };
            });

            const responseTime = Date.now() - startTime;

            this.healthStatus.websocket = {
                healthy: true,
                lastCheck: Date.now(),
                responseTime,
                consecutive_failures: 0,
                error: null
            };

            logger.debug(`✅ WebSocket health check passed:`, { responseTime });

        } catch (error) {
            const responseTime = Date.now() - startTime;
            
            this.healthStatus.websocket = {
                healthy: false,
                lastCheck: Date.now(),
                responseTime,
                consecutive_failures: this.healthStatus.websocket.consecutive_failures + 1,
                error: error.message
            };

            logger.warn(`❌ WebSocket health check failed:`, {
                error: error.message,
                responseTime,
                consecutive_failures: this.healthStatus.websocket.consecutive_failures
            });
        }

        this._notifyHealthStatusChange('websocket', this.healthStatus.websocket);
        return this.healthStatus.websocket;
    }

    /**
     * Check camera permissions and availability (non-blocking)
     */
    async checkCameraHealth() {
        const startTime = Date.now();
        
        try {
            // Check camera permissions without requesting them
            if (navigator.permissions) {
                const permission = await navigator.permissions.query({ name: 'camera' });
                
                const isHealthy = permission.state === 'granted' || permission.state === 'prompt';
                const responseTime = Date.now() - startTime;

                this.healthStatus.camera = {
                    healthy: isHealthy,
                    lastCheck: Date.now(),
                    responseTime,
                    consecutive_failures: isHealthy ? 0 : this.healthStatus.camera.consecutive_failures + 1,
                    permission: permission.state,
                    error: isHealthy ? null : `Camera permission ${permission.state}`
                };

                logger.debug(`📹 Camera health check:`, {
                    permission: permission.state,
                    healthy: isHealthy,
                    responseTime
                });

            } else {
                // Fallback: check if getUserMedia is available
                const isHealthy = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
                
                this.healthStatus.camera = {
                    healthy: isHealthy,
                    lastCheck: Date.now(),
                    responseTime: Date.now() - startTime,
                    consecutive_failures: isHealthy ? 0 : this.healthStatus.camera.consecutive_failures + 1,
                    permission: 'unknown',
                    error: isHealthy ? null : 'getUserMedia not available'
                };
            }

        } catch (error) {
            this.healthStatus.camera = {
                healthy: false,
                lastCheck: Date.now(),
                responseTime: Date.now() - startTime,
                consecutive_failures: this.healthStatus.camera.consecutive_failures + 1,
                error: error.message
            };

            logger.debug(`📹 Camera health check error (non-critical):`, error.message);
        }

        this._notifyHealthStatusChange('camera', this.healthStatus.camera);
        return this.healthStatus.camera;
    }

    /**
     * Check microphone permissions and availability (non-blocking)
     */
    async checkMicrophoneHealth() {
        const startTime = Date.now();
        
        try {
            // Check microphone permissions without requesting them
            if (navigator.permissions) {
                const permission = await navigator.permissions.query({ name: 'microphone' });
                
                const isHealthy = permission.state === 'granted' || permission.state === 'prompt';
                const responseTime = Date.now() - startTime;

                this.healthStatus.microphone = {
                    healthy: isHealthy,
                    lastCheck: Date.now(),
                    responseTime,
                    consecutive_failures: isHealthy ? 0 : this.healthStatus.microphone.consecutive_failures + 1,
                    permission: permission.state,
                    error: isHealthy ? null : `Microphone permission ${permission.state}`
                };

                logger.debug(`🎤 Microphone health check:`, {
                    permission: permission.state,
                    healthy: isHealthy,
                    responseTime
                });

            } else {
                // Fallback: check if getUserMedia is available  
                const isHealthy = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
                
                this.healthStatus.microphone = {
                    healthy: isHealthy,
                    lastCheck: Date.now(),
                    responseTime: Date.now() - startTime,
                    consecutive_failures: isHealthy ? 0 : this.healthStatus.microphone.consecutive_failures + 1,
                    permission: 'unknown',
                    error: isHealthy ? null : 'getUserMedia not available'
                };
            }

        } catch (error) {
            this.healthStatus.microphone = {
                healthy: false,
                lastCheck: Date.now(),
                responseTime: Date.now() - startTime,
                consecutive_failures: this.healthStatus.microphone.consecutive_failures + 1,
                error: error.message
            };

            logger.debug(`🎤 Microphone health check error (non-critical):`, error.message);
        }

        this._notifyHealthStatusChange('microphone', this.healthStatus.microphone);
        return this.healthStatus.microphone;
    }

    /**
     * Perform comprehensive health check of all systems
     */
    async performFullHealthCheck() {
        logger.info('🔍 Performing full system health check...');
        
        const startTime = Date.now();
        
        // Run all health checks in parallel (non-blocking for optional components)
        const [aliyunHealth, wsHealth, cameraHealth, micHealth] = await Promise.allSettled([
            this.checkAliyunHealth(),
            this.checkWebSocketHealth(), 
            this.checkCameraHealth(),
            this.checkMicrophoneHealth()
        ]);

        const totalTime = Date.now() - startTime;
        
        // Extract results (settled promises don't throw)
        const results = {
            aliyun: aliyunHealth.status === 'fulfilled' ? aliyunHealth.value : { healthy: false, error: aliyunHealth.reason?.message },
            websocket: wsHealth.status === 'fulfilled' ? wsHealth.value : { healthy: false, error: wsHealth.reason?.message },
            camera: cameraHealth.status === 'fulfilled' ? cameraHealth.value : { healthy: false, error: cameraHealth.reason?.message },
            microphone: micHealth.status === 'fulfilled' ? micHealth.value : { healthy: false, error: micHealth.reason?.message }
        };

        // Calculate overall health (camera and microphone are optional)
        const criticalSystemsHealthy = results.aliyun.healthy && results.websocket.healthy;
        const overallHealthy = criticalSystemsHealthy; // Optional systems don't affect overall health

        const healthSummary = {
            overall: {
                healthy: overallHealthy,
                lastCheck: Date.now(),
                totalTime,
                criticalSystemsHealthy,
                optionalSystemsHealthy: results.camera.healthy && results.microphone.healthy
            },
            components: results
        };

        logger.info(`📊 Full health check completed:`, {
            overall: overallHealthy,
            totalTime,
            aliyun: results.aliyun.healthy,
            websocket: results.websocket.healthy,
            camera: results.camera.healthy,
            microphone: results.microphone.healthy
        });

        return healthSummary;
    }

    /**
     * Get current health status for all components
     */
    getHealthStatus() {
        return {
            timestamp: Date.now(),
            status: { ...this.healthStatus }
        };
    }

    /**
     * Check if system is ready for voice interaction
     */
    isVoiceInteractionReady() {
        const aliyunHealthy = this.healthStatus.aliyun.healthy;
        const wsHealthy = this.healthStatus.websocket.healthy;
        
        // Microphone permission is checked but not required for readiness
        // (user will be prompted when needed)
        
        return aliyunHealthy && wsHealthy;
    }

    /**
     * Start periodic health checks
     */
    startPeriodicHealthChecks() {
        if (this.periodicCheckInterval) {
            clearInterval(this.periodicCheckInterval);
        }

        this.periodicCheckInterval = setInterval(async () => {
            try {
                await this.performFullHealthCheck();
            } catch (error) {
                logger.error('Error in periodic health check:', error);
            }
        }, this.options.healthCheckInterval);

        logger.info('🔄 Started periodic health checks', {
            interval: this.options.healthCheckInterval
        });
    }

    /**
     * Stop periodic health checks
     */
    stopPeriodicHealthChecks() {
        if (this.periodicCheckInterval) {
            clearInterval(this.periodicCheckInterval);
            this.periodicCheckInterval = null;
            logger.info('⏹️ Stopped periodic health checks');
        }
    }

    /**
     * Cleanup and dispose
     */
    dispose() {
        this.stopPeriodicHealthChecks();
        this.healthCheckCallbacks.clear();
        logger.debug('🧹 HealthCheckService disposed');
    }
}

export default HealthCheckService;