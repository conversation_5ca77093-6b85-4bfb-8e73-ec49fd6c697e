/**
 * Cache Manager
 * Intelligent caching with TTL support for model instances and routing decisions
 * Implements Week 2-3 optimization: node-level caching with TTL
 */

import { createLogger, LogLevel } from '@/utils/logger';

export class CacheManager {
    constructor(options = {}) {
        this.modelInstanceTTL = options.modelInstanceTTL || 300000; // 5 minutes
        this.routingDecisionTTL = options.routingDecisionTTL || 60000; // 1 minute
        this.maxCacheSize = options.maxCacheSize || 100;
        this.logger = options.logger || createLogger('CacheManager', LogLevel.INFO);
        
        // Cache storage
        this.modelCache = new Map();
        this.routingCache = new Map();
        this.cacheMetadata = new Map();
        
        // Cache metrics
        this.metrics = {
            hits: 0,
            misses: 0,
            evictions: 0,
            totalRequests: 0
        };
        
        // Cleanup interval
        this.cleanupInterval = setInterval(() => {
            this._cleanupExpiredEntries();
        }, 60000); // Clean up every minute
        
        this.logger.info('CacheManager initialized with TTL configuration');
    }

    /**
     * Generate cache key for model instances
     * @param {string} modelName - Model name
     * @param {Object} options - Model options
     * @returns {string} Cache key
     */
    generateModelKey(modelName, options = {}) {
        const optionsString = JSON.stringify(this._normalizeOptions(options));
        return `model:${modelName}:${this._hashString(optionsString)}`;
    }

    /**
     * Generate cache key for routing decisions
     * @param {Object} context - Routing context
     * @returns {string} Cache key
     */
    generateRoutingKey(context = {}) {
        const normalizedContext = this._normalizeRoutingContext(context);
        const contextString = JSON.stringify(normalizedContext);
        return `routing:${this._hashString(contextString)}`;
    }

    /**
     * Get model from cache
     * @param {string} key - Cache key
     * @returns {Promise<any|null>} Cached model or null
     */
    async getModel(key) {
        this.metrics.totalRequests++;
        
        if (!this.modelCache.has(key)) {
            this.metrics.misses++;
            return null;
        }
        
        const entry = this.modelCache.get(key);
        const metadata = this.cacheMetadata.get(key);
        
        // Check TTL
        if (metadata && this._isExpired(metadata, this.modelInstanceTTL)) {
            this.modelCache.delete(key);
            this.cacheMetadata.delete(key);
            this.metrics.misses++;
            this.logger.debug(`Cache miss (expired): ${key}`);
            return null;
        }
        
        // Update access time
        if (metadata) {
            metadata.lastAccessed = Date.now();
            metadata.accessCount++;
            this.cacheMetadata.set(key, metadata);
        }
        
        this.metrics.hits++;
        this.logger.debug(`Cache hit: ${key}`);
        return entry;
    }

    /**
     * Set model in cache
     * @param {string} key - Cache key
     * @param {any} model - Model instance
     * @param {string} modelName - Model name for metrics
     * @returns {Promise<void>}
     */
    async setModel(key, model, modelName = 'unknown') {
        // Check cache size limit
        if (this.modelCache.size >= this.maxCacheSize) {
            await this._evictOldestEntry();
        }
        
        const metadata = {
            type: 'model',
            modelName,
            createdAt: Date.now(),
            lastAccessed: Date.now(),
            accessCount: 0,
            size: this._estimateSize(model)
        };
        
        this.modelCache.set(key, model);
        this.cacheMetadata.set(key, metadata);
        
        this.logger.debug(`Cache set: ${key} (${modelName})`);
    }

    /**
     * Get routing decision from cache
     * @param {string} key - Cache key
     * @returns {Promise<string|null>} Cached routing decision or null
     */
    async getRoutingDecision(key) {
        this.metrics.totalRequests++;
        
        if (!this.routingCache.has(key)) {
            this.metrics.misses++;
            return null;
        }
        
        const entry = this.routingCache.get(key);
        const metadata = this.cacheMetadata.get(key);
        
        // Check TTL
        if (metadata && this._isExpired(metadata, this.routingDecisionTTL)) {
            this.routingCache.delete(key);
            this.cacheMetadata.delete(key);
            this.metrics.misses++;
            this.logger.debug(`Routing cache miss (expired): ${key}`);
            return null;
        }
        
        // Update access time
        if (metadata) {
            metadata.lastAccessed = Date.now();
            metadata.accessCount++;
            this.cacheMetadata.set(key, metadata);
        }
        
        this.metrics.hits++;
        this.logger.debug(`Routing cache hit: ${key}`);
        return entry;
    }

    /**
     * Set routing decision in cache
     * @param {string} key - Cache key
     * @param {string} decision - Routing decision
     * @returns {Promise<void>}
     */
    async setRoutingDecision(key, decision) {
        const metadata = {
            type: 'routing',
            decision,
            createdAt: Date.now(),
            lastAccessed: Date.now(),
            accessCount: 0,
            size: decision.length
        };
        
        this.routingCache.set(key, decision);
        this.cacheMetadata.set(key, metadata);
        
        this.logger.debug(`Routing cache set: ${key} -> ${decision}`);
    }

    /**
     * Invalidate cache entries by pattern
     * @param {string} pattern - Pattern to match keys
     * @returns {Promise<number>} Number of invalidated entries
     */
    async invalidate(pattern) {
        let invalidated = 0;
        const regex = new RegExp(pattern);
        
        // Check model cache
        for (const key of this.modelCache.keys()) {
            if (regex.test(key)) {
                this.modelCache.delete(key);
                this.cacheMetadata.delete(key);
                invalidated++;
            }
        }
        
        // Check routing cache
        for (const key of this.routingCache.keys()) {
            if (regex.test(key)) {
                this.routingCache.delete(key);
                this.cacheMetadata.delete(key);
                invalidated++;
            }
        }
        
        this.logger.info(`Invalidated ${invalidated} cache entries matching pattern: ${pattern}`);
        return invalidated;
    }

    /**
     * Get cache metrics
     * @returns {Object} Cache metrics
     */
    getMetrics() {
        const totalSize = Array.from(this.cacheMetadata.values())
            .reduce((sum, meta) => sum + (meta.size || 0), 0);
        
        return {
            ...this.metrics,
            hitRate: this.metrics.hits / Math.max(this.metrics.totalRequests, 1),
            missRate: this.metrics.misses / Math.max(this.metrics.totalRequests, 1),
            modelCacheSize: this.modelCache.size,
            routingCacheSize: this.routingCache.size,
            totalCacheSize: this.modelCache.size + this.routingCache.size,
            estimatedMemoryUsage: totalSize,
            maxCacheSize: this.maxCacheSize
        };
    }

    /**
     * Clear all cache entries
     * @returns {Promise<void>}
     */
    async clear() {
        const totalEntries = this.modelCache.size + this.routingCache.size;
        
        this.modelCache.clear();
        this.routingCache.clear();
        this.cacheMetadata.clear();
        
        // Reset metrics
        this.metrics = {
            hits: 0,
            misses: 0,
            evictions: 0,
            totalRequests: 0
        };
        
        this.logger.info(`Cleared ${totalEntries} cache entries`);
    }

    /**
     * Shutdown cache manager
     */
    shutdown() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
        
        this.clear();
        this.logger.info('CacheManager shutdown complete');
    }

    // Private methods

    /**
     * Check if cache entry is expired
     * @private
     */
    _isExpired(metadata, ttl) {
        return Date.now() - metadata.createdAt > ttl;
    }

    /**
     * Clean up expired entries
     * @private
     */
    _cleanupExpiredEntries() {
        let cleanedUp = 0;
        
        // Clean model cache
        for (const [key, metadata] of this.cacheMetadata.entries()) {
            if (metadata.type === 'model' && this._isExpired(metadata, this.modelInstanceTTL)) {
                this.modelCache.delete(key);
                this.cacheMetadata.delete(key);
                cleanedUp++;
            } else if (metadata.type === 'routing' && this._isExpired(metadata, this.routingDecisionTTL)) {
                this.routingCache.delete(key);
                this.cacheMetadata.delete(key);
                cleanedUp++;
            }
        }
        
        if (cleanedUp > 0) {
            this.logger.debug(`Cleaned up ${cleanedUp} expired cache entries`);
        }
    }

    /**
     * Evict oldest entry to make space
     * @private
     */
    async _evictOldestEntry() {
        let oldestKey = null;
        let oldestTime = Date.now();
        
        // Find oldest entry by last access time
        for (const [key, metadata] of this.cacheMetadata.entries()) {
            if (metadata.type === 'model' && metadata.lastAccessed < oldestTime) {
                oldestTime = metadata.lastAccessed;
                oldestKey = key;
            }
        }
        
        if (oldestKey) {
            this.modelCache.delete(oldestKey);
            this.cacheMetadata.delete(oldestKey);
            this.metrics.evictions++;
            this.logger.debug(`Evicted oldest cache entry: ${oldestKey}`);
        }
    }

    /**
     * Normalize options for consistent cache keys
     * @private
     */
    _normalizeOptions(options) {
        const normalized = { ...options };
        
        // Remove non-cacheable options
        delete normalized.logger;
        delete normalized.callbacks;
        delete normalized.onProgress;
        
        // Sort object keys for consistent stringification
        return this._sortObjectKeys(normalized);
    }

    /**
     * Normalize routing context for consistent cache keys
     * @private
     */
    _normalizeRoutingContext(context) {
        const normalized = {
            taskType: context.taskType || 'general',
            urgency: context.urgency || 'medium',
            complexity: context.complexity || 'medium',
            requiresTools: context.requiresTools || false,
            costSensitive: context.costSensitive || false
        };
        
        return this._sortObjectKeys(normalized);
    }

    /**
     * Sort object keys recursively for consistent hashing
     * @private
     */
    _sortObjectKeys(obj) {
        if (typeof obj !== 'object' || obj === null) {
            return obj;
        }
        
        if (Array.isArray(obj)) {
            return obj.map(item => this._sortObjectKeys(item));
        }
        
        const sorted = {};
        Object.keys(obj).sort().forEach(key => {
            sorted[key] = this._sortObjectKeys(obj[key]);
        });
        
        return sorted;
    }

    /**
     * Simple hash function for cache keys
     * @private
     */
    _hashString(str) {
        let hash = 0;
        if (str.length === 0) return hash;
        
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        
        return Math.abs(hash).toString(36);
    }

    /**
     * Estimate object size in bytes
     * @private
     */
    _estimateSize(obj) {
        if (typeof obj === 'string') {
            return obj.length * 2; // Approximate UTF-16 encoding
        }
        
        if (typeof obj === 'number') {
            return 8; // 64-bit number
        }
        
        if (typeof obj === 'boolean') {
            return 4;
        }
        
        if (obj === null || obj === undefined) {
            return 0;
        }
        
        // For complex objects, estimate based on JSON size
        try {
            return JSON.stringify(obj).length * 2;
        } catch (error) {
            return 1000; // Default estimate for complex objects
        }
    }
}

export default CacheManager;