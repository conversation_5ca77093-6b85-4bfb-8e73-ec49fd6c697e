/**
 * Intelligent Router
 * Performance-based model selection and routing with fallback chains
 * Implements Week 2-3 optimization: centralized routing with intelligent model selection
 */

import { createLogger, LogLevel } from '@/utils/logger';

export class IntelligentRouter {
    constructor(options = {}) {
        this.modelConfigs = options.modelConfigs || {};
        this.performanceTargets = options.performanceTargets || {
            maxResponseTime: 600,
            optimalResponseTime: 400,
            maxErrorRate: 0.05
        };
        this.logger = options.logger || createLogger('IntelligentRouter', LogLevel.INFO);
        
        // Historical performance tracking
        this.performanceHistory = new Map();
        
        // Routing metrics
        this.metrics = {
            routingDecisions: 0,
            fallbackUsed: 0,
            successfulRoutes: 0,
            failedRoutes: 0
        };
        
        this.logger.info('IntelligentRouter initialized with model configurations');
    }

    /**
     * Select optimal model based on context and historical performance
     * @param {Object} context - Routing context
     * @returns {Promise<string>} Selected model name
     */
    async selectOptimalModel(context = {}) {
        const startTime = Date.now();
        this.metrics.routingDecisions++;
        
        try {
            const candidates = this._getCandidateModels(context);
            const scored = await this._scoreModels(candidates, context);
            const selected = this._selectBestModel(scored);
            
            const routingTime = Date.now() - startTime;
            this.logger.debug(`Model routing completed in ${routingTime}ms: ${selected}`, {
                context: this._sanitizeContext(context),
                candidates: candidates.length,
                selected
            });
            
            this.metrics.successfulRoutes++;
            return selected;
            
        } catch (error) {
            this.metrics.failedRoutes++;
            this.logger.error('Model routing failed:', error.message);
            
            // Fallback to default model
            return this._getDefaultModel(context);
        }
    }

    /**
     * Get fallback chain for failed model
     * @param {string} failedModel - Model that failed
     * @param {Object} options - Fallback options
     * @returns {Promise<Array<string>>} Fallback model chain
     */
    async getFallbackChain(failedModel, options = {}) {
        const context = {
            failedModel,
            urgency: options.urgency || 'medium',
            requiresTools: options.requiresTools || false,
            taskType: options.taskType || 'general'
        };
        
        const allModels = Object.keys(this.modelConfigs);
        const availableModels = allModels.filter(model => model !== failedModel);
        
        // Sort by reliability and performance
        const fallbackChain = availableModels
            .map(model => ({
                model,
                config: this.modelConfigs[model],
                score: this._calculateReliabilityScore(model, context)
            }))
            .sort((a, b) => b.score - a.score)
            .map(item => item.model)
            .slice(0, 3); // Limit to top 3 fallbacks
        
        this.metrics.fallbackUsed++;
        this.logger.debug(`Generated fallback chain for ${failedModel}:`, fallbackChain);
        
        return fallbackChain;
    }

    /**
     * Get model configuration
     * @param {string} modelName - Model name
     * @returns {Object|null} Model configuration
     */
    getModelConfig(modelName) {
        return this.modelConfigs[modelName] || null;
    }

    /**
     * Update model performance data
     * @param {string} modelName - Model name
     * @param {Object} performance - Performance data
     */
    updateModelPerformance(modelName, performance) {
        if (!this.performanceHistory.has(modelName)) {
            this.performanceHistory.set(modelName, {
                responseTime: [],
                errorRate: 0,
                successCount: 0,
                failureCount: 0,
                lastUpdated: Date.now()
            });
        }
        
        const history = this.performanceHistory.get(modelName);
        
        if (performance.responseTime) {
            history.responseTime.push(performance.responseTime);
            // Keep only last 100 measurements
            if (history.responseTime.length > 100) {
                history.responseTime = history.responseTime.slice(-100);
            }
        }
        
        if (performance.success !== undefined) {
            if (performance.success) {
                history.successCount++;
            } else {
                history.failureCount++;
            }
            
            const totalRequests = history.successCount + history.failureCount;
            history.errorRate = history.failureCount / totalRequests;
        }
        
        history.lastUpdated = Date.now();
        this.performanceHistory.set(modelName, history);
    }

    /**
     * Get routing metrics
     * @returns {Object} Routing metrics
     */
    getMetrics() {
        return {
            ...this.metrics,
            modelPerformance: this._getModelPerformanceSummary(),
            routingSuccessRate: this.metrics.successfulRoutes / Math.max(this.metrics.routingDecisions, 1),
            fallbackRate: this.metrics.fallbackUsed / Math.max(this.metrics.routingDecisions, 1)
        };
    }

    /**
     * Reset router state
     */
    reset() {
        this.performanceHistory.clear();
        this.metrics = {
            routingDecisions: 0,
            fallbackUsed: 0,
            successfulRoutes: 0,
            failedRoutes: 0
        };
        this.logger.info('IntelligentRouter reset');
    }

    // Private methods

    /**
     * Get candidate models based on context
     * @private
     */
    _getCandidateModels(context) {
        const allModels = Object.keys(this.modelConfigs);
        
        return allModels.filter(modelName => {
            const config = this.modelConfigs[modelName];
            
            // Filter by capabilities
            if (context.requiresTools && !config.capabilities.includes('function_calling') && !config.capabilities.includes('autonomous_tools')) {
                return false;
            }
            
            // Filter by task type
            if (context.taskType && this._isTaskCompatible(context.taskType, config.capabilities)) {
                return true;
            }
            
            return true;
        });
    }

    /**
     * Score models based on context and performance
     * @private
     */
    async _scoreModels(candidates, context) {
        return candidates.map(modelName => {
            const config = this.modelConfigs[modelName];
            const history = this.performanceHistory.get(modelName);
            
            let score = 0;
            
            // Base quality score
            score += config.performance.qualityScore * 30;
            
            // Performance score
            const expectedTime = config.performance.expectedResponseTime;
            const targetTime = this._getTargetResponseTime(context);
            
            if (expectedTime <= targetTime) {
                score += 25; // Meets timing requirements
            } else {
                score -= (expectedTime - targetTime) / 10; // Penalty for slowness
            }
            
            // Cost efficiency score
            if (context.costSensitive) {
                score += (2.0 - config.performance.costMultiplier) * 15;
            }
            
            // Historical performance bonus
            if (history) {
                const avgResponseTime = this._calculateAverageResponseTime(history.responseTime);
                if (avgResponseTime > 0 && avgResponseTime < expectedTime) {
                    score += 10; // Bonus for better than expected performance
                }
                
                if (history.errorRate < this.performanceTargets.maxErrorRate) {
                    score += 15; // Bonus for low error rate
                }
            }
            
            // Urgency adjustment
            if (context.urgency === 'high' && config.capabilities.includes('fast_response')) {
                score += 20;
            }
            
            return {
                model: modelName,
                score: Math.max(score, 0),
                config,
                history
            };
        });
    }

    /**
     * Select best model from scored candidates
     * @private
     */
    _selectBestModel(scoredModels) {
        if (scoredModels.length === 0) {
            return this._getDefaultModel();
        }
        
        // Sort by score descending
        scoredModels.sort((a, b) => b.score - a.score);
        
        this.logger.debug('Model scoring results:', scoredModels.map(m => ({
            model: m.model,
            score: Math.round(m.score * 100) / 100
        })));
        
        return scoredModels[0].model;
    }

    /**
     * Get default model based on context
     * @private
     */
    _getDefaultModel(context = {}) {
        // Return a reasonable default based on context
        if (context.requiresTools || context.taskType === 'autonomous_tools') {
            return 'qwen-plus'; // Good balance for tool calling
        }
        
        if (context.urgency === 'high') {
            return 'qwen-turbo'; // Fastest option
        }
        
        return 'qwen-plus'; // Balanced default
    }

    /**
     * Check if task is compatible with model capabilities
     * @private
     */
    _isTaskCompatible(taskType, capabilities) {
        const taskCapabilityMap = {
            'autonomous_tools': ['autonomous_tools', 'function_calling'],
            'complex_analysis': ['complex_reasoning', 'high_accuracy'],
            'fast_response': ['fast_response', 'basic_reasoning'],
            'voice_processing': ['voice_processing', 'real_time']
        };
        
        const requiredCapabilities = taskCapabilityMap[taskType] || [];
        return requiredCapabilities.some(cap => capabilities.includes(cap));
    }

    /**
     * Get target response time based on context
     * @private
     */
    _getTargetResponseTime(context) {
        if (context.urgency === 'high') {
            return this.performanceTargets.optimalResponseTime;
        }
        
        if (context.taskType === 'autonomous_tools') {
            return 450; // Sub-600ms target with margin
        }
        
        return this.performanceTargets.maxResponseTime;
    }

    /**
     * Calculate reliability score for fallback selection
     * @private
     */
    _calculateReliabilityScore(modelName, context) {
        const config = this.modelConfigs[modelName];
        const history = this.performanceHistory.get(modelName);
        
        let score = config.performance.qualityScore * 50;
        
        if (history) {
            score += (1 - history.errorRate) * 30;
            
            const avgTime = this._calculateAverageResponseTime(history.responseTime);
            if (avgTime > 0) {
                const timeScore = Math.max(0, (1000 - avgTime) / 1000) * 20;
                score += timeScore;
            }
        }
        
        return score;
    }

    /**
     * Calculate average response time
     * @private
     */
    _calculateAverageResponseTime(responseTimes) {
        if (!responseTimes || responseTimes.length === 0) {
            return 0;
        }
        
        return responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    }

    /**
     * Get model performance summary
     * @private
     */
    _getModelPerformanceSummary() {
        const summary = {};
        
        for (const [modelName, history] of this.performanceHistory.entries()) {
            summary[modelName] = {
                averageResponseTime: this._calculateAverageResponseTime(history.responseTime),
                errorRate: history.errorRate,
                totalRequests: history.successCount + history.failureCount,
                lastUpdated: history.lastUpdated
            };
        }
        
        return summary;
    }

    /**
     * Sanitize context for logging
     * @private
     */
    _sanitizeContext(context) {
        const sanitized = { ...context };
        // Remove sensitive information
        delete sanitized.apiKey;
        delete sanitized.credentials;
        return sanitized;
    }
}

export default IntelligentRouter;