/**
 * Performance Coordinator
 * 
 * Coordinates between streaming-specific optimization (PerformanceOptimizer)
 * and infrastructure-level metrics (PerformanceTracker) for unified performance management
 * 
 * Architecture:
 * - PerformanceOptimizer: Adaptive streaming configuration
 * - PerformanceTracker: Comprehensive metrics collection  
 * - PerformanceCoordinator: Integration and coordination layer
 */

import { createLogger } from '@/utils/logger';

export class PerformanceCoordinator {
    constructor(options = {}) {
        this.logger = createLogger('PerformanceCoordinator');
        this.options = {
            targetLatency: 600,           // Sub-600ms response time target
            optimizationInterval: 30000,  // 30 seconds
            metricsWindow: 300000,        // 5 minutes
            enableAdaptiveOptimization: true,
            ...options
        };

        // Component references
        this.performanceOptimizer = null;
        this.performanceTracker = null;

        // Coordination state
        this.optimizationHistory = [];
        this.lastOptimization = 0;
        this.coordinationMetrics = {
            totalOptimizations: 0,
            successfulOptimizations: 0,
            averageImprovement: 0
        };

        // Auto-optimization timer
        this.optimizationTimer = null;

        this.logger.info('PerformanceCoordinator initialized', {
            targetLatency: this.options.targetLatency,
            optimizationInterval: this.options.optimizationInterval
        });
    }

    /**
     * Initialize with component instances
     */
    initialize(performanceOptimizer, performanceTracker) {
        this.performanceOptimizer = performanceOptimizer;
        this.performanceTracker = performanceTracker;

        if (this.options.enableAdaptiveOptimization) {
            this.startAdaptiveOptimization();
        }

        this.logger.info('PerformanceCoordinator initialized with components', {
            hasOptimizer: !!this.performanceOptimizer,
            hasTracker: !!this.performanceTracker
        });
    }

    /**
     * Get unified performance metrics from both systems
     */
    getUnifiedMetrics() {
        const optimizerMetrics = this.performanceOptimizer?.getMetrics() || {};
        const trackerMetrics = this.performanceTracker?.getMetrics() || {};

        return {
            // Combined metrics
            unified: {
                responseTime: trackerMetrics.recent?.averageResponseTime || optimizerMetrics.avgResponseTime || 0,
                throughput: trackerMetrics.recent?.averageThroughput || optimizerMetrics.throughput || 0,
                successRate: trackerMetrics.global?.successRate || optimizerMetrics.successRate || 1,
                errorRate: trackerMetrics.global?.failureRate || (1 - optimizerMetrics.successRate) || 0,

                // Performance vs targets
                meetsLatencyTarget: (trackerMetrics.recent?.averageResponseTime || optimizerMetrics.avgResponseTime || 0) <= this.options.targetLatency,
                performanceGrade: this.calculatePerformanceGrade(trackerMetrics, optimizerMetrics)
            },

            // Individual component metrics
            optimizer: optimizerMetrics,
            tracker: trackerMetrics,

            // Coordination metrics
            coordination: {
                ...this.coordinationMetrics,
                recentOptimizations: this.optimizationHistory.slice(-5),
                lastOptimization: this.lastOptimization,
                nextOptimization: this.lastOptimization + this.options.optimizationInterval
            },

            // Recommendations
            recommendations: this.generateUnifiedRecommendations(trackerMetrics, optimizerMetrics)
        };
    }

    /**
     * Compatibility: expose getMetrics() used by infrastructure metrics aggregators
     */
    getMetrics() {
        return this.getUnifiedMetrics();
    }

    /**
     * Perform coordinated optimization
     */
    async performCoordinatedOptimization() {
        try {
            const startTime = Date.now();

            // Get current metrics from both systems
            const trackerMetrics = this.performanceTracker?.getMetrics();
            const optimizerMetrics = this.performanceOptimizer?.getMetrics();

            if (!trackerMetrics || !optimizerMetrics) {
                this.logger.warn('Cannot perform optimization - missing component metrics');
                return false;
            }

            // Calculate current performance status
            const currentResponseTime = trackerMetrics.recent?.averageResponseTime || optimizerMetrics.avgResponseTime;
            const currentSuccessRate = trackerMetrics.global?.successRate || optimizerMetrics.successRate;

            this.logger.info('Starting coordinated optimization', {
                currentResponseTime,
                currentSuccessRate,
                targetLatency: this.options.targetLatency
            });

            // Perform optimization using PerformanceOptimizer
            const optimizedConfig = this.performanceOptimizer.optimizeStreamingConfig({
                avgResponseTime: currentResponseTime,
                avgChunkLatency: optimizerMetrics.avgChunkLatency,
                successRate: currentSuccessRate
            });

            // Record optimization
            const optimization = {
                timestamp: Date.now(),
                duration: Date.now() - startTime,
                beforeMetrics: {
                    responseTime: currentResponseTime,
                    successRate: currentSuccessRate
                },
                appliedConfig: optimizedConfig,
                success: true
            };

            this.optimizationHistory.push(optimization);

            // Maintain history size
            if (this.optimizationHistory.length > 50) {
                this.optimizationHistory = this.optimizationHistory.slice(-50);
            }

            // Update coordination metrics
            this.coordinationMetrics.totalOptimizations++;
            this.coordinationMetrics.successfulOptimizations++;
            this.lastOptimization = Date.now();

            this.logger.info('Coordinated optimization completed', {
                duration: optimization.duration,
                optimizedConfig
            });

            return optimizedConfig;

        } catch (error) {
            this.logger.error('Coordinated optimization failed:', error);

            // Record failed optimization
            this.optimizationHistory.push({
                timestamp: Date.now(),
                success: false,
                error: error.message
            });

            this.coordinationMetrics.totalOptimizations++;

            return false;
        }
    }

    /**
     * Start adaptive optimization timer
     */
    startAdaptiveOptimization() {
        if (this.optimizationTimer) {
            clearInterval(this.optimizationTimer);
        }

        this.optimizationTimer = setInterval(async () => {
            await this.performCoordinatedOptimization();
        }, this.options.optimizationInterval);

        this.logger.info('Adaptive optimization started', {
            interval: this.options.optimizationInterval
        });
    }

    /**
     * Stop adaptive optimization
     */
    stopAdaptiveOptimization() {
        if (this.optimizationTimer) {
            clearInterval(this.optimizationTimer);
            this.optimizationTimer = null;
        }

        this.logger.info('Adaptive optimization stopped');
    }

    /**
     * Monitor streaming session performance
     */
    monitorStreamingSession(sessionId, streamingManager) {
        if (!this.performanceOptimizer) {
            this.logger.warn('Cannot monitor session - PerformanceOptimizer not available');
            return null;
        }

        // Get monitoring callbacks from PerformanceOptimizer
        const monitor = this.performanceOptimizer.startPerformanceMonitoring(sessionId);

        // Enhance with coordination tracking
        const enhancedMonitor = {
            onChunk: (chunk) => {
                monitor.onChunk(chunk);

                // Additional coordination tracking
                if (this.performanceTracker) {
                    this.performanceTracker.recordResponseTime(Date.now() - (chunk.timestamp || Date.now()));
                }
            },

            onComplete: (result) => {
                monitor.onComplete(result);

                // Check if optimization needed
                const metrics = this.getUnifiedMetrics();
                if (!metrics.unified.meetsLatencyTarget) {
                    this.logger.warn('Session exceeded latency target, scheduling optimization', {
                        sessionId,
                        responseTime: result.totalDuration || 0,
                        target: this.options.targetLatency
                    });

                    // Schedule immediate optimization
                    setTimeout(() => this.performCoordinatedOptimization(), 1000);
                }
            },

            onError: (error) => {
                monitor.onError(error);

                // Record error in tracker
                if (this.performanceTracker) {
                    this.performanceTracker.recordModelFailure('streaming_session', 0, error);
                }
            }
        };

        return enhancedMonitor;
    }

    /**
     * Create optimized stream with coordinated monitoring
     */
    createOptimizedStream(baseStream, sessionId) {
        if (!this.performanceOptimizer) {
            this.logger.warn('Cannot create optimized stream - PerformanceOptimizer not available');
            return baseStream;
        }

        // Create optimized stream with monitoring
        const optimizedStream = this.performanceOptimizer.createOptimizedStream(baseStream, sessionId);

        // Add coordination tracking
        return {
            async*[Symbol.asyncIterator]() {
                const startTime = Date.now();
                let chunkCount = 0;

                try {
                    for await (const chunk of optimizedStream) {
                        chunkCount++;

                        // Track in PerformanceTracker
                        if (this.performanceTracker && chunkCount === 1) {
                            this.performanceTracker.recordModelCreation('optimized_stream', Date.now() - startTime);
                        }

                        yield chunk;
                    }

                    // Record successful completion
                    const totalDuration = Date.now() - startTime;
                    if (this.performanceTracker) {
                        this.performanceTracker.recordToolExecution('optimized_streaming', totalDuration, true, {
                            sessionId,
                            chunkCount
                        });
                    }

                } catch (error) {
                    // Record error
                    if (this.performanceTracker) {
                        this.performanceTracker.recordToolExecution('optimized_streaming', Date.now() - startTime, false, {
                            sessionId,
                            error: error.message
                        });
                    }
                    throw error;
                }
            }
        };
    }

    /**
     * Calculate overall performance grade
     */
    calculatePerformanceGrade(trackerMetrics, optimizerMetrics) {
        const responseTime = trackerMetrics?.recent?.averageResponseTime || optimizerMetrics?.avgResponseTime || 1000;
        const successRate = trackerMetrics?.global?.successRate || optimizerMetrics?.successRate || 0;
        const throughput = trackerMetrics?.recent?.averageThroughput || optimizerMetrics?.throughput || 0;

        let score = 0;

        // Response time scoring (40% weight)
        if (responseTime <= 300) score += 40;
        else if (responseTime <= 600) score += 30;
        else if (responseTime <= 1000) score += 20;
        else score += 10;

        // Success rate scoring (40% weight)
        score += successRate * 40;

        // Throughput scoring (20% weight)
        if (throughput >= 20) score += 20;
        else if (throughput >= 10) score += 15;
        else if (throughput >= 5) score += 10;
        else score += 5;

        // Convert to letter grade
        if (score >= 90) return 'A+';
        if (score >= 85) return 'A';
        if (score >= 80) return 'A-';
        if (score >= 75) return 'B+';
        if (score >= 70) return 'B';
        if (score >= 65) return 'B-';
        if (score >= 60) return 'C+';
        if (score >= 55) return 'C';
        return 'C-';
    }

    /**
     * Generate unified recommendations
     */
    generateUnifiedRecommendations(trackerMetrics, optimizerMetrics) {
        const recommendations = [];

        // Get recommendations from both systems
        const optimizerRecs = optimizerMetrics?.recommendations || [];
        const trackerRecs = trackerMetrics?.health?.recommendations || [];

        // Combine and deduplicate
        const allRecs = [...optimizerRecs, ...trackerRecs];
        const uniqueRecs = allRecs.filter((rec, index, arr) =>
            arr.findIndex(r => r.type === rec.type) === index
        );

        // Add coordination-specific recommendations
        const responseTime = trackerMetrics?.recent?.averageResponseTime || optimizerMetrics?.avgResponseTime;
        if (responseTime > this.options.targetLatency) {
            recommendations.push({
                type: 'coordination',
                priority: 'high',
                message: 'Enable adaptive optimization for automatic performance tuning',
                action: 'startAdaptiveOptimization'
            });
        }

        if (this.coordinationMetrics.totalOptimizations > 0) {
            const successRate = this.coordinationMetrics.successfulOptimizations / this.coordinationMetrics.totalOptimizations;
            if (successRate < 0.8) {
                recommendations.push({
                    type: 'coordination',
                    priority: 'medium',
                    message: 'Low optimization success rate detected - review component configuration',
                    action: 'reviewConfiguration'
                });
            }
        }

        return [...uniqueRecs, ...recommendations];
    }

    /**
     * Get coordination status
     */
    getStatus() {
        return {
            isActive: !!this.optimizationTimer,
            hasOptimizer: !!this.performanceOptimizer,
            hasTracker: !!this.performanceTracker,
            lastOptimization: this.lastOptimization,
            totalOptimizations: this.coordinationMetrics.totalOptimizations,
            successRate: this.coordinationMetrics.totalOptimizations > 0
                ? this.coordinationMetrics.successfulOptimizations / this.coordinationMetrics.totalOptimizations
                : 1
        };
    }

    /**
     * Reset coordination state
     */
    reset() {
        this.optimizationHistory = [];
        this.coordinationMetrics = {
            totalOptimizations: 0,
            successfulOptimizations: 0,
            averageImprovement: 0
        };
        this.lastOptimization = 0;

        this.logger.info('PerformanceCoordinator state reset');
    }

    /**
     * Shutdown coordinator
     */
    shutdown() {
        this.stopAdaptiveOptimization();
        this.reset();

        this.logger.info('PerformanceCoordinator shutdown complete');
    }
}

export default PerformanceCoordinator;