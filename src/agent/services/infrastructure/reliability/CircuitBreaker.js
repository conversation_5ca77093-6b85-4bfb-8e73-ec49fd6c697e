/**
 * Circuit Breaker
 * Implements circuit breaker pattern for service reliability
 * Implements Week 2-3 optimization: circuit breaker pattern in BaseToolNode.js
 */

import { createLogger, LogLevel } from '@/utils/logger';

// Circuit breaker states
const STATES = {
    CLOSED: 'CLOSED',     // Normal operation
    OPEN: 'OPEN',         // Circuit is open, failing fast
    HALF_OPEN: 'HALF_OPEN' // Testing if service is back
};

export class CircuitBreaker {
    constructor(options = {}) {
        this.failureThreshold = options.failureThreshold || 5;
        this.recoveryTimeout = options.recoveryTimeout || 30000; // 30 seconds
        this.halfOpenRetries = options.halfOpenRetries || 3;
        this.logger = options.logger || createLogger('CircuitBreaker', LogLevel.INFO);
        
        // Circuit state per service
        this.circuits = new Map();
        
        // Global metrics
        this.metrics = {
            totalCalls: 0,
            successfulCalls: 0,
            failedCalls: 0,
            circuitTrips: 0,
            fastFailures: 0
        };
        
        this.logger.info('CircuitBreaker initialized with fault tolerance configuration');
    }

    /**
     * Record successful operation
     * @param {string} serviceName - Service identifier
     */
    recordSuccess(serviceName) {
        const circuit = this._getCircuit(serviceName);
        
        circuit.consecutiveFailures = 0;
        circuit.successCount++;
        circuit.lastSuccessTime = Date.now();
        
        // Transition from HALF_OPEN to CLOSED if enough successes
        if (circuit.state === STATES.HALF_OPEN && circuit.halfOpenSuccesses >= this.halfOpenRetries) {
            this._transitionTo(serviceName, STATES.CLOSED);
            this.logger.info(`Circuit breaker CLOSED for ${serviceName} after successful recovery`);
        } else if (circuit.state === STATES.HALF_OPEN) {
            circuit.halfOpenSuccesses++;
        }
        
        this.metrics.totalCalls++;
        this.metrics.successfulCalls++;
        
        this._updateCircuit(serviceName, circuit);
    }

    /**
     * Record failed operation
     * @param {string} serviceName - Service identifier
     * @param {Error} error - Error that occurred (optional)
     */
    recordFailure(serviceName, error = null) {
        const circuit = this._getCircuit(serviceName);
        
        circuit.consecutiveFailures++;
        circuit.failureCount++;
        circuit.lastFailureTime = Date.now();
        
        if (error) {
            circuit.lastError = error.message;
        }
        
        // Reset half-open success count on any failure
        if (circuit.state === STATES.HALF_OPEN) {
            circuit.halfOpenSuccesses = 0;
            this._transitionTo(serviceName, STATES.OPEN);
            this.logger.warn(`Circuit breaker reopened for ${serviceName} due to failure during half-open state`);
        }
        
        // Check if we should trip the circuit
        if (circuit.state === STATES.CLOSED && circuit.consecutiveFailures >= this.failureThreshold) {
            this._transitionTo(serviceName, STATES.OPEN);
            this.metrics.circuitTrips++;
            this.logger.warn(`Circuit breaker OPENED for ${serviceName} after ${circuit.consecutiveFailures} consecutive failures`);
        }
        
        this.metrics.totalCalls++;
        this.metrics.failedCalls++;
        
        this._updateCircuit(serviceName, circuit);
    }

    /**
     * Check if circuit is open (should fail fast)
     * @param {string} serviceName - Service identifier
     * @returns {boolean} True if circuit is open
     */
    isOpen(serviceName) {
        const circuit = this._getCircuit(serviceName);
        
        if (circuit.state === STATES.CLOSED) {
            return false;
        }
        
        if (circuit.state === STATES.OPEN) {
            // Check if recovery timeout has passed
            if (Date.now() - circuit.lastFailureTime >= this.recoveryTimeout) {
                this._transitionTo(serviceName, STATES.HALF_OPEN);
                this.logger.info(`Circuit breaker transitioned to HALF_OPEN for ${serviceName}`);
                return false; // Allow one attempt
            }
            return true;
        }
        
        // HALF_OPEN state - allow limited attempts
        return false;
    }

    /**
     * Execute function with circuit breaker protection
     * @param {string} serviceName - Service identifier
     * @param {Function} fn - Function to execute
     * @returns {Promise<any>} Function result
     */
    async execute(serviceName, fn) {
        if (this.isOpen(serviceName)) {
            this.metrics.fastFailures++;
            throw new Error(`Circuit breaker is OPEN for ${serviceName}`);
        }
        
        try {
            const result = await fn();
            this.recordSuccess(serviceName);
            return result;
        } catch (error) {
            this.recordFailure(serviceName, error);
            throw error;
        }
    }

    /**
     * Get circuit state for service
     * @param {string} serviceName - Service identifier
     * @returns {string} Circuit state
     */
    getState(serviceName) {
        const circuit = this._getCircuit(serviceName);
        return circuit.state;
    }

    /**
     * Get service health information
     * @param {string} serviceName - Service identifier
     * @returns {Object} Health information
     */
    getHealthInfo(serviceName) {
        const circuit = this._getCircuit(serviceName);
        
        return {
            serviceName,
            state: circuit.state,
            consecutiveFailures: circuit.consecutiveFailures,
            successCount: circuit.successCount,
            failureCount: circuit.failureCount,
            lastSuccessTime: circuit.lastSuccessTime,
            lastFailureTime: circuit.lastFailureTime,
            lastError: circuit.lastError,
            failureRate: circuit.failureCount / Math.max(circuit.successCount + circuit.failureCount, 1),
            isHealthy: circuit.state === STATES.CLOSED && circuit.consecutiveFailures === 0
        };
    }

    /**
     * Force circuit to specific state (for testing/manual intervention)
     * @param {string} serviceName - Service identifier
     * @param {string} state - Target state
     */
    forceState(serviceName, state) {
        if (!Object.values(STATES).includes(state)) {
            throw new Error(`Invalid circuit breaker state: ${state}`);
        }
        
        this._transitionTo(serviceName, state);
        this.logger.warn(`Circuit breaker for ${serviceName} manually set to ${state}`);
    }

    /**
     * Reset circuit breaker for service
     * @param {string} serviceName - Service identifier
     */
    reset(serviceName = null) {
        if (serviceName) {
            this.circuits.delete(serviceName);
            this.logger.info(`Circuit breaker reset for ${serviceName}`);
        } else {
            this.circuits.clear();
            this.metrics = {
                totalCalls: 0,
                successfulCalls: 0,
                failedCalls: 0,
                circuitTrips: 0,
                fastFailures: 0
            };
            this.logger.info('All circuit breakers reset');
        }
    }

    /**
     * Get comprehensive metrics
     * @returns {Object} Circuit breaker metrics
     */
    getMetrics() {
        const serviceMetrics = {};
        
        for (const [serviceName] of this.circuits.entries()) {
            serviceMetrics[serviceName] = this.getHealthInfo(serviceName);
        }
        
        return {
            global: {
                ...this.metrics,
                successRate: this.metrics.successfulCalls / Math.max(this.metrics.totalCalls, 1),
                failureRate: this.metrics.failedCalls / Math.max(this.metrics.totalCalls, 1)
            },
            services: serviceMetrics,
            summary: {
                totalServices: this.circuits.size,
                healthyServices: Array.from(this.circuits.values()).filter(c => c.state === STATES.CLOSED).length,
                openCircuits: Array.from(this.circuits.values()).filter(c => c.state === STATES.OPEN).length,
                halfOpenCircuits: Array.from(this.circuits.values()).filter(c => c.state === STATES.HALF_OPEN).length
            }
        };
    }

    // Private methods

    /**
     * Get or create circuit for service
     * @private
     */
    _getCircuit(serviceName) {
        if (!this.circuits.has(serviceName)) {
            const circuit = {
                state: STATES.CLOSED,
                consecutiveFailures: 0,
                successCount: 0,
                failureCount: 0,
                lastSuccessTime: null,
                lastFailureTime: null,
                lastError: null,
                halfOpenSuccesses: 0,
                createdAt: Date.now()
            };
            
            this.circuits.set(serviceName, circuit);
            this.logger.debug(`Created new circuit breaker for ${serviceName}`);
        }
        
        return this.circuits.get(serviceName);
    }

    /**
     * Update circuit state
     * @private
     */
    _updateCircuit(serviceName, circuit) {
        circuit.updatedAt = Date.now();
        this.circuits.set(serviceName, circuit);
    }

    /**
     * Transition circuit to new state
     * @private
     */
    _transitionTo(serviceName, newState) {
        const circuit = this._getCircuit(serviceName);
        const oldState = circuit.state;
        
        circuit.state = newState;
        circuit.stateChangedAt = Date.now();
        
        // Reset counters based on state transition
        if (newState === STATES.CLOSED) {
            circuit.consecutiveFailures = 0;
            circuit.halfOpenSuccesses = 0;
        } else if (newState === STATES.HALF_OPEN) {
            circuit.halfOpenSuccesses = 0;
        }
        
        this._updateCircuit(serviceName, circuit);
        
        if (oldState !== newState) {
            this.logger.info(`Circuit breaker for ${serviceName}: ${oldState} -> ${newState}`);
        }
    }
}

export { STATES as CIRCUIT_STATES };
export default CircuitBreaker;