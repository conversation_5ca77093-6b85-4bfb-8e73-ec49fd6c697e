/**
 * Infrastructure Manager
 * Centralized management of all infrastructure services
 * Eliminates redundant initialization across DualBrainCoordinator, core.js, and ModelFactory
 */

import { createLogger, LogLevel } from '../../../utils/logger.js';

export class InfrastructureManager {
    constructor(options = {}) {
        this.logger = createLogger('InfrastructureManager', LogLevel.DEBUG);
        this.options = {
            // Performance Tracker defaults
            performanceTracker: {
                historySize: 100,
                metricsWindow: 300000, // 5 minutes
                ...options.performanceTracker
            },

            // Circuit Breaker defaults
            circuitBreaker: {
                failureThreshold: 5,
                recoveryTimeout: 30000,
                halfOpenRetries: 3,
                ...options.circuitBreaker
            },

            // Cache Manager defaults
            cacheManager: {
                maxCacheSize: 1000,
                defaultTTL: 300000, // 5 minutes
                modelInstanceTTL: 300000,
                routingDecisionTTL: 60000,
                ...options.cacheManager
            },

            // Performance Coordinator defaults
            performanceCoordinator: {
                targetLatency: 600,
                adaptiveThrottling: true,
                ...options.performanceCoordinator
            }
        };

        // Service instances (lazily initialized)
        this._performanceTracker = null;
        this._circuitBreaker = null;
        this._cacheManager = null;
        this._performanceCoordinator = null;
        this._router = null;

        // Cached service references for reuse
        this._serviceCache = new Map();

        this.logger.info('Infrastructure Manager created');
    }

    /**
     * Get or create PerformanceTracker instance
     * @param {Object} overrideOptions - Override default options
     * @returns {PerformanceTracker} Shared instance
     */
    async getPerformanceTracker(overrideOptions = {}) {
        const cacheKey = 'performanceTracker';

        if (this._serviceCache.has(cacheKey) && !overrideOptions.forceNew) {
            return this._serviceCache.get(cacheKey);
        }

        try {
            const { PerformanceMonitor } = await import('../monitoring/PerformanceMonitor.js');

            const options = {
                ...this.options.performanceTracker,
                ...overrideOptions,
                logger: this.logger
            };

            const instance = new PerformanceMonitor(options);
            this._serviceCache.set(cacheKey, instance);

            this.logger.debug('✅ PerformanceMonitor instance created and cached');
            return instance;

        } catch (error) {
            this.logger.warn('Failed to create PerformanceMonitor, using fallback:', error);
            return this._createFallbackPerformanceTracker();
        }
    }

    /**
     * Get or create CircuitBreaker instance
     * @param {Object} overrideOptions - Override default options
     * @returns {CircuitBreaker} Shared instance
     */
    async getCircuitBreaker(overrideOptions = {}) {
        const cacheKey = 'circuitBreaker';

        if (this._serviceCache.has(cacheKey) && !overrideOptions.forceNew) {
            return this._serviceCache.get(cacheKey);
        }

        try {
            const { CircuitBreaker } = await import('./reliability/CircuitBreaker.js');

            const options = {
                ...this.options.circuitBreaker,
                ...overrideOptions,
                logger: this.logger
            };

            const instance = new CircuitBreaker(options);
            this._serviceCache.set(cacheKey, instance);

            this.logger.debug('✅ CircuitBreaker instance created and cached');
            return instance;

        } catch (error) {
            this.logger.warn('Failed to create CircuitBreaker, using fallback:', error);
            return this._createFallbackCircuitBreaker();
        }
    }

    /**
     * Get or create CacheManager instance
     * @param {Object} overrideOptions - Override default options
     * @returns {CacheManager} Shared instance
     */
    async getCacheManager(overrideOptions = {}) {
        const cacheKey = 'cacheManager';

        if (this._serviceCache.has(cacheKey) && !overrideOptions.forceNew) {
            return this._serviceCache.get(cacheKey);
        }

        try {
            const { CacheManager } = await import('./caching/CacheManager.js');

            const options = {
                ...this.options.cacheManager,
                ...overrideOptions,
                logger: this.logger
            };

            const instance = new CacheManager(options);
            this._serviceCache.set(cacheKey, instance);

            this.logger.debug('✅ CacheManager instance created and cached');
            return instance;

        } catch (error) {
            this.logger.warn('Failed to create CacheManager, using fallback:', error);
            return this._createFallbackCacheManager();
        }
    }

    /**
     * Get or create PerformanceCoordinator instance
     * @param {Object} overrideOptions - Override default options
     * @returns {PerformanceCoordinator} Shared instance
     */
    async getPerformanceCoordinator(overrideOptions = {}) {
        const cacheKey = 'performanceCoordinator';

        if (this._serviceCache.has(cacheKey) && !overrideOptions.forceNew) {
            return this._serviceCache.get(cacheKey);
        }

        try {
            const { PerformanceCoordinator } = await import('./performance/PerformanceCoordinator.js');

            const options = {
                ...this.options.performanceCoordinator,
                ...overrideOptions,
                logger: this.logger
            };

            const instance = new PerformanceCoordinator(options);
            this._serviceCache.set(cacheKey, instance);

            this.logger.debug('✅ PerformanceCoordinator instance created and cached');
            return instance;

        } catch (error) {
            this.logger.warn('Failed to create PerformanceCoordinator, using fallback:', error);
            return this._createFallbackPerformanceCoordinator();
        }
    }

    /**
     * Get or create Router instance
     * @param {Object} overrideOptions - Override default options
     * @returns {Router} Shared instance
     */
    async getRouter(overrideOptions = {}) {
        const cacheKey = 'router';

        if (this._serviceCache.has(cacheKey) && !overrideOptions.forceNew) {
            return this._serviceCache.get(cacheKey);
        }

        try {
            const { IntelligentRouter: Router } = await import('./routing/IntelligentRouter.js');

            const options = {
                logger: this.logger,
                ...overrideOptions
            };

            const instance = new Router(options);
            this._serviceCache.set(cacheKey, instance);

            this.logger.debug('✅ Router instance created and cached');
            return instance;

        } catch (error) {
            this.logger.warn('Failed to create Router, using fallback:', error);
            return this._createFallbackRouter();
        }
    }

    /**
     * Get all infrastructure services as a bundle
     * @param {Object} overrideOptions - Override options for specific services
     * @returns {Object} Object containing all infrastructure services
     */
    async getAllServices(overrideOptions = {}) {
        const [
            performanceTracker,
            circuitBreaker,
            cacheManager,
            performanceCoordinator,
            router
        ] = await Promise.all([
            this.getPerformanceTracker(overrideOptions.performanceTracker),
            this.getCircuitBreaker(overrideOptions.circuitBreaker),
            this.getCacheManager(overrideOptions.cacheManager),
            this.getPerformanceCoordinator(overrideOptions.performanceCoordinator),
            this.getRouter(overrideOptions.router)
        ]);

        return {
            performanceTracker,
            circuitBreaker,
            cacheManager,
            performanceCoordinator,
            router
        };
    }

    /**
     * Create infrastructure services for a specific component
     * @param {string} componentName - Name of the component requesting services
     * @param {Object} componentOptions - Component-specific options
     * @returns {Object} Configured infrastructure services
     */
    async createServicesForComponent(componentName, componentOptions = {}) {
        this.logger.debug(`Creating infrastructure services for: ${componentName}`);

        const services = await this.getAllServices(componentOptions);

        // Add component-specific logging context
        Object.values(services).forEach(service => {
            if (service && typeof service.setContext === 'function') {
                service.setContext(`${componentName}`);
            }
        });

        this.logger.info(`✅ Infrastructure services created for ${componentName}`, {
            performanceTracker: !!services.performanceTracker,
            circuitBreaker: !!services.circuitBreaker,
            cacheManager: !!services.cacheManager,
            performanceCoordinator: !!services.performanceCoordinator,
            router: !!services.router
        });

        return services;
    }

    /**
     * Clear service cache and force recreation
     */
    clearCache() {
        this._serviceCache.clear();
        this.logger.debug('Service cache cleared');
    }

    /**
     * Get metrics from all infrastructure services
     * @returns {Object} Combined metrics from all services
     */
    async getAllMetrics() {
        const services = await this.getAllServices();

        const metrics = {
            timestamp: Date.now(),
            performanceTracker: services.performanceTracker?.getMetrics?.() || {},
            circuitBreaker: services.circuitBreaker?.getMetrics?.() || {},
            cacheManager: services.cacheManager?.getMetrics?.() || {},
            performanceCoordinator: services.performanceCoordinator?.getMetrics?.() || {},
            router: services.router?.getMetrics?.() || {}
        };

        return metrics;
    }

    /**
     * Shutdown all infrastructure services
     */
    async shutdown() {
        try {
            const shutdownPromises = [];

            for (const [serviceName, service] of this._serviceCache) {
                if (service && typeof service.shutdown === 'function') {
                    shutdownPromises.push(
                        service.shutdown().catch(error =>
                            this.logger.warn(`Error shutting down ${serviceName}:`, error)
                        )
                    );
                }
            }

            await Promise.allSettled(shutdownPromises);
            this.clearCache();

            this.logger.info('✅ All infrastructure services shutdown complete');

        } catch (error) {
            this.logger.error('Error during infrastructure shutdown:', error);
        }
    }

    // Fallback implementations for when infrastructure services are not available

    _createFallbackPerformanceTracker() {
        return {
            responseTimes: [],
            errorCount: 0,
            lastErrorTime: 0,
            connectionStability: 1.0,
            systemLoad: 0.5,
            maxSamples: 20,

            trackRequest: (duration, success) => {
                this.responseTimes.push(duration);
                if (this.responseTimes.length > this.maxSamples) {
                    this.responseTimes.shift();
                }
                if (!success) {
                    this.errorCount++;
                    this.lastErrorTime = Date.now();
                }
            },

            getAverageResponseTime: () => {
                const times = this.responseTimes;
                return times.length > 0 ? times.reduce((sum, time) => sum + time, 0) / times.length : 5000;
            },

            recordResponseTime: (time) => this.trackRequest(time, true),
            recordModelCreation: () => { },
            recordModelFailure: () => { },
            getMetrics: () => ({
                averageResponseTime: this.getAverageResponseTime(),
                errorCount: this.errorCount
            }),
            reset: () => {
                this.responseTimes = [];
                this.errorCount = 0;
            }
        };
    }

    _createFallbackCircuitBreaker() {
        return {
            isOpen: () => false,
            recordSuccess: () => { },
            recordFailure: () => { },
            executeWithFallback: async (operation, fallback) => {
                try {
                    return await operation();
                } catch (error) {
                    return fallback ? await fallback(error) : Promise.reject(error);
                }
            },
            getMetrics: () => ({ state: 'closed' }),
            reset: () => { }
        };
    }

    _createFallbackCacheManager() {
        const cache = new Map();

        return {
            get: (key) => Promise.resolve(cache.get(key) || null),
            set: (key, value) => {
                cache.set(key, value);
                return Promise.resolve();
            },
            clear: () => {
                cache.clear();
                return Promise.resolve();
            },
            generateModelKey: (model, options) => `${model}-${JSON.stringify(options)}`,
            getModel: (key) => Promise.resolve(cache.get(key) || null),
            setModel: (key, model) => {
                cache.set(key, model);
                return Promise.resolve();
            },
            getMetrics: () => ({ size: cache.size }),
            delete: (key) => {
                cache.delete(key);
                return Promise.resolve();
            }
        };
    }

    _createFallbackPerformanceCoordinator() {
        return {
            coordinate: (operation) => operation(),
            getMetrics: () => ({}),
            reset: () => { }
        };
    }

    _createFallbackRouter() {
        return {
            selectOptimalModel: (routingContext) => {
                // Return a reasonable default based on context
                if (routingContext?.taskType === 'autonomous_tools' || routingContext?.requiresTools) {
                    return Promise.resolve('qwen-plus'); // Default HTTP model for tools
                }
                return Promise.resolve('qwen-plus'); // Default HTTP model
            },
            getModelConfig: (modelName) => {
                // Return basic config for any model
                return {
                    type: 'http',
                    capabilities: {
                        functionCalling: true,
                        autonomousTools: true,
                        streaming: true
                    },
                    performance: {
                        expectedResponseTime: 500,
                        maxResponseTime: 1000
                    },
                    limits: {
                        maxTokens: 2000,
                        contextWindow: 32768
                    }
                };
            },
            getFallbackChain: (failedModel, options) => {
                // Simple fallback chain
                const fallbacks = ['qwen-plus', 'qwen-turbo'];
                return Promise.resolve(fallbacks.filter(model => model !== failedModel));
            },
            getMetrics: () => ({
                totalRoutingDecisions: 0,
                averageRoutingTime: 0
            }),
            reset: () => { }
        };
    }
}

// Singleton instance for global use
let globalInfrastructureManager = null;

/**
 * Get the global infrastructure manager instance
 * @param {Object} options - Configuration options (only used on first call)
 * @returns {InfrastructureManager} Shared global instance
 */
export function getInfrastructureManager(options = {}) {
    if (!globalInfrastructureManager) {
        globalInfrastructureManager = new InfrastructureManager(options);
    }
    return globalInfrastructureManager;
}

/**
 * Reset the global infrastructure manager (mainly for testing)
 */
export function resetInfrastructureManager() {
    if (globalInfrastructureManager) {
        globalInfrastructureManager.shutdown();
        globalInfrastructureManager = null;
    }
}

export default InfrastructureManager;