/**
 * Simple Connection Manager - Simplified WebSocket Connection Management
 * 
 * MISSION: Provide stable WebSocket connections without over-engineering
 * 
 * Key Features:
 * - Simple 3-state connection lifecycle (DISCONNECTED, CONNECTING, CONNECTED)
 * - Basic retry logic with exponential backoff
 * - Session initialization support
 * - Event-based state management
 * - Singleton pattern for shared connections
 */

import { EventEmitter } from 'events';
import { createLogger, LogLevel } from '../../../utils/logger.ts';
import { parseRobustJSON } from '../../../utils/jsonParser.js';

// Lazy-loaded central ErrorHandler (non-blocking)
let __connMgrErrorHandlerPromise = null;
function __connMgrGetErrorHandler() {
  if (!__connMgrErrorHandlerPromise) {
    __connMgrErrorHandlerPromise = import('../../arch/dualbrain/services/ErrorHandler.js')
      .then(mod => {
        try {
          if (typeof mod.createErrorHandler === 'function') return mod.createErrorHandler();
          if (typeof mod.ErrorHandler === 'function') return new mod.ErrorHandler();
        } catch (_) { return null; }
        return null;
      })
      .catch(() => null);
  }
  return __connMgrErrorHandlerPromise;
}

// Simple Connection States - only what we need
export const ConnectionState = {
  DISCONNECTED: 'DISCONNECTED',
  CONNECTING: 'CONNECTING',
  CONNECTED: 'CONNECTED',
  ERROR: 'ERROR',
  RECONNECTING: 'RECONNECTING'
};

// Simple state transitions
const VALID_TRANSITIONS = {
  [ConnectionState.DISCONNECTED]: [ConnectionState.CONNECTING],
  [ConnectionState.CONNECTING]: [ConnectionState.CONNECTED, ConnectionState.ERROR, ConnectionState.DISCONNECTED],
  [ConnectionState.CONNECTED]: [ConnectionState.ERROR, ConnectionState.DISCONNECTED, ConnectionState.RECONNECTING],
  [ConnectionState.ERROR]: [ConnectionState.RECONNECTING, ConnectionState.DISCONNECTED],
  [ConnectionState.RECONNECTING]: [ConnectionState.CONNECTING, ConnectionState.DISCONNECTED]
};

// Simple error classification
export const ConnectionErrorType = {
  NETWORK_ERROR: 'network',
  PROTOCOL_ERROR: 'protocol',
  AUTH_ERROR: 'authentication',
  TIMEOUT_ERROR: 'timeout',
  SESSION_ERROR: 'session'
};

/**
 * Simple Connection Manager with basic lifecycle management
 */
export class ConnectionManager extends EventEmitter {
  static _instance = null;
  static _initializingPromise = null;

  /**
   * Get singleton instance with thread-safe initialization
   */
  static async getInstance(config = {}) {
    // Return existing instance if available
    if (ConnectionManager._instance && !ConnectionManager._instance._isDisposed) {
      return ConnectionManager._instance;
    }

    // If already initializing, wait for that process to complete
    if (ConnectionManager._initializingPromise) {
      return ConnectionManager._initializingPromise;
    }

    // Start initialization process
    ConnectionManager._initializingPromise = (async () => {
      try {
        ConnectionManager._instance = new ConnectionManager(config);
        await ConnectionManager._instance._initialize();
        return ConnectionManager._instance;
      } catch (error) {
        ConnectionManager._instance = null;
        throw error;
      } finally {
        ConnectionManager._initializingPromise = null;
      }
    })();

    return ConnectionManager._initializingPromise;
  }

  constructor(config = {}) {
    super();

    this.logger = createLogger('ConnectionManager', LogLevel.DEBUG);

    // Simple configuration
    this.config = {
      connectionTimeout: config.connectionTimeout || 5000,
      maxRetries: config.maxRetries || 3,
      retryDelay: config.retryDelay || 1000,
      sessionInitTimeout: config.sessionInitTimeout || 3000,

      // Generic session event patterns - configurable per provider
      sessionCreatedPattern: config.sessionCreatedPattern || 'session.created',
      sessionReadyPattern: config.sessionReadyPattern || 'session.ready',

      // Message handling configuration
      enableSessionTracking: config.enableSessionTracking !== false,
      ...config
    };

    // Simple state management
    this._state = ConnectionState.DISCONNECTED;
    this._socket = null;
    this._wsConfig = null;
    this._connectionPromise = null;
    this._retryAttempts = 0;
    this._sessionId = null;
    this._retryTimer = null;

    // Event tracking for session management
    this._sessionReady = false;
    this._messageHandlers = new Map();

    // State change notification system
    this._stateSubscribers = new Map();
    this._isDisposed = false;

    this.logger.info('🚀 Connection Manager initialized');
  }

  /**
   * Initialize connection manager
   */
  async _initialize() {
    this.logger.info('🔧 Initializing Connection Manager...');
    this.emit('initialized');
    this.logger.info('✅ Connection Manager ready');
  }

  /**
   * Connect with simple lifecycle management
   */
  async connect(wsConfig) {
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;

    this.logger.debug('🔌 [CONN-MGR] Connection request received', {
      requestId,
      currentState: this._state,
      hasActivePromise: !!this._connectionPromise,
      wsUrl: wsConfig.url
    });

    if (this._connectionPromise) {
      this.logger.debug('⏳ Connection in progress, waiting for completion...', { requestId });
      const result = await this._connectionPromise;
      this.logger.debug('⏳ [CONN-MGR] Waited for existing connection', { requestId, result });
      return result;
    }

    if (!this._canTransitionTo(ConnectionState.CONNECTING)) {
      this.logger.warn(`❌ [CONN-MGR] Cannot connect from current state`, {
        requestId,
        currentState: this._state,
        allowedTransitions: VALID_TRANSITIONS[this._state]
      });
      throw new Error(`Cannot connect from state: ${this._state}`);
    }

    this.logger.debug('🚀 [CONN-MGR] Starting new connection process', { requestId });
    this._connectionPromise = this._performConnection(wsConfig);

    try {
      const result = await this._connectionPromise;
      this.logger.debug('✅ [CONN-MGR] Connection completed successfully', { requestId, result });
      return result;
    } catch (error) {
      this.logger.debug('❌ [CONN-MGR] Connection failed', { requestId, error: error.message });
      throw error;
    } finally {
      this._connectionPromise = null;
      this.logger.debug('🧹 [CONN-MGR] Connection promise cleared', { requestId });
    }
  }

  /**
 * Simple connection process with enhanced debugging
 */
  async _performConnection(wsConfig) {
    const connectionId = `conn_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;

    try {
      this.logger.debug('🔌 Starting connection process...', {
        connectionId,
        timeout: this.config.connectionTimeout,
        attempt: this._retryAttempts + 1,
        wsUrl: wsConfig.url,
        hasInitializer: !!wsConfig.initializeSession
      });

      this._wsConfig = { ...wsConfig };
      this._retryAttempts++;

      // Phase 1: Establish WebSocket connection
      this.logger.debug('📡 Phase 1: Establishing WebSocket connection...', { connectionId });
      this._setState(ConnectionState.CONNECTING);
      const socket = await this._createWebSocket(wsConfig);
      this._socket = socket;

      // Verify socket is still open after creation
      if (socket.readyState !== WebSocket.OPEN) {
        throw new Error(`WebSocket closed during connection (state: ${this._getReadyStateText(socket.readyState)})`);
      }

      // Phase 2: Basic connection established
      this.logger.debug('🔗 Phase 2: Setting up WebSocket handlers...', {
        connectionId,
        socketState: this._getReadyStateText(socket.readyState)
      });
      this._setState(ConnectionState.CONNECTED);
      this._setupWebSocketHandlers();

      // Phase 3: Initialize session if provided (with timeout and state checking)
      if (wsConfig.initializeSession) {
        this.logger.debug('🎬 Phase 3: Starting session initialization...', {
          connectionId,
          socketState: this._getReadyStateText(socket.readyState),
          timeout: this.config.sessionInitTimeout
        });

        try {
          // Check socket state before session initialization
          if (socket.readyState !== WebSocket.OPEN) {
            throw new Error(`WebSocket closed before session init (state: ${this._getReadyStateText(socket.readyState)})`);
          }

          // Use timeout for session initialization
          await this._withTimeout(
            wsConfig.initializeSession(),
            this.config.sessionInitTimeout,
            'Session initialization timeout'
          );

          this._sessionReady = true;
          this.logger.debug('✅ Session initialization completed successfully', { connectionId });

        } catch (sessionError) {
          this.logger.warn('⚠️ Session initialization failed but continuing:', {
            connectionId,
            error: sessionError.message,
            socketState: this._getReadyStateText(socket.readyState)
          });
          // Don't fail the connection for session errors, just continue without session
        }
      }

      // Final verification
      if (socket.readyState !== WebSocket.OPEN) {
        throw new Error(`WebSocket closed after initialization (state: ${this._getReadyStateText(socket.readyState)})`);
      }

      this.logger.info('✅ Connection established successfully', {
        connectionId,
        sessionReady: this._sessionReady,
        sessionId: this._sessionId,
        finalSocketState: this._getReadyStateText(socket.readyState)
      });

      this.emit('connectionReady', {
        connectionId,
        sessionId: this._sessionId,
        sessionReady: this._sessionReady,
        state: ConnectionState.CONNECTED
      });

      return true;

    } catch (error) {
      this._retryAttempts++;
      const errorType = this._classifyError(error);

      this.logger.error('❌ Connection failed:', {
        connectionId,
        error: error.message,
        type: errorType,
        attempt: this._retryAttempts,
        socketState: this._socket ? this._getReadyStateText(this._socket.readyState) : 'no_socket'
      });

      this._setState(ConnectionState.ERROR);

      // Clean up failed socket
      if (this._socket && this._socket.readyState !== WebSocket.CLOSED) {
        this._socket.close();
      }
      this._socket = null;

      // Simple retry logic
      if (this._shouldRetry(error, errorType)) {
        await this._scheduleRetry(error, errorType);
      }

      throw error;
    }
  }

  /**
   * Helper to add timeout to promises
   */
  async _withTimeout(promise, timeoutMs, timeoutMessage = 'Operation timeout') {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(timeoutMessage));
      }, timeoutMs);

      promise
        .then((result) => {
          clearTimeout(timer);
          resolve(result);
        })
        .catch((error) => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }

  /**
   * Create WebSocket with simple timeout
   */
  async _createWebSocket(wsConfig) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Connection timeout after ${this.config.connectionTimeout}ms`));
      }, this.config.connectionTimeout);

      try {
        let socket;

        if (typeof window !== 'undefined') {
          socket = new WebSocket(wsConfig.url);
        } else {
          import('ws').then(({ default: WebSocket }) => {
            socket = new WebSocket(wsConfig.url, wsConfig.options);
            this._setupConnectionHandlers(socket, resolve, reject, timeout);
          }).catch(reject);
          return;
        }

        this._setupConnectionHandlers(socket, resolve, reject, timeout);

      } catch (error) {
        clearTimeout(timeout);
        reject(error);
      }
    });
  }

  /**
   * Setup connection handlers
   */
  _setupConnectionHandlers(socket, resolve, reject, timeout) {
    const cleanup = () => {
      clearTimeout(timeout);
      socket.removeEventListener('open', onOpen);
      socket.removeEventListener('error', onError);
    };

    const onOpen = () => {
      cleanup();
      const connectionTime = Date.now() - startTime;
      this.logger.info('🔗 WebSocket opened', { connectionTime });
      resolve(socket);
    };

    const onError = (error) => {
      cleanup();
      this.logger.error('❌ WebSocket connection error:', error);
      reject(new Error('WebSocket connection failed'));
    };

    const startTime = Date.now();
    socket.addEventListener('open', onOpen);
    socket.addEventListener('error', onError);
  }

  /**
 * Setup WebSocket event handlers
 */
  _setupWebSocketHandlers() {
    if (!this._socket) {
      this.logger.warn('⚠️ Cannot setup handlers - no socket available');
      return;
    }

    this.logger.debug('🔧 Setting up WebSocket event handlers...', {
      socketState: this._getReadyStateText(this._socket.readyState),
      hasSocket: !!this._socket
    });

    this._socket.addEventListener('message', this._handleMessage.bind(this));
    this._socket.addEventListener('error', this._handleError.bind(this));
    this._socket.addEventListener('close', this._handleClose.bind(this));

    this.logger.debug('✅ WebSocket event handlers installed successfully');
  }

  /**
 * Handle WebSocket messages
 */
  async _handleMessage(event) {
    try {
      this.logger.debug('📨 Received WebSocket message:', {
        size: event.data?.length || event.data?.size,
        type: typeof event.data,
        isBlob: event.data instanceof Blob,
        socketState: this._getReadyStateText(this._socket?.readyState)
      });

      // Handle different data types properly
      let messageText;
      if (typeof event.data === 'string') {
        messageText = event.data;
      } else if (event.data instanceof Blob) {
        // Convert Blob to text for Aliyun WebSocket API
        messageText = await event.data.text();
        this.logger.debug('📄 Converted Blob to text:', {
          originalSize: event.data.size,
          textLength: messageText.length,
          preview: messageText.substring(0, 100)
        });
      } else if (event.data instanceof ArrayBuffer) {
        // Convert ArrayBuffer to text
        const decoder = new TextDecoder('utf-8');
        messageText = decoder.decode(event.data);
        this.logger.debug('📄 Converted ArrayBuffer to text:', {
          originalSize: event.data.byteLength,
          textLength: messageText.length
        });
      } else {
        throw new Error(`Unsupported WebSocket data type: ${typeof event.data}`);
      }

      // Use robust JSON parser instead of basic JSON.parse
      const parseResult = parseRobustJSON(messageText, {
        allowFallback: true,
        extractionStrategies: ['direct']
      });

      if (!parseResult.success) {
        throw new Error(`JSON parsing failed: ${parseResult.error}`);
      }

      const data = parseResult.data;

      this.logger.debug('📋 Parsed message data:', {
        type: data.type,
        hasSession: !!data.session,
        sessionId: data.session?.id
      });

      // Handle session-related messages using configurable patterns
      if (this.config.enableSessionTracking && data.type) {
        // Check for session created events
        if (this._matchesPattern(data.type, this.config.sessionCreatedPattern)) {
          this._sessionId = data.session?.id;
          this._sessionReady = true;
          this.logger.info('🎉 Session created successfully:', {
            sessionId: this._sessionId,
            sessionReady: this._sessionReady,
            eventType: data.type
          });
          this.emit('sessionReady', data);
        }

        // Check for session ready events 
        if (this._matchesPattern(data.type, this.config.sessionReadyPattern)) {
          this._sessionReady = true;
          this.logger.info('🎉 Session ready:', {
            sessionId: this._sessionId,
            sessionReady: this._sessionReady,
            eventType: data.type
          });
          this.emit('sessionReady', data);
        }
      }

      // Forward to message handlers
      this.emit('message', data);

    } catch (error) {
      // Safely handle different data types for debugging
      let rawDataPreview = 'N/A';
      try {
        if (typeof event.data === 'string') {
          rawDataPreview = event.data.substring(0, 200);
        } else if (event.data instanceof ArrayBuffer) {
          rawDataPreview = `[ArrayBuffer: ${event.data.byteLength} bytes]`;
        } else if (event.data instanceof Blob) {
          rawDataPreview = `[Blob: ${event.data.size} bytes, type: ${event.data.type}]`;
        } else if (event.data) {
          rawDataPreview = `[${typeof event.data}: ${String(event.data).substring(0, 100)}]`;
        }
      } catch (previewError) {
        rawDataPreview = '[Error creating preview]';
      }

      this.logger.error('❌ Error handling message:', {
        error: error.message,
        rawData: rawDataPreview,
        socketState: this._getReadyStateText(this._socket?.readyState)
      });
    }
  }

  /**
   * Handle WebSocket errors
   */
  _handleError(error) {
    const errorType = this._classifyError(error);
    this.logger.error('❌ WebSocket error:', { error, type: errorType });
    this.emit('error', error);

    // Delegate to central ErrorHandler asynchronously
    try {
      __connMgrGetErrorHandler().then(handler => {
        if (handler && typeof handler.handleError === 'function') {
          const normalized = error instanceof Error ? error : new Error(String(error));
          handler.handleError(normalized, {
            component: 'ConnectionManager',
            type: errorType,
            state: this._state,
            sessionId: this._sessionId,
            operation: 'connection_runtime'
          }).catch(() => { });
        }
      }).catch(() => { });
    } catch (_) {
      // ignore
    }
  }

  /**
 * Handle WebSocket close
 */
  _handleClose(event) {
    this.logger.debug('🔌 WebSocket close event received:', {
      code: event.code,
      reason: event.reason,
      wasClean: event.wasClean,
      currentState: this._state,
      sessionReady: this._sessionReady,
      sessionId: this._sessionId,
      retryAttempts: this._retryAttempts
    });

    // Common close codes and their meanings
    const closeCodeMeaning = this._getCloseCodeMeaning(event.code);
    this.logger.info('🔌 WebSocket closed:', {
      code: event.code,
      meaning: closeCodeMeaning,
      reason: event.reason || 'No reason provided',
      wasClean: event.wasClean
    });

    const errorType = event.wasClean ? 'normal' : 'network';

    // Reset session state on close
    this._sessionReady = false;
    this._sessionId = null;

    if (this._shouldRetry(event, errorType)) {
      this.logger.debug('🔄 Close event qualifies for retry');
      this._scheduleRetry(event, errorType);
    } else {
      this.logger.debug('🛑 Close event does not qualify for retry - going to DISCONNECTED');
      this._setState(ConnectionState.DISCONNECTED);
    }

    this.emit('close', event);
  }

  /**
   * Get human-readable close code meaning
   */
  _getCloseCodeMeaning(code) {
    switch (code) {
      case 1000: return 'Normal Closure';
      case 1001: return 'Going Away';
      case 1002: return 'Protocol Error';
      case 1003: return 'Unsupported Data';
      case 1005: return 'No Status Received';
      case 1006: return 'Abnormal Closure';
      case 1007: return 'Invalid frame payload data';
      case 1008: return 'Policy Violation';
      case 1009: return 'Message too big';
      case 1010: return 'Missing Extension';
      case 1011: return 'Internal Error';
      case 1012: return 'Service Restart';
      case 1013: return 'Try Again Later';
      case 1014: return 'Bad Gateway';
      case 1015: return 'TLS Handshake';
      default: return `Unknown (${code})`;
    }
  }

  /**
   * Check if connection is ready for operations
   * Enhanced with configurable readiness criteria
   */
  isReady() {
    const basicReady = this._state === ConnectionState.CONNECTED;
    const socketReady = this._socket && this._socket.readyState === WebSocket.OPEN;

    // If session tracking is disabled, just check basic connectivity
    if (!this.config.enableSessionTracking) {
      const ready = basicReady && socketReady;
      this.logger.debug('🔍 [CONN-MGR] Ready check (no session tracking)', {
        ready,
        currentState: this._state,
        hasSocket: !!this._socket,
        socketState: this._socket ? this._getReadyStateText(this._socket.readyState) : 'none'
      });
      return ready;
    }

    // If session tracking is enabled, also check session readiness
    const sessionReady = this._sessionReady;
    const ready = basicReady && socketReady && sessionReady;

    this.logger.debug('🔍 [CONN-MGR] Ready check (with session tracking)', {
      ready,
      currentState: this._state,
      hasSocket: !!this._socket,
      socketState: this._socket ? this._getReadyStateText(this._socket.readyState) : 'none',
      sessionReady: this._sessionReady,
      sessionId: this._sessionId
    });

    return ready;
  }

  /**
   * Wait for connection to be ready
   */
  async waitForReady(timeout = 5000) {
    if (this.isReady()) {
      return true;
    }

    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        this.removeListener('connectionReady', onReady);
        reject(new Error('Timeout waiting for connection'));
      }, timeout);

      const onReady = () => {
        clearTimeout(timer);
        resolve(true);
      };

      this.once('connectionReady', onReady);
    });
  }

  /**
 * Send message through WebSocket
 */
  send(message) {
    this.logger.debug('📤 Attempting to send message:', {
      isReady: this.isReady(),
      hasSocket: !!this._socket,
      socketState: this._socket?.readyState,
      socketStateText: this._getReadyStateText(this._socket?.readyState)
    });

    if (!this.isReady() || !this._socket) {
      throw new Error('Connection not ready');
    }

    // Check WebSocket state before sending
    if (this._socket.readyState !== WebSocket.OPEN) {
      this.logger.warn('⚠️ WebSocket not in OPEN state:', {
        readyState: this._socket.readyState,
        stateText: this._getReadyStateText(this._socket.readyState)
      });
      throw new Error(`WebSocket is not ready for sending (state: ${this._getReadyStateText(this._socket.readyState)})`);
    }

    const messageStr = typeof message === 'string' ? message : JSON.stringify(message);

    try {
      this._socket.send(messageStr);
      this.logger.debug('✅ Message sent successfully');
    } catch (error) {
      this.logger.error('❌ Failed to send message:', error);
      throw new Error(`Failed to send message through WebSocket: ${error.message}`);
    }
  }

  /**
   * Get human-readable WebSocket ready state
   */
  _getReadyStateText(readyState) {
    switch (readyState) {
      case 0: return 'CONNECTING';
      case 1: return 'OPEN';
      case 2: return 'CLOSING';
      case 3: return 'CLOSED';
      default: return 'UNKNOWN';
    }
  }

  /**
   * Disconnect WebSocket
   */
  async disconnect(code = 1000, reason = 'Manual disconnect') {
    const disconnectId = `disc_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;

    this.logger.debug('🔌 [CONN-MGR] Disconnect requested', {
      disconnectId,
      code,
      reason,
      currentState: this._state,
      hasSocket: !!this._socket,
      socketState: this._socket ? this._getReadyStateText(this._socket.readyState) : 'none'
    });

    if (this._socket) {
      this._setState(ConnectionState.DISCONNECTED);

      try {
        if (this._socket.readyState === WebSocket.OPEN || this._socket.readyState === WebSocket.CONNECTING) {
          this.logger.debug('🔌 [CONN-MGR] Closing WebSocket', { disconnectId, socketState: this._getReadyStateText(this._socket.readyState) });
          this._socket.close(code, reason);
        } else {
          this.logger.debug('🔌 [CONN-MGR] WebSocket already closed/closing', { disconnectId, socketState: this._getReadyStateText(this._socket.readyState) });
        }
      } catch (error) {
        this.logger.warn('⚠️ [CONN-MGR] Error closing WebSocket', { disconnectId, error: error.message });
      }

      this._socket = null;
      this.logger.debug('🧹 [CONN-MGR] Socket reference cleared', { disconnectId });
    }

    this._sessionId = null;
    this._sessionReady = false;
    this._retryAttempts = 0;

    this.logger.debug('✅ [CONN-MGR] Disconnect completed', { disconnectId });
  }

  /**
   * Get current connection state
   */
  getConnectionState() {
    return {
      state: this._state,
      connected: this.isReady(),
      sessionId: this._sessionId,
      sessionReady: this._sessionReady
    };
  }

  /**
   * Simple state management
   */
  _setState(newState) {
    // Don't transition to the same state
    if (this._state === newState) {
      this.logger.debug('🔄 Already in state', { currentState: this._state, requestedState: newState });
      return;
    }

    if (!this._canTransitionTo(newState)) {
      this.logger.error('❌ Invalid state transition', {
        from: this._state,
        to: newState,
        validTransitions: VALID_TRANSITIONS[this._state]
      });
      return;
    }

    const oldState = this._state;
    this._state = newState;

    this.logger.debug('🔄 State transition', { from: oldState, to: newState });
    this.emit('stateChange', { oldState, newState });

    // Notify subscribers
    this._stateSubscribers.forEach((callback, subscriberId) => {
      try {
        callback(newState, oldState);
      } catch (error) {
        this.logger.error('❌ Error in state subscriber:', error);
      }
    });
  }

  /**
   * Match event type against a pattern (supports exact match or contains)
   */
  _matchesPattern(eventType, pattern) {
    if (!eventType || !pattern) return false;

    // Support multiple pattern formats:
    // 1. Exact match: "session.created"
    // 2. Contains match: "session.created" (checks if eventType includes pattern)
    // 3. Multiple patterns: ["session.created", "session.ready"]
    if (Array.isArray(pattern)) {
      return pattern.some(p => this._matchesPattern(eventType, p));
    }

    return eventType === pattern || eventType.includes(pattern);
  }

  /**
   * Check if state transition is valid
   */
  _canTransitionTo(newState) {
    const validTransitions = VALID_TRANSITIONS[this._state] || [];
    return validTransitions.includes(newState);
  }

  /**
   * Subscribe to state changes
   */
  subscribeToStateNotifications(subscriberId, callback) {
    this._stateSubscribers.set(subscriberId, callback);
  }

  /**
   * Unsubscribe from state changes
   */
  unsubscribeFromStateNotifications(subscriberId) {
    this._stateSubscribers.delete(subscriberId);
  }

  /**
   * Classify errors for retry logic
   */
  _classifyError(error) {
    const message = error?.message || error?.toString() || 'Unknown error';
    this.logger.debug('🔍 Classifying error:', { message, errorType: typeof error, error });

    if (message.includes('timeout')) return ConnectionErrorType.TIMEOUT_ERROR;
    if (message.includes('401') || message.includes('auth')) return ConnectionErrorType.AUTH_ERROR;
    if (message.includes('protocol')) return ConnectionErrorType.PROTOCOL_ERROR;
    if (message.includes('session')) return ConnectionErrorType.SESSION_ERROR;
    return ConnectionErrorType.NETWORK_ERROR;
  }

  /**
   * Check if should retry connection
   */
  _shouldRetry(error, errorType) {
    if (this._retryAttempts >= this.config.maxRetries) return false;
    if (errorType === ConnectionErrorType.AUTH_ERROR) return false;
    return true;
  }

  /**
   * Schedule retry with exponential backoff
   */
  async _scheduleRetry(error, errorType) {
    // Prevent multiple retries from being scheduled
    if (this._retryTimer) {
      this.logger.debug('🔄 Retry already scheduled, skipping duplicate');
      return;
    }

    const delay = this.config.retryDelay * Math.pow(2, this._retryAttempts - 1);

    this.logger.info('🔄 Scheduling retry', {
      errorType,
      attempt: this._retryAttempts,
      maxAttempts: this.config.maxRetries,
      delay
    });

    this._setState(ConnectionState.RECONNECTING);

    this._retryTimer = setTimeout(async () => {
      this._retryTimer = null;
      try {
        await this._performConnection(this._wsConfig);
      } catch (retryError) {
        this.logger.error('❌ Retry failed:', retryError);
        this._setState(ConnectionState.ERROR);
      }
    }, delay);
  }

  /**
   * Dispose resources
   */
  async dispose() {
    this._isDisposed = true;

    // Clear retry timer
    if (this._retryTimer) {
      clearTimeout(this._retryTimer);
      this._retryTimer = null;
    }

    await this.disconnect();
    this.removeAllListeners();
    this._stateSubscribers.clear();

    if (ConnectionManager._instance === this) {
      ConnectionManager._instance = null;
    }
  }
}

// Export the ConnectionState for external use
export { ConnectionState as EnhancedConnectionState };

/**
 * Helper factory function to create ConnectionManager with provider-specific configuration
 */
export function createProviderConnectionManager(providerConfig = {}) {
  const {
    provider = 'generic',
    sessionCreatedPattern = 'session.created',
    sessionReadyPattern = 'session.ready',
    enableSessionTracking = true,
    connectionTimeout = 10000,
    sessionInitTimeout = 8000,
    maxRetries = 3,
    retryDelay = 1000,
    ...otherConfig
  } = providerConfig;

  return new ConnectionManager({
    // Provider-specific patterns
    sessionCreatedPattern,
    sessionReadyPattern,
    enableSessionTracking,

    // Timeout configuration
    connectionTimeout,
    sessionInitTimeout,

    // Retry configuration
    maxRetries,
    retryDelay,

    // Additional config
    ...otherConfig
  });
}