import { AlgorithmService } from '../../server/algorithm';
import { config } from '../../server/config';
import type { AlgorithmRequest, AlgorithmResponse, GradioResponse } from '../../server/types';

/**
 * TextTo3D Request interface
 */
export interface TextTo3DRequest extends AlgorithmRequest {
    textPrompt: string;
    sessionId?: string;
    saveOptions?: {
        savePath?: string;
        filename?: string;
    };
}

/**
 * TextTo3D Response interface
 */
export interface TextTo3DResponse {
    model3d?: any; // The 3D model result
    downloadGlb1?: any; // First download button result
    downloadGlb2?: any; // Second download button result
    sessionId?: string;
}

export class TextTo3DService extends AlgorithmService {
    private textTo3DClient: any;
    private sessionId: string | null = null;

    constructor() {
        super('textto3d');
        this.textTo3DClient = null;
    }

    protected async processRequest(request: AlgorithmRequest): Promise<AlgorithmResponse> {
        // Implementation specific to TextTo3D
        throw new Error('Not implemented');
    }

    private async ensureTextTo3DClient(): Promise<boolean> {
        try {
            // Import GradioClient dynamically to avoid circular dependencies
            const { GradioClient } = await import('../../server/api');

            // Get the text-to-3d client
            this.textTo3DClient = GradioClient.getInstance('text-to-3d');

            // Initialize the client if not already initialized
            return await this.textTo3DClient.init();
        } catch (error) {
            console.error('[TextTo3DService] Failed to initialize text-to-3d client:', error);
            return false;
        }
    }

    /**
     * Start a new session with the TextTo3D service
     */
    private async startSession(): Promise<string | null> {
        try {
            // Ensure client is initialized
            const initialized = await this.ensureTextTo3DClient();
            if (!initialized) {
                throw new Error('Text-to-3D client initialization failed');
            }

            console.log('[TextTo3DService] Starting new session');

            // Call the start_session API
            const result = await this.textTo3DClient.client.predict('/start_session', {});

            if (!result || !result.data) {
                throw new Error('No data received from start_session API');
            }

            console.log('[TextTo3DService] Session started successfully:', result);

            // Store session ID if returned
            this.sessionId = result.data.session_id || 'default';

            return this.sessionId;
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            console.error('[TextTo3DService] Error starting session:', errorMessage);
            return null;
        }
    }

    /**
     * Process text input to generate 3D model
     */
    async textTo3D(
        request: TextTo3DRequest,
        onProgress?: (progress: number) => void,
    ): Promise<GradioResponse<TextTo3DResponse>> {
        try {
            if (!request || !request.textPrompt) {
                throw new Error('[TextTo3DService] Text prompt is required');
            }

            console.log(`[TextTo3DService] Processing text-to-3D request: "${request.textPrompt}"`);

            // Ensure client is initialized
            const initialized = await this.ensureTextTo3DClient();
            if (!initialized) {
                throw new Error('Text-to-3D client initialization failed');
            }

            // Start session if we don't have one
            if (!this.sessionId) {
                const sessionId = await this.startSession();
                if (!sessionId) {
                    throw new Error('Failed to start session');
                }
            }

            // Update progress to 25%
            onProgress?.(25);

            // Format the inputs for the process_text_input endpoint
            const inputs = {
                text_prompt: request.textPrompt
            };

            console.log(`[TextTo3DService] Calling process_text_input API with:`, inputs);

            // Update progress to 50%
            onProgress?.(50);

            // Call the process_text_input API
            const result = await this.textTo3DClient.client.predict('/process_text_input', inputs);

            if (!result || !result.data) {
                throw new Error('No data received from process_text_input API');
            }

            console.log(`[TextTo3DService] Received response from process_text_input API:`, result);

            // Update progress to 75%
            onProgress?.(75);

            // Process the response
            // The API returns a list of 3 elements: [model3d, downloadGlb1, downloadGlb2]
            if (Array.isArray(result.data) && result.data.length >= 3) {
                const [model3d, downloadGlb1, downloadGlb2] = result.data;

                // Update progress to 100%
                onProgress?.(100);

                return {
                    success: true,
                    data: {
                        model3d: model3d,
                        downloadGlb1: downloadGlb1,
                        downloadGlb2: downloadGlb2,
                        sessionId: this.sessionId
                    }
                };
            } else {
                throw new Error('Unexpected response format from process_text_input API');
            }
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            console.error('[TextTo3DService] Error processing text-to-3D request:', errorMessage);
            return {
                success: false,
                error: errorMessage,
                data: null
            };
        }
    }

    /**
     * Reset the session
     */
    async resetSession(): Promise<void> {
        this.sessionId = null;
        console.log('[TextTo3DService] Session reset');
    }

    /**
     * Get the current session ID
     */
    getSessionId(): string | null {
        return this.sessionId;
    }

    /**
     * Dispose of the service and clean up resources
     */
    dispose(): void {
        this.sessionId = null;
        this.textTo3DClient = null;
        console.log('[TextTo3DService] Service disposed');
    }
}

export const textTo3DService = new TextTo3DService(); 