import { AlgorithmService } from '../../server/algorithm';
import { config } from '../../server/config';
import { ENDPOINTS } from '../../server/constants';
import type { AlgorithmRequest, AlgorithmResponse, AnyTo3DRequest, AnyTo3DResponse, GradioResponse } from '../../server/types';

export class AnyTo3DService extends AlgorithmService {
    private tripoDollClient: any;

    constructor() {
        super('anyto3d');
        this.tripoDollClient = null;
    }

    protected async processRequest(request: AlgorithmRequest): Promise<AlgorithmResponse> {
        // Implementation specific to AnyTo3D
        throw new Error('Not implemented');
    }

    private async ensureTripoDollClient(): Promise<boolean> {
        try {
            // Import GradioClient dynamically to avoid circular dependencies
            const { GradioClient } = await import('../../server/api');

            // Get the tripo-doll client
            this.tripoDollClient = GradioClient.getInstance('tripo-doll');

            // Initialize the client if not already initialized
            return await this.tripoDollClient.init();
        } catch (error) {
            console.error('[AnyTo3DService] Failed to initialize tripo-doll client:', error);
            return false;
        }
    }

    // Special method for handling tripo-doll requests
    private async processTripoDollRequest(
        input: any,
        gender: string,
        onProgress?: (progress: number) => void,
        saveOptions?: AnyTo3DRequest['saveOptions']
    ): Promise<GradioResponse<AnyTo3DResponse>> {
        try {
            // Ensure tripo-doll client is initialized
            const initialized = await this.ensureTripoDollClient();
            if (!initialized) {
                throw new Error('Tripo-doll client initialization failed');
            }

            console.log(`[anyTo3D] Processing tripo-doll request with gender: ${gender}`);

            // Format the inputs as named parameters according to the API documentation
            const namedInputs = {
                input_image: input,
                gender: gender
            };

            // Call the API with the named parameters
            console.log(`[anyTo3D] Calling tripo-doll API with parameters:`, namedInputs);

            // Update progress to 50%
            onProgress?.(50);

            // Use the client.predict method with named parameters
            const result = await this.tripoDollClient.client.predict('/process_image', namedInputs);

            if (!result || !result.data) {
                throw new Error('No data received from tripo-doll API');
            }

            console.log(`[anyTo3D] Received response from tripo-doll API:`, result);

            // Update progress to 100%
            onProgress?.(100);

            // Process the response
            // The tripo-doll API returns an array of 4 elements:
            // [0]: 2D doll image
            // [1]: 3D model GLB file
            // [2]: 3D model for display
            // [3]: Rigging file GLB
            if (Array.isArray(result.data) && result.data.length >= 4) {
                // No need to generate paths or timestamps here
                // The viewer will handle file saving with appropriate naming

                // Get the results from the API response
                // [0]: 2D doll image - will be saved with "tripo_doll_" prefix by the viewer
                const imageResult = result.data[0];

                // [1]: 3D model GLB file - this is the main mesh result
                const meshResult = result.data[1];

                // [3]: Rigging file GLB - will be saved as a mesh file
                const riggingResult = result.data[3];

                // Return the processed results
                // Note: We're not returning videoResult as per requirements
                return {
                    success: true,
                    data: {
                        imageResult: imageResult,
                        meshResult: riggingResult,
                        riggingResult: riggingResult,
                        // No video result as per requirements
                        videoResult: null,
                        // Add metadata to indicate this is a tripo-doll result
                        // This will help the viewer handle it appropriately
                        metadata: {
                            type: 'tripo-doll',
                            gender: gender
                        }
                    }
                };
            } else {
                throw new Error('Unexpected response format from tripo-doll API');
            }
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            console.error('[anyTo3D] Error processing tripo-doll request:', errorMessage);
            return {
                success: false,
                error: errorMessage,
                data: null
            };
        }
    }

    async anyTo3D(
        request: AnyTo3DRequest,
        onProgress?: (progress: number) => void,
    ): Promise<GradioResponse<AnyTo3DResponse>> {
        try {
            if (!request) {
                throw new Error('[anyTo3D] Request cannot be null or undefined');
            }

            // Special handling for tripo-doll requests
            if (request.source === 'tripo-doll') {
                // Use gender parameter or default to 'boy'
                const gender = request.gender || 'boy';

                // Use the special tripo-doll processing method
                return await this.processTripoDollRequest(request.input, gender, onProgress, request.saveOptions);
            }

            // For other requests, use the regular Gradio client
            const initialized = await this.ensureGradioClient();
            if (!initialized) {
                throw new Error('Gradio client initialization failed');
            }

            // Process input and determine endpoints
            const processedInput = await this.handleGradioInput(request.input, request.source);

            // Handle different input formats based on source type
            let inputs = [processedInput, request.seed];

            // Log input details for debugging
            console.log('[anyTo3D] Processing request:', {
                source: request.source,
                seed: request.seed,
                gender: request.gender,
                inputType: typeof request.input === 'string' ? 'string' : 'file',
                saveOptions: request.saveOptions
            });
            const endpoints = this.getEndpointsForSource(request.source);

            // Define generation stages
            const stages = [
                {
                    name: 'image',
                    endpoint: endpoints.image,
                    inputs: inputs,
                    progress: 33
                },
                {
                    name: 'video',
                    endpoint: endpoints.video,
                    inputs: inputs,
                    progress: 66
                },
                {
                    name: 'mesh',
                    endpoint: endpoints.mesh,
                    inputs: [processedInput],
                    progress: 100
                }
            ];

            const results: Record<string, any> = {};

            // Process each stage
            for (const stage of stages) {
                console.log(`[anyTo3D] Processing ${stage.name} using endpoint '${stage.endpoint}'`);
                onProgress?.(stage.progress);

                // Use the regular Gradio client (tripo-doll is handled separately)
                const client = this.gradioClient;

                const result = await (request.usePredict
                    ? client.predict(stage.endpoint, stage.inputs)
                    : client.submitWithProgress(stage.endpoint, stage.inputs)
                );

                if (!result.success) {
                    console.log(`[anyTo3D] ${stage.name} generation failed: ${result.error}`);
                    return {
                        success: false,
                        error: result.error || `${stage.name} generation failed`,
                        data: null
                    };
                }

                // Special handling for endpoints that return multiple files
                if (stage.endpoint === ENDPOINTS.DOWNLOAD_DOLL_MESH) {
                    console.log(`[anyTo3D] Processing download_doll_mesh response:`, result.data);

                    // Check if the result is an array with multiple files
                    if (Array.isArray(result.data) && result.data.length > 1) {
                        // Prioritize the second file if it exists
                        const secondFile = result.data[1];
                        if (secondFile) {
                            console.log(`[anyTo3D] Using second file from download_doll_mesh:`, secondFile);
                            results[`${stage.name}Result`] = secondFile;
                        } else {
                            // Fall back to first file if second doesn't exist
                            console.log(`[anyTo3D] Using first file from download_doll_mesh:`, result.data[0]);
                            results[`${stage.name}Result`] = result.data[0];
                        }
                    } else {
                        // If not an array or only one file, use the data as is
                        results[`${stage.name}Result`] = result.data;
                    }
                } else if (stage.endpoint === ENDPOINTS.DOWNLOAD_TRIPO_DOLL_MESH) {
                    console.log(`[anyTo3D] Processing tripo-doll response:`, result.data);

                    // The tripo-doll API returns an array of 4 elements:
                    // [0]: 2D doll image
                    // [1]: 3D model GLB file
                    // [2]: 3D model for display
                    // [3]: Rigging file GLB

                    if (Array.isArray(result.data) && result.data.length >= 4) {
                        // For image result, use the 2D doll image
                        if (stage.name === 'image') {
                            results[`${stage.name}Result`] = result.data[0];
                        }
                        // For mesh result, use the 3D model GLB file
                        else if (stage.name === 'mesh') {
                            results[`${stage.name}Result`] = result.data[1];
                            // Store the rigging file in a separate property
                            results['riggingResult'] = result.data[3];
                        }
                        // For video result, use the 3D model for display
                        else if (stage.name === 'video') {
                            results[`${stage.name}Result`] = result.data[2];
                        }
                    } else {
                        // If the response doesn't match expected format, use the data as is
                        console.warn(`[anyTo3D] Unexpected tripo-doll response format for ${stage.name}:`, result.data);
                        results[`${stage.name}Result`] = result.data;
                    }
                } else {
                    // Normal handling for other endpoints
                    results[`${stage.name}Result`] = result.data;
                }
            }

            return {
                success: true,
                data: {
                    meshResult: results.meshResult,
                    imageResult: results.imageResult,
                    videoResult: results.videoResult || null,
                    riggingResult: results.riggingResult || null,
                }
            };
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            console.error('[anyTo3D] Unexpected error:', errorMessage);
            return {
                success: false,
                error: errorMessage,
                data: null
            };
        }
    }

    private async handleGradioInput(input: any, type: 'text' | 'image' | 'audio' | 'doll'): Promise<any> {
        console.log(`[handleGradioInput] Processing ${type} input:`, input);

        try {
            switch (type) {
                case 'text':
                    // For text input, ensure it's in the correct format
                    if (typeof input === 'string') {
                        return input;
                    }
                    throw new Error('Invalid text input format');

                case 'image':
                case 'doll':
                    // For file inputs, return as is
                    return input;

                case 'audio':
                    // Handle audio files
                    return input;

                default:
                    throw new Error(`Unsupported input type: ${type}`);
            }
        } catch (error) {
            console.error(`[handleGradioInput] Error processing ${type} input:`, error);
            throw error;
        }
    }

    private getEndpointsForSource(source: string): { image: string; video: string; mesh: string } {

        switch (source) {
            case 'text':
                return {
                    image: ENDPOINTS.TEXT_TO_IMAGE,
                    video: ENDPOINTS.UPDATE_MESH_TEXT,
                    mesh: ENDPOINTS.DOWNLOAD_TEXT_MESH
                };
            case 'image':
                return {
                    image: ENDPOINTS.IMAGE_TO_MESH,
                    video: ENDPOINTS.UPDATE_MESH_IMAGE,
                    mesh: ENDPOINTS.DOWNLOAD_IMAGE_MESH
                };
            case 'doll':
                return {
                    image: ENDPOINTS.IMAGE_TO_DOLL,
                    video: ENDPOINTS.UPDATE_MESH_DOLL,
                    mesh: ENDPOINTS.DOWNLOAD_DOLL_MESH
                };
            default:
                throw new Error(`Invalid source type: ${source}`);
        }
    }
}

export const anyTo3DService = new AnyTo3DService();
