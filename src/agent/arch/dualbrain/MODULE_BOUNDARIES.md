# Module Boundaries and Integration Contracts (Updated)

## 🎯 **Clear Separation of Concerns**

This document defines the clear boundaries between the **simplified dual brain architecture** and other modules, establishing proper integration contracts and responsibilities.

## 📦 **Module Responsibilities**

### **Dual Brain Architecture (`src/agent/arch/dualbrain/`)**

#### **What It Does:**
- ✅ **Request Routing**: Route requests to System 1 (WebSocket) or System 2 (HTTP) based on complexity/urgency
- ✅ **Proactive Decision Handling**: Manage proactive decisions from System 2
- ✅ **LangGraph Integration**: Leverage existing LangGraph capabilities instead of reimplementing
- ✅ **Simplified Coordination**: Direct integration with agent service models

#### **What It Doesn't Do:**
- ❌ **Data Processing**: No direct MediaPipe, audio, or video processing (delegated to MediaCoordinator)
- ❌ **Media Capture**: No camera or microphone access (handled by MediaCoordinator)
- ❌ **Model Management**: No model creation (uses agent service models)
- ❌ **UI Management**: No direct UI component control
- ❌ **Complex Communication**: No over-engineered message passing (simplified)

#### **Dependencies:**
- **IN**: Agent service with System 1 and System 2 models
- **IN**: Request routing decisions based on context  
- **OUT**: Agent responses through existing LangGraph workflow

---

## 🗑️ **Removed Components (Architectural Simplification)**

### **Components Removed:**
- ❌ **System2PeriodicAnalysis.js**: Redundant with LangGraph's existing capabilities
- ❌ **DualBrainCommunicationInterface.js**: Over-engineered; replaced with direct model invocation
- ❌ **MediaContextProvider.js**: Replaced with MediaCoordinator pattern
- ❌ **MediaPipeContextProvider.js**: Replaced with MediaCoordinator pattern
- ❌ **Complex DualBrainCoordinator.js**: Replaced with SimpleDualBrainCoordinator.js (now DualBrainCoordinator.js)

### **Rationale:**
- ✅ **Leverage LangGraph**: Use existing LangGraph capabilities instead of reimplementing
- ✅ **Reduce Complexity**: Remove over-engineered abstractions
- ✅ **Improve Maintainability**: Fewer moving parts, clearer responsibilities
- ✅ **Better Separation**: Media concerns handled by MediaCoordinator, not chat models

---

### **MediaPipe Module (`src/modules/mediapipe/`)**

#### **What It Does:**
- ✅ **Computer Vision**: Pose, hand, face detection and tracking
- ✅ **Model Loading**: MediaPipe model initialization and caching
- ✅ **Frame Processing**: Real-time video frame analysis
- ✅ **Landmark Extraction**: 3D coordinate extraction and normalization

#### **What It Doesn't Do:**
- ❌ **Decision Making**: No proactive decisions or contextual analysis
- ❌ **Communication**: No direct communication with dual brain
- ❌ **State Management**: No dual brain state coordination
- ❌ **Tool Execution**: No avatar animation or speech control

#### **Integration with Dual Brain:**
- **THROUGH**: MediaCoordinator service pattern
- **PROTOCOL**: Direct service integration (no complex abstractions)
- **DATA**: Standard media modality data (audio, video, text, multimodal)

---

### **Chat Model Architecture (`src/agent/models/`)**

#### **Base Classes:**
- ✅ **BaseChatModel**: Universal LangChain compliance and core functionality
- ✅ **HttpChatModel**: HTTP-specific methods (invoke, _generate, stream)
- ✅ **WebSocketChatModel**: WebSocket-specific methods with multimodal support

#### **Provider Classes:**
- ✅ **AliyunHttpChatModel**: Aliyun-specific HTTP implementation (qwen-plus, qwen-turbo)
- ✅ **AliyunWebSocketChatModel**: Aliyun-specific WebSocket implementation (qwen-omni-turbo)

#### **What It Does:**
- ✅ **LangChain Integration**: Proper invoke, _generate, stream method implementation
- ✅ **Provider Abstraction**: Unified interface for different LLM providers
- ✅ **Modality Support**: Audio, video, text, and multimodal data handling
- ✅ **Connection Management**: WebSocket and HTTP connection handling

#### **What It Doesn't Do:**
- ❌ **Media Capture**: No direct media capture (delegated to MediaCoordinator)
- ❌ **UI Management**: No UI rendering or controls
- ❌ **Input Processing**: No input coordination (handled by MediaCoordinator)

---

### **Media Module (`src/media/`)**

#### **What It Does:**
- ✅ **Audio Processing**: Audio capture, analysis, and format conversion
- ✅ **Video Processing**: Video capture, frame extraction, and compression
- ✅ **VAD (Voice Activity Detection)**: Real-time voice activity analysis
- ✅ **Quality Assessment**: Audio and video quality estimation
- ✅ **Format Conversion**: Multi-format audio/video processing

#### **What It Doesn't Do:**
- ❌ **Contextual Analysis**: No behavioral or engagement analysis
- ❌ **Decision Logic**: No speaking or action decisions
- ❌ **Avatar Control**: No direct animation or speech synthesis
- ❌ **Coordination**: No system-to-system communication

#### **Integration with Dual Brain:**
- **THROUGH**: MediaContextProvider
- **PROTOCOL**: ContextBridge abstraction layer
- **DATA**: Audio/video features and VAD states

---

### **Core Agent (`src/agent/core.js`)**

#### **What It Does:**
- ✅ **Service Orchestration**: Coordinates all agent services
- ✅ **Model Management**: LLM model initialization and lifecycle
- ✅ **Tool Registration**: Registers and manages available tools
- ✅ **Memory Management**: Conversation history and user memory
- ✅ **Streaming Coordination**: Text and audio streaming management

#### **Integration with Dual Brain:**
- **RELATIONSHIP**: Owner/Controller - creates and manages dual brain
- **PROVIDES**: Model instances, tool references, configuration
- **RECEIVES**: Proactive decisions and coordination metrics
- **PROTOCOL**: Direct method calls and event callbacks

## 🔄 **Integration Contracts**

### **ContextBridge Contract**

```typescript
interface ContextBridgeContract {
    // Data provision
    registerProvider(provider: ContextProvider): void;
    unregisterProvider(name: string): void;
    
    // Data access
    getCurrentContext(type: ContextDataType): ContextData | null;
    getAggregatedContext(types: ContextDataType[]): Record<string, ContextData>;
    
    // Subscriptions
    subscribe(type: ContextDataType, callback: (data: ContextData) => void): void;
    unsubscribe(type: ContextDataType, callback: Function): void;
    
    // Lifecycle
    start(): Promise<boolean>;
    stop(): Promise<boolean>;
    getStatus(): BridgeStatus;
}
```

### **ContextProvider Contract**

```typescript
interface ContextProviderContract {
    // Identity
    name: string;
    
    // Lifecycle
    initialize(): Promise<boolean>;
    start(): Promise<boolean>;
    stop(): Promise<boolean>;
    
    // Data provision
    getContext(type: ContextDataType): Promise<ContextData | null>;
    subscribe(type: ContextDataType, callback: Function): void;
    unsubscribe(type: ContextDataType, callback: Function): void;
    
    // Status
    getStatus(): ProviderStatus;
}
```

### **DualBrainCoordinator Contract**

```typescript
interface DualBrainCoordinatorContract {
    // Initialization
    initialize(systems: SystemInstances): Promise<boolean>;
    
    // Decision making
    makeProactiveDecision(context?: Object): Promise<ProactiveDecision>;
    handleProactiveSpeaking(decision: ProactiveDecision): Promise<void>;
    
    // Coordination
    handleCollaborativeRequest(request: CollaborativeRequest): Promise<Response>;
    
    // State management
    getCoordinationState(): CoordinationState;
    getPeriodicAnalysisState(): AnalysisState;
    
    // Lifecycle
    shutdown(): Promise<void>;
}
```

## 🌉 **Data Flow Boundaries**

### **Input Flow**
```
MediaPipe Module → MediaPipeContextProvider → ContextBridge
Media Module → MediaContextProvider → ContextBridge
Other Sources → CustomContextProvider → ContextBridge
```

### **Processing Flow**
```
ContextBridge → System2Interface → DualBrainCoordinator
ContextBridge → System1Interface → DualBrainCoordinator
```

### **Output Flow**
```
DualBrainCoordinator → Core Agent → Tool Execution
DualBrainCoordinator → Core Agent → State Updates
```

## 🔒 **Encapsulation Rules**

### **Dual Brain Encapsulation**
- **MUST**: Use ContextBridge for all external data access
- **MUST**: Provide standardized decision formats  
- **MUST NOT**: Import from media or MediaPipe modules directly
- **MUST NOT**: Perform raw data processing internally

### **MediaPipe Encapsulation**
- **MUST**: Expose data through ContextProvider interface
- **MUST**: Use standardized confidence scoring
- **MUST NOT**: Make decisions about user behavior
- **MUST NOT**: Communicate directly with dual brain

### **Media Encapsulation**
- **MUST**: Process data and expose through ContextProvider
- **MUST**: Handle format conversions internally
- **MUST NOT**: Perform contextual behavioral analysis
- **MUST NOT**: Make speaking or animation decisions

### **Core Agent Encapsulation**
- **MUST**: Coordinate between all modules
- **MUST**: Provide dependency injection for dual brain
- **MUST NOT**: Contain dual brain logic internally
- **MUST NOT**: Process raw MediaPipe or media data

## 🧪 **Testing Boundaries**

### **Unit Testing Scope**
- **Dual Brain**: Mock ContextBridge and model instances
- **MediaPipe**: Mock detection results and model loading
- **Media**: Mock audio/video data and browser APIs
- **Core Agent**: Mock all service dependencies

### **Integration Testing Scope**
- **ContextBridge Integration**: Real providers with mock data
- **Dual Brain Integration**: Real bridge with mock context providers
- **End-to-End**: Real modules with controlled test data

### **Test Data Contracts**
```typescript
interface TestDataContract {
    // Standardized test context data
    createMockAudioContext(volume: number, vadActive: boolean): ContextData;
    createMockPoseContext(landmarks: Landmark[]): ContextData;
    createMockVideoContext(quality: number): ContextData;
    
    // Test decision validation
    validateProactiveDecision(decision: ProactiveDecision): boolean;
    validateToolRequirements(tools: ToolCall[]): boolean;
}
```

## 📊 **Performance Boundaries**

### **Latency Requirements**
- **ContextBridge**: < 10ms for data access
- **MediaPipe Provider**: < 50ms for frame processing
- **Media Provider**: < 20ms for audio analysis
- **Dual Brain Analysis**: < 500ms for decisions

### **Memory Boundaries**
- **ContextBridge Buffer**: Max 100 entries per type
- **Provider Memory**: < 50MB per provider
- **Dual Brain Cache**: < 20MB for analysis cache
- **Core Agent Memory**: Managed by garbage collection

### **CPU Boundaries**
- **MediaPipe**: Max 80% CPU for vision processing
- **Media Processing**: Max 20% CPU for audio analysis
- **Dual Brain**: Max 10% CPU for decision making
- **Context Bridge**: Max 5% CPU for data coordination

## 🔧 **Configuration Boundaries**

### **Module Configuration Isolation**
```typescript
interface ModuleConfigContract {
    // Each module manages its own config
    dualBrainConfig: DualBrainOptions;
    mediaPipeConfig: MediaPipeOptions;
    mediaConfig: MediaProcessingOptions;
    coreAgentConfig: AgentOptions;
    
    // Shared config through well-defined interfaces
    sharedConfig: {
        performance: PerformanceLimits;
        logging: LoggingOptions;
        debugging: DebugOptions;
    };
}
```

### **Dependency Injection Rules**
- **Core Agent**: Injects all dependencies into modules
- **Dual Brain**: Receives models and ContextBridge
- **Providers**: Receive source modules and configuration
- **ContextBridge**: Receives providers and configuration

## 🚀 **Deployment Boundaries**

### **Module Independence**
- Each module can be updated independently
- Backward compatibility maintained through contracts
- Gradual migration supported between versions
- Feature flags control integration points

### **Fallback Strategies**
- **ContextBridge Failure**: Dual brain uses cached data
- **Provider Failure**: Bridge continues with remaining providers
- **Dual Brain Failure**: Core agent continues with basic functionality
- **Module Unavailable**: Graceful degradation with logging

## 📋 **Compliance Checklist**

### **For New Integrations**
- [ ] Implements required interface contracts
- [ ] Uses ContextBridge for data access
- [ ] Follows encapsulation rules
- [ ] Provides comprehensive test coverage
- [ ] Documents integration points clearly
- [ ] Handles errors gracefully
- [ ] Supports graceful degradation
- [ ] Meets performance requirements

### **For Existing Code**
- [ ] Remove direct module dependencies
- [ ] Migrate to ContextBridge pattern
- [ ] Update tests to use contracts
- [ ] Document migration path
- [ ] Verify performance impact
- [ ] Test backward compatibility

This clear separation of concerns ensures that each module has a single responsibility while maintaining clean integration points through well-defined contracts.