/**
 * Dual Brain Coordinator - Clean Multi-Agent Architecture
 * 
 * Implements LangGraph Multi-Agent Supervisor Pattern within service boundaries:
 * - System 1 (Fast Brain): WebSocket/realtime responses with audio modality
 * - System 2 (Reasoning Brain): HTTP-based complex reasoning with tool calling
 * - Supervisor Pattern: Routes requests to appropriate system based on complexity
 * - Service Integration: Uses SystemInvoker service for clean model invocation
 * 
 * Key Features:
 * - LangGraph Multi-Agent Supervisor Pattern
 * - Service-based architecture (70% code reduction achieved)
 * - Eliminates model override issues through proper service invocation
 * - Provider-agnostic core.js integration
 */

import { createLogger, LogLevelValues as LogLevel } from '../../../utils/logger.ts';
import { createSystem2AnalysisPrompt } from '../../prompts/contextual.js';
import { parseLLMDecision } from '../../../utils/jsonParser.js';
import { createDualBrainServices, DEFAULT_SERVICE_CONFIG } from './services/index.js';
import {
  createNoActivityEngagementPrompt,
  generateContextualHints,
  generateAnimationCues
} from '../../prompts/noactivity.js';
import {
  DUAL_BRAIN_CONFIG
} from '../../config/LangGraphConfig.js';
import { RouteResolver } from './routing/RouteResolver.js';

const logger = createLogger('DualBrainCoordinator');
logger.setLogLevel(LogLevel.DEBUG);

export class DualBrainCoordinator {
  constructor(agentService, options = {}) {
    this.agentService = agentService; // LangGraph agent service from core.js

    // Use centralized configuration with option overrides
    const config = DUAL_BRAIN_CONFIG;
    this.options = {
      system2AnalysisInterval: options.system2AnalysisInterval || 10000,
      decisionCooldown: options.decisionCooldown || 5000,
      enableProactiveDecisions: options.enableProactiveDecisions !== false,
      ...options
    };

    // Service integration for clean architecture
    this.services = options.services || null;

    this.logger = logger;
    this.isActive = false;
    this.isInitialized = false;
    this.lastDecisionTime = 0;

    // LangGraph Memory Integration
    this.memoryManager = options.memoryManager || this.agentService?.getMemoryManager?.();
    this.userId = options.userId || 'default_user';

    // General state tracking with activity monitoring (using centralized config)
    this.state = {
      lastSystem1Response: null,
      lastSystem2Analysis: null,
      pendingDecision: null,
      // Activity monitoring for System 1 background operation
      lastUserInteraction: Date.now(),
      isQuietPeriod: false,
      noActivityTimer: null,
      activityPatterns: config.noActivity.patterns
    };

    this.logger.info('🧠 Clean Dual Brain Coordinator initialized', {
      hasAgentService: !!this.agentService,
      hasServices: !!this.services,
      enableProactive: this.options.enableProactiveDecisions
    });
  }

  /**
   * Initialize dual brain coordination
   * Leverages existing agent service capabilities
   */
  async initialize() {
    try {
      this.logger.info('🔧 Initializing dual brain coordination...');

      // Verify agent service has dual brain models
      const isDualBrain = this.agentService.isDualBrainMode();
      if (!isDualBrain) {
        throw new Error('Agent service is not configured for dual brain mode');
      }

      // Get models from agent service (already created by core.js)
      const system1Model = this.agentService.getModel('system1');
      const system2Model = this.agentService.getModel('system2');

      if (!system1Model || !system2Model) {
        throw new Error('Dual brain models not available from agent service');
      }

      // Initialize SystemInvoker service with models
      if (this.services?.systemInvoker) {
        await this.services.systemInvoker.initialize({
          system1: system1Model,
          system2: system2Model
        });

        // Configure SystemInvoker for multi-agent supervisor pattern
        this.services.systemInvoker.configureModels({
          system1: system1Model,
          system2: system2Model
        });

        // ✅ NEW: Configure SystemInvoker with agent service for tool calling
        this.services.systemInvoker.setAgentService(this.agentService);

        // ✅ NEW: Configure SystemInvoker with DecisionProcessor for response handling
        if (this.services.decisionProcessor) {
          this.services.systemInvoker.setDecisionProcessor(this.services.decisionProcessor);
        }

        this.logger.info('✅ SystemInvoker configured for multi-agent supervision', {
          system1: system1Model.constructor.name,
          system2: system2Model.constructor.name,
          toolsAvailable: this.agentService?.tools?.length || 0,
          hasDecisionProcessor: !!this.services.decisionProcessor
        });
      }

      this.logger.info('✅ Dual brain models verified:', {
        system1: system1Model.constructor.name,
        system2: system2Model.constructor.name
      });

      this.isInitialized = true;
      return true;

    } catch (error) {
      this.logger.error('❌ Failed to initialize dual brain coordination:', error);
      return false;
    }
  }

  /**
   * Start dual brain systems
   */
  async startDualBrainSystems() {
    if (this.isActive) {
      this.logger.warn('⚠️ Dual brain systems already active');
      return true;
    }

    if (!this.isInitialized) {
      this.logger.error('❌ Cannot start - dual brain not initialized');
      return false;
    }

    try {
      this.logger.info('🚀 Starting dual brain systems...');

      // Start services if available
      if (this.services?.contextProcessor) {
        await this.services.contextProcessor.start();
      }

      if (this.services?.decisionProcessor) {
        this.services.decisionProcessor.start();
      }

      // Start periodic analysis (modern trigger system may hook here)
      if (this.options.enableProactiveDecisions) {
        this._startPeriodicAnalysis();
      }

      // Start no-activity monitoring only if legacy fallback is needed.
      // If TriggerSystem service is available, delegate to it and avoid redundant timers.
      this._startNoActivityMonitoring();

      this.isActive = true;
      this.logger.info('✅ Dual brain systems started successfully with no-activity monitoring');
      return true;

    } catch (error) {
      this.logger.error('❌ Error starting dual brain systems:', error);
      return false;
    }
  }

  /**
   * Stop dual brain systems
   */
  async stopDualBrainSystems() {
    if (!this.isActive) {
      return true;
    }

    try {
      this.logger.info('🛑 Stopping dual brain systems...');

      // Stop services if available
      if (this.services?.contextProcessor) {
        await this.services.contextProcessor.stop();
      }

      if (this.services?.decisionProcessor) {
        this.services.decisionProcessor.stop();
      }

      // Stop no-activity monitoring
      this._stopNoActivityMonitoring();

      this.isActive = false;
      this.logger.info('✅ Dual brain systems stopped');
      return true;

    } catch (error) {
      this.logger.error('❌ Error stopping dual brain systems:', error);
      return false;
    }
  }

  /**
   * Simple request processing - routes to System 1 or System 2
   */
  async processMultiAgentRequest(input, options = {}) {
    if (!this.services?.systemInvoker) {
      // Fallback to agent service directly
      return await this.agentService.generateResponse(input, options);
    }

    try {
      // Simple routing: tools = System 2, otherwise System 1
      const needsTools = options.requiresReasoning || options.isProactive || input?.length > 100;
      const targetSystem = needsTools ? 'system2' : 'system1';

      this.logger.debug(`🧠 Routing to ${targetSystem}`, {
        reason: needsTools ? 'complex/tools' : 'simple',
        inputLength: input?.length || 0,
        forceToolCalling: targetSystem === 'system2'
      });

      // Use SystemInvoker
      // Provide routing capabilities so SystemInvoker can enable tool calling/speaking
      const routingCapabilities = targetSystem === 'system2'
        ? DUAL_BRAIN_CONFIG.routing.system2.capabilities
        : DUAL_BRAIN_CONFIG.routing.system1.capabilities;

      const result = await this.services.systemInvoker.invokeSupervisor(input, {
        routing: {
          targetSystem,
          reason: needsTools ? 'complex/tools' : 'simple',
          capabilities: routingCapabilities
        },
        context: options.context || {},
        options: options
      });

      return result;

    } catch (error) {
      this.logger.error('❌ Multi-agent request failed:', error);
      // Fallback to agent service
      return await this.agentService.generateResponse(input, options);
    }
  }

  /**
   * Route request to appropriate system with intelligent decision making
   * Core supervisor logic for multi-agent pattern (using centralized config)
   * 
   * 🔧 OPTIMIZATION: Now uses RouteResolver to eliminate 50+ lines of duplicate logic
   */
  _routeToAppropriateSystem(input, options = {}) {
    // 🧠 DUAL BRAIN LOGGING - Input Analysis for Routing
    this.logger.debug('🧠 [ROUTING-ANALYSIS] Input complexity analysis:', {
      input: input ? input.substring(0, 100) + '...' : 'none',
      options
    });

    // ✅ OPTIMIZATION: Use unified RouteResolver (eliminates 50+ duplicate lines)
    const routingResult = RouteResolver.resolveRoute(input, options);

    this.logger.debug('🧠 [ROUTING-ANALYSIS] RouteResolver decision:', routingResult);

    const { targetSystem, reason, capabilities } = routingResult;

    if (targetSystem === 'system2') {
      this.logger.info('🧠 [ROUTING-SYSTEM2] ➡️ System 2 (Reasoning Brain) selected:', {
        reason,
        capabilities,
        triggers: options
      });
    } else {
      this.logger.info('🧠 [ROUTING-SYSTEM1] ⚡ System 1 (Fast Brain) selected:', {
        reason,
        capabilities,
        triggers: options
      });
    }

    return {
      ...routingResult,
      useRealtime: targetSystem === 'system1'
    };
  }

  /**
   * Track system responses for coordination
   */
  _trackSystemResponse(systemType, result) {
    const timestamp = Date.now();

    if (systemType === 'system1') {
      this.state.lastSystem1Response = {
        timestamp,
        result: result.data,
        latency: result.latency
      };
    } else {
      this.state.lastSystem2Analysis = {
        timestamp,
        result: result.data,
        latency: result.latency
      };
    }
  }

  /**
   * Generate proactive decision using System 2
   * Enhanced with multi-agent supervisor pattern
   */
  async generateProactiveDecision(contextData = {}) {
    this.logger.debug('🎯 Generating proactive decision via multi-agent pattern...');

    if (!this.isActive) {
      return { shouldAct: false, reason: 'dual_brain_inactive' };
    }

    const now = Date.now();
    if (now - this.lastDecisionTime < this.options.decisionCooldown) {
      return {
        shouldAct: false,
        reason: 'decision_cooldown',
        cooldownRemaining: this.options.decisionCooldown - (now - this.lastDecisionTime)
      };
    }

    try {
      // Build analysis prompt for System 2
      const analysisPrompt = await this._buildProactiveAnalysisPrompt(contextData);

      this.logger.debug('🧠 Invoking System 2 for proactive analysis via supervisor pattern');

      // Use multi-agent supervisor pattern for proactive decision
      const response = await this.processMultiAgentRequest(analysisPrompt, {
        isProactive: true,
        requiresReasoning: true,
        complexity: 'high'
      });

      // Parse decision from response
      const decision = this._parseProactiveDecision(response.data);

      if (decision.shouldAct) {
        this.lastDecisionTime = now;
        this.logger.info('✅ Proactive action decision made via multi-agent coordination');
      }

      return decision;

    } catch (error) {
      this.logger.error('❌ Error generating proactive decision:', error);
      return {
        shouldAct: false,
        reason: 'decision_error',
        error: error.message
      };
    }
  }

  /**
   * Build enhanced prompt for proactive analysis
   */
  async _buildProactiveAnalysisPrompt(contextData) {
    // Get enhanced context information
    const enhancedContextData = {
      ...contextData,
      recentContext: contextData.recentContext || [],
      timestamp: Date.now()
    };

    // Use centralized prompt system
    return createSystem2AnalysisPrompt(enhancedContextData, {
      includeSystem1Analysis: true,
      useOptionalMetrics: false
    });
  }

  /**
   * Parse proactive decision from System 2 response
   */
  _parseProactiveDecision(response) {
    try {
      // Use robust JSON parser utility
      const parseResult = parseLLMDecision(response, {
        extractionStrategies: ['thinking', 'codeblock', 'direct', 'lastjson'],
        logLevel: 'debug',
        validateSchema: (obj) => {
          return obj && (
            typeof obj.shouldAct === 'boolean' ||
            typeof obj.confidence === 'number' ||
            typeof obj.reason === 'string'
          );
        }
      });

      if (parseResult.success) {
        const decision = parseResult.data;
        return {
          ...decision,
          extractionMethod: parseResult.method,
          wasRepaired: parseResult.wasRepaired || false,
          timestamp: Date.now()
        };
      }

      // Fallback decision
      return {
        shouldAct: false,
        reason: 'parsing_failed',
        confidence: 0,
        timestamp: Date.now()
      };

    } catch (error) {
      this.logger.error('Error parsing proactive decision:', error);
      return {
        shouldAct: false,
        reason: 'critical_parsing_error',
        confidence: 0,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Start periodic analysis using System 2
   */
  _startPeriodicAnalysis() {
    if (this.services?.triggerSystem) {
      this.logger.info('⚡ Starting trigger-based analysis system...');

      // Register analysis handler
      this.services.triggerSystem.onFallbackAnalysis(async () => {
        await this._performPeriodicAnalysis();
      });

      this.services.triggerSystem.start();
      this.logger.info('✅ Trigger system activated');
    } else {
      this.logger.warn('⚠️ TriggerSystem service not available');
    }
  }

  /**
   * Perform periodic analysis with LangGraph memory integration
   */
  async _performPeriodicAnalysis() {
    this.logger.debug('🔍 Starting periodic analysis cycle...');

    try {
      // Gather context from memory if available
      let recentContext = [];

      if (this.memoryManager) {
        try {
          const memoryResults = await this.memoryManager.searchMemories(
            this.userId,
            {
              context: 'dualbrain_context',
              limit: 5
            }
          );

          recentContext = memoryResults.map(memory => ({
            type: memory.type,
            data: memory.data,
            timestamp: memory.timestamp
          }));
        } catch (error) {
          this.logger.debug('Could not retrieve context from memory:', error);
        }
      }

      // Generate proactive decision with context
      const contextData = recentContext.length > 0 ?
        { recentContext } :
        { environmental: { activity: 'monitoring' } };

      const decision = await this.generateProactiveDecision(contextData);

      this.logger.debug('🎯 Periodic analysis decision:', {
        shouldAct: decision.shouldAct,
        reason: decision.reason,
        contextSize: recentContext.length
      });

    } catch (error) {
      this.logger.error('❌ Error in periodic analysis:', error);
    }
  }

  /**
   * Update context from external sources
   */
  async updateContext(type, data) {
    // Use agent service's context update mechanism
    this.agentService.updateDualBrainContext(type, data);

    // Store in LangGraph memory
    if (this.memoryManager) {
      try {
        await this.memoryManager.addMemories(
          this.userId,
          {
            type,
            data,
            timestamp: Date.now(),
            source: 'dualbrain_coordinator'
          },
          'dualbrain_context'
        );
      } catch (error) {
        this.logger.warn('Failed to store context in memory:', error);
      }
    }
  }

  /**
   * Get coordinator status
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isActive: this.isActive,
      lastDecisionTime: this.lastDecisionTime,
      memoryManagerAvailable: !!this.memoryManager,
      servicesReady: !!(this.services?.systemInvoker && this.services?.contextProcessor),
      system1LastResponse: this.state.lastSystem1Response?.timestamp || null,
      system2LastAnalysis: this.state.lastSystem2Analysis?.timestamp || null
    };
  }

  /**
   * Get coordination status with service information
   */
  getCoordinationStatus() {
    return {
      coordinator: this.getStatus(),
      services: {
        systemInvoker: this.services?.systemInvoker ? 'ready' : 'not_available',
        contextProcessor: this.services?.contextProcessor ? 'ready' : 'not_available',
        decisionProcessor: this.services?.decisionProcessor ? 'ready' : 'not_available',
        errorHandler: this.services?.errorHandler ? 'ready' : 'not_available'
      },
      multiAgentPattern: 'supervisor_implemented',
      timestamp: Date.now()
    };
  }

  /**
   * Start no-activity monitoring for System 1 background operation
   * When no audio/video input occurs, System 1 triggers System 2 with contextual engagement
   */
  _startNoActivityMonitoring() {
    // If the modern TriggerSystem service exists, let it handle no-activity logic per
    // MODERN_TRIGGER_SYSTEM_ARCHITECTURE.md to prevent redundant background work.
    if (this.services?.triggerSystem) {
      this.logger.info('⚡ Using TriggerSystem for no-activity monitoring; disabling legacy timer.');
      return;
    }

    this.logger.info('👁️ Starting System 1 no-activity monitoring (legacy fallback)...');

    // Reset state
    this.state.lastUserInteraction = Date.now();
    this.state.isQuietPeriod = false;

    // Only schedule checks if any modality (audio/video) is active.
    this._scheduleNoActivityCheck();

    this.logger.info('✅ No-activity monitoring (legacy) active - gated by modality presence');
  }

  /**
   * Stop no-activity monitoring
   */
  _stopNoActivityMonitoring() {
    if (this.state.noActivityTimer) {
      clearTimeout(this.state.noActivityTimer);
      this.state.noActivityTimer = null;
    }

    this.logger.info('🛑 No-activity monitoring stopped');
  }

  /**
   * Schedule the next no-activity check
   */
  _scheduleNoActivityCheck() {
    if (this.state.noActivityTimer) {
      clearTimeout(this.state.noActivityTimer);
    }

    // Gate legacy background checks by modality presence to keep System 1 idle
    // when no audio/video input is provided, aligning with architecture docs.
    this._isAnyModalityActive().then((hasModality) => {
      if (!hasModality) {
        // No modalities; keep idle and do not schedule checks.
        this.logger.debug('⏸️ [NO-ACTIVITY] Skipping legacy monitoring - no audio/video modalities active');
        return;
      }

      // Check every 5 seconds for no-activity patterns
      this.state.noActivityTimer = setTimeout(() => {
        this._checkForNoActivity();
        this._scheduleNoActivityCheck(); // Reschedule
      }, 5000);
    }).catch(() => {
      // On error, be conservative and do not schedule to avoid background churn
      this.logger.debug('⏸️ [NO-ACTIVITY] Could not determine modality state; skipping schedule');
    });
  }

  /**
   * Check for no-activity patterns and trigger System 2 if needed
   */
  async _checkForNoActivity() {
    const now = Date.now();
    const timeSinceLastInput = now - this.state.lastUserInteraction;

    // Avoid any background escalation when no audio/video modalities are active
    const hasModality = await this._isAnyModalityActive();
    if (!hasModality) {
      this.logger.debug('⏸️ [NO-ACTIVITY] No modalities active; System 1 remains idle');
      return;
    }

    // Determine activity level
    let activityLevel = null;
    let pattern = null;

    if (timeSinceLastInput >= this.state.activityPatterns.long_silence.threshold) {
      activityLevel = 'long_silence';
      pattern = this.state.activityPatterns.long_silence;
    } else if (timeSinceLastInput >= this.state.activityPatterns.extended_quiet.threshold) {
      activityLevel = 'extended_quiet';
      pattern = this.state.activityPatterns.extended_quiet;
    } else if (timeSinceLastInput >= this.state.activityPatterns.quiet_period.threshold) {
      activityLevel = 'quiet_period';
      pattern = this.state.activityPatterns.quiet_period;
    }

    if (pattern && !this.state.isQuietPeriod) {
      this.logger.info(`🔔 No-activity detected: ${activityLevel} (${Math.round(timeSinceLastInput / 1000)}s quiet)`);

      // Mark as quiet period to prevent repeated triggers
      this.state.isQuietPeriod = true;

      // System 1 → System 2 escalation with enriched context
      await this._handleNoActivityTrigger(activityLevel, pattern, timeSinceLastInput);
    }
  }

  /**
   * Enhanced no-activity handling with structured JSON response
   * Uses System 1 context data and structured output format
   */
  async _handleNoActivityTrigger(activityLevel, pattern, timeSinceLastInput) {
    try {
      this.logger.info(`🧠 System 1 escalating to System 2 for no-activity: ${activityLevel}`);

      // Gather enriched context with System 1 input data
      const enrichedContext = await this.gatherContext('no_activity', {
        activityLevel,
        timeSinceLastInput
      });

      // Build enhanced prompt with JSON structure instruction
      const prompt = createNoActivityEngagementPrompt(enrichedContext, pattern);

      this.logger.debug('🎯 [NO-ACTIVITY] Enhanced context prepared', {
        activityLevel,
        timeSinceLastInput,
        hasUserProfile: !!enrichedContext.userProfile,
        hasAvatarProfile: !!enrichedContext.avatarProfile,
        contextualHints: enrichedContext.contextualHints?.length || 0,
        system1Context: enrichedContext.system1Context
      });

      // Trigger System 2 with structured output expectation
      const response = await this.processMultiAgentRequest(prompt, {
        isProactive: true,
        requiresReasoning: true,
        context: {
          activityLevel,
          timeSinceLastInput,
          expectedFormat: 'json_object',
          noActivityTrigger: true
        },
        noActivityTrigger: true,
        expectedToolExecution: false // Expecting structured content, not tool calls
      });

      // Parse structured JSON response
      let parsedResponse = null;
      try {
        const responseContent = response?.data?.content || response?.content || JSON.stringify(response);
        parsedResponse = JSON.parse(responseContent);

        this.logger.info('✅ [NO-ACTIVITY] Structured JSON response received', {
          shouldRespond: parsedResponse.shouldRespond,
          responseType: parsedResponse.responseType,
          hasMessage: !!parsedResponse.message,
          animationType: parsedResponse.animation?.type,
          reasoning: parsedResponse.reasoning?.substring(0, 100) + '...'
        });
      } catch (parseError) {
        this.logger.warn('⚠️ [NO-ACTIVITY] Failed to parse JSON response, using fallback', {
          error: parseError.message,
          responsePreview: (response?.data?.content || JSON.stringify(response)).substring(0, 200)
        });

        // Fallback structured response
        parsedResponse = {
          shouldRespond: true,
          responseType: 'ambient',
          message: null,
          animation: {
            type: 'subtle_idle',
            intensity: 'low',
            duration: 3000
          },
          timing: {
            immediate: false,
            interruptible: true,
            priority: 'low'
          },
          reasoning: 'Fallback response due to JSON parsing failure'
        };
      }

      // Determine System 1 output modalities:
      // - Default to text-only (analysis/ambient)
      // - Upgrade to ['text','audio'] ONLY if speaking is explicitly requested via tools
      //   per DUAL_BRAIN_ARCHITECTURE_README.md (System 2 controls System 1 audio)
      const speakingActivated = Boolean(parsedResponse?.tools?.includes?.('speaking') || parsedResponse?.speak === true);
      const modalityDecision = this._determineSystem1Modalities(speakingActivated);
      this.logger.debug('🎚️ [NO-ACTIVITY] Modality decision', modalityDecision);

      // Store the engagement result in memory for learning
      await this._storeNoActivityEngagement(activityLevel, {
        structured: parsedResponse,
        raw: response
      }, enrichedContext);

      // Reset quiet period based on response timing
      const resetDelay = parsedResponse.timing?.immediate ?
        pattern.threshold * 0.3 :
        pattern.threshold * 0.5;

      setTimeout(() => {
        this.state.isQuietPeriod = false;
        this.logger.debug('🔄 [NO-ACTIVITY] Quiet period reset for future engagement');
      }, resetDelay);

      return {
        structured: parsedResponse,
        raw: response,
        activityLevel,
        timeSinceLastInput
      };

    } catch (error) {
      this.logger.error(`❌ No-activity trigger failed for ${activityLevel}:`, error);
      this.state.isQuietPeriod = false;
      return null;
    }
  }

  /**
   * Generalized context gathering for various scenarios
   * Includes user profile memory + avatar profile + System 1 context data
   * @param {string} contextType - Type of context ('no_activity', 'proactive', 'general')
   * @param {Object} additionalData - Additional context-specific data
   */
  async gatherContext(contextType = 'general', additionalData = {}) {
    const context = {
      contextType,
      timestamp: Date.now(),
      userProfile: null,
      avatarProfile: null,
      contextualHints: [],
      animationSuggestions: null,
      system1Context: {
        audioInput: false,
        videoInput: false,
        lastInteractionType: 'unknown',
        mediaActivity: false
      },
      ...additionalData
    };

    // 1. Get System 1 context from MediaCoordinator if available
    try {
      context.system1Context = await this._getSystem1MediaContext();
    } catch (error) {
      this.logger.debug('Could not retrieve System 1 media context:', error);
    }

    // 2. Get user profile from memory (LangGraph MemorySaver)
    try {
      context.userProfile = await this._getUserProfileFromMemory();
    } catch (error) {
      this.logger.debug('Could not retrieve user profile from memory:', error);
    }

    // 3. Get avatar profile from CharacterService
    try {
      context.avatarProfile = await this._getAvatarProfileFromCharacterService();
    } catch (error) {
      this.logger.debug('Could not retrieve avatar profile:', error);
    }

    // 4. Generate context-specific hints and suggestions
    if (contextType === 'no_activity' && additionalData.activityLevel && additionalData.timeSinceLastInput) {
      context.contextualHints = generateContextualHints(
        additionalData.activityLevel,
        additionalData.timeSinceLastInput,
        context.system1Context
      );
      context.animationSuggestions = generateAnimationCues(
        context.avatarProfile,
        context.system1Context
      );
    } else {
      // General contextual hints
      context.contextualHints = this._generateGeneralContextualHints(context);
      context.animationSuggestions = generateAnimationCues(
        context.avatarProfile,
        context.system1Context
      );
    }

    return context;
  }

  /**
   * Generate general contextual hints for non-activity scenarios
   * @private
   */
  _generateGeneralContextualHints(context) {
    const hints = [];
    const { system1Context, userProfile, avatarProfile } = context;

    if (system1Context.audioInput) hints.push('Audio input available - user may prefer voice interaction');
    if (system1Context.videoInput) hints.push('Video input active - user present and engaged');
    if (userProfile?.preferences?.length > 0) hints.push('User preferences available for personalization');
    if (avatarProfile?.personality) hints.push('Avatar personality configured for character-appropriate responses');
    if (!system1Context.mediaActivity) hints.push('No active media - text-based interaction mode');

    return hints;
  }

  /**
   * Get System 1 context from available sources (generic implementation)
   * MediaCoordinator integration is handled at application level, not here
   */
  async _getSystem1MediaContext() {
    // Generic System 1 context - applications can override this through context parameters
    const contextHints = {
      audioInput: false,
      videoInput: false,
      lastInteractionType: 'unknown',
      mediaActivity: false
    };

    // Check if context was provided through agent service or options
    if (this.agentService?.getSystemContext) {
      try {
        const systemContext = this.agentService.getSystemContext('system1');
        Object.assign(contextHints, systemContext);
      } catch (error) {
        this.logger.debug('No system context available from agent service');
      }
    }

    return contextHints;
  }

  /**
   * Set external context provider for System 1 context (generic integration)
   * Applications can provide their own context providers without tight coupling
   */
  setContextProvider(contextProvider) {
    if (!contextProvider) {
      this.logger.warn('⚠️ Cannot set context provider - not provided');
      return false;
    }

    try {
      // Store context provider reference for System 1 context access
      if (this.agentService) {
        this.agentService.contextProvider = contextProvider;
      }

      this.logger.info('✅ Context provider set for System 1 context integration');
      return true;
    } catch (error) {
      this.logger.error('❌ Failed to set context provider:', error);
      return false;
    }
  }

  /**
   * Get user profile from LangGraph memory system
   */
  async _getUserProfileFromMemory() {
    if (!this.memoryManager) return null;

    try {
      const userMemories = await this.memoryManager.searchMemories(
        this.userId,
        {
          context: 'user_profile',
          filter: { type: 'preferences', active: true },
          limit: 5
        }
      );

      return {
        preferences: userMemories.filter(m => m.type === 'preferences'),
        conversationHistory: userMemories.filter(m => m.type === 'conversation'),
        interests: userMemories.filter(m => m.type === 'interests'),
        communicationStyle: userMemories.find(m => m.type === 'communication_style')
      };
    } catch (error) {
      this.logger.warn('Could not retrieve user profile from memory:', error);
      return null;
    }
  }

  /**
   * Get avatar profile from CharacterService (will be consolidated service)
   */
  async _getAvatarProfileFromCharacterService() {
    try {
      const characterService = this.services?.characterService;
      if (!characterService) return null;

      const currentCharacter = characterService.getCurrentCharacter();
      if (!currentCharacter) return null;

      return {
        personality: currentCharacter.personality,
        voiceStyle: currentCharacter.voiceStyle,
        behaviorGuidelines: characterService.generateBehaviorGuidelines(currentCharacter),
        contextualCues: characterService.generateContextualCues(currentCharacter),
        idleAnimations: this._generateIdleAnimations(currentCharacter),
        proactiveTopics: this._generateProactiveTopics(currentCharacter)
      };
    } catch (error) {
      this.logger.warn('Could not retrieve avatar profile:', error);
      return null;
    }
  }


  /**
   * Generate idle animations for character
   */
  _generateIdleAnimations(character) {
    const personality = character.personality;
    const animations = [];

    if (personality.enthusiasm > 0.6) {
      animations.push('subtle_bounce', 'expectant_look');
    }
    if (personality.empathy > 0.6) {
      animations.push('warm_smile', 'gentle_nod');
    }
    if (personality.creativity > 0.6) {
      animations.push('thoughtful_gesture', 'creative_spark');
    }

    return animations.length > 0 ? animations : ['neutral_idle'];
  }

  /**
   * Generate proactive conversation topics
   */
  _generateProactiveTopics(character) {
    const personality = character.personality;
    const topics = [];

    if (personality.enthusiasm > 0.7) {
      topics.push('exciting_updates', 'new_possibilities');
    }
    if (personality.empathy > 0.7) {
      topics.push('how_are_you_feeling', 'personal_check_in');
    }
    if (personality.creativity > 0.7) {
      topics.push('creative_ideas', 'inspiring_thoughts');
    }

    return topics.length > 0 ? topics : ['general_availability'];
  }

  /**
   * Build contextual prompt for System 2 no-activity engagement
   * Uses centralized prompts from noactivity.js
   */
  _buildNoActivityPrompt(enrichedContext) {
    return createNoActivityEngagementPrompt(enrichedContext);
  }

  /**
   * Format user profile for prompt
   */
  _formatUserProfile(userProfile) {
    const sections = [];

    if (userProfile.preferences?.length > 0) {
      sections.push(`Preferences: ${userProfile.preferences.map(p => p.content || p).join(', ')}`);
    }
    if (userProfile.interests?.length > 0) {
      sections.push(`Interests: ${userProfile.interests.map(i => i.content || i).join(', ')}`);
    }
    if (userProfile.communicationStyle) {
      sections.push(`Communication Style: ${userProfile.communicationStyle.content || userProfile.communicationStyle}`);
    }

    return sections.length > 0 ? sections.join('\n') : 'Standard user preferences';
  }

  /**
   * Format avatar profile for prompt
   */
  _formatAvatarProfile(avatarProfile) {
    const sections = [];

    if (avatarProfile.personality) {
      const p = avatarProfile.personality;
      sections.push(`Personality: enthusiasm(${p.enthusiasm}), empathy(${p.empathy}), creativity(${p.creativity})`);
    }
    if (avatarProfile.voiceStyle) {
      sections.push(`Voice Style: ${avatarProfile.voiceStyle}`);
    }
    if (avatarProfile.behaviorGuidelines?.length > 0) {
      sections.push(`Guidelines: ${avatarProfile.behaviorGuidelines.slice(0, 3).join(', ')}`);
    }

    return sections.length > 0 ? sections.join('\n') : 'Balanced, helpful personality';
  }

  /**
   * Store no-activity engagement result in memory
   */
  async _storeNoActivityEngagement(activityLevel, response, enrichedContext) {
    if (!this.memoryManager) return;

    try {
      await this.memoryManager.addMemories(
        this.userId,
        {
          type: 'no_activity_engagement',
          activityLevel,
          // Prefer structured payload if available; otherwise store raw
          response: response?.structured ?? response?.raw ?? response,
          context: enrichedContext,
          timestamp: Date.now(),
          source: 'dualbrain_coordinator'
        },
        'dualbrain_context'
      );
    } catch (error) {
      this.logger.warn('Failed to store no-activity engagement in memory:', error);
    }
  }

  /**
   * Record user interaction to reset no-activity timer
   * Call this method whenever user provides input (audio, video, text)
   */
  recordUserInteraction(inputType = 'general') {
    this.state.lastUserInteraction = Date.now();
    this.state.isQuietPeriod = false;

    this.logger.debug(`👤 User interaction recorded: ${inputType}`);
  }

  /**
   * Get no-activity monitoring status
   */
  getNoActivityStatus() {
    const now = Date.now();
    const timeSinceLastInput = now - this.state.lastUserInteraction;

    return {
      isMonitoring: !!this.state.noActivityTimer,
      lastUserInteraction: this.state.lastUserInteraction,
      timeSinceLastInput,
      isQuietPeriod: this.state.isQuietPeriod,
      patterns: this.state.activityPatterns
    };
  }

  /**
   * Cleanup resources
   */
  dispose() {
    this.stopDualBrainSystems();
    this._stopNoActivityMonitoring();
    this.state.lastSystem1Response = null;
    this.state.lastSystem2Analysis = null;
    this.state.pendingDecision = null;
    this.memoryManager = null;
    this.logger.info('🗑️ Clean Dual Brain Coordinator disposed');
  }

  /**
   * Check whether any input modality is active for System 1 background operation
   * Uses System 1 media context (audio/video input availability)
   */
  async _isAnyModalityActive() {
    try {
      const ctx = await this._getSystem1MediaContext();
      return Boolean(ctx?.audioInput || ctx?.videoInput);
    } catch (e) {
      return false;
    }
  }

  /**
   * Decide System 1 output modalities based on centralized config
   * - Default: ['text']
   * - Speaking activated by System 2 tools: ['text','audio'] with configured voice
   */
  _determineSystem1Modalities(speakingActivated = false) {
    try {
      const { modalityControl } = DUAL_BRAIN_CONFIG;
      const modalities = speakingActivated ? (modalityControl?.speakingModalities || ['text', 'audio']) : (modalityControl?.defaultModalities || ['text']);
      const voice = speakingActivated ? (modalityControl?.defaultVoice || null) : null;
      return { modalities, voice, speakingActivated };
    } catch (e) {
      // Safe default
      return { modalities: speakingActivated ? ['text', 'audio'] : ['text'], voice: speakingActivated ? null : null, speakingActivated };
    }
  }
}

/**
 * Factory function to create a dual-brain coordinator
 * @param {Object} agentService - The LangGraph agent service from core.js
 * @param {Object} options - Configuration options
 * @returns {DualBrainCoordinator} Dual-brain coordinator instance
 */
export function createDualBrainCoordinator(agentService, options = {}) {
  if (!agentService) {
    throw new Error('Agent service is required to create DualBrainCoordinator');
  }

  // Create services if not provided
  if (!options.services) {
    const serviceConfig = {
      ...DEFAULT_SERVICE_CONFIG,
      ...options.serviceConfig
    };

    options.services = createDualBrainServices(serviceConfig);

    logger.info('🔧 Created DualBrain services', {
      services: Object.keys(options.services)
    });
  }

  const coordinator = new DualBrainCoordinator(agentService, options);

  logger.info('🏭 Created clean DualBrainCoordinator with multi-agent supervisor pattern', {
    hasAgentService: !!agentService,
    hasServices: !!options.services,
    multiAgentSupport: true
  });

  return coordinator;
}

export default DualBrainCoordinator;