# Service Architecture Guide

## 🏗️ Service-Based DualBrain Architecture

This guide provides developers with practical information about the service-oriented architecture introduced in the DualBrain refactoring.

## 🎯 Quick Overview

The DualBrainCoordinator has been refactored from a **1,871-line monolith** into a **559-line orchestrator** with **4 specialized services**:

- **SystemInvoker**: Model invocation with schema validation
- **ContextProcessor**: Context analysis using existing ContextBridge
- **DecisionProcessor**: AI decision parsing and execution  
- **ErrorHandler**: Unified error management

## 🚀 Quick Start

### Basic Usage (Backward Compatible)
```javascript
import { createDualBrainCoordinator } from './DualBrainCoordinator.js';

// Same API as before - zero breaking changes
const coordinator = createDualBrainCoordinator(agentService, {
  characterAnalysisService,
  sessionCoordinator
});

await coordinator.initialize();
await coordinator.startDualBrainSystems();
```

### Advanced Service Configuration
```javascript
import { createDualBrainCoordinator } from './DualBrainCoordinator.js';

const coordinator = createDualBrainCoordinator(agentService, {
  characterAnalysisService,
  sessionCoordinator,
  services: {
    systemInvoker: {
      timeout: 30000,
      retryAttempts: 3,
      validateInputs: true
    },
    contextProcessor: {
      analysisInterval: 1000,
      enableCaching: true,
      confidenceThreshold: 0.5
    },
    decisionProcessor: {
      maxConcurrentDecisions: 5,
      enableValidation: true,
      enablePriorityQueuing: true
    },
    errorHandler: {
      enableRetry: true,
      enableCircuitBreaker: true,
      maxRetryAttempts: 3
    }
  }
});
```

### Direct Service Usage
```javascript
import { 
  createSystemInvoker, 
  createContextProcessor,
  createDecisionProcessor,
  createErrorHandler
} from './services/index.js';

// Create services individually
const systemInvoker = createSystemInvoker({
  timeout: 30000,
  retryAttempts: 3
});

const contextProcessor = createContextProcessor({
  analysisInterval: 1000,
  enableCaching: true
});
```

## 🔧 Service APIs

### SystemInvoker Service

**Purpose**: Structured model invocation with schema validation

```javascript
const systemInvoker = createSystemInvoker(options);

// Initialize with models
await systemInvoker.initialize({
  system1: system1Model,
  system2: system2Model  
});

// Invoke System 1 (fast responses)
const result1 = await systemInvoker.invokeSystem1({
  input: "User message",
  context: multimodalContext,
  options: { priority: 'high', timeout: 5000 }
});

// Invoke System 2 (thinking responses)
const result2 = await systemInvoker.invokeSystem2({
  input: "Complex question",
  contextualInsights: insights,
  toolCalls: [],
  options: { priority: 'medium', timeout: 30000 }
});

// Get metrics
const metrics = systemInvoker.getMetrics();
```

### ContextProcessor Service

**Purpose**: High-level context analysis using existing ContextBridge

```javascript
const contextProcessor = createContextProcessor(options);

// Start processing
await contextProcessor.start();

// Get comprehensive analysis
const analysis = await contextProcessor.getContextAnalysis({
  types: ['audio', 'video', 'conversation', 'environmental'],
  priority: 'HIGH',
  includeHistory: true
});

// Subscribe to context changes with analysis
contextProcessor.subscribeWithAnalysis('audio', (contextData, analysis) => {
  console.log('Audio context updated:', analysis);
});

// Get status
const status = contextProcessor.getStatus();
```

### DecisionProcessor Service

**Purpose**: AI decision parsing, validation, and execution

```javascript
const decisionProcessor = createDecisionProcessor(options);

// Start processing
decisionProcessor.start();

// Register tool handlers
decisionProcessor.registerToolHandler('speak', async (toolCall) => {
  // Handle speaking tool
  return { executed: true, result: 'spoken' };
});

// Process AI decision
const result = await decisionProcessor.processDecision(aiResponse, {
  priority: 'high'
});

// Execute decision immediately
const immediateResult = await decisionProcessor.executeDecisionImmediate({
  type: 'proactive_speaking',
  content: 'Hello!',
  priority: 'high'
});

// Get status
const status = decisionProcessor.getStatus();
```

### ErrorHandler Service

**Purpose**: Unified error management and recovery

```javascript
const errorHandler = createErrorHandler(options);

// Handle error with automatic classification and recovery
const result = await errorHandler.handleError(error, {
  component: 'MyComponent',
  operation: 'processData'
});

// Register custom recovery handler
errorHandler.registerRecoveryHandler('network', async (error, context) => {
  // Custom network error recovery
  return { success: true, recovered: true };
});

// Register custom escalation handler  
errorHandler.registerEscalationHandler('critical', async (error, context) => {
  // Handle critical errors
  return { escalated: true, level: 'critical' };
});

// Get component health
const health = errorHandler.getComponentHealth('MyComponent');

// Reset circuit breaker
errorHandler.resetCircuitBreaker('MyComponent');
```

## 🔄 Service Integration Patterns

### Service Factory Pattern
```javascript
import { createDualBrainServices, getServicesStatus } from './services/index.js';

// Create all services at once
const services = createDualBrainServices({
  systemInvoker: { timeout: 30000 },
  contextProcessor: { enableCaching: true },
  decisionProcessor: { maxConcurrentDecisions: 5 },
  errorHandler: { enableCircuitBreaker: true }
});

// Get aggregated status
const status = getServicesStatus(services);
```

### Service Lifecycle Management
```javascript
// Initialize services
await Promise.all([
  services.systemInvoker.initialize({ system1, system2 }),
  services.contextProcessor.initialize(contextProviders)
]);

// Start services
await Promise.all([
  services.contextProcessor.start(),
  services.decisionProcessor.start()
]);

// Stop services
await Promise.all([
  services.contextProcessor.stop(),
  services.decisionProcessor.stop()
]);
```

### Service Communication
```javascript
// Services work together seamlessly
class MyComponent {
  constructor(services) {
    this.systemInvoker = services.systemInvoker;
    this.contextProcessor = services.contextProcessor;
    this.decisionProcessor = services.decisionProcessor;
    this.errorHandler = services.errorHandler;
  }

  async processRequest(input) {
    try {
      // Get context analysis
      const analysis = await this.contextProcessor.getContextAnalysis({
        types: ['conversation'],
        priority: 'HIGH'
      });

      // Invoke appropriate system
      const result = await this.systemInvoker.invokeSystem2({
        input,
        contextualInsights: analysis.analyses
      });

      // Process decisions
      await this.decisionProcessor.processDecision(result.data);

      return result;
    } catch (error) {
      return await this.errorHandler.handleError(error, {
        component: 'MyComponent',
        operation: 'processRequest'
      });
    }
  }
}
```

## 🧪 Testing with Services

### Unit Testing Individual Services
```javascript
describe('SystemInvoker', () => {
  let systemInvoker;
  
  beforeEach(() => {
    systemInvoker = createSystemInvoker({ timeout: 5000 });
  });

  it('should invoke System 1 successfully', async () => {
    // Mock the model
    const mockModel = {
      invoke: jest.fn().mockResolvedValue({ data: 'response' })
    };
    
    await systemInvoker.initialize({ system1: mockModel });
    
    const result = await systemInvoker.invokeSystem1({
      input: 'test',
      context: {}
    });
    
    expect(result.status).toBe('SUCCESS');
    expect(mockModel.invoke).toHaveBeenCalled();
  });
});
```

### Integration Testing with Service Mocks
```javascript
describe('DualBrainCoordinator Integration', () => {
  it('should coordinate services properly', async () => {
    const mockServices = {
      systemInvoker: {
        initialize: jest.fn(),
        invokeSystem2: jest.fn().mockResolvedValue({ data: 'response' })
      },
      contextProcessor: {
        start: jest.fn(),
        getContextAnalysis: jest.fn().mockResolvedValue({ analyses: {} })
      },
      decisionProcessor: {
        start: jest.fn(),
        processDecision: jest.fn()
      },
      errorHandler: {
        handleError: jest.fn()
      }
    };

    const coordinator = createDualBrainCoordinator(agentService, {
      services: mockServices
    });

    await coordinator.initialize();
    expect(mockServices.systemInvoker.initialize).toHaveBeenCalled();
  });
});
```

## 📊 Service Monitoring

### Getting Service Status
```javascript
// Individual service status
const systemInvokerStatus = systemInvoker.getMetrics();
const contextProcessorStatus = contextProcessor.getStatus();
const decisionProcessorStatus = decisionProcessor.getStatus(); 
const errorHandlerStatus = errorHandler.getStatus();

// Coordinator aggregated status
const coordinatorStatus = coordinator.getCoordinationStatus();
console.log(coordinatorStatus.services);
```

### Service Health Monitoring
```javascript
// Monitor component health
const componentHealth = errorHandler.getComponentHealth('DualBrainCoordinator');

if (componentHealth.status === 'critical') {
  // Take action
  errorHandler.resetCircuitBreaker('DualBrainCoordinator');
}

// Monitor service metrics
const metrics = systemInvoker.getMetrics();
if (metrics.errorRate > 0.1) {
  // Handle high error rate
}
```

## 🔧 Configuration Reference

### SystemInvoker Configuration
```javascript
{
  defaultTimeout: 30000,        // Default timeout in ms
  retryAttempts: 3,            // Number of retry attempts
  retryDelayMs: 1000,          // Base delay between retries
  validateInputs: true,        // Enable input validation
  validateOutputs: true        // Enable output validation
}
```

### ContextProcessor Configuration
```javascript
{
  analysisInterval: 1000,      // Analysis interval in ms
  contextRetentionMs: 30000,   // Context retention time
  confidenceThreshold: 0.5,    // Confidence threshold for triggers
  enableCaching: true,         // Enable analysis caching
  maxCacheSize: 100           // Maximum cache size
}
```

### DecisionProcessor Configuration
```javascript
{
  maxConcurrentDecisions: 5,   // Max concurrent decisions
  decisionTimeoutMs: 10000,    // Decision timeout
  enableValidation: true,      // Enable decision validation
  enablePriorityQueuing: true, // Enable priority-based queuing
  retryAttempts: 2            // Retry attempts for failed decisions
}
```

### ErrorHandler Configuration
```javascript
{
  enableRetry: true,           // Enable retry logic
  maxRetryAttempts: 3,        // Max retry attempts
  retryDelayMs: 1000,         // Base retry delay
  enableCircuitBreaker: true,  // Enable circuit breaker
  circuitBreakerThreshold: 5,  // Error threshold for circuit breaker
  circuitBreakerTimeoutMs: 60000, // Circuit breaker timeout
  enableErrorTracking: true,   // Enable error history tracking
  maxErrorHistory: 100        // Max error history size
}
```

## 🎯 Best Practices

### Service Design Principles
1. **Single Responsibility**: Each service has one clear purpose
2. **Loose Coupling**: Services communicate through well-defined interfaces
3. **High Cohesion**: Related functionality grouped within services
4. **Error Resilience**: All services handle errors gracefully
5. **Performance Monitoring**: Built-in metrics and health checks

### Development Guidelines
1. **Use Service Factories**: Create services through factory functions
2. **Mock for Testing**: Use service mocks for unit tests
3. **Monitor Health**: Check service status regularly
4. **Handle Errors**: Use ErrorHandler for all error management
5. **Configure Appropriately**: Tune service configuration for your use case

### Performance Tips
1. **Batch Operations**: Use parallel service operations where possible
2. **Cache Intelligently**: Enable caching in ContextProcessor for better performance
3. **Monitor Metrics**: Track service performance metrics
4. **Tune Timeouts**: Adjust timeouts based on your requirements
5. **Circuit Breakers**: Use circuit breakers to prevent cascade failures

---

## 📚 Related Documentation

- **[REFACTORING_REPORT.md](../REFACTORING_REPORT.md)**: Detailed refactoring analysis
- **[DUAL_BRAIN_ARCHITECTURE_README.md](../../../../docs/arch/DUAL_BRAIN_ARCHITECTURE_README.md)**: Complete architecture guide with consolidated information

## 🎉 Conclusion

The service-based architecture provides a clean, maintainable, and extensible foundation for the DualBrain system. Each service is focused, testable, and can be used independently or as part of the coordinated system.

**Key Benefits:**
- **70% code reduction** while maintaining functionality
- **Clean service boundaries** for better maintainability  
- **Enhanced error handling** with unified management
- **Better testability** with mockable services
- **Zero breaking changes** for existing code