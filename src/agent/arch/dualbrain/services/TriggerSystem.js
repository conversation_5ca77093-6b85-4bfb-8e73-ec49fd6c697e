/**
 * TriggerSystem Service - Modern Event-Driven Analysis System
 * 
 * Implements the documented modern trigger-based architecture from MODERN_TRIGGER_SYSTEM_ARCHITECTURE.md
 * Replaces legacy interval-based periodic analysis with Discord-style events and Netflix adaptive fallback.
 * 
 * Key Features:
 * - Discord-style event-driven triggers for System 1 (fast) and System 2 (reasoning)
 * - Netflix-style adaptive intervals based on activity levels
 * - DPT-Agent dual process theory integration
 * - Activity monitoring with intelligent state management
 * - Connection-aware scheduling to prevent WebSocket pressure
 * 
 * @version 1.0.0
 */

import { createLogger, LogLevelValues as LogLevel } from '../../../../utils/logger.ts';

const logger = createLogger('TriggerSystem', LogLevel.DEBUG);

/**
 * System states for adaptive interval management
 */
export const SystemState = {
  QUIET: 'quiet',
  ACTIVE: 'active', 
  PROCESSING: 'processing',
  EMERGENCY: 'emergency'
};

/**
 * Trigger priority levels
 */
export const TriggerPriority = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  IMMEDIATE: 'immediate'
};

/**
 * TriggerSystem Service Class
 * Implements modern trigger-based architecture for dual brain coordination
 */
export class TriggerSystem {
  constructor(options = {}) {
    this.options = {
      // Adaptive intervals (Netflix nearline style)
      intervals: {
        quiet: options.intervals?.quiet || 30000,          // 30s when no activity
        active: options.intervals?.active || 5000,         // 5s when user is active
        processing: options.intervals?.processing || 15000, // 15s when System 2 is processing
        emergency: options.intervals?.emergency || 1000     // 1s for urgent situations
      },
      
      // Activity management
      activityDecayRate: options.activityDecayRate || 0.95, // 5% decay per check
      activityCheckInterval: options.activityCheckInterval || 5000, // Check every 5s
      maxTriggerHistory: options.maxTriggerHistory || 50,
      
      // Connection awareness
      respectConnectionHealth: options.respectConnectionHealth !== false,
      skipOnUnhealthyConnections: options.skipOnUnhealthyConnections !== false,
      
      ...options
    };

    this.logger = logger;
    
    // Initialize trigger system state
    this._initializeTriggerSystem();
    
    // Event handlers
    this.eventHandlers = new Map();
    
    // Timers
    this.fallbackTimer = null;
    this.activityMonitor = null;
    this.monitoringTimer = null;
    
    // Connection health checker (injected)
    this.connectionHealthChecker = null;
    
    this.logger.info('🎯 TriggerSystem service initialized', {
      intervals: this.triggerSystem.intervals,
      activityDecay: this.options.activityDecayRate,
      maxHistory: this.options.maxTriggerHistory
    });
  }

  /**
   * Initialize hybrid trigger system based on documented architecture
   * @private
   */
  _initializeTriggerSystem() {
    this.triggerSystem = {
      // System 1: Fast reactive triggers (DPT-Agent FSM style)
      system1Triggers: [
        'user-input',           // Immediate user interaction
        'audio-activity',       // Voice activity detection (VAD)
        'visual-change',        // Camera/video input changes
        'context-shift',        // Environmental context changes
        'memory-update',        // New information in memory
        'session-event'         // WebSocket session events
      ],
      
      // System 2: Thoughtful analysis triggers (DPT-Agent reasoning style)
      system2Triggers: [
        'complex-decision-needed',    // System 1 escalation
        'reasoning-required',         // Explicit reasoning request
        'conflict-resolution',        // Contradictory information
        'planning-needed',           // Future planning required
        'character-analysis',        // Personality/behavior analysis
        'proactive-opportunity'      // Proactive engagement chance
      ],
      
      // Adaptive intervals (Netflix nearline style)
      intervals: { ...this.options.intervals },
      
      // System state management
      systemState: SystemState.QUIET,
      currentInterval: this.options.intervals.quiet,
      activityScore: 0,
      lastActivity: 0,
      triggerHistory: [],
      
      // Performance metrics
      metrics: {
        system1Triggers: 0,
        system2Triggers: 0,
        fallbackActivations: 0,
        avgResponseTime: 0
      }
    };
  }

  /**
   * Start the trigger system with event-driven and adaptive fallback
   */
  async start() {
    if (this.fallbackTimer || this.activityMonitor) {
      this.logger.warn('⚠️ TriggerSystem already started');
      return;
    }

    this.logger.info('⚡ Starting hybrid trigger-based analysis system...', {
      mode: 'hybrid-event-driven',
      system1Triggers: this.triggerSystem.system1Triggers.length,
      system2Triggers: this.triggerSystem.system2Triggers.length,
      adaptiveInterval: this.triggerSystem.currentInterval
    });

    // Start adaptive fallback scheduling
    this._scheduleAdaptiveFallback();
    
    // Start activity monitoring with decay
    this._startActivityMonitoring();

    this.logger.info('✅ Hybrid trigger system activated', {
      architecture: 'event-driven + adaptive-fallback',
      patterns: ['discord-style-events', 'netflix-nearline-processing', 'dpt-agent-dual-system']
    });
  }

  /**
   * Stop the trigger system
   */
  async stop() {
    this.logger.info('🛑 Stopping trigger system...');
    
    if (this.fallbackTimer) {
      clearTimeout(this.fallbackTimer);
      this.fallbackTimer = null;
    }
    
    if (this.activityMonitor) {
      clearInterval(this.activityMonitor);
      this.activityMonitor = null;
    }
    
    if (this.monitoringTimer) {
      clearTimeout(this.monitoringTimer);
      this.monitoringTimer = null;
    }
    
    this.eventHandlers.clear();
    
    this.logger.info('✅ Trigger system stopped');
  }

  /**
   * Set connection health checker function
   * @param {Function} checker - Function that returns true if connections are healthy
   */
  setConnectionHealthChecker(checker) {
    this.connectionHealthChecker = checker;
    this.logger.debug('🔗 Connection health checker configured');
  }

  /**
   * Register System 1 trigger handler (fast response)
   * @param {string} triggerType - Type of trigger
   * @param {Function} handler - Handler function
   */
  onSystem1Trigger(triggerType, handler) {
    if (!this.triggerSystem.system1Triggers.includes(triggerType)) {
      this.logger.warn(`⚠️ Unknown System 1 trigger type: ${triggerType}`);
    }
    
    const key = `system1:${triggerType}`;
    this.eventHandlers.set(key, handler);
    
    this.logger.debug('📡 Registered System 1 trigger handler', { triggerType });
  }

  /**
   * Register System 2 trigger handler (complex reasoning)
   * @param {string} triggerType - Type of trigger
   * @param {Function} handler - Handler function
   */
  onSystem2Trigger(triggerType, handler) {
    if (!this.triggerSystem.system2Triggers.includes(triggerType)) {
      this.logger.warn(`⚠️ Unknown System 2 trigger type: ${triggerType}`);
    }
    
    const key = `system2:${triggerType}`;
    this.eventHandlers.set(key, handler);
    
    this.logger.debug('🧠 Registered System 2 trigger handler', { triggerType });
  }

  /**
   * Register fallback analysis handler
   * @param {Function} handler - Fallback handler function
   */
  onFallbackAnalysis(handler) {
    this.eventHandlers.set('fallback:analysis', handler);
    this.logger.debug('🔄 Registered fallback analysis handler');
  }

  /**
   * Fire a System 1 trigger (immediate response)
   * @param {string} triggerType - Type of trigger
   * @param {Object} data - Trigger data
   * @param {Object} options - Trigger options
   */
  fireSystem1Trigger(triggerType, data = {}, options = {}) {
    const { priority = TriggerPriority.MEDIUM, requiresSystem2 = false } = options;
    
    this.logger.debug('⚡ System 1 trigger fired', {
      type: triggerType,
      priority,
      requiresSystem2
    });

    // Update activity tracking
    this._updateSystemActivity('system1', triggerType);

    // Record trigger in history
    this._recordTrigger('system1', triggerType, data);

    // Call handler if registered
    const handler = this.eventHandlers.get(`system1:${triggerType}`);
    if (handler) {
      try {
        handler(data, options);
      } catch (error) {
        this.logger.error('❌ System 1 trigger handler failed', { triggerType, error: error.message });
      }
    }

    // Escalate to System 2 if needed
    if (requiresSystem2) {
      this.fireSystem2Trigger('complex-decision-needed', data, {
        priority: TriggerPriority.HIGH,
        reasoning: 'system1-escalation'
      });
    }

    // Update metrics
    this.triggerSystem.metrics.system1Triggers++;
  }

  /**
   * Fire a System 2 trigger (complex reasoning)
   * @param {string} triggerType - Type of trigger
   * @param {Object} data - Trigger data
   * @param {Object} options - Trigger options
   */
  fireSystem2Trigger(triggerType, data = {}, options = {}) {
    const { priority = TriggerPriority.MEDIUM, reasoning = 'explicit' } = options;
    
    this.logger.debug('🧠 System 2 trigger fired', {
      type: triggerType,
      priority,
      reasoning
    });

    // Update activity tracking
    this._updateSystemActivity('system2', triggerType);

    // Record trigger in history
    this._recordTrigger('system2', triggerType, data);

    // Call handler if registered
    const handler = this.eventHandlers.get(`system2:${triggerType}`);
    if (handler) {
      try {
        handler(data, options);
      } catch (error) {
        this.logger.error('❌ System 2 trigger handler failed', { triggerType, error: error.message });
      }
    }

    // Update metrics
    this.triggerSystem.metrics.system2Triggers++;
  }

  /**
   * Schedule adaptive fallback based on current activity
   * @private
   */
  _scheduleAdaptiveFallback() {
    if (this.fallbackTimer) {
      clearTimeout(this.fallbackTimer);
    }

    // Check connection health if checker is available
    if (this.options.respectConnectionHealth && this.connectionHealthChecker && !this.connectionHealthChecker()) {
      this.logger.debug('🔄 Skipping fallback schedule - connections unhealthy');
      // Retry in 5 seconds
      this.fallbackTimer = setTimeout(() => {
        this._scheduleAdaptiveFallback();
      }, 5000);
      return;
    }

    const currentInterval = this.triggerSystem.intervals[this.triggerSystem.systemState];
    this.triggerSystem.currentInterval = currentInterval;

    this.fallbackTimer = setTimeout(async () => {
      try {
        await this._executeFallbackAnalysis();
      } catch (error) {
        this.logger.error('❌ Fallback analysis failed:', error);
      } finally {
        this._scheduleAdaptiveFallback(); // Reschedule
      }
    }, currentInterval);

    this.logger.debug('📅 Adaptive fallback scheduled', {
      state: this.triggerSystem.systemState,
      interval: currentInterval,
      nextAnalysis: new Date(Date.now() + currentInterval).toLocaleTimeString()
    });
  }

  /**
   * Execute fallback analysis when no events trigger
   * @private
   */
  async _executeFallbackAnalysis() {
    // Update activity and check if we should continue
    this._updateSystemActivity('fallback', 'periodic');
    
    // Only perform analysis if we're in an appropriate state
    if (this.triggerSystem.systemState === SystemState.QUIET && this.triggerSystem.activityScore < 5) {
      this.logger.debug('⏸️ Skipping fallback analysis - system quiet and low activity');
      return;
    }

    // Skip if connections are unhealthy
    if (this.options.skipOnUnhealthyConnections && this.connectionHealthChecker && !this.connectionHealthChecker()) {
      this.logger.debug('⏸️ Skipping fallback analysis - connections unhealthy');
      return;
    }

    this.logger.debug('🔄 Executing adaptive fallback analysis', {
      state: this.triggerSystem.systemState,
      activityScore: this.triggerSystem.activityScore
    });

    // Call fallback handler if registered
    const handler = this.eventHandlers.get('fallback:analysis');
    if (handler) {
      try {
        await handler({
          systemState: this.triggerSystem.systemState,
          activityScore: this.triggerSystem.activityScore,
          lastActivity: this.triggerSystem.lastActivity
        });
        
        // Update metrics
        this.triggerSystem.metrics.fallbackActivations++;
      } catch (error) {
        this.logger.error('❌ Fallback analysis handler failed:', error);
      }
    } else {
      this.logger.debug('⚠️ No fallback analysis handler registered');
    }
  }

  /**
   * Start activity monitoring with decay
   * @private
   */
  _startActivityMonitoring() {
    this.activityMonitor = setInterval(() => {
      // Decay activity score over time
      this.triggerSystem.activityScore *= this.options.activityDecayRate;
      
      // Clean up old trigger history
      if (this.triggerSystem.triggerHistory.length > this.options.maxTriggerHistory) {
        this.triggerSystem.triggerHistory = this.triggerSystem.triggerHistory.slice(-this.options.maxTriggerHistory);
      }
      
      // Update system state based on activity
      this._updateSystemStateFromActivity();
      
    }, this.options.activityCheckInterval);

    this.logger.debug('📊 Activity monitoring started', {
      decayRate: this.options.activityDecayRate,
      checkInterval: this.options.activityCheckInterval
    });
  }

  /**
   * Update system activity based on triggers
   * @private
   */
  _updateSystemActivity(systemType, triggerType) {
    const now = Date.now();
    
    // Calculate activity boost based on system and trigger type
    let activityBoost = 0;
    if (systemType === 'system1') {
      activityBoost = triggerType === 'user-input' ? 10 : 5;
    } else if (systemType === 'system2') {
      activityBoost = 15; // System 2 indicates higher activity
    } else if (systemType === 'fallback') {
      activityBoost = 1; // Minimal boost for fallback
    }

    this.triggerSystem.activityScore += activityBoost;
    this.triggerSystem.lastActivity = now;

    // Update system state based on activity
    this._updateSystemStateFromActivity();

    this.logger.debug('📊 Activity updated', {
      systemType,
      triggerType,
      boost: activityBoost,
      newScore: this.triggerSystem.activityScore,
      newState: this.triggerSystem.systemState
    });
  }

  /**
   * Update system state based on current activity
   * @private
   */
  _updateSystemStateFromActivity() {
    const timeSinceLastActivity = Date.now() - this.triggerSystem.lastActivity;
    const currentScore = this.triggerSystem.activityScore;

    let newState = SystemState.QUIET;

    if (timeSinceLastActivity < 10000) { // Less than 10 seconds
      if (currentScore > 30) {
        newState = SystemState.EMERGENCY;
      } else if (currentScore > 15) {
        newState = SystemState.ACTIVE;
      } else {
        newState = SystemState.ACTIVE;
      }
    } else if (timeSinceLastActivity < 60000) { // Less than 1 minute
      newState = currentScore > 10 ? SystemState.ACTIVE : SystemState.QUIET;
    } else {
      newState = SystemState.QUIET;
    }

    if (newState !== this.triggerSystem.systemState) {
      this.logger.info('🔄 System state transition', {
        from: this.triggerSystem.systemState,
        to: newState,
        activityScore: currentScore,
        timeSinceActivity: timeSinceLastActivity
      });
      
      this.triggerSystem.systemState = newState;
    }
  }

  /**
   * Record trigger in history
   * @private
   */
  _recordTrigger(system, type, data) {
    this.triggerSystem.triggerHistory.push({
      system,
      type,
      timestamp: Date.now(),
      data
    });
  }

  /**
   * Get trigger system status
   */
  getStatus() {
    return {
      systemState: this.triggerSystem.systemState,
      currentInterval: this.triggerSystem.currentInterval,
      activityScore: this.triggerSystem.activityScore,
      lastActivity: this.triggerSystem.lastActivity,
      triggerHistory: this.triggerSystem.triggerHistory.length,
      metrics: { ...this.triggerSystem.metrics },
      handlers: {
        system1: Array.from(this.eventHandlers.keys()).filter(k => k.startsWith('system1:')).length,
        system2: Array.from(this.eventHandlers.keys()).filter(k => k.startsWith('system2:')).length,
        fallback: this.eventHandlers.has('fallback:analysis')
      },
      timers: {
        fallbackActive: !!this.fallbackTimer,
        activityMonitorActive: !!this.activityMonitor
      }
    };
  }

  /**
   * Get system metrics
   */
  getMetrics() {
    return {
      ...this.triggerSystem.metrics,
      systemState: this.triggerSystem.systemState,
      activityScore: this.triggerSystem.activityScore,
      triggerHistorySize: this.triggerSystem.triggerHistory.length
    };
  }
}

/**
 * Factory function to create a TriggerSystem service
 * @param {Object} options - Configuration options
 * @returns {TriggerSystem} TriggerSystem instance
 */
export function createTriggerSystem(options = {}) {
  return new TriggerSystem(options);
}

export default TriggerSystem;