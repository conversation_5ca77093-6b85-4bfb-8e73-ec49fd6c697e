/**
 * Decision Processor Service
 * 
 * Handles parsing, validation, and execution of AI decisions from both
 * System 1 and System 2. Uses the existing CommunicationSchemas for
 * structured decision validation and provides unified decision handling.
 * 
 * This service eliminates decision parsing complexity from DualBrainCoordinator
 * and provides consistent decision validation and execution patterns.
 */

import { createLogger } from '../../../../utils/logger.ts';
import {
  ContextualEnhancementSchema,
  validateMessage,
  createMessage
} from '../schemas/CommunicationSchemas.js';

const logger = createLogger('DecisionProcessor');

/**
 * Decision types that can be processed
 */
export const DecisionType = {
  PROACTIVE_SPEAKING: 'proactive_speaking',
  CONTEXTUAL_RESPONSE: 'contextual_response', 
  TOOL_EXECUTION: 'tool_execution',
  SYSTEM_ACTION: 'system_action',
  MEMORY_UPDATE: 'memory_update',
  STATE_CHANGE: 'state_change'
};

/**
 * Decision priorities
 */
export const DecisionPriority = {
  CRITICAL: 'critical',
  HIGH: 'high', 
  MEDIUM: 'medium',
  LOW: 'low',
  BACKGROUND: 'background'
};

/**
 * Decision execution status
 */
export const ExecutionStatus = {
  PENDING: 'pending',
  EXECUTING: 'executing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
  TIMEOUT: 'timeout'
};

/**
 * DecisionProcessor - Unified decision parsing, validation, and execution
 */
export class DecisionProcessor {
  constructor(options = {}) {
    this.options = {
      maxConcurrentDecisions: 5,
      decisionTimeoutMs: 10000,
      enableValidation: true,
      enablePriorityQueuing: true,
      retryAttempts: 2,
      ...options
    };

    this.activeDecisions = new Map(); // decisionId -> decision
    this.decisionQueue = [];
    this.decisionHistory = [];
    
    this.metrics = {
      totalDecisions: 0,
      successfulDecisions: 0,
      averageExecutionTime: 0,
      decisionsByType: {},
      decisionsByPriority: {}
    };

    this.isProcessing = false;
    this.processingInterval = null;

    // Tool execution handlers
    this.toolHandlers = new Map();
    
    logger.info('⚖️ DecisionProcessor initialized', {
      maxConcurrent: this.options.maxConcurrentDecisions,
      timeoutMs: this.options.decisionTimeoutMs,
      validation: this.options.enableValidation
    });
  }

  /**
   * Register tool execution handler
   * @param {string} toolType - Tool type (speak, animation, audio, etc.)
   * @param {Function} handler - Handler function (toolCall) => Promise<result>
   */
  registerToolHandler(toolType, handler) {
    this.toolHandlers.set(toolType, handler);
    logger.debug(`🔧 Tool handler registered: ${toolType}`);
  }

  /**
   * Start decision processing
   */
  start() {
    if (this.isProcessing) {
      logger.warn('DecisionProcessor is already running');
      return;
    }

    this.isProcessing = true;
    
    // Start processing queue
    this.processingInterval = setInterval(async () => {
      await this._processDecisionQueue();
    }, 100); // Process every 100ms

    logger.info('🚀 DecisionProcessor started');
  }

  /**
   * Stop decision processing
   */
  async stop() {
    this.isProcessing = false;
    
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }

    // Cancel active decisions
    for (const [decisionId, decision] of this.activeDecisions) {
      decision.status = ExecutionStatus.CANCELLED;
      decision.endTime = Date.now();
    }

    this.activeDecisions.clear();
    this.decisionQueue = [];

    logger.info('🛑 DecisionProcessor stopped');
  }

  /**
   * Process AI decision from System 1 or System 2
   * @param {Object} aiResponse - AI response containing decisions
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} Processing result
   */
  async processDecision(aiResponse, options = {}) {
    const startTime = Date.now();
    const decisionId = this._generateDecisionId();

    try {
      logger.debug('📋 Processing AI decision', {
        id: decisionId,
        hasToolCalls: !!(aiResponse.toolCalls?.length > 0)
      });

      // Parse the AI response into structured decisions
      const parsedDecisions = await this._parseAiResponse(aiResponse, decisionId);

      // Validate decisions if enabled
      if (this.options.enableValidation) {
        const validation = await this._validateDecisions(parsedDecisions);
        if (!validation.valid) {
          return this._createProcessingResult(decisionId, 'validation_failed', 
            validation.errors, startTime);
        }
      }

      // Queue decisions for execution
      const queuedDecisions = [];
      for (const decision of parsedDecisions) {
        const queueResult = await this._queueDecision(decision, options);
        queuedDecisions.push(queueResult);
      }

      const processingTime = Date.now() - startTime;
      this._recordDecisionProcessing(decisionId, parsedDecisions, processingTime, 'queued');

      return this._createProcessingResult(decisionId, 'queued', {
        parsedDecisions: parsedDecisions.length,
        queuedDecisions: queuedDecisions.length,
        decisions: queuedDecisions
      }, startTime);

    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error('❌ Decision processing failed', {
        id: decisionId,
        error: error.message,
        processingTime
      });

      this._recordDecisionProcessing(decisionId, [], processingTime, 'failed', error);
      return this._createProcessingResult(decisionId, 'failed', error.message, startTime);
    }
  }

  /**
   * Execute specific decision immediately (bypass queue)
   * @param {Object} decision - Decision to execute
   * @param {Object} options - Execution options
   * @returns {Promise<Object>} Execution result
   */
  async executeDecisionImmediate(decision, options = {}) {
    const startTime = Date.now();
    const executionId = this._generateDecisionId();

    try {
      logger.debug('⚡ Executing decision immediately', {
        id: executionId,
        type: decision.type,
        priority: decision.priority
      });

      // Prepare decision for execution
      const preparedDecision = {
        ...decision,
        id: executionId,
        status: ExecutionStatus.EXECUTING,
        startTime,
        options
      };

      // Execute based on decision type
      const result = await this._executeDecision(preparedDecision);

      const executionTime = Date.now() - startTime;
      this._recordDecisionExecution(executionId, decision, executionTime, 'completed');

      return {
        executionId,
        status: 'completed',
        result,
        executionTime,
        decision: preparedDecision
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      logger.error('❌ Immediate decision execution failed', {
        id: executionId,
        error: error.message,
        executionTime
      });

      this._recordDecisionExecution(executionId, decision, executionTime, 'failed', error);
      
      return {
        executionId,
        status: 'failed',
        error: error.message,
        executionTime,
        decision
      };
    }
  }

  /**
   * Get decision processing status
   * @param {string} decisionId - Decision ID to check
   * @returns {Object} Status information
   */
  getDecisionStatus(decisionId) {
    // Check active decisions
    if (this.activeDecisions.has(decisionId)) {
      return {
        id: decisionId,
        status: this.activeDecisions.get(decisionId).status,
        decision: this.activeDecisions.get(decisionId)
      };
    }

    // Check queue
    const queuedDecision = this.decisionQueue.find(d => d.id === decisionId);
    if (queuedDecision) {
      return {
        id: decisionId,
        status: ExecutionStatus.PENDING,
        queuePosition: this.decisionQueue.indexOf(queuedDecision),
        decision: queuedDecision
      };
    }

    // Check history
    const historicalDecision = this.decisionHistory.find(h => h.id === decisionId);
    if (historicalDecision) {
      return {
        id: decisionId,
        status: historicalDecision.status,
        completed: true,
        decision: historicalDecision
      };
    }

    return {
      id: decisionId,
      status: 'not_found'
    };
  }

  /**
   * Get processing metrics and status
   * @returns {Object} Metrics and status
   */
  getStatus() {
    return {
      isProcessing: this.isProcessing,
      activeDecisions: this.activeDecisions.size,
      queueLength: this.decisionQueue.length,
      metrics: this.metrics,
      registeredToolHandlers: Array.from(this.toolHandlers.keys()),
      recentDecisions: this.decisionHistory.slice(-10)
    };
  }

  /**
   * Clear decision history and metrics
   */
  clearHistory() {
    this.decisionHistory = [];
    this.metrics = {
      totalDecisions: 0,
      successfulDecisions: 0,
      averageExecutionTime: 0,
      decisionsByType: {},
      decisionsByPriority: {}
    };
    logger.info('🧹 DecisionProcessor history cleared');
  }

  // Private Methods

  /**
   * Parse AI response into structured decisions
   * @private
   */
  async _parseAiResponse(aiResponse, decisionId) {
    const decisions = [];

    // Handle tool calls as decisions
    if (aiResponse.toolCalls && aiResponse.toolCalls.length > 0) {
      for (const toolCall of aiResponse.toolCalls) {
        decisions.push({
          id: `${decisionId}_tool_${decisions.length}`,
          type: DecisionType.TOOL_EXECUTION,
          priority: toolCall.priority || DecisionPriority.MEDIUM,
          toolCall: toolCall,
          createdAt: Date.now(),
          source: 'ai_response'
        });
      }
    }

    // Handle direct text response as speaking decision
    if (aiResponse.content || aiResponse.text) {
      decisions.push({
        id: `${decisionId}_speak`,
        type: DecisionType.PROACTIVE_SPEAKING,
        priority: DecisionPriority.HIGH,
        content: aiResponse.content || aiResponse.text,
        createdAt: Date.now(),
        source: 'ai_response'
      });
    }

    // Handle memory updates
    if (aiResponse.memoryUpdates) {
      decisions.push({
        id: `${decisionId}_memory`,
        type: DecisionType.MEMORY_UPDATE,
        priority: DecisionPriority.LOW,
        memoryUpdates: aiResponse.memoryUpdates,
        createdAt: Date.now(),
        source: 'ai_response'
      });
    }

    // Handle contextual insights
    if (aiResponse.contextualInsights) {
      decisions.push({
        id: `${decisionId}_context`,
        type: DecisionType.CONTEXTUAL_RESPONSE,
        priority: DecisionPriority.MEDIUM,
        contextualInsights: aiResponse.contextualInsights,
        createdAt: Date.now(),
        source: 'ai_response'
      });
    }

    return decisions;
  }

  /**
   * Validate decisions using schemas
   * @private
   */
  async _validateDecisions(decisions) {
    const errors = [];
    const warnings = [];

    for (const decision of decisions) {
      // Validate decision structure
      if (!decision.id || !decision.type || !decision.priority) {
        errors.push(`Decision missing required fields: ${JSON.stringify(decision)}`);
        continue;
      }

      // Validate decision type
      if (!Object.values(DecisionType).includes(decision.type)) {
        errors.push(`Invalid decision type: ${decision.type}`);
        continue;
      }

      // Validate priority
      if (!Object.values(DecisionPriority).includes(decision.priority)) {
        errors.push(`Invalid decision priority: ${decision.priority}`);
        continue;
      }

      // Validate tool calls if present
      if (decision.type === DecisionType.TOOL_EXECUTION && decision.toolCall) {
        const toolValidation = this._validateToolCall(decision.toolCall);
        if (!toolValidation.valid) {
          errors.push(`Tool call validation failed: ${toolValidation.errors.join(', ')}`);
        }
      }

      // Validate contextual insights if present
      if (decision.type === DecisionType.CONTEXTUAL_RESPONSE && decision.contextualInsights) {
        const contextValidation = validateMessage(
          { contextualInsights: decision.contextualInsights },
          { contextualInsights: ContextualEnhancementSchema.contextualInsights }
        );
        if (!contextValidation.valid) {
          warnings.push(`Contextual insights validation: ${contextValidation.errors.join(', ')}`);
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate tool call structure
   * @private
   */
  _validateToolCall(toolCall) {
    const errors = [];

    if (!toolCall.type || !toolCall.name) {
      errors.push('Tool call missing type or name');
    }

    if (toolCall.type && !['speak', 'animation', 'audio', 'visual', 'system', 'memory'].includes(toolCall.type)) {
      errors.push(`Invalid tool type: ${toolCall.type}`);
    }

    if (toolCall.confidence !== undefined && (toolCall.confidence < 0 || toolCall.confidence > 1)) {
      errors.push('Tool call confidence must be between 0 and 1');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Queue decision for execution
   * @private
   */
  async _queueDecision(decision, options) {
    decision.status = ExecutionStatus.PENDING;
    decision.queuedAt = Date.now();
    decision.options = options;

    // Add to appropriate position in queue based on priority
    if (this.options.enablePriorityQueuing) {
      this._insertByPriority(decision);
    } else {
      this.decisionQueue.push(decision);
    }

    logger.debug(`📤 Decision queued: ${decision.id} (${decision.type}, ${decision.priority})`);

    return {
      id: decision.id,
      status: ExecutionStatus.PENDING,
      queuePosition: this.decisionQueue.indexOf(decision),
      estimatedWaitTime: this._estimateWaitTime(decision)
    };
  }

  /**
   * Process decision queue
   * @private
   */
  async _processDecisionQueue() {
    if (!this.isProcessing || this.decisionQueue.length === 0) {
      return;
    }

    // Check if we can process more decisions
    if (this.activeDecisions.size >= this.options.maxConcurrentDecisions) {
      return;
    }

    // Get next decision from queue
    const decision = this.decisionQueue.shift();
    if (!decision) return;

    // Move to active decisions
    this.activeDecisions.set(decision.id, decision);
    decision.status = ExecutionStatus.EXECUTING;
    decision.startTime = Date.now();

    // Execute decision asynchronously
    this._executeDecisionAsync(decision);
  }

  /**
   * Execute decision asynchronously
   * @private
   */
  async _executeDecisionAsync(decision) {
    try {
      const result = await Promise.race([
        this._executeDecision(decision),
        this._createTimeoutPromise(this.options.decisionTimeoutMs)
      ]);

      decision.status = ExecutionStatus.COMPLETED;
      decision.endTime = Date.now();
      decision.result = result;

      logger.debug(`✅ Decision completed: ${decision.id}`, {
        type: decision.type,
        executionTime: decision.endTime - decision.startTime
      });

      this._recordDecisionExecution(decision.id, decision, 
        decision.endTime - decision.startTime, 'completed');

    } catch (error) {
      decision.status = error.message === 'timeout' ? ExecutionStatus.TIMEOUT : ExecutionStatus.FAILED;
      decision.endTime = Date.now();
      decision.error = error.message;

      logger.error(`❌ Decision failed: ${decision.id}`, {
        error: error.message,
        executionTime: decision.endTime - decision.startTime
      });

      this._recordDecisionExecution(decision.id, decision,
        decision.endTime - decision.startTime, decision.status, error);
    } finally {
      // Move from active to history
      this.activeDecisions.delete(decision.id);
      this.decisionHistory.push(decision);

      // Keep history manageable
      if (this.decisionHistory.length > 100) {
        this.decisionHistory.shift();
      }
    }
  }

  /**
   * Execute decision based on its type
   * @private
   */
  async _executeDecision(decision) {
    switch (decision.type) {
      case DecisionType.TOOL_EXECUTION:
        return await this._executeToolCall(decision);

      case DecisionType.PROACTIVE_SPEAKING:
        return await this._executeProactiveSpeaking(decision);

      case DecisionType.CONTEXTUAL_RESPONSE:
        return await this._executeContextualResponse(decision);

      case DecisionType.MEMORY_UPDATE:
        return await this._executeMemoryUpdate(decision);

      case DecisionType.SYSTEM_ACTION:
        return await this._executeSystemAction(decision);

      case DecisionType.STATE_CHANGE:
        return await this._executeStateChange(decision);

      default:
        throw new Error(`Unsupported decision type: ${decision.type}`);
    }
  }

  /**
   * Execute tool call
   * @private
   */
  async _executeToolCall(decision) {
    const toolCall = decision.toolCall;
    const handler = this.toolHandlers.get(toolCall.type);

    if (!handler) {
      throw new Error(`No handler registered for tool type: ${toolCall.type}`);
    }

    logger.debug(`🔧 Executing tool: ${toolCall.type}.${toolCall.name}`);

    try {
      const result = await handler(toolCall);
      return {
        toolType: toolCall.type,
        toolName: toolCall.name,
        executed: true,
        result
      };
    } catch (error) {
      logger.error(`Tool execution failed: ${toolCall.type}.${toolCall.name}`, error);
      throw error;
    }
  }

  /**
   * Execute proactive speaking
   * @private
   */
  async _executeProactiveSpeaking(decision) {
    const speakHandler = this.toolHandlers.get('speak');
    if (!speakHandler) {
      throw new Error('No speak handler registered');
    }

    const speakCall = {
      type: 'speak',
      name: 'proactive_speak',
      parameters: {
        text: decision.content,
        priority: decision.priority,
        reason: 'proactive_decision'
      }
    };

    return await speakHandler(speakCall);
  }

  /**
   * Execute contextual response
   * @private
   */
  async _executeContextualResponse(decision) {
    // Process contextual insights and generate appropriate response
    const insights = decision.contextualInsights;
    
    // This could trigger additional AI processing or tool calls
    // For now, we'll log the contextual response
    logger.info('📋 Processing contextual response', {
      insights: Object.keys(insights)
    });

    return {
      processed: true,
      contextualInsights: insights,
      timestamp: Date.now()
    };
  }

  /**
   * Execute memory update
   * @private
   */
  async _executeMemoryUpdate(decision) {
    const updates = decision.memoryUpdates;
    
    logger.debug('💾 Processing memory updates', {
      updateCount: Object.keys(updates).length
    });

    // Memory updates would typically be handled by a memory service
    return {
      updated: true,
      updates: Object.keys(updates),
      timestamp: Date.now()
    };
  }

  /**
   * Execute system action
   * @private
   */
  async _executeSystemAction(decision) {
    logger.debug('⚙️ Processing system action', {
      action: decision.action
    });

    return {
      executed: true,
      action: decision.action,
      timestamp: Date.now()
    };
  }

  /**
   * Execute state change
   * @private
   */
  async _executeStateChange(decision) {
    logger.debug('🔄 Processing state change', {
      state: decision.state
    });

    return {
      changed: true,
      state: decision.state,
      timestamp: Date.now()
    };
  }

  /**
   * Insert decision in queue by priority
   * @private
   */
  _insertByPriority(decision) {
    const priorityOrder = [
      DecisionPriority.CRITICAL,
      DecisionPriority.HIGH,
      DecisionPriority.MEDIUM,
      DecisionPriority.LOW,
      DecisionPriority.BACKGROUND
    ];

    const decisionPriorityIndex = priorityOrder.indexOf(decision.priority);
    
    // Find insertion point
    let insertIndex = this.decisionQueue.length;
    for (let i = 0; i < this.decisionQueue.length; i++) {
      const queuedPriorityIndex = priorityOrder.indexOf(this.decisionQueue[i].priority);
      if (decisionPriorityIndex < queuedPriorityIndex) {
        insertIndex = i;
        break;
      }
    }

    this.decisionQueue.splice(insertIndex, 0, decision);
  }

  /**
   * Estimate wait time for decision
   * @private
   */
  _estimateWaitTime(decision) {
    const position = this.decisionQueue.indexOf(decision);
    const avgExecutionTime = this.metrics.averageExecutionTime || 1000;
    return position * avgExecutionTime;
  }

  /**
   * Create timeout promise
   * @private
   */
  _createTimeoutPromise(timeoutMs) {
    return new Promise((_, reject) => {
      setTimeout(() => reject(new Error('timeout')), timeoutMs);
    });
  }

  /**
   * Record decision processing metrics
   * @private
   */
  _recordDecisionProcessing(id, decisions, processingTime, status, error = null) {
    // Implementation for recording metrics
  }

  /**
   * Record decision execution metrics
   * @private
   */
  _recordDecisionExecution(id, decision, executionTime, status, error = null) {
    this.metrics.totalDecisions++;
    
    if (status === 'completed') {
      this.metrics.successfulDecisions++;
    }

    // Update type metrics
    if (!this.metrics.decisionsByType[decision.type]) {
      this.metrics.decisionsByType[decision.type] = 0;
    }
    this.metrics.decisionsByType[decision.type]++;

    // Update priority metrics
    if (!this.metrics.decisionsByPriority[decision.priority]) {
      this.metrics.decisionsByPriority[decision.priority] = 0;
    }
    this.metrics.decisionsByPriority[decision.priority]++;

    // Update average execution time
    this.metrics.averageExecutionTime = 
      (this.metrics.averageExecutionTime * (this.metrics.totalDecisions - 1) + executionTime) /
      this.metrics.totalDecisions;
  }

  /**
   * Generate decision ID
   * @private
   */
  _generateDecisionId() {
    return `dec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Create processing result
   * @private
   */
  _createProcessingResult(id, status, data, startTime) {
    return {
      decisionId: id,
      status,
      data,
      processingTime: Date.now() - startTime,
      timestamp: Date.now()
    };
  }
}

/**
 * Factory function to create DecisionProcessor
 */
export function createDecisionProcessor(options = {}) {
  return new DecisionProcessor(options);
}

export default {
  DecisionProcessor,
  DecisionType,
  DecisionPriority,
  ExecutionStatus,
  createDecisionProcessor
};