/**
 * DualBrain Services Index
 * 
 * Centralized exports for all dualbrain services. This provides a clean
 * interface for importing services throughout the architecture.
 * 
 * Services are organized by functionality and provide consistent APIs
 * for managing dual brain coordination complexity.
 */

// System Invoker - Model invocation with schema validation
import { 
  SystemInvoker as SystemInvokerClass,
  SystemType,
  InvocationStatus,
  createSystemInvoker
} from './SystemInvoker.js';

// Context Processor - High-level context analysis
import {
  ContextProcessor as ContextProcessorClass,
  AnalysisType,
  ProcessingPriority,
  createContextProcessor
} from './ContextProcessor.js';

// Decision Processor - AI decision parsing and execution
import {
  DecisionProcessor as DecisionProcessorClass,
  DecisionType,
  DecisionPriority,
  ExecutionStatus,
  createDecisionProcessor
} from './DecisionProcessor.js';

// Error Handler - Unified error management
import {
  ErrorHandler as ErrorHandlerClass,
  ErrorCategory,
  ErrorSeverity,
  RecoveryStrategy,
  HandlingStatus,
  createErrorHandler
} from './ErrorHandler.js';

// Trigger System - Modern event-driven analysis system
import {
  TriggerSystem as TriggerSystemClass,
  SystemState,
  TriggerPriority,
  createTriggerSystem
} from './TriggerSystem.js';

// Re-export all the classes and constants
export {
  SystemInvokerClass as SystemInvoker,
  SystemType,
  InvocationStatus,
  createSystemInvoker,
  ContextProcessorClass as ContextProcessor,
  AnalysisType,
  ProcessingPriority,
  createContextProcessor,
  DecisionProcessorClass as DecisionProcessor,
  DecisionType,
  DecisionPriority,
  ExecutionStatus,
  createDecisionProcessor,
  ErrorHandlerClass as ErrorHandler,
  ErrorCategory,
  ErrorSeverity,
  RecoveryStrategy,
  HandlingStatus,
  createErrorHandler,
  TriggerSystemClass as TriggerSystem,
  SystemState,
  TriggerPriority,
  createTriggerSystem
};

// Service factory functions for easy initialization
export const createDualBrainServices = (options = {}) => {
  // Use the imported classes directly
  const systemInvoker = new SystemInvokerClass(options.systemInvoker);
  const contextProcessor = new ContextProcessorClass(options.contextProcessor);
  const decisionProcessor = new DecisionProcessorClass(options.decisionProcessor);
  const errorHandler = new ErrorHandlerClass(options.errorHandler);
  const triggerSystem = new TriggerSystemClass(options.triggerSystem);

  return {
    systemInvoker,
    contextProcessor,
    decisionProcessor,
    errorHandler,
    triggerSystem
  };
};

// Default service configurations
export const DEFAULT_SERVICE_CONFIG = {
  systemInvoker: {
    defaultTimeout: 30000,
    retryAttempts: 3,
    validateInputs: true,
    validateOutputs: true
  },
  contextProcessor: {
    analysisInterval: 1000,
    contextRetentionMs: 30000,
    confidenceThreshold: 0.5,
    enableCaching: true
  },
  decisionProcessor: {
    maxConcurrentDecisions: 5,
    decisionTimeoutMs: 10000,
    enableValidation: true,
    enablePriorityQueuing: true
  },
  errorHandler: {
    enableRetry: true,
    maxRetryAttempts: 3,
    enableCircuitBreaker: true,
    enableRecoveryStrategies: true
  },
  triggerSystem: {
    intervals: {
      quiet: 30000,      // 30s when no activity
      active: 5000,      // 5s when user is active  
      processing: 15000, // 15s when System 2 is processing
      emergency: 1000    // 1s for urgent situations
    },
    activityDecayRate: 0.95,
    respectConnectionHealth: true,
    skipOnUnhealthyConnections: true
  }
};

// Service status aggregator
export const getServicesStatus = (services) => {
  const { systemInvoker, contextProcessor, decisionProcessor, errorHandler, triggerSystem } = services;
  
  return {
    timestamp: Date.now(),
    services: {
      systemInvoker: {
        initialized: !!systemInvoker,
        metrics: systemInvoker?.getMetrics?.() || null
      },
      contextProcessor: {
        initialized: !!contextProcessor,
        status: contextProcessor?.getStatus?.() || null
      },
      decisionProcessor: {
        initialized: !!decisionProcessor,
        status: decisionProcessor?.getStatus?.() || null
      },
      errorHandler: {
        initialized: !!errorHandler,
        status: errorHandler?.getStatus?.() || null
      },
      triggerSystem: {
        initialized: !!triggerSystem,
        status: triggerSystem?.getStatus?.() || null
      }
    }
  };
};

export default {
  createDualBrainServices,
  DEFAULT_SERVICE_CONFIG,
  getServicesStatus
};