/**
 * Context Processor Service
 * 
 * High-level interface for processing multimodal context data using the
 * existing ContextBridge architecture. Provides business logic for context
 * analysis, aggregation, and decision making.
 * 
 * This service leverages the well-designed ContextBridge and ContextProvider
 * interfaces to eliminate context processing complexity from DualBrainCoordinator.
 */

import { createLogger } from '../../../../utils/logger.ts';
import {
  ContextBridge,
  ContextProvider,
  ContextData,
  ContextDataType,
  createContextBridge
} from '../interfaces/ContextBridge.js';

const logger = createLogger('ContextProcessor');

/**
 * Context analysis result types
 */
export const AnalysisType = {
  USER_ENGAGEMENT: 'user_engagement',
  ENVIRONMENTAL: 'environmental',
  CONVERSATION_FLOW: 'conversation_flow',
  DECISION_TRIGGERS: 'decision_triggers',
  SYSTEM_HEALTH: 'system_health'
};

/**
 * Context processing priorities
 */
export const ProcessingPriority = {
  CRITICAL: 'critical',
  HIGH: 'high',
  NORMAL: 'normal',
  LOW: 'low',
  BACKGROUND: 'background'
};

/**
 * ContextProcessor - High-level context analysis and processing
 */
export class ContextProcessor {
  constructor(options = {}) {
    this.options = {
      analysisInterval: 1000, // 1 second
      contextRetentionMs: 30000, // 30 seconds
      confidenceThreshold: 0.5,
      enableCaching: true,
      maxCacheSize: 100,
      ...options
    };

    // Initialize ContextBridge
    this.contextBridge = createContextBridge({
      bufferSize: this.options.maxCacheSize,
      maxAge: this.options.contextRetentionMs
    });

    this.analysisCache = new Map();
    this.analysisHistory = [];
    this.processingMetrics = {
      totalAnalyses: 0,
      successfulAnalyses: 0,
      averageProcessingTime: 0,
      lastAnalysisTimestamp: null
    };

    this.isRunning = false;
    this.analysisInterval = null;

    logger.info('🧮 ContextProcessor initialized', {
      analysisInterval: this.options.analysisInterval,
      retentionMs: this.options.contextRetentionMs,
      confidenceThreshold: this.options.confidenceThreshold
    });
  }

  /**
   * Initialize with context providers
   * @param {ContextProvider[]} providers - Array of context providers
   */
  async initialize(providers = []) {
    try {
      // Register all providers with the bridge
      for (const provider of providers) {
        if (provider instanceof ContextProvider) {
          this.contextBridge.registerProvider(provider);
        } else {
          logger.warn('Invalid provider type, skipping:', provider);
        }
      }

      // Start the context bridge
      await this.contextBridge.start();

      logger.info('✅ ContextProcessor initialized with providers', {
        providerCount: providers.length,
        bridgeStatus: this.contextBridge.getStatus()
      });

      return true;
    } catch (error) {
      logger.error('❌ Failed to initialize ContextProcessor:', error);
      throw error;
    }
  }

  /**
   * Start continuous context processing
   */
  async start() {
    if (this.isRunning) {
      logger.warn('ContextProcessor is already running');
      return;
    }

    this.isRunning = true;

    // Start periodic analysis
    this.analysisInterval = setInterval(async () => {
      try {
        await this._performPeriodicAnalysis();
      } catch (error) {
        logger.error('Error in periodic analysis:', error);
      }
    }, this.options.analysisInterval);

    logger.info('🚀 ContextProcessor started');
  }

  /**
   * Stop context processing
   */
  async stop() {
    this.isRunning = false;

    if (this.analysisInterval) {
      clearInterval(this.analysisInterval);
      this.analysisInterval = null;
    }

    await this.contextBridge.stop();
    logger.info('🛑 ContextProcessor stopped');
  }

  /**
   * Get comprehensive context analysis
   * @param {Object} options - Analysis options
   * @param {string[]} options.types - Context types to analyze
   * @param {string} options.priority - Analysis priority level
   * @param {boolean} options.includeHistory - Include historical context
   * @returns {Promise<Object>} Analysis result
   */
  async getContextAnalysis(options = {}) {
    const startTime = Date.now();
    const {
      types = Object.values(ContextDataType),
      priority = ProcessingPriority.NORMAL,
      includeHistory = false
    } = options;

    try {
      logger.debug('🔍 Starting context analysis', {
        types,
        priority,
        includeHistory
      });

      // Get aggregated context from bridge
      const aggregatedContext = this.contextBridge.getAggregatedContext(types);

      // Perform different types of analysis
      const analysis = {
        timestamp: Date.now(),
        priority,
        contextData: aggregatedContext,
        analyses: {}
      };

      // User engagement analysis
      if (this._shouldPerformAnalysis(AnalysisType.USER_ENGAGEMENT, priority)) {
        analysis.analyses.userEngagement = await this._analyzeUserEngagement(aggregatedContext);
      }

      // Environmental analysis
      if (this._shouldPerformAnalysis(AnalysisType.ENVIRONMENTAL, priority)) {
        analysis.analyses.environmental = await this._analyzeEnvironmental(aggregatedContext);
      }

      // Conversation flow analysis
      if (this._shouldPerformAnalysis(AnalysisType.CONVERSATION_FLOW, priority)) {
        analysis.analyses.conversationFlow = await this._analyzeConversationFlow(aggregatedContext);
      }

      // Decision triggers analysis
      if (this._shouldPerformAnalysis(AnalysisType.DECISION_TRIGGERS, priority)) {
        analysis.analyses.decisionTriggers = await this._analyzeDecisionTriggers(aggregatedContext);
      }

      // System health analysis
      if (this._shouldPerformAnalysis(AnalysisType.SYSTEM_HEALTH, priority)) {
        analysis.analyses.systemHealth = await this._analyzeSystemHealth();
      }

      // Add historical context if requested
      if (includeHistory) {
        analysis.history = this._getRelevantHistory();
      }

      const processingTime = Date.now() - startTime;
      this._recordAnalysis(analysis, processingTime, 'success');

      logger.debug('✅ Context analysis completed', {
        processingTime,
        analysisTypes: Object.keys(analysis.analyses)
      });

      return analysis;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this._recordAnalysis(null, processingTime, 'failed', error);

      logger.error('❌ Context analysis failed:', error);
      throw error;
    }
  }

  /**
   * Get specific context type with analysis
   * @param {string} contextType - Context type to retrieve
   * @param {Object} analysisOptions - Analysis options
   * @returns {Promise<Object>} Context with analysis
   */
  async getContextWithAnalysis(contextType, analysisOptions = {}) {
    const contextData = this.contextBridge.getCurrentContext(contextType);
    
    if (!contextData) {
      return null;
    }

    // Add analysis based on context type
    const analysis = await this._analyzeSpecificContext(contextType, contextData, analysisOptions);

    return {
      context: contextData,
      analysis,
      timestamp: Date.now()
    };
  }

  /**
   * Subscribe to context changes with analysis
   * @param {string} contextType - Context type to monitor
   * @param {Function} callback - Callback with analysis (contextData, analysis) => void
   * @param {Object} analysisOptions - Analysis options
   */
  subscribeWithAnalysis(contextType, callback, analysisOptions = {}) {
    this.contextBridge.subscribe(contextType, async (contextData) => {
      try {
        const analysis = await this._analyzeSpecificContext(contextType, contextData, analysisOptions);
        callback(contextData, analysis);
      } catch (error) {
        logger.error(`Error analyzing context ${contextType}:`, error);
        callback(contextData, null);
      }
    });
  }

  /**
   * Get processing metrics and status
   * @returns {Object} Metrics and status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      bridgeStatus: this.contextBridge.getStatus(),
      processingMetrics: this.processingMetrics,
      cacheSize: this.analysisCache.size,
      historySize: this.analysisHistory.length,
      recentAnalyses: this.analysisHistory.slice(-5)
    };
  }

  /**
   * Clear analysis cache and history
   */
  clearCache() {
    this.analysisCache.clear();
    this.analysisHistory = [];
    this.processingMetrics = {
      totalAnalyses: 0,
      successfulAnalyses: 0,
      averageProcessingTime: 0,
      lastAnalysisTimestamp: null
    };
    logger.info('🧹 ContextProcessor cache cleared');
  }

  // Private Analysis Methods

  /**
   * Perform periodic context analysis
   * @private
   */
  async _performPeriodicAnalysis() {
    if (!this.isRunning) return;

    try {
      const analysis = await this.getContextAnalysis({
        priority: ProcessingPriority.BACKGROUND,
        includeHistory: false
      });

      // Cache the analysis
      if (this.options.enableCaching) {
        this.analysisCache.set(Date.now(), analysis);
        this._trimCache();
      }

    } catch (error) {
      logger.warn('Periodic analysis failed:', error.message);
    }
  }

  /**
   * Analyze user engagement from context
   * @private
   */
  async _analyzeUserEngagement(context) {
    const engagement = {
      level: 'unknown',
      confidence: 0,
      factors: [],
      recommendations: []
    };

    // Audio engagement analysis
    if (context[ContextDataType.AUDIO]) {
      const audioContext = context[ContextDataType.AUDIO];
      const audioData = audioContext.data;

      if (audioData.volume > 0.3) {
        engagement.factors.push('active_speaking');
        engagement.level = 'high';
        engagement.confidence += 0.3;
      }

      if (audioData.vadActivity?.isActive) {
        engagement.factors.push('voice_activity');
        engagement.confidence += 0.2;
      }
    }

    // Visual engagement analysis
    if (context[ContextDataType.VIDEO] || context[ContextDataType.FACE]) {
      const visualContext = context[ContextDataType.VIDEO] || context[ContextDataType.FACE];
      const visualData = visualContext.data;

      if (visualData.attention?.eyeContact > 0.7) {
        engagement.factors.push('direct_eye_contact');
        engagement.level = 'high';
        engagement.confidence += 0.4;
      }

      if (visualData.attention?.engagementScore > 0.6) {
        engagement.factors.push('visual_engagement');
        engagement.confidence += 0.3;
      }
    }

    // Conversation engagement
    if (context[ContextDataType.CONVERSATION]) {
      const convContext = context[ContextDataType.CONVERSATION];
      const convData = convContext.data;

      if (convData.turnCount > 3) {
        engagement.factors.push('active_conversation');
        engagement.confidence += 0.2;
      }

      if (convData.lastUserMessage?.confidence > 0.8) {
        engagement.factors.push('clear_communication');
        engagement.confidence += 0.2;
      }
    }

    // Normalize confidence
    engagement.confidence = Math.min(1.0, engagement.confidence);

    // Generate recommendations
    if (engagement.confidence < this.options.confidenceThreshold) {
      engagement.recommendations.push('Consider prompting user interaction');
      engagement.recommendations.push('Check audio/video quality');
    }

    if (engagement.level === 'unknown' && engagement.confidence > 0.3) {
      engagement.level = engagement.confidence > 0.7 ? 'high' : 'medium';
    }

    return engagement;
  }

  /**
   * Analyze environmental context
   * @private
   */
  async _analyzeEnvironmental(context) {
    const environmental = {
      conditions: {},
      suitabilityScore: 0,
      issues: [],
      recommendations: []
    };

    // Audio environment
    if (context[ContextDataType.AUDIO]) {
      const audioData = context[ContextDataType.AUDIO].data;
      
      environmental.conditions.backgroundNoise = audioData.quality || 'unknown';
      environmental.conditions.audioQuality = audioData.quality;

      if (audioData.quality === 'poor' || audioData.quality === 'distorted') {
        environmental.issues.push('Poor audio quality detected');
        environmental.recommendations.push('Check microphone or reduce background noise');
      } else {
        environmental.suitabilityScore += 0.5;
      }
    }

    // Visual environment
    if (context[ContextDataType.VIDEO]) {
      const videoData = context[ContextDataType.VIDEO].data;
      
      environmental.conditions.lighting = videoData.quality || 'unknown';
      environmental.conditions.visualClarity = videoData.quality;

      if (videoData.quality === 'poor') {
        environmental.issues.push('Poor lighting or video quality');
        environmental.recommendations.push('Improve lighting conditions');
      } else {
        environmental.suitabilityScore += 0.5;
      }
    }

    // System environment
    if (context[ContextDataType.ENVIRONMENTAL]) {
      const envData = context[ContextDataType.ENVIRONMENTAL].data;
      
      environmental.conditions.systemLoad = envData.processingLoad || 0;
      environmental.conditions.connectionQuality = envData.connectionQuality || 'unknown';

      if (envData.processingLoad > 0.8) {
        environmental.issues.push('High system load detected');
        environmental.recommendations.push('Consider reducing processing load');
      }

      if (envData.connectionQuality === 'poor') {
        environmental.issues.push('Poor network connection');
        environmental.recommendations.push('Check network connectivity');
      }
    }

    environmental.suitabilityScore = Math.min(1.0, environmental.suitabilityScore);

    return environmental;
  }

  /**
   * Analyze conversation flow
   * @private
   */
  async _analyzeConversationFlow(context) {
    const flow = {
      continuity: 0,
      pacing: 'unknown',
      naturalness: 0,
      suggestions: []
    };

    if (context[ContextDataType.CONVERSATION]) {
      const convData = context[ContextDataType.CONVERSATION].data;

      // Analyze continuity
      if (convData.turnCount > 0) {
        flow.continuity = Math.min(1.0, convData.turnCount / 10);
      }

      // Analyze pacing
      if (convData.conversationAge && convData.turnCount) {
        const avgTurnTime = convData.conversationAge / convData.turnCount;
        if (avgTurnTime < 5000) { // 5 seconds
          flow.pacing = 'fast';
        } else if (avgTurnTime < 15000) { // 15 seconds
          flow.pacing = 'normal';
        } else {
          flow.pacing = 'slow';
        }
      }

      // Analyze naturalness based on transcript confidence
      if (convData.transcriptConfidence) {
        flow.naturalness = convData.transcriptConfidence;
      }

      // Generate suggestions
      if (flow.continuity < 0.3) {
        flow.suggestions.push('Consider initiating conversation');
      }

      if (flow.pacing === 'slow') {
        flow.suggestions.push('User may need more engagement');
      }

      if (flow.naturalness < 0.7) {
        flow.suggestions.push('Speech recognition may need adjustment');
      }
    }

    return flow;
  }

  /**
   * Analyze decision triggers
   * @private
   */
  async _analyzeDecisionTriggers(context) {
    const triggers = {
      proactiveSpeaking: false,
      contextualResponse: false,
      systemAction: false,
      triggers: [],
      confidence: 0
    };

    // Check for proactive speaking triggers
    const engagement = await this._analyzeUserEngagement(context);
    if (engagement.level === 'high' && engagement.confidence > 0.8) {
      triggers.proactiveSpeaking = true;
      triggers.triggers.push({
        type: 'high_engagement',
        reason: 'User is highly engaged',
        confidence: engagement.confidence
      });
    }

    // Check for contextual response triggers
    if (context[ContextDataType.CONVERSATION]) {
      const convData = context[ContextDataType.CONVERSATION].data;
      if (convData.lastUserMessage && convData.lastUserMessage.confidence > 0.8) {
        triggers.contextualResponse = true;
        triggers.triggers.push({
          type: 'clear_user_input',
          reason: 'Clear user message received',
          confidence: convData.lastUserMessage.confidence
        });
      }
    }

    // Check for system action triggers
    if (context[ContextDataType.ENVIRONMENTAL]) {
      const envData = context[ContextDataType.ENVIRONMENTAL].data;
      if (envData.errorRate > 0.1) {
        triggers.systemAction = true;
        triggers.triggers.push({
          type: 'system_issues',
          reason: 'High error rate detected',
          confidence: 0.9
        });
      }
    }

    // Calculate overall confidence
    if (triggers.triggers.length > 0) {
      triggers.confidence = triggers.triggers.reduce((sum, t) => sum + t.confidence, 0) / triggers.triggers.length;
    }

    return triggers;
  }

  /**
   * Analyze system health
   * @private
   */
  async _analyzeSystemHealth() {
    const bridgeStatus = this.contextBridge.getStatus();
    
    return {
      overall: bridgeStatus.isActive ? 'healthy' : 'offline',
      contextBridge: {
        status: bridgeStatus.isActive ? 'active' : 'inactive',
        providers: bridgeStatus.totalProviders,
        subscribers: bridgeStatus.totalSubscribers
      },
      processing: {
        isRunning: this.isRunning,
        totalAnalyses: this.processingMetrics.totalAnalyses,
        successRate: this.processingMetrics.totalAnalyses > 0 ? 
          this.processingMetrics.successfulAnalyses / this.processingMetrics.totalAnalyses : 0,
        averageLatency: this.processingMetrics.averageProcessingTime
      },
      recommendations: []
    };
  }

  /**
   * Analyze specific context type
   * @private
   */
  async _analyzeSpecificContext(contextType, contextData, options) {
    switch (contextType) {
      case ContextDataType.AUDIO:
        return this._analyzeAudioContext(contextData, options);
      case ContextDataType.VIDEO:
        return this._analyzeVideoContext(contextData, options);
      case ContextDataType.CONVERSATION:
        return this._analyzeConversationContext(contextData, options);
      default:
        return { type: contextType, confidence: contextData.getConfidence() };
    }
  }

  /**
   * Analyze audio context specifically
   * @private
   */
  _analyzeAudioContext(contextData, options) {
    const audioData = contextData.data;
    
    return {
      volume: audioData.volume || 0,
      quality: audioData.quality || 'unknown',
      vadActive: audioData.vadActivity?.isActive || false,
      confidence: contextData.getConfidence(),
      isRecent: contextData.isRecent(),
      analysis: {
        speakingDetected: (audioData.volume || 0) > 0.2,
        qualityGood: audioData.quality === 'good' || audioData.quality === 'excellent',
        voiceActivityConfident: (audioData.vadActivity?.confidence || 0) > 0.7
      }
    };
  }

  /**
   * Analyze video context specifically
   * @private
   */
  _analyzeVideoContext(contextData, options) {
    const videoData = contextData.data;
    
    return {
      faceDetected: videoData.faceDetected || false,
      emotion: videoData.emotion || { primary: 'neutral', confidence: 0 },
      attention: videoData.attention || { eyeContact: 0, engagementScore: 0 },
      confidence: contextData.getConfidence(),
      isRecent: contextData.isRecent(),
      analysis: {
        userPresent: videoData.faceDetected,
        emotionConfident: (videoData.emotion?.confidence || 0) > 0.6,
        attentive: (videoData.attention?.eyeContact || 0) > 0.5
      }
    };
  }

  /**
   * Analyze conversation context specifically
   * @private
   */
  _analyzeConversationContext(contextData, options) {
    const convData = contextData.data;
    
    return {
      transcript: convData.currentTranscript || '',
      confidence: contextData.getConfidence(),
      turnCount: convData.turnCount || 0,
      age: convData.conversationAge || 0,
      isRecent: contextData.isRecent(),
      analysis: {
        hasContent: (convData.currentTranscript?.length || 0) > 0,
        confidenceHigh: (convData.transcriptConfidence || 0) > 0.8,
        activeConversation: (convData.turnCount || 0) > 0
      }
    };
  }

  /**
   * Determine if analysis should be performed
   * @private
   */
  _shouldPerformAnalysis(analysisType, priority) {
    // All analysis types are performed for high and critical priorities
    if (priority === ProcessingPriority.CRITICAL || priority === ProcessingPriority.HIGH) {
      return true;
    }

    // Normal priority - perform most analyses
    if (priority === ProcessingPriority.NORMAL) {
      return analysisType !== AnalysisType.SYSTEM_HEALTH;
    }

    // Low priority - only essential analyses
    if (priority === ProcessingPriority.LOW) {
      return analysisType === AnalysisType.USER_ENGAGEMENT || 
             analysisType === AnalysisType.DECISION_TRIGGERS;
    }

    // Background priority - minimal analyses
    return analysisType === AnalysisType.DECISION_TRIGGERS;
  }

  /**
   * Get relevant historical context
   * @private
   */
  _getRelevantHistory() {
    return this.analysisHistory
      .filter(h => h.status === 'success')
      .slice(-5)
      .map(h => ({
        timestamp: h.timestamp,
        processingTime: h.processingTime,
        analysisTypes: Object.keys(h.analysis?.analyses || {}),
        summary: this._summarizeAnalysis(h.analysis)
      }));
  }

  /**
   * Summarize analysis for history
   * @private
   */
  _summarizeAnalysis(analysis) {
    if (!analysis?.analyses) return 'No analysis data';

    const summaries = [];
    if (analysis.analyses.userEngagement?.level) {
      summaries.push(`Engagement: ${analysis.analyses.userEngagement.level}`);
    }
    if (analysis.analyses.decisionTriggers?.triggers?.length > 0) {
      summaries.push(`Triggers: ${analysis.analyses.decisionTriggers.triggers.length}`);
    }

    return summaries.join(', ') || 'Basic analysis';
  }

  /**
   * Record analysis for metrics
   * @private
   */
  _recordAnalysis(analysis, processingTime, status, error = null) {
    const record = {
      timestamp: Date.now(),
      processingTime,
      status,
      analysis,
      error: error?.message || null
    };

    this.analysisHistory.push(record);

    // Keep history manageable
    if (this.analysisHistory.length > 50) {
      this.analysisHistory.shift();
    }

    // Update metrics
    this.processingMetrics.totalAnalyses++;
    if (status === 'success') {
      this.processingMetrics.successfulAnalyses++;
    }

    this.processingMetrics.averageProcessingTime = 
      (this.processingMetrics.averageProcessingTime * (this.processingMetrics.totalAnalyses - 1) + processingTime) /
      this.processingMetrics.totalAnalyses;

    this.processingMetrics.lastAnalysisTimestamp = Date.now();
  }

  /**
   * Trim analysis cache to size limit
   * @private
   */
  _trimCache() {
    if (this.analysisCache.size > this.options.maxCacheSize) {
      const entries = Array.from(this.analysisCache.entries())
        .sort(([a], [b]) => b - a); // Sort by timestamp descending
      
      this.analysisCache.clear();
      
      // Keep only the most recent entries
      entries.slice(0, this.options.maxCacheSize).forEach(([key, value]) => {
        this.analysisCache.set(key, value);
      });
    }
  }
}

/**
 * Factory function to create ContextProcessor
 */
export function createContextProcessor(options = {}) {
  return new ContextProcessor(options);
}

export default {
  ContextProcessor,
  AnalysisType,
  ProcessingPriority,
  createContextProcessor
};