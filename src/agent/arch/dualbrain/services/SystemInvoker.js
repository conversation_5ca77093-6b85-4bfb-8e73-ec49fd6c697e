/**
 * System Invoker Service
 * 
 * Provides structured invocation of System 1 and System 2 models using
 * existing CommunicationSchemas for validation and error handling.
 * 
 * This service eliminates redundant error handling patterns in DualBrainCoordinator
 * and provides a clean interface for model invocation with proper schema validation.
 */

import { createLogger, LogLevel } from '../../../../utils/logger.ts';
import {
  ContextualEnhancementSchema,
  MultimodalContextSchema,
  CoordinationMessageSchema,
  validateMessage,
  createMessage
} from '../schemas/CommunicationSchemas.js';
import {
  DUAL_BRAIN_CONFIG,
  getSystemConfig,
  getTwoPhaseConfig,
  getRoutingConfig
} from '../../../config/DualBrainConfig.js';
import {
  createLangGraphOptions
} from '../../../config/LangGraphConfig.js';
import { RouteResolver } from '../routing/RouteResolver.js';

const logger = createLogger('SystemInvoker');
logger.setLogLevel(LogLevel.DEBUG);

/**
 * System types supported by the invoker
 */
export const SystemType = {
  SYSTEM1: 'system1',
  SYSTEM2: 'system2'
};

/**
 * Invocation result status
 */
export const InvocationStatus = {
  SUCCESS: 'success',
  PARTIAL: 'partial',
  FAILED: 'failed',
  TIMEOUT: 'timeout',
  VALIDATION_ERROR: 'validation_error'
};

/**
 * SystemInvoker - Structured model invocation with schema validation
 */
export class SystemInvoker {
  constructor(options = {}) {
    // Use consolidated dual brain configuration
    const config = DUAL_BRAIN_CONFIG.coordination.systemInvoker;
    this.options = {
      defaultTimeout: config.dualBrainTimeout,
      retryAttempts: config.retryAttempts,
      retryDelayMs: config.retryDelayMs,
      validateInputs: config.validateInputs,
      validateOutputs: config.validateOutputs,
      ...options
    };

    this.system1Model = null;
    this.system2Model = null;
    this.agentService = null; // ✅ NEW: Agent service for tool calling
    this.invocationHistory = [];
    this.performanceMetrics = {
      totalInvocations: 0,
      successfulInvocations: 0,
      averageLatency: 0,
      errorRate: 0
    };

    logger.info('🧠 SystemInvoker initialized with consolidated config', {
      timeout: this.options.defaultTimeout,
      retryAttempts: this.options.retryAttempts,
      validation: this.options.validateInputs
    });
  }

  /**
   * Initialize with system models
   * @param {Object} systems - System model instances
   * @param {Object} systems.system1 - System 1 model (fast responses)
   * @param {Object} systems.system2 - System 2 model (thinking responses)
   */
  initialize(systems) {
    this.system1Model = systems.system1;
    this.system2Model = systems.system2;

    logger.info('🔗 SystemInvoker connected to models', {
      system1: !!this.system1Model,
      system2: !!this.system2Model
    });
  }

  /**
   * ✅ NEW: Set agent service for tool calling support
   * @param {Object} agentService - LangGraph agent service with tools
   */
  setAgentService(agentService) {
    this.agentService = agentService;
    logger.info('🔧 [INTEGRATION-FIX] Agent service connected to SystemInvoker', {
      hasAgentService: !!agentService,
      toolsAvailable: agentService?.tools?.length || 0,
      toolNames: agentService?.tools?.map(t => t.name) || []
    });
  }

  /**
   * ✅ NEW: Set decision processor for response handling
   * @param {Object} decisionProcessor - DecisionProcessor service
   */
  setDecisionProcessor(decisionProcessor) {
    this.decisionProcessor = decisionProcessor;
    logger.info('🔧 [INTEGRATION-FIX] DecisionProcessor connected to SystemInvoker');
  }

  /**
   * Configure models for dual brain coordination
   * Used by DualBrainCoordinator to set up multi-agent routing
   * @param {Object} models - Model instances
   */
  configureModels(models) {
    if (models.system1) this.system1Model = models.system1;
    if (models.system2) this.system2Model = models.system2;

    logger.debug('🔧 SystemInvoker models configured for dual brain', {
      system1: !!this.system1Model,
      system2: !!this.system2Model
    });
  }

  /**
   * ✅ NEW: Configure agent service for tool calling support
   * @param {Object} agentService - Agent service instance from core.js
   */
  setAgentService(agentService) {
    this.agentService = agentService;
    logger.info('🔗 SystemInvoker configured with agent service for tool calling', {
      hasAgentService: !!this.agentService,
      toolsAvailable: this.agentService?.tools?.length || 0
    });
  }

  /**
   * ✅ NEW: Configure decision processor for handling responses
   * @param {Object} decisionProcessor - DecisionProcessor service instance
   */
  setDecisionProcessor(decisionProcessor) {
    this.decisionProcessor = decisionProcessor;
    logger.info('🔗 SystemInvoker configured with decision processor', {
      hasDecisionProcessor: !!this.decisionProcessor
    });
  }

  /**
   * LangGraph Multi-Agent Supervisor Pattern Integration
   * Routes requests to appropriate system based on complexity and context
   * Implements two-phase processing for audio input: Analysis → Decision → Response
   * @param {string} input - Input text/query
   * @param {Object} context - Request context and routing information
   * @returns {Promise<Object>} Invocation result with routing metadata
   */
  async invokeSupervisor(input, context = {}) {
    const startTime = Date.now();
    const invocationId = this._generateInvocationId();

    try {
      logger.debug('🎯 Supervisor pattern invocation started', {
        id: invocationId,
        inputLength: input?.length || 0,
        hasRouting: !!context.routing,
        targetSystem: context.routing?.targetSystem,
        hasAudioInput: context.routing?.reason === 'audio_input_routing'
      });

      // 🔧 CRITICAL FIX: System 1 runs in background for ALL user interactions (not just audio)
      // Following MODERN_TRIGGER_SYSTEM_ARCHITECTURE pattern: System 1 handles all events
      // ✅ OPTIMIZATION: Use RouteResolver's unified media detection
      if (RouteResolver.isMediaInput(context)) {
        return await this._handleAudioInputTwoPhase(input, context, invocationId, startTime);
      }

      // Determine target system using LangGraph supervisor logic
      const targetSystem = this._determineSupervisorRouting(input, context);

      // Execute appropriate system invocation
      let result;
      if (targetSystem === 'system1') {
        result = await this.invokeSystem1({
          input,
          context: {
            ...context,
            supervisorRouting: true,
            fastResponse: true,
            // 🔧 FIX: Only enable audio output if explicitly requested
            audioOutput: context.routing?.modalityOverride?.includes('audio') || 
                        context.routing?.capabilities?.includes('audioOutput')
          },
          options: {
            ...context.options,
            useRealtime: context.routing?.useRealtime,
            skipMemoryContext: true,
            modalityOverride: context.routing?.modalityOverride // Pass modality control
          }
        });
      } else {
        result = await this.invokeSystem2({
          input,
          context: {
            ...context,
            supervisorRouting: true,
            enableToolCalling: context.routing?.capabilities?.includes('tools'),
            enableSpeaking: context.routing?.capabilities?.includes('speaking')
          },
          options: {
            ...context.options,
            enableThinking: true,
            includeMemoryContext: true // System2 full context
          }
        });
      }

      // Add supervisor routing metadata to result
      const supervisorResult = {
        ...result,
        routing: {
          targetSystem,
          reason: context.routing?.reason || 'Supervisor decision',
          capabilities: context.routing?.capabilities || [],
          latency: Date.now() - startTime
        }
      };

      logger.debug('✅ Supervisor pattern invocation completed', {
        id: invocationId,
        targetSystem,
        latency: Date.now() - startTime,
        success: !!supervisorResult.data
      });

      return supervisorResult;

    } catch (error) {
      const latency = Date.now() - startTime;
      logger.error('❌ Supervisor pattern invocation failed', {
        id: invocationId,
        error: error.message,
        latency
      });

      this._recordInvocation(invocationId, 'supervisor', 'failed', latency, error);
      return this._createFailureResult(invocationId, 'failed', error.message, startTime);
    }
  }

  /**
   * 🔧 NEW: Handle audio input with two-phase processing
   * Phase 1: System 1 generates text description (modalities: ["text"])
   * Phase 2: System 2 makes decision about tool calling/speaking
   * @private
   */
  async _handleAudioInputTwoPhase(input, context, invocationId, startTime) {
    try {
      logger.info('🎤 Starting two-phase audio processing...', {
        invocationId,
        phase: 'audio_analysis'
      });

      // Phase 1: System 1 Audio → Text Analysis (with text-only modalities)
      const textDescription = await this._generateSceneDescription(input, context);
      
      logger.debug('✅ Phase 1 complete: System 1 text analysis', {
        invocationId,
        descriptionLength: textDescription.data?.length || 0,
        phase: 'analysis_complete'
      });

      // Phase 2: System 2 Decision Making (with tool calling enabled)
      const system2Decision = await this._processSceneAnalysisForSpeaking(
        textDescription.data, 
        { ...context, originalInput: input, phase1Result: textDescription }
      );

      logger.info('✅ Two-phase audio processing complete', {
        invocationId,
        phase1: 'text_analysis_complete',
        phase2: 'decision_complete',
        hasToolCalls: !!(system2Decision.data?.tool_calls?.length > 0),
        totalLatency: Date.now() - startTime
      });

      // Return combined result with both phases
      return {
        ...system2Decision,
        routing: {
          targetSystem: 'two_phase_processing',
          reason: 'audio_input_two_phase',
          capabilities: ['audioAnalysis', 'textGeneration', 'toolCalling', 'speaking'],
          latency: Date.now() - startTime,
          phases: {
            phase1: 'system1_text_analysis',
            phase2: 'system2_decision_making'
          }
        },
        phase1Result: textDescription,
        phase2Result: system2Decision
      };

    } catch (error) {
      logger.error('❌ Two-phase audio processing failed', {
        invocationId,
        error: error.message,
        phase: 'two_phase_processing'
      });
      throw error;
    }
  }

  /**
   * 🔧 NEW: Generate scene description using System 1 with text-only output
   * Uses consolidated configuration for prompt template
   * @private
   */
  async _generateSceneDescription(audioInput, context) {
    const phase1Config = getTwoPhaseConfig('phase1');
    
    // Use template from consolidated config
    const analysisPrompt = phase1Config.promptTemplate
      .replace('{audioStatus}', audioInput ? '[Audio stream detected]' : '[No audio detected]')
      .replace('{videoStatus}', context.routing?.capabilities?.includes('videoInput') ? '[Camera active]' : '[No video]');

    return await this.invokeSystem1({
      input: analysisPrompt,
      context: {
        ...context,
        purpose: phase1Config.purpose,
        modalityOverride: phase1Config.modalities,
        skipAudioOutput: phase1Config.skipAudioOutput
      },
      options: {
        useRealtime: true,
        modalityOverride: phase1Config.modalities,
        skipMemoryContext: true,
        timeout: phase1Config.timeout
      }
    });
  }

  /**
   * 🔧 NEW: Process scene analysis through System 2 for speaking decisions
   * Uses consolidated configuration for prompt template
   * @private
   */
  async _processSceneAnalysisForSpeaking(sceneDescription, originalContext) {
    const phase2Config = getTwoPhaseConfig('phase2');
    const availableTools = this.agentService?.tools?.map(t => t.name || 'unnamed').join(', ') || 'none';
    
    // Use template from consolidated config
    const decisionPrompt = phase2Config.promptTemplate
      .replace('{sceneDescription}', sceneDescription)
      .replace('{originalContext}', JSON.stringify(originalContext, null, 2))
      .replace('{availableTools}', availableTools);

    return await this.invokeSystem2({
      input: decisionPrompt,
      context: {
        ...originalContext,
        enableToolCalling: phase2Config.enableToolCalling,
        enableSpeaking: phase2Config.enableSpeaking,
        sceneAnalysis: sceneDescription,
        purpose: phase2Config.purpose
      },
      options: {
        enableThinking: true,
        includeMemoryContext: true,
        enableToolCalling: phase2Config.enableToolCalling,
        timeout: phase2Config.timeout
      }
    });
  }

  /**
   * Determine system routing using LangGraph supervisor logic
   * 🔧 OPTIMIZATION: Now uses RouteResolver to eliminate duplicate logic
   * @private
   */
  _determineSupervisorRouting(input, context) {
    // If routing decision already provided, use it
    if (context.routing?.targetSystem) {
      return context.routing.targetSystem;
    }

    // ✅ OPTIMIZATION: Use unified RouteResolver (eliminates duplicate routing logic)
    const routingResult = RouteResolver.resolveRoute(input, context);
    
    return routingResult.targetSystem;
  }

  /**
   * Invoke System 1 (fast responses) with multimodal context
   * @param {Object} request - Invocation request
   * @param {string} request.input - Input text or data
   * @param {Object} request.context - Multimodal context data
   * @param {Object} request.options - Invocation options
   * @returns {Promise<Object>} Invocation result
   */
  async invokeSystem1(request) {
    const startTime = Date.now();
    const invocationId = this._generateInvocationId();

    try {
      logger.debug('🚀 System1 invocation started', {
        id: invocationId,
        hasContext: !!request.context
      });

      // Validate input using MultimodalContextSchema
      if (this.options.validateInputs && request.context) {
        const validation = this._validateMultimodalContext(request.context);
        if (!validation.valid) {
          return this._createFailureResult(invocationId, 'validation_error',
            `Input validation failed: ${validation.errors.join(', ')}`, startTime);
        }
      }

      // Prepare System 1 invocation
      const system1Request = this._prepareSystem1Request(request, invocationId);

      // Execute invocation with retry logic
      const result = await this._executeWithRetry(
        () => this._invokeSystem1Model(system1Request),
        this.options.retryAttempts
      );

      // Validate output if enabled
      if (this.options.validateOutputs && result.data) {
        const validation = this._validateSystem1Output(result.data);
        if (!validation.valid) {
          logger.warn('⚠️ System1 output validation failed', validation.warnings);
        }
      }

      const latency = Date.now() - startTime;
      this._recordInvocation(invocationId, SystemType.SYSTEM1, 'success', latency);

      return this._createSuccessResult(invocationId, result, latency);

    } catch (error) {
      const latency = Date.now() - startTime;
      logger.error('❌ System1 invocation failed', {
        id: invocationId,
        error: error.message,
        latency
      });

      this._recordInvocation(invocationId, SystemType.SYSTEM1, 'failed', latency, error);
      return this._createFailureResult(invocationId, 'failed', error.message, startTime);
    }
  }

  /**
   * Invoke System 2 (thinking responses) with contextual enhancement
   * @param {Object} request - Invocation request
   * @param {string} request.input - Input text or complex query
   * @param {Object} request.contextualInsights - Contextual insights for enhancement
   * @param {Array} request.toolCalls - Requested tool calls
   * @param {Object} request.options - Invocation options
   * @returns {Promise<Object>} Invocation result
   */
  async invokeSystem2(request) {
    const startTime = Date.now();
    const invocationId = this._generateInvocationId();

    try {
      logger.debug('🧠 System2 invocation started', {
        id: invocationId,
        hasInsights: !!request.contextualInsights,
        toolCallCount: request.toolCalls?.length || 0
      });

      // Validate input using ContextualEnhancementSchema
      if (this.options.validateInputs && request.contextualInsights) {
        const validation = this._validateContextualEnhancement(request);
        if (!validation.valid) {
          return this._createFailureResult(invocationId, 'validation_error',
            `Input validation failed: ${validation.errors.join(', ')}`, startTime);
        }
      }

      // Prepare System 2 invocation
      const system2Request = this._prepareSystem2Request(request, invocationId);

      // 🧠 DUAL BRAIN LOGGING - Context passing from System 1 to System 2
      logger.debug('🧠 [CONTEXT-PASS] System 1 → System 2 context transfer', {
        invocationId,
        originalRequest: {
          hasInput: !!request.input,
          inputLength: request.input ? request.input.length : 0,
          inputPreview: request.input ? request.input.substring(0, 100) + '...' : 'none',
          hasContextualInsights: !!request.contextualInsights,
          contextualInsightsKeys: request.contextualInsights ? Object.keys(request.contextualInsights) : [],
          hasToolCalls: !!(request.toolCalls && request.toolCalls.length > 0),
          toolCallsCount: request.toolCalls ? request.toolCalls.length : 0,
          optionsKeys: request.options ? Object.keys(request.options) : []
        },
        preparedMessages: {
          messageCount: system2Request.length,
          messages: system2Request.map((msg, idx) => ({
            index: idx,
            role: msg.role,
            contentLength: msg.content ? msg.content.length : 0,
            contentPreview: msg.content ? msg.content.substring(0, 150) + '...' : 'empty'
          }))
        }
      });

      // Execute invocation with retry logic
      const result = await this._executeWithRetry(
        () => this._invokeSystem2Model(system2Request, request.context),
        this.options.retryAttempts
      );

      // Validate output if enabled
      if (this.options.validateOutputs && result.data) {
        const validation = this._validateSystem2Output(result.data);
        if (!validation.valid) {
          logger.warn('⚠️ System2 output validation failed', validation.warnings);
        }
      }

      const latency = Date.now() - startTime;
      this._recordInvocation(invocationId, SystemType.SYSTEM2, 'success', latency);

      return this._createSuccessResult(invocationId, result, latency);

    } catch (error) {
      const latency = Date.now() - startTime;
      logger.error('❌ System2 invocation failed', {
        id: invocationId,
        error: error.message,
        latency
      });

      this._recordInvocation(invocationId, SystemType.SYSTEM2, 'failed', latency, error);
      return this._createFailureResult(invocationId, 'failed', error.message, startTime);
    }
  }

  /**
   * Send coordination message between systems
   * @param {Object} message - Coordination message
   * @returns {Promise<Object>} Message result
   */
  async sendCoordinationMessage(message) {
    const startTime = Date.now();
    const messageId = this._generateInvocationId();

    try {
      // Validate coordination message
      const validation = validateMessage(message, CoordinationMessageSchema);
      if (!validation.valid) {
        return this._createFailureResult(messageId, 'validation_error',
          `Message validation failed: ${validation.errors.join(', ')}`, startTime);
      }

      // Process coordination message
      const result = await this._processCoordinationMessage(message);
      const latency = Date.now() - startTime;

      logger.debug('📡 Coordination message processed', {
        id: messageId,
        type: message.messageType,
        latency
      });

      return this._createSuccessResult(messageId, result, latency);

    } catch (error) {
      const latency = Date.now() - startTime;
      logger.error('❌ Coordination message failed', {
        id: messageId,
        error: error.message,
        latency
      });

      return this._createFailureResult(messageId, 'failed', error.message, startTime);
    }
  }

  /**
   * Get performance metrics
   * @returns {Object} Performance metrics
   */
  getMetrics() {
    return {
      ...this.performanceMetrics,
      recentInvocations: this.invocationHistory.slice(-10)
    };
  }

  /**
   * Clear performance history
   */
  clearHistory() {
    this.invocationHistory = [];
    this.performanceMetrics = {
      totalInvocations: 0,
      successfulInvocations: 0,
      averageLatency: 0,
      errorRate: 0
    };
  }

  // Private Methods

  /**
   * Execute System 1 model invocation
   * @private
   */
  async _invokeSystem1Model(request) {
    if (!this.system1Model) {
      throw new Error('System 1 model not initialized');
    }

    // System 1 is typically WebSocket-based for real-time responses
    if (typeof this.system1Model.invoke === 'function') {
      return await this.system1Model.invoke(request);
    } else if (typeof this.system1Model.stream === 'function') {
      // Handle streaming responses
      const chunks = [];
      for await (const chunk of this.system1Model.stream(request)) {
        chunks.push(chunk);
      }
      return { data: chunks.join(''), streaming: true };
    } else {
      throw new Error('System 1 model does not support invoke or stream methods');
    }
  }

  /**
   * Execute System 2 model invocation with enhanced tool calling support
   * @private
   */
  async _invokeSystem2Model(request, context = {}) {
    if (!this.system2Model) {
      throw new Error('System 2 model not initialized');
    }

    // 🔧 CRITICAL FIX: Always use agent service for System 2 when available (enables tool calling)
    if (this.agentService) {
      try {
        // Extract message content from request
        const messageContent = Array.isArray(request) && request.length > 0
          ? request[0].content
          : (typeof request === 'string' ? request : JSON.stringify(request));

        // 🔧 TOOL DEBUGGING: Log agent service tool availability
        logger.debug('🔧 [TOOL-DEBUG] System 2 agent service analysis', {
          hasAgentService: !!this.agentService,
          agentServiceTools: this.agentService.tools?.length || 0,
          agentServiceToolNames: this.agentService.tools?.map(t => t.name) || [],
          system2ModelBoundTools: this.system2Model.boundTools?.length || 0,
          system2ModelBoundToolNames: this.system2Model.boundTools?.map(t => t.name) || []
        });

        // ✅ CRITICAL FIX: Ensure agent service is properly configured with tools
        if (!this.agentService.tools || this.agentService.tools.length === 0) {
          logger.warn('🚨 [TOOL-DEBUG] Agent service has no tools configured!');
          logger.warn('🚨 [TOOL-DEBUG] Expected tool execution will fail - check tool registration');
        }

        // Process through agent service with centralized configuration
        const langGraphOptions = createLangGraphOptions({
          targetSystem: 'system2',
          enableToolCalling: true,
          capabilities: ['tools', 'speaking', 'thinking'],
          context: context || {},
          streaming: false
        });

        // 🔧 ENHANCED LOGGING: Pre-invocation state
        logger.debug('🔧 [TOOL-DEBUG] Pre-invocation state', {
          messageContent: messageContent.substring(0, 100) + '...',
          langGraphOptions: {
            enableToolCalling: langGraphOptions.enableToolCalling,
            capabilities: langGraphOptions.capabilities,
            targetSystem: langGraphOptions.targetSystem
          }
        });

        const result = await this.agentService.generateResponse(messageContent, langGraphOptions);

        // 🔧 ENHANCED TOOL DEBUGGING: Comprehensive response analysis
        const hasToolCalls = !!(result?.tool_calls?.length > 0 || result?.toolCalls?.length > 0);
        const hasDecisionProcessor = !!this.decisionProcessor;
        const expectedToolExecution = true; // We expect tools when using agent service
        const resultStructure = {
          hasToolCalls: hasToolCalls,
          hasToolCallsAlt: !!(result?.toolCalls?.length > 0),
          hasContent: !!result?.content,
          keys: result ? Object.keys(result) : []
        };

        logger.debug('✅ System 2 agent service response received', {
          hasResult: !!result,
          resultType: typeof result,
          resultStructure: resultStructure,
          toolCallCount: result?.tool_calls?.length || result?.toolCalls?.length || 0,
          toolCallDetails: (result?.tool_calls || result?.toolCalls || []).map(tc => ({
            id: tc.id,
            name: tc.name || tc.function?.name,
            hasArgs: !!(tc.args || tc.function?.arguments)
          })),
          hasContent: !!result?.content,
          contentLength: result?.content?.length || 0,
          contentPreview: result?.content ? result.content.substring(0, 200) + '...' : null
        });

        // ✅ CRITICAL DEBUGGING: Check if we got expected tool calls vs. content
        if (!hasToolCalls && result?.content && expectedToolExecution) {
          logger.warn('🚨 [TOOL-DEBUG] No tool calls detected in System 2 response!', {
            hasDecisionProcessor,
            resultStructure,
            expectedToolExecution: true
          });
          logger.warn('🚨 [TOOL-DEBUG] System 2 returned content instead of tool calls:', {
            content: result.content?.substring(0, 300) + '...',
            parsedJSON: (() => {
              try {
                return JSON.parse(result.content);
              } catch (e) {
                return undefined;
              }
            })()
          });
        }

        // ✅ NEW: Enhanced DecisionProcessor integration with structured output handling
        if (this.decisionProcessor) {
          try {
            // 🔧 CRITICAL FIX: Properly detect tool calls in different formats
            const hasToolCalls = !!(result?.tool_calls?.length > 0 || result?.toolCalls?.length > 0);
            const hasContent = !!result?.content;
            const hasResponse = hasToolCalls || hasContent;
            
            if (hasResponse) {
              // Process structured decision result
              await this.decisionProcessor.processDecision(result, { 
                source: 'system2_agent_service',
                hasToolCalls,
                hasContent,
                timestamp: Date.now()
              });
              logger.debug('✅ [SYSTEM-2-INTEGRATION] DecisionProcessor handled structured response', {
                hasToolCalls,
                hasContent,
                toolCallCount: (result?.tool_calls?.length || result?.toolCalls?.length || 0)
              });
            } else {
              logger.debug('ℹ️ [SYSTEM-2-INTEGRATION] No actionable content for DecisionProcessor', {
                resultType: typeof result,
                resultKeys: result ? Object.keys(result) : [],
                hasDecisionProcessor: true
              });
            }
          } catch (processingError) {
            logger.error('❌ [SYSTEM-2-INTEGRATION] DecisionProcessor failed:', processingError.message);
          }
        } else {
          logger.debug('ℹ️ [SYSTEM-2-INTEGRATION] No DecisionProcessor available - structured output not processed');
        }

        // 🔧 CRITICAL FIX: Return properly structured response with enhanced metadata
        return {
          data: result,
          metadata: {
            toolCallsExecuted: result?.tool_calls?.length || result?.toolCalls?.length || 0,
            method: 'agent_service_with_tools',
            hasStructuredOutput: !!(result?.tool_calls || result?.toolCalls || result?.content),
            integrationStatus: 'success',
            timestamp: Date.now(),
            system: 'system2'
          }
        };
      } catch (error) {
        logger.error('❌ [SYSTEM-2-INTEGRATION] Agent service invocation failed:', {
          error: error.message,
          stack: error.stack,
          fallbackToDirectModel: true
        });
        // Fall through to direct model invocation with enhanced error context
      }
    } else {
      logger.warn('🚨 [TOOL-DEBUG] No agent service available for System 2 tool calling!');
    }

    // 🔧 FALLBACK: Direct model invocation should also include tools when available
    logger.debug('🔧 System 2 using direct model invocation with bound tools', {
      modelType: this.system2Model.constructor.name,
      hasBoundTools: !!(this.system2Model.boundTools?.length > 0),
      boundToolsCount: this.system2Model.boundTools?.length || 0
    });

    // Ensure we use the model's invoke method which supports bound tools
    if (typeof this.system2Model.invoke === 'function') {
      // 🔧 CRITICAL: Ensure tools are bound before direct invocation
      if (this.agentService?.tools?.length > 0 && this.system2Model.boundTools?.length === 0) {
        logger.warn('🔧 [TOOL-FIX] System 2 model has no bound tools but agent service has tools - binding now');
        try {
          const boundModel = this.system2Model.bindTools(this.agentService.tools);
          logger.info('✅ Emergency tool binding successful', {
            toolsCount: boundModel.boundTools?.length || 0
          });
          return await boundModel.invoke(request);
        } catch (bindError) {
          logger.error('❌ Emergency tool binding failed:', bindError.message);
        }
      }
      
      // Direct invocation preserves bound tools from core.js
      return await this.system2Model.invoke(request);
    } else if (typeof this.system2Model._generate === 'function') {
      const result = await this.system2Model._generate([request]);
      return { data: result.generations[0][0].text };
    } else {
      throw new Error('System 2 model does not support invoke or _generate methods');
    }
  }

  /**
   * Prepare System 1 request with multimodal context and modality control
   * @private
   */
  _prepareSystem1Request(request, invocationId) {
    // 🔧 CRITICAL: Handle modality override for proper audio/text control
    const modalityOverride = request.options?.modalityOverride || request.context?.modalityOverride;
    
    return {
      input: request.input,
      context: {
        ...request.context,
        // Pass modality control to System 1 model
        modalityOverride: modalityOverride,
        skipAudioOutput: request.context?.skipAudioOutput || modalityOverride?.includes('text') && !modalityOverride?.includes('audio')
      },
      metadata: {
        invocationId,
        system: 'system1',
        timestamp: Date.now(),
        modalityConfiguration: {
          requested: modalityOverride || ['text', 'audio'],
          textOnly: modalityOverride?.length === 1 && modalityOverride[0] === 'text',
          audioEnabled: modalityOverride?.includes('audio') !== false
        },
        ...request.options
      }
    };
  }

  /**
   * Prepare System 2 request with contextual enhancement
   * @private
   */
  _prepareSystem2Request(request, invocationId) {
    // Convert structured request to proper message format for LangChain
    const messages = [];

    // Add main input as user message
    if (request.input) {
      messages.push({
        role: 'user',
        content: request.input
      });
    }

    // Add contextual insights as system context if available
    if (request.contextualInsights) {
      messages.unshift({
        role: 'system',
        content: `Contextual insights: ${JSON.stringify(request.contextualInsights)}`
      });
    }

    return messages;
  }

  /**
   * Execute with retry logic
   * @private
   */
  async _executeWithRetry(operation, maxRetries) {
    let lastError;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        if (attempt < maxRetries) {
          const delay = this.options.retryDelayMs * Math.pow(2, attempt);
          logger.warn(`Retry attempt ${attempt + 1}/${maxRetries + 1} in ${delay}ms`, {
            error: error.message
          });
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError;
  }

  /**
   * Validate multimodal context
   * @private
   */
  _validateMultimodalContext(context) {
    return validateMessage(context, MultimodalContextSchema);
  }

  /**
   * Validate contextual enhancement
   * @private
   */
  _validateContextualEnhancement(request) {
    const enhancementMessage = {
      contextualInsights: request.contextualInsights,
      toolCalls: request.toolCalls || [],
      metadata: { timestamp: Date.now() }
    };
    return validateMessage(enhancementMessage, ContextualEnhancementSchema);
  }

  /**
   * Validate System 1 output
   * @private
   */
  _validateSystem1Output(output) {
    // Basic output validation - can be extended
    return {
      valid: true,
      warnings: []
    };
  }

  /**
   * Validate System 2 output
   * @private
   */
  _validateSystem2Output(output) {
    // Basic output validation - can be extended
    return {
      valid: true,
      warnings: []
    };
  }

  /**
   * Process coordination message
   * @private
   */
  async _processCoordinationMessage(message) {
    // Implementation depends on message type
    switch (message.messageType) {
      case 'sync':
        return { status: 'synchronized', timestamp: Date.now() };
      case 'handoff':
        return { status: 'handoff_complete', timestamp: Date.now() };
      case 'status_update':
        return { status: 'status_received', timestamp: Date.now() };
      default:
        return { status: 'processed', timestamp: Date.now() };
    }
  }

  /**
   * Record invocation for metrics
   * @private
   */
  _recordInvocation(id, system, status, latency, error = null) {
    const record = {
      id,
      system,
      status,
      latency,
      timestamp: Date.now(),
      error: error?.message || null
    };

    this.invocationHistory.push(record);

    // Keep history size manageable
    if (this.invocationHistory.length > 100) {
      this.invocationHistory.shift();
    }

    // Update performance metrics
    this.performanceMetrics.totalInvocations++;
    if (status === 'success') {
      this.performanceMetrics.successfulInvocations++;
    }

    // Update average latency
    const successfulInvocations = this.invocationHistory.filter(r => r.status === 'success');
    if (successfulInvocations.length > 0) {
      this.performanceMetrics.averageLatency =
        successfulInvocations.reduce((sum, r) => sum + r.latency, 0) / successfulInvocations.length;
    }

    // Update error rate
    this.performanceMetrics.errorRate =
      (this.performanceMetrics.totalInvocations - this.performanceMetrics.successfulInvocations) /
      this.performanceMetrics.totalInvocations;
  }

  /**
   * Generate unique invocation ID
   * @private
   */
  _generateInvocationId() {
    return `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Create success result
   * @private
   */
  _createSuccessResult(id, result, latency) {
    return {
      status: InvocationStatus.SUCCESS,
      invocationId: id,
      data: result.data || result,
      latency,
      timestamp: Date.now(),
      metadata: result.metadata || {}
    };
  }

  /**
   * Create failure result
   * @private
   */
  _createFailureResult(id, status, error, startTime) {
    return {
      status,
      invocationId: id,
      error,
      latency: Date.now() - startTime,
      timestamp: Date.now(),
      data: null
    };
  }
}

/**
 * Factory function to create SystemInvoker
 */
export function createSystemInvoker(options = {}) {
  return new SystemInvoker(options);
}

export default {
  SystemInvoker,
  SystemType,
  InvocationStatus,
  createSystemInvoker
};