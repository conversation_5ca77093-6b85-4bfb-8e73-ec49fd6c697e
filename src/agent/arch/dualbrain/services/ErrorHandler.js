/**
 * Error Handler Service
 * 
 * Provides unified error handling, classification, and recovery strategies
 * for the dual brain architecture. Eliminates redundant error handling
 * patterns throughout the codebase and provides consistent error management.
 * 
 * This service handles error classification, logging, recovery attempts,
 * and escalation based on error severity and context.
 */

import { createLogger } from '../../../../utils/logger.ts';

const logger = createLogger('ErrorHandler');

/**
 * Error categories for classification
 */
export const ErrorCategory = {
  SYSTEM: 'system',
  VALIDATION: 'validation',
  NETWORK: 'network',
  MODEL: 'model',
  CONTEXT: 'context',
  DECISION: 'decision',
  TOOL: 'tool',
  CONFIGURATION: 'configuration',
  SECURITY: 'security',
  UNKNOWN: 'unknown'
};

/**
 * Error severity levels
 */
export const ErrorSeverity = {
  CRITICAL: 'critical',
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low',
  INFO: 'info'
};

/**
 * Recovery strategies
 */
export const RecoveryStrategy = {
  RETRY: 'retry',
  FALLBACK: 'fallback',
  GRACEFUL_DEGRADATION: 'graceful_degradation',
  ESCALATE: 'escalate',
  IGNORE: 'ignore',
  RESTART_COMPONENT: 'restart_component',
  EMERGENCY_STOP: 'emergency_stop'
};

/**
 * Error handling result status
 */
export const HandlingStatus = {
  RECOVERED: 'recovered',
  PARTIALLY_RECOVERED: 'partially_recovered',
  ESCALATED: 'escalated',
  FAILED: 'failed',
  IGNORED: 'ignored'
};

/**
 * ErrorHandler - Unified error management system
 */
export class ErrorHandler {
  constructor(options = {}) {
    this.options = {
      enableRetry: true,
      maxRetryAttempts: 3,
      retryDelayMs: 1000,
      enableCircuitBreaker: true,
      circuitBreakerThreshold: 5,
      circuitBreakerTimeoutMs: 60000,
      enableErrorTracking: true,
      maxErrorHistory: 100,
      enableRecoveryStrategies: true,
      ...options
    };

    this.errorHistory = [];
    this.componentHealth = new Map(); // component -> health status
    this.circuitBreakers = new Map(); // component -> circuit breaker state
    this.recoveryHandlers = new Map(); // category -> recovery handler
    this.escalationHandlers = new Map(); // severity -> escalation handler

    this.metrics = {
      totalErrors: 0,
      errorsByCategory: {},
      errorsBySeverity: {},
      recoveryAttempts: 0,
      successfulRecoveries: 0,
      escalatedErrors: 0
    };

    this._initializeDefaultRecoveryHandlers();
    this._initializeDefaultEscalationHandlers();

    logger.info('🛡️ ErrorHandler initialized', {
      retryEnabled: this.options.enableRetry,
      circuitBreakerEnabled: this.options.enableCircuitBreaker,
      errorTracking: this.options.enableErrorTracking
    });
  }

  /**
   * Handle error with automatic classification and recovery
   * @param {Error|string} error - Error to handle
   * @param {Object} context - Error context information
   * @returns {Promise<Object>} Handling result
   */
  async handleError(error, context = {}) {
    const startTime = Date.now();
    const errorId = this._generateErrorId();

    try {
      // Normalize error
      const normalizedError = this._normalizeError(error, context);
      
      // Classify error
      const classification = await this._classifyError(normalizedError, context);

      // Check circuit breaker
      if (this._isCircuitBreakerOpen(context.component)) {
        return this._createHandlingResult(errorId, HandlingStatus.ESCALATED, {
          reason: 'circuit_breaker_open',
          component: context.component
        }, startTime);
      }

      logger.debug('🚨 Handling error', {
        id: errorId,
        category: classification.category,
        severity: classification.severity,
        component: context.component
      });

      // Record error
      this._recordError(errorId, normalizedError, classification, context);

      // Update component health
      this._updateComponentHealth(context.component, classification);

      // Attempt recovery
      const recoveryResult = await this._attemptRecovery(
        normalizedError, classification, context
      );

      // Handle escalation if recovery failed
      if (recoveryResult.status === HandlingStatus.FAILED) {
        const escalationResult = await this._handleEscalation(
          normalizedError, classification, context
        );
        return this._createHandlingResult(errorId, escalationResult.status, {
          recovery: recoveryResult,
          escalation: escalationResult
        }, startTime);
      }

      return this._createHandlingResult(errorId, recoveryResult.status, {
        recovery: recoveryResult
      }, startTime);

    } catch (handlingError) {
      logger.error('❌ Error handling failed', {
        id: errorId,
        originalError: error.message,
        handlingError: handlingError.message
      });

      return this._createHandlingResult(errorId, HandlingStatus.FAILED, {
        originalError: error.message,
        handlingError: handlingError.message
      }, startTime);
    }
  }

  /**
   * Register custom recovery handler
   * @param {string} category - Error category
   * @param {Function} handler - Recovery handler function
   */
  registerRecoveryHandler(category, handler) {
    this.recoveryHandlers.set(category, handler);
    logger.debug(`🔧 Recovery handler registered: ${category}`);
  }

  /**
   * Register custom escalation handler
   * @param {string} severity - Error severity
   * @param {Function} handler - Escalation handler function
   */
  registerEscalationHandler(severity, handler) {
    this.escalationHandlers.set(severity, handler);
    logger.debug(`📈 Escalation handler registered: ${severity}`);
  }

  /**
   * Get error handling metrics and status
   * @returns {Object} Metrics and status
   */
  getStatus() {
    return {
      metrics: this.metrics,
      componentHealth: Object.fromEntries(this.componentHealth),
      circuitBreakers: Object.fromEntries(this.circuitBreakers),
      recentErrors: this.errorHistory.slice(-10),
      registeredHandlers: {
        recovery: Array.from(this.recoveryHandlers.keys()),
        escalation: Array.from(this.escalationHandlers.keys())
      }
    };
  }

  /**
   * Get component health status
   * @param {string} component - Component name
   * @returns {Object} Health status
   */
  getComponentHealth(component) {
    return this.componentHealth.get(component) || {
      status: 'unknown',
      errorCount: 0,
      lastError: null,
      uptime: Date.now()
    };
  }

  /**
   * Reset circuit breaker for component
   * @param {string} component - Component name
   */
  resetCircuitBreaker(component) {
    if (this.circuitBreakers.has(component)) {
      this.circuitBreakers.delete(component);
      logger.info(`🔄 Circuit breaker reset: ${component}`);
    }
  }

  /**
   * Clear error history and reset metrics
   */
  clearHistory() {
    this.errorHistory = [];
    this.componentHealth.clear();
    this.circuitBreakers.clear();
    this.metrics = {
      totalErrors: 0,
      errorsByCategory: {},
      errorsBySeverity: {},
      recoveryAttempts: 0,
      successfulRecoveries: 0,
      escalatedErrors: 0
    };
    logger.info('🧹 ErrorHandler history cleared');
  }

  // Private Methods

  /**
   * Normalize error to standard format
   * @private
   */
  _normalizeError(error, context) {
    if (error instanceof Error) {
      return {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: error.code,
        details: error.details || {},
        timestamp: Date.now(),
        context
      };
    }

    if (typeof error === 'string') {
      return {
        name: 'Error',
        message: error,
        stack: null,
        code: null,
        details: {},
        timestamp: Date.now(),
        context
      };
    }

    if (typeof error === 'object' && error !== null) {
      return {
        name: error.name || 'Error',
        message: error.message || 'Unknown error',
        stack: error.stack || null,
        code: error.code || null,
        details: error.details || error,
        timestamp: Date.now(),
        context
      };
    }

    return {
      name: 'UnknownError',
      message: String(error),
      stack: null,
      code: null,
      details: { originalError: error },
      timestamp: Date.now(),
      context
    };
  }

  /**
   * Classify error by category and severity
   * @private
   */
  async _classifyError(normalizedError, context) {
    let category = ErrorCategory.UNKNOWN;
    let severity = ErrorSeverity.MEDIUM;

    const { name, message, code, context: errorContext } = normalizedError;
    const lowerMessage = message.toLowerCase();

    // Network errors
    if (lowerMessage.includes('network') || 
        lowerMessage.includes('connection') ||
        lowerMessage.includes('timeout') ||
        code === 'ECONNREFUSED' ||
        code === 'ETIMEDOUT') {
      category = ErrorCategory.NETWORK;
      severity = ErrorSeverity.HIGH;
    }

    // Model errors
    else if (lowerMessage.includes('model') || 
             lowerMessage.includes('invoke') ||
             lowerMessage.includes('generation') ||
             errorContext.component?.includes('model')) {
      category = ErrorCategory.MODEL;
      severity = ErrorSeverity.HIGH;
    }

    // Validation errors
    else if (lowerMessage.includes('validation') ||
             lowerMessage.includes('schema') ||
             lowerMessage.includes('invalid') ||
             name === 'ValidationError') {
      category = ErrorCategory.VALIDATION;
      severity = ErrorSeverity.MEDIUM;
    }

    // Context errors
    else if (lowerMessage.includes('context') ||
             lowerMessage.includes('bridge') ||
             lowerMessage.includes('provider') ||
             errorContext.component === 'context') {
      category = ErrorCategory.CONTEXT;
      severity = ErrorSeverity.MEDIUM;
    }

    // Decision errors
    else if (lowerMessage.includes('decision') ||
             lowerMessage.includes('tool') ||
             errorContext.component === 'decision') {
      category = ErrorCategory.DECISION;
      severity = ErrorSeverity.MEDIUM;
    }

    // System errors
    else if (lowerMessage.includes('system') ||
             lowerMessage.includes('memory') ||
             lowerMessage.includes('cpu') ||
             name === 'SystemError') {
      category = ErrorCategory.SYSTEM;
      severity = ErrorSeverity.CRITICAL;
    }

    // Configuration errors
    else if (lowerMessage.includes('config') ||
             lowerMessage.includes('setting') ||
             lowerMessage.includes('option') ||
             name === 'ConfigurationError') {
      category = ErrorCategory.CONFIGURATION;
      severity = ErrorSeverity.LOW;
    }

    // Security errors
    else if (lowerMessage.includes('security') ||
             lowerMessage.includes('auth') ||
             lowerMessage.includes('permission') ||
             name === 'SecurityError') {
      category = ErrorCategory.SECURITY;
      severity = ErrorSeverity.CRITICAL;
    }

    return { category, severity };
  }

  /**
   * Check if circuit breaker is open for component
   * @private
   */
  _isCircuitBreakerOpen(component) {
    if (!this.options.enableCircuitBreaker || !component) {
      return false;
    }

    const breaker = this.circuitBreakers.get(component);
    if (!breaker) {
      return false;
    }

    // Check if timeout has passed
    if (Date.now() - breaker.timestamp > this.options.circuitBreakerTimeoutMs) {
      this.circuitBreakers.delete(component);
      return false;
    }

    return breaker.isOpen;
  }

  /**
   * Update circuit breaker state
   * @private
   */
  _updateCircuitBreaker(component, classification) {
    if (!this.options.enableCircuitBreaker || !component) {
      return;
    }

    let breaker = this.circuitBreakers.get(component) || {
      errorCount: 0,
      isOpen: false,
      timestamp: Date.now()
    };

    if (classification.severity === ErrorSeverity.CRITICAL ||
        classification.severity === ErrorSeverity.HIGH) {
      breaker.errorCount++;
      breaker.timestamp = Date.now();

      if (breaker.errorCount >= this.options.circuitBreakerThreshold) {
        breaker.isOpen = true;
        logger.warn(`⚡ Circuit breaker opened: ${component}`, {
          errorCount: breaker.errorCount,
          threshold: this.options.circuitBreakerThreshold
        });
      }

      this.circuitBreakers.set(component, breaker);
    }
  }

  /**
   * Record error in history and update metrics
   * @private
   */
  _recordError(errorId, normalizedError, classification, context) {
    const errorRecord = {
      id: errorId,
      error: normalizedError,
      classification,
      context,
      timestamp: Date.now()
    };

    if (this.options.enableErrorTracking) {
      this.errorHistory.push(errorRecord);

      // Keep history size manageable
      if (this.errorHistory.length > this.options.maxErrorHistory) {
        this.errorHistory.shift();
      }
    }

    // Update metrics
    this.metrics.totalErrors++;

    if (!this.metrics.errorsByCategory[classification.category]) {
      this.metrics.errorsByCategory[classification.category] = 0;
    }
    this.metrics.errorsByCategory[classification.category]++;

    if (!this.metrics.errorsBySeverity[classification.severity]) {
      this.metrics.errorsBySeverity[classification.severity] = 0;
    }
    this.metrics.errorsBySeverity[classification.severity]++;
  }

  /**
   * Update component health status
   * @private
   */
  _updateComponentHealth(component, classification) {
    if (!component) return;

    let health = this.componentHealth.get(component) || {
      status: 'healthy',
      errorCount: 0,
      lastError: null,
      uptime: Date.now()
    };

    health.errorCount++;
    health.lastError = Date.now();

    // Update status based on error severity and frequency
    if (classification.severity === ErrorSeverity.CRITICAL) {
      health.status = 'critical';
    } else if (classification.severity === ErrorSeverity.HIGH && health.errorCount > 3) {
      health.status = 'degraded';
    } else if (health.errorCount > 10) {
      health.status = 'unstable';
    }

    this.componentHealth.set(component, health);

    // Update circuit breaker
    this._updateCircuitBreaker(component, classification);
  }

  /**
   * Attempt error recovery
   * @private
   */
  async _attemptRecovery(normalizedError, classification, context) {
    if (!this.options.enableRecoveryStrategies) {
      return { status: HandlingStatus.FAILED, reason: 'recovery_disabled' };
    }

    this.metrics.recoveryAttempts++;

    // Get recovery handler for this error category
    const recoveryHandler = this.recoveryHandlers.get(classification.category);
    if (!recoveryHandler) {
      return { status: HandlingStatus.FAILED, reason: 'no_recovery_handler' };
    }

    try {
      logger.debug(`🔄 Attempting recovery: ${classification.category}`);

      const recoveryResult = await recoveryHandler(normalizedError, context);

      if (recoveryResult.success) {
        this.metrics.successfulRecoveries++;
        return { status: HandlingStatus.RECOVERED, result: recoveryResult };
      } else {
        return { status: HandlingStatus.FAILED, reason: recoveryResult.reason };
      }

    } catch (recoveryError) {
      logger.error('Recovery attempt failed', {
        category: classification.category,
        error: recoveryError.message
      });

      return { 
        status: HandlingStatus.FAILED, 
        reason: 'recovery_exception',
        error: recoveryError.message
      };
    }
  }

  /**
   * Handle error escalation
   * @private
   */
  async _handleEscalation(normalizedError, classification, context) {
    this.metrics.escalatedErrors++;

    const escalationHandler = this.escalationHandlers.get(classification.severity);
    if (!escalationHandler) {
      logger.warn(`No escalation handler for severity: ${classification.severity}`);
      return { status: HandlingStatus.ESCALATED, reason: 'no_escalation_handler' };
    }

    try {
      logger.warn(`📈 Escalating error: ${classification.severity}`, {
        category: classification.category,
        message: normalizedError.message
      });

      const escalationResult = await escalationHandler(normalizedError, context);
      return { status: HandlingStatus.ESCALATED, result: escalationResult };

    } catch (escalationError) {
      logger.error('Escalation failed', {
        severity: classification.severity,
        error: escalationError.message
      });

      return {
        status: HandlingStatus.FAILED,
        reason: 'escalation_exception',
        error: escalationError.message
      };
    }
  }

  /**
   * Initialize default recovery handlers
   * @private
   */
  _initializeDefaultRecoveryHandlers() {
    // Network error recovery
    this.recoveryHandlers.set(ErrorCategory.NETWORK, async (error, context) => {
      if (this.options.enableRetry) {
        return await this._retryWithBackoff(
          context.operation,
          this.options.maxRetryAttempts,
          this.options.retryDelayMs
        );
      }
      return { success: false, reason: 'retry_disabled' };
    });

    // Model error recovery
    this.recoveryHandlers.set(ErrorCategory.MODEL, async (error, context) => {
      // Try fallback model or graceful degradation
      return { success: false, reason: 'no_fallback_model' };
    });

    // Validation error recovery
    this.recoveryHandlers.set(ErrorCategory.VALIDATION, async (error, context) => {
      // Validation errors typically can't be automatically recovered
      return { success: false, reason: 'validation_error_cannot_recover' };
    });

    // Context error recovery
    this.recoveryHandlers.set(ErrorCategory.CONTEXT, async (error, context) => {
      // Try to reinitialize context providers
      return { success: false, reason: 'context_recovery_not_implemented' };
    });

    // Configuration error recovery
    this.recoveryHandlers.set(ErrorCategory.CONFIGURATION, async (error, context) => {
      // Try default configuration
      return { success: false, reason: 'config_recovery_not_implemented' };
    });
  }

  /**
   * Initialize default escalation handlers
   * @private
   */
  _initializeDefaultEscalationHandlers() {
    this.escalationHandlers.set(ErrorSeverity.CRITICAL, async (error, context) => {
      logger.error('🚨 CRITICAL ERROR', {
        message: error.message,
        component: context.component,
        stack: error.stack
      });
      return { escalated: true, level: 'critical' };
    });

    this.escalationHandlers.set(ErrorSeverity.HIGH, async (error, context) => {
      logger.error('⚠️ HIGH SEVERITY ERROR', {
        message: error.message,
        component: context.component
      });
      return { escalated: true, level: 'high' };
    });

    this.escalationHandlers.set(ErrorSeverity.MEDIUM, async (error, context) => {
      logger.warn('⚠️ Medium severity error', {
        message: error.message,
        component: context.component
      });
      return { escalated: true, level: 'medium' };
    });
  }

  /**
   * Retry with exponential backoff
   * @private
   */
  async _retryWithBackoff(operation, maxAttempts, baseDelayMs) {
    if (!operation) {
      return { success: false, reason: 'no_operation_to_retry' };
    }

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const result = await operation();
        return { success: true, result, attempts: attempt };
      } catch (error) {
        if (attempt === maxAttempts) {
          return { 
            success: false, 
            reason: 'max_attempts_exceeded',
            attempts: attempt,
            lastError: error.message
          };
        }

        const delay = baseDelayMs * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * Generate unique error ID
   * @private
   */
  _generateErrorId() {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Create handling result
   * @private
   */
  _createHandlingResult(errorId, status, data, startTime) {
    return {
      errorId,
      status,
      data,
      handlingTime: Date.now() - startTime,
      timestamp: Date.now()
    };
  }
}

/**
 * Factory function to create ErrorHandler
 */
export function createErrorHandler(options = {}) {
  return new ErrorHandler(options);
}

export default {
  ErrorHandler,
  ErrorCategory,
  ErrorSeverity,
  RecoveryStrategy,
  HandlingStatus,
  createErrorHandler
};