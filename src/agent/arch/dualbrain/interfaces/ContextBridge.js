/**
 * Context Bridge Interface
 * 
 * Provides a clean abstraction layer between dual brain coordination
 * and various data sources (MediaPipe, Media modules, etc.)
 * 
 * This decouples the dual brain architecture from specific implementations,
 * allowing it to work with any data provider that implements this contract.
 */

import { createLogger } from '@/utils/logger.js';

const logger = createLogger('ContextBridge');

/**
 * Context data types that can be processed
 */
export const ContextDataType = {
  AUDIO: 'audio',
  VIDEO: 'video',
  POSE: 'pose',
  HANDS: 'hands',
  FACE: 'face',
  ENVIRONMENTAL: 'environmental',
  CONVERSATION: 'conversation',
  MULTIMODAL: 'multimodal'
};

/**
 * Standard context data structure
 */
export class ContextData {
  constructor(type, data, metadata = {}) {
    this.type = type;
    this.data = data;
    this.metadata = {
      timestamp: Date.now(),
      source: 'unknown',
      confidence: 1.0,
      ...metadata
    };
  }

  /**
   * Get normalized confidence score (0-1)
   */
  getConfidence() {
    return Math.max(0, Math.min(1, this.metadata.confidence || 0));
  }

  /**
   * Check if data is recent (within threshold)
   */
  isRecent(thresholdMs = 5000) {
    return (Date.now() - this.metadata.timestamp) < thresholdMs;
  }

  /**
   * Convert to simplified object for analysis
   */
  toAnalysisFormat() {
    return {
      type: this.type,
      data: this.data,
      confidence: this.getConfidence(),
      age: Date.now() - this.metadata.timestamp,
      source: this.metadata.source
    };
  }
}

/**
 * Context Provider Interface
 * Any module that wants to provide context data should implement this interface
 */
export class ContextProvider {
  constructor(name, options = {}) {
    this.name = name;
    this.options = options;
    this.listeners = new Map();
    this.isActive = false;
  }

  /**
   * Initialize the context provider
   * @returns {Promise<boolean>} Success status
   */
  async initialize() {
    // Override in subclasses
    this.isActive = true;
    return true;
  }

  /**
   * Start providing context data
   * @returns {Promise<boolean>} Success status
   */
  async start() {
    // Override in subclasses
    return this.isActive;
  }

  /**
   * Stop providing context data
   * @returns {Promise<boolean>} Success status
   */
  async stop() {
    // Override in subclasses
    return true;
  }

  /**
   * Get current context data
   * @param {string} type - Type of context to get
   * @returns {Promise<ContextData|null>} Context data or null
   */
  async getContext(type) {
    // Override in subclasses
    return null;
  }

  /**
   * Subscribe to context updates
   * @param {string} type - Context type to listen for
   * @param {Function} callback - Callback function (contextData) => void
   */
  subscribe(type, callback) {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, new Set());
    }
    this.listeners.get(type).add(callback);
  }

  /**
   * Unsubscribe from context updates
   * @param {string} type - Context type
   * @param {Function} callback - Callback to remove
   */
  unsubscribe(type, callback) {
    if (this.listeners.has(type)) {
      this.listeners.get(type).delete(callback);
    }
  }

  /**
   * Emit context data to subscribers
   * @param {ContextData} contextData - Context data to emit
   * @protected
   */
  _emit(contextData) {
    const listeners = this.listeners.get(contextData.type);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(contextData);
        } catch (error) {
          logger.warn(`Error in context listener for ${contextData.type}:`, error);
        }
      });
    }
  }

  /**
   * Create standardized context data
   * @param {string} type - Context type
   * @param {*} data - Raw data
   * @param {Object} metadata - Additional metadata
   * @returns {ContextData} Standardized context data
   * @protected
   */
  _createContext(type, data, metadata = {}) {
    return new ContextData(type, data, {
      source: this.name,
      ...metadata
    });
  }
}

/**
 * Context Bridge - Central coordinator for all context providers
 */
export class ContextBridge {
  constructor(options = {}) {
    this.options = {
      bufferSize: 100,
      maxAge: 30000, // 30 seconds
      ...options
    };

    this.providers = new Map();
    this.contextBuffer = new Map(); // type -> ContextData[]
    this.subscribers = new Map(); // type -> Set<callback>
    this.isActive = false;

    logger.info('🌉 ContextBridge initialized', {
      bufferSize: this.options.bufferSize,
      maxAge: this.options.maxAge
    });
  }

  /**
   * Register a context provider
   * @param {ContextProvider} provider - Context provider instance
   */
  registerProvider(provider) {
    if (!(provider instanceof ContextProvider)) {
      throw new Error('Provider must extend ContextProvider class');
    }

    this.providers.set(provider.name, provider);

    // Subscribe to all context types from this provider
    Object.values(ContextDataType).forEach(type => {
      provider.subscribe(type, (contextData) => {
        this._handleContextUpdate(contextData);
      });
    });

    logger.info(`📡 Context provider registered: ${provider.name}`);
  }

  /**
   * Unregister a context provider
   * @param {string} name - Provider name
   */
  unregisterProvider(name) {
    const provider = this.providers.get(name);
    if (provider) {
      provider.stop();
      this.providers.delete(name);
      logger.info(`📡 Context provider unregistered: ${name}`);
    }
  }

  /**
   * Start all context providers
   */
  async start() {
    const startPromises = Array.from(this.providers.values()).map(provider =>
      provider.start().catch(error => {
        logger.warn(`Failed to start provider ${provider.name}:`, error);
        return false;
      })
    );

    await Promise.all(startPromises);
    this.isActive = true;
    logger.info('🚀 ContextBridge started with all providers');
  }

  /**
   * Stop all context providers
   */
  async stop() {
    const stopPromises = Array.from(this.providers.values()).map(provider =>
      provider.stop().catch(error => {
        logger.warn(`Failed to stop provider ${provider.name}:`, error);
        return false;
      })
    );

    await Promise.all(stopPromises);
    this.isActive = false;
    logger.info('🛑 ContextBridge stopped');
  }

  /**
   * Get current context data of a specific type
   * @param {string} type - Context type
   * @returns {ContextData|null} Most recent context data or null
   */
  getCurrentContext(type) {
    const buffer = this.contextBuffer.get(type);
    if (!buffer || buffer.length === 0) {
      return null;
    }

    // Return most recent context that's still valid
    const now = Date.now();
    const recentContext = buffer.find(ctx =>
      (now - ctx.metadata.timestamp) < this.options.maxAge
    );

    return recentContext || null;
  }

  /**
   * Get aggregated context for multiple types
   * @param {string[]} types - Array of context types
   * @returns {Object} Object with type -> ContextData mappings
   */
  getAggregatedContext(types = Object.values(ContextDataType)) {
    const context = {};
    types.forEach(type => {
      const currentContext = this.getCurrentContext(type);
      if (currentContext) {
        context[type] = currentContext;
      }
    });
    return context;
  }

  /**
   * Subscribe to context updates
   * @param {string} type - Context type
   * @param {Function} callback - Callback function
   */
  subscribe(type, callback) {
    if (!this.subscribers.has(type)) {
      this.subscribers.set(type, new Set());
    }
    this.subscribers.get(type).add(callback);
  }

  /**
   * Unsubscribe from context updates
   * @param {string} type - Context type  
   * @param {Function} callback - Callback to remove
   */
  unsubscribe(type, callback) {
    if (this.subscribers.has(type)) {
      this.subscribers.get(type).delete(callback);
    }
  }

  /**
   * Handle context update from providers
   * @param {ContextData} contextData - New context data
   * @private
   */
  _handleContextUpdate(contextData) {
    // Add to buffer
    if (!this.contextBuffer.has(contextData.type)) {
      this.contextBuffer.set(contextData.type, []);
    }

    const buffer = this.contextBuffer.get(contextData.type);
    buffer.unshift(contextData); // Add to front

    // Trim buffer to size
    if (buffer.length > this.options.bufferSize) {
      buffer.splice(this.options.bufferSize);
    }

    // Clean old entries
    this._cleanBuffer(contextData.type);

    // Notify subscribers
    this._emitToSubscribers(contextData);
  }

  /**
   * Clean old entries from buffer
   * @param {string} type - Context type
   * @private
   */
  _cleanBuffer(type) {
    const buffer = this.contextBuffer.get(type);
    if (!buffer) return;

    const now = Date.now();
    const validEntries = buffer.filter(ctx =>
      (now - ctx.metadata.timestamp) < this.options.maxAge
    );

    this.contextBuffer.set(type, validEntries);
  }

  /**
   * Emit context data to subscribers
   * @param {ContextData} contextData - Context data to emit
   * @private
   */
  _emitToSubscribers(contextData) {
    const subscribers = this.subscribers.get(contextData.type);
    if (subscribers) {
      subscribers.forEach(callback => {
        try {
          callback(contextData);
        } catch (error) {
          logger.warn(`Error in context subscriber for ${contextData.type}:`, error);
        }
      });
    }
  }

  /**
   * Get bridge status and statistics
   * @returns {Object} Status information
   */
  getStatus() {
    const providerStatus = {};
    this.providers.forEach((provider, name) => {
      providerStatus[name] = {
        isActive: provider.isActive,
        name: provider.name
      };
    });

    const bufferStatus = {};
    this.contextBuffer.forEach((buffer, type) => {
      bufferStatus[type] = {
        count: buffer.length,
        lastUpdate: buffer.length > 0 ? buffer[0].metadata.timestamp : null
      };
    });

    return {
      isActive: this.isActive,
      providers: providerStatus,
      buffers: bufferStatus,
      totalProviders: this.providers.size,
      totalSubscribers: Array.from(this.subscribers.values()).reduce(
        (sum, set) => sum + set.size, 0
      )
    };
  }
}

/**
 * Factory function to create context bridge with common providers
 */
export function createContextBridge(options = {}) {
  return new ContextBridge(options);
}

export default {
  ContextBridge,
  ContextProvider,
  ContextData,
  ContextDataType,
  createContextBridge
};