/**
 * Communication Schemas for Dual-Brain System
 * 
 * Structured schemas that define the interface between System 1 (Fast Brain) 
 * and System 2 (Thinking Brain) for optimal performance and compatibility
 */

/**
 * Base Communication Schema
 * Core structure for all dual-brain messages
 */
export const BaseCommunicationSchema = {
    id: {
        type: 'string',
        required: true,
        description: 'Unique message identifier'
    },
    type: {
        type: 'string',
        required: true,
        enum: ['contextual_enhancement', 'multimodal_context', 'coordination', 'system_status'],
        description: 'Message type identifier'
    },
    version: {
        type: 'string',
        required: true,
        default: '2.0',
        description: 'Schema version for compatibility'
    },
    timestamp: {
        type: 'number',
        required: true,
        description: 'Unix timestamp when message was created'
    },
    priority: {
        type: 'string',
        required: false,
        enum: ['low', 'normal', 'high', 'critical'],
        default: 'normal',
        description: 'Message processing priority'
    },
    metadata: {
        type: 'object',
        required: false,
        description: 'Additional metadata for message processing'
    }
};

/**
 * System 2 -> System 1: Contextual Enhancement Schema
 * Enhanced context and decision guidance for System 1
 */
export const ContextualEnhancementSchema = {
    ...BaseCommunicationSchema,
    type: { ...BaseCommunicationSchema.type, default: 'contextual_enhancement' },
    
    // Core contextual insights
    contextualInsights: {
        type: 'object',
        required: true,
        properties: {
            userProfile: {
                type: 'object',
                properties: {
                    communicationStyle: {
                        type: 'string',
                        enum: ['direct', 'conversational', 'technical', 'casual', 'formal'],
                        default: 'conversational',
                        description: 'Preferred communication approach'
                    },
                    attentionSpan: {
                        type: 'number',
                        minimum: 0,
                        maximum: 300000, // 5 minutes max
                        default: 120000, // 2 minutes
                        description: 'Estimated attention span in milliseconds'
                    },
                    preferredResponseTime: {
                        type: 'string',
                        enum: ['immediate', 'normal', 'thoughtful', 'detailed'],
                        default: 'normal',
                        description: 'User preference for response timing'
                    },
                    currentMood: {
                        type: 'string',
                        enum: ['engaged', 'distracted', 'focused', 'frustrated', 'neutral', 'enthusiastic'],
                        default: 'neutral',
                        description: 'Inferred current user mood'
                    },
                    interactionHistory: {
                        type: 'array',
                        maxItems: 10,
                        items: {
                            type: 'object',
                            properties: {
                                timestamp: { type: 'number' },
                                action: { type: 'string' },
                                outcome: { type: 'string' },
                                satisfaction: { type: 'number', minimum: 0, maximum: 1 }
                            }
                        },
                        description: 'Recent interaction patterns'
                    }
                }
            },
            environmentalContext: {
                type: 'object',
                properties: {
                    backgroundNoise: {
                        type: 'string',
                        enum: ['silent', 'low', 'medium', 'high', 'very_high'],
                        default: 'low',
                        description: 'Background audio noise level'
                    },
                    interruptionRisk: {
                        type: 'string',
                        enum: ['very_low', 'low', 'medium', 'high', 'very_high'],
                        default: 'low',
                        description: 'Risk of user interruption'
                    },
                    optimalSpeakingMoment: {
                        type: 'boolean',
                        default: false,
                        description: 'Whether current moment is optimal for speaking'
                    },
                    currentActivity: {
                        type: 'string',
                        enum: ['listening', 'thinking', 'typing', 'multitasking', 'focused', 'idle'],
                        default: 'listening',
                        description: 'Inferred current user activity'
                    },
                    contextualFactors: {
                        type: 'array',
                        items: { type: 'string' },
                        description: 'Additional environmental factors affecting interaction'
                    }
                }
            },
            conversationalFlow: {
                type: 'object',
                properties: {
                    topicContinuity: {
                        type: 'number',
                        minimum: 0,
                        maximum: 1,
                        default: 0.5,
                        description: 'Coherence score of current topic flow'
                    },
                    responsePattern: {
                        type: 'string',
                        enum: ['immediate', 'quick', 'delayed', 'varied', 'inconsistent'],
                        default: 'normal',
                        description: 'User response timing pattern'
                    },
                    engagementTrend: {
                        type: 'string',
                        enum: ['increasing', 'stable', 'declining', 'fluctuating'],
                        default: 'stable',  
                        description: 'User engagement trend over time'
                    },
                    lastInteractionAge: {
                        type: 'number',
                        minimum: 0,
                        description: 'Milliseconds since last user interaction'
                    },
                    conversationDepth: {
                        type: 'number',
                        minimum: 0,
                        maximum: 10,
                        default: 1,
                        description: 'Current conversation complexity level'
                    }
                }
            }
        }
    },
    
    // Tool calls - speaking is now generalized as one of many possible tool calls
    toolCalls: {
        type: 'array',
        required: true,
        minItems: 0,
        maxItems: 10,
        items: {
            type: 'object',
            required: ['type', 'name'],
            properties: {
                type: {
                    type: 'string',
                    enum: ['speak', 'animation', 'audio', 'visual', 'system', 'memory'],
                    description: 'Tool category type'
                },
                name: {
                    type: 'string',
                    description: 'Specific tool function name'
                },
                parameters: {
                    type: 'object',
                    description: 'Tool-specific parameters'
                },
                confidence: {
                    type: 'number',
                    minimum: 0,
                    maximum: 1,
                    default: 0.8,
                    description: 'Confidence score for this tool call'
                },
                priority: {
                    type: 'string',
                    enum: ['very_low', 'low', 'medium', 'high', 'critical'],
                    default: 'medium',
                    description: 'Priority level for tool execution'
                },
                timing: {
                    type: 'object',
                    properties: {
                        delay: {
                            type: 'number',
                            minimum: 0,
                            maximum: 10000,
                            default: 0,
                            description: 'Delay before execution (milliseconds)'
                        },
                        strategy: {
                            type: 'string',
                            enum: ['immediate', 'delayed', 'when_optimal', 'after_pause'],
                            default: 'immediate',
                            description: 'Timing strategy for execution'
                        }
                    }
                },
                reason: {
                    type: 'string',
                    required: true,
                    description: 'Human-readable reason for this tool call'
                },
                fallbackStrategy: {
                    type: 'string',
                    enum: ['skip', 'retry', 'alternative', 'graceful_degradation'],
                    default: 'graceful_degradation',
                    description: 'Strategy if tool call fails'
                }
            }
        },
        description: 'Array of tool calls to execute (speaking, animations, etc.)'
    },
    
    // Memory and context state
    memoryContext: {
        type: 'object',
        required: false,
        properties: {
            sessionMemory: {
                type: 'object',
                maxProperties: 20,
                description: 'Current session context data'
            },
            longTermPatterns: {
                type: 'object',
                maxProperties: 10,
                description: 'Long-term user behavior patterns'
            },
            contextualTriggers: {
                type: 'array',
                maxItems: 10,
                items: { type: 'string' },
                description: 'Active contextual triggers affecting decisions'
            },
            relevantHistory: {
                type: 'array',
                maxItems: 5,
                items: {
                    type: 'object',
                    properties: {
                        content: { type: 'string' },
                        timestamp: { type: 'number' },
                        relevanceScore: { type: 'number', minimum: 0, maximum: 1 }
                    }
                },
                description: 'Contextually relevant historical interactions'
            }
        }
    },
    
    // System 1 operational instructions
    system1Instructions: {
        type: 'object',
        required: true,
        properties: {
            audioProcessingHints: {
                type: 'object',
                properties: {
                    expectedInputType: {
                        type: 'string',
                        enum: ['question', 'command', 'conversation', 'unclear', 'silence'],
                        default: 'conversation',
                        description: 'Expected type of user input'
                    },
                    sensitivityLevel: {
                        type: 'string',
                        enum: ['very_low', 'low', 'normal', 'high', 'very_high'],
                        default: 'normal',
                        description: 'Audio processing sensitivity level'
                    },
                    vadThresholds: {
                        type: 'object',
                        properties: {
                            speechStartThreshold: { type: 'number', minimum: 0, maximum: 1, default: 0.5 },
                            speechEndThreshold: { type: 'number', minimum: 0, maximum: 1, default: 0.3 },
                            silenceDuration: { type: 'number', minimum: 0, maximum: 5000, default: 1500 }
                        },
                        description: 'Voice Activity Detection threshold adjustments'
                    }
                }
            },
            responseGeneration: {
                type: 'object',
                properties: {
                    preferredStyle: {
                        type: 'string',
                        enum: ['conversational', 'informative', 'supportive', 'professional', 'casual', 'technical'],
                        default: 'conversational',
                        description: 'Preferred response generation style'
                    },
                    maxResponseLength: {
                        type: 'number',
                        minimum: 50,
                        maximum: 1000,
                        default: 250,
                        description: 'Maximum recommended response length in characters'
                    },
                    emotionalTone: {
                        type: 'string',
                        enum: ['neutral', 'encouraging', 'professional', 'friendly', 'enthusiastic', 'calm', 'urgent'],
                        default: 'neutral',
                        description: 'Recommended emotional tone for response'
                    },
                    includePersonalization: {
                        type: 'boolean',
                        default: false,
                        description: 'Whether to include user-specific personalization'
                    },
                    responseStructure: {
                        type: 'string',
                        enum: ['direct', 'contextual', 'explanatory', 'question_based'],
                        default: 'contextual',
                        description: 'Recommended response structure approach'
                    }
                }
            }
        }
    }
};

/**
 * System 1 -> System 2: Multimodal Context Schema
 * Real-time context and processing state from System 1
 */
export const MultimodalContextSchema = {
    ...BaseCommunicationSchema,
    type: { ...BaseCommunicationSchema.type, default: 'multimodal_context' },
    
    // Audio analysis context
    audioContext: {
        type: 'object',
        required: true,
        properties: {
            volume: {
                type: 'number',
                minimum: 0,
                maximum: 1,
                default: 0,
                description: 'Current audio volume level (0-1)'
            },
            quality: {
                type: 'string',
                enum: ['silent', 'poor', 'fair', 'good', 'excellent', 'distorted'],
                default: 'silent',
                description: 'Audio quality assessment'
            },
            sentiment: {
                type: 'string',
                enum: ['very_negative', 'negative', 'neutral', 'positive', 'very_positive', 'mixed'],
                default: 'neutral',
                description: 'Detected sentiment in audio'
            },
            speechRate: {
                type: 'number',
                minimum: 0,
                maximum: 400, // words per minute
                default: 0,
                description: 'Detected speech rate in words per minute'
            },
            pauseDuration: {
                type: 'number',
                minimum: 0,
                description: 'Current pause/silence duration in milliseconds'
            },
            vadActivity: {
                type: 'object',
                properties: {
                    isActive: {
                        type: 'boolean',
                        default: false,
                        description: 'Whether voice activity is currently detected'
                    },
                    confidence: {
                        type: 'number',
                        minimum: 0,
                        maximum: 1,
                        default: 0,
                        description: 'Voice activity detection confidence'
                    },
                    duration: {
                        type: 'number',
                        minimum: 0,
                        default: 0,
                        description: 'Duration of current voice activity in milliseconds'
                    },
                    pattern: {
                        type: 'string',
                        enum: ['continuous', 'intermittent', 'choppy', 'smooth'],
                        description: 'Voice activity pattern classification'
                    }
                }
            },
            acousticFeatures: {
                type: 'object',
                properties: {
                    pitch: { type: 'number', description: 'Average pitch level' },
                    energy: { type: 'number', description: 'Audio energy level' },
                    spectralCentroid: { type: 'number', description: 'Spectral centroid for voice quality' }
                }
            }
        }
    },
    
    // Visual context (if available)
    visualContext: {
        type: 'object',
        required: false,
        properties: {
            faceDetected: {
                type: 'boolean',
                default: false,
                description: 'Whether a face is detected in video'
            },
            emotion: {
                type: 'object',
                properties: {
                    primary: {
                        type: 'string',
                        enum: ['joy', 'sadness', 'anger', 'fear', 'surprise', 'disgust', 'neutral', 'contempt'],
                        default: 'neutral',
                        description: 'Primary detected emotion'
                    },
                    confidence: {
                        type: 'number',
                        minimum: 0,
                        maximum: 1,
                        default: 0,
                        description: 'Emotion detection confidence'
                    },
                    secondary: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                emotion: { type: 'string' },
                                confidence: { type: 'number', minimum: 0, maximum: 1 }
                            }
                        },
                        description: 'Secondary emotions detected'
                    }
                }
            },
            attention: {
                type: 'object',
                properties: {
                    eyeContact: {
                        type: 'number',
                        minimum: 0,
                        maximum: 1,
                        default: 0.5,
                        description: 'Eye contact level (0=away, 1=direct)'
                    },
                    focusDirection: {
                        type: 'string',
                        enum: ['screen', 'away', 'distracted', 'focused', 'looking_down', 'looking_up'],
                        default: 'screen',
                        description: 'Direction of user attention'
                    },
                    engagementScore: {
                        type: 'number',
                        minimum: 0,
                        maximum: 1,
                        default: 0.5,
                        description: 'Overall visual engagement score'
                    },
                    blinkRate: {
                        type: 'number',
                        minimum: 0,
                        description: 'Blink rate per minute (indicator of focus/fatigue)'
                    }
                }
            },
            bodyLanguage: {
                type: 'object',
                properties: {
                    posture: {
                        type: 'string',
                        enum: ['upright', 'leaning_forward', 'leaning_back', 'slouched', 'tense', 'relaxed'],
                        default: 'upright',
                        description: 'Body posture classification'
                    },
                    gestureActivity: {
                        type: 'number',
                        minimum: 0,
                        maximum: 1,
                        default: 0,
                        description: 'Level of gesture activity (0=still, 1=very active)'
                    },
                    movementLevel: {
                        type: 'number',
                        minimum: 0,
                        maximum: 1,
                        default: 0,
                        description: 'Overall movement activity level'
                    },
                    handPositions: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                hand: { type: 'string', enum: ['left', 'right'] },
                                position: { type: 'string' },
                                gesture: { type: 'string' }
                            }
                        },
                        description: 'Hand positions and gestures detected'
                    }
                }
            }
        }
    },
    
    // Conversation state
    conversationState: {
        type: 'object',
        required: true,
        properties: {
            currentTranscript: {
                type: 'string',
                maxLength: 1000,
                default: '',
                description: 'Latest speech-to-text transcription'
            },
            transcriptConfidence: {
                type: 'number',
                minimum: 0,
                maximum: 1,
                default: 0,
                description: 'Confidence in current transcription'
            },
            conversationAge: {
                type: 'number',
                minimum: 0,
                default: 0,
                description: 'Duration of current conversation in milliseconds'
            },
            turnCount: {
                type: 'number',
                minimum: 0,
                default: 0,
                description: 'Number of conversation turns so far'
            },
            lastUserMessage: {
                type: 'object',
                properties: {
                    content: { type: 'string', maxLength: 500 },
                    timestamp: { type: 'number' },
                    confidence: { type: 'number', minimum: 0, maximum: 1 },
                    intent: { type: 'string' }
                },
                description: 'Last completed user message'
            },
            contextualCues: {
                type: 'array',
                items: { type: 'string' },
                description: 'Contextual cues extracted from conversation'
            }
        }
    },
    
    // System 1 processing state
    systemState: {
        type: 'object',
        required: true,
        properties: {
            processingLoad: {
                type: 'number',
                minimum: 0,
                maximum: 1,
                default: 0,
                description: 'Current system processing load (0=idle, 1=maxed)'
            },
            connectionQuality: {
                type: 'string',
                enum: ['poor', 'fair', 'good', 'excellent'],
                default: 'good',
                description: 'Network/connection quality status'
            },
            latency: {
                type: 'number',
                minimum: 0,
                default: 0,
                description: 'Current processing latency in milliseconds'
            },
            errorRate: {
                type: 'number',
                minimum: 0,
                maximum: 1,
                default: 0,
                description: 'Recent error frequency (0=no errors, 1=frequent errors)'
            },
            availableModalities: {
                type: 'array',
                items: {
                    type: 'string',
                    enum: ['audio', 'video', 'text', 'touch', 'gesture']
                },
                default: ['audio', 'text'],
                description: 'Currently available input modalities'
            },
            resourceUsage: {
                type: 'object',
                properties: {
                    cpu: { type: 'number', minimum: 0, maximum: 1 },
                    memory: { type: 'number', minimum: 0, maximum: 1 },
                    bandwidth: { type: 'number', minimum: 0, maximum: 1 }
                },
                description: 'System resource utilization'
            }
        }
    },
    
    // Timing and coordination
    temporalContext: {
        type: 'object',
        required: true,
        properties: {
            timestamp: {
                type: 'number',
                required: true,
                description: 'When this context was captured (Unix timestamp)'
            },
            contextAge: {
                type: 'number',
                minimum: 0,
                default: 0,
                description: 'Age of context data in milliseconds'
            },
            projectedRelevance: {
                type: 'number',
                minimum: 0,
                maximum: 300000, // 5 minutes
                default: 30000,
                description: 'Estimated relevance duration in milliseconds'
            },
            syncToken: {
                type: 'string',
                required: true,
                pattern: '^sync_[0-9]+_[a-z0-9]+$',
                description: 'Synchronization token for message coordination'
            },
            sequenceNumber: {
                type: 'number',
                minimum: 0,
                description: 'Message sequence number for ordering'
            }
        }
    }
};

/**
 * Coordination Message Schema
 * For system handoffs, synchronization, and status updates
 */
export const CoordinationMessageSchema = {
    ...BaseCommunicationSchema,
    type: { ...BaseCommunicationSchema.type, default: 'coordination' },
    
    messageType: {
        type: 'string',
        enum: ['sync', 'handoff', 'status_update', 'emergency', 'heartbeat', 'acknowledgment'],
        required: true,
        description: 'Specific coordination message type'
    },
    sender: {
        type: 'string',
        enum: ['system1', 'system2', 'coordinator', 'external'],
        required: true,
        description: 'Message sender identification'
    },
    recipient: {
        type: 'string',
        enum: ['system1', 'system2', 'coordinator', 'broadcast'],
        required: true,
        description: 'Message recipient identification'
    },
    
    coordinationData: {
        type: 'object',
        required: true,
        properties: {
            systemState: {
                type: 'string',
                enum: ['initializing', 'ready', 'busy', 'error', 'maintenance', 'shutdown'],
                default: 'ready',
                description: 'Current system operational state'
            },
            requestedAction: {
                type: 'string',
                maxLength: 100,
                description: 'Specific action being requested'
            },
            expectedResponse: {
                type: 'string',
                maxLength: 100,
                description: 'Expected response or acknowledgment'
            },
            timeout: {
                type: 'number',
                minimum: 100,
                maximum: 30000,
                default: 5000,
                description: 'Timeout for response in milliseconds'
            },
            retryPolicy: {
                type: 'object',
                properties: {
                    maxRetries: { type: 'number', minimum: 0, maximum: 5, default: 3 },
                    backoffMs: { type: 'number', minimum: 100, maximum: 5000, default: 1000 },
                    strategy: { type: 'string', enum: ['linear', 'exponential', 'fixed'], default: 'exponential' }
                },
                description: 'Retry policy for failed operations'
            },
            payload: {
                type: 'object',
                description: 'Additional coordination data'
            }
        }
    },
    
    statusData: {
        type: 'object',
        required: false,
        properties: {
            operationalStatus: {
                type: 'string',
                enum: ['optimal', 'degraded', 'critical', 'offline'],
                description: 'Overall operational status'
            },
            performanceMetrics: {
                type: 'object',
                properties: {
                    responseTime: { type: 'number', minimum: 0 },
                    throughput: { type: 'number', minimum: 0 },
                    errorRate: { type: 'number', minimum: 0, maximum: 1 },
                    uptime: { type: 'number', minimum: 0 }
                },
                description: 'Performance metrics data'
            },
            lastActivity: {
                type: 'number',
                description: 'Timestamp of last system activity'
            }
        }
    }
};

/**
 * System Status Schema
 * For health checks and system monitoring
 */
export const SystemStatusSchema = {
    ...BaseCommunicationSchema,
    type: { ...BaseCommunicationSchema.type, default: 'system_status' },
    
    systemId: {
        type: 'string',
        enum: ['system1', 'system2', 'coordinator'],
        required: true,
        description: 'System reporting status'
    },
    
    healthStatus: {
        type: 'object',
        required: true,
        properties: {
            overall: {
                type: 'string',
                enum: ['healthy', 'warning', 'critical', 'unknown'],
                required: true,
                description: 'Overall system health'
            },
            components: {
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        name: { type: 'string' },
                        status: { type: 'string', enum: ['healthy', 'warning', 'critical', 'offline'] },
                        message: { type: 'string', maxLength: 200 },
                        lastCheck: { type: 'number' }
                    }
                },
                description: 'Individual component health status'
            }
        }
    },
    
    performanceMetrics: {
        type: 'object',
        properties: {
            cpu: { type: 'number', minimum: 0, maximum: 1 },
            memory: { type: 'number', minimum: 0, maximum: 1 },
            latency: { type: 'number', minimum: 0 },
            throughput: { type: 'number', minimum: 0 },
            activeConnections: { type: 'number', minimum: 0 }
        }
    },
    
    capabilities: {
        type: 'object',
        properties: {
            supportedModalities: {
                type: 'array',
                items: { type: 'string' }
            },
            maxConcurrentRequests: { type: 'number', minimum: 1 },
            supportedFormats: {
                type: 'array',
                items: { type: 'string' }
            }
        }
    }
};

/**
 * Schema Validation Utilities
 */

/**
 * Validate message against schema
 * @param {Object} message - Message to validate
 * @param {Object} schema - Schema to validate against
 * @returns {Object} Validation result with success flag and errors
 */
export function validateMessage(message, schema) {
    const errors = [];
    const warnings = [];
    
    // Basic validation implementation
    for (const [key, rules] of Object.entries(schema)) {
        if (rules.required && !message.hasOwnProperty(key)) {
            errors.push(`Required field '${key}' is missing`);
            continue;
        }
        
        if (message.hasOwnProperty(key)) {
            const value = message[key];
            
            // Type validation
            if (rules.type && typeof value !== rules.type) {
                errors.push(`Field '${key}' must be of type ${rules.type}, got ${typeof value}`);
                continue;
            }
            
            // Enum validation
            if (rules.enum && !rules.enum.includes(value)) {
                errors.push(`Field '${key}' must be one of: ${rules.enum.join(', ')}`);
                continue;
            }
            
            // String length validation
            if (rules.type === 'string') {
                if (rules.maxLength && value.length > rules.maxLength) {
                    errors.push(`Field '${key}' exceeds maximum length of ${rules.maxLength}`);
                }
                if (rules.minLength && value.length < rules.minLength) {
                    errors.push(`Field '${key}' is shorter than minimum length of ${rules.minLength}`);
                }
                if (rules.pattern && !new RegExp(rules.pattern).test(value)) {
                    errors.push(`Field '${key}' does not match required pattern`);
                }
            }
            
            // Number range validation
            if (rules.type === 'number') {
                if (rules.minimum !== undefined && value < rules.minimum) {
                    errors.push(`Field '${key}' must be >= ${rules.minimum}`);
                }
                if (rules.maximum !== undefined && value > rules.maximum) {
                    errors.push(`Field '${key}' must be <= ${rules.maximum}`);
                }
            }
            
            // Array validation
            if (rules.type === 'array') {
                if (rules.maxItems && value.length > rules.maxItems) {
                    warnings.push(`Field '${key}' has ${value.length} items, maximum recommended is ${rules.maxItems}`);
                }
                if (rules.minItems && value.length < rules.minItems) {
                    errors.push(`Field '${key}' must have at least ${rules.minItems} items`);
                }
            }
            
            // Object validation
            if (rules.type === 'object' && rules.properties) {
                const nestedResult = validateMessage(value, rules.properties);
                errors.push(...nestedResult.errors.map(err => `${key}.${err}`));
                warnings.push(...nestedResult.warnings.map(warn => `${key}.${warn}`));
            }
        }
    }
    
    return {
        valid: errors.length === 0,
        errors,
        warnings
    };
}

/**
 * Create message with default values
 * @param {string} messageType - Type of message to create
 * @param {Object} data - Message data
 * @returns {Object} Message with defaults applied
 */
export function createMessage(messageType, data = {}) {
    const schemas = {
        'contextual_enhancement': ContextualEnhancementSchema,
        'multimodal_context': MultimodalContextSchema,
        'coordination': CoordinationMessageSchema,
        'system_status': SystemStatusSchema
    };
    
    const schema = schemas[messageType];
    if (!schema) {
        throw new Error(`Unknown message type: ${messageType}`);
    }
    
    const message = {
        id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: messageType,
        version: '2.0',
        timestamp: Date.now(),
        priority: 'normal',
        ...data
    };
    
    // Apply default values from schema
    applyDefaults(message, schema);
    
    return message;
}

/**
 * Apply default values from schema to message
 * @private
 */
function applyDefaults(obj, schema) {
    for (const [key, rules] of Object.entries(schema)) {
        if (rules.default !== undefined && !obj.hasOwnProperty(key)) {
            obj[key] = rules.default;
        }
        
        if (rules.type === 'object' && rules.properties && obj[key]) {
            applyDefaults(obj[key], rules.properties);
        }
    }
}

/**
 * Get schema by message type
 * @param {string} messageType - Message type
 * @returns {Object} Schema object
 */
export function getSchema(messageType) {
    const schemas = {
        'contextual_enhancement': ContextualEnhancementSchema,
        'multimodal_context': MultimodalContextSchema,
        'coordination': CoordinationMessageSchema,
        'system_status': SystemStatusSchema
    };
    
    return schemas[messageType] || null;
}

export default {
    BaseCommunicationSchema,
    ContextualEnhancementSchema,
    MultimodalContextSchema,
    CoordinationMessageSchema,
    SystemStatusSchema,
    validateMessage,
    createMessage,
    getSchema
};