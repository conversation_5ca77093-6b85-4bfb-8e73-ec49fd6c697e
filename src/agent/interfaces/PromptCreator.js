/**
 * Universal Prompt Interface
 * 
 * Provides a standardized interface for creating system prompts across different
 * applications and use cases while maintaining provider agnosticism
 */

import { createLogger, LogLevel } from '@/utils/logger';

const logger = createLogger('UniversalPromptInterface', LogLevel.DEBUG);

/**
 * Universal Prompt Creator Interface
 * Defines the contract for creating system prompts in any application context
 */
export class UniversalPromptCreator {
    constructor(config = {}) {
        this.config = {
            // Application context
            applicationName: config.applicationName || 'AI Assistant',
            assistantName: config.assistantName || 'Javis',
            assistantRole: config.assistantRole || 'helpful AI assistant',
            
            // Prompt configuration
            includeToolInfo: config.includeToolInfo !== false,
            includeMemoryInfo: config.includeMemoryInfo !== false,
            includeCapabilities: config.includeCapabilities !== false,
            
            // Provider configuration
            provider: config.provider || 'universal',
            modelCapabilities: config.modelCapabilities || {},
            
            // Behavioral settings
            communicationStyle: config.communicationStyle || 'professional',
            responseLength: config.responseLength || 'concise',
            languageSupport: config.languageSupport || ['english'],
            
            // Custom prompt sections
            customInstructions: config.customInstructions || '',
            domainSpecificKnowledge: config.domainSpecificKnowledge || '',
            conversationContext: config.conversationContext || '',
            
            ...config
        };
    }

    /**
     * Create universal system prompt
     */
    async createSystemPrompt(options = {}) {
        const mergedOptions = { ...this.config, ...options };
        
        logger.debug('Creating universal system prompt:', {
            provider: mergedOptions.provider,
            applicationName: mergedOptions.applicationName,
            includeToolInfo: mergedOptions.includeToolInfo,
            hasCustomInstructions: !!mergedOptions.customInstructions
        });

        const sections = [
            this._createIdentitySection(mergedOptions),
            this._createCapabilitiesSection(mergedOptions),
            this._createToolSection(mergedOptions),
            this._createMemorySection(mergedOptions),
            this._createBehaviorSection(mergedOptions),
            this._createCustomSection(mergedOptions)
        ].filter(Boolean);

        const systemPrompt = sections.join('\n\n');
        
        logger.debug('Universal system prompt created:', {
            length: systemPrompt.length,
            sections: sections.length,
            preview: systemPrompt.substring(0, 100) + '...'
        });

        return systemPrompt;
    }

    /**
     * Create identity section of the prompt
     * @private
     */
    _createIdentitySection(options) {
        const { assistantName, assistantRole, applicationName } = options;
        
        return `You are ${assistantName}, a ${assistantRole} designed for ${applicationName}. ` +
               `You provide helpful, accurate, and contextually appropriate responses.`;
    }

    /**
     * Create capabilities section
     * @private
     */
    _createCapabilitiesSection(options) {
        if (!options.includeCapabilities) return null;

        const capabilities = [
            'Understand and respond to complex queries',
            'Provide detailed explanations and analysis',
            'Engage in natural, contextual conversations'
        ];

        // Add provider-specific capabilities
        if (options.modelCapabilities?.multimodal) {
            capabilities.push('Process and understand multiple types of media');
        }

        if (options.modelCapabilities?.realtime) {
            capabilities.push('Engage in real-time conversations');
        }

        if (options.modelCapabilities?.voiceInteraction) {
            capabilities.push('Participate in voice-based interactions');
        }

        return `**Core Capabilities:**\n${capabilities.map(cap => `- ${cap}`).join('\n')}`;
    }

    /**
     * Create tool information section
     * @private
     */
    _createToolSection(options) {
        if (!options.includeToolInfo) return null;

        return `**Tool Usage:**\n` +
               `You have access to various tools to enhance conversations. ` +
               `Use tools contextually based on user needs and conversation flow. ` +
               `Choose when to speak versus when to work silently based on the task complexity and user expectations.`;
    }

    /**
     * Create memory information section
     * @private
     */
    _createMemorySection(options) {
        if (!options.includeMemoryInfo) return null;

        return `**Memory & Context:**\n` +
               `You maintain conversation history and can remember previous interactions. ` +
               `Use this context to provide personalized and coherent responses across sessions.`;
    }

    /**
     * Create behavior guidelines section
     * @private
     */
    _createBehaviorSection(options) {
        const { communicationStyle, responseLength, languageSupport } = options;
        
        const guidelines = [
            `Communication style: ${communicationStyle}`,
            `Response length: ${responseLength}`,
            `Supported languages: ${languageSupport.join(', ')}`
        ];

        // Add provider-specific behavior guidelines
        if (options.provider === 'aliyun' && options.modelCapabilities?.realtime) {
            guidelines.push('For voice interactions, maintain natural conversation rhythm with sub-600ms response times');
        }

        if (options.modelCapabilities?.autonomous) {
            guidelines.push('Make autonomous decisions about when to speak versus think silently based on context');
        }

        return `**Behavior Guidelines:**\n${guidelines.map(g => `- ${g}`).join('\n')}`;
    }

    /**
     * Create custom instructions section
     * @private
     */
    _createCustomSection(options) {
        const customSections = [];

        if (options.customInstructions) {
            customSections.push(`**Custom Instructions:**\n${options.customInstructions}`);
        }

        if (options.domainSpecificKnowledge) {
            customSections.push(`**Domain Knowledge:**\n${options.domainSpecificKnowledge}`);
        }

        if (options.conversationContext) {
            customSections.push(`**Context:**\n${options.conversationContext}`);
        }

        return customSections.length > 0 ? customSections.join('\n\n') : null;
    }

    /**
     * Update configuration
     */
    updateConfig(updates) {
        this.config = { ...this.config, ...updates };
    }

    /**
     * Get current configuration
     */
    getConfig() {
        return { ...this.config };
    }
}

/**
 * Application-specific prompt creators
 */
export const PromptCreatorFactories = {
    /**
     * Avatar/Speaking assistant prompt creator
     */
    avatar: (config = {}) => new UniversalPromptCreator({
        applicationName: 'Avatar Interaction System',
        assistantName: config.assistantName || 'Javis',
        assistantRole: 'intelligent conversational avatar',
        includeToolInfo: true,
        includeMemoryInfo: true,
        includeCapabilities: true,
        modelCapabilities: {
            multimodal: true,
            realtime: true,
            voiceInteraction: true,
            autonomous: true
        },
        customInstructions: `You are an interactive avatar that can speak, show animations, and engage users in natural conversations. 
Use tools strategically to create engaging interactions while maintaining natural conversation flow.`,
        ...config
    }),

    /**
     * Text-only assistant prompt creator
     */
    textAssistant: (config = {}) => new UniversalPromptCreator({
        applicationName: 'Text Assistant',
        assistantName: config.assistantName || 'Assistant',
        assistantRole: 'knowledgeable text-based assistant',
        includeToolInfo: true,
        includeMemoryInfo: true,
        communicationStyle: 'professional',
        responseLength: 'detailed',
        ...config
    }),

    /**
     * Code assistant prompt creator
     */
    codeAssistant: (config = {}) => new UniversalPromptCreator({
        applicationName: 'Code Assistant',
        assistantName: config.assistantName || 'CodeHelper',
        assistantRole: 'expert programming assistant',
        includeToolInfo: true,
        includeCapabilities: true,
        domainSpecificKnowledge: `Expertise in software development, code analysis, debugging, and best practices across multiple programming languages.`,
        communicationStyle: 'technical',
        responseLength: 'precise',
        ...config
    }),

    /**
     * Generic assistant prompt creator
     */
    generic: (config = {}) => new UniversalPromptCreator({
        applicationName: 'AI Assistant',
        includeToolInfo: config.includeToolInfo !== false,
        includeMemoryInfo: config.includeMemoryInfo !== false,
        ...config
    })
};

/**
 * Create prompt creator based on application type
 */
export function createPromptCreator(applicationType = 'generic', config = {}) {
    const factory = PromptCreatorFactories[applicationType];
    
    if (!factory) {
        logger.warn(`Unknown application type: ${applicationType}, using generic`);
        return PromptCreatorFactories.generic(config);
    }

    return factory(config);
}

/**
 * Create prompt creation function for dependency injection
 */
export function createPromptFunction(applicationType = 'generic', config = {}) {
    const promptCreator = createPromptCreator(applicationType, config);
    
    return async (options = {}) => {
        return await promptCreator.createSystemPrompt(options);
    };
}

export default UniversalPromptCreator;