/**
 * Universal Realtime Interface
 * 
 * Provides provider-agnostic realtime mode functionality for audio/video interactions
 * Works with any LLM provider that supports realtime capabilities
 */

import { createLogger, LogLevel } from '@/utils/logger';

const logger = createLogger('UniversalRealtimeInterface');

/**
 * Universal Realtime States
 */
export const UniversalRealtimeStates = {
    DISCONNECTED: 'disconnected',
    CONNECTING: 'connecting',
    CONNECTED: 'connected',
    AUTHENTICATED: 'authenticated',
    SESSION_READY: 'session_ready',
    STREAMING: 'streaming',
    ERROR: 'error',
    CLOSING: 'closing'
};

/**
 * Universal Realtime Events
 */
export const UniversalRealtimeEvents = {
    CONNECTION_STATE_CHANGE: 'connection_state_change',
    VOICE_ACTIVITY_DETECTED: 'voice_activity_detected',
    VOICE_ACTIVITY_STOPPED: 'voice_activity_stopped',
    AUDIO_RECEIVED: 'audio_received',
    SESSION_READY: 'session_ready',
    ERROR: 'error',
    DISCONNECTED: 'disconnected'
};

/**
 * Universal Realtime Interface
 * Provides standardized methods for realtime interactions across providers
 */
export class UniversalRealtimeInterface {
    constructor(providerConfig, model) {
        this.providerConfig = providerConfig;
        this.model = model;
        this.provider = providerConfig.config.provider;
        this.logger = createLogger(`UniversalRealtime-${this.provider}`, LogLevel.DEBUG);
        
        // Realtime state
        this.state = UniversalRealtimeStates.DISCONNECTED;
        this.eventHandlers = new Map();
        
        // Configuration
        this.realtimeConfig = providerConfig.getRealtimeConfig();
        this.connectionConfig = providerConfig.getConnectionConfig();
        
        this.logger.debug('Universal realtime interface created:', {
            provider: this.provider,
            realtimeEnabled: this.realtimeConfig.enabled,
            vadEnabled: this.realtimeConfig.vadEnabled
        });
    }

    /**
     * Check if realtime mode is supported by the provider
     */
    isSupported() {
        return this.realtimeConfig.enabled && this.model && 
               typeof this.model.initializeRealtimeMode === 'function';
    }

    /**
     * Check if realtime mode is currently active
     */
    isActive() {
        return this.state === UniversalRealtimeStates.SESSION_READY || 
               this.state === UniversalRealtimeStates.STREAMING;
    }

    /**
     * Check if realtime session is ready for audio streaming
     */
    isSessionReady() {
        // Provider-specific check
        if (this.model?.realtimeClient?.isSessionReady) {
            return this.model.realtimeClient.isSessionReady();
        }
        
        // Generic check
        return this.state === UniversalRealtimeStates.SESSION_READY ||
               this.state === UniversalRealtimeStates.STREAMING;
    }

    /**
     * Initialize realtime mode
     */
    async initialize(options = {}) {
        if (!this.isSupported()) {
            this.logger.warn('Realtime mode not supported by provider or model');
            return false;
        }

        try {
            this.logger.info(`Initializing realtime mode for provider: ${this.provider}`);
            this._setState(UniversalRealtimeStates.CONNECTING);

            // Prepare universal initialization options
            const initOptions = this._buildInitializationOptions(options);

            // Initialize realtime mode on the model
            const success = await this.model.initializeRealtimeMode(initOptions);

            if (success) {
                // Wait for session to be ready if requested
                if (options.waitForSession !== false) {
                    const sessionReady = await this.waitForSessionReady({
                        timeoutMs: options.sessionTimeoutMs || 10000,
                        checkIntervalMs: options.checkIntervalMs || 500,
                        onProgress: options.onProgress
                    });

                    if (sessionReady) {
                        this._setState(UniversalRealtimeStates.SESSION_READY);
                    } else {
                        this._setState(UniversalRealtimeStates.ERROR);
                        return false;
                    }
                }

                this.logger.info('Realtime mode initialized successfully');
                return true;
            } else {
                this._setState(UniversalRealtimeStates.ERROR);
                return false;
            }

        } catch (error) {
            this.logger.error('Failed to initialize realtime mode:', error);
            this._setState(UniversalRealtimeStates.ERROR);
            return false;
        }
    }

    /**
     * Build initialization options for the specific provider
     * @private
     */
    _buildInitializationOptions(options) {
        const baseOptions = {
            // Audio configuration
            sampleRate: this.realtimeConfig.sampleRate,
            format: this.realtimeConfig.format,
            channels: this.realtimeConfig.channels,
            
            // VAD configuration
            vadEnabled: this.realtimeConfig.vadEnabled,
            
            // Connection configuration
            endpoint: this.connectionConfig.websocketEndpoint || this.connectionConfig.endpoint,
            timeout: this.connectionConfig.timeout,
            
            // Event handlers
            onVoiceActivityDetected: (event) => this._handleUniversalEvent(UniversalRealtimeEvents.VOICE_ACTIVITY_DETECTED, event),
            onVoiceActivityStopped: (event) => this._handleUniversalEvent(UniversalRealtimeEvents.VOICE_ACTIVITY_STOPPED, event),
            onAudioReceived: (event) => this._handleUniversalEvent(UniversalRealtimeEvents.AUDIO_RECEIVED, event),
            onSessionReady: (event) => this._handleUniversalEvent(UniversalRealtimeEvents.SESSION_READY, event),
            onError: (event) => this._handleUniversalEvent(UniversalRealtimeEvents.ERROR, event),
            onDisconnected: (event) => this._handleUniversalEvent(UniversalRealtimeEvents.DISCONNECTED, event),
            
            // Provider-specific options
            ...this.providerConfig.getProviderSpecificConfig(),
            ...options
        };

        return baseOptions;
    }

    /**
     * Send audio data to realtime API
     */
    async sendAudio(audioData, format = 'arraybuffer') {
        if (!this.isSessionReady()) {
            this.logger.warn('Cannot send audio: realtime session not ready');
            return false;
        }

        try {
            // Use centralized audio utility if available
            if (typeof sendRealtimeAudio === 'function') {
                return await sendRealtimeAudio(this.model, audioData, format, this.logger);
            }

            // Fallback to model-specific method
            if (this.model.sendRealtimeAudio) {
                return await this.model.sendRealtimeAudio(audioData, format);
            }

            this.logger.warn('No method available to send realtime audio');
            return false;

        } catch (error) {
            this.logger.error('Failed to send realtime audio:', error);
            return false;
        }
    }

    /**
     * Wait for realtime session to be ready
     */
    async waitForSessionReady(options = {}) {
        const { timeoutMs = 10000, checkIntervalMs = 500, onProgress } = options;
        const startTime = Date.now();

        return new Promise((resolve) => {
            const checkSession = () => {
                const elapsed = Date.now() - startTime;
                
                if (this.isSessionReady()) {
                    this.logger.debug('Realtime session ready');
                    resolve(true);
                    return;
                }

                if (elapsed >= timeoutMs) {
                    this.logger.warn('Timeout waiting for realtime session ready');
                    resolve(false);
                    return;
                }

                // Progress callback
                if (onProgress) {
                    onProgress(elapsed, timeoutMs);
                }

                // Continue checking
                setTimeout(checkSession, checkIntervalMs);
            };

            checkSession();
        });
    }

    /**
     * Stop realtime streaming
     */
    async stop() {
        try {
            this.logger.debug('Stopping realtime streaming...');
            this._setState(UniversalRealtimeStates.CLOSING);

            // Stop model realtime streaming
            if (this.model?.stopRealtimeStreaming) {
                await this.model.stopRealtimeStreaming();
            } else if (this.model?.stopStreaming) {
                await this.model.stopStreaming();
            }

            this._setState(UniversalRealtimeStates.DISCONNECTED);
            this.logger.info('Realtime streaming stopped');
            return true;

        } catch (error) {
            this.logger.error('Error stopping realtime streaming:', error);
            this._setState(UniversalRealtimeStates.ERROR);
            return false;
        }
    }

    /**
     * Register event handler
     */
    on(event, handler) {
        if (!this.eventHandlers.has(event)) {
            this.eventHandlers.set(event, []);
        }
        this.eventHandlers.get(event).push(handler);
    }

    /**
     * Unregister event handler
     */
    off(event, handler) {
        if (this.eventHandlers.has(event)) {
            const handlers = this.eventHandlers.get(event);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    /**
     * Handle universal realtime events
     * @private
     */
    _handleUniversalEvent(eventType, eventData) {
        this.logger.debug(`Universal realtime event: ${eventType}`, eventData);

        // Update state based on event
        switch (eventType) {
            case UniversalRealtimeEvents.SESSION_READY:
                this._setState(UniversalRealtimeStates.SESSION_READY);
                break;
            case UniversalRealtimeEvents.ERROR:
                this._setState(UniversalRealtimeStates.ERROR);
                break;
            case UniversalRealtimeEvents.DISCONNECTED:
                this._setState(UniversalRealtimeStates.DISCONNECTED);
                break;
        }

        // Emit to registered handlers
        if (this.eventHandlers.has(eventType)) {
            const handlers = this.eventHandlers.get(eventType);
            handlers.forEach(handler => {
                try {
                    handler(eventData);
                } catch (error) {
                    this.logger.error(`Error in event handler for ${eventType}:`, error);
                }
            });
        }

        // Emit state change event (but not for CONNECTION_STATE_CHANGE to prevent recursion)
        if (eventType !== UniversalRealtimeEvents.CONNECTION_STATE_CHANGE) {
            this._emitStateChange();
        }
    }

    /**
     * Set realtime state
     * @private
     */
    _setState(newState) {
        if (this.state !== newState) {
            const oldState = this.state;
            this.state = newState;
            this.logger.debug(`Realtime state change: ${oldState} -> ${newState}`);
            this._emitStateChange();
        }
    }

    /**
     * Emit state change event
     * @private
     */
    _emitStateChange() {
        // Directly emit to registered handlers without calling _handleUniversalEvent
        // to prevent infinite recursion
        const eventType = UniversalRealtimeEvents.CONNECTION_STATE_CHANGE;
        const eventData = {
            state: this.state,
            timestamp: Date.now()
        };

        this.logger.debug(`State change event: ${eventType}`, eventData);

        // Emit to registered handlers only
        if (this.eventHandlers.has(eventType)) {
            const handlers = this.eventHandlers.get(eventType);
            handlers.forEach(handler => {
                try {
                    handler(eventData);
                } catch (error) {
                    this.logger.error(`Error in state change handler:`, error);
                }
            });
        }
    }

    /**
     * Get current state
     */
    getState() {
        return this.state;
    }

    /**
     * Get realtime configuration
     */
    getConfig() {
        return {
            provider: this.provider,
            realtimeConfig: this.realtimeConfig,
            connectionConfig: this.connectionConfig,
            state: this.state,
            supported: this.isSupported(),
            active: this.isActive(),
            sessionReady: this.isSessionReady()
        };
    }

    /**
     * Dispose realtime interface
     */
    dispose() {
        this.eventHandlers.clear();
        this._setState(UniversalRealtimeStates.DISCONNECTED);
    }
}

/**
 * Create universal realtime interface for a provider
 */
export function createUniversalRealtimeInterface(providerConfig, model) {
    return new UniversalRealtimeInterface(providerConfig, model);
}

export default UniversalRealtimeInterface;