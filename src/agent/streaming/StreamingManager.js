/**
 * Unified LangGraph Streaming Manager
 *
 * Production-ready streaming manager with optimized LangGraph patterns
 * Consolidates functionality from OptimizedStreamingManager for better performance
 *
 * Features:
 * - Native LangGraph streaming (values, updates, messages)
 * - Token-level streaming with buffering
 * - Media input processing for multimodal interactions
 * - Performance optimization and adaptive configuration
 * - Enterprise-grade error handling and recovery
 */

import { createLogger } from '@/utils/logger';
import { getStreamingConfig } from '@/agent/config/AgentConfig.js';

// Import performance components for coordination
let PerformanceOptimizer, PerformanceTracker, PerformanceCoordinator;

// Lazy load performance components
async function loadPerformanceComponents() {
    if (!PerformanceOptimizer) {
        const { PerformanceOptimizer: PO } = await import('./PerformanceOptimizer.js');
        const { PerformanceMonitor: PT } = await import('../services/monitoring/PerformanceMonitor.js');
        const { PerformanceCoordinator: PC } = await import('../services/infrastructure/performance/PerformanceCoordinator.js');

        PerformanceOptimizer = PO;
        PerformanceTracker = PT; // Keep variable name for compatibility
        PerformanceCoordinator = PC;
    }
    return { PerformanceOptimizer, PerformanceTracker, PerformanceCoordinator };
}

export class StreamingManager {
    constructor(options = {}) {
        this.logger = createLogger('StreamingManager');

        // Use centralized configuration with overrides
        const defaultConfig = getStreamingConfig();
        this.options = {
            ...defaultConfig,
            ...options
        };

        // Active streaming sessions
        this.activeSessions = new Map();

        // Audio streaming state for enhanced functionality
        this.audioStreamingState = new Map(); // sessionId -> audio state

        // Stream mode configuration
        this.streamModes = {
            values: 'values',      // Full state after each node
            updates: 'updates',    // State updates as they happen
            messages: 'messages',  // Individual LLM tokens
            custom: 'custom'       // Custom events from tools
        };

        // Performance coordination components (lazy-loaded)
        this.performanceOptimizer = null;
        this.performanceTracker = null;
        this.performanceCoordinator = null;
        this.performanceInitialized = false;

        this.logger.info('Unified StreamingManager initialized with performance optimizations');
    }

    /**
     * ENHANCED: Initialize performance coordination components
     * 🔥 FIXED: Use shared InfrastructureManager instead of creating duplicate instances
     */
    async initializePerformanceCoordination(infrastructureManager = null) {
        if (this.performanceInitialized) {
            return;
        }

        try {
            this.logger.debug('Initializing performance coordination with shared infrastructure...');

            if (infrastructureManager) {
                // Use shared infrastructure services to prevent conflicts
                this.logger.info('🔄 Using shared InfrastructureManager for performance coordination');

                const services = await infrastructureManager.createServicesForComponent('StreamingManager', {
                    performanceTracker: {
                        historySize: 1000,
                        metricsWindow: 300000
                    },
                    performanceCoordinator: {
                        targetLatency: this.options.targetLatency,
                        enableAdaptiveOptimization: this.options.adaptiveThrottling
                    }
                });

                this.performanceTracker = services.performanceTracker;
                this.performanceCoordinator = services.performanceCoordinator;

                // Still create local PerformanceOptimizer for streaming-specific optimization
                const { PerformanceOptimizer } = await loadPerformanceComponents();
                this.performanceOptimizer = new PerformanceOptimizer({
                    targetLatency: this.options.targetLatency,
                    chunkSize: this.options.chunkBuffer,
                    bufferSize: this.options.bufferSize,
                    adaptiveThrottling: this.options.adaptiveThrottling
                });

                // Initialize coordinator with local optimizer
                this.performanceCoordinator.initialize(this.performanceOptimizer, this.performanceTracker);

            } else {
                // Fallback: Create local instances (old behavior)
                this.logger.warn('⚠️ No InfrastructureManager provided - creating local performance instances');

                const { PerformanceOptimizer, PerformanceTracker: PerformanceMonitor, PerformanceCoordinator } = await loadPerformanceComponents();

                this.performanceOptimizer = new PerformanceOptimizer({
                    targetLatency: this.options.targetLatency,
                    chunkSize: this.options.chunkBuffer,
                    bufferSize: this.options.bufferSize,
                    adaptiveThrottling: this.options.adaptiveThrottling
                });

                this.performanceTracker = new PerformanceMonitor({
                    historySize: 1000,
                    metricsWindow: 300000
                });

                this.performanceCoordinator = new PerformanceCoordinator({
                    targetLatency: this.options.targetLatency,
                    enableAdaptiveOptimization: this.options.adaptiveThrottling
                });

                this.performanceCoordinator.initialize(this.performanceOptimizer, this.performanceTracker);
            }

            this.performanceInitialized = true;

            this.logger.info('✅ Performance coordination initialized successfully', {
                usingSharedInfrastructure: !!infrastructureManager,
                hasOptimizer: !!this.performanceOptimizer,
                hasTracker: !!this.performanceTracker,
                hasCoordinator: !!this.performanceCoordinator
            });

        } catch (error) {
            this.logger.warn('Failed to initialize performance coordination:', error);
            // Continue without performance coordination
        }
    }

    /**
     * Get unified performance metrics
     */
    getPerformanceMetrics() {
        if (!this.performanceCoordinator) {
            return {
                error: 'Performance coordination not initialized',
                unified: { performanceGrade: 'Unknown' }
            };
        }

        return this.performanceCoordinator.getUnifiedMetrics();
    }

    /**
     * Create streaming configuration for LangGraph agent
     * Based on LangGraph streaming API patterns from official docs
     * @param {Object} options - Streaming configuration
     * @returns {Object} LangGraph streaming config
     */
    createStreamConfig(options = {}) {
        const {
            modes = ['updates', 'messages'],
            sessionId = 'default',
            includeTypes = null,
            excludeTypes = null,
            includeTags = null,
            excludeTags = null,
            includeNames = null,
            excludeNames = null,
            subgraphs = false,
            encoding = null,
            enableAudio = false,
            audioFormat = 'arraybuffer'
        } = options;

        // Validate stream modes based on LangGraph API
        const validModes = modes.filter(mode =>
            Object.values(this.streamModes).includes(mode)
        );

        if (validModes.length === 0) {
            this.logger.warn('No valid stream modes specified, using defaults');
            validModes.push('updates', 'messages');
        }

        const config = {
            streamMode: validModes,
            configurable: {
                thread_id: sessionId
            }
        };

        // Add audio-specific configuration
        if (enableAudio) {
            config.configurable = {
                ...config.configurable,
                enable_audio: enableAudio,
                audio_format: audioFormat
            };
        }

        // Add subgraph streaming support (LangGraph feature)
        if (subgraphs) {
            config.subgraphs = true;
        }

        // Add encoding for HTTP streaming (server-sent events)
        if (encoding) {
            config.encoding = encoding;
        }

        // Add filtering options based on LangGraph streamEvents API
        if (includeTypes) {
            config.includeTypes = includeTypes;
        }
        if (excludeTypes) {
            config.excludeTypes = excludeTypes;
        }
        if (includeTags) {
            config.includeTags = includeTags;
        }
        if (excludeTags) {
            config.excludeTags = excludeTags;
        }
        if (includeNames) {
            config.includeNames = includeNames;
        }
        if (excludeNames) {
            config.excludeNames = excludeNames;
        }

        this.logger.debug('Created stream config:', {
            modes: validModes,
            sessionId,
            subgraphs,
            encoding,
            enableAudio,
            audioFormat,
            includeTypes,
            excludeTypes,
            includeTags,
            excludeTags,
            includeNames,
            excludeNames
        });

        return config;
    }

    /**
     * Create stream events configuration for granular event streaming
     * Based on LangGraph streamEvents API
     * @param {Object} options - Stream events configuration
     * @returns {Object} Stream events config
     */
    createStreamEventsConfig(options = {}) {
        const {
            version = 'v2',
            encoding = null,
            includeTypes = null,
            excludeTypes = null,
            includeTags = null,
            excludeTags = null,
            includeNames = null,
            excludeNames = null
        } = options;

        const config = { version };

        // Add encoding for HTTP streaming
        if (encoding) {
            config.encoding = encoding;
        }

        // Add filtering options
        const filterOptions = {};
        if (includeTypes) filterOptions.includeTypes = includeTypes;
        if (excludeTypes) filterOptions.excludeTypes = excludeTypes;
        if (includeTags) filterOptions.includeTags = includeTags;
        if (excludeTags) filterOptions.excludeTags = excludeTags;
        if (includeNames) filterOptions.includeNames = includeNames;
        if (excludeNames) filterOptions.excludeNames = excludeNames;

        this.logger.debug('Created stream events config:', config);

        return { config, filterOptions };
    }

    /**
     * Start streaming session with LangGraph agent
     * ENHANCED: Combines native LangGraph patterns with production features
     */
    async startStream(agent, input, options = {}) {
        const sessionId = options.sessionId || 'default';
        const streamConfig = this.createStreamConfig(options);

        try {
            this.logger.info(`Starting streaming session: ${sessionId}`, {
                inputType: typeof input,
                streamModes: streamConfig.streamMode
            });

            // Create the stream using LangGraph's native streaming
            const stream = await agent.stream(input, streamConfig);

            // Store session info (CONSOLIDATED)
            this._createSession(sessionId, input, streamConfig, 'unified_stream');

            return this.processStream(stream, sessionId, options);

        } catch (error) {
            this.logger.error(`Failed to start stream for session ${sessionId}:`, error);
            this.activeSessions.delete(sessionId);
            throw error;
        }
    }

    /**
     * ENHANCED: Start native LangGraph stream with optimized patterns and performance coordination
     * Integrated from OptimizedStreamingManager for better performance
     */
    async startNativeStream(agent, input, options = {}) {
        const sessionId = options.sessionId || 'default';

        try {
            // Initialize performance coordination if not already done
            if (!this.performanceInitialized) {
                await this.initializePerformanceCoordination();
            }

            // Configure LangGraph streaming modes
            // FIX: Use 'values' mode instead of 'messages' to avoid name property issues
            const streamConfig = {
                streamMode: options.modes || ['values', 'updates'], // Changed from ['messages', 'updates']
                configurable: {
                    thread_id: sessionId,
                    // Add multimodal support configuration
                    enable_multimodal: options.enableMultimodal || false,
                    audio_format: options.audioFormat || 'float32',
                    video_format: options.videoFormat || 'base64'
                }
            };

            // Apply performance optimizations if available
            if (this.performanceCoordinator) {
                const optimizedConfig = await this.performanceCoordinator.performCoordinatedOptimization();
                if (optimizedConfig) {
                    // Apply optimized settings to stream config
                    streamConfig.configurable = {
                        ...streamConfig.configurable,
                        streaming_chunk_size: optimizedConfig.chunkSize,
                        streaming_delay_ms: optimizedConfig.delayMs,
                        enable_compression: true
                    };

                    this.logger.debug('Applied performance optimizations to stream config', optimizedConfig);
                }
            }

            // FIX: Add error handling for undefined agent
            if (!agent || typeof agent.stream !== 'function') {
                throw new Error('Invalid agent: must have stream method');
            }

            // FIX: Validate input format
            if (!input || typeof input !== 'object') {
                throw new Error('Invalid input: must be an object with messages array');
            }

            this.logger.info(`Starting native LangGraph stream for session: ${sessionId}`, {
                modes: streamConfig.streamMode,
                multimodal: streamConfig.configurable.enable_multimodal,
                optimized: !!this.performanceCoordinator
            });

            // Use agent.stream() directly for optimal performance
            const stream = await agent.stream(input, streamConfig);

            // Create optimized stream with monitoring if performance coordination is available
            const monitoredStream = this.performanceCoordinator
                ? this.performanceCoordinator.createOptimizedStream(stream, sessionId)
                : stream;

            // Store session info (CONSOLIDATED)
            this._createSession(sessionId, input, streamConfig, 'native_stream', {
                monitored: !!this.performanceCoordinator
            });

            return this.processNativeStream(monitoredStream, sessionId, options);

        } catch (error) {
            // Enhanced error handling
            this.logger.error(`Failed to start native stream for session ${sessionId}:`, {
                error: error.message,
                stack: error.stack,
                agentType: agent?.constructor?.name,
                inputType: typeof input
            });
            this.activeSessions.delete(sessionId);
            throw error;
        }
    }

    /**
     * ENHANCED: Process native LangGraph stream with optimized chunk handling
     * Integrated from OptimizedStreamingManager
     */
    async* processNativeStream(stream, sessionId, options = {}) {
        const callbacks = {
            onMessage: options.onMessage || (() => { }),
            onToolCall: options.onToolCall || (() => { }),
            onStateUpdate: options.onStateUpdate || (() => { }),
            onComplete: options.onComplete || (() => { }),
            onError: options.onError || (() => { })
        };

        let messageBuffer = '';
        let chunkCount = 0;

        try {
            // Iterate through native LangGraph stream with optimized processing
            for await (const chunk of stream) {
                chunkCount++;

                // Process with enhanced chunk handling
                const processedChunk = this.processLangGraphChunk(chunk, sessionId, chunkCount);

                // Handle message chunks (token streaming)
                if (processedChunk.type === 'message' && processedChunk.content) {
                    messageBuffer += processedChunk.content;
                    callbacks.onMessage({
                        sessionId,
                        token: processedChunk.content,
                        fullMessage: messageBuffer,
                        chunkIndex: chunkCount
                    });
                }

                // Handle tool calls
                if (processedChunk.type === 'tool_call') {
                    callbacks.onToolCall({
                        sessionId,
                        toolCall: processedChunk.toolCall,
                        chunkIndex: chunkCount
                    });
                }

                // Handle state updates
                if (processedChunk.type === 'state_update') {
                    callbacks.onStateUpdate({
                        sessionId,
                        update: processedChunk.update,
                        chunkIndex: chunkCount
                    });
                }

                yield processedChunk;
            }

            // Mark session as complete
            const session = this.activeSessions.get(sessionId);
            if (session) {
                session.status = 'completed';
                session.endTime = Date.now();
                session.totalChunks = chunkCount;
                session.fullMessage = messageBuffer;
            }

            callbacks.onComplete({
                sessionId,
                fullMessage: messageBuffer,
                chunkCount,
                duration: session?.endTime - session?.startTime
            });

        } catch (error) {
            this.logger.error(`Native stream error for session ${sessionId}:`, error);

            const session = this.activeSessions.get(sessionId);
            if (session) {
                session.status = 'failed';
                session.error = error;
            }

            callbacks.onError({ sessionId, error });
            throw error;
        }
    }

    /**
     * ENHANCED: Process LangGraph chunk with optimized patterns
     * Integrated from OptimizedStreamingManager for better performance
     */
    processLangGraphChunk(chunk, sessionId, chunkIndex) {
        const timestamp = Date.now();

        // LangGraph chunks have specific structures
        // Messages mode: { messages: [AIMessageChunk] }
        if (chunk.messages && chunk.messages.length > 0) {
            const lastMessage = chunk.messages[chunk.messages.length - 1];

            return {
                type: 'message',
                content: lastMessage.content || '',
                toolCalls: lastMessage.tool_calls || [],
                sessionId,
                chunkIndex,
                timestamp,
                metadata: {
                    messageType: lastMessage.constructor.name,
                    hasToolCalls: (lastMessage.tool_calls || []).length > 0
                }
            };
        }

        // Updates mode: { [nodeId]: nodeOutput }
        if (typeof chunk === 'object' && !chunk.messages) {
            return {
                type: 'state_update',
                update: chunk,
                sessionId,
                chunkIndex,
                timestamp,
                metadata: {
                    updateKeys: Object.keys(chunk)
                }
            };
        }

        // Tool execution chunks
        if (chunk.tool_calls || (chunk.messages && chunk.messages.some(m => m.tool_calls))) {
            return {
                type: 'tool_call',
                toolCall: chunk.tool_calls || chunk.messages[0].tool_calls,
                sessionId,
                chunkIndex,
                timestamp
            };
        }

        // Generic chunk
        return {
            type: 'generic',
            data: chunk,
            sessionId,
            chunkIndex,
            timestamp
        };
    }

    /**
     * Start stream events for granular event monitoring
     * Based on LangGraph streamEvents API
     * @param {Object} agent - LangGraph agent instance
     * @param {Object} input - Initial input
     * @param {Object} options - Stream events options
     * @returns {AsyncIterator} Stream events iterator
     */
    async startStreamEvents(agent, input, options = {}) {
        const sessionId = options.sessionId || 'default';
        const { config, filterOptions } = this.createStreamEventsConfig(options);

        try {
            this.logger.info(`Starting stream events for session: ${sessionId}`, {
                inputType: typeof input,
                filterOptions
            });

            // Store session info (CONSOLIDATED)
            this._createSession(sessionId, input, config, 'events', { filterOptions });

            // Create the stream events using LangGraph's streamEvents API
            const eventStream = await agent.streamEvents(input, config, filterOptions);

            return this.processStreamEvents(eventStream, sessionId, options);

        } catch (error) {
            this.logger.error(`Failed to start stream events for session ${sessionId}:`, error);
            this.activeSessions.delete(sessionId);
            throw error;
        }
    }

    /**
     * Stream with multiple modes simultaneously
     * Based on LangGraph API: agent.stream(input, { streamMode: ["updates", "messages", "custom"] })
     * @param {Object} agent - LangGraph agent instance
     * @param {Object} input - Initial input
     * @param {Object} options - Streaming options
     * @returns {AsyncIterator} Stream iterator with multiple modes
     */
    async startMultiModeStream(agent, input, options = {}) {
        const {
            modes = ['updates', 'messages', 'custom'],
            sessionId = 'default'
        } = options;

        const config = this.createStreamConfig({ ...options, modes });

        try {
            this.logger.info(`Starting multi-mode stream for session: ${sessionId}`, {
                modes
            });

            // Store session info (CONSOLIDATED)
            this._createSession(sessionId, input, config, 'multi-mode');

            // Create multi-mode stream
            const stream = await agent.stream(input, config);

            return this.processMultiModeStream(stream, sessionId, options);

        } catch (error) {
            this.logger.error(`Failed to start multi-mode stream for session ${sessionId}:`, error);
            this.activeSessions.delete(sessionId);
            throw error;
        }
    }

    /**
     * Create HTTP-compatible streaming response
     * Based on LangGraph server-sent events pattern
     * @param {Object} agent - LangGraph agent instance
     * @param {Object} input - Initial input
     * @param {Object} options - HTTP streaming options
     * @returns {Response} HTTP Response with event stream
     */
    async createHTTPStream(agent, input, options = {}) {
        const {
            sessionId = 'default',
            encoding = 'text/event-stream'
        } = options;

        try {
            // Create stream events with HTTP encoding
            const { config, filterOptions } = this.createStreamEventsConfig({
                ...options,
                encoding
            });

            const eventStream = await agent.streamEvents(input, config, filterOptions);

            // Return HTTP Response with proper headers
            return new Response(eventStream, {
                headers: {
                    'content-type': 'text/event-stream',
                    'cache-control': 'no-cache',
                    'connection': 'keep-alive'
                }
            });

        } catch (error) {
            this.logger.error(`Failed to create HTTP stream for session ${sessionId}:`, error);
            throw error;
        }
    }

    /**
     * Stream with automatic token concatenation
     * Based on LangChain concat utility pattern
     * @param {Object} agent - LangGraph agent instance
     * @param {Object} input - Initial input
     * @param {Object} options - Token streaming options
     * @returns {Promise<Object>} Final concatenated message
     */
    async streamWithTokenConcat(agent, input, options = {}) {
        const {
            sessionId = 'default',
            onTokenChunk = () => { },
            onComplete = () => { }
        } = options;

        try {
            // Stream in messages mode for token-by-token output
            const config = this.createStreamConfig({
                ...options,
                modes: ['messages']
            });

            const stream = await agent.stream(input, config);
            let finalMessage = undefined;

            // Concatenate chunks using LangChain pattern
            for await (const [message, metadata] of stream) {
                // Use concat pattern from LangChain docs
                if (finalMessage) {
                    finalMessage = this.concatChunks(finalMessage, message);
                } else {
                    finalMessage = message;
                }

                onTokenChunk({
                    sessionId,
                    token: message.content || '',
                    fullMessage: finalMessage,
                    metadata
                });
            }

            onComplete({ sessionId, finalMessage });
            return finalMessage;

        } catch (error) {
            this.logger.error(`Token concat stream error for session ${sessionId}:`, error);
            throw error;
        }
    }

    /**
     * Concatenate message chunks (implements LangChain concat pattern)
     * @param {Object} chunk1 - First chunk
     * @param {Object} chunk2 - Second chunk
     * @returns {Object} Concatenated chunk
     */
    concatChunks(chunk1, chunk2) {
        // Basic concatenation logic for AIMessageChunk-like objects
        if (!chunk1) return chunk2;
        if (!chunk2) return chunk1;

        // Implement additive behavior for message chunks
        return {
            ...chunk1,
            content: (chunk1.content || '') + (chunk2.content || ''),
            // Merge tool_call_chunks if present
            tool_call_chunks: [
                ...(chunk1.tool_call_chunks || []),
                ...(chunk2.tool_call_chunks || [])
            ]
        };
    }

    /**
     * Process LangGraph stream and emit structured events
     * @param {AsyncIterator} stream - LangGraph stream
     * @param {string} sessionId - Session identifier
     * @param {Object} options - Processing options
     * @returns {AsyncIterator} Processed stream
     */
    async* processStream(stream, sessionId, options = {}) {
        const callbacks = {
            onStateUpdate: options.onStateUpdate || (() => { }),
            onMessageChunk: options.onMessageChunk || (() => { }),
            onToolCall: options.onToolCall || (() => { }),
            onComplete: options.onComplete || (() => { }),
            onError: options.onError || (() => { })
        };

        let fullResponse = '';
        let chunkCount = 0;

        try {
            for await (const chunk of stream) {
                chunkCount++;

                const processedChunk = await this.processChunk(
                    chunk,
                    sessionId,
                    chunkCount,
                    callbacks
                );

                // Accumulate response text
                if (processedChunk.content) {
                    fullResponse += processedChunk.content;
                }

                yield processedChunk;
            }

            // Mark session as complete
            const session = this.activeSessions.get(sessionId);
            if (session) {
                session.status = 'completed';
                session.endTime = Date.now();
                session.totalChunks = chunkCount;
                session.fullResponse = fullResponse;
            }

            callbacks.onComplete({
                sessionId,
                fullResponse,
                chunkCount,
                duration: session?.endTime - session?.startTime
            });

            this.logger.info(`Stream completed for session ${sessionId}`, {
                chunkCount,
                responseLength: fullResponse.length
            });

        } catch (error) {
            this.logger.error(`Stream error for session ${sessionId}:`, error);

            // Mark session as failed
            const session = this.activeSessions.get(sessionId);
            if (session) {
                session.status = 'failed';
                session.error = error;
            }

            callbacks.onError({ sessionId, error });
            throw error;
        }
    }

    /**
     * Process LangGraph stream events and emit structured events
     * @param {AsyncIterator} eventStream - LangGraph event stream
     * @param {string} sessionId - Session identifier
     * @param {Object} options - Processing options
     * @returns {AsyncIterator} Processed event stream
     */
    async* processStreamEvents(eventStream, sessionId, options = {}) {
        const callbacks = {
            onChatModelStream: options.onChatModelStream || (() => { }),
            onChatModelEnd: options.onChatModelEnd || (() => { }),
            onToolStart: options.onToolStart || (() => { }),
            onToolEnd: options.onToolEnd || (() => { }),
            onCustomEvent: options.onCustomEvent || (() => { }),
            onComplete: options.onComplete || (() => { }),
            onError: options.onError || (() => { })
        };

        let eventCount = 0;
        const processedEvents = [];

        try {
            for await (const event of eventStream) {
                eventCount++;

                const processedEvent = await this.processEvent(
                    event,
                    sessionId,
                    eventCount,
                    callbacks
                );

                processedEvents.push(processedEvent);
                yield processedEvent;
            }

            // Mark session as complete
            const session = this.activeSessions.get(sessionId);
            if (session) {
                session.status = 'completed';
                session.endTime = Date.now();
                session.totalEvents = eventCount;
                session.processedEvents = processedEvents;
            }

            callbacks.onComplete({
                sessionId,
                eventCount,
                duration: session?.endTime - session?.startTime
            });

            this.logger.info(`Stream events completed for session ${sessionId}`, {
                eventCount
            });

        } catch (error) {
            this.logger.error(`Stream events error for session ${sessionId}:`, error);

            // Mark session as failed
            const session = this.activeSessions.get(sessionId);
            if (session) {
                session.status = 'failed';
                session.error = error;
            }

            callbacks.onError({ sessionId, error });
            throw error;
        }
    }

    /**
     * Process media input from MediaCaptureManager (direct integration)
     * @param {Object} mediaInput - Processed media input from bridge
     * @param {string} sessionId - Session identifier
     * @returns {Promise<boolean>} Success status
     */
    async processMediaInput(mediaInput, sessionId) {
        try {
            this.logger.debug('Processing media input from bridge:', {
                sessionId,
                inputType: mediaInput.streamingContext?.inputType,
                hasAudio: !!mediaInput.content?.audio,
                hasVideo: !!mediaInput.content?.video,
                hasText: !!mediaInput.content?.text
            });

            // Create streaming session if not exists
            if (!this.activeSessions.has(sessionId)) {
                this.activeSessions.set(sessionId, {
                    startTime: Date.now(),
                    status: 'active',
                    type: 'media_input',
                    inputType: mediaInput.streamingContext?.inputType
                });
            }

            // Convert media input to streaming format
            const streamChunk = this.convertMediaInputToStreamChunk(mediaInput, sessionId);

            // Process the chunk through existing pipeline
            const processedChunk = await this.processChunk(
                streamChunk,
                sessionId,
                this.getNextChunkIndex(sessionId),
                {
                    onStateUpdate: () => { },
                    onMessageChunk: (data) => {
                        this.logger.debug('Media input processed to message chunk:', data);
                    },
                    onToolCall: (data) => {
                        this.logger.debug('Media input triggered tool call:', data);
                    },
                    onComplete: () => { },
                    onError: (error) => {
                        this.logger.error('Media input processing error:', error);
                    }
                }
            );

            return true;
        } catch (error) {
            this.logger.error('Failed to process media input:', error);
            return false;
        }
    }

    /**
     * Convert media input to streaming chunk format
     * @param {Object} mediaInput - Media input from bridge
     * @param {string} sessionId - Session identifier
     * @returns {Object} Stream chunk
     */
    convertMediaInputToStreamChunk(mediaInput, sessionId) {
        const { content, streamingContext } = mediaInput;

        // Create base chunk structure
        const chunk = {
            sessionId,
            timestamp: streamingContext.timestamp,
            type: 'media_input',
            data: content,
            metadata: {
                inputType: streamingContext.inputType,
                originalMetadata: content.metadata
            }
        };

        // Adapt based on input type
        switch (streamingContext.inputType) {
            case 'audio':
                chunk.type = 'audio_chunk';
                chunk.audioData = content.audio;
                chunk.format = content.metadata?.format;
                break;

            case 'video':
                chunk.type = 'video_frame';
                chunk.videoFrames = content.video;
                chunk.format = content.metadata?.format;
                break;

            case 'text':
                chunk.type = 'text_input';
                chunk.content = content.text;
                chunk.language = content.metadata?.language;
                break;

            case 'multimodal':
                chunk.type = 'multimodal_input';
                chunk.content = content.text;
                chunk.audioData = content.audio;
                chunk.videoFrames = content.video;
                break;
        }

        return chunk;
    }

    /**
     * Get next chunk index for session
     * @param {string} sessionId - Session identifier
     * @returns {number} Next chunk index
     */
    getNextChunkIndex(sessionId) {
        const session = this.activeSessions.get(sessionId);
        if (!session) return 0;

        session.chunkCount = (session.chunkCount || 0) + 1;
        return session.chunkCount;
    }

    /**
     * Process individual stream chunk
     * @param {Object} chunk - Raw chunk from LangGraph
     * @param {string} sessionId - Session identifier
     * @param {number} chunkIndex - Chunk sequence number
     * @param {Object} callbacks - Event callbacks
     * @returns {Object} Processed chunk
     */
    async processChunk(chunk, sessionId, chunkIndex, callbacks) {
        const timestamp = Date.now();

        // Initialize processed chunk structure
        const processedChunk = {
            sessionId,
            chunkIndex,
            timestamp,
            type: 'unknown',
            content: null,
            metadata: {}
        };

        try {
            // Process different chunk types based on LangGraph streaming modes

            // Handle 'updates' mode - state updates
            if (chunk && typeof chunk === 'object' && chunk.type === 'updates') {
                processedChunk.type = 'state_update';
                processedChunk.updates = chunk.data || chunk;
                processedChunk.metadata.updateKeys = Object.keys(chunk.data || {});

                callbacks.onStateUpdate({
                    sessionId,
                    updates: processedChunk.updates,
                    chunkIndex
                });

                this.logger.debug(`State update chunk ${chunkIndex}:`, {
                    updateKeys: processedChunk.metadata.updateKeys
                });
            }

            // Handle 'messages' mode - LLM token streaming
            else if (chunk && chunk.type === 'messages') {
                processedChunk.type = 'message_chunk';
                processedChunk.content = chunk.content || '';
                processedChunk.metadata.messageType = chunk.messageType || 'token';

                if (processedChunk.content) {
                    callbacks.onMessageChunk({
                        sessionId,
                        content: processedChunk.content,
                        isPartial: true,
                        chunkIndex
                    });
                }

                this.logger.debug(`Message chunk ${chunkIndex}: ${processedChunk.content.length} chars`);
            }

            // Handle 'values' mode - full state values
            else if (chunk && chunk.type === 'values') {
                processedChunk.type = 'state_value';
                processedChunk.state = chunk.data || chunk;
                processedChunk.metadata.stateKeys = Object.keys(chunk.data || {});

                this.logger.debug(`State value chunk ${chunkIndex}`);
            }

            // Handle media input chunks (from MediaCaptureManager direct integration)
            else if (chunk && (chunk.type === 'audio_chunk' || chunk.type === 'video_frame' ||
                chunk.type === 'text_input' || chunk.type === 'multimodal_input')) {
                processedChunk.type = chunk.type;
                processedChunk.content = chunk.content || '';
                processedChunk.mediaData = {
                    audio: chunk.audioData,
                    video: chunk.videoFrames,
                    text: chunk.content
                };
                processedChunk.metadata.inputType = chunk.metadata?.inputType;
                processedChunk.metadata.format = chunk.format;

                // Trigger appropriate callbacks based on media type
                if (chunk.audioData) {
                    callbacks.onMessageChunk({
                        sessionId,
                        content: '[Audio Input]',
                        audioData: chunk.audioData,
                        isPartial: false,
                        chunkIndex
                    });
                }

                if (chunk.videoFrames) {
                    callbacks.onMessageChunk({
                        sessionId,
                        content: '[Video Input]',
                        videoFrames: chunk.videoFrames,
                        isPartial: false,
                        chunkIndex
                    });
                }

                if (chunk.content) {
                    callbacks.onMessageChunk({
                        sessionId,
                        content: chunk.content,
                        isPartial: false,
                        chunkIndex
                    });
                }

                this.logger.debug(`Media input chunk ${chunkIndex}: ${chunk.type}`);
            }

            // Handle tool execution events
            else if (chunk && (chunk.type === 'tool_calls' || chunk.tool_calls)) {
                processedChunk.type = 'tool_execution';
                processedChunk.toolCalls = chunk.tool_calls || chunk.data?.tool_calls || [];
                processedChunk.metadata.toolCount = processedChunk.toolCalls.length;

                callbacks.onToolCall({
                    sessionId,
                    toolCalls: processedChunk.toolCalls,
                    chunkIndex
                });

                this.logger.debug(`Tool execution chunk ${chunkIndex}: ${processedChunk.toolCalls.length} tools`);
            }

            // Handle direct content (fallback)
            else if (typeof chunk === 'string') {
                processedChunk.type = 'text_content';
                processedChunk.content = chunk;

                callbacks.onMessageChunk({
                    sessionId,
                    content: chunk,
                    isPartial: true,
                    chunkIndex
                });
            }

            // Handle generic object chunks
            else if (chunk && typeof chunk === 'object') {
                processedChunk.type = 'generic_object';
                processedChunk.data = chunk;
                processedChunk.metadata.objectKeys = Object.keys(chunk);

                // Try to extract content from common patterns
                if (chunk.content) {
                    processedChunk.content = chunk.content;
                    callbacks.onMessageChunk({
                        sessionId,
                        content: chunk.content,
                        isPartial: true,
                        chunkIndex
                    });
                }
            }

        } catch (error) {
            this.logger.error(`Error processing chunk ${chunkIndex}:`, error);
            processedChunk.type = 'error';
            processedChunk.error = error.message;
        }

        return processedChunk;
    }

    /**
     * Process individual stream event based on LangGraph event types
     * @param {Object} event - Raw event from LangGraph
     * @param {string} sessionId - Session identifier
     * @param {number} eventIndex - Event sequence number
     * @param {Object} callbacks - Event callbacks
     * @returns {Object} Processed event
     */
    async processEvent(event, sessionId, eventIndex, callbacks) {
        const timestamp = Date.now();

        // Initialize processed event structure
        const processedEvent = {
            sessionId,
            eventIndex,
            timestamp,
            originalEvent: event.event,
            name: event.name,
            data: event.data,
            metadata: event.metadata || {}
        };

        try {
            // Process different event types based on LangGraph streamEvents API
            switch (event.event) {
                case 'on_chain_start':
                    processedEvent.type = 'chain_start';
                    processedEvent.inputs = event.data?.input || {};
                    this.logger.debug(`Chain started: ${event.name}`, {
                        runId: event.run_id
                    });
                    break;

                case 'on_chain_stream':
                    processedEvent.type = 'chain_stream';
                    processedEvent.chunk = event.data?.chunk || null;
                    break;

                case 'on_chain_end':
                    processedEvent.type = 'chain_end';
                    processedEvent.outputs = event.data?.output || {};
                    break;

                case 'on_chat_model_start':
                    processedEvent.type = 'chat_model_start';
                    processedEvent.inputs = event.data?.input || {};
                    processedEvent.modelName = event.name;
                    break;

                case 'on_chat_model_stream':
                    processedEvent.type = 'chat_model_stream';
                    processedEvent.content = event.data?.chunk?.content || '';
                    processedEvent.chunk = event.data?.chunk || null;

                    // Handle tool call chunks from streaming
                    if (event.data?.chunk?.tool_call_chunks) {
                        processedEvent.toolCallChunks = event.data.chunk.tool_call_chunks;
                    }

                    if (processedEvent.content || processedEvent.toolCallChunks) {
                        callbacks.onChatModelStream({
                            sessionId,
                            content: processedEvent.content,
                            chunk: processedEvent.chunk,
                            toolCallChunks: processedEvent.toolCallChunks,
                            eventIndex
                        });
                    }
                    break;

                case 'on_chat_model_end':
                    processedEvent.type = 'chat_model_end';
                    processedEvent.outputs = event.data?.output || {};
                    processedEvent.finalMessage = event.data?.output;

                    callbacks.onChatModelEnd({
                        sessionId,
                        outputs: processedEvent.outputs,
                        finalMessage: processedEvent.finalMessage,
                        eventIndex
                    });
                    break;

                case 'on_tool_start':
                    processedEvent.type = 'tool_start';
                    processedEvent.toolName = event.name;
                    processedEvent.inputs = event.data?.input || {};

                    callbacks.onToolStart({
                        sessionId,
                        toolName: processedEvent.toolName,
                        inputs: processedEvent.inputs,
                        eventIndex
                    });
                    break;

                case 'on_tool_stream':
                    processedEvent.type = 'tool_stream';
                    processedEvent.toolName = event.name;
                    processedEvent.chunk = event.data?.chunk || null;
                    break;

                case 'on_tool_end':
                    processedEvent.type = 'tool_end';
                    processedEvent.toolName = event.name;
                    processedEvent.outputs = event.data?.output || {};

                    callbacks.onToolEnd({
                        sessionId,
                        toolName: processedEvent.toolName,
                        outputs: processedEvent.outputs,
                        eventIndex
                    });
                    break;

                case 'on_custom_event':
                    processedEvent.type = 'custom_event';
                    processedEvent.customName = event.name;
                    processedEvent.customData = event.data || {};

                    callbacks.onCustomEvent({
                        sessionId,
                        customName: processedEvent.customName,
                        customData: processedEvent.customData,
                        eventIndex
                    });
                    break;

                case 'on_parser_start':
                    processedEvent.type = 'parser_start';
                    processedEvent.parserName = event.name;
                    processedEvent.inputs = event.data?.input || {};
                    break;

                case 'on_parser_stream':
                    processedEvent.type = 'parser_stream';
                    processedEvent.parserName = event.name;
                    processedEvent.chunk = event.data?.chunk || null;
                    break;

                case 'on_parser_end':
                    processedEvent.type = 'parser_end';
                    processedEvent.parserName = event.name;
                    processedEvent.outputs = event.data?.output || {};
                    break;

                case 'on_retriever_start':
                    processedEvent.type = 'retriever_start';
                    processedEvent.retrieverName = event.name;
                    processedEvent.inputs = event.data?.input || {};
                    break;

                case 'on_retriever_end':
                    processedEvent.type = 'retriever_end';
                    processedEvent.retrieverName = event.name;
                    processedEvent.outputs = event.data?.output || {};
                    break;

                default:
                    processedEvent.type = 'unknown_event';
                    this.logger.debug(`Unknown event type: ${event.event}`, {
                        name: event.name,
                        runId: event.run_id
                    });
                    break;
            }

        } catch (error) {
            this.logger.error(`Error processing event ${eventIndex}:`, error);
            processedEvent.type = 'error';
            processedEvent.error = error.message;
        }

        return processedEvent;
    }

    /**
     * Stop streaming session
     * @param {string} sessionId - Session to stop
     * @returns {boolean} Success status
     */
    stopStream(sessionId) {
        const session = this.activeSessions.get(sessionId);

        if (session) {
            session.status = 'stopped';
            session.endTime = Date.now();

            this.logger.info(`Stopped streaming session: ${sessionId}`);
            return true;
        }

        this.logger.warn(`Session not found for stopping: ${sessionId}`);
        return false;
    }

    /**
     * Get session status
     * @param {string} sessionId - Session identifier
     * @returns {Object|null} Session info
     */
    getSessionStatus(sessionId) {
        return this.activeSessions.get(sessionId) || null;
    }

    /**
     * Get all active sessions
     * @returns {Object} Sessions map
     */
    getActiveSessions() {
        return Object.fromEntries(this.activeSessions);
    }

    /**
     * Clean up completed sessions
     * @param {number} maxAge - Max age in milliseconds
     */
    cleanupSessions(maxAge = 300000) { // 5 minutes default
        const now = Date.now();

        for (const [sessionId, session] of this.activeSessions.entries()) {
            const sessionAge = now - session.startTime;
            const isCompleted = ['completed', 'failed', 'stopped'].includes(session.status);

            if (isCompleted && sessionAge > maxAge) {
                this.activeSessions.delete(sessionId);
                this.logger.debug(`Cleaned up session: ${sessionId}`);
            }
        }
    }

    /**
     * ENHANCED: Create optimized token-level streaming with buffering
     * Integrated from OptimizedStreamingManager for sub-600ms performance
     */
    async* createTokenStream(messageStream, options = {}) {
        const {
            sessionId = 'default',
            tokenDelay = 20, // ms between tokens for natural streaming effect
            onToken = () => { }
        } = options;

        let fullText = '';
        let tokenCount = 0;

        try {
            for await (const chunk of messageStream) {
                if (chunk.type === 'message' && chunk.content) {
                    // Simple word-based tokenization for streaming effect
                    const tokens = chunk.content.split(/(\s+)/).filter(t => t.length > 0);

                    for (const token of tokens) {
                        tokenCount++;
                        fullText += token;

                        const tokenData = {
                            token,
                            tokenIndex: tokenCount,
                            fullText,
                            sessionId,
                            timestamp: Date.now()
                        };

                        onToken(tokenData);
                        yield tokenData;

                        // Add delay for natural streaming effect (optimized for performance)
                        if (tokenDelay > 0) {
                            await new Promise(resolve => setTimeout(resolve, tokenDelay));
                        }
                    }
                }
            }
        } catch (error) {
            this.logger.error('Enhanced token stream error:', error);
            throw error;
        }
    }

    /**
     * CONSOLIDATED: Create session with standardized pattern
     * Eliminates duplicate session creation code
     */
    _createSession(sessionId, input, streamConfig, type, additionalProps = {}) {
        this.activeSessions.set(sessionId, {
            startTime: Date.now(),
            input,
            streamConfig,
            status: 'active',
            type,
            ...additionalProps
        });
    }

    /**
     * Simple tokenization for streaming (CONSOLIDATED)
     * @param {string} content - Content to tokenize
     * @returns {Array} Token array
     */
    tokenizeContent(content) {
        // Simple word-based tokenization for streaming effect
        return content.split(/(\s+)/).filter(token => token.length > 0);
    }

    /**
     * ENHANCED: Dispose of streaming manager with performance coordination cleanup
     */
    dispose() {
        // Stop all active sessions
        for (const sessionId of this.activeSessions.keys()) {
            this.stopStream(sessionId);
        }

        // Shutdown performance coordination
        if (this.performanceCoordinator) {
            this.performanceCoordinator.shutdown();
        }

        if (this.performanceTracker) {
            this.performanceTracker.shutdown();
        }

        // Clear all state
        this.activeSessions.clear();
        this.audioStreamingState.clear();

        // Reset performance components
        this.performanceOptimizer = null;
        this.performanceTracker = null;
        this.performanceCoordinator = null;
        this.performanceInitialized = false;

        this.logger.info('✅ Unified StreamingManager disposed with performance coordination cleanup');
    }
}

export default StreamingManager;