/**
 * Performance Optimizer for LangGraph Streaming
 * Implements advanced optimization patterns for real-time applications
 */

import { createLogger } from '@/utils/logger';

export class PerformanceOptimizer {
    constructor(options = {}) {
        this.logger = createLogger('PerformanceOptimizer');
        this.options = {
            targetLatency: 600, // ms - sub-600ms response time
            chunkSize: 20,      // Optimal chunk size for streaming
            bufferSize: 5,      // Number of chunks to buffer
            adaptiveThrottling: true,
            ...options
        };
        
        // Performance metrics
        this.metrics = {
            avgResponseTime: 0,
            avgChunkLatency: 0,
            totalRequests: 0,
            successRate: 0,
            throughput: 0
        };
        
        // Performance tracking
        this.requestHistory = [];
        this.maxHistorySize = 100;
        
        // Adaptive configuration
        this.adaptiveConfig = {
            currentChunkSize: this.options.chunkSize,
            currentDelay: 20, // ms between chunks
            lastAdjustment: 0
        };
    }

    /**
     * Optimize streaming configuration based on performance metrics
     */
    optimizeStreamingConfig(currentMetrics = {}) {
        const { avgResponseTime, avgChunkLatency } = currentMetrics;
        
        // Adaptive chunk size adjustment
        if (avgResponseTime > this.options.targetLatency) {
            // Increase chunk size to reduce overhead
            this.adaptiveConfig.currentChunkSize = Math.min(
                this.adaptiveConfig.currentChunkSize * 1.2,
                100 // Max chunk size
            );
            
            // Reduce delay between chunks
            this.adaptiveConfig.currentDelay = Math.max(
                this.adaptiveConfig.currentDelay * 0.8,
                5 // Min delay
            );
            
            this.logger.debug('Performance optimization: Increased chunk size and reduced delay', {
                newChunkSize: this.adaptiveConfig.currentChunkSize,
                newDelay: this.adaptiveConfig.currentDelay
            });
        } else if (avgResponseTime < this.options.targetLatency * 0.5) {
            // Reduce chunk size for smoother streaming
            this.adaptiveConfig.currentChunkSize = Math.max(
                this.adaptiveConfig.currentChunkSize * 0.9,
                5 // Min chunk size
            );
            
            this.logger.debug('Performance optimization: Reduced chunk size for smoother streaming', {
                newChunkSize: this.adaptiveConfig.currentChunkSize
            });
        }
        
        return {
            chunkSize: Math.round(this.adaptiveConfig.currentChunkSize),
            delayMs: Math.round(this.adaptiveConfig.currentDelay),
            bufferSize: this.options.bufferSize
        };
    }

    /**
     * Create optimized stream configuration for LangGraph
     */
    createOptimizedConfig(baseConfig = {}) {
        const optimizedSettings = this.optimizeStreamingConfig(this.metrics);
        
        return {
            ...baseConfig,
            streamMode: ['messages'], // Focus on messages for token streaming
            configurable: {
                ...baseConfig.configurable,
                // Performance optimizations
                streaming_chunk_size: optimizedSettings.chunkSize,
                streaming_delay_ms: optimizedSettings.delayMs,
                enable_compression: true,
                // Timeout optimizations
                request_timeout_ms: this.options.targetLatency * 2,
                chunk_timeout_ms: 100
            }
        };
    }

    /**
     * Monitor streaming performance
     */
    startPerformanceMonitoring(sessionId) {
        const startTime = Date.now();
        let chunkCount = 0;
        let totalChunkLatency = 0;
        let lastChunkTime = startTime;
        
        return {
            onChunk: (chunk) => {
                const now = Date.now();
                const chunkLatency = now - lastChunkTime;
                
                chunkCount++;
                totalChunkLatency += chunkLatency;
                lastChunkTime = now;
                
                // Real-time latency monitoring
                if (chunkLatency > 100) { // >100ms chunk latency warning
                    this.logger.warn('High chunk latency detected', {
                        sessionId,
                        chunkLatency,
                        chunkIndex: chunkCount
                    });
                }
            },
            
            onComplete: (result) => {
                const endTime = Date.now();
                const totalDuration = endTime - startTime;
                const avgChunkLatency = chunkCount > 0 ? totalChunkLatency / chunkCount : 0;
                
                // Update metrics
                this.updateMetrics({
                    responseTime: totalDuration,
                    chunkLatency: avgChunkLatency,
                    chunkCount,
                    success: true
                });
                
                this.logger.info('Streaming performance metrics', {
                    sessionId,
                    totalDuration,
                    avgChunkLatency,
                    chunkCount,
                    throughput: chunkCount / (totalDuration / 1000)
                });
            },
            
            onError: (error) => {
                const endTime = Date.now();
                const totalDuration = endTime - startTime;
                
                this.updateMetrics({
                    responseTime: totalDuration,
                    chunkLatency: 0,
                    chunkCount,
                    success: false
                });
                
                this.logger.error('Streaming performance error', {
                    sessionId,
                    error: error.message,
                    duration: totalDuration
                });
            }
        };
    }

    /**
     * Update performance metrics
     */
    updateMetrics(data) {
        const { responseTime, chunkLatency, chunkCount, success } = data;
        
        // Add to history
        this.requestHistory.push({
            timestamp: Date.now(),
            responseTime,
            chunkLatency,
            chunkCount,
            success
        });
        
        // Maintain history size
        while (this.requestHistory.length > this.maxHistorySize) {
            this.requestHistory.shift();
        }
        
        // Calculate averages
        const recentRequests = this.requestHistory.slice(-20); // Last 20 requests
        
        this.metrics.avgResponseTime = recentRequests.reduce((sum, req) => sum + req.responseTime, 0) / recentRequests.length;
        this.metrics.avgChunkLatency = recentRequests.reduce((sum, req) => sum + req.chunkLatency, 0) / recentRequests.length;
        this.metrics.totalRequests = this.requestHistory.length;
        this.metrics.successRate = recentRequests.filter(req => req.success).length / recentRequests.length;
        
        // Calculate throughput (chunks per second)
        const totalChunks = recentRequests.reduce((sum, req) => sum + req.chunkCount, 0);
        const totalTime = recentRequests.reduce((sum, req) => sum + req.responseTime, 0) / 1000;
        this.metrics.throughput = totalTime > 0 ? totalChunks / totalTime : 0;
    }

    /**
     * Get current performance metrics
     */
    getMetrics() {
        return {
            ...this.metrics,
            adaptiveConfig: { ...this.adaptiveConfig },
            recentPerformance: this.requestHistory.slice(-5),
            recommendations: this.getPerformanceRecommendations()
        };
    }

    /**
     * Get performance recommendations
     */
    getPerformanceRecommendations() {
        const recommendations = [];
        
        if (this.metrics.avgResponseTime > this.options.targetLatency) {
            recommendations.push({
                type: 'latency',
                severity: 'high',
                message: `Average response time (${Math.round(this.metrics.avgResponseTime)}ms) exceeds target (${this.options.targetLatency}ms)`,
                actions: [
                    'Increase chunk size',
                    'Reduce processing complexity',
                    'Enable compression',
                    'Optimize model selection'
                ]
            });
        }
        
        if (this.metrics.successRate < 0.95) {
            recommendations.push({
                type: 'reliability',
                severity: 'medium',
                message: `Success rate (${Math.round(this.metrics.successRate * 100)}%) below 95%`,
                actions: [
                    'Improve error handling',
                    'Add retry mechanisms',
                    'Increase timeout values',
                    'Monitor resource usage'
                ]
            });
        }
        
        if (this.metrics.avgChunkLatency > 50) {
            recommendations.push({
                type: 'chunk_latency',
                severity: 'medium',
                message: `Average chunk latency (${Math.round(this.metrics.avgChunkLatency)}ms) is high`,
                actions: [
                    'Optimize chunk processing',
                    'Reduce buffer size',
                    'Enable parallel processing'
                ]
            });
        }
        
        return recommendations;
    }

    /**
     * Create performance-optimized streaming wrapper
     */
    createOptimizedStream(baseStream, sessionId) {
        const monitor = this.startPerformanceMonitoring(sessionId);
        const optimizedConfig = this.optimizeStreamingConfig(this.metrics);
        
        return {
            async* [Symbol.asyncIterator]() {
                let chunkBuffer = [];
                let lastFlushTime = Date.now();
                
                try {
                    for await (const chunk of baseStream) {
                        monitor.onChunk(chunk);
                        
                        // Buffer chunks for optimal delivery
                        chunkBuffer.push(chunk);
                        
                        const now = Date.now();
                        const shouldFlush = 
                            chunkBuffer.length >= optimizedConfig.bufferSize ||
                            (now - lastFlushTime) >= optimizedConfig.delayMs;
                        
                        if (shouldFlush) {
                            // Yield buffered chunks
                            for (const bufferedChunk of chunkBuffer) {
                                yield bufferedChunk;
                            }
                            
                            chunkBuffer = [];
                            lastFlushTime = now;
                            
                            // Adaptive delay
                            if (optimizedConfig.delayMs > 0) {
                                await new Promise(resolve => 
                                    setTimeout(resolve, optimizedConfig.delayMs)
                                );
                            }
                        }
                    }
                    
                    // Flush remaining chunks
                    for (const bufferedChunk of chunkBuffer) {
                        yield bufferedChunk;
                    }
                    
                    monitor.onComplete({ sessionId });
                    
                } catch (error) {
                    monitor.onError(error);
                    throw error;
                }
            }
        };
    }

    /**
     * Reset metrics and adaptive configuration
     */
    reset() {
        this.requestHistory = [];
        this.metrics = {
            avgResponseTime: 0,
            avgChunkLatency: 0,
            totalRequests: 0,
            successRate: 0,
            throughput: 0
        };
        
        this.adaptiveConfig = {
            currentChunkSize: this.options.chunkSize,
            currentDelay: 20,
            lastAdjustment: 0
        };
        
        this.logger.info('Performance optimizer reset');
    }
}

export default PerformanceOptimizer;