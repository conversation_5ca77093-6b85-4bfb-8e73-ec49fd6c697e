/**
 * Unified Agent Streaming Module
 * Consolidated streaming interface for all agent communication
 * Eliminates duplication between multiple stream processors
 */

// Unified streaming manager - now the single source of truth
export { StreamingManager } from './StreamingManager.js';

// Performance optimization components (integrated with StreamingManager)
export { PerformanceOptimizer } from './PerformanceOptimizer.js';

// Default export - unified streaming interface
export { StreamingManager as default } from './StreamingManager.js';

/**
 * Factory function to create consolidated streaming manager
 * 
 * @param {Object} options - Configuration options
 * @returns {StreamingManager}
 */
export function createStreamingManager(options = {}) {
    return new StreamingManager(options);
}

/**
 * Create streaming manager with performance optimization
 * 
 * @param {Object} options - Configuration options
 * @returns {StreamingManager}
 */
export function createOptimizedStreamingManager(options = {}) {
    return new StreamingManager({
        ...options,
        enablePerformanceOptimization: true,
        targetLatency: options.targetLatency || 600,
        adaptiveThrottling: true
    });
}

/**
 * Consolidated streaming capabilities
 */
export const StreamingCapabilities = {
    // Native LangGraph streaming modes
    VALUES: 'values',      // Full state after each node
    UPDATES: 'updates',    // State updates as they happen  
    MESSAGES: 'messages',  // Individual LLM tokens
    CUSTOM: 'custom',      // Custom events from tools
    
    // Performance modes
    OPTIMIZED: 'optimized',     // Performance-optimized streaming
    REALTIME: 'realtime',       // Real-time audio/video streaming
    BATCH: 'batch'              // Batched streaming for efficiency
};

/**
 * Migration utilities for legacy stream processors
 */
export const StreamingMigration = {
    /**
     * Migrate from legacy LangChainStreamProcessor
     * @param {Object} legacyOptions - Old processor options
     * @returns {Object} - New StreamingManager options
     */
    fromLangChainProcessor(legacyOptions = {}) {
        return {
            preferredMode: 'messages',
            enableMultiMode: true,
            bufferSize: legacyOptions.bufferSize || 1000,
            enablePerformanceOptimization: true,
            targetLatency: legacyOptions.timeout || 600
        };
    },

    /**
     * Migrate from legacy StreamProcessor
     * @param {Object} legacyOptions - Old processor options
     * @returns {Object} - New StreamingManager options
     */
    fromStreamProcessor(legacyOptions = {}) {
        return {
            preferredMode: 'updates',
            enableValueStream: true,
            enableUpdateStream: true,
            chunkBuffer: legacyOptions.chunkSize || 50,
            adaptiveThrottling: true
        };
    }
};

/**
 * Backwards compatibility - deprecated but maintained for existing code
 * @deprecated Use StreamingManager directly instead
 */
export class LegacyStreamProcessor {
    constructor(options = {}) {
        console.warn('LegacyStreamProcessor is deprecated. Use StreamingManager instead.');
        return new StreamingManager(StreamingMigration.fromStreamProcessor(options));
    }
}

/**
 * Backwards compatibility - deprecated but maintained for existing code
 * @deprecated Use StreamingManager directly instead
 */
export class LegacyLangChainStreamProcessor {
    constructor(options = {}) {
        console.warn('LegacyLangChainStreamProcessor is deprecated. Use StreamingManager instead.');
        return new StreamingManager(StreamingMigration.fromLangChainProcessor(options));
    }
}

// Legacy exports for backwards compatibility
export const LangChainStreamProcessor = LegacyLangChainStreamProcessor;
export const StreamProcessor = LegacyStreamProcessor;
