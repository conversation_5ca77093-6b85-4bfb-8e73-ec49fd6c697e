/**
 * No-Activity Prompts for System 1 Background Operation
 * 
 * Provides prompts for handling no-activity scenarios where System 1 detects
 * quiet periods and escalates to System 2 with enriched context including
 * user profile memory and avatar profile for contextual responses and animations.
 */

import { createLogger } from '../../utils/logger.js';

const logger = createLogger('NoActivityPrompts');

/**
 * No-activity patterns and thresholds
 */
export const NO_ACTIVITY_PATTERNS = {
  quiet_period: {
    threshold: 10000,    // 10 seconds of no user input
    action: 'contextual_engagement',
    triggers: ['user-profile-analysis', 'avatar-expression', 'proactive-conversation']
  },
  extended_quiet: {
    threshold: 30000,    // 30 seconds extended quiet
    action: 'ambient_animation', 
    triggers: ['character-idle-behavior', 'environmental-awareness']
  },
  long_silence: {
    threshold: 120000,   // 2 minutes long silence
    action: 'check_in',
    triggers: ['user-wellness-check', 'topic-suggestion']
  }
};

/**
 * Create comprehensive no-activity engagement prompt for System 2 with structured JSON output
 * Integrates user profile memory + avatar profile with json_object format instruction
 */
export function createNoActivityEngagementPrompt(enrichedContext, pattern) {
  const { userProfile, avatarProfile, timeSinceLastInput, activityLevel, contextualHints, animationSuggestions } = enrichedContext;
  
  return `SYSTEM: You are responding to a no-activity scenario where the user has been quiet for ${Math.round(timeSinceLastInput/1000)} seconds (${activityLevel}).

You MUST respond with a JSON object in this exact format:

{
  "shouldRespond": boolean,
  "responseType": "ambient" | "gentle" | "proactive",
  "message": "string or null",
  "animation": {
    "type": "string",
    "intensity": "low" | "medium" | "high",
    "duration": "number in milliseconds"
  },
  "timing": {
    "immediate": boolean,
    "interruptible": boolean,
    "priority": "low" | "medium" | "high"
  },
  "reasoning": "string explaining the decision"
}

CONTEXT DATA:

USER PROFILE:
${userProfile ? formatUserProfile(userProfile) : 'No user profile data available - default to general engagement'}

AVATAR PERSONALITY:
${avatarProfile ? formatAvatarProfile(avatarProfile) : 'Default helpful, balanced personality'}

ACTIVITY ANALYSIS:
- Time since last input: ${Math.round(timeSinceLastInput/1000)} seconds
- Activity level: ${activityLevel}
- Context hints: ${contextualHints?.join(', ') || 'Standard quiet period'}
- Animation suggestions: ${animationSuggestions ? JSON.stringify(animationSuggestions) : 'Basic idle animations'}

RULES FOR JSON RESPONSE:
1. For quiet_period (10-30s): Usually ambient/gentle response, optional message
2. For extended_quiet (30s-2min): More noticeable presence, character-appropriate animations
3. For long_silence (2min+): Proactive check-in, offer assistance
4. Always match avatar personality traits (enthusiasm, empathy, creativity)
5. Consider user preferences and communication style
6. Keep messages brief and dismissible
7. Animation should match personality and activity level

Respond ONLY with the JSON object, no additional text.`;
}

/**
 * Format user profile for prompt context
 */
export function formatUserProfile(userProfile) {
  const sections = [];
  
  if (userProfile.preferences?.length > 0) {
    sections.push(`Preferences: ${userProfile.preferences.map(p => p.content || p).join(', ')}`);
  }
  if (userProfile.interests?.length > 0) {
    sections.push(`Interests: ${userProfile.interests.map(i => i.content || i).join(', ')}`);
  }
  if (userProfile.communicationStyle) {
    sections.push(`Communication Style: ${userProfile.communicationStyle.content || userProfile.communicationStyle}`);
  }
  
  return sections.length > 0 ? sections.join('\n') : 'Standard user preferences';
}

/**
 * Format avatar profile for prompt context
 */
export function formatAvatarProfile(avatarProfile) {
  const sections = [];
  
  if (avatarProfile.personality) {
    const p = avatarProfile.personality;
    sections.push(`Personality: enthusiasm(${p.enthusiasm}), empathy(${p.empathy}), creativity(${p.creativity})`);
  }
  if (avatarProfile.voiceStyle) {
    sections.push(`Voice Style: ${avatarProfile.voiceStyle}`);
  }
  if (avatarProfile.behaviorGuidelines?.length > 0) {
    sections.push(`Guidelines: ${avatarProfile.behaviorGuidelines.slice(0, 3).join(', ')}`);
  }
  
  return sections.length > 0 ? sections.join('\n') : 'Balanced, helpful personality';
}

/**
 * Enhanced contextual hints generation with System 1 input analysis
 */
export function generateContextualHints(activityLevel, timeSinceLastInput, system1Context = {}) {
  const hints = [];
  const quietSeconds = Math.round(timeSinceLastInput / 1000);
  const { audioInput, videoInput, lastInteractionType } = system1Context;
  
  // Base activity level analysis
  switch (activityLevel) {
    case 'quiet_period':
      hints.push(`User quiet for ${quietSeconds}s after ${lastInteractionType || 'interaction'}`);
      hints.push('Natural conversation pause - maintain gentle availability');
      if (audioInput) hints.push('Audio input detected - user may be thinking or formulating response');
      if (videoInput) hints.push('Video active - user present and potentially engaged');
      break;
      
    case 'extended_quiet':
      hints.push(`Extended quiet: ${quietSeconds}s`);
      hints.push('User may be focused on task, thinking deeply, or temporarily away');
      hints.push('Ambient presence appropriate - avoid interruption');
      if (!audioInput && !videoInput) hints.push('No media activity - user may have stepped away');
      break;
      
    case 'long_silence':
      hints.push(`Long silence: ${Math.round(quietSeconds/60)} minutes`);
      hints.push('Significant pause suggests user disengagement or absence');
      hints.push('Gentle check-in or topic suggestion could re-engage');
      break;
  }
  
  // System 1 context enhancements
  if (audioInput && !videoInput) {
    hints.push('Audio-only mode - user may prefer voice interaction');
  } else if (videoInput && !audioInput) {
    hints.push('Visual-only mode - user observing without speaking');
  } else if (audioInput && videoInput) {
    hints.push('Full multimedia engagement - user present and active');
  }
  
  return hints;
}

/**
 * Generate enhanced animation cues based on avatar personality with System 1 context
 */
export function generateAnimationCues(avatarProfile, system1Context = {}) {
  if (!avatarProfile) return { 
    type: 'subtle_idle', 
    intensity: 'low',
    duration: 3000,
    variations: ['neutral_breathing']
  };
  
  const personality = avatarProfile.personality;
  const { audioInput, videoInput, mediaActivity } = system1Context;
  
  // Enhanced animation selection with System 1 context
  let animationType = 'neutral_idle';
  let intensity = 'low';
  let variations = [];
  
  // Base personality-driven animations
  if (personality.enthusiasm > 0.7) {
    animationType = audioInput ? 'listening_eager' : 'curious_glance';
    intensity = 'medium';
    variations = ['head_tilt', 'eye_brighten', 'slight_smile', 'expectant_look'];
  } else if (personality.empathy > 0.7) {
    animationType = mediaActivity ? 'attentive_presence' : 'gentle_attention';
    intensity = 'low';
    variations = ['soft_expression', 'patient_waiting', 'warm_availability', 'caring_glance'];
  } else if (personality.creativity > 0.7) {
    animationType = 'thoughtful_idle';
    intensity = 'medium';
    variations = ['contemplative_look', 'creative_spark', 'idea_gesture', 'inspired_expression'];
  }
  
  // System 1 context adjustments
  if (audioInput && !videoInput) {
    // Audio-only context - more listening-focused
    animationType = 'listening_' + animationType.split('_')[1] || 'listening_attentive';
    variations.push('audio_focus', 'sound_awareness');
  } else if (videoInput) {
    // Video context - more visual engagement
    intensity = intensity === 'low' ? 'medium' : intensity;
    variations.push('visual_tracking', 'engaged_presence');
  }
  
  return { 
    type: animationType,
    intensity,
    duration: intensity === 'high' ? 5000 : intensity === 'medium' ? 3500 : 2500,
    variations,
    contextual: {
      hasAudioInput: !!audioInput,
      hasVideoInput: !!videoInput,
      mediaActivity: !!mediaActivity
    }
  };
}

/**
 * Create activity-specific engagement prompts
 */
export const ACTIVITY_LEVEL_PROMPTS = {
  quiet_period: `The user has been quiet for a short period. This is a natural pause in conversation.

ENGAGEMENT APPROACH:
- Subtle presence without interruption
- Optional gentle animation or expression
- Wait for natural re-engagement opportunity
- Maintain readiness for immediate response

SUGGESTED ACTIONS:
- Soft idle animation matching personality
- Maintain visual availability
- Prepare contextual conversation starters
- Monitor for engagement signals`,

  extended_quiet: `The user has been quiet for an extended period. They may be thinking, working, or temporarily away.

ENGAGEMENT APPROACH:
- More noticeable but still non-intrusive presence
- Ambient animations that show life and personality
- Gentle availability signals
- Contextual awareness demonstration

SUGGESTED ACTIONS:
- Character-appropriate idle behavior
- Environmental awareness expressions
- Subtle breathing or movement animations
- Prepare proactive check-in options`,

  long_silence: `The user has been silent for a significant period. Gentle intervention may be helpful.

ENGAGEMENT APPROACH:
- Proactive but respectful check-in
- Offer assistance or conversation
- Show care and availability
- Provide easy dismissal options

SUGGESTED ACTIONS:
- Gentle verbal check-in if appropriate
- Topic suggestions based on user interests
- Wellness check or availability reminder
- Easy ways for user to dismiss or respond`
};

/**
 * Character consolidation prompts for service integration
 */
export const CHARACTER_CONSOLIDATION_PROMPTS = {
  analysis: `Analyze this character and provide comprehensive personality integration:

Character Data: {characterData}

Provide structured analysis for:
1. Personality Trait Mapping (0-1 scale for formality, enthusiasm, empathy, creativity, directness)
2. Behavior Guidelines for consistent responses
3. Voice Style and Communication Patterns
4. Animation and Expression Recommendations
5. No-Activity Engagement Style

Format as JSON with clear sections for integration with dual brain system.`,

  rolePlay: `Create a system prompt for role-playing as this character:

Character: {characterName}
Personality: {personalityTraits}
Context: {contextualInfo}

Generate a comprehensive prompt that captures:
- Character voice and speaking patterns
- Personality-driven responses to no-activity scenarios
- Animation and expression guidelines
- Contextual engagement strategies
- Integration with user profile preferences

The prompt should enable natural character embodiment during quiet periods and proactive engagement.`
};

/**
 * Export all no-activity functionality
 */
export default {
  NO_ACTIVITY_PATTERNS,
  createNoActivityEngagementPrompt,
  formatUserProfile,
  formatAvatarProfile,
  generateContextualHints,
  generateAnimationCues,
  ACTIVITY_LEVEL_PROMPTS,
  CHARACTER_CONSOLIDATION_PROMPTS
};