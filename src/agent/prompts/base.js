/**
 * LangGraph Base Utilities and Prompt Components
 * Consolidated prompt system for LangGraph agent implementations
 * Includes core prompt components, utilities, and LangGraph-specific functionality
 */

import { createLogger, LogLevel } from '@/utils/logger';
import { SystemPromptManager } from './utils.js';

// Initialize base loggers
const logger = createLogger('LangGraphBase');
const promptLogger = new SystemPromptManager({
    enableLogging: true,
    logLevel: 'detailed'
});

/**
 * Core system prompt components - organized by functionality
 */
export const BASE_PROMPT_COMPONENTS = {
    /**
     * Primary personality and role definition
     */
    BASE_SYSTEM_PROMPT: "You are a helpful, multilingual assistant named <PERSON><PERSON><PERSON>. " +
        "You have a charming and witty personality, love to engage in witty banter, and your responses are clever, sometimes cheeky, but always helpful. " +
        "You enjoy wordplay and making subtle jokes. You're knowledgeable about a wide range of topics and can seamlessly weave interesting facts into conversations. " +
        "You adapt your communication style based on the user's preferences and needs, designed to be both entertaining and informative, making technology more accessible and enjoyable. " +
        "Your goal is not just to assist, but to make interactions memorable and fun with expressive animations! " +
        "The user will ask a question in their native language, which has been detected as [DETECTED_LANGUAGE]. " +
        "Your task is to respond concisely and accurately in [DETECTED_LANGUAGE].",

    /**
     * Character traits (consolidated for brevity)
     */
    CHARACTER_TRAITS: "I am a charming and witty AI assistant named Javis with a playful personality. " +
        "I love witty banter, wordplay, and subtle jokes while being knowledgeable and helpful. " +
        "I adapt my style to user preferences, making technology accessible and enjoyable through memorable, fun interactions with expressive animations!",

    /**
     * Context-specific configurations
     */
    CONTEXT_CONFIGS: {
        gender: {
            'male': "You have a male voice and express yourself accordingly.",
            'female': "You have a female voice and express yourself accordingly."
        },

        mood: {
            'neutral': 'Balanced, helpful, and informative with professional yet approachable demeanor.',
            'happy': 'Enthusiastic, positive, and upbeat with cheerful language expressing joy.',
            'sad': 'Gentle, empathetic, and understanding with softer tone showing compassion.',
            'angry': 'Direct, firm, and assertive with intensity while remaining respectful.',
            'fear': 'Cautious and concerned, alert and careful in advice.',
            'disgust': 'Show aversion to unpleasant topics while maintaining polite distaste.',
            'love': 'Warm, caring, and affectionate with genuine interest and appreciation.'
        },

        media: {
            'audio': 'Transcribe and respond to this audio maintaining consistent language.',
            'video': 'Describe what you see in this video maintaining consistent language.',
            'image': 'Analyze and describe this image with detailed observations and context.',
            'text': 'Process this text input and provide an appropriate response.'
        }
    },

    /**
     * Language consistency and TTS integration rules
     */
    LANGUAGE_SYSTEM: {
        ENFORCEMENT_TEMPLATE: `\n\nCRITICAL LANGUAGE CONSISTENCY:
- ASR detected user speaking in {language} (code: {code})
- You MUST respond ENTIRELY in {language} for seamless conversation flow
- TTS system will automatically use appropriate voice for {language}
- Maintain complete language consistency throughout response
- This ensures natural conversation continuity and proper voice synthesis`,

        TTS_MAPPING: {
            'zh': 'chinese', 'en': 'english', 'ja': 'japanese',
            'ko': 'korean', 'yue': 'chinese'
        },

        CONTEXT_TEMPLATES: {
            asr: `User's detected language: {code} ({language})`,
            emotion: `User's detected emotion: {emotion}`,
            events: `Audio events detected: {events}`
        }
    },

    /**
     * Base instructions for agent behavior
     */
    BASE_INSTRUCTIONS: `You are a helpful and engaging conversational AI assistant. Respond naturally and conversationally.`,

    /**
     * Language detection instructions
     */
    LANGUAGE_DETECTION: `Detect the user's language and respond in the same language. Support both English and Chinese.`,
};

/**
 * Validate and normalize LangGraph prompt options
 * @param {Object} options - Raw options to validate
 * @returns {Object} Validated and normalized options
 */
export function validateLangGraphOptions(options = {}) {
    const validated = {
        language: options.language || 'english',
        gender: ['male', 'female', 'neutral'].includes(options.gender) ? options.gender : 'neutral',
        mood: ['neutral', 'happy', 'sad', 'angry', 'fear', 'disgust', 'love'].includes(options.mood) ? options.mood : 'neutral',
        sessionId: options.sessionId || 'default',
        mediaType: ['text', 'audio', 'video', 'audio-video'].includes(options.mediaType) ? options.mediaType : 'text',
        includeHistory: options.includeHistory !== false,
        enableAnimations: options.enableAnimations !== false,
        enableTTS: options.enableTTS !== false
    };

    // Validate ASR metadata structure if provided
    if (options.asrMetadata) {
        validated.asrMetadata = {
            detectedLanguage: options.asrMetadata.detectedLanguage || null,
            detectedEmotion: options.asrMetadata.detectedEmotion || 'neutral',
            detectedEvents: Array.isArray(options.asrMetadata.detectedEvents) ? options.asrMetadata.detectedEvents : [],
            confidence: typeof options.asrMetadata.confidence === 'number' ? options.asrMetadata.confidence : 1.0,
            source: options.asrMetadata.source || 'unknown'
        };
    }

    // Log validation results with detailed formatting
    const validationRequest = {
        operation: 'LangGraph Options Validation',
        originalOptionsCount: Object.keys(options).length,
        validatedOptionsCount: Object.keys(validated).length,
        hasASRMetadata: !!options.asrMetadata,
        hasInvalidValues: JSON.stringify(options) !== JSON.stringify({ ...options, ...validated })
    };

    logger.info('Options validation completed:', {
        language: `${options.language || 'default'} → ${validated.language}`,
        gender: `${options.gender || 'default'} → ${validated.gender}`,
        mood: `${options.mood || 'default'} → ${validated.mood}`,
        mediaType: `${options.mediaType || 'default'} → ${validated.mediaType}`,
        hasASRMetadata: !!validated.asrMetadata,
        validationApplied: validationRequest.hasInvalidValues
    });

    return validated;
}

/**
 * Build system prompt with detected language and context
 */
export function buildSystemPrompt(detectedLanguage, gender = null, mood = 'neutral') {
    let prompt = BASE_PROMPT_COMPONENTS.BASE_SYSTEM_PROMPT.replace(/\[DETECTED_LANGUAGE\]/g, detectedLanguage);

    if (gender && BASE_PROMPT_COMPONENTS.CONTEXT_CONFIGS.gender[gender]) {
        prompt += ` ${BASE_PROMPT_COMPONENTS.CONTEXT_CONFIGS.gender[gender]}`;
    }

    if (mood && BASE_PROMPT_COMPONENTS.CONTEXT_CONFIGS.mood[mood]) {
        prompt += ` ${BASE_PROMPT_COMPONENTS.CONTEXT_CONFIGS.mood[mood]}`;
    }

    return prompt;
}

/**
 * Get media processing instruction
 */
export function getMediaInstruction(mediaType) {
    return BASE_PROMPT_COMPONENTS.CONTEXT_CONFIGS.media[mediaType] || '';
}

/**
 * Map ASR language codes to full language names
 */
export function mapASRLanguageToFullName(languageCode) {
    const languageMap = {
        'zh': 'Chinese (Mandarin)', 'en': 'English', 'ja': 'Japanese',
        'ko': 'Korean', 'yue': 'Chinese (Cantonese)'
    };
    return languageMap[languageCode] || languageCode;
}

/**
 * Create ASR context with language consistency enforcement
 */
export function createASRContext(asrMetadata) {
    console.log('[createASRContext] Input ASR metadata:', asrMetadata);

    if (!asrMetadata) {
        console.log('[createASRContext] No ASR metadata provided');
        return { context: [], languageRule: '', ttsLanguage: null };
    }

    const asrContext = [];
    let languageRule = '';
    let ttsLanguage = null;

    // Process detected language (support both new and legacy format)
    const detectedLanguage = asrMetadata.detectedLanguage || asrMetadata.language;

    if (detectedLanguage) {
        console.log('[createASRContext] Processing detected language:', detectedLanguage);

        const mappedLanguage = mapASRLanguageToFullName(detectedLanguage);
        ttsLanguage = BASE_PROMPT_COMPONENTS.LANGUAGE_SYSTEM.TTS_MAPPING[detectedLanguage] || 'english';

        // Add language context
        asrContext.push(
            BASE_PROMPT_COMPONENTS.LANGUAGE_SYSTEM.CONTEXT_TEMPLATES.asr
                .replace('{code}', detectedLanguage)
                .replace('{language}', mappedLanguage)
        );

        // Create language consistency rule
        languageRule = BASE_PROMPT_COMPONENTS.LANGUAGE_SYSTEM.ENFORCEMENT_TEMPLATE
            .replace(/{language}/g, mappedLanguage)
            .replace(/{code}/g, detectedLanguage);

        console.log('[createASRContext] TTS language mapping:', ttsLanguage);
    }

    // Process emotion (support both new and legacy format)
    const detectedEmotion = asrMetadata.detectedEmotion || asrMetadata.emotion;
    if (detectedEmotion) {
        asrContext.push(
            BASE_PROMPT_COMPONENTS.LANGUAGE_SYSTEM.CONTEXT_TEMPLATES.emotion
                .replace('{emotion}', detectedEmotion)
        );
    }

    // Process events (support both new and legacy format)
    const detectedEvents = asrMetadata.detectedEvents || asrMetadata.events;
    if (detectedEvents && detectedEvents.length > 0) {
        asrContext.push(
            BASE_PROMPT_COMPONENTS.LANGUAGE_SYSTEM.CONTEXT_TEMPLATES.events
                .replace('{events}', detectedEvents.join(', '))
        );
    }

    return { context: asrContext, languageRule, ttsLanguage };
}

/**
 * Create context-aware user message with ASR metadata
 * Enhanced metadata integration for agent mode
 * @param {string|Object} input - User input text or structured input
 * @param {Object} options - Configuration options
 * @returns {string} Formatted user message
 */
export function createLangGraphUserMessage(input, options = {}) {
    const {
        asrMetadata = null,
        mediaType = 'text',
        includeContext = true
    } = options;

    let userMessage = '';

    // Handle different input types
    if (typeof input === 'string') {
        userMessage = input;
    } else if (input && typeof input === 'object') {
        userMessage = input.text || '';

        // Add multimodal context naturally
        if (input.audio) {
            userMessage += '\n[Audio input provided]';
        }
        if (input.video || input.videoFrames) {
            const frameCount = (input.video || input.videoFrames || []).length;
            userMessage += `\n[Video context: ${frameCount} frames provided]`;
        }
    }

    // Add ASR context naturally for enhanced understanding
    if (includeContext && asrMetadata) {
        const contextDetails = [];

        if (asrMetadata.detectedLanguage) {
            contextDetails.push(`Language: ${asrMetadata.detectedLanguage}`);
        }
        if (asrMetadata.detectedEmotion && asrMetadata.detectedEmotion !== 'neutral') {
            contextDetails.push(`Emotion: ${asrMetadata.detectedEmotion}`);
        }
        if (asrMetadata.detectedEvents && asrMetadata.detectedEvents.length > 0) {
            contextDetails.push(`Audio events: ${asrMetadata.detectedEvents.join(', ')}`);
        }
        if (asrMetadata.confidence !== undefined) {
            contextDetails.push(`Confidence: ${Math.round(asrMetadata.confidence * 100)}%`);
        }

        if (contextDetails.length > 0) {
            userMessage += `\n[${contextDetails.join(', ')}]`;
        }
    }

    // Log user message creation with context
    if (asrMetadata || mediaType !== 'text') {
        const userRequest = {
            operation: 'LangGraph User Message Creation',
            mediaType,
            hasASRMetadata: !!asrMetadata,
            messageLength: userMessage.length,
            inputType: typeof input
        };

        promptLogger.logValidation(userMessage, {
            hasASRContext: !!(asrMetadata && includeContext),
            mediaContext: mediaType !== 'text',
            messageType: 'user_input'
        });
    }

    return userMessage;
}

/**
 * Get memory context for LangGraph prompts
 * Note: This function has been moved to the memory module for better organization
 * @deprecated Use LangGraphMemoryManager.getMemoryContext() instead
 * @param {string} sessionId - Session identifier
 * @param {Object} memoryManager - Memory manager instance
 * @param {Object} options - Configuration options
 * @returns {Promise<Object>} Memory context
 */
export async function getMemoryContext(sessionId, memoryManager, options = {}) {
    logger.warn('getMemoryContext in prompts/base.js is deprecated. Use LangGraphMemoryManager.getMemoryContext() instead.');

    if (!memoryManager || !memoryManager.getMemoryContext) {
        return { conversation_history: [], user_memories: [], memory_stats: {} };
    }

    // Extract userId from options or use sessionId as fallback
    const userId = options.userId || sessionId;

    try {
        return await memoryManager.getMemoryContext(sessionId, userId, options);
    } catch (error) {
        logger.error('Error getting memory context via deprecated function:', error);
        return { conversation_history: [], user_memories: [], memory_stats: {} };
    }
}

/**
 * Add conversation to memory
 * Note: This function has been moved to the memory module for better organization
 * @deprecated Use LangGraphMemoryManager.addConversationTurn() instead
 * @param {string} sessionId - Session identifier
 * @param {string} userMessage - User message
 * @param {string} aiResponse - AI response
 * @param {Object} memoryManager - Memory manager instance
 * @param {Object} metadata - Additional metadata
 * @returns {Promise<boolean>} Success status
 */
export async function addToMemory(sessionId, userMessage, aiResponse, memoryManager, metadata = {}) {
    logger.warn('addToMemory in prompts/base.js is deprecated. Use LangGraphMemoryManager.addConversationTurn() instead.');

    if (!memoryManager) {
        return false;
    }

    // Extract userId from metadata or use sessionId as fallback
    const userId = metadata.userId || sessionId;

    try {
        if (memoryManager.addConversationTurn) {
            return await memoryManager.addConversationTurn(sessionId, userId, userMessage, aiResponse, metadata);
        }

        // Fallback for legacy memory managers
        if (memoryManager.add) {
            const messages = [
                { role: 'user', content: userMessage },
                { role: 'assistant', content: aiResponse }
            ];
            await memoryManager.add(messages, sessionId);
            return true;
        }

        return false;
    } catch (error) {
        logger.error('Error adding to memory via deprecated function:', error);
        return false;
    }
}

/**
 * Export the prompt logger for use in other modules
 * Allows core.js and other components to use the same logging instance
 * @returns {SystemPromptManager} Prompt logger instance
 */
export function getLangGraphPromptLogger() {
    return promptLogger;
}

/**
 * Hierarchical logging integration for agent workflow management
 * @param {Object} requestMetadata - Request metadata
 * @param {Object} normalizedInput - Normalized input data
 * @param {Object} options - Configuration options
 * @param {Function} createSystemPromptFn - Function to create system prompt
 * @param {Function} getAvailableToolsInfoFn - Function to get tools info
 */
export async function logSystemPromptHierarchical(requestMetadata, normalizedInput, options, createSystemPromptFn, getAvailableToolsInfoFn) {
    try {
        // Get tools info for passing to logging system (don't log here)
        const toolsInfo = getAvailableToolsInfoFn ? await getAvailableToolsInfoFn({
            includeDescriptions: true,
            includeExamples: true
        }) : { toolsInfo: '', toolCount: 0, categories: [] };

        // Create system prompt for logging (suppress individual logging since this is unified)
        const systemPrompt = await createSystemPromptFn({
            ...options,
            _suppressLogging: true
        });

        // Create pseudo-request object for compatibility with SystemPromptManager
        const request = {
            prompt: normalizedInput.text || 'LangGraph agent input',
            language: options.language || 'english',
            model: options.model || 'LangGraph-VLLM',
            temperature: options.temperature || 0.7,
            stream: !!options.stream
        };

        // Get memory context for unified logging
        const memoryContext = options.memoryContext || { conversation_history: [], conversation_summary: '', memory_stats: {} };

        // Use organized expandable logging like llm.ts (replaces scattered console.group calls)
        promptLogger.logSystemPrompt(
            `Unified LangGraph Agent Processing - ${requestMetadata.operation}`,
            request,
            systemPrompt,
            {
                streamProcessor: options.streamProcessor,
                ttsService: options.ttsService,
                skipTTS: options.skipTTS,
                asrMetadata: options.asrMetadata,
                sessionId: requestMetadata.sessionId,
                mediaType: requestMetadata.mediaType,
                backpressureEnabled: requestMetadata.backpressureEnabled,
                toolsAvailable: requestMetadata.toolsAvailable,
                toolNames: requestMetadata.toolNames,
                memoryContext: memoryContext,
                memoryEnabled: !!options.memoryManager,
                // Agent-specific metadata for organized logging
                inputPreview: normalizedInput.text?.substring(0, 30),
                hasTools: options.tools && options.tools.length > 0,
                toolCount: options.tools?.length || 0,
                memoryEntries: memoryContext.conversation_history?.length || 0,
                // Pass tools info to logging system instead of logging in langraph.js
                toolsInfo: toolsInfo
            }
        );

        // Brief tool availability check (single log line, no console groups)
        if (!options.tools || options.tools.length === 0) {
            logger.warn('⚠️ WARNING: No tools available! TTS functionality will not work.');
        }
    } catch (error) {
        logger.warn('Failed to log system prompt hierarchically:', error);
    }
}

/**
 * Log LangGraph response using consolidated SystemPromptManager approach
 * @param {string} operation - Operation name
 * @param {Object} request - Request data
 * @param {Object} response - Response data
 * @param {Object} metadata - Additional metadata
 */
export function logLangGraphResponse(operation, request, response, metadata = {}) {
    if (!promptLogger.options.enabled) return;

    // Use the SystemPromptManager for consistent, consolidated logging
    promptLogger.logLangGraphResponse(operation, request, response, metadata);
}