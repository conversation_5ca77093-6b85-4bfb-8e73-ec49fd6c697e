/**
 * Universal LangGraph Agent Service
 * Provider-agnostic, config-agnostic core agent service
 * Follows enterprise-grade patterns with dependency injection and service abstraction
 */

// BROWSER COMPATIBILITY FIX: Using polyfill for AsyncLocalStorage in browser environment
// Vite alias configured to redirect node:async_hooks to browser-compatible polyfill

import { createReactAgent } from "@langchain/langgraph/prebuilt";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { createLogger, LogLevel } from '@/utils/logger';
import { createModelProvider } from './models/index.js';
import { autoRegisterTools } from './tools/index.js';
import {
    validateLangGraphOptions,
    getLangGraphPromptLogger,
} from './prompts/base.js';
import { ConnectionManager } from './services/connection/ConnectionManager.js';
// ContextualService removed - functionality moved to dualbrain/interfaces/System2PeriodicAnalysis.js
import { sendRealtimeAudio, waitForRealtimeSessionReady } from '../media/modality/audio.ts';

import { initializeAgent } from './init.js';
import { getInfrastructureManager } from './services/infrastructure/InfrastructureManager.js';

// Import centralized configuration
import {
    getCompleteAgentConfig,
    getAgentConfig,
    getStreamingConfig,
    getPerformanceConfig,
    validateAgentConfig
} from './config/AgentConfig.js';

// Provider configuration (consolidated single source of truth)
import {
    createProviderConfig,
    validateProviderConfig,
    ProviderConfigFactories,
    autoDetectProvider
} from './config/ProviderConfig.js';
// Use canonical ModelFactory as the universal factory
import { ModelFactory as UniversalModelFactory } from './models/ModelFactory.js';
import {
    UniversalPromptCreator,
    createPromptCreator,
    createPromptFunction
} from './interfaces/PromptCreator.js';
import {
    UniversalRealtimeInterface,
    createUniversalRealtimeInterface,
    UniversalRealtimeStates,
    UniversalRealtimeEvents
} from './interfaces/RealtimeInterface.js';


/**
 * Universal LangGraph Agent Service - Provider-Agnostic Implementation
 * 
 * Enterprise-grade patterns:
 * - Provider abstraction through dependency injection
 * - Configuration injection with validation
 * - Service-oriented architecture with loose coupling
 * - Universal dual brain coordination for any LLM provider
 * - LangGraph-native patterns with enhanced reliability
 */

// Create logger with DEBUG level for enhanced error diagnosis
const logger = createLogger('LangGraphAgent', LogLevel.DEBUG);
logger.debug('🔍 [ConnectionDebug] LangGraphAgent logger initialized with DEBUG level');

// Use the centralized state annotation from AgentStateDefinitions.js
// This eliminates duplication and ensures consistency across the application

/**
 * Core LangGraph Agent Service - Generic state management with streaming
 */
export class LangGraphAgentService {
    constructor(options = {}) {
        this.logger = createLogger('LangGraphAgent');
        this.logger.setLogLevel(LogLevel.DEBUG);
        this.logger.debug('🔍 Creating Universal LangGraph Agent Service with provider abstraction');

        // Use centralized configuration with overrides
        const centralizedConfig = getCompleteAgentConfig({
            provider: options.provider || options.modelProvider || 'aliyun',
            applicationType: options.applicationType || 'avatar',
            agentOverrides: options.agentConfig || {},
            streamingOverrides: options.streamingConfig || {},
            performanceOverrides: options.performanceConfig || {},
            providerOverrides: options.providerConfig || {},
            applicationOverrides: options.applicationConfig || {}
        });

        // Validate configuration
        const validation = validateAgentConfig(centralizedConfig);
        if (!validation.isValid) {
            this.logger.warn('Configuration validation issues:', validation.errors);
        }

        this.options = { ...centralizedConfig.agent, ...options };
        this.centralizedConfig = centralizedConfig;

        // Initialize universal provider configuration
        this.providerConfig = this._initializeProviderConfig(options);

        // Initialize universal prompt creator with provider-aware configuration
        this.promptCreator = this._initializePromptCreator(options);

        // Dependency injection for system prompt creation (backward compatibility)
        this.createSystemPrompt = options.createSystemPrompt || this.promptCreator.createSystemPrompt.bind(this.promptCreator);
        this.getAvailableToolsInfo = options.getAvailableToolsInfo || null;

        // Tool choice configuration (configurable instead of hardcoded)
        this.toolChoiceStrategy = options.toolChoiceStrategy || 'auto';

        // Initialize prompt logger from LangGraph module for formatted logging
        this.promptLogger = getLangGraphPromptLogger();

        // Core configuration from provider config
        const modelConfig = this.providerConfig.getModelConfig();
        this.temperature = modelConfig.temperature;
        this.maxTokens = modelConfig.maxTokens;

        // Store minimal services - tools are self-sufficient
        this._baseServices = {
            onStateChange: options.onStateChange || (() => { }),
            onConversationUpdate: options.onConversationUpdate || (() => { }),
            ...options.services
        };
        this.services = { ...this._baseServices };

        // Universal model factory (provider-agnostic)
        this._modelFactory = null;

        // Shared infrastructure manager (eliminates redundancy with DualBrainCoordinator)
        this.infrastructureManager = getInfrastructureManager({
            performanceTracker: options.performanceTracker,
            circuitBreaker: options.circuitBreaker,
            cacheManager: options.cacheManager,
            performanceCoordinator: options.performanceCoordinator
        });

        // Initialize agent initialization service with proper configuration
        this.initializationOptions = {
            ...options,
            // Include agentConfig so dual brain can be properly initialized
            enableDualBrain: options.agentConfig?.enableDualBrain,
            agentConfig: options.agentConfig,
            provider: this.providerConfig.config.provider,
            modelProvider: this.providerConfig.config.provider
        };

        // Initialize basic coordination services - provider-agnostic
        // UI coordination removed - handled directly by components  
        this.connectionCoordination = null;

        // Initialize memory and workflow
        this.memoryManager = null; // Will be initialized in _initializeMemoryManager
        this.checkpointer = null; // Will be set from memoryManager.getCheckpointer()
        this.store = null; // Will be set from memoryManager.getStore()
        this.agent = null;
        this.model = null;
        this.tools = [];

        // Universal realtime interface
        this.realtimeInterface = null;

        // Native LangGraph streaming manager
        this.streamingManager = null;

        // Direct media integration - no bridge needed

        // Unified state coordination callbacks - no more competing state managers
        this.coordinationCallbacks = {
            onUIStateChange: options.onUIStateChange || (() => { }),
            onConnectionStateChange: options.onConnectionStateChange || (() => { }),
            onContextualUpdate: options.onContextualUpdate || (() => { }),
            onProcessingStart: options.onProcessingStart || (() => { }),
            onProcessingEnd: options.onProcessingEnd || (() => { })
        };

        // Memory management - use centralized configuration
        const memoryConfig = this.centralizedConfig?.agent || {};
        this.memoryOptions = {
            maxHistoryLength: memoryConfig.maxHistoryLength || 10,
            summarizeAfter: memoryConfig.summarizeAfter || 20,
            enableSummary: memoryConfig.enableSummary !== false
        };

        this._initialized = false;
        this.logger.info(`Universal LangGraph Agent Service created for provider: ${this.providerConfig.config.provider}`);

        // Generic callbacks for external integrations
        this.callbacks = {
            onTextChunk: options.onTextChunk || (() => { }),
            onToolCall: options.onToolCall || (() => { }),
            onProcessingStart: options.onProcessingStart || (() => { }),
            onProcessingEnd: options.onProcessingEnd || (() => { }),
            onError: options.onError || (() => { }),
            // CRITICAL FIX: Add onModeChange callback for state manager integration
            onModeChange: options.onModeChange || (() => { })
        };
    }

    /**
     * Initialize universal provider configuration with validation
     * @private
     */
    _initializeProviderConfig(options) {
        this.logger.debug('🔧 Initializing universal provider configuration...');

        // Auto-detect provider if not specified  
        const detectedProvider = autoDetectProvider(options);
        const provider = options.provider || options.modelProvider || detectedProvider;

        this.logger.debug(`Provider detected/specified: ${provider}`);

        // Create provider-specific configuration (raw consolidated object)
        let rawProviderConfig;

        switch (provider.toLowerCase()) {
            case 'aliyun':
                rawProviderConfig = ProviderConfigFactories.aliyun(options);
                break;
            case 'vllm':
                rawProviderConfig = ProviderConfigFactories.vllm(options);
                break;
            case 'openai':
                rawProviderConfig = ProviderConfigFactories.openai(options);
                break;
            default:
                // Generic provider configuration
                rawProviderConfig = createProviderConfig(provider, options);
        }

        // Validate configuration
        validateProviderConfig(rawProviderConfig);

        // Adapt consolidated config to legacy interface expected by core
        const providerConfig = this._createProviderConfigAdapter(rawProviderConfig);

        this.logger.info(`✅ Provider configuration initialized: ${provider}`, {
            hasAuth: !!providerConfig.getAuthConfig().apiKey,
            hasEndpoint: !!providerConfig.getConnectionConfig().endpoint,
            streamingEnabled: providerConfig.getStreamingConfig().enabled,
            realtimeEnabled: providerConfig.getRealtimeConfig().enabled
        });

        return providerConfig;
    }

    /**
     * Create an adapter around consolidated provider config to match legacy interface
     * @private
     */
    _createProviderConfigAdapter(raw) {
        const safe = (v, d) => (v === undefined || v === null ? d : v);
        const features = raw.features || {};
        const defaults = raw.defaultParameters || {};
        const audio = raw.audioConfig || {};
        return {
            config: { provider: raw.provider },
            getAuthConfig() {
                return { apiKey: safe(raw.apiKey, ''), apiSecret: safe(raw.apiSecret, '') };
            },
            getConnectionConfig() {
                return {
                    endpoint: safe(raw.httpEndpoint, ''),
                    httpEndpoint: safe(raw.httpEndpoint, ''),
                    websocketEndpoint: safe(raw.websocketEndpoint, ''),
                    realtimeEndpoint: safe(raw.realtimeEndpoint, '')
                };
            },
            getStreamingConfig() {
                return {
                    enabled: !!features.streaming,
                    chunkSize: 50,
                    bufferSize: 1024
                };
            },
            getRealtimeConfig() {
                return {
                    enabled: !!features.realtime,
                    sampleRate: safe(audio.sampleRate, 16000),
                    format: safe(audio.format, 'pcm16')
                };
            },
            getDebugConfig() {
                return { enabled: false };
            },
            getToolsConfig() {
                return { enabled: !!features.toolCalling };
            },
            getModelConfig() {
                return {
                    name: safe(raw.defaultHttpModel || raw.defaultRealtimeModel || raw.defaultWebSocketModel, ''),
                    temperature: safe(defaults.temperature, 0.7),
                    maxTokens: safe(defaults.maxTokens, 2000),
                    topP: safe(defaults.topP, 0.8),
                    timeout: safe(raw.timeout, 30000)
                };
            },
            getProviderSpecificConfig() {
                return {};
            }
        };
    }

    /**
     * Initialize universal prompt creator with provider-aware configuration
     * @private
     */
    _initializePromptCreator(options) {
        this.logger.debug('📝 Initializing universal prompt creator...');

        // Determine application type for prompt creation
        const applicationType = options.applicationType || this._detectApplicationType(options);

        // Create prompt configuration with provider context
        const promptConfig = {
            // Provider context
            provider: this.providerConfig.config.provider,
            modelCapabilities: this._getProviderCapabilities(),

            // Application context
            applicationName: options.applicationName || 'Universal AI Agent',
            assistantName: options.assistantName || 'Javis',
            assistantRole: options.assistantRole,

            // Prompt behavior
            includeToolInfo: options.includeToolInfo !== false,
            includeMemoryInfo: options.includeMemoryInfo !== false,
            includeCapabilities: options.includeCapabilities !== false,

            // Communication settings
            communicationStyle: options.communicationStyle || 'professional',
            responseLength: options.responseLength || 'concise',
            languageSupport: options.languageSupport || ['english'],

            // Custom sections
            customInstructions: options.customInstructions || '',
            domainSpecificKnowledge: options.domainSpecificKnowledge || '',

            // Provider-specific options
            ...options.promptConfig
        };

        const promptCreator = createPromptCreator(applicationType, promptConfig);

        this.logger.info(`✅ Universal prompt creator initialized:`, {
            applicationType,
            provider: promptConfig.provider,
            assistantName: promptConfig.assistantName,
            hasCustomInstructions: !!promptConfig.customInstructions
        });

        return promptCreator;
    }

    /**
     * Detect application type based on configuration
     * @private
     */
    _detectApplicationType(options) {
        // Look for indicators of application type
        if (options.applicationType) {
            return options.applicationType;
        }

        // Check for avatar-specific features
        if (options.enableTTS || options.enableAnimation || options.avatarMode) {
            return 'avatar';
        }

        // Check for code-specific features
        if (options.codeAssistant || options.programmingLanguages) {
            return 'codeAssistant';
        }

        // Check for text-only mode
        if (options.textOnly || !this.providerConfig.getRealtimeConfig().enabled) {
            return 'textAssistant';
        }

        // Default to generic
        return 'generic';
    }

    /**
     * Get provider capabilities for prompt configuration
     * @private
     */
    _getProviderCapabilities() {
        const realtimeConfig = this.providerConfig.getRealtimeConfig();
        const toolsConfig = this.providerConfig.getToolsConfig();
        const provider = this.providerConfig.config.provider;

        return {
            multimodal: provider === 'aliyun' || provider === 'openai',
            realtime: realtimeConfig.enabled,
            voiceInteraction: realtimeConfig.enabled,
            toolCalling: toolsConfig.enabled,
            autonomous: provider === 'aliyun', // Provider supports autonomous communication
            streaming: this.providerConfig.getStreamingConfig().enabled
        };
    }

    /**
     * Initialize universal realtime interface
     * @private
     */
    _initializeRealtimeInterface() {
        if (!this.model) {
            this.logger.warn('Model not available for realtime interface initialization');
            return;
        }

        try {
            this.realtimeInterface = createUniversalRealtimeInterface(this.providerConfig, this.model);

            this.logger.debug('Universal realtime interface initialized:', {
                provider: this.providerConfig.config.provider,
                supported: this.realtimeInterface.isSupported(),
                realtimeEnabled: this.providerConfig.getRealtimeConfig().enabled
            });
        } catch (error) {
            this.logger.warn('Failed to initialize universal realtime interface:', error);
            this.realtimeInterface = null;
        }
    }

    /**
     * Initialize the service with LangGraph workflow and tools
     */
    async initialize() {
        if (this._initialized) return;

        try {
            this.logger.info('Initializing Core LangGraph Agent Service using unified initialization...');

            // Use the unified initialization from init.js instead of separate service
            const { services: initializedServices } = await initializeAgent(this, this.initializationOptions);

            // Extract initialized components
            this.modelFactory = initializedServices.modelFactory;
            this.streamingManager = initializedServices.streamingManager;
            this.providerConfigManager = initializedServices.providerConfigManager;

            this._initialized = true;
            this.logger.info('Core LangGraph Agent Service initialized successfully via unified initialization');

        } catch (error) {
            this.logger.error('❌ Failed to initialize Core LangGraph Agent Service:', error);
            throw error;
        }
    }

    /**
     * Get model by type - supports both single and dual brain architectures
     * @param {string} modelType - 'primary', 'system1', 'system2', or specific task type
     * @returns {Object} Model instance
     */
    getModel(modelType = 'primary') {
        if (this.models && this.models[modelType]) {
            return this.models[modelType];
        }

        // Fallback to primary model for backward compatibility
        return this.model;
    }

    /**
     * Check if dual brain mode is enabled
     * @returns {boolean} True if dual brain architecture is active
     */
    isDualBrainMode() {
        // Check if dual brain is configured and models are available
        const hasDualBrainConfig = this.options.agentConfig?.enableDualBrain;
        const hasModels = !!(this.models?.system1 && this.models?.system2);

        // Allow initialization if config is enabled, even if coordinator isn't attached yet
        return !!(hasDualBrainConfig && hasModels);
    }

    /**
     * Get or create universal model factory with shared infrastructure services
     * @private
     */
    async _getOrCreateModelFactory() {
        if (!this._modelFactory) {
            const provider = this.providerConfig.config.provider;

            this.logger.debug(`Creating universal model factory for provider: ${provider} with shared infrastructure`);

            // Get infrastructure services to pass to model factory
            const infrastructureServices = await this.infrastructureManager.createServicesForComponent(
                `ModelFactory-${provider}`,
                {
                    performanceTracker: { component: `${provider}ModelFactory` },
                    circuitBreaker: { component: `${provider}ModelFactory` },
                    cacheManager: { component: `${provider}ModelFactory` }
                }
            );

            // Create provider-specific model factory
            switch (provider.toLowerCase()) {
                case 'aliyun':
                    const { AliyunModelFactory } = await import('./models/aliyun/AliyunModelFactory.js');
                    this._modelFactory = new AliyunModelFactory({
                        providerConfig: this.providerConfig,
                        apiKey: this.providerConfig.getAuthConfig().apiKey,
                        // Pass shared infrastructure manager to eliminate redundancy
                        infrastructureManager: this.infrastructureManager
                    });
                    this.logger.info('✅ Aliyun ModelFactory created with shared infrastructure services');
                    break;

                case 'vllm':
                case 'openai':
                default:
                    // Use universal model factory with provider config
                    this._modelFactory = new UniversalModelFactory(this.providerConfig);

                    // Inject infrastructure services into universal factory
                    if (this._modelFactory._initializeInfrastructure) {
                        this._modelFactory.performanceTracker = infrastructureServices.performanceTracker;
                        this._modelFactory.circuitBreaker = infrastructureServices.circuitBreaker;
                        this._modelFactory.cacheManager = infrastructureServices.cacheManager;
                        this._modelFactory.performanceCoordinator = infrastructureServices.performanceCoordinator;
                    }

                    // Override getModelForTask for providers without specialized factory
                    const self = this;
                    this._modelFactory.getModelForTask = async () => {
                        // For now, return the primary model for all tasks
                        // Future enhancement: implement task-specific model selection
                        return self.model;
                    };

                    this.logger.info(`✅ Universal ModelFactory created for provider: ${provider} with shared infrastructure`);
                    break;
            }
        }
        return this._modelFactory;
    }

    /**
     * Initialize models using universal provider configuration
     */
    async _initializeModels() {
        const provider = this.providerConfig.config.provider;
        this.logger.debug(`🔍 Initializing model with universal provider config: ${provider}`);

        try {
            // Build provider options from universal config
            const providerOptions = this._buildProviderOptions();

            this.logger.debug('Creating model with universal provider factory:', {
                provider,
                config: {
                    ...providerOptions,
                    // Mask sensitive data
                    apiKey: providerOptions.apiKey ?
                        `${providerOptions.apiKey.substring(0, 3)}...${providerOptions.apiKey.substring(providerOptions.apiKey.length - 3)}` :
                        'none',
                    aliyunApiKey: providerOptions.aliyunApiKey ?
                        `${providerOptions.aliyunApiKey.substring(0, 3)}...${providerOptions.aliyunApiKey.substring(providerOptions.aliyunApiKey.length - 3)}` :
                        'none'
                }
            });

            // Create primary model using universal provider factory
            this.model = await createModelProvider(provider, providerOptions);

            // Initialize model references for dual brain architecture
            this.models = {
                primary: this.model,        // Main model for single-brain mode
                system1: null,              // Fast Brain (WebSocket/Realtime) 
                system2: null               // Thinking Brain (HTTP)
            };

            this.logger.debug('Universal model created successfully:', {
                provider,
                modelType: this.model.constructor.name,
                hasGenerateMethod: typeof this.model._generate === 'function',
                llmType: this.model._llmType?.() || 'unknown',
                identifyingParams: this.model._identifyingParams
            });

        } catch (error) {
            this.logger.error(`❌ Failed to initialize ${provider} model:`, error);
            throw new Error(`Failed to initialize ${provider} model: ${error.message}`);
        }
    }

    /**
     * Build provider options from universal configuration
     * @private
     */
    _buildProviderOptions() {
        const authConfig = this.providerConfig.getAuthConfig();
        const modelConfig = this.providerConfig.getModelConfig();
        const connectionConfig = this.providerConfig.getConnectionConfig();
        const streamingConfig = this.providerConfig.getStreamingConfig();
        const realtimeConfig = this.providerConfig.getRealtimeConfig();
        const debugConfig = this.providerConfig.getDebugConfig();

        return {
            // Authentication
            apiKey: authConfig.apiKey,
            aliyunApiKey: authConfig.apiKey, // Backward compatibility
            openaiApiKey: authConfig.apiKey, // Backward compatibility

            // Model configuration
            model: modelConfig.name,
            temperature: modelConfig.temperature,
            maxTokens: modelConfig.maxTokens,
            topP: modelConfig.topP,
            timeout: modelConfig.timeout,

            // Connection configuration
            endpoint: connectionConfig.endpoint,
            httpEndpoint: connectionConfig.httpEndpoint,
            vllmEndpoint: connectionConfig.endpoint, // Backward compatibility
            realtimeEndpoint: connectionConfig.websocketEndpoint,

            // Streaming configuration
            streaming: streamingConfig.enabled,
            chunkSize: streamingConfig.chunkSize,
            bufferSize: streamingConfig.bufferSize,

            // Realtime configuration
            enableRealtime: realtimeConfig.enabled,
            sampleRate: realtimeConfig.sampleRate,
            audioFormat: realtimeConfig.format,
            // VAD handling moved to UI layer

            // Debug configuration
            verbose: debugConfig.enabled,
            enableDebugLogging: debugConfig.enabled,

            // Provider-specific options
            ...this.providerConfig.getProviderSpecificConfig()
        };
    }


    /**
     * Initialize unified LangGraph streaming manager
     * 🔥 ENHANCED: Uses consolidated StreamingManager with shared infrastructure
     */
    async _initializeStreamingManager() {
        try {
            this.logger.debug('Initializing unified LangGraph streaming manager with shared infrastructure...');

            // Import unified StreamingManager with enhanced features
            const { StreamingManager } = await import('./streaming/StreamingManager.js');

            // Use centralized streaming configuration
            const streamingConfig = this.centralizedConfig?.streaming || getStreamingConfig();
            this.streamingManager = new StreamingManager(streamingConfig);

            // 🔥 CRITICAL FIX: Pass shared InfrastructureManager to prevent performance coordinator conflicts
            if (this.infrastructureManager && this.streamingManager.initializePerformanceCoordination) {
                await this.streamingManager.initializePerformanceCoordination(this.infrastructureManager);
                this.logger.info('✅ StreamingManager using shared infrastructure - prevents coordinator conflicts');
            } else {
                this.logger.warn('⚠️ StreamingManager initialized without shared infrastructure');
            }

            // Store reference for external services that need it
            this._streamingManagerReady = true;

            this.logger.info('✅ Unified StreamingManager initialized with enhanced performance patterns');

        } catch (error) {
            this.logger.error('Critical: Failed to initialize unified streaming manager:', error);
            throw new Error('StreamingManager initialization failed - this is a critical system component');
        }
    }



    /**
     * Setup direct media-to-streaming integration
     * Replaces MediaInputBridge with direct MediaCaptureManager → StreamingManager connection
     */
    _setupDirectMediaIntegration() {
        // Direct integration is now handled by MediaCaptureManager.setStreamingManager()
        // No bridge component needed - applications call connectMediaCapture() to link components
        this.logger.info('Direct media integration pattern configured');
    }

    // _autoRegisterTools removed – core now defers to tools module autoRegisterTools directly in initialize()

    /**
     * Initialize memory manager with LangGraph MemorySaver and InMemoryStore
     */
    async _initializeMemoryManager() {
        if (this.memoryManager) return this.memoryManager;

        try {
            // Use LangGraph memory implementation
            const { LangGraphMemoryManager } = await import('./memory/index.js');
            this.memoryManager = new LangGraphMemoryManager(this.memoryOptions);

            // Get checkpointer and store from memory manager
            this.checkpointer = this.memoryManager.getCheckpointer();
            this.store = this.memoryManager.getStore();

            this.logger.info('✅ Initialized LangGraph Memory Manager with MemorySaver and InMemoryStore');
            return this.memoryManager;
        } catch (error) {
            this.logger.error('❌ Failed to initialize LangGraph memory manager:', error);

            // Fallback: try legacy mem0 implementation
            // try {
            //     const avatarMemory = await import('./legacy/mem0.js');
            //     const mem0Instance = avatarMemory.default;

            //     if (!mem0Instance.initialized) {
            //         await mem0Instance.initialize();
            //     }

            //     this.memoryManager = mem0Instance;
            //     this.logger.warn('⚠️ Using legacy Mem0 memory manager as fallback');
            //     return this.memoryManager;
            // } catch (mem0Error) {
            //     this.logger.error('❌ Failed to initialize any memory manager:', mem0Error);
            //     this.memoryManager = null;
            //     return null;
            // }
        }
    }

    /**
     * Initialize basic coordination services - provider-agnostic
     */
    async _initializeCoordinationServices() {
        try {
            this.logger.info('Initializing basic coordination services...');

            // Initialize Connection Manager - simple connection management
            this.connectionCoordination = await ConnectionManager.getInstance({
                maxRetries: this.options.maxRetries || 3,
                retryDelay: this.options.reconnectDelay || 2000,
                connectionTimeout: this.options.sessionTimeout || 10000
            });

            // Set up coordination callbacks for basic connection management
            this._setupCoordinationCallbacks();

            this.logger.info('✅ Basic coordination services initialized successfully:', {
                connectionCoordination: !!this.connectionCoordination
            });

        } catch (error) {
            this.logger.error('❌ Failed to initialize coordination services:', error);
            throw new Error(`Failed to initialize coordination services: ${error.message}`);
        }
    }

    /**
     * Initialize dual brain models and assign them to this.models
     * @private
     */
    async _initializeDualBrainModels() {
        try {
            this.logger.debug('🧠 Initializing dual brain models...');

            const modelFactory = await this._getOrCreateModelFactory();
            this.logger.debug('🏭 Model factory obtained', { hasFactory: !!modelFactory });

            const { system1Model, system2Model } = await this._createDualBrainModels(modelFactory);
            this.logger.debug('🔧 Dual brain models created', {
                system1Created: !!system1Model,
                system2Created: !!system2Model,
                system1Type: system1Model?.constructor?.name,
                system2Type: system2Model?.constructor?.name
            });

            // Assign the models to the models object
            this.models.system1 = system1Model;
            this.models.system2 = system2Model;

            this.logger.info('✅ Dual brain models initialized and assigned', {
                system1Type: system1Model?.constructor?.name || 'Unknown',
                system2Type: system2Model?.constructor?.name || 'Unknown',
                system1HasInvoke: typeof system1Model?.invoke === 'function',
                system2HasInvoke: typeof system2Model?.invoke === 'function'
            });

        } catch (error) {
            this.logger.error('❌ Failed to initialize dual brain models:', error);
            // NO FALLBACK: Dual brain system requires proper HTTP model for System 2
            throw new Error(`Dual brain initialization failed: ${error.message}. System 2 MUST use HTTP model.`);
        }
    }

    /**
     * Create dual brain models using universal provider patterns
     * @private
     */
    async _createDualBrainModels(modelFactory) {
        const provider = this.providerConfig.config.provider;
        const realtimeConfig = this.providerConfig.getRealtimeConfig();

        this.logger.debug(`Creating dual brain models for provider: ${provider}`);

        let system1Model, system2Model;

        try {
            // System 1 (Fast Brain): Use realtime/websocket model when available
            if (realtimeConfig.enabled && provider === 'aliyun') {
                // Provider supports realtime - create specialized fast brain
                system1Model = await modelFactory.getModelForTask('realtime_processing', {
                    inputType: 'voice',
                    requiresRealtime: true,
                    priority: 'speed'
                });
            } else {
                // Fallback: use primary model for fast responses
                system1Model = this.model;
            }

            // System 2 (Thinking Brain): ALWAYS use HTTP model for complex reasoning
            // No fallback - System 2 must be HTTP for proper dual brain architecture
            if (provider === 'aliyun') {
                system2Model = await modelFactory.getModelForTask('complex_reasoning', {
                    requiresTools: true,
                    complexity: 'high',
                    priority: 'accuracy'
                });

                // Verify we got an HTTP model, not WebSocket
                if (system2Model.constructor.name.includes('WebSocket')) {
                    throw new Error('System 2 must use HTTP model, got WebSocket model instead. Check HTTP endpoint configuration.');
                }
            } else {
                // For non-Aliyun providers, force HTTP model creation
                system2Model = await modelFactory.createHttpModel();
                if (!system2Model) {
                    throw new Error(`System 2 HTTP model creation failed for provider: ${provider}`);
                }
            }

            // Ensure we have valid models - NO FALLBACK to prevent WebSocket contamination
            if (!system1Model) {
                throw new Error('System 1 model creation failed - no fallback allowed for dual brain architecture');
            }
            if (!system2Model) {
                throw new Error('System 2 model creation failed - no fallback allowed for dual brain architecture');
            }

            this.logger.debug('Dual brain models created:', {
                system1Type: system1Model.constructor?.name || 'Unknown',
                system2Type: system2Model.constructor?.name || 'Unknown',
                sameModel: system1Model === system2Model,
                hasRealtime: realtimeConfig.enabled
            });

            return { system1Model, system2Model };

        } catch (error) {
            this.logger.error('Failed to create specialized dual brain models:', error);
            // Don't use fallback - throw error to ensure proper HTTP model setup
            throw new Error(`Dual brain model initialization failed: ${error.message}`);
        }
    }


    /**
     * Handle context updates from dual brain system
     */
    _handleDualBrainContextUpdate(type, contextData, enrichedContext) {
        this.logger.debug(`🧠 Dual brain context update: ${type}`, {
            hasContextData: !!contextData,
            hasEnrichedContext: !!enrichedContext,
            contextAge: enrichedContext?.summary?.totalAge || null
        });

        // Notify coordination callbacks about context updates
        if (this.coordinationCallbacks.onContextualUpdate) {
            this.coordinationCallbacks.onContextualUpdate({
                type,
                contextData,
                enrichedContext,
                timestamp: Date.now()
            });
        }

        // Context updates are now handled internally by DualBrainCoordinator
        // No need for separate contextual analysis service
    }

    /**
     * Set up coordination callbacks to bridge services with LangGraph state
     */
    _setupCoordinationCallbacks() {
        // UI Coordination callbacks removed - UI handled directly by components

        // Connection Manager callbacks  
        if (this.connectionCoordination) {
            this.connectionCoordination.on('stateChange', ({ oldState, newState }) => {
                this.coordinationCallbacks.onConnectionStateChange({
                    state: newState.toLowerCase(),
                    oldState: oldState.toLowerCase(),
                    timestamp: Date.now()
                });
            });

            this.connectionCoordination.on('connectionReady', (connectionData) => {
                this.coordinationCallbacks.onConnectionStateChange({
                    state: 'connected',
                    ...connectionData
                });
            });

            this.connectionCoordination.on('error', (error) => {
                this.coordinationCallbacks.onConnectionStateChange({
                    state: 'error',
                    error: error.message || error,
                    timestamp: Date.now()
                });
                this.callbacks.onError?.(error);
            });

            this.connectionCoordination.on('close', (event) => {
                this.coordinationCallbacks.onConnectionStateChange({
                    state: 'disconnected',
                    code: event.code,
                    reason: event.reason,
                    timestamp: Date.now()
                });
            });
        }

        // Contextual Analysis - now integrated into DualBrainCoordinator
        // All contextual analysis is handled by System2PeriodicAnalysis when dual-brain is enabled
    }

    /**
     * Create the React agent using LangGraph's standard implementation
     */
    async _createAgent(options = {}) {
        // Create optimized system message with generic context
        const systemMessage = await this._createSystemMessage(options);

        this.logger.debug('Creating LangGraph React agent with optimized prompt:', {
            modelType: this.model?.constructor?.name,
            toolsCount: this.tools.length,
            hasCheckpointer: !!this.checkpointer,
            systemMessageLength: systemMessage?.length || 0,
            optimizedPrompt: true
        });

        try {
            // CRITICAL FIX: Use ToolManagementService for validation instead of scattered logic
            // Use System 2 (HTTP) for tool-calling; fallback to current model if dual-brain not ready
            const modelForTools = (this.isDualBrainMode() && this.models?.system2) ? this.models.system2 : this.model;

            // ENHANCED VALIDATION: Import enhanced validation and debugging
            const { validateToolsForLangGraph, getToolChoiceStrategy } = await import('./config/LangGraphConfig.js');
            const { debugToolsForLangGraph } = await import('./config/debug/ToolDebugger.js');

            // Get tools directly from toolManager (simpler approach)
            const managedTools = this.tools || [];

            this.logger.debug('🔍 Pre-validation tool check:', {
                managedToolsCount: managedTools.length,
                legacyToolsCount: this.tools?.length || 0,
                toolsType: Array.isArray(managedTools) ? 'array' : typeof managedTools,
                toolNames: managedTools.map(t => t?.name || 'unnamed')
            });

            // CRITICAL FIX: Debug tools first to identify undefined/null tools
            const debugReport = debugToolsForLangGraph(managedTools);
            if (debugReport.undefinedTools.length > 0 || debugReport.invalidTools.length > 0) {
                this.logger.warn('⚠️ Tool validation issues detected:', {
                    undefinedTools: debugReport.undefinedTools.length,
                    invalidTools: debugReport.invalidTools.length,
                    invalidToolDetails: debugReport.invalidTools.map(t => ({
                        name: t.toolName,
                        issues: t.issues
                    })),
                    recommendations: debugReport.recommendations
                });

                // Log detailed issues for debugging
                if (debugReport.invalidTools.length > 0) {
                    this.logger.debug('🔍 Detailed tool issues:', debugReport.invalidTools);
                }
            }

            // Validate tools before binding to prevent LangGraph errors
            const validatedTools = validateToolsForLangGraph(managedTools, {
                skipInvalidTools: true, // CRITICAL: Skip invalid tools instead of throwing
                requireDescription: true,
                requireSchema: true,
                requireInvokeMethod: true,
                logValidationWarnings: true
            });

            this.logger.info(`🔧 Tool validation complete: ${validatedTools.length}/${managedTools.length} tools validated`, {
                validatedToolNames: validatedTools.map(t => t.name),
                originalToolCount: managedTools.length,
                validatedToolCount: validatedTools.length
            });

            // Check model supports tool binding
            if (!modelForTools || typeof modelForTools.bindTools !== 'function') {
                throw new Error(`Model ${modelForTools?.constructor?.name || 'undefined'} does not support tool binding`);
            }

            // CRITICAL FIX: Only bind tools if we have valid tools and model supports binding
            if (validatedTools.length === 0) {
                this.logger.warn('⚠️ No valid tools to bind - ReactAgent will have no tool calling capability');
                // Create agent without tools
                const agentConfig = {
                    llm: modelForTools, // Use raw model without tool binding
                    tools: [], // Empty tools array
                    checkpointSaver: this.checkpointer,
                    messageModifier: systemMessage
                };

                if (this.store) {
                    agentConfig.store = this.store;
                }

                this.agent = createReactAgent(agentConfig);
                this.logger.warn('⚠️ ReactAgent created without tools due to validation failures');
                return;
            }

            // FIX: Don't re-bind tools if model is already bound
            const isModelAlreadyBound = modelForTools.boundTools && modelForTools.boundTools.length > 0;

            let boundModel;
            if (isModelAlreadyBound) {
                boundModel = modelForTools;
                this.logger.debug('✅ Using pre-bound model for ReactAgent', {
                    boundToolsCount: modelForTools.boundTools.length,
                    toolNames: validatedTools.map(t => t.name)
                });
            } else {
                // Only bind tools if not already bound
                this.logger.debug('🔧 Binding tools to model for ReactAgent', {
                    toolCount: validatedTools.length,
                    toolNames: validatedTools.map(t => t.name)
                });
                boundModel = modelForTools.bindTools(validatedTools);
            }

            // Use createReactAgent with properly bound LLM and validated tools
            const agentConfig = {
                llm: boundModel, // Use bound LLM instead of raw model
                tools: validatedTools, // Use validated tools instead of raw tools
                checkpointSaver: this.checkpointer,
                messageModifier: systemMessage
            };

            // Add store if available for long-term memory
            if (this.store) {
                agentConfig.store = this.store;
                this.logger.debug('✅ Adding InMemoryStore to ReactAgent for long-term memory');
            }

            this.agent = createReactAgent(agentConfig);

            this.logger.debug('React agent created successfully with tool-bound LLM:', {
                agentType: this.agent?.constructor?.name,
                hasStreamMethod: typeof this.agent?.stream === 'function',
                hasInvokeMethod: typeof this.agent?.invoke === 'function',
                agentMethods: this.agent ? Object.getOwnPropertyNames(Object.getPrototypeOf(this.agent)) : null,
                systemMessagePreview: systemMessage.substring(0, 100) + '...'
            });

        } catch (agentError) {
            this.logger.error('Error creating LangGraph agent:', agentError);
            throw new Error(`Failed to create LangGraph agent: ${agentError.message}`);
        }
    }

    /**
     * Default system prompt creation using universal prompt creator
     */
    async _defaultSystemPrompt(options = {}) {
        this.logger.debug('Using universal prompt creator for system prompt generation');

        try {
            // Use the universal prompt creator with current configuration
            return await this.promptCreator.createSystemPrompt({
                ...options,
                provider: this.providerConfig.config.provider,
                modelCapabilities: this._getProviderCapabilities()
            });
        } catch (error) {
            this.logger.warn('Error using universal prompt creator, falling back to basic prompt:', error);
            // Final fallback to basic prompt
            return `You are ${this.promptCreator.config.assistantName || 'Javis'}, a ${this.promptCreator.config.assistantRole || 'helpful AI assistant'}. You can use tools to enhance conversations.`;
        }
    }

    /**
     * Create system message using injected prompt creation function
     */
    async _createSystemMessage(options = {}) {
        try {
            // Validate and normalize options
            const validatedOptions = validateLangGraphOptions(options);

            // Use the injected system prompt creation function
            return await this.createSystemPrompt(validatedOptions);
        } catch (error) {
            this.logger.warn('Error creating system message with injected function, using fallback:', error);
            // Fallback to simple system message
            return `You are Javis, a helpful AI assistant. You can use tools to enhance conversations.`;
        }
    }

    // _createLangGraphWorkflow removed - unused method

    // === LEGACY STATEGRAPH NODE METHODS MOVED ===
    // These node methods were moved to src/agent/legacy/state/definitions.js
    // They were designed for custom StateGraph workflows but are not used
    // since this implementation uses ReactAgent (which handles its own workflow).
    // 
    // For future StateGraph-based implementations, use:
    // import { createLegacyStateGraphNodes } from './legacy/state/definitions.js';
    // 
    // The current ReactAgent implementation handles tool calling and state
    // management automatically without requiring custom node definitions.

    /**
     * Tool choice strategy for ReactAgent
     * Simplified version since ReactAgent handles tool binding internally
     */
    _getToolChoiceForTools() {
        const hasSpeakingTool = this.tools.some(tool => tool.name === 'control_avatar_speech');

        this.logger.debug('🎯 Determining tool choice strategy:', {
            configuredStrategy: this.toolChoiceStrategy,
            toolsAvailable: this.tools.length,
            hasSpeakingTool,
            toolNames: this.tools.map(t => t.name)
        });

        // For avatar applications, use 'auto' to let LLM decide contextually
        if (hasSpeakingTool) {
            this.logger.debug('🔧 Using "auto" tool choice for autonomous avatar communication');
            return 'auto'; // Let LLM decide when to speak vs think silently
        }

        // For other applications, follow configured strategy or default to 'auto'
        const strategy = this.toolChoiceStrategy === 'required' ? 'auto' : (this.toolChoiceStrategy || 'auto');

        this.logger.debug('🔧 Using tool choice strategy:', strategy);
        return strategy;
    }

    /**
     * Route after response generation based on tool calls
     */
    _routeAfterGeneration(state) {
        const { messages } = state;

        this.logger.debug('🔀 Routing after generation:', {
            messageCount: messages?.length || 0,
            hasMessages: !!(messages && messages.length > 0)
        });

        if (messages && messages.length > 0) {
            const lastMessage = messages[messages.length - 1];

            this.logger.debug('🔍 Examining last message for tool calls:', {
                hasLastMessage: !!lastMessage,
                messageType: lastMessage?.constructor?.name || typeof lastMessage,
                hasToolCalls: !!(lastMessage?.tool_calls && lastMessage.tool_calls.length > 0),
                toolCallCount: lastMessage?.tool_calls?.length || 0,
                toolCallNames: lastMessage?.tool_calls?.map(tc => tc.name) || []
            });

            // Check if the last message (AI response) has tool calls
            if (lastMessage?.tool_calls && lastMessage.tool_calls.length > 0) {
                this.logger.debug('🛠️ Tool calls detected, routing to executeTools');
                return 'executeTools';
            }
        }

        this.logger.debug('✅ No tool calls detected, routing to updateAgent');
        return 'updateAgent';
    }

    /**
     * Generate response using LangGraph ReactAgent with CORRECT streaming
     */
    async generateResponse(input, options = {}) {
        if (!this._initialized) {
            await this.initialize();
        }

        const sessionId = options.sessionId || 'default';
        const language = options.language || 'english';

        try {
            this.logger.debug('🚀 LangGraph ReactAgent starting response generation:', {
                input: typeof input === 'string' ? input.substring(0, 100) + '...' : input,
                sessionId,
                language,
                streaming: options.stream !== false,
                toolsAvailable: this.tools.length
            });

            // Notify processing start
            this.callbacks.onModeChange('processing', sessionId);

            // Load memory context for conversation history (OPTIMIZATION: Skip for System 1 fast path)
            let memoryContext = { conversation_history: [], user_memories: [], memory_type: 'none' };
            if (!options.skipMemoryContext && this.memoryManager && this.memoryManager.getMemoryContext) {
                try {
                    // Extract userId from options or use sessionId as fallback
                    const userId = options.userId || sessionId;
                    memoryContext = await this.memoryManager.getMemoryContext(sessionId, userId, {
                        includeConversationHistory: true,
                        includeUserMemories: true,
                        maxConversationHistory: 10,
                        maxUserMemories: 5
                    });
                } catch (error) {
                    this.logger.warn('Memory loading failed:', error);
                }
            } else if (options.skipMemoryContext) {
                this.logger.debug('🚀 Skipping memory context loading for optimized System 1 performance');
            }

            // Prepare messages with conversation history and system prompt (OPTIMIZATION: Lightweight for System 1)
            let systemPrompt, conversationHistory, messages;

            if (options.skipMemoryContext) {
                // OPTIMIZATION: Minimal system prompt and no conversation history for System 1
                systemPrompt = `You are Javis, a helpful AI assistant. Provide quick, direct responses.`;
                conversationHistory = [];
                messages = [
                    { role: 'system', content: systemPrompt },
                    { role: 'user', content: input }
                ];
                this.logger.debug('🚀 Using lightweight message structure for System 1');
            } else {
                // STANDARD: Full system prompt and conversation history for System 2
                systemPrompt = await this.createSystemPrompt({
                    language,
                    includeToolInfo: true,
                    sessionId,
                    _suppressLogging: false
                });

                conversationHistory = memoryContext.conversation_history || [];
                messages = [
                    { role: 'system', content: systemPrompt },
                    ...conversationHistory.slice(-5).map(msg => ({
                        role: (msg.role === 'ai') ? 'assistant' : (msg.role || 'user'),
                        content: msg.content
                    })),
                    { role: 'user', content: input }
                ];
            }

            // CORRECT: Prepare LangGraph-compatible input
            const agentInput = {
                messages: messages.map(msg => ({
                    role: msg.role === 'system' ? 'system'
                        : (msg.role === 'human' || msg.role === 'user') ? 'human'
                            : (msg.role === 'assistant' || msg.role === 'ai') ? 'ai'
                                : 'human',
                    content: msg.content
                }))
            };

            // Enable streaming for all models - let OpenAI SDK handle it internally
            if (options.stream !== false && this.streamingManager) {
                this.logger.debug('🌊 Starting native LangGraph streaming');

                const streamOptions = {
                    sessionId,
                    modes: ['values'], // FIX: Use 'values' instead of 'messages' to avoid name property issues
                    onMessage: (data) => {
                        this.callbacks.onTextChunk?.(data.token, data.fullMessage);
                    },
                    onComplete: (data) => {
                        this.callbacks.onModeChange('idle', sessionId);

                        // Save to memory
                        if (data.fullMessage && input && this.memoryManager) {
                            this._saveToMemory(sessionId, input, data.fullMessage, options);
                        }
                    },
                    onError: (error) => {
                        this.logger.error('Streaming error details:', {
                            error: error.message,
                            stack: error.stack,
                            sessionId,
                            agentType: this.agent?.constructor?.name
                        });
                        this.callbacks.onModeChange('error', sessionId);
                        this.callbacks.onError(error);
                    }
                };

                // FIX: Add validation before streaming
                try {
                    return await this.streamingManager.startNativeStream(
                        this.agent,
                        agentInput,
                        streamOptions
                    );
                } catch (streamError) {
                    this.logger.error('Failed to start streaming, falling back to invoke:', streamError);
                    // Fallback to non-streaming mode
                    const result = await this.agent.invoke(agentInput, {
                        configurable: { thread_id: sessionId }
                    });
                    const finalMessage = result?.messages?.[result.messages.length - 1];
                    const agentResponse = finalMessage?.content || 'I apologize, but I was unable to generate a response.';

                    // Save to memory if successful
                    if (agentResponse && input && this.memoryManager) {
                        await this._saveToMemory(sessionId, input, agentResponse, options);
                    }

                    this.callbacks.onModeChange('idle', sessionId);
                    return agentResponse;
                }
            }

            // Non-streaming path: use invoke
            const result = await this.agent.invoke(agentInput, {
                configurable: {
                    thread_id: sessionId,
                    sessionId: sessionId
                }
            });

            this.logger.debug('✅ ReactAgent execution completed:', {
                hasResult: !!result,
                hasMessages: !!(result?.messages),
                messageCount: result?.messages?.length || 0,
                lastMessageType: result?.messages?.[result.messages.length - 1]?.constructor?.name
            });

            // Extract final response from ReactAgent result
            const finalMessage = result?.messages?.[result.messages.length - 1];
            const agentResponse = finalMessage?.content || 'I apologize, but I was unable to generate a response.';

            // Save to memory if successful
            if (agentResponse && input && this.memoryManager) {
                await this._saveToMemory(sessionId, input, agentResponse, options);
            }

            // Update mode to idle
            this.callbacks.onModeChange('idle', sessionId);

            this.logger.debug('🎯 Returning ReactAgent response:', {
                responseLength: agentResponse.length,
                responsePreview: agentResponse.substring(0, 100) + '...'
            });

            return agentResponse;

        } catch (error) {
            this.logger.error('❌ Error in ReactAgent generateResponse:', error);
            this.callbacks.onModeChange('error', sessionId);
            this.callbacks.onError(error);
            throw error;
        }
    }

    /**
     * Helper method to save conversation to memory
     */
    async _saveToMemory(sessionId, input, response, options = {}) {
        try {
            if (this.memoryManager && this.memoryManager.addConversationTurn) {
                // Extract userId from options or use sessionId as fallback
                const userId = options.userId || sessionId;
                await this.memoryManager.addConversationTurn(sessionId, userId, input, response, {
                    timestamp: Date.now(),
                    source: 'agent_conversation',
                    ...options
                });
            }
        } catch (memoryError) {
            this.logger.warn('Memory save failed:', memoryError);
        }
    }

    /**
     * Start ReactAgent streaming using StreamingManager
     */
    async _startReactAgentStreaming(result, sessionId, options) {
        if (!this.streamingManager) {
            this.logger.warn('Streaming manager not available, returning direct response');
            const finalMessage = result?.messages?.[result.messages.length - 1];
            return finalMessage?.content || '';
        }

        try {
            // Extract response content
            const response = result?.messages?.[result.messages.length - 1]?.content || '';

            if (!response) {
                this.logger.warn('No response content to stream');
                return '';
            }

            // Create a stream from the ReactAgent response using StreamingManager
            const streamConfig = this.streamingManager.createStreamConfig({
                chunkSize: options.chunkSize || 50,
                delayMs: options.delayMs || 20,
                enableTokenization: true
            });

            // Use StreamingManager to create token stream
            const tokenStream = this.streamingManager.createTokenStream(
                this._createResponseStream(response),
                streamConfig
            );

            // Process the stream with callbacks
            return this.streamingManager.processStream(tokenStream, sessionId, {
                onChunk: options.onChunk,
                onComplete: options.onComplete,
                onError: options.onError
            });

        } catch (error) {
            this.logger.error('Failed to start ReactAgent streaming:', error);

            // Fallback to direct response
            const finalMessage = result?.messages?.[result.messages.length - 1];
            return finalMessage?.content || '';
        }
    }

    /**
     * Create a simple response stream from text content
     * @private
     */
    async* _createResponseStream(response) {
        // Simple generator that yields the response
        yield { content: response, done: false };
        yield { content: '', done: true };
    }

    /**
     * Generic external state update notification
     */
    async _notifyExternalStateUpdate(stateUpdate, sessionId) {
        try {
            // Notify external state callbacks
            this.stateCallbacks.onExternalStateUpdate(stateUpdate, sessionId);

            this.logger.debug(`External state updated:`, stateUpdate);
        } catch (error) {
            this.logger.warn('Failed to notify external state update:', error);
        }
    }

    // Generic service management
    updateServices(newServices) {
        this.services = { ...this.services, ...newServices };

        // Tools are self-sufficient and don't need re-registration when services change
        this.logger.debug('Services updated - tools remain self-sufficient');
    }

    updateConfig(newConfig) {
        if (newConfig) {
            this.options = { ...this.options, ...newConfig };
        }
    }

    async setMode(mode, sessionId = 'default') {
        // Delegate mode changes to external state managers
        await this.updateExternalState({ mode }, sessionId);
    }

    async handleInterruption(sessionId = 'default') {
        // Delegate interruption handling to external state managers
        await this.updateExternalState({ interrupted: true }, sessionId);
    }

    async updateExternalState(stateUpdate, sessionId = 'default') {
        this.stateCallbacks.onExternalStateUpdate(stateUpdate, sessionId);
    }

    async getState(sessionId = 'default') {
        return {
            streaming: false,
            sessionId,
            lastUpdate: Date.now()
        };
    }

    async testConnection() {
        if (this.model) {
            return await this.model.testConnection();
        }
        return false;
    }

    /**
     * Stop streaming operations across all models and services
     * @param {string} sessionId - Session identifier
     * @returns {Promise<boolean>} Success status
     */
    async stopStreaming(sessionId = 'default') {
        try {
            this.logger.debug('🛑 Stopping all streaming operations...');

            const stopPromises = [];

            // Stop model streaming if available
            if (this.model && typeof this.model.stopStreaming === 'function') {
                try {
                    const stopStreamingResult = this.model.stopStreaming();
                    // Only add to promises if result is actually a Promise
                    if (stopStreamingResult && typeof stopStreamingResult.catch === 'function') {
                        stopPromises.push(
                            stopStreamingResult.catch(error =>
                                this.logger.warn('Error stopping model streaming:', error)
                            )
                        );
                    } else {
                        this.logger.debug('Model stopStreaming returned non-promise:', stopStreamingResult);
                    }
                } catch (error) {
                    this.logger.warn('Error calling model.stopStreaming:', error);
                }
            }

            // Stop realtime streaming if available
            if (this.model && typeof this.model.stopRealtimeStreaming === 'function') {
                try {
                    const stopRealtimeResult = this.model.stopRealtimeStreaming();
                    // Only add to promises if result is actually a Promise
                    if (stopRealtimeResult && typeof stopRealtimeResult.catch === 'function') {
                        stopPromises.push(
                            stopRealtimeResult.catch(error =>
                                this.logger.warn('Error stopping realtime streaming:', error)
                            )
                        );
                    } else {
                        this.logger.debug('Model stopRealtimeStreaming returned non-promise:', stopRealtimeResult);
                    }
                } catch (error) {
                    this.logger.warn('Error calling model.stopRealtimeStreaming:', error);
                }
            }

            // Notify state change to idle
            await this._notifyExternalStateUpdate({ phase: 'idle', streaming: false }, sessionId);

            // Wait for all stop operations
            await Promise.allSettled(stopPromises);

            this.logger.info('✅ All streaming operations stopped');
            return true;
        } catch (error) {
            this.logger.error('❌ Error stopping streaming:', error);
            return false;
        }
    }

    /**
     * Stop realtime streaming specifically
     * @param {string} sessionId - Session identifier
     * @returns {Promise<boolean>} Success status
     */
    async stopRealtimeStreaming(sessionId = 'default') {
        try {
            this.logger.debug('🔇 Stopping realtime streaming...');

            if (this.model && typeof this.model.stopRealtimeStreaming === 'function') {
                await this.model.stopRealtimeStreaming();
            } else if (this.model && typeof this.model.stopStreaming === 'function') {
                await this.model.stopStreaming();
            }

            await this._notifyExternalStateUpdate({ phase: 'idle', realtimeStreaming: false }, sessionId);
            this.logger.info('✅ Realtime streaming stopped');
            return true;
        } catch (error) {
            this.logger.error('❌ Error stopping realtime streaming:', error);
            return false;
        }
    }

    /**
     * Check if realtime mode is active and session is ready
     * Uses universal realtime interface for provider-agnostic functionality
     * @returns {boolean} True if realtime mode is active and session is ready
     */
    isRealtimeModeActive() {
        // Use universal realtime interface if available
        if (this.realtimeInterface) {
            return this.realtimeInterface.isActive();
        }

        // Fallback to model-specific check
        if (!this.model || typeof this.model.isRealtimeModeActive !== 'function') {
            return false;
        }

        return this.model.isRealtimeModeActive();
    }

    /**
     * Check if realtime session is ready for audio streaming
     * Uses universal realtime interface for provider-agnostic functionality
     * @returns {boolean} True if session is ready for audio data
     */
    isRealtimeSessionReady() {
        // Use universal realtime interface if available
        if (this.realtimeInterface) {
            return this.realtimeInterface.isSessionReady();
        }

        // Fallback to model-specific check
        if (this.model?.realtimeClient?.isSessionReady) {
            return this.model.realtimeClient.isSessionReady();
        }

        // Final fallback to basic realtime mode check
        return this.isRealtimeModeActive();
    }

    /**
     * Wait for realtime session to be ready - uses universal interface
     */
    async waitForRealtimeSessionReady(options = {}) {
        // Use universal realtime interface if available
        if (this.realtimeInterface) {
            return await this.realtimeInterface.waitForSessionReady(options);
        }

        // Fallback to centralized audio utility
        return await waitForRealtimeSessionReady(this.model, { ...options, logger: this.logger });
    }

    /**
     * Initialize realtime mode using universal interface
     * Provider-agnostic method that works with any provider
     * @param {Object} options - Realtime initialization options
     * @returns {Promise<boolean>} Success status
     */
    async initializeRealtimeMode(options = {}) {
        try {
            this.logger.debug('🎙️ Initializing realtime mode with universal interface...', {
                provider: this.providerConfig.config.provider,
                hasRealtimeInterface: !!this.realtimeInterface,
                realtimeSupported: this.realtimeInterface?.isSupported()
            });

            // Use universal realtime interface if available
            if (this.realtimeInterface) {
                const success = await this.realtimeInterface.initialize({
                    ...options,
                    agentService: this // Pass self reference for workflow triggering
                });

                if (success) {
                    this.logger.info('✅ Realtime mode initialized successfully via universal interface');
                    return true;
                } else {
                    this.logger.warn('⚠️ Universal realtime interface initialization failed');
                    return false;
                }
            }

            // Fallback to model-specific initialization
            if (!this.model || typeof this.model.initializeRealtimeMode !== 'function') {
                this.logger.warn('⚠️ No realtime interface or model support available');
                return false;
            }

            this.logger.debug('Falling back to model-specific realtime initialization');

            await this.model.initializeRealtimeMode({
                ...options,
                agentService: this // Pass self reference for workflow triggering
            });

            // Wait for session to be ready if requested
            if (options.waitForSession !== false) {
                const sessionReady = await this.waitForRealtimeSessionReady({
                    timeoutMs: options.sessionTimeoutMs || 10000,
                    checkIntervalMs: options.checkIntervalMs || 500,
                    onProgress: options.onProgress
                });

                if (!sessionReady) {
                    this.logger.warn('⚠️ Realtime session did not become ready within timeout');
                    return false;
                }
            }

            this.logger.info('✅ Realtime mode initialized successfully via model fallback');
            return true;
        } catch (error) {
            this.logger.error('❌ Failed to initialize realtime mode:', error);
            return false;
        }
    }

    /**
     * Send audio data to realtime API using universal interface
     */
    async sendRealtimeAudio(audioData, format = 'arraybuffer') {
        // Check if realtime session is ready before sending
        if (!this.isRealtimeSessionReady()) {
            this.logger.warn('⚠️ Cannot send audio: realtime session not ready');
            return false;
        }

        // Use universal realtime interface if available
        if (this.realtimeInterface) {
            return await this.realtimeInterface.sendAudio(audioData, format);
        }

        // Fallback to centralized audio utility
        return await sendRealtimeAudio(this.model, audioData, format, this.logger);
    }

    /**
     * Connect MediaCaptureManager directly to realtime WebSocket audio streaming
     * @param {MediaCaptureManager} mediaCaptureManager - Media capture manager instance
     * @returns {Promise<boolean>} Success status
     */
    async connectMediaCapture(mediaCaptureManager) {
        try {
            this.logger.info('🎤 Connecting MediaCaptureManager to realtime WebSocket audio streaming...');

            // Get the realtime model (System 1 in dual brain architecture)
            const realtimeModel = this.isDualBrainMode() ? this.models.system1 : this.model;

            if (!realtimeModel || !realtimeModel._sendAudioToProvider) {
                this.logger.warn('❌ Realtime model not available or does not support audio streaming');
                return false;
            }

            // Set up audio callback to send to WebSocket using input_audio_buffer.append
            mediaCaptureManager.setAudioCallback(async (audioData, format) => {
                try {
                    // Ensure we're sending in the correct format for Aliyun API
                    let processedAudioData = audioData;
                    let audioFormat = format;

                    // Convert to base64 if needed for WebSocket transmission
                    if (format === 'pcm16' && audioData instanceof ArrayBuffer) {
                        // Convert ArrayBuffer to base64 for WebSocket transmission
                        const bytes = new Uint8Array(audioData);
                        processedAudioData = btoa(String.fromCharCode(...bytes));
                        audioFormat = 'base64';
                    } else if (format === 'float32' && audioData instanceof Float32Array) {
                        // Convert Float32Array to PCM16 ArrayBuffer, then to base64
                        const pcm16Array = new Int16Array(audioData.length);
                        for (let i = 0; i < audioData.length; i++) {
                            pcm16Array[i] = Math.max(-32768, Math.min(32767, audioData[i] * 32767));
                        }
                        const arrayBuffer = pcm16Array.buffer;
                        const bytes = new Uint8Array(arrayBuffer);
                        processedAudioData = btoa(String.fromCharCode(...bytes));
                        audioFormat = 'base64';
                    }

                    // Send audio to WebSocket via input_audio_buffer.append
                    const success = await realtimeModel._sendAudioToProvider(processedAudioData, audioFormat);

                    if (!success) {
                        this.logger.debug('⚠️ Failed to send audio chunk to WebSocket - connection may be unstable');

                        // Check if WebSocket connection is still alive
                        if (realtimeModel.isSessionReady && !realtimeModel.isSessionReady()) {
                            this.logger.warn('❌ WebSocket session not ready - attempting to reconnect');
                            // Attempt to reconnect if session is not ready
                            try {
                                await realtimeModel.initializeSession();
                            } catch (reconnectError) {
                                this.logger.error('❌ Failed to reconnect WebSocket session:', reconnectError);
                            }
                        }
                    }

                    return success;
                } catch (error) {
                    this.logger.error('❌ Error sending audio to WebSocket:', error);
                    return false;
                }
            }, 'pcm16'); // Request PCM16 format from MediaCaptureManager

            // Also connect to StreamingManager for fallback/additional processing
            if (this.streamingManager) {
                mediaCaptureManager.setStreamingManager(this.streamingManager);
            }

            this.logger.info('✅ MediaCaptureManager connected to realtime WebSocket audio streaming');
            return true;

        } catch (error) {
            this.logger.error('❌ Failed to connect media capture to realtime audio streaming:', error);
            return false;
        }
    }

    /**
     * Get streaming manager for direct media integration
     * @returns {StreamingManager|null} Streaming manager instance
     */
    getStreamingManager() {
        if (!this._streamingManagerReady || !this.streamingManager) {
            this.logger.debug('StreamingManager not ready or not initialized');
            return null;
        }
        return this.streamingManager;
    }

    /**
     * Check if StreamingManager is ready for use
     * @returns {boolean} True if StreamingManager is initialized and ready
     */
    isStreamingManagerReady() {
        return !!(this._streamingManagerReady && this.streamingManager);
    }

    /**
     * Connect external service to StreamingManager automatically
     * This method ensures StreamingManager is ready before connecting
     * @param {Object} externalService - Service that needs StreamingManager connection
     * @param {string} connectionMethod - Method name to call on service (default: 'setStreamingManager')
     * @returns {Promise<boolean>} Success status
     */
    async connectToStreamingManager(externalService, connectionMethod = 'setStreamingManager') {
        try {
            // Wait for initialization if not ready
            if (!this._initialized) {
                this.logger.info('Agent service not initialized, initializing now...');
                await this.initialize();
            }

            // ✅ FIX: Add retry mechanism for StreamingManager readiness
            let retries = 0;
            const maxRetries = 5;
            const retryDelay = 1000; // 1 second

            while (!this.isStreamingManagerReady() && retries < maxRetries) {
                this.logger.debug(`StreamingManager not ready, attempt ${retries + 1}/${maxRetries}, waiting ${retryDelay}ms...`);
                await new Promise(resolve => setTimeout(resolve, retryDelay));
                retries++;
            }

            // Final check after retries
            if (!this.isStreamingManagerReady()) {
                this.logger.warn('StreamingManager not ready for connection after retries');
                return false;
            }

            // Check if external service has the connection method
            if (typeof externalService[connectionMethod] !== 'function') {
                this.logger.warn(`External service does not have method: ${connectionMethod}`);
                return false;
            }

            // Connect StreamingManager to external service
            externalService[connectionMethod](this.streamingManager);
            this.logger.info(`✅ StreamingManager connected to external service via ${connectionMethod}`);
            return true;

        } catch (error) {
            this.logger.error('Failed to connect external service to StreamingManager:', error);
            return false;
        }
    }

    // === COORDINATION SERVICE ACCESS METHODS ===
    // These methods provide access to the unified coordination services
    // that replace the competing state managers

    // UI coordination methods removed - UI handled directly by components

    /**
     * Get connection coordination service for provider connection management  
     * Replaces direct access to AliyunStateManager connection functionality
     */
    getConnectionCoordination() {
        return this.connectionCoordination;
    }

    // Contextual analysis has been moved into DualBrainCoordinator internals.
    // Keeping accessor removed to avoid exposing unused surface area.

    /**
     * Get dual brain coordinator for advanced AI reasoning
     * Consolidated from TalkingAvatar to core agent service
     */
    getDualBrainCoordinator() {
        return this.dualBrainCoordinator;
    }

    /**
     * Set dual brain coordinator from external service/plugin
     * Maintains model-agnostic architecture by allowing external coordinator injection
     * @param {Object} coordinator - DualBrainCoordinator instance or null to clear
     */
    setDualBrainCoordinator(coordinator) {
        if (coordinator && typeof coordinator.initialize !== 'function') {
            throw new Error('Invalid coordinator: must implement initialize() method');
        }

        // Cleanup existing coordinator
        if (this.dualBrainCoordinator && typeof this.dualBrainCoordinator.shutdown === 'function') {
            this.dualBrainCoordinator.shutdown();
        }

        // Inject infrastructure services if coordinator doesn't have them
        if (coordinator && !coordinator.infrastructureManager) {
            coordinator.infrastructureManager = this.infrastructureManager;
            this.logger.debug('🔧 Injected infrastructure services into DualBrainCoordinator');
        }

        this.dualBrainCoordinator = coordinator;
        this.logger.debug(`🧠 DualBrainCoordinator ${coordinator ? 'attached' : 'detached'}`, {
            hasDualBrainMode: this.isDualBrainMode(),
            hasInfrastructure: !!(coordinator?.infrastructureManager)
        });
    }

    /**
     * Update dual brain context with multimodal data
     * Centralized method for context bridge updates
     */
    updateDualBrainContext(type, contextData) {
        if (this.dualBrainCoordinator?.contextBridge) {
            this.dualBrainCoordinator.contextBridge.updateContext(type, contextData);
            this.logger.debug(`🧠 Dual brain context updated: ${type}`, { hasData: !!contextData });
        } else {
            this.logger.debug(`⚠️ Dual brain coordinator not available for context update: ${type}`);
        }
    }

    // VAD handling has been moved to UI layer (AgentCoordinator)
    // Core service remains general-purpose and provider-agnostic

    // updateUIState method removed - UI state handled directly by components

    /**
     * Initialize provider connection through coordination service
     * Unified method to replace direct connection management
     */
    async initializeProviderConnection(providerType, providerConfig, callbacks = {}) {
        if (this.connectionCoordination) {
            return await this.connectionCoordination.initializeConnection(providerType, providerConfig, callbacks);
        }
        this.logger.warn('⚠️ Connection coordination service not available');
        return false;
    }

    /**
     * Get comprehensive coordination status
     * Replaces scattered state queries across multiple managers
     */
    getCoordinationStatus() {
        return {
            connection: this.connectionCoordination?.getConnectionStatus() || null,
            contextual: {
                hasService: !!this.contextualAnalysis,
                ready: this.contextualAnalysis?.initialized || false,
            },
            dualBrain: {
                hasCoordinator: !!this.dualBrainCoordinator,
                isActive: this.dualBrainCoordinator?.isActive || false,
                metrics: this.dualBrainCoordinator?.getMetrics() || null
            },
            timestamp: Date.now()
        };
    }

    // === MEMORY ACCESS METHODS ===
    // These methods provide access to LangGraph memory functionality

    /**
     * Get the memory manager instance
     * @returns {LangGraphMemoryManager|null} Memory manager instance
     */
    getMemoryManager() {
        return this.memoryManager;
    }

    /**
     * Add memories to long-term storage
     * @param {string} userId - User identifier
     * @param {Array|Object} memories - Memory content to store
     * @param {string} context - Optional context (e.g., 'conversation', 'preferences')
     * @returns {Promise<string[]>} Array of memory IDs
     */
    async addMemories(userId, memories, context = 'general') {
        if (this.memoryManager && typeof this.memoryManager.addMemories === 'function') {
            return await this.memoryManager.addMemories(userId, memories, context);
        }
        this.logger.warn('Memory manager not available or does not support addMemories');
        return [];
    }

    /**
     * Search long-term memories
     * @param {string} userId - User identifier
     * @param {Object} options - Search options
     * @returns {Promise<Array>} Array of memory objects
     */
    async searchMemories(userId, options = {}) {
        if (this.memoryManager && typeof this.memoryManager.searchMemories === 'function') {
            return await this.memoryManager.searchMemories(userId, options);
        }
        this.logger.warn('Memory manager not available or does not support searchMemories');
        return [];
    }

    /**
     * Get all memories for a user
     * @param {string} userId - User identifier
     * @param {string} context - Optional context filter
     * @returns {Promise<Array>} Array of all user memories
     */
    async getAllMemories(userId, context) {
        if (this.memoryManager && typeof this.memoryManager.getAllMemories === 'function') {
            return await this.memoryManager.getAllMemories(userId, context);
        }
        this.logger.warn('Memory manager not available or does not support getAllMemories');
        return [];
    }

    /**
     * Clear memories for a user
     * @param {string} userId - User identifier
     * @param {string} context - Optional context to clear
     * @returns {Promise<boolean>} Success status
     */
    async clearMemories(userId, context) {
        if (this.memoryManager && typeof this.memoryManager.clearMemories === 'function') {
            return await this.memoryManager.clearMemories(userId, context);
        }
        this.logger.warn('Memory manager not available or does not support clearMemories');
        return false;
    }

    /**
     * Get memory statistics for a user
     * @param {string} userId - User identifier
     * @returns {Promise<Object>} Memory statistics
     */
    async getMemoryStats(userId) {
        if (this.memoryManager && typeof this.memoryManager.getMemoryStats === 'function') {
            return await this.memoryManager.getMemoryStats(userId);
        }
        this.logger.warn('Memory manager not available or does not support getMemoryStats');
        return {};
    }

    /**
     * 🔥 CRITICAL FIX: Connect DualBrainCoordinator to external service for integration
     * @param {Object} externalService - External service requiring DualBrainCoordinator integration
     * @param {string} connectionMethod - Method name to call on service (default: 'setDualBrainCoordinator')
     * @returns {Promise<boolean>} Success status
     */
    async connectToDualBrainCoordinator(externalService, connectionMethod = 'setDualBrainCoordinator') {
        try {
            // Wait for initialization if not ready
            if (!this._initialized) {
                this.logger.info('Agent service not initialized, initializing now...');
                await this.initialize();
            }

            // Check if DualBrainCoordinator is available
            if (!this.dualBrainCoordinator) {
                this.logger.warn('DualBrainCoordinator not available for connection');
                return false;
            }

            // Check if external service has the connection method
            if (typeof externalService[connectionMethod] !== 'function') {
                this.logger.warn(`External service does not have method: ${connectionMethod}`);
                return false;
            }

            // Connect DualBrainCoordinator to external service
            externalService[connectionMethod](this.dualBrainCoordinator);
            this.logger.info(`✅ DualBrainCoordinator connected to external service via ${connectionMethod}`);
            return true;

        } catch (error) {
            this.logger.error('Failed to connect external service to DualBrainCoordinator:', error);
            return false;
        }
    }

    /**
     * Get DualBrainCoordinator instance
     * @returns {Object|null} DualBrainCoordinator instance or null if not available
     */
    getDualBrainCoordinator() {
        return this.dualBrainCoordinator || null;
    }

    /**
     * Set DualBrainCoordinator instance
     * @param {Object} coordinator - DualBrainCoordinator instance
     */
    setDualBrainCoordinator(coordinator) {
        if (this.dualBrainCoordinator && typeof this.dualBrainCoordinator.shutdown === 'function') {
            this.dualBrainCoordinator.shutdown();
        }
        this.dualBrainCoordinator = coordinator;
        this.logger.info('✅ DualBrainCoordinator set and ready for integration');
    }

    /**
     * Connect InputCoordinationManager for direct media integration
     * @param {Object} inputCoordinator - The InputCoordinationManager instance
     * @returns {Promise<boolean>} Success status
     */
    async connectInputCoordinator(inputCoordinator) {
        try {
            this.logger.info('🔗 Connecting InputCoordinationManager to LangGraph Agent Service');

            if (!inputCoordinator) {
                this.logger.error('❌ InputCoordinator is null or undefined');
                return false;
            }

            // Store reference to input coordinator for direct media integration
            this.inputCoordinator = inputCoordinator;

            // Set up callback for processing media input
            if (typeof inputCoordinator.setAgentCallback === 'function') {
                inputCoordinator.setAgentCallback(async (mediaInput, sessionId) => {
                    try {
                        // Process media input through the agent
                        const response = await this.processResponse(mediaInput.content || mediaInput, {
                            sessionId: sessionId || 'default',
                            stream: true,
                            mediaType: mediaInput.streamingContext?.inputType || 'text'
                        });
                        return response;
                    } catch (error) {
                        this.logger.error('Error processing media input:', error);
                        return null;
                    }
                });
            }

            this.logger.info('✅ InputCoordinationManager connected successfully');
            return true;
        } catch (error) {
            this.logger.error('❌ Failed to connect InputCoordinationManager:', error);
            return false;
        }
    }

    /**
     * Process input - compatibility method for AgentCoordinator
     * Delegates to generateResponse for consistent interface
     */
    async processInput(input, options = {}) {
        this.logger.debug('🔄 Processing input through compatibility interface');
        return await this.generateResponse(input, options);
    }

    /**
     * Handle proactive speaking decisions - compatibility method for AgentCoordinator
     * @param {Object} decision - Speaking decision with shouldSpeak, reason, priority, etc.
     */
    async handleProactiveSpeakingDecision(decision) {
        try {
            this.logger.info('🎯 Handling proactive speaking decision', {
                shouldSpeak: decision.shouldSpeak,
                reason: decision.reason,
                priority: decision.priority
            });

            if (!decision.shouldSpeak) {
                this.logger.debug('Decision indicates no speaking required');
                return;
            }

            // Use dual brain coordination if available
            if (this.isDualBrainMode() && this.dualBrainCoordinator) {
                await this.dualBrainCoordinator.handleProactiveSpeaking(decision);
                return;
            }

            // Fallback: Generate appropriate response
            const responseOptions = {
                sessionId: decision.context?.sessionId || 'default',
                stream: true,
                priority: decision.priority,
                interruptible: decision.interruptible,
                showVisualCues: decision.showVisualCues
            };

            if (decision.context?.input) {
                await this.generateResponse(decision.context.input, responseOptions);
            } else {
                this.logger.debug('No input provided for proactive speaking, skipping response generation');
            }

        } catch (error) {
            this.logger.error('❌ Error handling proactive speaking decision:', error);
        }
    }

    /**
     * Start speaking - compatibility method for AgentCoordinator fallback
     * @param {Object} options - Speaking options with reason, priority, context
     */
    async startSpeaking(options = {}) {
        try {
            this.logger.info('🎤 Starting speaking with options', options);

            // Convert to proactive speaking decision format
            const decision = {
                shouldSpeak: true,
                reason: options.reason || 'Fallback speaking request',
                priority: options.priority || 'medium',
                interruptible: true,
                showVisualCues: true,
                context: options.context || {}
            };

            await this.handleProactiveSpeakingDecision(decision);

        } catch (error) {
            this.logger.error('❌ Error starting speaking:', error);
        }
    }

    async dispose() {
        // Dispose input coordinator connection
        if (this.inputCoordinator) {
            this.inputCoordinator = null;
        }

        // Dispose shared infrastructure services first
        if (this.infrastructureManager) {
            await this.infrastructureManager.shutdown();
            this.infrastructureManager = null;
        }

        // Dispose coordination services
        // UI coordination removed - no cleanup needed

        if (this.connectionCoordination) {
            this.connectionCoordination.dispose();
            this.connectionCoordination = null;
        }

        if (this.contextualAnalysis) {
            // ContextualAnalysisService may not have dispose method
            this.contextualAnalysis = null;
        }

        // Dispose dual brain coordinator
        if (this.dualBrainCoordinator) {
            this.dualBrainCoordinator.shutdown();
            this.dualBrainCoordinator = null;
        }

        // Dispose memory manager
        if (this.memoryManager && typeof this.memoryManager.dispose === 'function') {
            this.memoryManager.dispose();
        }
        this.memoryManager = null;
        this.checkpointer = null;
        this.store = null;

        // Original disposal logic
        this.graph = null;
        this.tools = [];
        this.services = {};
        this._initialized = false;
        this.logger.info('Core LangGraph Agent Service disposed with unified coordination services');
    }

    // === DUAL BRAIN SYSTEM CONTROL ===
    // These methods provide safe interfaces to dual brain system operations

    /**
     * Start dual brain systems through coordinator
     */
    async startDualBrainSystems() {
        if (!this.dualBrainCoordinator) {
            this.logger.warn('⚠️ Dual brain coordinator not available for system start');
            return false;
        }

        try {
            if (typeof this.dualBrainCoordinator.startDualBrainSystems === 'function') {
                const result = await this.dualBrainCoordinator.startDualBrainSystems();
                this.logger.debug('✅ Dual brain systems started through core.js interface');
                return result;
            } else {
                this.logger.warn('⚠️ startDualBrainSystems method not available on coordinator');
                return false;
            }
        } catch (error) {
            this.logger.error('❌ Failed to start dual brain systems:', error);
            return false;
        }
    }
}

// Export default instance
const langGraphAgentService = new LangGraphAgentService();
export { langGraphAgentService };
export default LangGraphAgentService;