/**
 * Unified VAD Handler System
 * Eliminates all VAD callback redundancy by providing a single source of truth
 * for Voice Activity Detection event management and callback coordination
 * 
 * Features:
 * - Single registration system for ALL VAD callbacks
 * - Bulletproof callback preservation during reconnections
 * - Rich environmental analysis for dual brain coordination
 * - Error-free operation with comprehensive logging
 * - Workflow integration with LangGraph triggering
 */

import { createLogger } from '../../utils/logger.js';
import { VADState } from '../models/aliyun/AliyunConfig.js';

// Lazy-loaded central ErrorHandler (best-effort, non-blocking)
let __vadErrorHandlerPromise = null;
function __getVADErrorHandler() {
    if (!__vadErrorHandlerPromise) {
        __vadErrorHandlerPromise = import('../arch/dualbrain/services/ErrorHandler.js')
            .then(mod => {
                try {
                    if (typeof mod.createErrorHandler === 'function') return mod.createErrorHandler();
                    if (typeof mod.ErrorHandler === 'function') return new mod.ErrorHandler();
                } catch (_) { return null; }
                return null;
            })
            .catch(() => null);
    }
    return __vadErrorHandlerPromise;
}

export class VADHandler {
    constructor(connectionManager, logger = null) {
        this.connectionManager = connectionManager;
        this.logger = logger || createLogger('VADHandler');

        // Single registration system - eliminates redundancy
        this.callbacks = {
            speechStarted: new Set(),
            speechStopped: new Set(),
            transcriptReceived: new Set(),
            audioReceived: new Set(),
            error: new Set()
        };

        // Callback preservation for bulletproof reconnections
        this.preservedCallbacks = null;
        this.callbackMetadata = new Map();
        this.serializationId = 0;

        // Environmental analysis state
        this.currentEnvironment = null;
        this.environmentalHistory = [];
        this.speechStartTime = null;
        this.lastAnalysis = null;

        // Workflow integration
        this.agentService = null;
        this.workflowTriggerEnabled = true;

        // Configuration
        this.config = {
            maxCallbacks: 50,
            maxHistoryLength: 100,
            analysisDepth: 'enhanced',
            environmentalIntelligence: true,
            preservationStrategy: 'deep-clone-with-validation',
            errorRecovery: true
        };

        // Error tracking and recovery
        this.errorCount = 0;
        this.lastError = null;
        this.recoveryAttempts = 0;

        // Performance metrics
        this.metrics = {
            callbacksRegistered: 0,
            callbacksPreserved: 0,
            callbacksRestored: 0,
            speechEventsProcessed: 0,
            environmentalAnalysesPerformed: 0,
            workflowTriggersExecuted: 0,
            errorsHandled: 0
        };

        this.logger.info('🎙️ VADHandler initialized - Unified callback management active', {
            maxCallbacks: this.config.maxCallbacks,
            preservationStrategy: this.config.preservationStrategy,
            environmentalIntelligence: this.config.environmentalIntelligence
        });
    }

    // ===== SINGLE REGISTRATION SYSTEM =====

    /**
     * Register callback for VAD events - SINGLE SOURCE OF TRUTH
     * @param {string} eventType - 'speechStarted', 'speechStopped', 'transcriptReceived', 'audioReceived', 'error'
     * @param {Function} handler - Callback function
     * @param {Object} metadata - Optional metadata for debugging
     * @returns {string} Registration ID for unregistering
     */
    registerCallback(eventType, handler, metadata = {}) {
        if (!this.callbacks[eventType]) {
            throw new Error(`Invalid VAD event type: ${eventType}. Valid types: ${Object.keys(this.callbacks).join(', ')}`);
        }

        if (typeof handler !== 'function') {
            throw new Error(`VAD callback must be a function, got ${typeof handler}`);
        }

        // Check for duplicate registrations
        if (this.callbacks[eventType].has(handler)) {
            this.logger.warn(`🎙️ [VADHandler] Callback already registered for ${eventType}, skipping duplicate`);
            return null;
        }

        // Enforce callback limits
        if (this.callbacks[eventType].size >= this.config.maxCallbacks) {
            this.logger.error(`🎙️ [VADHandler] Maximum callbacks reached for ${eventType} (${this.config.maxCallbacks})`);
            throw new Error(`Maximum VAD callbacks reached for ${eventType}`);
        }

        const registrationId = `vad_${eventType}_${Date.now()}_${++this.serializationId}`;

        // Store metadata for debugging and preservation
        this.callbackMetadata.set(handler, {
            id: registrationId,
            eventType,
            registeredAt: Date.now(),
            metadata,
            functionString: handler.toString().substring(0, 200) + '...'
        });

        // Add to callbacks
        this.callbacks[eventType].add(handler);
        this.metrics.callbacksRegistered++;

        this.logger.info(`🎙️ [VADHandler] Callback registered successfully`, {
            eventType,
            registrationId,
            totalCallbacks: this.callbacks[eventType].size,
            metadata: metadata.name || 'anonymous'
        });

        return registrationId;
    }

    /**
     * Unregister callback by handler function or registration ID
     */
    unregisterCallback(eventType, handlerOrId) {
        if (!this.callbacks[eventType]) {
            return false;
        }

        let removed = false;

        if (typeof handlerOrId === 'function') {
            // Remove by function reference
            removed = this.callbacks[eventType].delete(handlerOrId);
            if (removed) {
                this.callbackMetadata.delete(handlerOrId);
            }
        } else if (typeof handlerOrId === 'string') {
            // Remove by registration ID
            for (const [handler, meta] of this.callbackMetadata.entries()) {
                if (meta.id === handlerOrId && meta.eventType === eventType) {
                    this.callbacks[eventType].delete(handler);
                    this.callbackMetadata.delete(handler);
                    removed = true;
                    break;
                }
            }
        }

        if (removed) {
            this.logger.info(`🎙️ [VADHandler] Callback unregistered for ${eventType}`);
        }

        return removed;
    }

    /**
     * Clear all callbacks for an event type
     */
    clearAllCallbacks(eventType = null) {
        if (eventType) {
            if (this.callbacks[eventType]) {
                const count = this.callbacks[eventType].size;
                // Remove metadata for all handlers
                for (const handler of this.callbacks[eventType]) {
                    this.callbackMetadata.delete(handler);
                }
                this.callbacks[eventType].clear();
                this.logger.info(`🎙️ [VADHandler] Cleared ${count} callbacks for ${eventType}`);
            }
        } else {
            // Clear all callbacks
            let totalCleared = 0;
            for (const [type, callbackSet] of Object.entries(this.callbacks)) {
                totalCleared += callbackSet.size;
                callbackSet.clear();
            }
            this.callbackMetadata.clear();
            this.logger.info(`🎙️ [VADHandler] Cleared all ${totalCleared} callbacks`);
        }
    }

    // ===== BULLETPROOF CALLBACK PRESERVATION =====

    /**
     * Preserve all callbacks before reconnection - BULLETPROOF SYSTEM
     */
    preserveCallbacks() {
        try {
            const preservationData = {
                callbacks: {},
                metadata: new Map(),
                preservedAt: Date.now(),
                serializationId: this.serializationId
            };

            // Deep clone all callbacks with validation
            for (const [eventType, callbackSet] of Object.entries(this.callbacks)) {
                preservationData.callbacks[eventType] = [];

                for (const callback of callbackSet) {
                    if (typeof callback === 'function') {
                        // Create preserved callback wrapper
                        const preservedCallback = {
                            original: callback,
                            metadata: this.callbackMetadata.get(callback),
                            eventType,
                            preservedAt: Date.now(),
                            validated: this._validateCallback(callback)
                        };

                        preservationData.callbacks[eventType].push(preservedCallback);

                        // Clone metadata
                        if (this.callbackMetadata.has(callback)) {
                            preservationData.metadata.set(callback, {
                                ...this.callbackMetadata.get(callback)
                            });
                        }
                    }
                }
            }

            this.preservedCallbacks = preservationData;
            this.metrics.callbacksPreserved = this._countPreservedCallbacks(preservationData);

            this.logger.info('🎙️ [VADHandler] Callbacks preserved successfully', {
                totalPreserved: this.metrics.callbacksPreserved,
                preservationStrategy: this.config.preservationStrategy,
                eventTypes: Object.keys(preservationData.callbacks),
                timestamp: new Date(preservationData.preservedAt).toISOString()
            });

            return true;
        } catch (error) {
            this.logger.error('🎙️ [VADHandler] Failed to preserve callbacks:', error);
            this.errorCount++;
            this.lastError = error;
            return false;
        }
    }

    /**
     * Restore preserved callbacks after reconnection
     */
    restoreCallbacks() {
        if (!this.preservedCallbacks) {
            this.logger.warn('🎙️ [VADHandler] No preserved callbacks to restore');
            return false;
        }

        try {
            let restoredCount = 0;
            let validationErrors = 0;

            // Clear existing callbacks before restoring
            this.clearAllCallbacks();

            for (const [eventType, preservedCallbacks] of Object.entries(this.preservedCallbacks.callbacks)) {
                for (const preservedCallback of preservedCallbacks) {
                    try {
                        // Validate callback is still functional
                        if (this._validateCallback(preservedCallback.original)) {
                            this.callbacks[eventType].add(preservedCallback.original);

                            // Restore metadata
                            if (preservedCallback.metadata) {
                                this.callbackMetadata.set(preservedCallback.original, {
                                    ...preservedCallback.metadata,
                                    restoredAt: Date.now()
                                });
                            }

                            restoredCount++;
                        } else {
                            validationErrors++;
                            this.logger.warn('🎙️ [VADHandler] Failed to validate preserved callback', {
                                eventType,
                                callbackId: preservedCallback.metadata?.id
                            });
                        }
                    } catch (error) {
                        validationErrors++;
                        this.logger.error('🎙️ [VADHandler] Error restoring callback:', error);
                    }
                }
            }

            this.metrics.callbacksRestored = restoredCount;

            this.logger.info('🎙️ [VADHandler] Callbacks restored successfully', {
                restoredCount,
                validationErrors,
                successRate: restoredCount > 0 ? ((restoredCount / (restoredCount + validationErrors)) * 100).toFixed(1) + '%' : '0%',
                restoredEventTypes: Object.keys(this.preservedCallbacks.callbacks)
            });

            // Clear preserved data after successful restoration
            this.preservedCallbacks = null;

            return restoredCount > 0;
        } catch (error) {
            this.logger.error('🎙️ [VADHandler] Failed to restore callbacks:', error);
            this.errorCount++;
            this.lastError = error;
            return false;
        }
    }

    /**
     * Validate callback function integrity
     * @private
     */
    _validateCallback(callback) {
        if (typeof callback !== 'function') {
            return false;
        }

        try {
            // Basic function validation
            const funcStr = callback.toString();
            return funcStr.length > 10 && !funcStr.includes('[native code]');
        } catch (error) {
            return false;
        }
    }

    /**
     * Count preserved callbacks
     * @private
     */
    _countPreservedCallbacks(preservationData) {
        let count = 0;
        for (const callbackArray of Object.values(preservationData.callbacks)) {
            count += callbackArray.length;
        }
        return count;
    }

    // ===== VAD EVENT PROCESSING =====

    /**
     * Handle speech started event with rich environmental analysis
     */
    handleSpeechStarted(event) {
        this.speechStartTime = Date.now();
        this.metrics.speechEventsProcessed++;

        try {
            // Generate enhanced environmental context
            const environmentalContext = this.analyzeAudioEnvironment({
                ...event,
                eventType: 'speechStarted',
                timestamp: this.speechStartTime
            });

            this.currentEnvironment = environmentalContext;
            this._addToEnvironmentalHistory(environmentalContext);

            this.logger.info('🎙️ [VADHandler] Speech started - Enhanced environmental analysis', {
                timestamp: new Date(this.speechStartTime).toISOString(),
                environmentType: environmentalContext.acousticEnvironment?.type,
                audioQuality: environmentalContext.audioQuality?.rating,
                speakerProximity: environmentalContext.speakerProximity?.estimation,
                callbackCount: this.callbacks.speechStarted.size
            });

            // Execute all registered callbacks
            this._executeCallbacks('speechStarted', environmentalContext, event);

            return environmentalContext;
        } catch (error) {
            this.logger.error('🎙️ [VADHandler] Error processing speech started event:', error);
            this._handleError('speechStarted', error);
            throw error;
        }
    }

    /**
     * Handle speech stopped event with workflow triggering
     */
    handleSpeechStopped(event) {
        const speechDuration = this.speechStartTime ? Date.now() - this.speechStartTime : 0;
        this.metrics.speechEventsProcessed++;

        try {
            // Generate comprehensive context for speech end
            const speechStoppedContext = this.analyzePostSpeechEnvironment({
                ...event,
                eventType: 'speechStopped',
                speechDuration,
                speechStartTime: this.speechStartTime
            });

            this.logger.info('🎙️ [VADHandler] Speech stopped - Comprehensive analysis', {
                speechDuration: `${speechDuration}ms`,
                speechQuality: speechStoppedContext.speechQuality?.rating,
                readinessForResponse: speechStoppedContext.readinessForResponse?.ready,
                callbackCount: this.callbacks.speechStopped.size,
                workflowTriggerEnabled: this.workflowTriggerEnabled
            });

            // Execute all registered callbacks first
            this._executeCallbacks('speechStopped', speechStoppedContext, event);

            // Trigger LangGraph workflow if enabled and agent service available
            if (this.workflowTriggerEnabled && this.agentService && speechStoppedContext.readinessForResponse?.ready) {
                this._triggerWorkflow(event, speechStoppedContext);
            }

            return speechStoppedContext;
        } catch (error) {
            this.logger.error('🎙️ [VADHandler] Error processing speech stopped event:', error);
            this._handleError('speechStopped', error);
            throw error;
        }
    }

    // ===== RICH ENVIRONMENTAL ANALYSIS =====

    /**
     * Analyze audio environment with comprehensive intelligence
     */
    analyzeAudioEnvironment(vadData) {
        this.metrics.environmentalAnalysesPerformed++;

        try {
            const analysis = {
                timestamp: Date.now(),
                eventType: vadData.eventType,

                // Audio quality assessment
                audioQuality: this._assessAudioQuality(vadData),

                // Speaker proximity estimation
                speakerProximity: this._estimateSpeakerProximity(vadData),

                // Acoustic environment classification
                acousticEnvironment: this._classifyAcousticEnvironment(vadData),

                // Engagement level calculation
                engagementLevel: this._analyzeEngagementLevel(vadData),

                // Contextual situation analysis
                contextualSituation: this._analyzeContextualSituation(vadData),

                // DualBrain coordination context
                dualBrainContext: this._generateDualBrainContext(vadData),

                // Environmental stability
                environmentalStability: this._assessEnvironmentalStability(),

                // Processing recommendations
                processingRecommendations: this._generateProcessingRecommendations(vadData)
            };

            this.lastAnalysis = analysis;

            this.logger.debug('🎙️ [VADHandler] Environmental analysis completed', {
                audioQuality: analysis.audioQuality.rating,
                speakerProximity: analysis.speakerProximity.estimation,
                environmentType: analysis.acousticEnvironment.type,
                engagementLevel: analysis.engagementLevel.level,
                dualBrainRecommendation: analysis.dualBrainContext.systemTwoRecommendation
            });

            return analysis;
        } catch (error) {
            this.logger.error('🎙️ [VADHandler] Error during environmental analysis:', error);
            return this._getFallbackAnalysis(vadData);
        }
    }

    /**
     * Analyze post-speech environment for response readiness
     */
    analyzePostSpeechEnvironment(vadData) {
        try {
            const speechDuration = vadData.speechDuration || 0;
            const confidence = vadData.confidence || 0.8;

            return {
                timestamp: Date.now(),
                eventType: vadData.eventType,

                // Speech quality analysis
                speechQuality: this._assessSpeechQuality(speechDuration, confidence),

                // Environmental transition analysis
                environmentalTransition: this._analyzeEnvironmentalTransition(speechDuration),

                // Conversation flow analysis
                conversationTransition: this._analyzeConversationTransition(),

                // Response readiness assessment
                readinessForResponse: this._assessResponseReadiness(speechDuration, confidence),

                // DualBrain handoff preparation
                dualBrainHandoff: this._prepareDualBrainHandoff(vadData),

                // Contextual insights from history
                environmentalInsights: this._getEnvironmentalInsights()
            };
        } catch (error) {
            this.logger.error('🎙️ [VADHandler] Error in post-speech analysis:', error);
            return this._getFallbackPostSpeechAnalysis(vadData);
        }
    }

    /**
     * Generate contextual insights from environmental history
     */
    generateContextualInsights() {
        if (this.environmentalHistory.length < 3) {
            return {
                trend: 'insufficient_data',
                confidence: 0.1,
                recommendations: ['continue_monitoring']
            };
        }

        const recent = this.environmentalHistory.slice(-10);

        return {
            engagementTrend: this._analyzeEngagementTrend(recent),
            acousticTrend: this._analyzeAcousticTrend(recent),
            qualityTrend: this._analyzeQualityTrend(recent),
            overallStability: this._calculateEnvironmentalStability(recent),
            adaptiveRecommendations: this._generateAdaptiveRecommendations(recent)
        };
    }

    // ===== WORKFLOW INTEGRATION =====

    /**
     * Set agent service for workflow triggering
     */
    setAgentService(agentService) {
        this.agentService = agentService;
        this.logger.info('🎙️ [VADHandler] Agent service registered for workflow integration');
    }

    /**
     * Trigger LangGraph workflow with contextual data
     */
    triggerWorkflow(transcript, context) {
        if (!this.agentService) {
            this.logger.warn('🎙️ [VADHandler] Cannot trigger workflow - no agent service available');
            return false;
        }

        try {
            this.metrics.workflowTriggersExecuted++;

            const workflowContext = {
                sessionId: 'default',
                inputType: 'voice',
                stream: true,
                vadTriggered: true,
                transcript,
                environmentalContext: context,
                processingRecommendations: context.processingRecommendations,
                dualBrainContext: context.dualBrainContext,
                configurable: {
                    useTools: true,
                    language: 'english',
                    temperature: 0.6
                }
            };

            this.logger.info('🎙️ [VADHandler] Triggering LangGraph workflow', {
                transcript: transcript.substring(0, 100) + (transcript.length > 100 ? '...' : ''),
                contextualComplexity: context.dualBrainContext?.contextualComplexity,
                processingMode: context.dualBrainContext?.processingRecommendation?.responseStrategy
            });

            // Execute workflow asynchronously
            this.agentService.generateResponse(transcript, workflowContext)
                .then(result => {
                    this.logger.info('✅ [VADHandler] Workflow completed successfully', {
                        hasResult: !!result,
                        resultType: typeof result
                    });
                })
                .catch(error => {
                    this.logger.error('❌ [VADHandler] Workflow execution failed:', error);
                    this._handleError('workflow', error);
                });

            return true;
        } catch (error) {
            this.logger.error('🎙️ [VADHandler] Error triggering workflow:', error);
            this._handleError('workflow', error);
            return false;
        }
    }

    // ===== CONFIGURATION =====

    /**
     * Configure VAD handler settings
     */
    configure(vadConfig) {
        try {
            this.config = {
                ...this.config,
                ...vadConfig
            };

            this.logger.info('🎙️ [VADHandler] Configuration updated', {
                maxCallbacks: this.config.maxCallbacks,
                analysisDepth: this.config.analysisDepth,
                environmentalIntelligence: this.config.environmentalIntelligence,
                errorRecovery: this.config.errorRecovery
            });

            return true;
        } catch (error) {
            this.logger.error('🎙️ [VADHandler] Configuration error:', error);
            return false;
        }
    }

    /**
     * Check if VAD handler is properly configured
     */
    isConfigured() {
        return this.config &&
            this.callbacks &&
            Object.keys(this.callbacks).length > 0;
    }

    // ===== INTERNAL METHODS =====

    /**
     * Execute callbacks for specific event type
     * @private
     */
    _executeCallbacks(eventType, context, originalEvent) {
        const callbacks = this.callbacks[eventType];

        if (callbacks.size === 0) {
            this.logger.warn(`🎙️ [VADHandler] No callbacks registered for ${eventType}`);
            return;
        }

        let successCount = 0;
        let errorCount = 0;

        for (const callback of callbacks) {
            try {
                callback(context, originalEvent);
                successCount++;
            } catch (error) {
                errorCount++;
                this.logger.error(`🎙️ [VADHandler] Callback error in ${eventType}:`, error);

                // Find callback metadata for better error reporting
                const metadata = this.callbackMetadata.get(callback);
                if (metadata) {
                    this.logger.error('🎙️ [VADHandler] Failed callback details:', {
                        id: metadata.id,
                        registeredAt: new Date(metadata.registeredAt).toISOString(),
                        metadata: metadata.metadata
                    });
                }
            }
        }

        this.logger.debug(`🎙️ [VADHandler] Callbacks executed for ${eventType}`, {
            totalCallbacks: callbacks.size,
            successful: successCount,
            failed: errorCount
        });
    }

    /**
     * Trigger workflow from VAD event
     * @private
     */
    _triggerWorkflow(event, context) {
        try {
            // Extract transcript from event or use fallback
            const transcript = event.transcript ||
                event.text ||
                "Continue the conversation based on the audio input received";

            this.triggerWorkflow(transcript, context);
        } catch (error) {
            this.logger.error('🎙️ [VADHandler] Error in workflow trigger:', error);
        }
    }

    /**
     * Add entry to environmental history
     * @private
     */
    _addToEnvironmentalHistory(environmentalContext) {
        this.environmentalHistory.push({
            timestamp: Date.now(),
            audioQuality: environmentalContext.audioQuality?.rating,
            speakerProximity: environmentalContext.speakerProximity?.estimation,
            acousticEnvironment: environmentalContext.acousticEnvironment?.type,
            engagementLevel: environmentalContext.engagementLevel?.level,
            environmentalStability: environmentalContext.environmentalStability?.level
        });

        // Maintain history length
        if (this.environmentalHistory.length > this.config.maxHistoryLength) {
            this.environmentalHistory = this.environmentalHistory.slice(-this.config.maxHistoryLength);
        }
    }

    /**
     * Handle errors with recovery strategies
     * @private
     */
    _handleError(context, error) {
        this.errorCount++;
        this.lastError = { context, error, timestamp: Date.now() };
        this.metrics.errorsHandled++;

        // Delegate to central ErrorHandler asynchronously
        try {
            __getVADErrorHandler().then(handler => {
                if (handler && typeof handler.handleError === 'function') {
                    const normalized = error instanceof Error ? error : new Error(String(error));
                    handler.handleError(normalized, {
                        component: 'VADHandler',
                        context,
                        sessionId: 'default',
                        operation: 'vad_runtime'
                    }).catch(() => { });
                }
            }).catch(() => { });
        } catch (_) {
            // ignore
        }

        if (this.config.errorRecovery) {
            this._attemptErrorRecovery(context, error);
        }
    }

    /**
     * Attempt error recovery based on context
     * @private
     */
    _attemptErrorRecovery(context, error) {
        this.recoveryAttempts++;

        this.logger.info('🎙️ [VADHandler] Attempting error recovery', {
            context,
            recoveryAttempt: this.recoveryAttempts,
            errorMessage: error.message
        });

        // Recovery strategies based on context
        switch (context) {
            case 'speechStarted':
            case 'speechStopped':
                // Try to preserve callbacks if they were corrupted
                if (this.preservedCallbacks) {
                    this.restoreCallbacks();
                }
                break;

            case 'workflow':
                // Reset workflow trigger state
                this.workflowTriggerEnabled = true;
                break;

            default:
                this.logger.debug('🎙️ [VADHandler] No specific recovery strategy for context:', context);
        }
    }

    // ===== AUDIO ANALYSIS METHODS =====

    /**
     * Assess audio quality based on VAD data
     * @private
     */
    _assessAudioQuality(vadData) {
        const confidence = vadData.confidence || 0.8;
        const threshold = vadData.threshold || 0.5;

        const qualityScore = confidence * 0.7 + (1 - threshold) * 0.3;

        return {
            score: Math.round(qualityScore * 100),
            rating: qualityScore > 0.8 ? 'excellent' :
                qualityScore > 0.6 ? 'good' :
                    qualityScore > 0.4 ? 'fair' : 'poor',
            confidence,
            threshold,
            factors: {
                signalStrength: confidence > 0.7 ? 'strong' : 'moderate',
                noiseLevel: threshold < 0.3 ? 'low' : threshold < 0.6 ? 'moderate' : 'high'
            }
        };
    }

    /**
     * Estimate speaker proximity based on signal indicators
     * @private
     */
    _estimateSpeakerProximity(vadData) {
        const confidence = vadData.confidence || 0.8;
        const proximityScore = confidence * 0.8 + 0.2; // Base proximity

        let proximity;
        if (proximityScore > 0.8) proximity = 'very_close';
        else if (proximityScore > 0.6) proximity = 'close';
        else if (proximityScore > 0.4) proximity = 'moderate';
        else proximity = 'distant';

        return {
            estimation: proximity,
            score: Math.round(proximityScore * 100),
            confidence: confidence,
            indicators: {
                signalStrength: confidence,
                clarity: proximityScore
            }
        };
    }

    /**
     * Classify acoustic environment type
     * @private
     */
    _classifyAcousticEnvironment(vadData) {
        const audioQuality = this._assessAudioQuality(vadData);
        const speakerProximity = this._estimateSpeakerProximity(vadData);

        const isHighQuality = audioQuality.rating === 'excellent' || audioQuality.rating === 'good';
        const isCloseProximity = speakerProximity.estimation === 'very_close' || speakerProximity.estimation === 'close';
        const isQuiet = audioQuality.factors.noiseLevel === 'low';

        let environment;
        if (isQuiet && isHighQuality && isCloseProximity) {
            environment = 'controlled_studio';
        } else if (isQuiet && isHighQuality) {
            environment = 'quiet_indoor';
        } else if (isHighQuality && isCloseProximity) {
            environment = 'close_conversation';
        } else if (audioQuality.factors.noiseLevel === 'high') {
            environment = 'noisy_environment';
        } else {
            environment = 'general_indoor';
        }

        return {
            type: environment,
            confidence: Math.round((audioQuality.score + speakerProximity.score) / 2),
            characteristics: {
                noise_level: audioQuality.factors.noiseLevel,
                audio_quality: audioQuality.rating,
                speaker_distance: speakerProximity.estimation
            }
        };
    }

    /**
     * Analyze engagement level based on environmental factors
     * @private
     */
    _analyzeEngagementLevel(vadData) {
        const recentHistory = this.environmentalHistory.slice(-5);
        const consistentActivity = recentHistory.length > 2;
        const highQualityRecent = recentHistory.filter(h => h.audioQuality === 'excellent' || h.audioQuality === 'good').length;

        let engagementScore = 0.5; // Base score

        if (consistentActivity) engagementScore += 0.2;
        if (highQualityRecent > recentHistory.length * 0.6) engagementScore += 0.2;
        if (vadData.confidence && vadData.confidence > 0.8) engagementScore += 0.1;

        const level = engagementScore > 0.8 ? 'highly_engaged' :
            engagementScore > 0.6 ? 'actively_engaged' :
                engagementScore > 0.4 ? 'moderately_engaged' :
                    engagementScore > 0.2 ? 'passively_engaged' : 'low_engagement';

        return {
            level,
            score: Math.round(engagementScore * 100),
            factors: {
                consistency: consistentActivity,
                audioQuality: highQualityRecent / Math.max(recentHistory.length, 1),
                signalStrength: vadData.confidence || 0.8
            }
        };
    }

    /**
     * Analyze contextual situation with intelligent assessment
     * @private
     */
    _analyzeContextualSituation(vadData) {
        const engagementLevel = this._analyzeEngagementLevel(vadData);
        const acousticEnvironment = this._classifyAcousticEnvironment(vadData);
        const environmentalStability = this._assessEnvironmentalStability();

        return {
            userEngagement: engagementLevel.level,
            interactionMode: this._determineInteractionMode(vadData),
            environmentalState: environmentalStability.level,
            urgencyLevel: this._assessUrgencyLevel(vadData),
            contextComplexity: this._calculateContextComplexity(),
            situationalIntelligence: {
                adaptationRecommendation: this._generateAdaptationRecommendation(engagementLevel, acousticEnvironment),
                responseStrategy: this._suggestResponseStrategy(engagementLevel, environmentalStability)
            }
        };
    }

    /**
     * Generate DualBrain coordination context
     * @private
     */
    _generateDualBrainContext(vadData) {
        const contextComplexity = this._calculateContextComplexity();
        const environmentalReadiness = this._assessEnvironmentalReadiness();
        const systemTwoNeed = this._evaluateSystemTwoNeed(contextComplexity, vadData);

        return {
            contextualComplexity: contextComplexity,
            environmentalReadiness: environmentalReadiness,
            systemTwoRecommendation: systemTwoNeed.engage ? 'activate_system_two' : 'continue_system_one',
            processingMode: systemTwoNeed.engage ? 'deliberative' : 'intuitive',
            coordinationMetrics: {
                vadLatency: vadData.timestamp ? Date.now() - vadData.timestamp : 0,
                environmentalStability: environmentalReadiness.score,
                decisionUrgency: this._assessUrgencyLevel(vadData).level
            }
        };
    }

    // ===== HELPER METHODS FOR ANALYSIS =====

    _assessEnvironmentalStability() {
        const recentHistory = this.environmentalHistory.slice(-5);
        if (recentHistory.length < 2) {
            return { level: 'unknown', score: 0.5 };
        }

        const stabilityScore = this._calculateStabilityFromHistory(recentHistory);

        return {
            level: stabilityScore > 0.7 ? 'high' : stabilityScore > 0.4 ? 'moderate' : 'low',
            score: stabilityScore
        };
    }

    _calculateStabilityFromHistory(history) {
        // Simple stability calculation based on consistency of audio quality and environment
        let consistencyCount = 0;
        for (let i = 1; i < history.length; i++) {
            if (history[i].audioQuality === history[i - 1].audioQuality &&
                history[i].acousticEnvironment === history[i - 1].acousticEnvironment) {
                consistencyCount++;
            }
        }
        return consistencyCount / Math.max(history.length - 1, 1);
    }

    _assessEnvironmentalReadiness() {
        const stability = this._assessEnvironmentalStability();
        const hasCurrentEnvironment = !!this.currentEnvironment;

        const readinessScore = stability.score * 0.6 + (hasCurrentEnvironment ? 0.4 : 0);

        return {
            score: readinessScore,
            level: readinessScore > 0.7 ? 'high' : readinessScore > 0.4 ? 'moderate' : 'low'
        };
    }

    _calculateContextComplexity() {
        const historyLength = this.environmentalHistory.length;
        const recentEngagement = this.environmentalHistory.slice(-3).filter(h =>
            h.engagementLevel === 'highly_engaged' || h.engagementLevel === 'actively_engaged'
        ).length;

        const complexityScore = (historyLength / 10) * 0.5 + (recentEngagement / 3) * 0.5;

        return complexityScore > 0.7 ? 'high' : complexityScore > 0.4 ? 'moderate' : 'low';
    }

    _evaluateSystemTwoNeed(complexity, vadData) {
        const shouldEngage = complexity === 'high' ||
            (vadData.confidence && vadData.confidence > 0.9) ||
            this.environmentalHistory.length > 5;

        return {
            engage: shouldEngage,
            confidence: shouldEngage ? 0.8 : 0.4,
            reasoning: shouldEngage ? 'complex_context_requires_deliberation' : 'intuitive_processing_sufficient'
        };
    }

    _determineInteractionMode(vadData) {
        return {
            mode: 'voice_primary_interaction',
            confidence: 0.8,
            adaptations: ['optimize_for_speech', 'use_conversational_tone']
        };
    }

    _assessUrgencyLevel(vadData) {
        const confidence = vadData.confidence || 0.8;
        const urgency = confidence > 0.9 ? 'high' : confidence > 0.7 ? 'moderate' : 'normal';

        return {
            level: urgency,
            indicators: {
                signalStrength: confidence,
                responseSpeed: urgency === 'high' ? 'immediate' : 'normal'
            }
        };
    }

    _generateAdaptationRecommendation(engagementLevel, acousticEnvironment) {
        if (engagementLevel.level === 'highly_engaged' && acousticEnvironment.type === 'controlled_studio') {
            return 'provide_detailed_intelligent_response';
        }
        return 'balance_detail_with_clarity';
    }

    _suggestResponseStrategy(engagementLevel, environmentalStability) {
        if (environmentalStability.level === 'high' && engagementLevel.level === 'highly_engaged') {
            return 'engaging_conversational_response';
        }
        return 'measured_thoughtful_response';
    }

    _generateProcessingRecommendations(vadData) {
        return {
            priority: vadData.confidence > 0.8 ? 'high' : 'moderate',
            processingMode: 'enhanced_environmental_analysis',
            responseOptimization: 'context_aware'
        };
    }

    // ===== POST-SPEECH ANALYSIS HELPERS =====

    _assessSpeechQuality(duration, confidence) {
        const durationOptimal = duration >= 1000 && duration <= 10000;
        const confidenceHigh = confidence > 0.7;

        let qualityScore = 0.5;
        if (durationOptimal) qualityScore += 0.3;
        if (confidenceHigh) qualityScore += 0.2;

        return {
            score: Math.round(qualityScore * 100),
            rating: qualityScore > 0.8 ? 'excellent' :
                qualityScore > 0.6 ? 'good' :
                    qualityScore > 0.4 ? 'fair' : 'poor',
            factors: {
                duration,
                durationOptimal,
                confidence,
                confidenceHigh
            }
        };
    }

    _analyzeEnvironmentalTransition(speechDuration) {
        const transitionSpeed = speechDuration < 1000 ? 'quick' :
            speechDuration < 5000 ? 'normal' : 'extended';

        return {
            transitionType: 'speech_to_silence',
            transitionSpeed,
            transitionQuality: this._assessTransitionQuality(speechDuration),
            readyForProcessing: true
        };
    }

    _assessTransitionQuality(duration) {
        if (duration >= 1000 && duration <= 8000) {
            return { quality: 'clean', score: 0.9 };
        } else if (duration >= 500 && duration <= 12000) {
            return { quality: 'good', score: 0.7 };
        } else {
            return { quality: 'irregular', score: 0.4 };
        }
    }

    _analyzeConversationTransition() {
        return {
            fromSpeech: true,
            toProcessing: true,
            contextualContinuity: this.environmentalHistory.length > 0 ? 'maintained' : 'initial',
            flowQuality: this.environmentalHistory.length > 2 ? 'established' : 'developing'
        };
    }

    _assessResponseReadiness(speechDuration, confidence) {
        const durationAppropriate = speechDuration >= 300;
        const confidenceHigh = confidence > 0.6;
        const environmentReady = this._assessEnvironmentalStability().level !== 'low';

        const readinessScore = (durationAppropriate ? 0.4 : 0) +
            (confidenceHigh ? 0.3 : 0) +
            (environmentReady ? 0.3 : 0);

        return {
            ready: readinessScore > 0.6,
            score: readinessScore,
            level: readinessScore > 0.8 ? 'high' : readinessScore > 0.6 ? 'moderate' : 'low',
            factors: {
                speechDuration: durationAppropriate,
                confidence: confidenceHigh,
                environment: environmentReady
            }
        };
    }

    _prepareDualBrainHandoff(vadData) {
        const complexity = this._calculateContextComplexity();
        const environmentalReadiness = this._assessEnvironmentalReadiness();

        return {
            handoffType: 'speech_to_processing',
            contextualComplexity: complexity,
            environmentalReadiness: environmentalReadiness,
            processingRecommendation: {
                engageSystemTwo: complexity === 'high',
                priority: vadData.confidence > 0.8 ? 'high' : 'moderate',
                responseStrategy: complexity === 'high' ? 'deliberative_processing' : 'intuitive_processing'
            }
        };
    }

    _getEnvironmentalInsights() {
        return this.generateContextualInsights();
    }

    // ===== FALLBACK METHODS =====

    _getFallbackAnalysis(vadData) {
        return {
            timestamp: Date.now(),
            eventType: vadData.eventType,
            audioQuality: { rating: 'unknown', score: 50 },
            speakerProximity: { estimation: 'moderate', score: 50 },
            acousticEnvironment: { type: 'general_indoor', confidence: 50 },
            engagementLevel: { level: 'moderately_engaged', score: 50 },
            contextualSituation: { userEngagement: 'moderate' },
            dualBrainContext: { systemTwoRecommendation: 'continue_system_one' },
            environmentalStability: { level: 'moderate', score: 0.5 }
        };
    }

    _getFallbackPostSpeechAnalysis(vadData) {
        return {
            timestamp: Date.now(),
            eventType: vadData.eventType,
            speechQuality: { rating: 'fair', score: 60 },
            environmentalTransition: { transitionType: 'speech_to_silence', transitionSpeed: 'normal' },
            readinessForResponse: { ready: true, level: 'moderate' },
            dualBrainHandoff: { handoffType: 'speech_to_processing' }
        };
    }

    // ===== TREND ANALYSIS HELPERS =====

    _analyzeEngagementTrend(history) {
        const engagementLevels = history.map(h => h.engagementLevel);
        const trend = this._calculateTrendDirection(engagementLevels);

        return {
            direction: trend,
            currentLevel: engagementLevels[engagementLevels.length - 1],
            confidence: history.length >= 5 ? 0.8 : 0.5
        };
    }

    _analyzeAcousticTrend(history) {
        const acousticTypes = history.map(h => h.acousticEnvironment).filter(Boolean);
        const uniqueTypes = [...new Set(acousticTypes)];

        return {
            consistency: uniqueTypes.length === 1 ? 'consistent' : 'variable',
            dominantType: this._findMostFrequent(acousticTypes),
            stability: uniqueTypes.length <= 2 ? 'stable' : 'unstable'
        };
    }

    _analyzeQualityTrend(history) {
        const qualityLevels = history.map(h => h.audioQuality);
        const trend = this._calculateTrendDirection(qualityLevels);

        return {
            direction: trend,
            currentLevel: qualityLevels[qualityLevels.length - 1],
            stability: this._calculateStabilityFromHistory(history)
        };
    }

    _calculateTrendDirection(values) {
        if (values.length < 2) return 'stable';

        const firstHalf = values.slice(0, Math.floor(values.length / 2));
        const secondHalf = values.slice(Math.floor(values.length / 2));

        // Simple trend calculation - could be enhanced with actual numerical scoring
        return 'stable'; // Simplified for now
    }

    _findMostFrequent(array) {
        if (!array.length) return null;

        const counts = {};
        array.forEach(item => counts[item] = (counts[item] || 0) + 1);

        return Object.keys(counts).reduce((most, current) =>
            counts[current] > counts[most] ? current : most
        );
    }

    _generateAdaptiveRecommendations(history) {
        const recommendations = ['maintain_current_quality'];

        const recentQuality = history.slice(-3).map(h => h.audioQuality);
        if (recentQuality.filter(q => q === 'poor' || q === 'fair').length > 1) {
            recommendations.push('improve_audio_environment');
        }

        return recommendations;
    }

    // ===== STATUS AND DEBUGGING =====

    /**
     * Get comprehensive status of VAD handler
     */
    getStatus() {
        return {
            configuration: {
                isConfigured: this.isConfigured(),
                maxCallbacks: this.config.maxCallbacks,
                analysisDepth: this.config.analysisDepth,
                environmentalIntelligence: this.config.environmentalIntelligence,
                errorRecovery: this.config.errorRecovery
            },
            callbacks: {
                total: Object.values(this.callbacks).reduce((sum, set) => sum + set.size, 0),
                byType: Object.fromEntries(
                    Object.entries(this.callbacks).map(([type, set]) => [type, set.size])
                ),
                preserved: this.preservedCallbacks ? this._countPreservedCallbacks(this.preservedCallbacks) : 0,
                metadataEntries: this.callbackMetadata.size
            },
            environment: {
                currentAnalysis: this.lastAnalysis ? {
                    timestamp: this.lastAnalysis.timestamp,
                    audioQuality: this.lastAnalysis.audioQuality?.rating,
                    acousticEnvironment: this.lastAnalysis.acousticEnvironment?.type,
                    engagementLevel: this.lastAnalysis.engagementLevel?.level
                } : null,
                historyLength: this.environmentalHistory.length,
                speechActive: !!this.speechStartTime,
                speechDuration: this.speechStartTime ? Date.now() - this.speechStartTime : 0
            },
            workflow: {
                agentServiceAvailable: !!this.agentService,
                triggerEnabled: this.workflowTriggerEnabled,
                triggersExecuted: this.metrics.workflowTriggersExecuted
            },
            performance: {
                ...this.metrics,
                errorRate: this.metrics.speechEventsProcessed > 0 ?
                    (this.errorCount / this.metrics.speechEventsProcessed * 100).toFixed(2) + '%' : '0%',
                avgAnalysisTime: this.metrics.environmentalAnalysesPerformed > 0 ?
                    'N/A' : 'N/A' // Could be tracked if needed
            },
            errors: {
                totalErrors: this.errorCount,
                lastError: this.lastError,
                recoveryAttempts: this.recoveryAttempts
            }
        };
    }

    /**
     * Get debug information for troubleshooting
     */
    getDebugInfo() {
        return {
            callbacks: Array.from(this.callbackMetadata.entries()).map(([func, meta]) => ({
                id: meta.id,
                eventType: meta.eventType,
                registeredAt: new Date(meta.registeredAt).toISOString(),
                metadata: meta.metadata,
                functionPreview: meta.functionString
            })),
            environmentalHistory: this.environmentalHistory.slice(-10),
            preservedCallbacks: this.preservedCallbacks ? {
                preservedAt: new Date(this.preservedCallbacks.preservedAt).toISOString(),
                eventTypes: Object.keys(this.preservedCallbacks.callbacks),
                totalPreserved: this._countPreservedCallbacks(this.preservedCallbacks)
            } : null,
            currentState: {
                speechStartTime: this.speechStartTime,
                currentEnvironment: this.currentEnvironment,
                lastAnalysis: this.lastAnalysis
            }
        };
    }

    /**
     * Reset all state (for testing or recovery)
     */
    reset() {
        this.clearAllCallbacks();
        this.preservedCallbacks = null;
        this.callbackMetadata.clear();
        this.environmentalHistory = [];
        this.currentEnvironment = null;
        this.speechStartTime = null;
        this.lastAnalysis = null;
        this.errorCount = 0;
        this.lastError = null;
        this.recoveryAttempts = 0;
        this.serializationId = 0;

        // Reset metrics
        this.metrics = {
            callbacksRegistered: 0,
            callbacksPreserved: 0,
            callbacksRestored: 0,
            speechEventsProcessed: 0,
            environmentalAnalysesPerformed: 0,
            workflowTriggersExecuted: 0,
            errorsHandled: 0
        };

        this.logger.info('🎙️ [VADHandler] Reset completed - all state cleared');
    }

    /**
     * Dispose of resources and cleanup
     */
    dispose() {
        this.reset();
        this.connectionManager = null;
        this.agentService = null;

        this.logger.info('🎙️ VADHandler disposed');
    }
}

export default VADHandler;