/**
 * Runtime Error Recovery Manager
 * Implements circuit breaker pattern and connection pool management
 * for robust runtime error recovery mechanisms
 */

import { createLogger } from '../../utils/logger.js';

export class RuntimeErrorRecovery {
    constructor(options = {}) {
        this.logger = options.logger || createLogger('RuntimeErrorRecovery');
        this.maxRetries = options.maxRetries || 3;
        this.baseBackoffMs = options.baseBackoffMs || 1000;
        this.maxBackoffMs = options.maxBackoffMs || 30000;
        this.circuitBreakerThreshold = options.circuitBreakerThreshold || 5;
        this.circuitTestInterval = options.circuitTestInterval || 30000; // 30 seconds
        this.operationTimeout = options.operationTimeout || 10000; // 10 seconds
        
        // Circuit breaker state
        this.circuitState = 'closed'; // closed, open, half-open
        this.failureCount = 0;
        this.lastFailureTime = 0;
        this.circuitOpenTime = 0;
        this.successCount = 0;
        
        // Connection pool management
        this.connectionPool = new Map();
        this.maxPoolSize = options.maxPoolSize || 3;
        this.connectionHealthChecks = new Map();
        
        // Operation tracking
        this.activeOperations = new Map();
        this.operationHistory = [];
        this.maxHistorySize = options.maxHistorySize || 100;
        
        // Performance metrics
        this.metrics = {
            totalOperations: 0,
            successfulOperations: 0,
            failedOperations: 0,
            retriedOperations: 0,
            circuitBreakerTrips: 0,
            averageResponseTime: 0,
            responseTimeHistory: []
        };
        
        this.logger.debug('🔧 RuntimeErrorRecovery initialized:', {
            maxRetries: this.maxRetries,
            circuitBreakerThreshold: this.circuitBreakerThreshold,
            maxPoolSize: this.maxPoolSize,
            circuitTestInterval: this.circuitTestInterval
        });
    }
    
    /**
     * Execute operation with circuit breaker pattern and retry logic
     */
    async executeWithCircuitBreaker(operation, operationId = 'default', options = {}) {
        const startTime = Date.now();
        const operationKey = `${operationId}_${startTime}`;
        
        // Check circuit breaker state
        if (this.circuitState === 'open') {
            if (Date.now() - this.circuitOpenTime > this.circuitTestInterval) {
                this.circuitState = 'half-open';
                this.logger.info('🔧 [CircuitBreaker] Transitioning to half-open state for testing');
            } else {
                const remainingTime = this.circuitTestInterval - (Date.now() - this.circuitOpenTime);
                this.logger.warn('🔧 [CircuitBreaker] Circuit is open - operation blocked', {
                    operationId,
                    remainingTime: Math.ceil(remainingTime / 1000) + 's'
                });
                throw new CircuitBreakerOpenError(`Circuit breaker is open for ${operationId}. Retry in ${Math.ceil(remainingTime / 1000)}s`);
            }
        }
        
        // Track active operation
        this.activeOperations.set(operationKey, {
            operationId,
            startTime,
            options
        });
        
        try {
            this.metrics.totalOperations++;
            
            const result = await this._executeWithRetry(operation, operationId, options);
            const responseTime = Date.now() - startTime;
            
            // Success - update circuit breaker and metrics
            this._recordSuccess(responseTime);
            
            if (this.circuitState === 'half-open') {
                this.circuitState = 'closed';
                this.failureCount = 0;
                this.logger.info('🔧 [CircuitBreaker] Reset to closed state after successful operation');
            }
            
            this._recordOperationHistory(operationId, 'success', responseTime, null, options);
            return result;
            
        } catch (error) {
            const responseTime = Date.now() - startTime;
            this._recordFailure(error, responseTime);
            this._recordOperationHistory(operationId, 'failure', responseTime, error, options);
            throw error;
            
        } finally {
            this.activeOperations.delete(operationKey);
        }
    }
    
    /**
     * Execute operation with exponential backoff retry
     * @private
     */
    async _executeWithRetry(operation, operationId, options = {}) {
        const maxRetries = options.maxRetries || this.maxRetries;
        const timeout = options.timeout || this.operationTimeout;
        let lastError;
        let attempt = 0;
        
        while (attempt < maxRetries) {
            try {
                this.logger.debug(`🔧 [Retry] Attempting operation ${operationId} (${attempt + 1}/${maxRetries})`);
                
                // Wrap operation with timeout
                const result = await this._executeWithTimeout(operation, timeout, operationId);
                
                if (attempt > 0) {
                    this.metrics.retriedOperations++;
                    this.logger.info(`✅ [Retry] Operation ${operationId} succeeded on attempt ${attempt + 1}`);
                }
                
                return result;
                
            } catch (error) {
                lastError = error;
                attempt++;
                
                this.logger.warn(`⚠️ [Retry] Operation ${operationId} failed on attempt ${attempt}:`, {
                    error: error.message,
                    errorType: error.constructor.name,
                    isTimeout: error.name === 'TimeoutError'
                });
                
                if (attempt < maxRetries) {
                    const backoffMs = this._calculateBackoff(attempt, options);
                    this.logger.debug(`⏳ [Retry] Backing off ${backoffMs}ms before retry ${attempt + 1}`);
                    await new Promise(resolve => setTimeout(resolve, backoffMs));
                }
            }
        }
        
        throw new RetryExhaustedError(`Operation ${operationId} failed after ${maxRetries} attempts: ${lastError.message}`, lastError);
    }
    
    /**
     * Execute operation with timeout protection
     * @private
     */
    async _executeWithTimeout(operation, timeout, operationId) {
        return new Promise(async (resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new TimeoutError(`Operation ${operationId} timed out after ${timeout}ms`));
            }, timeout);
            
            try {
                const result = await operation();
                clearTimeout(timeoutId);
                resolve(result);
            } catch (error) {
                clearTimeout(timeoutId);
                reject(error);
            }
        });
    }
    
    /**
     * Calculate exponential backoff with jitter
     * @private
     */
    _calculateBackoff(attempt, options = {}) {
        const baseBackoff = options.baseBackoffMs || this.baseBackoffMs;
        const maxBackoff = options.maxBackoffMs || this.maxBackoffMs;
        const jitter = options.jitter !== false; // Default to true
        
        // Exponential backoff: base * 2^attempt
        let backoffMs = baseBackoff * Math.pow(2, attempt - 1);
        
        // Cap at maximum
        backoffMs = Math.min(backoffMs, maxBackoff);
        
        // Add jitter to prevent thundering herd
        if (jitter) {
            const jitterAmount = backoffMs * 0.1; // 10% jitter
            backoffMs += (Math.random() - 0.5) * 2 * jitterAmount;
        }
        
        return Math.floor(backoffMs);
    }
    
    /**
     * Record successful operation
     * @private
     */
    _recordSuccess(responseTime) {
        this.successCount++;
        this.metrics.successfulOperations++;
        
        // Update response time metrics
        this.metrics.responseTimeHistory.push(responseTime);
        if (this.metrics.responseTimeHistory.length > 100) {
            this.metrics.responseTimeHistory.shift();
        }
        
        // Calculate rolling average
        const totalTime = this.metrics.responseTimeHistory.reduce((sum, time) => sum + time, 0);
        this.metrics.averageResponseTime = totalTime / this.metrics.responseTimeHistory.length;
        
        // If we were in half-open state and got a success, we can consider closing
        if (this.circuitState === 'half-open') {
            this.logger.debug('🔧 [CircuitBreaker] Successful operation in half-open state');
        }
    }
    
    /**
     * Record failed operation and update circuit breaker
     * @private
     */
    _recordFailure(error, responseTime) {
        this.failureCount++;
        this.lastFailureTime = Date.now();
        this.metrics.failedOperations++;
        
        this.logger.warn('🔧 [CircuitBreaker] Recorded failure:', {
            failureCount: this.failureCount,
            threshold: this.circuitBreakerThreshold,
            error: error.message,
            errorType: error.constructor.name,
            responseTime
        });
        
        // Check if we should trip the circuit breaker
        if (this.failureCount >= this.circuitBreakerThreshold && this.circuitState !== 'open') {
            this.circuitState = 'open';
            this.circuitOpenTime = Date.now();
            this.metrics.circuitBreakerTrips++;
            
            this.logger.error('🔧 [CircuitBreaker] Circuit opened due to excessive failures:', {
                failureCount: this.failureCount,
                threshold: this.circuitBreakerThreshold,
                lastError: error.message
            });
        }
    }
    
    /**
     * Record operation in history for analysis
     * @private
     */
    _recordOperationHistory(operationId, status, responseTime, error, options) {
        const record = {
            operationId,
            status,
            responseTime,
            error: error ? {
                message: error.message,
                type: error.constructor.name,
                stack: error.stack
            } : null,
            timestamp: Date.now(),
            options: { ...options }
        };
        
        this.operationHistory.push(record);
        
        // Maintain history size limit
        if (this.operationHistory.length > this.maxHistorySize) {
            this.operationHistory.shift();
        }
    }
    
    /**
     * Manage connection pool for reliability
     */
    addConnection(connectionId, connection, healthCheckFn = null) {
        if (this.connectionPool.size >= this.maxPoolSize) {
            this.logger.warn('🔧 [ConnectionPool] Pool is full, removing oldest connection');
            const oldestId = this.connectionPool.keys().next().value;
            this.removeConnection(oldestId);
        }
        
        this.connectionPool.set(connectionId, {
            connection,
            createdAt: Date.now(),
            lastUsed: Date.now(),
            useCount: 0,
            isHealthy: true
        });
        
        if (healthCheckFn) {
            this.connectionHealthChecks.set(connectionId, healthCheckFn);
        }
        
        this.logger.debug('🔧 [ConnectionPool] Added connection:', {
            connectionId,
            poolSize: this.connectionPool.size
        });
    }
    
    /**
     * Get healthy connection from pool
     */
    getConnection(connectionId = null) {
        if (connectionId) {
            const connectionData = this.connectionPool.get(connectionId);
            if (connectionData && connectionData.isHealthy) {
                connectionData.lastUsed = Date.now();
                connectionData.useCount++;
                return connectionData.connection;
            }
        }
        
        // Find any healthy connection
        for (const [id, data] of this.connectionPool.entries()) {
            if (data.isHealthy) {
                data.lastUsed = Date.now();
                data.useCount++;
                this.logger.debug('🔧 [ConnectionPool] Using connection:', { connectionId: id });
                return data.connection;
            }
        }
        
        return null;
    }
    
    /**
     * Remove connection from pool
     */
    removeConnection(connectionId) {
        const removed = this.connectionPool.delete(connectionId);
        this.connectionHealthChecks.delete(connectionId);
        
        if (removed) {
            this.logger.debug('🔧 [ConnectionPool] Removed connection:', {
                connectionId,
                poolSize: this.connectionPool.size
            });
        }
        
        return removed;
    }
    
    /**
     * Perform health checks on all connections
     */
    async performHealthChecks() {
        const healthCheckPromises = [];
        
        for (const [connectionId, healthCheckFn] of this.connectionHealthChecks.entries()) {
            healthCheckPromises.push(
                this._checkConnectionHealth(connectionId, healthCheckFn)
            );
        }
        
        const results = await Promise.allSettled(healthCheckPromises);
        
        let healthyCount = 0;
        let unhealthyCount = 0;
        
        results.forEach((result, index) => {
            if (result.status === 'fulfilled' && result.value) {
                healthyCount++;
            } else {
                unhealthyCount++;
            }
        });
        
        this.logger.debug('🔧 [HealthCheck] Completed health checks:', {
            total: results.length,
            healthy: healthyCount,
            unhealthy: unhealthyCount
        });
        
        return { healthy: healthyCount, unhealthy: unhealthyCount, total: results.length };
    }
    
    /**
     * Check individual connection health
     * @private
     */
    async _checkConnectionHealth(connectionId, healthCheckFn) {
        try {
            const connectionData = this.connectionPool.get(connectionId);
            if (!connectionData) {
                return false;
            }
            
            const isHealthy = await healthCheckFn(connectionData.connection);
            connectionData.isHealthy = isHealthy;
            
            if (!isHealthy) {
                this.logger.warn('🔧 [HealthCheck] Connection marked unhealthy:', { connectionId });
            }
            
            return isHealthy;
        } catch (error) {
            this.logger.error('🔧 [HealthCheck] Health check failed:', {
                connectionId,
                error: error.message
            });
            
            const connectionData = this.connectionPool.get(connectionId);
            if (connectionData) {
                connectionData.isHealthy = false;
            }
            
            return false;
        }
    }
    
    /**
     * Get comprehensive recovery status
     */
    getStatus() {
        return {
            circuitBreaker: {
                state: this.circuitState,
                failureCount: this.failureCount,
                successCount: this.successCount,
                lastFailureTime: this.lastFailureTime,
                circuitOpenTime: this.circuitOpenTime,
                timeUntilTest: this.circuitState === 'open' 
                    ? Math.max(0, this.circuitTestInterval - (Date.now() - this.circuitOpenTime))
                    : 0
            },
            connectionPool: {
                totalConnections: this.connectionPool.size,
                maxPoolSize: this.maxPoolSize,
                healthyConnections: Array.from(this.connectionPool.values()).filter(c => c.isHealthy).length,
                connections: Array.from(this.connectionPool.entries()).map(([id, data]) => ({
                    id,
                    isHealthy: data.isHealthy,
                    createdAt: data.createdAt,
                    lastUsed: data.lastUsed,
                    useCount: data.useCount,
                    ageMs: Date.now() - data.createdAt
                }))
            },
            operations: {
                active: this.activeOperations.size,
                total: this.metrics.totalOperations,
                successful: this.metrics.successfulOperations,
                failed: this.metrics.failedOperations,
                retried: this.metrics.retriedOperations,
                successRate: this.metrics.totalOperations > 0 
                    ? (this.metrics.successfulOperations / this.metrics.totalOperations * 100).toFixed(2) + '%'
                    : '0%',
                averageResponseTime: Math.round(this.metrics.averageResponseTime) + 'ms'
            },
            health: {
                isHealthy: this.circuitState === 'closed' && this.failureCount < this.circuitBreakerThreshold,
                circuitBreakerTrips: this.metrics.circuitBreakerTrips,
                recentFailures: this.operationHistory.filter(op => 
                    op.status === 'failure' && Date.now() - op.timestamp < 60000
                ).length
            }
        };
    }
    
    /**
     * Get recent operation history
     */
    getOperationHistory(limit = 20) {
        return this.operationHistory
            .slice(-limit)
            .map(op => ({
                ...op,
                error: op.error ? {
                    message: op.error.message,
                    type: op.error.type,
                    // Don't include full stack trace in summary
                } : null
            }));
    }
    
    /**
     * Reset circuit breaker manually
     */
    resetCircuitBreaker() {
        const previousState = this.circuitState;
        
        this.circuitState = 'closed';
        this.failureCount = 0;
        this.successCount = 0;
        this.lastFailureTime = 0;
        this.circuitOpenTime = 0;
        
        this.logger.info('🔧 [CircuitBreaker] Manually reset to closed state:', {
            previousState,
            resetTime: new Date().toISOString()
        });
    }
    
    /**
     * Clear metrics and history
     */
    clearMetrics() {
        this.metrics = {
            totalOperations: 0,
            successfulOperations: 0,
            failedOperations: 0,
            retriedOperations: 0,
            circuitBreakerTrips: 0,
            averageResponseTime: 0,
            responseTimeHistory: []
        };
        
        this.operationHistory = [];
        
        this.logger.info('🔧 [Metrics] Cleared all metrics and history');
    }
    
    /**
     * Dispose of resources and cleanup
     */
    dispose() {
        // Clear all connections
        this.connectionPool.clear();
        this.connectionHealthChecks.clear();
        this.activeOperations.clear();
        
        // Clear metrics
        this.clearMetrics();
        
        this.logger.info('🔧 RuntimeErrorRecovery disposed');
    }
}

/**
 * Custom error classes for better error handling
 */
export class CircuitBreakerOpenError extends Error {
    constructor(message) {
        super(message);
        this.name = 'CircuitBreakerOpenError';
    }
}

export class RetryExhaustedError extends Error {
    constructor(message, originalError) {
        super(message);
        this.name = 'RetryExhaustedError';
        this.originalError = originalError;
    }
}

export class TimeoutError extends Error {
    constructor(message) {
        super(message);
        this.name = 'TimeoutError';
    }
}

export default RuntimeErrorRecovery;