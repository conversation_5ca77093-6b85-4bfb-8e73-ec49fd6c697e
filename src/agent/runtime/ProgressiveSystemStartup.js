/**
 * Progressive System Startup with Dependency Management
 * Coordinates the sequential initialization of system components with proper dependency checks
 * Ensures reliable startup order and handles initialization failures gracefully
 */

import { createLogger } from '../../utils/logger.js';

export class ProgressiveSystemStartup {
    constructor(options = {}) {
        this.logger = options.logger || createLogger('ProgressiveSystemStartup');
        this.maxRetries = options.maxRetries || 3;
        this.retryDelayMs = options.retryDelayMs || 2000;
        this.healthCheckInterval = options.healthCheckInterval || 5000;
        this.startupTimeout = options.startupTimeout || 60000; // 1 minute
        this.enableAutoRetry = options.enableAutoRetry !== false;
        
        // System components and their dependencies\n        this.components = new Map();\n        this.initializationOrder = [];\n        this.initializationStatus = new Map();\n        \n        // Runtime state\n        this.isInitializing = false;\n        this.startupStartTime = 0;\n        this.healthCheckTimer = null;\n        this.initializationPromise = null;\n        \n        // Event handlers\n        this.eventHandlers = {\n            componentStarted: [],\n            componentFailed: [],\n            systemReady: [],\n            systemFailed: [],\n            healthCheckFailed: []\n        };\n        \n        // Statistics\n        this.stats = {\n            totalComponents: 0,\n            successfulComponents: 0,\n            failedComponents: 0,\n            retryAttempts: 0,\n            totalStartupTime: 0,\n            startupAttempts: 0\n        };\n        \n        this.logger.debug('🚀 ProgressiveSystemStartup initialized:', {\n            maxRetries: this.maxRetries,\n            retryDelayMs: this.retryDelayMs,\n            startupTimeout: this.startupTimeout,\n            enableAutoRetry: this.enableAutoRetry\n        });\n    }\n    \n    /**\n     * Register a system component with its initialization function and dependencies\n     */\n    registerComponent(name, config) {\n        const {\n            initFunction,\n            dependencies = [],\n            healthCheck = null,\n            criticalComponent = false,\n            initTimeout = 10000,\n            retryAttempts = null // null means use global maxRetries\n        } = config;\n        \n        if (typeof initFunction !== 'function') {\n            throw new Error(`Init function for component '${name}' must be a function`);\n        }\n        \n        const component = {\n            name,\n            initFunction,\n            dependencies: Array.isArray(dependencies) ? dependencies : [dependencies],\n            healthCheck,\n            criticalComponent,\n            initTimeout,\n            retryAttempts: retryAttempts !== null ? retryAttempts : this.maxRetries,\n            registeredAt: Date.now()\n        };\n        \n        this.components.set(name, component);\n        this.initializationStatus.set(name, {\n            status: 'pending',\n            attempts: 0,\n            lastError: null,\n            startTime: null,\n            endTime: null,\n            initializationTime: null\n        });\n        \n        this.logger.debug(`🚀 [Startup] Registered component '${name}':`, {\n            dependencies: component.dependencies,\n            isCritical: criticalComponent,\n            timeout: initTimeout,\n            retryAttempts: component.retryAttempts\n        });\n        \n        // Recalculate initialization order\n        this._calculateInitializationOrder();\n    }\n    \n    /**\n     * Register event handler\n     */\n    on(eventType, handler) {\n        if (this.eventHandlers[eventType]) {\n            this.eventHandlers[eventType].push(handler);\n            this.logger.debug(`🚀 [Events] Registered handler for ${eventType}`);\n        } else {\n            this.logger.warn(`🚀 [Events] Unknown event type: ${eventType}`);\n        }\n    }\n    \n    /**\n     * Start the progressive system initialization\n     */\n    async startSystem() {\n        if (this.isInitializing) {\n            this.logger.info('🚀 [Startup] System initialization already in progress');\n            return this.initializationPromise;\n        }\n        \n        this.isInitializing = true;\n        this.startupStartTime = Date.now();\n        this.stats.startupAttempts++;\n        \n        this.logger.info('🚀 [Startup] Starting progressive system initialization:', {\n            totalComponents: this.components.size,\n            initializationOrder: this.initializationOrder,\n            timeout: this.startupTimeout\n        });\n        \n        this.initializationPromise = this._performSystemStartup();\n        \n        try {\n            const result = await this.initializationPromise;\n            return result;\n        } finally {\n            this.isInitializing = false;\n            this.initializationPromise = null;\n        }\n    }\n    \n    /**\n     * Perform the actual system startup\n     * @private\n     */\n    async _performSystemStartup() {\n        try {\n            // Initialize components in dependency order\n            for (const componentName of this.initializationOrder) {\n                const component = this.components.get(componentName);\n                const status = this.initializationStatus.get(componentName);\n                \n                if (status.status === 'initialized') {\n                    this.logger.debug(`🚀 [Startup] Component '${componentName}' already initialized`);\n                    continue;\n                }\n                \n                this.logger.info(`🚀 [Startup] Initializing component: ${componentName}`);\n                \n                const success = await this._initializeComponent(component);\n                \n                if (!success && component.criticalComponent) {\n                    const error = new Error(`Critical component '${componentName}' failed to initialize`);\n                    this._emit('systemFailed', { error, failedComponent: componentName });\n                    throw error;\n                }\n                \n                if (!success) {\n                    this.logger.warn(`🚀 [Startup] Non-critical component '${componentName}' failed to initialize, continuing...`);\n                }\n            }\n            \n            // All components initialized successfully\n            const totalStartupTime = Date.now() - this.startupStartTime;\n            this.stats.totalStartupTime = totalStartupTime;\n            \n            this.logger.info('🚀 [Startup] System initialization completed successfully:', {\n                totalTime: totalStartupTime + 'ms',\n                successfulComponents: Array.from(this.initializationStatus.entries())\n                    .filter(([, status]) => status.status === 'initialized').length,\n                failedComponents: Array.from(this.initializationStatus.entries())\n                    .filter(([, status]) => status.status === 'failed').length\n            });\n            \n            // Start health monitoring\n            this._startHealthMonitoring();\n            \n            this._emit('systemReady', {\n                totalStartupTime,\n                componentStatuses: this._getComponentStatuses()\n            });\n            \n            return true;\n            \n        } catch (error) {\n            const totalStartupTime = Date.now() - this.startupStartTime;\n            this.stats.totalStartupTime = totalStartupTime;\n            \n            this.logger.error('🚀 [Startup] System initialization failed:', {\n                error: error.message,\n                totalTime: totalStartupTime + 'ms',\n                componentStatuses: this._getComponentStatuses()\n            });\n            \n            throw error;\n        }\n    }\n    \n    /**\n     * Initialize a single component with retry logic\n     * @private\n     */\n    async _initializeComponent(component) {\n        const status = this.initializationStatus.get(component.name);\n        let attempt = 0;\n        \n        while (attempt < component.retryAttempts) {\n            try {\n                status.attempts++;\n                status.startTime = Date.now();\n                status.status = 'initializing';\n                \n                this.logger.debug(`🚀 [Startup] Initializing ${component.name} (attempt ${attempt + 1}/${component.retryAttempts})`);\n                \n                // Check dependencies are satisfied\n                const dependenciesReady = await this._checkDependencies(component.dependencies);\n                if (!dependenciesReady) {\n                    throw new Error(`Dependencies not satisfied for component '${component.name}'`);\n                }\n                \n                // Initialize with timeout\n                await Promise.race([\n                    component.initFunction(),\n                    new Promise((_, reject) => {\n                        setTimeout(() => {\n                            reject(new Error(`Initialization timeout for component '${component.name}'`));\n                        }, component.initTimeout);\n                    })\n                ]);\n                \n                // Verify initialization with health check if available\n                if (component.healthCheck) {\n                    const isHealthy = await component.healthCheck();\n                    if (!isHealthy) {\n                        throw new Error(`Health check failed for component '${component.name}'`);\n                    }\n                }\n                \n                // Success!\n                status.status = 'initialized';\n                status.endTime = Date.now();\n                status.initializationTime = status.endTime - status.startTime;\n                status.lastError = null;\n                \n                this.stats.successfulComponents++;\n                \n                this.logger.info(`🚀 [Startup] Successfully initialized '${component.name}':`, {\n                    initTime: status.initializationTime + 'ms',\n                    attempts: status.attempts\n                });\n                \n                this._emit('componentStarted', {\n                    componentName: component.name,\n                    initializationTime: status.initializationTime,\n                    attempts: status.attempts\n                });\n                \n                return true;\n                \n            } catch (error) {\n                attempt++;\n                status.lastError = error;\n                this.stats.retryAttempts++;\n                \n                this.logger.warn(`🚀 [Startup] Component '${component.name}' initialization failed (attempt ${attempt}):`, {\n                    error: error.message,\n                    willRetry: attempt < component.retryAttempts\n                });\n                \n                if (attempt < component.retryAttempts) {\n                    // Wait before retry with exponential backoff\n                    const retryDelay = this.retryDelayMs * Math.pow(2, attempt - 1);\n                    this.logger.debug(`🚀 [Startup] Retrying '${component.name}' in ${retryDelay}ms`);\n                    await new Promise(resolve => setTimeout(resolve, retryDelay));\n                }\n            }\n        }\n        \n        // All attempts failed\n        status.status = 'failed';\n        status.endTime = Date.now();\n        status.initializationTime = status.endTime - status.startTime;\n        this.stats.failedComponents++;\n        \n        this.logger.error(`🚀 [Startup] Component '${component.name}' failed to initialize after ${attempt} attempts:`, {\n            lastError: status.lastError?.message,\n            isCritical: component.criticalComponent\n        });\n        \n        this._emit('componentFailed', {\n            componentName: component.name,\n            error: status.lastError,\n            attempts: status.attempts,\n            isCritical: component.criticalComponent\n        });\n        \n        return false;\n    }\n    \n    /**\n     * Check if all dependencies are satisfied\n     * @private\n     */\n    async _checkDependencies(dependencies) {\n        for (const depName of dependencies) {\n            const depStatus = this.initializationStatus.get(depName);\n            \n            if (!depStatus || depStatus.status !== 'initialized') {\n                this.logger.debug(`🚀 [Dependencies] Dependency '${depName}' not ready:`, {\n                    status: depStatus?.status || 'not-registered'\n                });\n                return false;\n            }\n            \n            // If dependency has health check, verify it's still healthy\n            const depComponent = this.components.get(depName);\n            if (depComponent?.healthCheck) {\n                try {\n                    const isHealthy = await depComponent.healthCheck();\n                    if (!isHealthy) {\n                        this.logger.warn(`🚀 [Dependencies] Dependency '${depName}' failed health check`);\n                        return false;\n                    }\n                } catch (error) {\n                    this.logger.warn(`🚀 [Dependencies] Health check error for '${depName}':`, error.message);\n                    return false;\n                }\n            }\n        }\n        \n        return true;\n    }\n    \n    /**\n     * Calculate the initialization order based on dependencies\n     * @private\n     */\n    _calculateInitializationOrder() {\n        const visited = new Set();\n        const visiting = new Set();\n        const order = [];\n        \n        const visit = (componentName) => {\n            if (visiting.has(componentName)) {\n                throw new Error(`Circular dependency detected involving component '${componentName}'`);\n            }\n            \n            if (visited.has(componentName)) {\n                return;\n            }\n            \n            visiting.add(componentName);\n            \n            const component = this.components.get(componentName);\n            if (component) {\n                for (const depName of component.dependencies) {\n                    if (!this.components.has(depName)) {\n                        throw new Error(`Unknown dependency '${depName}' for component '${componentName}'`);\n                    }\n                    visit(depName);\n                }\n            }\n            \n            visiting.delete(componentName);\n            visited.add(componentName);\n            order.push(componentName);\n        };\n        \n        // Visit all components\n        for (const componentName of this.components.keys()) {\n            if (!visited.has(componentName)) {\n                visit(componentName);\n            }\n        }\n        \n        this.initializationOrder = order;\n        \n        this.logger.debug('🚀 [Dependencies] Calculated initialization order:', {\n            order: this.initializationOrder,\n            totalComponents: this.components.size\n        });\n    }\n    \n    /**\n     * Start health monitoring for initialized components\n     * @private\n     */\n    _startHealthMonitoring() {\n        if (this.healthCheckTimer) {\n            clearInterval(this.healthCheckTimer);\n        }\n        \n        this.healthCheckTimer = setInterval(async () => {\n            await this._performHealthChecks();\n        }, this.healthCheckInterval);\n        \n        this.logger.debug('🚀 [Health] Started health monitoring:', {\n            interval: this.healthCheckInterval\n        });\n    }\n    \n    /**\n     * Perform health checks on all initialized components\n     * @private\n     */\n    async _performHealthChecks() {\n        const healthCheckPromises = [];\n        \n        for (const [componentName, component] of this.components.entries()) {\n            const status = this.initializationStatus.get(componentName);\n            \n            if (status.status === 'initialized' && component.healthCheck) {\n                healthCheckPromises.push(\n                    this._checkComponentHealth(componentName, component)\n                        .catch(error => ({ componentName, error }))\n                );\n            }\n        }\n        \n        if (healthCheckPromises.length === 0) {\n            return;\n        }\n        \n        const results = await Promise.allSettled(healthCheckPromises);\n        \n        const failedChecks = results\n            .filter(result => result.status === 'rejected' || result.value?.error)\n            .map(result => result.value || result.reason);\n        \n        if (failedChecks.length > 0) {\n            this.logger.warn('🚀 [Health] Health check failures detected:', {\n                failedComponents: failedChecks.map(f => f.componentName || 'unknown'),\n                totalChecked: healthCheckPromises.length\n            });\n            \n            this._emit('healthCheckFailed', { failedChecks });\n        }\n    }\n    \n    /**\n     * Check health of individual component\n     * @private\n     */\n    async _checkComponentHealth(componentName, component) {\n        try {\n            const isHealthy = await component.healthCheck();\n            if (!isHealthy) {\n                throw new Error(`Health check returned false for component '${componentName}'`);\n            }\n        } catch (error) {\n            this.logger.warn(`🚀 [Health] Component '${componentName}' health check failed:`, error.message);\n            \n            // If auto-retry is enabled and this is a critical component, attempt restart\n            if (this.enableAutoRetry && component.criticalComponent) {\n                this.logger.info(`🚀 [Health] Attempting to restart critical component '${componentName}'`);\n                await this._restartComponent(componentName);\n            }\n            \n            throw error;\n        }\n    }\n    \n    /**\n     * Restart a failed component\n     * @private\n     */\n    async _restartComponent(componentName) {\n        const component = this.components.get(componentName);\n        const status = this.initializationStatus.get(componentName);\n        \n        status.status = 'pending';\n        status.attempts = 0;\n        status.lastError = null;\n        \n        this.logger.info(`🚀 [Restart] Restarting component '${componentName}'`);\n        \n        const success = await this._initializeComponent(component);\n        \n        if (success) {\n            this.logger.info(`🚀 [Restart] Successfully restarted component '${componentName}'`);\n        } else {\n            this.logger.error(`🚀 [Restart] Failed to restart component '${componentName}'`);\n        }\n        \n        return success;\n    }\n    \n    /**\n     * Emit event to registered handlers\n     * @private\n     */\n    _emit(eventType, data) {\n        const handlers = this.eventHandlers[eventType] || [];\n        \n        handlers.forEach(handler => {\n            try {\n                handler(data);\n            } catch (error) {\n                this.logger.error(`🚀 [Events] Error in ${eventType} handler:`, error);\n            }\n        });\n    }\n    \n    /**\n     * Get current component statuses\n     * @private\n     */\n    _getComponentStatuses() {\n        const statuses = {};\n        \n        for (const [componentName, status] of this.initializationStatus.entries()) {\n            statuses[componentName] = {\n                status: status.status,\n                attempts: status.attempts,\n                initializationTime: status.initializationTime,\n                lastError: status.lastError?.message || null\n            };\n        }\n        \n        return statuses;\n    }\n    \n    /**\n     * Get current system status\n     */\n    getStatus() {\n        const componentStatuses = this._getComponentStatuses();\n        const totalComponents = this.components.size;\n        const initializedComponents = Object.values(componentStatuses)\n            .filter(status => status.status === 'initialized').length;\n        const failedComponents = Object.values(componentStatuses)\n            .filter(status => status.status === 'failed').length;\n        \n        return {\n            system: {\n                isInitializing: this.isInitializing,\n                isReady: initializedComponents === totalComponents && failedComponents === 0,\n                startupTime: this.isInitializing ? Date.now() - this.startupStartTime : this.stats.totalStartupTime,\n                healthMonitoring: !!this.healthCheckTimer\n            },\n            components: {\n                total: totalComponents,\n                initialized: initializedComponents,\n                failed: failedComponents,\n                pending: totalComponents - initializedComponents - failedComponents\n            },\n            statistics: this.stats,\n            componentDetails: componentStatuses,\n            initializationOrder: this.initializationOrder\n        };\n    }\n    \n    /**\n     * Stop health monitoring\n     */\n    stopHealthMonitoring() {\n        if (this.healthCheckTimer) {\n            clearInterval(this.healthCheckTimer);\n            this.healthCheckTimer = null;\n            this.logger.debug('🚀 [Health] Stopped health monitoring');\n        }\n    }\n    \n    /**\n     * Shutdown the system gracefully\n     */\n    async shutdown() {\n        this.logger.info('🚀 [Shutdown] Starting graceful system shutdown');\n        \n        this.stopHealthMonitoring();\n        \n        // Reset component statuses\n        for (const [componentName, status] of this.initializationStatus.entries()) {\n            if (status.status === 'initialized') {\n                status.status = 'pending';\n            }\n        }\n        \n        this.logger.info('🚀 [Shutdown] System shutdown completed');\n    }\n    \n    /**\n     * Dispose of resources and cleanup\n     */\n    dispose() {\n        this.stopHealthMonitoring();\n        \n        this.components.clear();\n        this.initializationStatus.clear();\n        this.initializationOrder = [];\n        \n        // Clear event handlers\n        Object.keys(this.eventHandlers).forEach(key => {\n            this.eventHandlers[key] = [];\n        });\n        \n        this.logger.info('🚀 ProgressiveSystemStartup disposed');\n    }\n}\n\nexport default ProgressiveSystemStartup;