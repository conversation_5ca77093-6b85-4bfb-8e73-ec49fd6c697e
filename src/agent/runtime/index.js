/**
 * Runtime Recovery Components Export
 * Centralized export for all runtime recovery and error handling components
 */

export { AudioStreamingCoordinator } from './AudioStreamingCoordinator.js';
export { RuntimeErrorRecovery, CircuitBreakerOpenError, RetryExhaustedError, TimeoutError } from './RuntimeErrorRecovery.js';
export { VADEventBuffer } from './VADEventBuffer.js';
export { ProgressiveSystemStartup } from './ProgressiveSystemStartup.js';

/**
 * Create a complete runtime recovery system
 * @param {Object} options - Configuration options for all components
 * @returns {Object} Configured runtime recovery system
 */
export function createRuntimeRecoverySystem(options = {}) {
    const {
        audio = {},
        errorRecovery = {},
        vadBuffer = {},
        systemStartup = {},
        logger = console
    } = options;

    const components = {
        audioCoordinator: new AudioStreamingCoordinator({
            logger,
            ...audio
        }),
        
        errorRecovery: new RuntimeErrorRecovery({
            logger,
            ...errorRecovery
        }),
        
        vadBuffer: new VADEventBuffer({
            logger,
            ...vadBuffer
        }),
        
        systemStartup: new ProgressiveSystemStartup({
            logger,
            ...systemStartup
        })
    };

    // Cross-component integration
    const system = {
        ...components,
        
        /**
         * Start a coordinated 1011 reconnection recovery
         */
        async startReconnectionRecovery() {
            logger.info('🔄 Starting coordinated reconnection recovery');
            
            // Start buffering in both audio and VAD systems
            components.audioCoordinator.startBuffering();
            components.vadBuffer.startBuffering();
            
            return {
                stopRecovery: async (shouldReplay = true) => {
                    logger.info('✅ Stopping reconnection recovery');
                    
                    // Stop buffering and process/replay
                    await Promise.all([
                        components.audioCoordinator.stopBuffering(shouldReplay),
                        components.vadBuffer.stopBuffering()
                    ]);
                    
                    logger.info('✅ Reconnection recovery completed');
                }
            };
        },
        
        /**
         * Get comprehensive system status
         */
        getSystemStatus() {
            return {
                audio: components.audioCoordinator.getBufferStatus(),
                errorRecovery: components.errorRecovery.getStatus(),
                vadBuffer: components.vadBuffer.getStatus(),
                systemStartup: components.systemStartup.getStatus(),
                timestamp: Date.now()
            };
        },
        
        /**
         * Perform comprehensive health checks
         */
        async performHealthChecks() {
            const results = {};
            
            try {
                results.errorRecovery = await components.errorRecovery.performHealthChecks();
            } catch (error) {
                results.errorRecovery = { error: error.message };
            }
            
            results.audio = components.audioCoordinator.getPerformanceStats();
            results.vadBuffer = components.vadBuffer.getStatus();
            results.systemStartup = components.systemStartup.getStatus();
            
            return results;
        },
        
        /**
         * Dispose of all components
         */
        async dispose() {
            logger.info('🧹 Disposing runtime recovery system');
            
            await Promise.all([
                components.audioCoordinator.dispose?.(),
                components.errorRecovery.dispose?.(),
                components.vadBuffer.dispose?.(),
                components.systemStartup.dispose?.()
            ]);
            
            logger.info('✅ Runtime recovery system disposed');
        }
    };

    logger.info('🚀 Runtime recovery system created:', {
        components: Object.keys(components),
        hasIntegratedRecovery: true
    });

    return system;
}

/**
 * Default configuration for runtime recovery system
 */
export const DEFAULT_RUNTIME_CONFIG = {
    audio: {
        bufferSize: 50,
        replayEnabled: true,
        maxReplayDelayMs: 10000
    },
    errorRecovery: {
        maxRetries: 3,
        baseBackoffMs: 1000,
        circuitBreakerThreshold: 5,
        maxPoolSize: 3
    },
    vadBuffer: {
        bufferSize: 100,
        flushInterval: 500,
        maxEventAge: 30000,
        retryAttempts: 3
    },
    systemStartup: {
        maxRetries: 3,
        retryDelayMs: 2000,
        healthCheckInterval: 5000,
        startupTimeout: 60000
    }
};

export default {
    AudioStreamingCoordinator,
    RuntimeErrorRecovery,
    VADEventBuffer,
    ProgressiveSystemStartup,
    createRuntimeRecoverySystem,
    DEFAULT_RUNTIME_CONFIG
};