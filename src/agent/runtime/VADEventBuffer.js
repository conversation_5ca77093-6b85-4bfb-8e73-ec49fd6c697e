/**
 * VAD Event Buffering System
 * Prevents loss of Voice Activity Detection events during reconnections
 * Implements buffering, queuing, and reliable delivery of VAD events
 */

import { createLogger } from '../../utils/logger.js';

export class VADEventBuffer {
    constructor(options = {}) {
        this.logger = options.logger || createLogger('VADEventBuffer');
        this.bufferSize = options.bufferSize || 100; // Buffer up to 100 VAD events
        this.enablePersistence = options.enablePersistence !== false;
        this.flushInterval = options.flushInterval || 500; // 500ms
        this.maxEventAge = options.maxEventAge || 30000; // 30 seconds
        this.retryAttempts = options.retryAttempts || 3;
        
        // Event buffer
        this.eventBuffer = [];
        this.processingQueue = [];
        this.failedEvents = [];
        
        // State management
        this.isBuffering = false;
        this.isProcessing = false;
        this.lastFlushTime = 0;
        this.eventHandlers = new Map();
        
        // Statistics
        this.stats = {
            totalEvents: 0,
            bufferedEvents: 0,
            processedEvents: 0,
            failedEvents: 0,
            duplicateEvents: 0,
            expiredEvents: 0
        };
        
        // Auto-flush timer
        this.flushTimer = null;
        this.startAutoFlush();
        
        this.logger.debug('🎙️ VADEventBuffer initialized:', {
            bufferSize: this.bufferSize,
            flushInterval: this.flushInterval,
            maxEventAge: this.maxEventAge,
            enablePersistence: this.enablePersistence
        });
    }
    
    /**
     * Register event handler for specific VAD event types
     */
    registerHandler(eventType, handler) {
        if (!this.eventHandlers.has(eventType)) {
            this.eventHandlers.set(eventType, []);
        }
        
        this.eventHandlers.get(eventType).push(handler);
        
        this.logger.debug(`🎙️ [VADBuffer] Registered handler for ${eventType}:`, {
            handlersCount: this.eventHandlers.get(eventType).length
        });
    }
    
    /**
     * Remove event handler
     */
    removeHandler(eventType, handler) {
        if (this.eventHandlers.has(eventType)) {
            const handlers = this.eventHandlers.get(eventType);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
                this.logger.debug(`🎙️ [VADBuffer] Removed handler for ${eventType}`);
            }
        }
    }
    
    /**
     * Start buffering VAD events
     */
    startBuffering() {
        if (this.isBuffering) {
            this.logger.debug('🎙️ [VADBuffer] Already buffering VAD events');
            return;
        }
        
        this.isBuffering = true;
        this.eventBuffer = [];
        
        this.logger.info('🎙️ [VADBuffer] Started buffering VAD events:', {
            bufferSize: this.bufferSize,
            currentTime: new Date().toISOString()
        });
    }
    
    /**
     * Stop buffering and process all buffered events
     */
    async stopBuffering() {
        if (!this.isBuffering) {
            this.logger.debug('🎙️ [VADBuffer] Not currently buffering');
            return;
        }
        
        this.isBuffering = false;
        const bufferedCount = this.eventBuffer.length;
        
        this.logger.info('🎙️ [VADBuffer] Stopped buffering, processing events:', {
            bufferedEvents: bufferedCount,
            currentTime: new Date().toISOString()
        });
        
        if (bufferedCount > 0) {
            await this.flushBuffer();
        }
    }
    
    /**
     * Buffer a VAD event
     */
    bufferEvent(eventType, eventData, metadata = {}) {
        this.stats.totalEvents++;
        
        const event = {
            id: this._generateEventId(),
            type: eventType,
            data: eventData,
            metadata: {
                ...metadata,
                timestamp: Date.now(),
                bufferedAt: Date.now(),
                source: 'vad-buffer'
            },
            retryCount: 0,
            processed: false
        };
        
        // Check for duplicates (within last 1 second)
        const isDuplicate = this.eventBuffer.some(bufferedEvent => 
            bufferedEvent.type === eventType &&
            Math.abs(bufferedEvent.metadata.timestamp - event.metadata.timestamp) < 1000 &&
            JSON.stringify(bufferedEvent.data) === JSON.stringify(eventData)
        );
        
        if (isDuplicate) {
            this.stats.duplicateEvents++;
            this.logger.debug('🎙️ [VADBuffer] Duplicate event detected, skipping:', {
                eventType,
                eventId: event.id
            });
            return false;
        }
        
        if (this.isBuffering) {
            // Add to buffer
            this.eventBuffer.push(event);
            this.stats.bufferedEvents++;
            
            // Maintain buffer size
            if (this.eventBuffer.length > this.bufferSize) {
                const removed = this.eventBuffer.shift();
                this.logger.warn('🎙️ [VADBuffer] Buffer overflow, removed oldest event:', {
                    removedEventId: removed.id,
                    removedEventType: removed.type
                });
            }
            
            this.logger.debug('🎙️ [VADBuffer] Buffered VAD event:', {
                eventType,
                eventId: event.id,
                bufferSize: this.eventBuffer.length,
                metadata: event.metadata
            });
            
            return true;
        } else {
            // Process immediately if not buffering
            return this._processEventImmediately(event);
        }
    }
    
    /**
     * Process event immediately (when not buffering)
     * @private
     */
    async _processEventImmediately(event) {
        try {
            await this._deliverEvent(event);
            this.stats.processedEvents++;
            
            this.logger.debug('🎙️ [VADBuffer] Processed event immediately:', {
                eventType: event.type,
                eventId: event.id
            });
            
            return true;
        } catch (error) {
            this.logger.error('🎙️ [VADBuffer] Failed to process event immediately:', {
                eventType: event.type,
                eventId: event.id,
                error: error.message
            });
            
            // Add to failed events for retry
            this.failedEvents.push(event);
            this.stats.failedEvents++;
            return false;
        }
    }
    
    /**
     * Flush all buffered events
     */
    async flushBuffer() {
        if (this.isProcessing) {
            this.logger.debug('🎙️ [VADBuffer] Already processing, skipping flush');
            return;
        }
        
        if (this.eventBuffer.length === 0 && this.failedEvents.length === 0) {
            return;
        }
        
        this.isProcessing = true;
        this.lastFlushTime = Date.now();
        
        const totalEvents = this.eventBuffer.length + this.failedEvents.length;
        this.logger.info('🎙️ [VADBuffer] Starting flush operation:', {
            bufferedEvents: this.eventBuffer.length,
            failedEvents: this.failedEvents.length,
            totalEvents
        });
        
        try {
            // Process buffered events first
            await this._processEventBatch(this.eventBuffer, 'buffered');
            
            // Process failed events with retry logic
            await this._processEventBatch(this.failedEvents, 'retry');
            
            // Clear processed events
            this.eventBuffer = [];
            this.failedEvents = [];
            
            this.logger.info('🎙️ [VADBuffer] Flush operation completed:', {
                processedEvents: totalEvents,
                remainingFailed: this.failedEvents.length
            });
            
        } catch (error) {
            this.logger.error('🎙️ [VADBuffer] Error during flush operation:', error);
        } finally {
            this.isProcessing = false;
        }
    }
    
    /**
     * Process a batch of events
     * @private
     */
    async _processEventBatch(events, batchType) {
        const processedEvents = [];
        const failedEvents = [];
        
        for (const event of events) {
            try {
                // Check if event has expired
                if (this._isEventExpired(event)) {
                    this.stats.expiredEvents++;
                    this.logger.debug('🎙️ [VADBuffer] Event expired, skipping:', {
                        eventId: event.id,
                        eventType: event.type,
                        age: Date.now() - event.metadata.timestamp
                    });
                    continue;
                }
                
                await this._deliverEvent(event);
                processedEvents.push(event);
                this.stats.processedEvents++;
                
                this.logger.debug('🎙️ [VADBuffer] Successfully processed event:', {
                    eventType: event.type,
                    eventId: event.id,
                    batchType,
                    retryCount: event.retryCount
                });
                
            } catch (error) {
                event.retryCount = (event.retryCount || 0) + 1;
                
                if (event.retryCount <= this.retryAttempts) {
                    failedEvents.push(event);
                    this.logger.warn('🎙️ [VADBuffer] Event delivery failed, will retry:', {
                        eventId: event.id,
                        eventType: event.type,
                        retryCount: event.retryCount,
                        maxRetries: this.retryAttempts,
                        error: error.message
                    });
                } else {
                    this.stats.failedEvents++;
                    this.logger.error('🎙️ [VADBuffer] Event delivery failed permanently:', {
                        eventId: event.id,
                        eventType: event.type,
                        retryCount: event.retryCount,
                        error: error.message
                    });
                }
            }
        }
        
        // Update failed events list if this was processing failed events
        if (batchType === 'retry') {
            this.failedEvents = failedEvents;
        } else {
            // Add new failures to the failed events list
            this.failedEvents.push(...failedEvents);
        }
        
        this.logger.debug('🎙️ [VADBuffer] Batch processing completed:', {
            batchType,
            totalEvents: events.length,
            processedEvents: processedEvents.length,
            failedEvents: failedEvents.length
        });
    }
    
    /**
     * Deliver event to registered handlers
     * @private
     */
    async _deliverEvent(event) {
        const handlers = this.eventHandlers.get(event.type) || [];
        
        if (handlers.length === 0) {
            this.logger.warn('🎙️ [VADBuffer] No handlers registered for event type:', {
                eventType: event.type,
                eventId: event.id
            });
            // Don't throw error - this is not a delivery failure
            return;
        }
        
        const deliveryPromises = handlers.map(async (handler, index) => {
            try {
                await handler(event.data, event.metadata);
                this.logger.debug(`🎙️ [VADBuffer] Handler ${index + 1} processed event successfully`);
            } catch (error) {
                this.logger.error(`🎙️ [VADBuffer] Handler ${index + 1} failed:`, {
                    eventType: event.type,
                    eventId: event.id,
                    handlerIndex: index,
                    error: error.message
                });
                throw error; // Re-throw to trigger retry logic
            }
        });
        
        // Wait for all handlers to complete
        await Promise.all(deliveryPromises);
        
        event.processed = true;
        event.metadata.processedAt = Date.now();
    }
    
    /**
     * Check if event has expired
     * @private
     */
    _isEventExpired(event) {
        const age = Date.now() - event.metadata.timestamp;
        return age > this.maxEventAge;
    }
    
    /**
     * Generate unique event ID
     * @private
     */
    _generateEventId() {
        return `vad_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * Start automatic flushing
     * @private
     */
    startAutoFlush() {
        if (this.flushTimer) {
            clearInterval(this.flushTimer);
        }
        
        this.flushTimer = setInterval(async () => {
            if (!this.isProcessing && (this.eventBuffer.length > 0 || this.failedEvents.length > 0)) {
                this.logger.debug('🎙️ [VADBuffer] Auto-flush triggered');
                await this.flushBuffer();
            }
        }, this.flushInterval);
        
        this.logger.debug('🎙️ [VADBuffer] Auto-flush started:', {
            interval: this.flushInterval
        });
    }
    
    /**
     * Stop automatic flushing
     */
    stopAutoFlush() {
        if (this.flushTimer) {
            clearInterval(this.flushTimer);
            this.flushTimer = null;
            this.logger.debug('🎙️ [VADBuffer] Auto-flush stopped');
        }
    }
    
    /**
     * Get buffer status and statistics
     */
    getStatus() {
        return {
            buffering: {
                isBuffering: this.isBuffering,
                isProcessing: this.isProcessing,
                bufferSize: this.eventBuffer.length,
                maxBufferSize: this.bufferSize,
                failedEventsCount: this.failedEvents.length,
                lastFlushTime: this.lastFlushTime,
                nextFlushIn: this.flushTimer ? this.flushInterval - (Date.now() - this.lastFlushTime) : 0
            },
            statistics: {
                ...this.stats,
                successRate: this.stats.totalEvents > 0 
                    ? ((this.stats.processedEvents / this.stats.totalEvents) * 100).toFixed(2) + '%'
                    : '0%',
                bufferUtilization: ((this.eventBuffer.length / this.bufferSize) * 100).toFixed(1) + '%'
            },
            handlers: {
                registeredTypes: Array.from(this.eventHandlers.keys()),
                totalHandlers: Array.from(this.eventHandlers.values()).reduce((sum, handlers) => sum + handlers.length, 0),
                handlersPerType: Object.fromEntries(
                    Array.from(this.eventHandlers.entries()).map(([type, handlers]) => [type, handlers.length])
                )
            },
            health: {
                isHealthy: this.stats.failedEvents < this.stats.totalEvents * 0.1, // Less than 10% failure rate
                oldestBufferedEvent: this.eventBuffer.length > 0 
                    ? Date.now() - this.eventBuffer[0].metadata.timestamp 
                    : 0,
                expiredEventsRatio: this.stats.totalEvents > 0 
                    ? (this.stats.expiredEvents / this.stats.totalEvents * 100).toFixed(2) + '%'
                    : '0%'
            }
        };
    }
    
    /**
     * Get recent event history for debugging
     */
    getEventHistory(limit = 10) {
        const recentEvents = [...this.eventBuffer, ...this.failedEvents]
            .sort((a, b) => b.metadata.timestamp - a.metadata.timestamp)
            .slice(0, limit);
            
        return recentEvents.map(event => ({
            id: event.id,
            type: event.type,
            timestamp: event.metadata.timestamp,
            bufferedAt: event.metadata.bufferedAt,
            processedAt: event.metadata.processedAt,
            retryCount: event.retryCount,
            processed: event.processed,
            age: Date.now() - event.metadata.timestamp,
            dataPreview: typeof event.data === 'object' 
                ? Object.keys(event.data).join(', ')
                : String(event.data).substring(0, 50)
        }));
    }
    
    /**
     * Clear all buffers and reset statistics
     */
    clear() {
        this.eventBuffer = [];
        this.failedEvents = [];
        this.processingQueue = [];
        
        this.stats = {
            totalEvents: 0,
            bufferedEvents: 0,
            processedEvents: 0,
            failedEvents: 0,
            duplicateEvents: 0,
            expiredEvents: 0
        };
        
        this.logger.info('🎙️ [VADBuffer] Cleared all buffers and reset statistics');
    }
    
    /**
     * Dispose of resources and cleanup
     */
    dispose() {
        this.stopAutoFlush();
        this.clear();
        this.eventHandlers.clear();
        
        this.logger.info('🎙️ VADEventBuffer disposed');
    }
}