/**
 * Audio Streaming Coordinator for reliable audio handling during reconnections
 * Provides buffering and replay capabilities to prevent audio loss during 1011 reconnections
 */

import { createLogger } from '../../utils/logger.js';

export class AudioStreamingCoordinator {
    constructor(options = {}) {
        this.logger = options.logger || createLogger('AudioStreamingCoordinator');
        this.bufferSize = options.bufferSize || 50; // Buffer up to 50 audio chunks
        this.replayEnabled = options.replayEnabled !== false;
        this.maxReplayDelayMs = options.maxReplayDelayMs || 10000; // 10 seconds max replay
        
        // Audio buffering state
        this.audioBuffer = [];
        this.isBuffering = false;
        this.lastAudioTimestamp = 0;
        
        // Reconnection state
        this.isReconnecting = false;
        this.reconnectionStartTime = 0;
        this.reconnectionCount = 0;
        
        // Event handlers
        this.eventHandlers = {
            audioReplay: [],
            bufferOverflow: [],
            reconnectionStart: [],
            reconnectionEnd: []
        };
        
        this.logger.debug('🔊 AudioStreamingCoordinator initialized:', {
            bufferSize: this.bufferSize,
            replayEnabled: this.replayEnabled,
            maxReplayDelayMs: this.maxReplayDelayMs
        });
    }
    
    /**
     * Register event handler
     */
    on(eventType, handler) {
        if (this.eventHandlers[eventType]) {
            this.eventHandlers[eventType].push(handler);
            this.logger.debug(`📝 Registered handler for ${eventType} events`);
        } else {
            this.logger.warn(`⚠️ Unknown event type: ${eventType}`);
        }
    }
    
    /**
     * Emit event to registered handlers
     * @private
     */
    _emit(eventType, data) {
        if (this.eventHandlers[eventType]) {
            this.eventHandlers[eventType].forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    this.logger.error(`❌ Error in ${eventType} handler:`, error);
                }
            });
        }
    }
    
    /**
     * Start buffering audio during reconnection
     */
    startBuffering() {
        if (this.isBuffering) {
            this.logger.debug('🔊 Audio buffering already active');
            return;
        }
        
        this.isBuffering = true;
        this.isReconnecting = true;
        this.reconnectionStartTime = Date.now();
        this.reconnectionCount++;
        this.audioBuffer = [];
        
        this.logger.info('🔊 [AudioBuffer] Started audio buffering during reconnection:', {
            reconnectionCount: this.reconnectionCount,
            bufferSize: this.bufferSize
        });
        
        this._emit('reconnectionStart', {
            timestamp: this.reconnectionStartTime,
            reconnectionCount: this.reconnectionCount
        });
    }
    
    /**
     * Stop buffering and optionally replay buffered audio
     */
    async stopBuffering(shouldReplay = true) {
        if (!this.isBuffering) {
            this.logger.debug('🔊 Audio buffering not active');
            return;
        }
        
        this.isBuffering = false;
        const bufferSize = this.audioBuffer.length;
        const reconnectionDuration = Date.now() - this.reconnectionStartTime;
        
        this.logger.info('🔊 [AudioBuffer] Stopped buffering:', {
            bufferedChunks: bufferSize,
            reconnectionDuration,
            willReplay: shouldReplay && this.replayEnabled && bufferSize > 0,
            reconnectionCount: this.reconnectionCount
        });
        
        // Check if replay should be skipped due to excessive delay
        const shouldSkipReplay = reconnectionDuration > this.maxReplayDelayMs;
        if (shouldSkipReplay && bufferSize > 0) {
            this.logger.warn('⚠️ [AudioBuffer] Skipping replay due to excessive delay:', {
                reconnectionDuration,
                maxReplayDelayMs: this.maxReplayDelayMs,
                bufferedChunks: bufferSize
            });
        }
        
        if (shouldReplay && this.replayEnabled && bufferSize > 0 && !shouldSkipReplay) {
            await this._replayBufferedAudio();
        }
        
        // Clean up
        this.audioBuffer = [];
        this.isReconnecting = false;
        
        this._emit('reconnectionEnd', {
            reconnectionDuration,
            bufferedChunks: bufferSize,
            replayExecuted: shouldReplay && this.replayEnabled && bufferSize > 0 && !shouldSkipReplay
        });
    }
    
    /**
     * Buffer audio chunk during reconnection
     */
    bufferAudio(audioData, metadata = {}) {
        if (!this.isBuffering) {
            return false;
        }
        
        const chunk = {
            audioData,
            metadata,
            timestamp: Date.now(),
            bufferedAt: Date.now(),
            chunkId: `chunk_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        };
        
        this.audioBuffer.push(chunk);
        
        // Maintain buffer size limit
        if (this.audioBuffer.length > this.bufferSize) {
            const removed = this.audioBuffer.shift();
            this.logger.debug('🔊 [AudioBuffer] Buffer full, removed oldest chunk:', {
                removedChunkId: removed.chunkId,
                removedTimestamp: removed.timestamp,
                bufferSize: this.audioBuffer.length
            });
            
            this._emit('bufferOverflow', {
                removedChunk: removed,
                currentBufferSize: this.audioBuffer.length
            });
        }
        
        this.lastAudioTimestamp = Date.now();
        
        this.logger.debug('🔊 [AudioBuffer] Buffered audio chunk:', {
            chunkId: chunk.chunkId,
            bufferSize: this.audioBuffer.length,
            metadata: metadata
        });
        
        return true;
    }
    
    /**
     * Replay buffered audio after reconnection
     * @private
     */
    async _replayBufferedAudio() {
        if (this.audioBuffer.length === 0) {
            return;
        }
        
        const totalChunks = this.audioBuffer.length;
        const firstTimestamp = this.audioBuffer[0].timestamp;
        const lastTimestamp = this.audioBuffer[totalChunks - 1].timestamp;
        const totalDuration = lastTimestamp - firstTimestamp;
        
        this.logger.info('🔊 [AudioReplay] Starting replay of buffered audio:', {
            chunks: totalChunks,
            totalDuration,
            firstChunkId: this.audioBuffer[0].chunkId,
            lastChunkId: this.audioBuffer[totalChunks - 1].chunkId
        });
        
        // Calculate optimal replay timing
        const replayInterval = Math.min(Math.max(totalDuration / totalChunks, 5), 50); // 5-50ms per chunk
        
        for (let i = 0; i < this.audioBuffer.length; i++) {
            const chunk = this.audioBuffer[i];
            
            try {
                const replayData = {
                    audioData: chunk.audioData,
                    metadata: chunk.metadata,
                    originalTimestamp: chunk.timestamp,
                    replayIndex: i,
                    totalChunks: totalChunks,
                    chunkId: chunk.chunkId,
                    isReplay: true
                };
                
                this._emit('audioReplay', replayData);
                
                this.logger.debug('🔊 [AudioReplay] Replayed chunk:', {
                    chunkId: chunk.chunkId,
                    replayIndex: i,
                    totalChunks: totalChunks
                });
                
                // Controlled delay to prevent overwhelming the system
                if (i < this.audioBuffer.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, replayInterval));
                }
                
            } catch (error) {
                this.logger.error('❌ [AudioReplay] Error replaying chunk:', {
                    chunkId: chunk.chunkId,
                    error: error.message
                });
            }
        }
        
        this.logger.info('✅ [AudioReplay] Completed replay of buffered audio:', {
            totalChunks,
            replayInterval,
            totalReplayTime: totalChunks * replayInterval
        });
    }
    
    /**
     * Handle audio data - either buffer or pass through
     */
    handleAudioData(audioData, metadata = {}) {
        if (this.isBuffering) {
            return this.bufferAudio(audioData, metadata);
        } else {
            // Pass through normally - not during reconnection
            this.lastAudioTimestamp = Date.now();
            return false; // Indicates not buffered
        }
    }
    
    /**
     * Get current buffer status
     */
    getBufferStatus() {
        return {
            isBuffering: this.isBuffering,
            isReconnecting: this.isReconnecting,
            bufferSize: this.audioBuffer.length,
            maxBufferSize: this.bufferSize,
            lastAudioTimestamp: this.lastAudioTimestamp,
            reconnectionDuration: this.isReconnecting ? Date.now() - this.reconnectionStartTime : 0,
            reconnectionCount: this.reconnectionCount,
            oldestChunkAge: this.audioBuffer.length > 0 ? Date.now() - this.audioBuffer[0].timestamp : 0,
            newestChunkAge: this.audioBuffer.length > 0 ? Date.now() - this.audioBuffer[this.audioBuffer.length - 1].timestamp : 0
        };
    }
    
    /**
     * Force clear buffer and reset state
     */
    clearBuffer() {
        const clearedChunks = this.audioBuffer.length;
        this.audioBuffer = [];
        this.isBuffering = false;
        this.isReconnecting = false;
        this.lastAudioTimestamp = 0;
        this.reconnectionStartTime = 0;
        
        this.logger.info('🔊 [AudioBuffer] Buffer manually cleared:', {
            clearedChunks,
            reconnectionCount: this.reconnectionCount
        });
    }
    
    /**
     * Get performance statistics
     */
    getPerformanceStats() {
        return {
            reconnectionCount: this.reconnectionCount,
            totalBufferCapacity: this.bufferSize,
            replayEnabled: this.replayEnabled,
            maxReplayDelayMs: this.maxReplayDelayMs,
            eventHandlerCounts: Object.keys(this.eventHandlers).reduce((acc, key) => {
                acc[key] = this.eventHandlers[key].length;
                return acc;
            }, {}),
            lastAudioTimestamp: this.lastAudioTimestamp,
            currentStatus: this.getBufferStatus()
        };
    }
    
    /**
     * Dispose of resources and cleanup
     */
    dispose() {
        this.clearBuffer();
        
        // Clear event handlers
        Object.keys(this.eventHandlers).forEach(key => {
            this.eventHandlers[key] = [];
        });
        
        this.logger.info('🔊 AudioStreamingCoordinator disposed');
    }
}

export default AudioStreamingCoordinator;