/**
 * BaseSessionCoordinator - Universal WebSocket Session Management
 * 
 * MISSION: Provide universal session coordination patterns for any WebSocket provider
 * 
 * This class extracts the common session coordination logic from provider-specific
 * implementations (like AliyunSessionCoordinator) into a reusable base that can
 * be extended by any WebSocket chat model provider.
 * 
 * KEY FEATURES:
 * - Provider-agnostic session lifecycle management
 * - Configurable VAD (Voice Activity Detection) handling
 * - Universal event mapping and coordination
 * - Session state management with recovery
 * - Audio stream coordination
 * - Pluggable provider configuration
 */

import { EventEmitter } from 'events';
import { createLogger } from '../../../utils/logger.ts';
import { ConnectionManager } from '../../services/connection/ConnectionManager.js';

// Universal session states
export const SessionState = {
  IDLE: 'IDLE',
  CREATING: 'CREATING',
  CREATED: 'CREATED',
  UPDATING: 'UPDATING',
  ACTIVE: 'ACTIVE',
  INTERRUPTED: 'INTERRUPTED',
  ERROR: 'ERROR',
  TERMINATED: 'TERMINATED'
};

// Universal event types that can be mapped to provider-specific events
export const BaseEventType = {
  SESSION_CREATED: 'session.created',
  SESSION_UPDATED: 'session.updated',
  SESSION_INTERRUPTED: 'session.interrupted',
  SESSION_READY: 'session.ready',
  CONVERSATION_CREATED: 'conversation.created',
  CONVERSATION_UPDATED: 'conversation.updated',
  VAD_SPEECH_STARTED: 'vad.speech.started',
  VAD_SPEECH_STOPPED: 'vad.speech.stopped',
  INPUT_AUDIO_BUFFER_COMMITTED: 'input_audio_buffer.committed',
  INPUT_AUDIO_BUFFER_CLEARED: 'input_audio_buffer.cleared',
  AUDIO_RESPONSE_GENERATED: 'response.audio.generated',
  TEXT_RESPONSE_GENERATED: 'response.text.generated',
  ERROR: 'error'
};

// Default VAD configuration (provider-agnostic)
export const DEFAULT_VAD_CONFIG = {
  threshold: 0.6,
  prefix_padding_ms: 300,
  silence_duration_ms: 200,
  min_speech_duration_ms: 500,
  auto_commit: true
};

/**
 * Universal Base Session Coordinator
 * Abstract base class for coordinating WebSocket sessions across providers
 */
export class BaseSessionCoordinator extends EventEmitter {

  constructor(options = {}) {
    super();

    // Core configuration
    this.provider = options.provider || 'universal';
    this.logger = createLogger(`BaseSessionCoordinator:${this.provider}`);

    // Session state
    this.sessionState = SessionState.IDLE;
    this.sessionId = null;
    this.conversationId = null;
    this.currentSession = null;

    // Provider configuration (pluggable)
    this.providerConfig = options.providerConfig || {};
    this.eventMappings = options.eventMappings || {};

    // VAD configuration (configurable per provider)
    this.vadConfig = { ...DEFAULT_VAD_CONFIG, ...options.vadConfig };

    // Connection management
    this.connectionManager = null;
    this.isConnected = false;

    // Session update functions (provider-specific)
    this.sessionUpdateHandler = options.sessionUpdateHandler || this._defaultSessionUpdate.bind(this);

    // Event handlers
    this.eventHandlers = new Map();

    // Session recovery
    this.lastKnownState = null;
    this.recoveryAttempts = 0;
    this.maxRecoveryAttempts = options.maxRecoveryAttempts || 3;

    // VAD speech tracking for enhanced context
    this.speechStartTime = null;

    this.logger.info('🎛️ BaseSessionCoordinator initialized', {
      provider: this.provider,
      vadConfig: this.vadConfig
    });
  }

  /**
   * Initialize connection manager and setup coordination
   * @param {Object} wsConfig - WebSocket configuration
   * @returns {Promise<boolean>} Success status
   */
  async initialize(wsConfig = {}) {
    try {
      this.logger.info('🔧 Initializing session coordinator...');

      // Get singleton ConnectionManager instance
      this.connectionManager = await ConnectionManager.getInstance(wsConfig);

      // Setup connection event handlers
      this._setupConnectionHandlers();

      this.logger.info('✅ Session coordinator initialized');
      return true;

    } catch (error) {
      this.logger.error('❌ Failed to initialize session coordinator:', error);
      return false;
    }
  }

  /**
   * Setup connection event handlers
   * @private
   */
  _setupConnectionHandlers() {
    if (!this.connectionManager) {
      return;
    }

    // Connection events
    this.connectionManager.on('connected', this._handleConnectionEstablished.bind(this));
    this.connectionManager.on('disconnected', this._handleConnectionLost.bind(this));
    this.connectionManager.on('message', this._handleProviderMessage.bind(this));
    this.connectionManager.on('error', this._handleConnectionError.bind(this));
    this.connectionManager.on('sessionReady', this._handleSessionReady.bind(this));
  }

  /**
   * Create a new session with provider-specific configuration
   * @param {Object} sessionConfig - Session configuration
   * @returns {Promise<Object>} Session creation result
   */
  async createSession(sessionConfig = {}) {
    if (this.sessionState !== SessionState.IDLE) {
      throw new Error(`Cannot create session from state: ${this.sessionState}`);
    }

    this._setState(SessionState.CREATING);

    try {
      this.logger.info('🎬 Creating new session...', {
        config: this._sanitizeConfig(sessionConfig)
      });

      // Build provider-specific session configuration
      const providerSessionConfig = this._buildProviderSessionConfig(sessionConfig);

      // Send session creation message
      const success = await this._sendSessionMessage('create', providerSessionConfig);

      if (success) {
        this.logger.info('✅ Session creation message sent');
        // State will be updated when we receive session.created event
        return { success: true, sessionConfig: providerSessionConfig };
      } else {
        this._setState(SessionState.ERROR);
        throw new Error('Failed to send session creation message');
      }

    } catch (error) {
      this.logger.error('❌ Session creation failed:', error);
      this._setState(SessionState.ERROR);
      throw error;
    }
  }

  /**
   * Update existing session configuration
   * @param {Object} updateConfig - Update configuration
   * @returns {Promise<boolean>} Success status
   */
  async updateSession(updateConfig = {}) {
    if (![SessionState.CREATED, SessionState.ACTIVE].includes(this.sessionState)) {
      throw new Error(`Cannot update session from state: ${this.sessionState}`);
    }

    this._setState(SessionState.UPDATING);

    try {
      this.logger.info('🔄 Updating session...', {
        sessionId: this.sessionId,
        updates: Object.keys(updateConfig)
      });

      // Apply session update handler (provider-specific logic)
      const providerUpdateData = await this.sessionUpdateHandler(updateConfig, this.currentSession);

      // Send update message
      const success = await this._sendSessionMessage('update', providerUpdateData);

      if (success) {
        this.logger.info('✅ Session update message sent');
        return true;
      } else {
        this._setState(SessionState.ERROR);
        throw new Error('Failed to send session update message');
      }

    } catch (error) {
      this.logger.error('❌ Session update failed:', error);
      this._setState(SessionState.ERROR);
      throw error;
    }
  }

  /**
   * Terminate current session
   * @returns {Promise<boolean>} Success status
   */
  async terminateSession() {
    if (this.sessionState === SessionState.TERMINATED || this.sessionState === SessionState.IDLE) {
      return true;
    }

    try {
      this.logger.info('🛑 Terminating session...', { sessionId: this.sessionId });

      // Send termination message if connected
      if (this.isConnected && this.connectionManager) {
        await this._sendSessionMessage('terminate', { sessionId: this.sessionId });
      }

      // Clean up session state
      this._cleanup();
      this._setState(SessionState.TERMINATED);

      this.logger.info('✅ Session terminated');
      return true;

    } catch (error) {
      this.logger.error('❌ Session termination failed:', error);
      this._setState(SessionState.ERROR);
      throw error;
    }
  }

  /**
   * Handle VAD events with configurable behavior
   * @param {string} vadEvent - VAD event type ('speech_started', 'speech_stopped', etc.)
   * @param {Object} vadData - VAD event data
   */
  handleVADEvent(vadEvent, vadData = {}) {
    this.logger.debug('🎤 VAD Event:', { event: vadEvent, data: vadData });

    try {
      switch (vadEvent) {
        case 'speech_started':
          this._handleSpeechStarted(vadData);
          break;
        case 'speech_stopped':
          this._handleSpeechStopped(vadData);
          break;
        case 'silence_detected':
          this._handleSilenceDetected(vadData);
          break;
        default:
          this.logger.warn('🤷 Unknown VAD event:', vadEvent);
      }

      // Emit universal VAD event
      this.emit('vad_event', {
        type: vadEvent,
        data: vadData,
        timestamp: Date.now()
      });

    } catch (error) {
      this.logger.error('❌ VAD event handling failed:', error);
    }
  }

  /**
   * Handle audio buffer operations
   * @param {string} operation - Operation type ('commit', 'clear', 'append')
   * @param {Object} bufferData - Buffer operation data
   */
  async handleAudioBuffer(operation, bufferData = {}) {
    if (!this.isSessionActive()) {
      this.logger.warn('❌ Cannot handle audio buffer: session not active');
      return false;
    }

    try {
      this.logger.debug('🔊 Audio Buffer Operation:', { operation, bufferData });

      const message = this._buildAudioBufferMessage(operation, bufferData);
      const success = await this._sendMessage(message);

      if (success) {
        this.emit('audio_buffer_operation', {
          operation,
          data: bufferData,
          timestamp: Date.now()
        });
      }

      return success;

    } catch (error) {
      this.logger.error('❌ Audio buffer operation failed:', error);
      return false;
    }
  }

  /**
   * Check if session is in active state
   * @returns {boolean} Active status
   */
  isSessionActive() {
    return this.sessionState === SessionState.ACTIVE && this.isConnected;
  }

  /**
   * Get current session information
   * @returns {Object} Session info
   */
  getSessionInfo() {
    return {
      sessionId: this.sessionId,
      conversationId: this.conversationId,
      state: this.sessionState,
      isConnected: this.isConnected,
      provider: this.provider,
      vadConfig: this.vadConfig,
      session: this.currentSession
    };
  }

  /**
   * Add event handler for specific event types
   * @param {string} eventType - Event type to handle
   * @param {Function} handler - Handler function
   */
  addEventHandler(eventType, handler) {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType).push(handler);

    this.logger.debug('📋 Added event handler:', { eventType, handlerCount: this.eventHandlers.get(eventType).length });
  }

  /**
   * Remove event handler
   * @param {string} eventType - Event type
   * @param {Function} handler - Handler function to remove
   */
  removeEventHandler(eventType, handler) {
    if (this.eventHandlers.has(eventType)) {
      const handlers = this.eventHandlers.get(eventType);
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  // === PROTECTED METHODS (to be overridden by provider-specific subclasses) ===

  /**
   * Build provider-specific session configuration
   * @param {Object} baseConfig - Base session configuration
   * @returns {Object} Provider-specific configuration
   * @protected
   */
  _buildProviderSessionConfig(baseConfig) {
    // Default implementation - override in subclasses
    return {
      model: baseConfig.model || 'default',
      voice: baseConfig.voice || 'default',
      ...baseConfig,
      // Add provider-specific defaults
      ...this.providerConfig
    };
  }

  /**
   * Build provider-specific message format
   * @param {string} messageType - Message type ('create', 'update', 'terminate')
   * @param {Object} messageData - Message data
   * @returns {Object} Provider-specific message
   * @protected
   */
  _buildProviderMessage(messageType, messageData) {
    // Default implementation - override in subclasses
    return {
      type: `session.${messageType}`,
      data: messageData,
      timestamp: Date.now()
    };
  }

  /**
   * Map provider event to universal event type
   * @param {string} providerEvent - Provider-specific event type
   * @returns {string} Universal event type
   * @protected
   */
  _mapProviderEvent(providerEvent) {
    // Check custom mappings first
    if (this.eventMappings[providerEvent]) {
      return this.eventMappings[providerEvent];
    }

    // Default mappings - override in subclasses for provider-specific events
    const defaultMappings = {
      'session.created': BaseEventType.SESSION_CREATED,
      'session.updated': BaseEventType.SESSION_UPDATED,
      'session.interrupted': BaseEventType.SESSION_INTERRUPTED,
      'conversation.created': BaseEventType.CONVERSATION_CREATED,
      'conversation.updated': BaseEventType.CONVERSATION_UPDATED,
      'response.audio.generated': BaseEventType.AUDIO_RESPONSE_GENERATED,
      'response.text.generated': BaseEventType.TEXT_RESPONSE_GENERATED,
      'error': BaseEventType.ERROR
    };

    return defaultMappings[providerEvent] || providerEvent;
  }

  /**
   * Default session update handler
   * @param {Object} updateConfig - Update configuration
   * @param {Object} currentSession - Current session data
   * @returns {Object} Provider update data
   * @protected
   */
  _defaultSessionUpdate(updateConfig, currentSession) {
    // Default implementation - merge update with current session
    return {
      ...currentSession,
      ...updateConfig,
      updated_at: new Date().toISOString()
    };
  }

  // === PRIVATE METHODS ===

  /**
   * Handle connection established
   * @private
   */
  _handleConnectionEstablished(connectionData) {
    this.logger.info('🔗 Connection established');
    this.isConnected = true;
    this.emit('connection_established', connectionData);
  }

  /**
   * Handle connection lost
   * @private
   */
  _handleConnectionLost(disconnectionData) {
    this.logger.warn('🔗 Connection lost');
    this.isConnected = false;
    this.emit('connection_lost', disconnectionData);

    // Attempt recovery if session was active
    if (this.sessionState === SessionState.ACTIVE) {
      this._attemptSessionRecovery();
    }
  }

  /**
   * Handle connection error
   * @private
   */
  _handleConnectionError(error) {
    this.logger.error('🔗 Connection error:', error);
    this.emit('connection_error', error);
  }

  /**
   * Handle session ready signal from connection
   * @private
   */
  _handleSessionReady(sessionData) {
    this.logger.info('🎯 Session ready signal received');

    if (this.sessionState === SessionState.CREATING || this.sessionState === SessionState.UPDATING) {
      this._setState(SessionState.ACTIVE);
    }

    this.emit('session_ready', sessionData);
  }

  /**
   * Handle incoming provider messages
   * @private
   */
  _handleProviderMessage(messageData) {
    try {
      const eventType = this._mapProviderEvent(messageData.type);

      this.logger.debug('📨 Provider message:', {
        originalType: messageData.type,
        mappedType: eventType
      });

      // Handle session lifecycle events
      switch (eventType) {
        case BaseEventType.SESSION_CREATED:
          this._handleSessionCreated(messageData);
          break;
        case BaseEventType.SESSION_UPDATED:
          this._handleSessionUpdated(messageData);
          break;
        case BaseEventType.SESSION_INTERRUPTED:
          this._handleSessionInterrupted(messageData);
          break;
        case BaseEventType.CONVERSATION_CREATED:
          this._handleConversationCreated(messageData);
          break;
        default:
          // Handle other events through registered handlers
          this._dispatchEvent(eventType, messageData);
          break;
      }

    } catch (error) {
      this.logger.error('❌ Error handling provider message:', error);
    }
  }

  /**
   * Handle session created event
   * @private
   */
  _handleSessionCreated(messageData) {
    this.sessionId = messageData.session?.id || messageData.session_id;
    this.currentSession = messageData.session || messageData.data;

    this._setState(SessionState.CREATED);

    this.logger.info('🎬 Session created:', { sessionId: this.sessionId });
    this.emit('session_created', {
      sessionId: this.sessionId,
      session: this.currentSession
    });
  }

  /**
   * Handle session updated event
   * @private
   */
  _handleSessionUpdated(messageData) {
    this.currentSession = { ...this.currentSession, ...messageData.session };

    this._setState(SessionState.ACTIVE);

    this.logger.info('🔄 Session updated:', { sessionId: this.sessionId });
    this.emit('session_updated', {
      sessionId: this.sessionId,
      session: this.currentSession
    });
  }

  /**
   * Handle session interrupted event
   * @private
   */
  _handleSessionInterrupted(messageData) {
    this._setState(SessionState.INTERRUPTED);

    this.logger.warn('⚠️ Session interrupted:', messageData);
    this.emit('session_interrupted', messageData);
  }

  /**
   * Handle conversation created event
   * @private
   */
  _handleConversationCreated(messageData) {
    this.conversationId = messageData.conversation?.id || messageData.conversation_id;

    this.logger.info('💬 Conversation created:', { conversationId: this.conversationId });
    this.emit('conversation_created', {
      conversationId: this.conversationId,
      conversation: messageData.conversation
    });
  }

  /**
   * Handle speech started VAD event with enhanced context
   * @private
   */
  _handleSpeechStarted(vadData) {
    this.logger.debug('🗣️ Speech started with enhanced context', vadData);
    
    // Store speech start time for duration tracking
    this.speechStartTime = Date.now();
    
    // Create enhanced speech started context
    const enhancedSpeechContext = {
      eventType: 'speech_started',
      timestamp: this.speechStartTime,
      vadData,
      audioQuality: vadData.audioQuality || 'good',
      speakerProximity: vadData.speakerProximity || 'close',
      environmentalContext: vadData.environmentalContext || 'indoor',
      engagementLevel: vadData.engagementLevel || 'active'
    };
    
    // Emit enhanced event for dual brain coordination
    this.emit('enhanced_vad_event', {
      type: 'speech_started',
      context: enhancedSpeechContext,
      timestamp: this.speechStartTime
    });

    if (this.vadConfig.auto_commit) {
      // Auto-commit any pending audio buffer
      this.handleAudioBuffer('commit', vadData);
    }
  }

  /**
   * Handle speech stopped VAD event with enhanced context
   * @private
   */
  _handleSpeechStopped(vadData) {
    const speechDuration = this.speechStartTime ? Date.now() - this.speechStartTime : 0;
    
    this.logger.debug('🤐 Speech stopped with enhanced context', {
      duration: speechDuration,
      vadData
    });
    
    // Create enhanced speech stopped context
    const enhancedSpeechStoppedContext = {
      eventType: 'speech_stopped',
      timestamp: Date.now(),
      speechDuration,
      speechStartTime: this.speechStartTime,
      vadData,
      speechQuality: vadData.speechQuality || 'good',
      readinessForResponse: vadData.readinessForResponse || { ready: true, level: 'high' },
      conversationTransition: {
        fromSpeech: true,
        toProcessing: true,
        flowQuality: speechDuration > 1000 ? 'established' : 'developing'
      },
      environmentalTransition: {
        transitionType: 'speech_to_silence',
        transitionSpeed: speechDuration < 1000 ? 'quick' : 'normal',
        readyForProcessing: true
      }
    };
    
    // Emit enhanced event for dual brain coordination
    this.emit('enhanced_vad_event', {
      type: 'speech_stopped',
      context: enhancedSpeechStoppedContext,
      timestamp: Date.now()
    });
    
    // Reset speech tracking
    this.speechStartTime = null;

    // Can implement auto-clear or other logic here
  }

  /**
   * Handle silence detected VAD event
   * @private
   */
  _handleSilenceDetected(vadData) {
    this.logger.debug('🔇 Silence detected');

    if (vadData.duration >= this.vadConfig.silence_duration_ms && this.vadConfig.auto_commit) {
      // Auto-commit after silence threshold
      this.handleAudioBuffer('commit', vadData);
    }
  }

  /**
   * Build audio buffer message
   * @private
   */
  _buildAudioBufferMessage(operation, bufferData) {
    // Default implementation - override in subclasses
    return {
      type: `input_audio_buffer.${operation}`,
      data: bufferData,
      timestamp: Date.now()
    };
  }

  /**
   * Send session-related message
   * @private
   */
  async _sendSessionMessage(messageType, messageData) {
    if (!this.connectionManager) {
      throw new Error('Connection manager not initialized');
    }

    const message = this._buildProviderMessage(messageType, messageData);
    return this.connectionManager.send(message);
  }

  /**
   * Send generic message
   * @private
   */
  async _sendMessage(message) {
    if (!this.connectionManager) {
      throw new Error('Connection manager not initialized');
    }

    return this.connectionManager.send(message);
  }

  /**
   * Dispatch event to registered handlers
   * @private
   */
  _dispatchEvent(eventType, messageData) {
    if (this.eventHandlers.has(eventType)) {
      const handlers = this.eventHandlers.get(eventType);
      handlers.forEach(handler => {
        try {
          handler(messageData);
        } catch (error) {
          this.logger.error(`❌ Error in event handler for ${eventType}:`, error);
        }
      });
    }

    // Also emit as EventEmitter event
    this.emit(eventType, messageData);
  }

  /**
   * Attempt session recovery after connection loss
   * @private
   */
  async _attemptSessionRecovery() {
    if (this.recoveryAttempts >= this.maxRecoveryAttempts) {
      this.logger.error('❌ Session recovery attempts exhausted');
      this._setState(SessionState.ERROR);
      return;
    }

    this.recoveryAttempts++;

    this.logger.info(`🔄 Attempting session recovery (${this.recoveryAttempts}/${this.maxRecoveryAttempts})`);

    try {
      // Wait for connection to be re-established
      if (this.connectionManager) {
        const connectionRestored = await this.connectionManager.waitForReady(5000);

        if (connectionRestored && this.lastKnownState) {
          // Try to restore session
          await this.createSession(this.lastKnownState);
          this.recoveryAttempts = 0; // Reset on success
        }
      }

    } catch (error) {
      this.logger.error('❌ Session recovery failed:', error);

      // Retry with backoff
      setTimeout(() => {
        this._attemptSessionRecovery();
      }, Math.pow(2, this.recoveryAttempts) * 1000);
    }
  }

  /**
   * Set session state with validation and logging
   * @private
   */
  _setState(newState) {
    if (this.sessionState === newState) {
      return; // No change
    }

    const oldState = this.sessionState;
    this.sessionState = newState;

    this.logger.debug(`🔄 Session state: ${oldState} → ${newState}`);

    // Store state for recovery
    if (newState === SessionState.ACTIVE) {
      this.lastKnownState = { ...this.currentSession };
    }

    this.emit('state_change', {
      from: oldState,
      to: newState,
      timestamp: Date.now()
    });
  }

  /**
   * Sanitize configuration for logging
   * @private
   */
  _sanitizeConfig(config) {
    const sanitized = { ...config };

    // Remove sensitive data
    if (sanitized.apiKey) {
      sanitized.apiKey = '[REDACTED]';
    }
    if (sanitized.token) {
      sanitized.token = '[REDACTED]';
    }

    return sanitized;
  }

  /**
   * Clean up session resources
   * @private
   */
  _cleanup() {
    this.sessionId = null;
    this.conversationId = null;
    this.currentSession = null;
    this.lastKnownState = null;
    this.recoveryAttempts = 0;
  }

  /**
   * Dispose of coordinator and clean up resources
   */
  async dispose() {
    try {
      this.logger.info('🗑️ Disposing session coordinator...');

      // Terminate session if active
      if (this.isSessionActive()) {
        await this.terminateSession();
      }

      // Clean up event handlers
      this.eventHandlers.clear();
      this.removeAllListeners();

      // Disconnect from connection manager
      if (this.connectionManager) {
        this.connectionManager.off('connected', this._handleConnectionEstablished);
        this.connectionManager.off('disconnected', this._handleConnectionLost);
        this.connectionManager.off('message', this._handleProviderMessage);
        this.connectionManager.off('error', this._handleConnectionError);
        this.connectionManager.off('sessionReady', this._handleSessionReady);
      }

      this._cleanup();

      this.logger.info('✅ Session coordinator disposed');

    } catch (error) {
      this.logger.error('❌ Error during coordinator disposal:', error);
    }
  }
}

export default BaseSessionCoordinator;