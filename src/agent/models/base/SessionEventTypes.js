/**
 * SessionEventTypes - Universal Event Type Definitions
 * 
 * MISSION: Standardize event types across all WebSocket providers
 * 
 * This module defines universal event types that can be used consistently
 * across different WebSocket providers. Provider-specific events are mapped
 * to these universal types through the ProviderConfig system.
 */

/**
 * Universal Session Lifecycle Events
 */
export const SessionEvents = {
  // Session management
  SESSION_CREATING: 'session.creating',
  SESSION_CREATED: 'session.created',
  SESSION_UPDATING: 'session.updating', 
  SESSION_UPDATED: 'session.updated',
  SESSION_READY: 'session.ready',
  SESSION_INTERRUPTED: 'session.interrupted',
  SESSION_TERMINATED: 'session.terminated',
  
  // Session errors
  SESSION_ERROR: 'session.error',
  SESSION_RECOVERY_STARTED: 'session.recovery.started',
  SESSION_RECOVERY_SUCCESS: 'session.recovery.success',
  SESSION_RECOVERY_FAILED: 'session.recovery.failed',
};

/**
 * Universal Conversation Events
 */
export const ConversationEvents = {
  // Conversation lifecycle
  CONVERSATION_CREATED: 'conversation.created',
  CONVERSATION_UPDATED: 'conversation.updated',
  CONVERSATION_ITEM_CREATED: 'conversation.item.created',
  CONVERSATION_ITEM_TRUNCATED: 'conversation.item.truncated',
  CONVERSATION_ITEM_DELETED: 'conversation.item.deleted',
  
  // Turn management
  TURN_STARTED: 'turn.started',
  TURN_COMPLETED: 'turn.completed',
  TURN_INTERRUPTED: 'turn.interrupted',
};

/**
 * Universal Voice Activity Detection (VAD) Events
 */
export const VADEvents = {
  // Speech detection
  SPEECH_STARTED: 'vad.speech.started',
  SPEECH_STOPPED: 'vad.speech.stopped',
  SPEECH_DETECTED: 'vad.speech.detected',
  
  // Silence detection
  SILENCE_STARTED: 'vad.silence.started',
  SILENCE_DETECTED: 'vad.silence.detected',
  
  // Audio level monitoring
  AUDIO_LEVEL_CHANGED: 'vad.audio_level.changed',
  AUDIO_THRESHOLD_CROSSED: 'vad.threshold.crossed',
  
  // VAD configuration
  VAD_ENABLED: 'vad.enabled',
  VAD_DISABLED: 'vad.disabled',
  VAD_CONFIGURED: 'vad.configured',
};

/**
 * Universal Audio Buffer Events
 */
export const AudioBufferEvents = {
  // Buffer operations
  BUFFER_CREATED: 'audio_buffer.created',
  BUFFER_APPENDED: 'audio_buffer.appended',
  BUFFER_COMMITTED: 'audio_buffer.committed',
  BUFFER_CLEARED: 'audio_buffer.cleared',
  BUFFER_TRUNCATED: 'audio_buffer.truncated',
  
  // Buffer state
  BUFFER_FULL: 'audio_buffer.full',
  BUFFER_OVERFLOW: 'audio_buffer.overflow',
  BUFFER_UNDERRUN: 'audio_buffer.underrun',
  
  // Input audio buffer (provider-agnostic)
  INPUT_AUDIO_BUFFER_COMMITTED: 'input_audio_buffer.committed',
  INPUT_AUDIO_BUFFER_CLEARED: 'input_audio_buffer.cleared',
  INPUT_AUDIO_BUFFER_SPEECH_STARTED: 'input_audio_buffer.speech_started',
  INPUT_AUDIO_BUFFER_SPEECH_STOPPED: 'input_audio_buffer.speech_stopped',
};

/**
 * Universal Response Events
 */
export const ResponseEvents = {
  // Response lifecycle
  RESPONSE_CREATED: 'response.created',
  RESPONSE_STARTED: 'response.started',
  RESPONSE_COMPLETED: 'response.completed',
  RESPONSE_CANCELLED: 'response.cancelled',
  RESPONSE_FAILED: 'response.failed',
  
  // Audio responses
  AUDIO_RESPONSE_STARTED: 'response.audio.started',
  AUDIO_RESPONSE_DELTA: 'response.audio.delta',
  AUDIO_RESPONSE_GENERATED: 'response.audio.generated',
  AUDIO_RESPONSE_COMPLETED: 'response.audio.completed',
  
  // Text responses
  TEXT_RESPONSE_STARTED: 'response.text.started',
  TEXT_RESPONSE_DELTA: 'response.text.delta', 
  TEXT_RESPONSE_GENERATED: 'response.text.generated',
  TEXT_RESPONSE_COMPLETED: 'response.text.completed',
  
  // Function call responses
  FUNCTION_CALL_STARTED: 'response.function_call.started',
  FUNCTION_CALL_COMPLETED: 'response.function_call.completed',
  FUNCTION_CALL_ARGUMENTS: 'response.function_call.arguments',
  
  // Response output
  RESPONSE_OUTPUT_ITEM_ADDED: 'response.output_item.added',
  RESPONSE_OUTPUT_ITEM_COMPLETED: 'response.output_item.completed',
  RESPONSE_CONTENT_PART_ADDED: 'response.content_part.added',
  RESPONSE_CONTENT_PART_COMPLETED: 'response.content_part.completed',
};

/**
 * Universal Connection Events
 */
export const ConnectionEvents = {
  // Connection lifecycle
  CONNECTION_ESTABLISHING: 'connection.establishing',
  CONNECTION_ESTABLISHED: 'connection.established',
  CONNECTION_READY: 'connection.ready',
  CONNECTION_LOST: 'connection.lost',
  CONNECTION_RECONNECTING: 'connection.reconnecting',
  CONNECTION_TERMINATED: 'connection.terminated',
  
  // Connection health
  CONNECTION_HEALTHY: 'connection.healthy',
  CONNECTION_DEGRADED: 'connection.degraded',
  CONNECTION_UNSTABLE: 'connection.unstable',
  
  // Connection errors
  CONNECTION_ERROR: 'connection.error',
  CONNECTION_TIMEOUT: 'connection.timeout',
  CONNECTION_AUTH_FAILED: 'connection.auth_failed',
  CONNECTION_RATE_LIMITED: 'connection.rate_limited',
};

/**
 * Universal Error Events
 */
export const ErrorEvents = {
  // General errors
  ERROR: 'error',
  WARNING: 'warning',
  
  // Session errors
  SESSION_CREATION_FAILED: 'error.session.creation_failed',
  SESSION_UPDATE_FAILED: 'error.session.update_failed',
  SESSION_TERMINATION_FAILED: 'error.session.termination_failed',
  
  // Audio errors
  AUDIO_ENCODING_ERROR: 'error.audio.encoding_failed',
  AUDIO_DECODING_ERROR: 'error.audio.decoding_failed',
  AUDIO_FORMAT_ERROR: 'error.audio.format_unsupported',
  AUDIO_BUFFER_ERROR: 'error.audio.buffer_failed',
  
  // Network errors
  NETWORK_ERROR: 'error.network',
  WEBSOCKET_ERROR: 'error.websocket',
  TIMEOUT_ERROR: 'error.timeout',
  
  // Authentication errors
  AUTH_ERROR: 'error.authentication',
  PERMISSION_ERROR: 'error.permission',
  
  // Rate limiting errors
  RATE_LIMIT_ERROR: 'error.rate_limit',
  QUOTA_EXCEEDED_ERROR: 'error.quota_exceeded',
  
  // Configuration errors
  CONFIG_ERROR: 'error.configuration',
  VALIDATION_ERROR: 'error.validation',
};

/**
 * Universal Status Events
 */
export const StatusEvents = {
  // Health monitoring
  HEALTH_CHECK: 'status.health_check',
  PERFORMANCE_METRICS: 'status.performance_metrics',
  RESOURCE_USAGE: 'status.resource_usage',
  
  // State changes
  STATE_CHANGED: 'status.state_changed',
  MODE_CHANGED: 'status.mode_changed',
  
  // Progress tracking
  PROGRESS_UPDATED: 'status.progress_updated',
  TASK_STARTED: 'status.task_started',
  TASK_COMPLETED: 'status.task_completed',
};

/**
 * All Universal Events - Combined for easy access
 */
export const UniversalEvents = {
  ...SessionEvents,
  ...ConversationEvents,
  ...VADEvents,
  ...AudioBufferEvents,
  ...ResponseEvents,
  ...ConnectionEvents,
  ...ErrorEvents,
  ...StatusEvents,
};

/**
 * Event Categories - For filtering and organization
 */
export const EventCategories = {
  SESSION: Object.values(SessionEvents),
  CONVERSATION: Object.values(ConversationEvents),
  VAD: Object.values(VADEvents),
  AUDIO_BUFFER: Object.values(AudioBufferEvents),
  RESPONSE: Object.values(ResponseEvents),
  CONNECTION: Object.values(ConnectionEvents),
  ERROR: Object.values(ErrorEvents),
  STATUS: Object.values(StatusEvents),
};

/**
 * Get event category for a given event type
 * @param {string} eventType - Event type to categorize
 * @returns {string|null} Event category or null if not found
 */
export function getEventCategory(eventType) {
  for (const [category, events] of Object.entries(EventCategories)) {
    if (events.includes(eventType)) {
      return category.toLowerCase();
    }
  }
  return null;
}

/**
 * Check if event type is valid
 * @param {string} eventType - Event type to validate
 * @returns {boolean} Validation result
 */
export function isValidEventType(eventType) {
  return Object.values(UniversalEvents).includes(eventType);
}

/**
 * Get all events in a specific category
 * @param {string} category - Category name
 * @returns {string[]} Array of event types in category
 */
export function getEventsInCategory(category) {
  const upperCategory = category.toUpperCase();
  return EventCategories[upperCategory] || [];
}

/**
 * Check if event is an error event
 * @param {string} eventType - Event type to check
 * @returns {boolean} True if error event
 */
export function isErrorEvent(eventType) {
  return EventCategories.ERROR.includes(eventType);
}

/**
 * Check if event is a session event
 * @param {string} eventType - Event type to check
 * @returns {boolean} True if session event
 */
export function isSessionEvent(eventType) {
  return EventCategories.SESSION.includes(eventType);
}

/**
 * Check if event is a VAD event
 * @param {string} eventType - Event type to check
 * @returns {boolean} True if VAD event
 */
export function isVADEvent(eventType) {
  return EventCategories.VAD.includes(eventType);
}

/**
 * Check if event is a response event
 * @param {string} eventType - Event type to check
 * @returns {boolean} True if response event
 */
export function isResponseEvent(eventType) {
  return EventCategories.RESPONSE.includes(eventType);
}

/**
 * Create custom event type following universal naming convention
 * @param {string} category - Event category
 * @param {string} subcategory - Event subcategory
 * @param {string} action - Event action
 * @returns {string} Formatted event type
 */
export function createEventType(category, subcategory, action) {
  return `${category.toLowerCase()}.${subcategory.toLowerCase()}.${action.toLowerCase()}`;
}

export default {
  // Main event objects
  SessionEvents,
  ConversationEvents,
  VADEvents,
  AudioBufferEvents,
  ResponseEvents,
  ConnectionEvents,
  ErrorEvents,
  StatusEvents,
  UniversalEvents,
  EventCategories,
  
  // Utility functions
  getEventCategory,
  isValidEventType,
  getEventsInCategory,
  isErrorEvent,
  isSessionEvent,
  isVADEvent,
  isResponseEvent,
  createEventType,
};