/**
 * HTTP Chat Model Base Class
 * Specialized base class for HTTP-based chat models
 * 
 * Provides HTTP-specific functionality without WebSocket streaming overhead
 */

import { BaseChatModel } from './BaseChatModel.js';
import { AIMessage, ToolMessage } from '@langchain/core/messages';

export class HttpChatModel extends BaseChatModel {
  constructor(options = {}) {
    super({ ...options, apiMode: 'http' });

    // HTTP-specific configuration
    this.httpConfig = {
      baseUrl: options.baseUrl || '',
      timeout: options.timeout || 5000,
      retryAttempts: options.retryAttempts || 3,
      retryDelay: options.retryDelay || 1000,
      maxTokens: options.maxTokens || 2000,
      streaming: options.streaming || false,
      ...options.httpConfig
    };

    // HTTP request tracking
    this.requestMetrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      totalResponseTime: 0
    };
  }

  /**
   * HTTP-specific initialization
   * @protected
   */
  _initializeModeSpecific(options) {
    this._validateHttpConfiguration();
    this.logger.debug('✅ HTTP chat model configuration validated');
  }

  /**
   * Validate HTTP-specific configuration
   * @protected
   */
  _validateHttpConfiguration() {
    if (!this.httpConfig.baseUrl && !this.apiKey) {
      throw new Error('HTTP model requires either baseUrl or apiKey configuration');
    }

    if (this.httpConfig.timeout < 100) {
      this.logger.warn('HTTP timeout is very low, may cause request failures');
    }
  }

  /**
   * Make HTTP request with retry logic
   * @protected
   */
  async _makeHttpRequest(messages, options = {}, context = {}) {
    const startTime = Date.now();
    this.requestMetrics.totalRequests++;

    for (let attempt = 1; attempt <= this.httpConfig.retryAttempts; attempt++) {
      try {
        const response = await this._performHttpRequest(messages, options, context);

        // Record successful request
        const responseTime = Date.now() - startTime;
        this._recordHttpMetrics(responseTime, true);

        return response;

      } catch (error) {
        if (attempt === this.httpConfig.retryAttempts || !this._isRetryableError(error)) {
          // Record failed request
          this._recordHttpMetrics(Date.now() - startTime, false);
          throw error;
        }

        // Wait before retry
        await this._sleep(this.httpConfig.retryDelay * attempt);
        this.logger.warn(`HTTP request attempt ${attempt} failed, retrying...`, error.message);
      }
    }
  }

  /**
   * Main invoke method for HTTP API calls with tool execution support
   */
  async invoke(messages, options = {}) {
    // CRITICAL: Call super.invoke() first to apply API limiting from BaseChatModel
    await super.invoke(messages, options);

    const startTime = Date.now();
    const context = {
      streaming: false,
      hasToolCalls: (this.boundTools && this.boundTools.length > 0),
      messageCount: messages.length,
      urgency: options.urgency || 'medium',
      isDualBrainAnalysis: options.isDualBrainAnalysis || false,
      isContextualAnalysis: options.isContextualAnalysis || false
    };

    this.logger.debug(`[${this.constructor.name}] Invoking with ${messages.length} messages via HTTP API`, context);

    // Check API limits before making request
    if (!this._checkApiLimits()) {
      throw new Error('API rate limit exceeded. Please wait before making more requests.');
    }

    try {
      // Make initial HTTP request
      let response = await this._makeHttpRequest(messages, options, context);
      const processingTime = Date.now() - startTime;

      // Handle tool calls if present
      if (response.generations?.[0]?.tool_calls?.length > 0) {
        this.logger.debug(`🔧 Processing ${response.generations[0].tool_calls.length} tool calls`);
        response = await this._executeToolCalls(response, messages, options, context);
      }

      // Record successful metrics
      this._recordMetrics(processingTime, true);
      this._recordApiCost(messages, response, processingTime, true);

      this.logger.info(`✅ HTTP API call completed in ${processingTime}ms`);
      return response;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this._recordMetrics(processingTime, false);
      this._recordApiCost(messages, null, processingTime, false);
      this.logger.error(`❌ HTTP API call failed after ${processingTime}ms:`, error.message);
      throw this._handleError(error, context);
    }
  }

  /**
   * Execute tool calls and continue conversation with results
   * @private
   */
  async _executeToolCalls(initialResponse, originalMessages, options, context) {
    const toolCalls = initialResponse.generations[0].tool_calls;
    const updatedMessages = [...originalMessages];

    // Add the AI message with tool calls to conversation history
    updatedMessages.push(new AIMessage({
      content: initialResponse.generations[0].text || '',
      tool_calls: toolCalls
    }));

    // Execute each tool call
    const toolResults = [];
    for (const toolCall of toolCalls) {
      try {
        this.logger.debug(`🔧 Executing tool: ${toolCall.name} with args:`, toolCall.args);

        // Find the bound tool
        const boundTool = this.boundTools.find(tool => tool.name === toolCall.name);
        if (!boundTool) {
          throw new Error(`Tool '${toolCall.name}' not found in bound tools`);
        }

        // Execute the tool
        const startTime = Date.now();
        const result = await boundTool.func(toolCall.args);
        const executionTime = Date.now() - startTime;

        this.logger.info(`✅ Tool '${toolCall.name}' executed in ${executionTime}ms`);

        // Create tool message with result
        const toolMessage = new ToolMessage({
          content: typeof result === 'string' ? result : JSON.stringify(result),
          tool_call_id: toolCall.id,
          name: toolCall.name
        });

        updatedMessages.push(toolMessage);
        toolResults.push({
          toolCall,
          result,
          executionTime,
          success: true
        });

      } catch (error) {
        this.logger.error(`❌ Tool '${toolCall.name}' execution failed:`, error.message);

        // Create error message for the tool
        const errorMessage = new ToolMessage({
          content: `Error: ${error.message}`,
          tool_call_id: toolCall.id,
          name: toolCall.name
        });

        updatedMessages.push(errorMessage);
        toolResults.push({
          toolCall,
          error: error.message,
          success: false
        });
      }
    }

    // Make follow-up request with tool results
    try {
      this.logger.debug(`🔄 Making follow-up request with ${toolResults.length} tool results`);
      const followUpResponse = await this._makeHttpRequest(updatedMessages, options, {
        ...context,
        isToolFollowUp: true,
        toolResults
      });

      // Combine original response metadata with follow-up content
      return {
        generations: [{
          ...followUpResponse.generations[0],
          tool_calls: toolCalls, // Keep original tool calls for reference
          tool_results: toolResults, // Add tool execution results
          text: followUpResponse.generations[0].text,
          message: followUpResponse.generations[0].message
        }]
      };

    } catch (error) {
      this.logger.error(`❌ Tool follow-up request failed:`, error.message);

      // Return original response with tool results but note the error
      return {
        generations: [{
          ...initialResponse.generations[0],
          tool_calls: toolCalls,
          tool_results: toolResults,
          tool_follow_up_error: error.message
        }]
      };
    }
  }

  /**
   * Build OpenAI-compatible request payload
   * @protected
   */
  _buildOpenAICompatiblePayload(messages, options = {}) {
    const payload = {
      model: options.model || this.model || 'gpt-3.5-turbo',
      messages: this._formatMessages(messages),
      temperature: options.temperature || this.temperature || 0.7,
      max_tokens: options.max_tokens || options.maxTokens || this.httpConfig.maxTokens || 2000
    };

    // Add tools if available - use existing formatting method
    if (this.boundTools && this.boundTools.length > 0) {
      // Use existing _formatToolsForAPI method from BaseChatModel
      payload.tools = this._formatToolsForAPI(this.boundTools);
      payload.tool_choice = options.tool_choice || options.toolChoice || 'auto';
      this.logger.debug(`🔧 Added ${this.boundTools.length} tools to request`);
    }

    // OpenAI-compatible streaming
    if (options.stream || this.httpConfig.streaming) {
      payload.stream = true;
    }

    // Structured output configuration
    const structuredOutput = this._buildStructuredOutputConfig(options);
    if (structuredOutput) {
      Object.assign(payload, structuredOutput);
    }

    // Provider-specific enhancements
    if (options.enable_thinking) {
      payload.enable_thinking = true;
    }

    if (options.thinking_budget) {
      payload.thinking_budget = options.thinking_budget;
    }

    if (options.enable_search) {
      payload.enable_search = true;
    }

    if (options.search_options) {
      payload.search_options = options.search_options;
    }

    return payload;
  }

  // ToolManagementService removed - using simplified approach

  /**
   * Generate tool schema from tool function if not provided
   * @private
   */
  _generateToolSchema(tool) {
    // Basic schema generation - can be enhanced
    return {
      type: 'object',
      properties: {},
      required: []
    };
  }

  /**
   * Build structured output configuration for request payload
   * @protected
   */
  _buildStructuredOutputConfig(options = {}) {
    // Check if structured output is requested
    const responseFormat = options.response_format || options.responseFormat;

    if (!responseFormat) {
      return null;
    }

    const config = {};

    // Handle different response format types
    if (responseFormat.type === 'json_object') {
      config.response_format = {
        type: 'json_object'
      };
      this.logger.debug('🔧 Enabling JSON object structured output');
    } else if (responseFormat.type === 'json_schema') {
      if (responseFormat.json_schema) {
        config.response_format = {
          type: 'json_schema',
          json_schema: responseFormat.json_schema
        };
        this.logger.debug('🔧 Enabling JSON schema structured output', {
          schemaName: responseFormat.json_schema.name
        });
      } else {
        this.logger.warn('JSON schema mode requested but no schema provided, falling back to json_object');
        config.response_format = { type: 'json_object' };
      }
    }

    return config;
  }

  /**
   * Base streaming implementation for HTTP models (LangChain v0.3 compatible)
   * Subclasses should override _performStreamingRequest to provide their streaming logic
   * @protected
   */
  async *_streamResponseChunks(messages, options, runManager) {
    const startTime = Date.now();
    const context = {
      streaming: true,
      hasToolCalls: this.boundTools.length > 0,
      messageCount: messages.length,
      urgency: options?.urgency || 'medium',
      isDualBrainAnalysis: options?.isDualBrainAnalysis || false,
      isContextualAnalysis: options?.isContextualAnalysis || false
    };

    this.logger.debug(`[${this.constructor.name}] _streamResponseChunks() called with ${messages.length} messages`, context);

    if (!this._checkApiLimits()) {
      throw new Error('API rate limit exceeded. Please wait before making more requests.');
    }

    try {
      // Let subclass handle the actual streaming implementation
      const stream = this._performStreamingRequest(messages, options, context);

      for await (const chunk of stream) {
        // Ensure chunk has the correct LangChain v0.3 format
        const formattedChunk = this._formatStreamingChunk(chunk, runManager);

        if (runManager && formattedChunk.text) {
          await runManager.handleLLMNewToken(formattedChunk.text, formattedChunk);
        }

        yield formattedChunk;
      }

      const processingTime = Date.now() - startTime;
      this._recordMetrics(processingTime, true);
      this.logger.info(`✅ _streamResponseChunks() completed in ${processingTime}ms`);

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this._recordMetrics(processingTime, false);
      this.logger.error(`❌ _streamResponseChunks() failed after ${processingTime}ms:`, error.message);
      throw this._handleError(error, context);
    }
  }

  /**
   * Format streaming chunk to LangChain v0.3 format
   * @protected
   */
  _formatStreamingChunk(chunk, runManager) {
    // If chunk is already in correct format, return as-is
    if (chunk.message && typeof chunk.text === 'string') {
      return chunk;
    }

    // Convert legacy formats
    return {
      message: chunk.message || this._createAIMessage(chunk.text || '', chunk.tool_calls || []),
      text: chunk.text || chunk.content || ''
    };
  }

  /**
   * Perform streaming request - to be implemented by subclasses
   * Should return an async generator that yields streaming chunks
   * @protected
   */
  async *_performStreamingRequest(messages, options, context) {
    throw new Error('_performStreamingRequest() must be implemented by HTTP model subclass');
  }

  /**
   * LangChain compatibility: Core _generate method implementation
   */
  async _generate(messages, options, runManager) {
    const startTime = Date.now();
    const context = {
      streaming: false,
      hasToolCalls: this.boundTools.length > 0,
      messageCount: messages.length,
      urgency: options?.urgency || 'medium',
      isDualBrainAnalysis: options?.isDualBrainAnalysis || false,
      isContextualAnalysis: options?.isContextualAnalysis || false
    };

    this.logger.debug(`[${this.constructor.name}] _generate() called with ${messages.length} messages`, context);

    if (!this._checkApiLimits()) {
      throw new Error('API rate limit exceeded. Please wait before making more requests.');
    }

    try {
      const response = await this._makeHttpRequest(messages, options, context);
      const processingTime = Date.now() - startTime;

      this._recordMetrics(processingTime, true);
      this.logger.info(`✅ _generate() completed in ${processingTime}ms`);

      // Return in LangChain ChatGeneration format
      return {
        generations: response.generations || [{
          text: response.content || '',
          message: response.message || new AIMessage({
            content: response.content || '',
            tool_calls: response.tool_calls
          })
        }]
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this._recordMetrics(processingTime, false);
      this.logger.error(`❌ _generate() failed after ${processingTime}ms:`, error.message);
      throw this._handleError(error, context);
    }
  }

  /**
   * Perform the actual HTTP request - to be implemented by subclasses
   * @protected
   */
  async _performHttpRequest(messages, options = {}, context = {}) {
    throw new Error('_performHttpRequest() must be implemented by HTTP model subclass');
  }

  // Abstract methods from BaseChatModel that HTTP models don't support
  buildWebSocketUrl() { throw new Error('HttpChatModel does not support WebSocket connections'); }
  getConnectionOptions() { throw new Error('HttpChatModel does not support WebSocket connections'); }
  processProviderMessage(message) { throw new Error('HttpChatModel does not support WebSocket message processing'); }
  async initializeSession(sessionConfig = {}) { return Promise.resolve(true); }
  async _sendAudioToProvider(audioData, format) { throw new Error('Audio transmission not supported by HttpChatModel.'); }

  /**
   * Build HTTP headers - to be implemented by subclasses
   * @protected
   */
  _buildHttpHeaders(options = {}) {
    return {
      'Content-Type': 'application/json',
      'User-Agent': 'HttpChatModel/1.0',
      ...options.headers
    };
  }

  /**
   * Build request payload - to be implemented by subclasses
   * @protected
   */
  _buildRequestPayload(messages, options = {}) {
    return {
      messages: this._formatMessages(messages),
      max_tokens: options.max_tokens || this.httpConfig.maxTokens,
      temperature: options.temperature || this.temperature,
      ...options
    };
  }

  /**
   * Parse HTTP response - to be implemented by subclasses
   * @protected
   */
  _parseHttpResponse(response) {
    throw new Error('_parseHttpResponse() must be implemented by HTTP model subclass');
  }

  /**
   * Calculate adaptive timeout for HTTP requests
   * @protected
   */
  _calculateTimeout(context = {}) {
    return this._calculateAdaptiveTimeout(this.httpConfig.timeout, {
      ...context,
      streaming: false,
      maxTimeout: this.httpConfig.timeout * 2
    });
  }

  /**
   * Record HTTP-specific metrics
   * @protected
   */
  _recordHttpMetrics(responseTime, success) {
    if (success) {
      this.requestMetrics.successfulRequests++;
      this.requestMetrics.totalResponseTime += responseTime;
      this.requestMetrics.averageResponseTime =
        this.requestMetrics.totalResponseTime / this.requestMetrics.successfulRequests;
    } else {
      this.requestMetrics.failedRequests++;
    }

    // Update base model metrics
    this._recordMetrics(responseTime, success);
  }

  /**
   * HTTP-specific health check
   */
  async _performHealthCheck() {
    try {
      const testMessages = [{ role: 'user', content: 'test' }];
      const response = await this._makeHttpRequest(testMessages, { max_tokens: 1 });

      return {
        httpEndpoint: this.httpConfig.baseUrl || 'configured',
        responseTime: Date.now(),
        hasResponse: !!response
      };
    } catch (error) {
      throw new Error(`HTTP health check failed: ${error.message}`);
    }
  }

  /**
   * Get HTTP-specific metrics
   */
  getHttpMetrics() {
    return {
      ...this.requestMetrics,
      successRate: this.requestMetrics.totalRequests > 0 ?
        (this.requestMetrics.successfulRequests / this.requestMetrics.totalRequests * 100).toFixed(2) + '%' : '0%',
      failureRate: this.requestMetrics.totalRequests > 0 ?
        (this.requestMetrics.failedRequests / this.requestMetrics.totalRequests * 100).toFixed(2) + '%' : '0%'
    };
  }

  /**
   * Enhanced metrics including HTTP-specific data
   */
  getMetrics() {
    return {
      ...super.getMetrics(),
      http: this.getHttpMetrics(),
      config: {
        timeout: this.httpConfig.timeout,
        retryAttempts: this.httpConfig.retryAttempts,
        maxTokens: this.httpConfig.maxTokens
      }
    };
  }

  /**
   * Reset HTTP metrics
   */
  resetHttpMetrics() {
    this.requestMetrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      totalResponseTime: 0
    };
  }

  /**
   * Cleanup HTTP resources
   */
  async cleanup() {
    await super.cleanup();
    this.resetHttpMetrics();
  }
}

export default HttpChatModel;