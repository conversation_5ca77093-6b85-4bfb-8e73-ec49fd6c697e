/**
 * Universal Base Chat Model
 * Unified base class combining LangChain compliance with streaming capabilities
 * 
 * This class merges functionality from:
 * - RealtimeStreamingClient: Universal streaming capabilities for any provider
 * - AliyunBaseChatModel: LangChain v0.3 compliance and tool binding patterns
 * 
 * Provides unified foundation for:
 * - HTTP models for text-based interactions and function calling
 * - WebSocket models for real-time communication  
 * - Realtime streaming models for audio/video processing
 */

import { BaseChatModel as LangChainBaseChatModel } from '@langchain/core/language_models/chat_models';
import { AIMessage } from '@langchain/core/messages';
import { createLogger, LogLevel } from '../../../utils/logger.ts';
import { getEnvVar } from '../../../config/env.ts';
import { createCredentialManager, createWebSocketJsonParser, SecurityError } from '../../../security/SecureCredentialManager.js';

// Lazy-loaded global ErrorHandler instance (non-blocking)
let __globalErrorHandlerPromise = null;
function __getGlobalErrorHandler() {
    if (!__globalErrorHandlerPromise) {
        __globalErrorHandlerPromise = import('../../arch/dualbrain/services/ErrorHandler.js')
            .then(mod => {
                try {
                    if (typeof mod.createErrorHandler === 'function') {
                        return mod.createErrorHandler();
                    }
                    if (typeof mod.ErrorHandler === 'function') {
                        return new mod.ErrorHandler();
                    }
                } catch (_) {
                    return null;
                }
                return null;
            })
            .catch(() => null);
    }
    return __globalErrorHandlerPromise;
}

/**
 * Connection states for any realtime streaming provider
 */
export const StreamingState = {
    DISCONNECTED: 'disconnected',
    CONNECTING: 'connecting',
    CONNECTED: 'connected',
    AUTHENTICATED: 'authenticated',
    SESSION_READY: 'session_ready',
    STREAMING: 'streaming',
    ERROR: 'error',
    CLOSING: 'closing'
};

/**
 * Standard close codes across providers
 */
export const StreamingCloseCodes = {
    1000: 'Normal closure',
    1006: 'Abnormal closure (connection lost)',
    1011: 'Server internal error',
    4001: 'Authentication failed',
    4008: 'Rate limit exceeded',
    4009: 'Audio format error'
};

/**
 * Base configuration interface for all providers
 */
export const createBaseConfig = () => ({
    // Authentication
    apiKey: '',

    // Connection
    endpoint: '',
    model: '',
    provider: 'universal',

    // Performance configuration
    timeout: 500,
    temperature: 0.7,
    maxTokens: 2000,
    topP: 0.8,

    // Tool calling configuration
    toolChoice: 'auto',

    // Retry and reliability configuration
    maxRetries: 2,
    retryDelay: 100,

    // Audio configuration
    audioConfig: {
        sampleRate: 16000,
        bitDepth: 16,
        channels: 1,
        format: 'pcm16'
    },

    // Rate limiting
    rateLimiting: {
        enabled: true,
        maxChunksPerSecond: 5,
        minIntervalMs: 200
    },

    // Connection management
    connectionTimeout: 10000,
    maxReconnectAttempts: 3,
    reconnectBackoffMs: 1000,

    // Debugging
    enableDebugLogging: false
});

/**
 * Universal Base Chat Model
 * Abstract base class providing both LangChain compliance and streaming capabilities
 */
export class BaseChatModel extends LangChainBaseChatModel {
    constructor(options = {}) {
        super(options);

        // Core configuration
        this.config = { ...createBaseConfig(), ...options };
        this.apiKey = options.apiKey || getEnvVar('VITE_DASHSCOPE_API_KEY', '');
        this.model = options.model || 'default-model';
        this.apiMode = options.apiMode || 'http'; // 'http', 'websocket', or 'realtime'
        this.provider = options.provider || 'universal'; // Provider name for multi-provider support

        // Performance configuration
        this.timeout = options.timeout || 500;
        this.temperature = options.temperature || 0.7;
        this.maxTokens = options.maxTokens || 2000;
        this.topP = options.topP || 0.8;

        // Tool calling configuration
        this.toolChoice = options.toolChoice || 'auto';
        this.boundTools = [];

        // Retry and reliability configuration
        this.maxRetries = options.maxRetries || 2;
        this.retryDelay = options.retryDelay || 100;

        // Initialize logger
        this.logger = createLogger(`${this.constructor.name}`);
        this.logger.setLogLevel(LogLevel.DEBUG);

        // Initialize secure credential management
        this.credentialManager = null;
        this.jsonParser = null;
        this._initializeSecurityComponents();

        // Streaming state management
        this.socket = null;
        this.state = StreamingState.DISCONNECTED;
        this.connectionStrategy = this._determineConnectionStrategy();

        // Session management
        this.sessionId = null;
        this.sessionStabilized = false;

        // Rate limiting
        this.lastAudioSendTime = 0;
        this.audioQueue = [];
        this.isProcessingQueue = false;

        // API usage limiting for testing (moved from AliyunModelFactory for general usage)
        this.apiLimits = {
            maxAttempts: parseInt(getEnvVar('VITE_API_MAX_ATTEMPTS', '10')),
            currentAttempts: 0,
            rateLimitEnabled: getEnvVar('VITE_API_RATE_LIMIT_ENABLED', 'true') === 'true',
            testMode: getEnvVar('VITE_API_TEST_MODE', 'false') === 'true',
            costTrackingEnabled: getEnvVar('VITE_COST_TRACKING', 'true') === 'true'
        };

        // Connection management
        this.reconnectAttempts = 0;
        this.connectionTimeout = null;

        // Event handlers
        this.eventHandlers = {
            stateChange: [],
            message: [],
            sessionReady: [],
            audioResponse: [],
            error: [],
            close: []
        };

        // Performance tracking (chat model metrics)
        this.metrics = {
            requests: 0,
            successes: 0,
            failures: 0,
            totalResponseTime: 0,
            averageResponseTime: 0,
            healthChecks: 0,
            toolCalls: 0,
            lastRequestTime: null,
            lastResponseTime: null
        };

        // Streaming statistics
        this.stats = {
            connectionsAttempted: 0,
            connectionsSuccessful: 0,
            messagesReceived: 0,
            messagesSent: 0,
            audioChunksSent: 0,
            reconnections: 0,
            lastConnectTime: null,
            uptime: 0
        };

        // Dual brain architecture would be provided by DualBrainChatModel subclass

        // Initialize with validation - defer to async init
        this._initPromise = this._initialize(options);
    }

    /**
     * Initialize the model with configuration validation
     * @protected
     */
    async _initialize(options) {
        // Validate base configuration
        this._validateConfiguration();

        // Initialize mode-specific configuration
        await this._initializeModeSpecific(options);

        this.logger.info(`${this.constructor.name} initialized:`, {
            model: this.model,
            apiMode: this.apiMode,
            timeout: this.timeout,
            hasBoundTools: this.boundTools.length > 0,
            provider: (this.config && this.config.provider) || this.provider || 'unknown',
            strategy: this.connectionStrategy
        });
    }

    /**
     * Mode-specific initialization - to be overridden by subclasses
     * @protected
     */
    _initializeModeSpecific(options) {
        // Override in subclasses for mode-specific setup
    }

    /**
     * Validate base configuration
     * @protected
     */
    _validateConfiguration() {
        // Security validation will be handled by credential manager
        if (this.timeout > 600) {
            this.logger.warn(`Timeout ${this.timeout}ms exceeds sub-600ms requirement`);
        }

        this.logger.debug('✅ Base model configuration validated');
    }

    /**
     * Initialize security components with safe credential management
     * @protected
     */
    _initializeSecurityComponents() {
        try {
            // Initialize credential manager if API key is provided
            if (this.apiKey) {
                this.credentialManager = createCredentialManager(this.apiKey, this.provider);
                this.logger.info(`✅ [Security] Credential validation passed (${this.credentialManager.getMaskedCredential()})`);
            } else {
                this.logger.warn('⚠️ [Security] No API key provided - some functionality may be limited');
            }

            // Initialize safe JSON parser for all message handling
            this.jsonParser = createWebSocketJsonParser();
            this.logger.debug('✅ [Security] Safe JSON parser initialized');

        } catch (error) {
            this.logger.error(`❌ [Security] Security initialization failed: ${error.message}`);
            // Don't throw - allow model to function with limited security
        }
    }

    /**
     * LangChain compatibility method
     */
    _llmType() {
        return `${(this.config && this.config.provider) || this.provider || 'universal'}-${this.apiMode}`;
    }

    /**
     * LangChain v0.3 compliance: Return invocation parameters for tracing
     */
    invocationParams(options = {}) {
        const formattedTools = (this.boundTools || []).map(tool => ({
            type: 'function',
            function: {
                name: tool.name,
                description: tool.description,
                parameters: tool.schema || {}
            }
        }));

        return {
            model_name: this.model,
            api_mode: this.apiMode,
            temperature: this.temperature,
            max_tokens: this.maxTokens,
            top_p: this.topP,
            timeout: this.timeout,
            tools: options?.tools || formattedTools,
            tool_choice: options?.tool_choice || this.toolChoice,
            retry_config: {
                max_retries: this.maxRetries,
                retry_delay: this.retryDelay
            },
            streaming_config: {
                connection_strategy: this.connectionStrategy,
                rate_limiting: this.config.rateLimiting
            }
        };
    }

    // ToolManagementService removed - using simplified approach

    /**
     * Bind tools to the model for function calling
     * Required by LangGraph and LangChain v0.3+ for tool integration
     * @param {Array} tools - Array of tools to bind to the model
     * @param {Object} options - Binding options
     * @returns {BaseChatModel} A new model instance with bound tools
     */
    bindTools(tools, options = {}) {
        this.logger.debug(`🔧 bindTools called with ${tools?.length || 0} tools`);

        // ENHANCED DEBUG: Log detailed tool structure to identify undefined tools
        if (Array.isArray(tools) && tools.length > 0) {
            this.logger.debug(`🔍 Analyzing tools structure:`, tools.map((tool, index) => ({
                index,
                type: typeof tool,
                isNull: tool === null,
                isUndefined: tool === undefined,
                name: tool?.name,
                hasInvoke: !!(tool?.invoke || tool?.func),
                invokeType: typeof (tool?.invoke || tool?.func)
            })));
        }

        // Simple validation and filtering
        const validTools = (tools || []).filter(tool => {
            if (!tool || typeof tool !== 'object') return false;
            if (!tool.name || typeof tool.name !== 'string') return false;
            if (!tool.invoke && !tool.func) return false;
            return true;
        });

        if (validTools.length === 0) {
            this.logger.warn('No valid tools to bind');
            return this;
        }

        // Create bound model with proper prototype chain
        const boundModel = Object.create(Object.getPrototypeOf(this));
        Object.assign(boundModel, this);

        // Store bound tools (merge with existing) and de-duplicate by tool name
        const mergedTools = [...(this.boundTools || []), ...validTools];
        const uniqueTools = [];
        const seenNames = new Set();
        for (const tool of mergedTools) {
            const name = tool && typeof tool.name === 'string' ? tool.name : undefined;
            if (!name) continue;
            if (seenNames.has(name)) continue;
            seenNames.add(name);
            uniqueTools.push(tool);
        }
        boundModel.boundTools = uniqueTools;

        // Set tool choice if provided
        if (options.tool_choice) {
            boundModel.toolChoice = options.tool_choice;
        }

        this.logger.info(`✅ Simple bindTools completed: ${boundModel.boundTools.length} tools bound`);
        return boundModel;
    }

    /**
     * Abstract invoke method - must be implemented by subclasses
     */
    async invoke(messages, options = {}) {
        // Check API usage limits for testing (applies to all models)
        if (this.apiLimits?.testMode && this.apiLimits.currentAttempts >= this.apiLimits.maxAttempts) {
            throw new Error(`API limit reached: ${this.apiLimits.currentAttempts}/${this.apiLimits.maxAttempts} attempts used. Increase VITE_API_MAX_ATTEMPTS or disable VITE_API_TEST_MODE.`);
        }

        this.apiLimits.currentAttempts++;

        // throw new Error('invoke() method must be implemented by subclass');
    }

    // === STREAMING CAPABILITIES ===

    /**
     * Abstract method: Must be implemented by subclasses
     * Build WebSocket URL with provider-specific parameters
     */
    buildWebSocketUrl() {
        throw new Error('buildWebSocketUrl() must be implemented by subclass');
    }

    /**
     * Abstract method: Must be implemented by subclasses
     * Get WebSocket connection options (headers, protocols, etc.)
     */
    getConnectionOptions() {
        throw new Error('getConnectionOptions() must be implemented by subclass');
    }

    /**
     * Abstract method: Must be implemented by subclasses
     * Handle provider-specific message processing
     */
    processProviderMessage(message) {
        throw new Error('processProviderMessage() must be implemented by subclass');
    }

    /**
     * Abstract method: Must be implemented by subclasses
     * Initialize session with provider-specific configuration
     */
    async initializeSession(sessionConfig = {}) {
        throw new Error('initializeSession() must be implemented by subclass');
    }

    /**
     * Abstract method: Send audio to provider
     */
    async _sendAudioToProvider(audioData, format) {
        throw new Error('_sendAudioToProvider() must be implemented by subclass');
    }

    /**
     * Connect to media coordinator for audio streaming (deprecated method)
     * This functionality has been moved to MediaCoordinator services
     * @deprecated Use MediaCoordinator pattern instead
     */
    async startAudioStreaming() {
        this.logger.warn('⚠️ startAudioStreaming() is deprecated. Use MediaCoordinator pattern instead.');
        return false;
    }

    /**
     * Stop audio streaming (deprecated method)
     * @deprecated Use MediaCoordinator pattern instead
     */
    async stopAudioStreaming() {
        this.logger.warn('⚠️ stopAudioStreaming() is deprecated. Use MediaCoordinator pattern instead.');
    }

    /**
     * Send audio data (deprecated - moved to MediaCoordinator)
     * @deprecated Use MediaCoordinator for audio handling
     */
    _sendAudioChunk(audioData, format) {
        this.logger.warn('⚠️ _sendAudioChunk() is deprecated. Use MediaCoordinator pattern instead.');
    }

    /**
     * Establish WebSocket connection using client-to-server strategy
     */
    async connect() {
        if (this.state === StreamingState.CONNECTED || this.state === StreamingState.CONNECTING) {
            this.logger.warn('Connection already established or in progress');
            return true;
        }

        this._setState(StreamingState.CONNECTING);
        this.stats.connectionsAttempted++;

        try {
            const WSClass = await this._getWebSocketClass();
            const wsUrl = this.buildWebSocketUrl();
            const options = this.getConnectionOptions();

            this.logger.info(`🔌 Connecting directly to ${(this.config && this.config.provider) || this.provider || 'provider'}:`,
                wsUrl.replace((this.config && this.config.apiKey) || this.apiKey || '', '[REDACTED]'));

            // Create WebSocket with provider-specific options
            if (this.connectionStrategy === 'browser') {
                // Browser: May need proxy for CORS or use provider SDK
                this.socket = await this._createBrowserConnection(WSClass, wsUrl, options);
            } else {
                // Node.js: Direct connection with headers
                this.socket = new WSClass(wsUrl, options);
            }

            // Set connection timeout
            this.connectionTimeout = setTimeout(() => {
                if (this.state === StreamingState.CONNECTING) {
                    this._handleConnectionTimeout();
                }
            }, this.config.connectionTimeout);

            // Setup WebSocket handlers
            this._setupWebSocketHandlers();

            return new Promise((resolve, reject) => {
                this._connectionPromise = { resolve, reject };
            });

        } catch (error) {
            this.logger.error('❌ Connection attempt failed:', error);
            this._setState(StreamingState.ERROR);
            return false;
        }
    }

    /**
     * Send audio data with rate limiting
     */
    async sendAudio(audioData, format = 'base64') {
        if (!this.isSessionReady()) {
            this.logger.warn('❌ Cannot send audio: session not ready');
            return false;
        }

        // Add to queue if rate limiting is enabled
        if (this.config.rateLimiting.enabled) {
            this.audioQueue.push({ audioData, format, timestamp: Date.now() });
            this._processAudioQueue();
            return true;
        }

        // Send immediately if rate limiting disabled
        return this._sendAudioImmediate(audioData, format);
    }

    /**
     * Check if session is ready for streaming
     */
    isSessionReady() {
        return this.state === StreamingState.SESSION_READY &&
            this.sessionStabilized &&
            this.socket &&
            this.socket.readyState === 1;
    }

    /**
     * Check if connection is active
     */
    isConnected() {
        return this.socket && this.socket.readyState === 1;
    }

    /**
     * Gracefully disconnect
     */
    async disconnect(code = 1000, reason = 'Client disconnect') {
        if (this.state === StreamingState.DISCONNECTED || this.state === StreamingState.CLOSING) {
            return;
        }

        this.logger.info('🔌 Disconnecting from realtime stream');
        this._setState(StreamingState.CLOSING);

        this._clearTimers();
        this._clearAudioQueue();

        if (this.socket && this.socket.readyState === 1) {
            try {
                this.socket.close(code, reason);
            } catch (error) {
                this.logger.warn('Error closing WebSocket:', error);
            }
        }

        this._cleanup();
        this._setState(StreamingState.DISCONNECTED);
    }

    /**
     * Add event listener
     */
    on(event, handler) {
        if (this.eventHandlers[event]) {
            this.eventHandlers[event].push(handler);
            this.logger.debug(`Added event handler for '${event}', total handlers: ${this.eventHandlers[event].length}`);
        } else {
            this.logger.warn(`Unknown event type: ${event}`);
        }
    }

    /**
     * Remove event listener  
     */
    off(event, handler) {
        if (this.eventHandlers[event]) {
            const index = this.eventHandlers[event].indexOf(handler);
            if (index > -1) {
                this.eventHandlers[event].splice(index, 1);
            }
        }
    }

    // === UTILITY METHODS ===

    /**
     * Format messages for API - common implementation with safer type detection
     * @protected
     */
    _formatMessages(messages) {
        return messages.map(message => {
            // Handle both LangChain message objects and plain objects
            let role = 'user'; // default fallback

            if (typeof message._getType === 'function') {
                // Proper LangChain message object
                const messageType = message._getType();
                role = messageType === 'human' ? 'user' :
                    messageType === 'ai' ? 'assistant' :
                        messageType === 'system' ? 'system' : 'user';
            } else if (message.role) {
                // Plain object with role property
                role = message.role === 'human' ? 'user' :
                    message.role === 'ai' ? 'assistant' :
                        message.role;
            } else if (message.constructor && message.constructor.name) {
                // Try to infer from constructor name
                const constructorName = message.constructor.name.toLowerCase();
                if (constructorName.includes('human')) {
                    role = 'user';
                } else if (constructorName.includes('ai')) {
                    role = 'assistant';
                } else if (constructorName.includes('system')) {
                    role = 'system';
                }
            }

            return {
                role,
                content: typeof message.content === 'string' ? message.content :
                    JSON.stringify(message.content)
            };
        });
    }

    /**
     * Format tools for API - common implementation
     * @protected
     */
    _formatToolsForAPI(tools) {
        if (!Array.isArray(tools)) {
            this.logger.warn('🚨 Tools is not an array:', typeof tools);
            return [];
        }

        return tools
            .filter(tool => {
                // Enhanced validation - filter out invalid tools
                if (!tool || typeof tool !== 'object') {
                    this.logger.warn('🚨 Filtering out invalid tool (not an object):', tool);
                    return false;
                }
                if (!tool.name || typeof tool.name !== 'string' || tool.name.trim() === '') {
                    this.logger.warn('🚨 Filtering out tool with invalid name:', tool);
                    return false;
                }
                return true;
            })
            .map(tool => {
                try {
                    // Ensure all required properties exist and are valid
                    const toolName = tool.name.trim();
                    const toolDescription = tool.description || tool.func?.description || `Execute ${toolName}`;
                    const toolParameters = tool.schema || tool.parameters || {
                        type: 'object',
                        properties: {},
                        required: []
                    };

                    return {
                        type: 'function',
                        function: {
                            name: toolName,
                            description: toolDescription,
                            parameters: toolParameters
                        }
                    };
                } catch (error) {
                    this.logger.error('❌ Error formatting tool:', error, tool);
                    return null;
                }
            })
            .filter(formattedTool => {
                // Final validation of formatted tools
                if (!formattedTool ||
                    !formattedTool.function ||
                    !formattedTool.function.name ||
                    typeof formattedTool.function.name !== 'string') {
                    this.logger.warn('🚨 Filtering out malformed tool after formatting');
                    return false;
                }
                return true;
            });
    }

    /**
     * Check if error is retryable - common logic
     * @protected
     */
    _isRetryableError(error) {
        const retryableErrors = [
            'ENOTFOUND',
            'ECONNRESET',
            'ECONNREFUSED',
            'ETIMEDOUT',
            'Request timeout',
            '503', // Service unavailable
            '502', // Bad gateway
            '429', // Rate limit
            '500'  // Internal server error
        ];

        return retryableErrors.some(retryableError =>
            error.message.includes(retryableError) ||
            error.code?.toString().includes(retryableError)
        );
    }

    /**
     * Sleep utility for retry delays
     * @protected
     */
    _sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Enhanced error handling with context
     * @protected
     */
    _handleError(error, context = {}) {
        const errorInfo = {
            message: error.message,
            code: error.code,
            context,
            timestamp: new Date().toISOString(),
            model: this.model,
            apiMode: this.apiMode
        };

        this.logger.error('Model error:', errorInfo);

        // Update metrics
        this.metrics.failures++;

        // Enhance error with context
        const enhancedError = new Error(error.message);
        enhancedError.originalError = error;
        enhancedError.context = context;
        enhancedError.model = this.model;
        enhancedError.apiMode = this.apiMode;

        // Best-effort delegate to central ErrorHandler (non-blocking, preserves sync API)
        try {
            __getGlobalErrorHandler().then(handler => {
                if (handler && typeof handler.handleError === 'function') {
                    handler.handleError(enhancedError, {
                        component: this.constructor?.name || 'BaseChatModel',
                        model: this.model,
                        apiMode: this.apiMode,
                        provider: (this.config && this.config.provider) || this.provider || 'universal',
                        operation: context?.operation,
                        ...context
                    }).catch(() => { });
                }
            }).catch(() => { });
        } catch (_) {
            // Ignore delegation failures
        }

        return enhancedError;
    }

    /**
     * Record performance metrics
     * @protected
     */
    _recordMetrics(responseTime, success = true) {
        this.metrics.requests++;
        this.metrics.lastRequestTime = Date.now();

        if (success) {
            this.metrics.successes++;
            this.metrics.totalResponseTime += responseTime;
            this.metrics.averageResponseTime = this.metrics.totalResponseTime / this.metrics.successes;
            this.metrics.lastResponseTime = responseTime;
        } else {
            this.metrics.failures++;
        }
    }

    /**
     * Health check implementation - can be overridden by subclasses
     */
    async healthCheck() {
        try {
            this.metrics.healthChecks++;

            const startTime = Date.now();

            // Basic connectivity test - override in subclasses for specific tests
            const result = await this._performHealthCheck();

            const responseTime = Date.now() - startTime;

            return {
                healthy: true,
                responseTime,
                model: this.model,
                apiMode: this.apiMode,
                timestamp: new Date().toISOString(),
                ...result
            };

        } catch (error) {
            return {
                healthy: false,
                error: error.message,
                model: this.model,
                apiMode: this.apiMode,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Perform mode-specific health check - to be overridden by subclasses
     * @protected
     */
    async _performHealthCheck() {
        // Default implementation - override in subclasses
        return { basicCheck: true };
    }

    /**
     * Get comprehensive model performance metrics
     */
    getMetrics() {
        const uptime = this.metrics.lastRequestTime ?
            Date.now() - this.metrics.lastRequestTime : 0;

        return {
            // Chat model metrics
            ...this.metrics,
            model: this.model,
            apiMode: this.apiMode,
            boundToolsCount: this.boundTools.length,
            supportsFunctionCalling: this.boundTools.length > 0,
            successRate: this.metrics.requests > 0 ?
                this.metrics.successes / this.metrics.requests : 0,
            errorRate: this.metrics.requests > 0 ?
                this.metrics.failures / this.metrics.requests : 0,
            uptime,

            // Streaming metrics
            streaming: {
                ...this.stats,
                currentState: this.state,
                sessionId: this.sessionId,
                connectionStrategy: this.connectionStrategy,
                queueLength: this.audioQueue.length,
                successRate: this.stats.connectionsAttempted > 0
                    ? (this.stats.connectionsSuccessful / this.stats.connectionsAttempted) * 100
                    : 0
            },

            // Security metrics
            security: {
                hasCredentialManager: !!this.credentialManager,
                hasSecureJsonParser: !!this.jsonParser,
                credentialStatus: this.credentialManager ? this.credentialManager.isValid() : false
            }
        };
    }

    /**
     * Record API cost for cost tracking (shared method for all models)
     * @protected
     */
    async _recordApiCost(messages, response, responseTime, success) {
        try {
            // Import cost tracker from consolidated monitoring service
            const { defaultCostCalculator } = await import('../../models/aliyun/AliyunPricingConfig.js');

            // Cost tracking is always enabled with the new pricing calculator

            // Calculate token counts (approximate)
            const inputTokens = this._estimateTokenCount(messages);
            const outputTokens = this._estimateTokenCount([{ content: response?.content || '' }]);

            // Record the API request cost using new pricing calculator
            defaultCostCalculator.recordCost(this.model, {
                inputTokens,
                outputTokens,
                responseTime,
                success,
                sessionId: this.sessionId || 'default',
                requestType: this.apiMode || 'unknown'
            });

        } catch (error) {
            // Don't let cost tracking errors affect the main flow
            this.logger.debug('Failed to record API cost:', error);
        }
    }

    /**
     * Estimate token count for messages (rough approximation) - shared method
     * @protected
     */
    _estimateTokenCount(messages) {
        if (!Array.isArray(messages)) {
            return 0;
        }

        return messages.reduce((total, message) => {
            const content = typeof message === 'string' ? message :
                typeof message?.content === 'string' ? message.content :
                    JSON.stringify(message?.content || '');

            // Rough estimation: ~4 characters per token for Chinese/English mixed text
            return total + Math.ceil(content.length / 4);
        }, 0);
    }

    /**
     * Check API limits before making requests (shared method)
     * @protected
     */
    _checkApiLimits() {
        if (!this.apiLimits.rateLimitEnabled) {
            return true; // Rate limiting disabled
        }

        // In test mode, enforce strict limits
        if (this.apiLimits.testMode) {
            if (this.apiLimits.currentAttempts >= this.apiLimits.maxAttempts) {
                this.logger.warn(`🚫 API limit reached in test mode: ${this.apiLimits.currentAttempts}/${this.apiLimits.maxAttempts}`);
                return false;
            }

            // Increment attempt counter
            this.apiLimits.currentAttempts++;
            this.logger.debug(`📊 Test mode API usage: ${this.apiLimits.currentAttempts}/${this.apiLimits.maxAttempts}`);
        }

        return true;
    }

    /**
     * Reset API limits (useful for testing or manual reset) - shared method
     */
    resetApiLimits() {
        this.apiLimits.currentAttempts = 0;
        this.logger.info('🔄 API limits reset');
    }

    /**
     * Get current API limits status - shared method
     */
    getApiLimitsStatus() {
        return {
            enabled: this.apiLimits.rateLimitEnabled,
            testMode: this.apiLimits.testMode,
            currentAttempts: this.apiLimits.currentAttempts,
            maxAttempts: this.apiLimits.maxAttempts,
            remainingAttempts: Math.max(0, this.apiLimits.maxAttempts - this.apiLimits.currentAttempts),
            limitReached: this.apiLimits.currentAttempts >= this.apiLimits.maxAttempts
        };
    }

    /**
     * Safe JSON parsing with security validation
     * Available to all subclasses for secure message processing
     * @protected
     */
    _safeJsonParse(jsonString, context = 'unknown') {
        if (!this.jsonParser) {
            this.logger.warn(`🚨 [Security] JSON parser not initialized, falling back to unsafe parsing for ${context}`);
            try {
                return JSON.parse(jsonString);
            } catch (error) {
                this.logger.error(`❌ [Parse] Unsafe JSON parsing failed for ${context}: ${error.message}`);
                throw new Error(`Invalid JSON format in ${context}`);
            }
        }

        try {
            return this.jsonParser.parse(jsonString, this.jsonParser.webSocketSchema);
        } catch (error) {
            if (error instanceof SecurityError) {
                this.logger.error(`🚨 [Security] JSON parsing blocked for ${context}: ${error.message}`);
                throw error;
            }
            this.logger.error(`❌ [Parse] Failed to parse JSON for ${context}: ${error.message}`);
            throw new Error(`Invalid JSON format in ${context}`);
        }
    }

    // Removed _getAuthorizationHeader() method - only used in tests and provider-specific implementations should handle auth

    /**
     * Get masked credential for logging
     * @protected
     */
    _getMaskedCredential() {
        if (!this.credentialManager) {
            return this.apiKey ? `${this.apiKey.slice(0, 4)}****` : 'not-set';
        }

        return this.credentialManager.getMaskedCredential();
    }

    /**
     * Common streaming utilities for both HTTP and WebSocket models
     * @protected
     */

    /**
     * Parse tool arguments safely from streaming chunks (shared utility)
     * @protected
     */
    _parseStreamingToolArguments(argsString) {
        if (!argsString || typeof argsString !== 'string') {
            return {};
        }

        try {
            return JSON.parse(argsString);
        } catch (error) {
            this.logger.warn('Failed to parse streaming tool arguments:', argsString.substring(0, 100));
            return { arguments: argsString };
        }
    }

    /**
     * Handle runManager callbacks for streaming (shared utility)
     * @protected
     */
    async _handleStreamingCallback(runManager, text, chunk) {
        if (runManager && text && typeof runManager.handleLLMNewToken === 'function') {
            try {
                await runManager.handleLLMNewToken(text, chunk);
            } catch (error) {
                this.logger.warn('runManager callback failed:', error.message);
            }
        }
    }

    /**
     * Create AI message with tool calls - common utility
     * @protected
     */
    _createAIMessage(content, toolCalls = [], metadata = {}) {
        return new AIMessage({
            content,
            tool_calls: toolCalls.length > 0 ? toolCalls : undefined,
            response_metadata: {
                model: this.model,
                api_mode: this.apiMode,
                ...metadata
            }
        });
    }

    /**
     * Parse tool calls from API response - common utility
     * @protected
     */
    _parseToolCalls(toolCallsData) {
        if (!Array.isArray(toolCallsData)) {
            return [];
        }

        return toolCallsData.map(call => ({
            id: call.id || `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            name: call.function?.name || call.name,
            args: typeof call.function?.arguments === 'string' ?
                JSON.parse(call.function.arguments) :
                call.function?.arguments || call.args || {}
        }));
    }

    /**
     * Execute tool calls - common implementation
     * @protected
     */
    async _executeToolCalls(toolCalls) {
        const results = [];

        for (const call of toolCalls) {
            try {
                const tool = this.boundTools.find(t => t.name === call.name);

                if (!tool) {
                    results.push({
                        id: call.id,
                        name: call.name,
                        error: `Tool '${call.name}' not found in bound tools`
                    });
                    continue;
                }

                const result = typeof tool.func === 'function' ?
                    await tool.func(call.args) :
                    await tool.call(call.args);

                results.push({
                    id: call.id,
                    name: call.name,
                    result
                });

                this.metrics.toolCalls++;

            } catch (error) {
                results.push({
                    id: call.id,
                    name: call.name,
                    error: error.message
                });
            }
        }

        return results;
    }

    // === PRIVATE STREAMING METHODS ===

    /**
     * Determine connection strategy based on environment
     */
    _determineConnectionStrategy() {
        const isBrowser = typeof window !== 'undefined' && window.WebSocket;

        // Check if provider supports direct browser connections
        if (isBrowser && this.config.supportsBrowserDirectConnection) {
            return 'browser-direct';
        } else if (isBrowser) {
            return 'browser'; // May need proxy
        } else {
            return 'node'; // Direct connection
        }
    }

    /**
     * Get WebSocket class for environment
     */
    async _getWebSocketClass() {
        if (typeof window !== 'undefined' && window.WebSocket) {
            return window.WebSocket;
        }

        try {
            const { default: WebSocket } = await import('ws');
            return WebSocket;
        } catch (error) {
            throw new Error('WebSocket not available. Install ws package for Node.js environments.');
        }
    }

    /**
     * Create browser connection (may use proxy or provider SDK)
     */
    async _createBrowserConnection(WSClass, wsUrl, options) {
        if (this.config.supportsBrowserDirectConnection) {
            // Direct connection supported
            return new WSClass(wsUrl);
        } else {
            // Use proxy or provider SDK
            return this._createProxyConnection(WSClass, wsUrl, options);
        }
    }

    /**
     * Create proxy connection for browser CORS limitations
     */
    async _createProxyConnection(WSClass, wsUrl, options) {
        // Default proxy implementation - subclasses can override
        const proxyUrl = this._buildProxyUrl(wsUrl);
        return new WSClass(proxyUrl);
    }

    /**
     * Build proxy URL (default implementation)
     */
    _buildProxyUrl(originalUrl) {
        // Default proxy logic - subclasses should override
        const url = new URL(originalUrl);
        return `ws://localhost:2994/ws?${url.searchParams.toString()}`;
    }

    /**
     * Setup WebSocket event handlers
     */
    _setupWebSocketHandlers() {
        this.socket.onopen = (event) => this._handleConnectionOpen(event);
        this.socket.onmessage = (event) => this._handleMessage(event);
        this.socket.onerror = (error) => this._handleError(error);
        this.socket.onclose = (event) => this._handleClose(event);
    }

    /**
     * Handle connection open
     */
    _handleConnectionOpen(event) {
        this._clearTimers();
        this._setState(StreamingState.CONNECTED);
        this.stats.connectionsSuccessful++;
        this.stats.lastConnectTime = Date.now();
        this.reconnectAttempts = 0;

        this.logger.info('✅ WebSocket connection established');

        if (this._connectionPromise) {
            this._connectionPromise.resolve(true);
            this._connectionPromise = null;
        }

        // For some providers (like Aliyun), wait for server to initiate session
        // For others (like OpenAI), we need to initialize session ourselves
        const provider = (this.config && this.config.provider) || this.provider || 'unknown';
        if (provider !== 'Aliyun') {
            this.initializeSession().catch(error => {
                this.logger.error('❌ Session initialization failed:', error);
            });
        } else {
            this.logger.debug('🔌 [AliyunFix] Waiting for server to send session.created (prevents 1011 errors)');
        }
    }

    /**
     * Handle incoming messages
     */
    _handleMessage(event) {
        this.stats.messagesReceived++;

        try {
            // Handle both text and binary messages
            let messageData;
            if (event.data instanceof Blob) {
                event.data.text().then(text => {
                    messageData = this._safeJsonParse(text, 'blob message');
                    this._processMessage(messageData, event);
                });
                return;
            } else if (typeof event.data === 'string') {
                messageData = this._safeJsonParse(event.data, 'websocket message');
            } else {
                messageData = event.data;
            }

            this._processMessage(messageData, event);

        } catch (error) {
            if (error instanceof SecurityError) {
                this.logger.error('🚨 [Security] Message blocked due to security violation:', error.message);
                // Don't process potentially malicious messages
                return;
            }
            this.logger.error('❌ Error processing message:', error);
        }
    }

    /**
     * Process parsed message
     */
    _processMessage(messageData, originalEvent) {
        // Log message for debugging
        this.logger.debug('📥 Message received:', {
            type: messageData.type || 'unknown',
            eventId: messageData.event_id || 'none'
        });

        // Let subclass handle provider-specific processing
        const result = this.processProviderMessage(messageData);

        // Emit to event handlers
        this.eventHandlers.message.forEach(handler => {
            try {
                handler(messageData, originalEvent);
            } catch (error) {
                this.logger.error('Error in message handler:', error);
            }
        });

        return result;
    }

    /**
     * Handle connection close
     */
    _handleClose(event) {
        this._clearTimers();

        const closeReason = StreamingCloseCodes[event.code] || `Unknown (${event.code})`;

        this.logger.warn('🔌 Connection closed:', {
            code: event.code,
            reason: event.reason || closeReason,
            wasClean: event.wasClean
        });

        this._cleanup();
        this._setState(StreamingState.DISCONNECTED);

        this.eventHandlers.close.forEach(handler => {
            try {
                handler(event);
            } catch (error) {
                this.logger.error('Error in close handler:', error);
            }
        });

        // Auto-reconnect logic
        if (this._shouldAutoReconnect(event.code)) {
            this._attemptReconnect();
        }
    }

    /**
     * Handle connection timeout
     */
    _handleConnectionTimeout() {
        this.logger.error('❌ Connection timeout');
        this._setState(StreamingState.ERROR);

        if (this.socket) {
            this.socket.close();
        }

        if (this._connectionPromise) {
            this._connectionPromise.reject(new Error('Connection timeout'));
            this._connectionPromise = null;
        }
    }

    /**
     * Process audio queue with rate limiting
     */
    async _processAudioQueue() {
        if (this.isProcessingQueue || this.audioQueue.length === 0) {
            return;
        }

        this.isProcessingQueue = true;

        while (this.audioQueue.length > 0) {
            const now = Date.now();
            const timeSinceLastSend = now - this.lastAudioSendTime;

            if (timeSinceLastSend < this.config.rateLimiting.minIntervalMs) {
                const waitTime = this.config.rateLimiting.minIntervalMs - timeSinceLastSend;
                await new Promise(resolve => setTimeout(resolve, waitTime));
            }

            const audioItem = this.audioQueue.shift();
            if (audioItem) {
                await this._sendAudioImmediate(audioItem.audioData, audioItem.format);
                this.lastAudioSendTime = Date.now();
            }
        }

        this.isProcessingQueue = false;
    }

    /**
     * Send audio immediately (bypassing queue)
     */
    async _sendAudioImmediate(audioData, format) {
        if (!this.isConnected()) {
            this.logger.warn('❌ Cannot send audio: not connected');
            return false;
        }

        try {
            // Subclasses should override this method
            const success = await this._sendAudioToProvider(audioData, format);
            if (success) {
                this.stats.audioChunksSent++;
            }
            return success;
        } catch (error) {
            this.logger.error('❌ Error sending audio:', error);
            return false;
        }
    }

    /**
     * Determine if should auto-reconnect
     */
    _shouldAutoReconnect(code) {
        const noReconnectCodes = [4001, 4003, 4004]; // Auth failures
        return !noReconnectCodes.includes(code) &&
            this.reconnectAttempts < this.config.maxReconnectAttempts;
    }

    /**
     * Attempt reconnection with backoff
     */
    async _attemptReconnect() {
        this.reconnectAttempts++;
        this.stats.reconnections++;

        const delay = Math.min(
            this.config.reconnectBackoffMs * Math.pow(2, this.reconnectAttempts - 1),
            10000
        );

        this.logger.info(`🔄 Attempting reconnection ${this.reconnectAttempts}/${this.config.maxReconnectAttempts} in ${delay}ms`);

        setTimeout(async () => {
            try {
                await this.connect();
            } catch (error) {
                this.logger.error('❌ Reconnection failed:', error);
            }
        }, delay);
    }

    /**
     * Clear all timers
     */
    _clearTimers() {
        if (this.connectionTimeout) {
            clearTimeout(this.connectionTimeout);
            this.connectionTimeout = null;
        }
    }

    /**
     * Clear audio queue
     */
    _clearAudioQueue() {
        this.audioQueue = [];
        this.isProcessingQueue = false;
    }

    /**
     * Cleanup resources
     */
    _cleanup() {
        this._clearTimers();
        this._clearAudioQueue();
        this.socket = null;
        this.sessionId = null;
        this.sessionStabilized = false;
    }

    /**
     * Set state and notify handlers
     */
    _setState(newState) {
        const oldState = this.state;
        this.state = newState;

        if (oldState !== newState) {
            this.logger.debug(`State transition: ${oldState} → ${newState}`);

            this.eventHandlers.stateChange.forEach(handler => {
                try {
                    handler(newState, oldState);
                } catch (error) {
                    this.logger.error('Error in state change handler:', error);
                }
            });
        }
    }

    /**
     * Cleanup resources - enhanced for both streaming and chat model
     */
    async cleanup() {
        // Disconnect streaming if active
        if (this.isConnected()) {
            await this.disconnect();
        }

        // Reset all metrics
        this.reset();

        this.logger.debug(`${this.constructor.name} cleanup completed`);
    }

    /**
     * Reset model state and metrics - enhanced for both streaming and chat model
     */
    reset() {
        // Reset chat model metrics
        this.metrics = {
            requests: 0,
            successes: 0,
            failures: 0,
            totalResponseTime: 0,
            averageResponseTime: 0,
            healthChecks: 0,
            toolCalls: 0,
            lastRequestTime: null,
            lastResponseTime: null
        };

        // Reset streaming stats
        this.stats = {
            connectionsAttempted: 0,
            connectionsSuccessful: 0,
            messagesReceived: 0,
            messagesSent: 0,
            audioChunksSent: 0,
            reconnections: 0,
            lastConnectTime: null,
            uptime: 0
        };

        this.logger.debug(`${this.constructor.name} all metrics reset`);
    }

    // === UTILITY METHODS FOR SUBCLASSES ===

    /**
     * Browser-compatible base64 encoding for audio data
     * Common utility method used by both WebSocket and HTTP models
     * @protected
     */
    _arrayBufferToBase64(uint8Array) {
        let binary = '';
        const len = uint8Array.byteLength;
        for (let i = 0; i < len; i++) {
            binary += String.fromCharCode(uint8Array[i]);
        }
        return btoa(binary);
    }

    /**
     * Calculate adaptive timeout based on request context
     * General method for timeout calculation that can be used by HTTP models
     * @protected
     */
    _calculateAdaptiveTimeout(baseTimeout, context = {}) {
        let calculatedTimeout = baseTimeout;

        // Adjust for streaming
        if (context.streaming && context.streamingTimeout) {
            calculatedTimeout = context.streamingTimeout;
        }

        // Adjust for tool calls (autonomous tools need faster response)
        if (context.hasToolCalls && context.toolCallTimeout) {
            calculatedTimeout = Math.min(calculatedTimeout, context.toolCallTimeout);
        }

        // Adjust for model type if specified
        if (context.modelType === 'fast' && context.fastModelTimeout) {
            calculatedTimeout = Math.min(calculatedTimeout, context.fastModelTimeout);
        }

        // Apply context-specific adjustments
        if (context.messageCount > 10) {
            calculatedTimeout *= 1.1; // Longer conversations need slightly more time
        }

        if (context.urgency === 'high') {
            calculatedTimeout *= 0.8; // Prioritize speed for urgent requests
        }

        // Ensure within bounds
        if (context.maxTimeout) {
            calculatedTimeout = Math.min(calculatedTimeout, context.maxTimeout);
        }
        calculatedTimeout = Math.max(calculatedTimeout, 200); // Minimum 200ms

        return calculatedTimeout;
    }
}

export default BaseChatModel;