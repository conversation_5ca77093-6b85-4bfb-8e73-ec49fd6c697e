/**
 * Universal Provider Error Hierarchy - Structured Error Handling
 * ARCHITECTURAL FIX: Moved from AliyunErrors to be provider-agnostic
 * Provides proper error chaining and context preservation for all providers
 */

/**
 * Base provider error class with proper context preservation
 */
export class ProviderError extends Error {
    constructor(message, originalError = null, context = {}) {
        super(message);
        this.name = this.constructor.name;
        this.originalError = originalError;
        this.context = context;
        this.timestamp = Date.now();
        this.provider = context.provider || 'unknown';
        
        // Preserve original stack trace if available
        if (originalError?.stack) {
            this.stack = originalError.stack;
        }
        
        // Add context to error for better debugging
        this.errorCode = context.code || 'PROVIDER_UNKNOWN';
        this.retryable = context.retryable !== false; // Default to retryable
    }
    
    /**
     * Get detailed error information for logging
     */
    getDetails() {
        return {
            name: this.name,
            message: this.message,
            errorCode: this.errorCode,
            provider: this.provider,
            timestamp: this.timestamp,
            retryable: this.retryable,
            context: this.context,
            originalError: this.originalError ? {
                name: this.originalError.name,
                message: this.originalError.message
            } : null
        };
    }
}

/**
 * Authentication and API key related errors
 */
export class ProviderAuthenticationError extends ProviderError {
    constructor(message, originalError = null, context = {}) {
        super(message, originalError, { 
            ...context, 
            code: `${context.provider?.toUpperCase() || 'PROVIDER'}_AUTH_FAILED`,
            retryable: false // Auth errors typically not retryable
        });
    }
}

/**
 * Network and connection related errors
 */
export class ProviderConnectionError extends ProviderError {
    constructor(message, originalError = null, context = {}) {
        super(message, originalError, { 
            ...context, 
            code: `${context.provider?.toUpperCase() || 'PROVIDER'}_CONNECTION_FAILED`,
            retryable: context.retryable !== false
        });
    }
}

/**
 * Timeout related errors with adaptive context
 */
export class ProviderTimeoutError extends ProviderError {
    constructor(message, originalError = null, context = {}) {
        super(message, originalError, { 
            ...context, 
            code: `${context.provider?.toUpperCase() || 'PROVIDER'}_TIMEOUT`,
            retryable: true
        });
        this.timeoutDuration = context.timeout || 'unknown';
    }
}

/**
 * Rate limiting errors
 */
export class ProviderRateLimitError extends ProviderError {
    constructor(message, originalError = null, context = {}) {
        super(message, originalError, { 
            ...context, 
            code: `${context.provider?.toUpperCase() || 'PROVIDER'}_RATE_LIMITED`,
            retryable: true
        });
        this.retryAfter = context.retryAfter || 1000;
    }
}

/**
 * Model-specific errors (invalid model, model overloaded, etc.)
 */
export class ProviderModelError extends ProviderError {
    constructor(message, originalError = null, context = {}) {
        super(message, originalError, { 
            ...context, 
            code: `${context.provider?.toUpperCase() || 'PROVIDER'}_MODEL_ERROR`
        });
        this.modelName = context.model || 'unknown';
    }
}

/**
 * WebSocket specific errors
 */
export class ProviderWebSocketError extends ProviderError {
    constructor(message, originalError = null, context = {}) {
        super(message, originalError, { 
            ...context, 
            code: `${context.provider?.toUpperCase() || 'PROVIDER'}_WEBSOCKET_ERROR`
        });
        this.closeCode = context.closeCode || null;
        this.retryable = this._isRetryableCloseCode(this.closeCode);
    }
    
    _isRetryableCloseCode(code) {
        // Universal retryable close codes
        const retryableCodes = [
            1006, // Abnormal closure
            1011, // Server error
            1012, // Service restart
            1013, // Try again later
            1014  // Bad gateway
        ];
        return retryableCodes.includes(code);
    }
}

/**
 * Session and realtime specific errors
 */
export class ProviderSessionError extends ProviderError {
    constructor(message, originalError = null, context = {}) {
        super(message, originalError, { 
            ...context, 
            code: `${context.provider?.toUpperCase() || 'PROVIDER'}_SESSION_ERROR`
        });
        this.sessionId = context.sessionId || null;
    }
}

/**
 * Configuration and validation errors
 */
export class ProviderConfigurationError extends ProviderError {
    constructor(message, originalError = null, context = {}) {
        super(message, originalError, { 
            ...context, 
            code: `${context.provider?.toUpperCase() || 'PROVIDER'}_CONFIG_ERROR`,
            retryable: false // Configuration errors are not retryable
        });
    }
}

/**
 * Audio processing and VAD errors
 */
export class ProviderAudioError extends ProviderError {
    constructor(message, originalError = null, context = {}) {
        super(message, originalError, { 
            ...context, 
            code: `${context.provider?.toUpperCase() || 'PROVIDER'}_AUDIO_ERROR`
        });
        this.audioFormat = context.format || 'unknown';
    }
}

/**
 * Quota and billing errors
 */
export class ProviderQuotaError extends ProviderError {
    constructor(message, originalError = null, context = {}) {
        super(message, originalError, { 
            ...context, 
            code: `${context.provider?.toUpperCase() || 'PROVIDER'}_QUOTA_EXCEEDED`,
            retryable: false // Quota errors typically not retryable
        });
        this.quotaType = context.quotaType || 'unknown';
    }
}

/**
 * Enhanced error factory for creating appropriate error types
 */
export class ProviderErrorFactory {
    /**
     * Create appropriate error type based on error information
     * @param {Error} originalError - Original error
     * @param {Object} context - Error context (must include provider)
     * @returns {ProviderError} Appropriate error type
     */
    static createFromError(originalError, context = {}) {
        const message = originalError.message || 'Unknown error';
        const errorString = message.toLowerCase();
        
        // Ensure provider is set for proper error categorization
        if (!context.provider) {
            context.provider = 'unknown';
        }
        
        // Authentication errors
        if (errorString.includes('auth') || 
            errorString.includes('401') || 
            errorString.includes('unauthorized') ||
            errorString.includes('api key') ||
            errorString.includes('4001')) {
            return new ProviderAuthenticationError(message, originalError, context);
        }
        
        // Timeout errors
        if (errorString.includes('timeout') || 
            errorString.includes('timed out')) {
            return new ProviderTimeoutError(message, originalError, context);
        }
        
        // Rate limiting errors (provider-specific codes)
        if (errorString.includes('rate limit') || 
            errorString.includes('4008') ||  // Aliyun
            errorString.includes('429') ||   // Standard HTTP
            errorString.includes('too many requests')) {
            return new ProviderRateLimitError(message, originalError, context);
        }
        
        // Quota errors
        if (errorString.includes('quota') ||
            errorString.includes('billing') ||
            errorString.includes('insufficient funds') ||
            errorString.includes('usage limit')) {
            return new ProviderQuotaError(message, originalError, context);
        }
        
        // Connection errors
        if (errorString.includes('connection') || 
            errorString.includes('network') ||
            errorString.includes('econnrefused') ||
            errorString.includes('websocket') ||
            errorString.includes('1006')) {
            return new ProviderConnectionError(message, originalError, context);
        }
        
        // WebSocket specific errors
        if (context.closeCode || 
            errorString.includes('websocket') ||
            errorString.includes('1006') ||
            errorString.includes('1011') ||
            errorString.includes('1012') ||
            errorString.includes('1013') ||
            errorString.includes('1014')) {
            return new ProviderWebSocketError(message, originalError, context);
        }
        
        // Model errors
        if (errorString.includes('model') ||
            errorString.includes('gpt') ||
            errorString.includes('qwen') ||
            errorString.includes('claude') ||
            context.model) {
            return new ProviderModelError(message, originalError, context);
        }
        
        // Session errors
        if (errorString.includes('session') ||
            context.sessionId) {
            return new ProviderSessionError(message, originalError, context);
        }
        
        // Audio errors
        if (errorString.includes('audio') ||
            errorString.includes('vad') ||
            errorString.includes('4009') ||  // Aliyun audio format error
            context.audioFormat) {
            return new ProviderAudioError(message, originalError, context);
        }
        
        // Configuration errors
        if (errorString.includes('config') ||
            errorString.includes('invalid') ||
            errorString.includes('validation')) {
            return new ProviderConfigurationError(message, originalError, context);
        }
        
        // Default to base error
        return new ProviderError(message, originalError, context);
    }
    
    /**
     * Create error from HTTP response
     * @param {Response} response - HTTP response
     * @param {Object} context - Additional context (must include provider)
     * @returns {ProviderError} Appropriate error type
     */
    static createFromResponse(response, context = {}) {
        const statusContext = {
            ...context,
            status: response.status,
            statusText: response.statusText
        };
        
        switch (response.status) {
            case 401:
                return new ProviderAuthenticationError(
                    `Authentication failed: ${response.statusText}`,
                    null,
                    statusContext
                );
            case 429:
                return new ProviderRateLimitError(
                    `Rate limit exceeded: ${response.statusText}`,
                    null,
                    statusContext
                );
            case 402:
            case 403:
                return new ProviderQuotaError(
                    `Quota exceeded: ${response.statusText}`,
                    null,
                    statusContext
                );
            case 504:
                return new ProviderTimeoutError(
                    `Gateway timeout: ${response.statusText}`,
                    null,
                    statusContext
                );
            default:
                return new ProviderError(
                    `HTTP error ${response.status}: ${response.statusText}`,
                    null,
                    statusContext
                );
        }
    }
    
    /**
     * Create provider-specific error with proper context
     * @param {string} provider - Provider name (aliyun, openai, etc.)
     * @param {string} errorType - Error type
     * @param {string} message - Error message
     * @param {Object} context - Additional context
     * @returns {ProviderError} Appropriate error type
     */
    static createProviderError(provider, errorType, message, context = {}) {
        const fullContext = { ...context, provider };
        
        switch (errorType) {
            case 'auth':
                return new ProviderAuthenticationError(message, null, fullContext);
            case 'timeout':
                return new ProviderTimeoutError(message, null, fullContext);
            case 'rate_limit':
                return new ProviderRateLimitError(message, null, fullContext);
            case 'connection':
                return new ProviderConnectionError(message, null, fullContext);
            case 'websocket':
                return new ProviderWebSocketError(message, null, fullContext);
            case 'model':
                return new ProviderModelError(message, null, fullContext);
            case 'session':
                return new ProviderSessionError(message, null, fullContext);
            case 'audio':
                return new ProviderAudioError(message, null, fullContext);
            case 'config':
                return new ProviderConfigurationError(message, null, fullContext);
            case 'quota':
                return new ProviderQuotaError(message, null, fullContext);
            default:
                return new ProviderError(message, null, fullContext);
        }
    }
}

/**
 * Universal error recovery strategies
 */
export const ProviderErrorRecovery = {
    /**
     * Determine if error is retryable
     * @param {ProviderError} error - Error to check
     * @returns {boolean} Whether error is retryable
     */
    isRetryable(error) {
        if (error.retryable !== undefined) {
            return error.retryable;
        }
        
        // Configuration and authentication errors are not retryable
        if (error instanceof ProviderConfigurationError || 
            error instanceof ProviderAuthenticationError ||
            error instanceof ProviderQuotaError) {
            return false;
        }
        
        // Most other errors are retryable with proper strategy
        return true;
    },
    
    /**
     * Get retry delay for error
     * @param {ProviderError} error - Error instance
     * @param {number} attemptNumber - Current attempt number
     * @returns {number} Delay in milliseconds
     */
    getRetryDelay(error, attemptNumber) {
        if (error instanceof ProviderRateLimitError) {
            return Math.max(error.retryAfter, 1000 * attemptNumber);
        }
        
        // Provider-specific base delays
        const providerDelays = {
            aliyun: 1000,
            openai: 2000,
            anthropic: 1500,
            default: 1000
        };
        
        const baseDelay = providerDelays[error.provider] || providerDelays.default;
        const maxDelay = 30000; // 30 seconds
        const delay = Math.min(baseDelay * Math.pow(2, attemptNumber - 1), maxDelay);
        
        // Add jitter (±25%)
        const jitter = delay * 0.25 * (Math.random() - 0.5);
        return Math.floor(delay + jitter);
    },
    
    /**
     * Get maximum retry attempts for error type
     * @param {ProviderError} error - Error instance
     * @returns {number} Maximum retry attempts
     */
    getMaxRetries(error) {
        if (error instanceof ProviderConfigurationError || 
            error instanceof ProviderAuthenticationError ||
            error instanceof ProviderQuotaError) {
            return 0; // No retries for these error types
        }
        
        if (error instanceof ProviderRateLimitError) {
            return 5; // More retries for rate limiting
        }
        
        if (error instanceof ProviderTimeoutError) {
            return 3; // Moderate retries for timeouts
        }
        
        if (error instanceof ProviderConnectionError ||
            error instanceof ProviderWebSocketError) {
            return 4; // Good retries for connection issues
        }
        
        return 2; // Default retry count
    }
};

export default {
    ProviderError,
    ProviderAuthenticationError,
    ProviderConnectionError,
    ProviderTimeoutError,
    ProviderRateLimitError,
    ProviderModelError,
    ProviderWebSocketError,
    ProviderSessionError,
    ProviderConfigurationError,
    ProviderAudioError,
    ProviderQuotaError,
    ProviderErrorFactory,
    ProviderErrorRecovery
};