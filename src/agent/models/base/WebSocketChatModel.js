/**
 * WebSocket Chat Model Base Class
 * Specialized base class for WebSocket-based realtime chat models
 * 
 * Provides WebSocket-specific functionality including audio streaming,
 * realtime connections, and session management
 */

import { BaseChatModel } from './BaseChatModel.js';
import { StreamingState, StreamingCloseCodes } from './BaseChatModel.js';

// Re-export StreamingState for downstream consumers
export { StreamingState, StreamingCloseCodes };

// Lazy-loaded global ErrorHandler (best-effort, non-blocking)
let __wsGlobalErrorHandlerPromise = null;
function __wsGetGlobalErrorHandler() {
  if (!__wsGlobalErrorHandlerPromise) {
    __wsGlobalErrorHandlerPromise = import('../../arch/dualbrain/services/ErrorHandler.js')
      .then(mod => {
        try {
          if (typeof mod.createErrorHandler === 'function') return mod.createErrorHandler();
          if (typeof mod.ErrorHandler === 'function') return new mod.ErrorHandler();
        } catch (_) { return null; }
        return null;
      })
      .catch(() => null);
  }
  return __wsGlobalErrorHandlerPromise;
}

export class WebSocketChatModel extends BaseChatModel {
  constructor(options = {}) {
    super({ ...options, apiMode: 'websocket' });

    // WebSocket-specific configuration
    this.wsConfig = {
      autoReconnect: options.autoReconnect !== false,
      maxReconnectAttempts: options.maxReconnectAttempts || 5,
      reconnectDelay: options.reconnectDelay || 1000,
      pingInterval: options.pingInterval || 30000,
      pongTimeout: options.pongTimeout || 5000,
      ...options.wsConfig
    };

    // WebSocket state
    this.socket = null;
    this.connectionState = StreamingState.DISCONNECTED;
    this.sessionId = null;
    this.sessionStabilized = false;
    this.reconnectAttempts = 0;

    // Media coordinator connection
    this._connectedMediaCoordinator = null;

    // WebSocket metrics
    this.wsMetrics = {
      connectionsAttempted: 0,
      connectionsSuccessful: 0,
      messagesReceived: 0,
      messagesSent: 0,
      audioChunksSent: 0,
      videoChunksSent: 0,
      textMessagesSent: 0,
      multimodalChunksSent: 0,
      reconnections: 0,
      lastConnectTime: null,
      uptime: 0,
      avgLatency: 0
    };
  }

  /**
   * WebSocket-specific initialization
   * @protected
   */
  _initializeModeSpecific(options) {
    this._validateWebSocketConfiguration();
    this._setupWebSocketEventHandlers();
    this.logger.debug('✅ WebSocket chat model configuration validated');
  }

  /**
   * Validate WebSocket-specific configuration
   * @protected
   */
  _validateWebSocketConfiguration() {
    if (!this.apiKey && !this.wsConfig.endpoint) {
      throw new Error('WebSocket model requires either apiKey or endpoint configuration');
    }
  }

  /**
   * Setup WebSocket event handlers - to be called after socket creation
   * @protected
   */
  _setupWebSocketEventHandlers() {
    // Event handlers will be set up when socket is created
    this.eventHandlers = {
      open: [],
      message: [],
      error: [],
      close: [],
      audioResponse: [],
      sessionReady: []
    };
  }

  /**
   * Connect to media coordinator for audio streaming
   * This method should be called by MediaCoordinator, not directly
   */
  async connectMediaCoordinator(mediaCoordinator) {
    if (!mediaCoordinator) {
      this.logger.warn('⚠️ No media coordinator provided');
      return false;
    }

    this.logger.info('🔗 Connecting to MediaCoordinator for audio streaming');
    this._connectedMediaCoordinator = mediaCoordinator;
    return true;
  }

  /**
   * Disconnect from media coordinator
   */
  async disconnectMediaCoordinator() {
    if (this._connectedMediaCoordinator) {
      this.logger.info('🔗 Disconnecting from MediaCoordinator');
      this._connectedMediaCoordinator = null;
    }
  }

  /**
   * Send modality data through WebSocket (called by MediaCoordinator)
   * Supports audio, video, text, and multimodal input
   * @protected
   */
  sendModalityData(modalityData, type = 'audio', format = 'default') {
    if (!this.isConnected()) {
      this.logger.warn(`❌ Cannot send ${type} data: WebSocket not connected`);
      return false;
    }

    try {
      let success = false;

      switch (type) {
        case 'audio':
          success = this._sendAudioToProvider(modalityData, format);
          if (success) this.wsMetrics.audioChunksSent++;
          break;
        case 'video':
          success = this._sendVideoToProvider(modalityData, format);
          if (success) this.wsMetrics.videoChunksSent = (this.wsMetrics.videoChunksSent || 0) + 1;
          break;
        case 'text':
          success = this._sendTextToProvider(modalityData, format);
          if (success) this.wsMetrics.textMessagesSent = (this.wsMetrics.textMessagesSent || 0) + 1;
          break;
        case 'multimodal':
          success = this._sendMultimodalToProvider(modalityData, format);
          if (success) this.wsMetrics.multimodalChunksSent = (this.wsMetrics.multimodalChunksSent || 0) + 1;
          break;
        default:
          this.logger.warn(`❌ Unsupported modality type: ${type}`);
          return false;
      }

      return success;
    } catch (error) {
      this.logger.error(`❌ Error sending ${type} data:`, error);
      return false;
    }
  }


  /**
   * Get WebSocket class (Node.js vs Browser compatibility)
   * @protected
   */
  async _getWebSocketClass() {
    if (typeof window !== 'undefined') {
      // Browser environment
      return WebSocket;
    } else {
      // Node.js environment
      const { default: WSClass } = await import('ws');
      return WSClass;
    }
  }

  /**
   * Connect to WebSocket endpoint
   */
  async connect() {
    if (this.connectionState === StreamingState.CONNECTED ||
      this.connectionState === StreamingState.CONNECTING) {
      this.logger.warn('Connection already established or in progress');
      return true;
    }

    this._setState(StreamingState.CONNECTING);
    this.wsMetrics.connectionsAttempted++;

    try {
      const WSClass = await this._getWebSocketClass();
      const wsUrl = this.buildWebSocketUrl();
      const options = this.getConnectionOptions();

      this.logger.info(`🔌 Connecting to WebSocket: ${wsUrl.replace(this.apiKey || '', '[REDACTED]')}`);

      // Create WebSocket connection
      this.socket = this._createWebSocketConnection(WSClass, wsUrl, options);

      // Setup event handlers
      this._attachWebSocketHandlers();

      return new Promise((resolve, reject) => {
        this._connectionPromise = { resolve, reject };

        // Connection timeout
        setTimeout(() => {
          if (this.connectionState === StreamingState.CONNECTING) {
            reject(new Error('Connection timeout'));
          }
        }, this.config.connectionTimeout || 10000);
      });

    } catch (error) {
      this.logger.error('❌ Connection attempt failed:', error);
      this._setState(StreamingState.ERROR);
      return false;
    }
  }

  /**
   * Create WebSocket connection - can be overridden by subclasses
   * @protected
   */
  _createWebSocketConnection(WSClass, wsUrl, options) {
    if (typeof window !== 'undefined') {
      // Browser environment
      return new WSClass(wsUrl);
    } else {
      // Node.js environment with headers
      return new WSClass(wsUrl, options);
    }
  }

  /**
   * Attach WebSocket event handlers
   * @protected
   */
  _attachWebSocketHandlers() {
    this.socket.onopen = (event) => this._handleOpen(event);
    this.socket.onmessage = (event) => this._handleMessage(event);
    this.socket.onerror = (error) => this._handleError(error);
    this.socket.onclose = (event) => this._handleClose(event);
  }

  /**
   * Handle WebSocket open event
   * @protected
   */
  _handleOpen(event) {
    this._setState(StreamingState.CONNECTED);
    this.wsMetrics.connectionsSuccessful++;
    this.wsMetrics.lastConnectTime = Date.now();
    this.reconnectAttempts = 0;

    this.logger.info('✅ WebSocket connection established');

    if (this._connectionPromise) {
      this._connectionPromise.resolve(true);
      this._connectionPromise = null;
    }

    // Emit to event handlers
    this._emitEvent('open', event);
  }

  /**
   * Handle incoming WebSocket message
   * @protected
   */
  _handleMessage(event) {
    this.wsMetrics.messagesReceived++;

    try {
      let messageData;

      if (event.data instanceof Blob) {
        // Handle binary data
        event.data.text().then(text => {
          messageData = this._safeJsonParse(text, 'blob message');
          this._processWebSocketMessage(messageData, event);
        });
        return;
      } else if (typeof event.data === 'string') {
        messageData = this._safeJsonParse(event.data, 'websocket message');
      } else {
        messageData = event.data;
      }

      this._processWebSocketMessage(messageData, event);

    } catch (error) {
      this.logger.error('❌ Error processing WebSocket message:', error);
    }
  }

  /**
   * Process WebSocket message - to be implemented by subclasses
   * @protected
   */
  _processWebSocketMessage(messageData, originalEvent) {
    // Default implementation - subclasses should override
    this.logger.debug('📥 WebSocket message received:', {
      type: messageData.type || 'unknown',
      hasData: !!messageData.data
    });

    // Let subclass handle provider-specific processing
    const result = this.processProviderMessage(messageData);

    // Emit to event handlers
    this._emitEvent('message', messageData, originalEvent);

    return result;
  }

  /**
   * Handle WebSocket error
   * @protected
   */
  _handleError(error) {
    this.logger.error('❌ WebSocket error:', error);
    this._emitEvent('error', error);

    // Delegate to central ErrorHandler asynchronously
    try {
      __wsGetGlobalErrorHandler().then(handler => {
        if (handler && typeof handler.handleError === 'function') {
          handler.handleError(error instanceof Error ? error : new Error(String(error)), {
            component: this.constructor?.name || 'WebSocketChatModel',
            model: this.model,
            apiMode: this.apiMode,
            provider: (this.config && this.config.provider) || this.provider || 'universal',
            operation: 'websocket_runtime'
          }).catch(() => { });
        }
      }).catch(() => { });
    } catch (_) {
      // ignore
    }
  }

  /**
   * Handle WebSocket close
   * @protected
   */
  _handleClose(event) {
    const closeReason = StreamingCloseCodes[event.code] || `Unknown (${event.code})`;

    this.logger.warn('🔌 WebSocket connection closed:', {
      code: event.code,
      reason: event.reason || closeReason,
      wasClean: event.wasClean
    });

    this._cleanup();
    this._setState(StreamingState.DISCONNECTED);
    this._emitEvent('close', event);

    // Auto-reconnect if enabled
    if (this.wsConfig.autoReconnect && this._shouldAutoReconnect(event.code)) {
      this._attemptReconnect();
    }
  }

  /**
   * Disconnect WebSocket
   */
  async disconnect(code = 1000, reason = 'Client disconnect') {
    if (this.connectionState === StreamingState.DISCONNECTED) {
      return;
    }

    this.logger.info('🔌 Disconnecting WebSocket');
    this._setState(StreamingState.CLOSING);

    // Disconnect from media coordinator if connected
    await this.disconnectMediaCoordinator();

    if (this.socket && this.socket.readyState === 1) { // WebSocket.OPEN
      try {
        this.socket.close(code, reason);
      } catch (error) {
        this.logger.warn('Error closing WebSocket:', error);
      }
    }

    this._cleanup();
    this._setState(StreamingState.DISCONNECTED);
  }

  /**
   * Check if session is ready for streaming
   */
  isSessionReady() {
    return this.connectionState === StreamingState.SESSION_READY &&
      this.sessionStabilized &&
      this.socket &&
      this.socket.readyState === 1;
  }

  /**
   * Check if WebSocket is connected
   */
  isConnected() {
    return this.socket && this.socket.readyState === 1;
  }

  /**
   * Send message through WebSocket
   * @protected
   */
  _sendWebSocketMessage(message) {
    if (!this.isConnected()) {
      this.logger.warn('❌ Cannot send message: WebSocket not connected');
      return false;
    }

    try {
      const messageString = typeof message === 'string' ? message : JSON.stringify(message);
      this.socket.send(messageString);
      this.wsMetrics.messagesSent++;
      return true;
    } catch (error) {
      this.logger.error('❌ Error sending WebSocket message:', error);
      return false;
    }
  }

  /**
   * Emit event to handlers
   * @protected
   */
  _emitEvent(eventType, ...args) {
    if (this.eventHandlers[eventType]) {
      this.eventHandlers[eventType].forEach(handler => {
        try {
          handler(...args);
        } catch (error) {
          this.logger.error(`Error in ${eventType} handler:`, error);
        }
      });
    }
  }

  /**
   * Set connection state
   * @protected
   */
  _setState(newState) {
    const oldState = this.connectionState;
    this.connectionState = newState;

    if (oldState !== newState) {
      this.logger.debug(`WebSocket state: ${oldState} → ${newState}`);
      this._emitEvent('stateChange', newState, oldState);
    }
  }

  /**
   * Determine if should auto-reconnect
   * @protected
   */
  _shouldAutoReconnect(code) {
    const noReconnectCodes = [4001, 4003, 4004]; // Auth failures
    return !noReconnectCodes.includes(code) &&
      this.reconnectAttempts < this.wsConfig.maxReconnectAttempts;
  }

  /**
   * Attempt reconnection with backoff
   * @protected
   */
  async _attemptReconnect() {
    this.reconnectAttempts++;
    this.wsMetrics.reconnections++;

    const delay = Math.min(
      this.wsConfig.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1),
      10000
    );

    this.logger.info(`🔄 Attempting reconnection ${this.reconnectAttempts}/${this.wsConfig.maxReconnectAttempts} in ${delay}ms`);

    setTimeout(async () => {
      try {
        await this.connect();
      } catch (error) {
        this.logger.error('❌ Reconnection failed:', error);
      }
    }, delay);
  }

  /**
   * Cleanup WebSocket resources
   * @protected
   */
  _cleanup() {
    this.socket = null;
    this.sessionId = null;
    this.sessionStabilized = false;
  }

  /**
   * WebSocket-specific health check
   */
  async _performHealthCheck() {
    if (!this.isConnected()) {
      throw new Error('WebSocket not connected');
    }

    return {
      connected: true,
      sessionId: this.sessionId,
      sessionStabilized: this.sessionStabilized,
      mediaCoordinatorConnected: !!this._connectedMediaCoordinator,
      messagesSent: this.wsMetrics.messagesSent,
      messagesReceived: this.wsMetrics.messagesReceived
    };
  }

  /**
   * Get WebSocket-specific metrics
   */
  getWebSocketMetrics() {
    return {
      ...this.wsMetrics,
      connectionState: this.connectionState,
      reconnectAttempts: this.reconnectAttempts,
      mediaCoordinatorConnected: !!this._connectedMediaCoordinator,
      successRate: this.wsMetrics.connectionsAttempted > 0 ?
        (this.wsMetrics.connectionsSuccessful / this.wsMetrics.connectionsAttempted * 100).toFixed(2) + '%' : '0%'
    };
  }

  /**
   * Enhanced metrics including WebSocket-specific data
   */
  getMetrics() {
    return {
      ...super.getMetrics(),
      websocket: this.getWebSocketMetrics(),
      streaming: {
        state: this.connectionState,
        sessionReady: this.isSessionReady(),
        mediaCoordinatorConnected: !!this._connectedMediaCoordinator
      }
    };
  }

  /**
   * Add event listener
   */
  on(eventType, handler) {
    if (this.eventHandlers[eventType]) {
      this.eventHandlers[eventType].push(handler);
    } else {
      this.logger.warn(`Unknown WebSocket event type: ${eventType}`);
    }
  }

  /**
   * Remove event listener
   */
  off(eventType, handler) {
    if (this.eventHandlers[eventType]) {
      const index = this.eventHandlers[eventType].indexOf(handler);
      if (index > -1) {
        this.eventHandlers[eventType].splice(index, 1);
      }
    }
  }

  /**
   * Main invoke method - routes between WebSocket and HTTP modes
   * This is the critical method that was missing and causing DualBrain failures
   * @override
   */
  async invoke(messages, options = {}) {
    // CRITICAL: Call super.invoke() first to apply API limiting from BaseChatModel
    await super.invoke(messages, options);

    // Check if we should use WebSocket mode based on configuration or options
    if (this._shouldUseWebSocket(messages, options)) {
      // Use WebSocket for real-time streaming scenarios
      return this._invokeWebSocket(messages, options);
    } else {
      // Fall back to HTTP mode for standard text interactions
      return this._invokeHttpFallback(messages, options);
    }
  }

  /**
   * Determine if WebSocket mode should be used
   * @protected
   */
  _shouldUseWebSocket(messages, options) {
    // Use WebSocket if:
    // 1. Explicitly requested via options
    // 2. Model is configured for audio/streaming modalities
    // 3. Options indicate real-time requirements
    return (
      options.streaming === true ||
      options.realtime === true ||
      options.websocket === true ||
      (this.modalities && this.modalities.includes('audio')) ||
      this.apiMode === 'websocket'
    );
  }

  /**
   * WebSocket-based invocation for real-time scenarios
   * Default implementation - subclasses should override for provider-specific logic
   * @protected
   */
  async _invokeWebSocket(messages, options = {}) {
    // Default WebSocket implementation
    // Subclasses should override this method for provider-specific WebSocket logic
    throw new Error('_invokeWebSocket() must be implemented by subclass for WebSocket functionality');
  }

  /**
   * HTTP fallback invocation for standard text interactions
   * This delegates to a standard HTTP-based chat model implementation
   * @protected
   */
  async _invokeHttpFallback(messages, options = {}) {
    // For HTTP fallback, we need to create or use an HTTP-based model
    // This is the missing piece that was causing the invoke() error

    try {
      this.logger.info('🔄 Falling back to HTTP mode for text-only interaction');

      // Create an HTTP-based model instance for fallback
      const httpModel = await this._createHttpFallbackModel();

      if (!httpModel) {
        throw new Error('HTTP fallback model not available');
      }

      // Delegate to HTTP model
      const result = await httpModel.invoke(messages, {
        ...options,
        // Ensure HTTP mode
        streaming: false,
        websocket: false,
        apiMode: 'http'
      });

      return result;

    } catch (error) {
      this.logger.error('❌ HTTP fallback failed:', error);

      // If both WebSocket and HTTP fail, provide a meaningful error
      throw new Error(`Both WebSocket and HTTP modes failed. WebSocket: ${this.constructor.name} not fully implemented. HTTP fallback: ${error.message}`);
    }
  }

  /**
   * Create HTTP fallback model instance
   * Subclasses should override this to provide their specific HTTP implementation
   * @protected
   */
  async _createHttpFallbackModel() {
    // Default implementation - subclasses should override
    // This could import and create provider-specific HTTP models
    this.logger.warn('⚠️ HTTP fallback model creation not implemented by subclass');

    // Try to create a generic HTTP model using the same configuration
    try {
      // Import the appropriate HTTP model class dynamically
      const moduleName = this.constructor.name.replace('WebSocket', '').replace('ChatModel', '');
      const httpModelPath = `../${moduleName.toLowerCase()}/${moduleName}HttpChatModel.js`;

      this.logger.debug(`Attempting to load HTTP fallback: ${httpModelPath}`);

      // This is a fallback attempt - may not work for all providers
      return null; // Subclasses should implement proper fallback

    } catch (error) {
      this.logger.debug('Could not auto-create HTTP fallback model:', error.message);
      return null;
    }
  }

  // Abstract methods to be implemented by subclasses
  buildWebSocketUrl() { throw new Error('buildWebSocketUrl() must be implemented by subclass'); }
  getConnectionOptions() { throw new Error('getConnectionOptions() must be implemented by subclass'); }
  processProviderMessage(message) { throw new Error('processProviderMessage() must be implemented by subclass'); }
  async initializeSession(sessionConfig = {}) { throw new Error('initializeSession() must be implemented by subclass'); }

  // Modality-specific abstract methods
  async _sendAudioToProvider(audioData, format) { throw new Error('_sendAudioToProvider() must be implemented by subclass'); }
  async _sendVideoToProvider(videoData, format) { throw new Error('_sendVideoToProvider() must be implemented by subclass'); }
  async _sendTextToProvider(textData, format) { throw new Error('_sendTextToProvider() must be implemented by subclass'); }
  async _sendMultimodalToProvider(multimodalData, format) { throw new Error('_sendMultimodalToProvider() must be implemented by subclass'); }

  /**
   * Cleanup WebSocket resources
   */
  async cleanup() {
    if (this.isConnected()) {
      await this.disconnect();
    }

    await this.disconnectMediaCoordinator();
    await super.cleanup();
  }
}

export default WebSocketChatModel;