/**
 * Aliyun DashScope Pricing Configuration
 * Based on official pricing from Bailian Console: https://bailian.console.aliyun.com/
 * 
 * Updated: 2024 - Real-time pricing for cost monitoring
 */

import { createLogger } from '@/utils/logger';

const logger = createLogger('AliyunPricing');

/**
 * Aliyun DashScope Model Pricing Configuration
 * Prices are per 1K tokens (input/output may differ)
 */
export const ALIYUN_MODEL_PRICING = {
  // Qwen Models
  'qwen-turbo': {
    input: 0.0008,  // ¥0.0008 per 1K input tokens
    output: 0.002,  // ¥0.002 per 1K output tokens
    currency: 'CNY',
    tier: 'basic',
    description: 'Cost-optimized model for simple tasks'
  },

  'qwen-plus': {
    input: 0.004,   // ¥0.004 per 1K input tokens
    output: 0.012,  // ¥0.012 per 1K output tokens
    currency: 'CNY',
    tier: 'standard',
    description: 'Balanced performance and cost'
  },

  'qwen-max': {
    input: 0.02,    // ¥0.02 per 1K input tokens
    output: 0.06,   // ¥0.06 per 1K output tokens
    currency: 'CNY',
    tier: 'premium',
    description: 'High-performance model for complex tasks'
  },

  'qwen-omni-turbo-realtime': {
    input: 0.006,   // ¥0.006 per 1K input tokens
    output: 0.018,  // ¥0.018 per 1K output tokens
    realtime: 0.001, // ¥0.001 per second of realtime connection
    currency: 'CNY',
    tier: 'realtime',
    description: 'Real-time multimodal model with WebSocket'
  },

  // VL Models (Vision-Language)
  'qwen-vl-plus': {
    input: 0.008,   // ¥0.008 per 1K input tokens
    output: 0.024,  // ¥0.024 per 1K output tokens
    image: 0.001,   // ¥0.001 per image
    currency: 'CNY',
    tier: 'vision',
    description: 'Vision-language model for multimodal tasks'
  },

  'qwen-vl-max': {
    input: 0.02,    // ¥0.02 per 1K input tokens
    output: 0.06,   // ¥0.06 per 1K output tokens
    image: 0.002,   // ¥0.002 per image
    currency: 'CNY',
    tier: 'vision-premium',
    description: 'Premium vision-language model'
  }
};

/**
 * Currency conversion rates (CNY to other currencies)
 * Should be updated regularly or fetched from external API
 */
export const CURRENCY_RATES = {
  CNY_TO_USD: 0.14,  // 1 CNY = 0.14 USD (approximate)
  CNY_TO_EUR: 0.13,  // 1 CNY = 0.13 EUR (approximate)
  CNY_TO_JPY: 20.5   // 1 CNY = 20.5 JPY (approximate)
};

/**
 * Cost calculation utilities
 */
export class AliyunCostCalculator {
  constructor(options = {}) {
    this.logger = logger;
    this.defaultCurrency = options.currency || 'CNY';
    this.sessionCosts = new Map();
    this.totalCosts = {
      CNY: 0,
      USD: 0,
      requests: 0,
      tokens: { input: 0, output: 0 },
      realtimeSeconds: 0,
      images: 0
    };
  }

  /**
   * Calculate cost for a model request
   */
  calculateRequestCost(modelName, usage, options = {}) {
    const pricing = ALIYUN_MODEL_PRICING[modelName];
    if (!pricing) {
      this.logger.warn(`No pricing data for model: ${modelName}`);
      return { cost: 0, currency: 'CNY', breakdown: {} };
    }

    const breakdown = {};
    let totalCost = 0;

    // Token costs
    if (usage.inputTokens) {
      const inputCost = (usage.inputTokens / 1000) * pricing.input;
      breakdown.input = { tokens: usage.inputTokens, cost: inputCost };
      totalCost += inputCost;
    }

    if (usage.outputTokens) {
      const outputCost = (usage.outputTokens / 1000) * pricing.output;
      breakdown.output = { tokens: usage.outputTokens, cost: outputCost };
      totalCost += outputCost;
    }

    // Realtime connection cost
    if (usage.realtimeSeconds && pricing.realtime) {
      const realtimeCost = usage.realtimeSeconds * pricing.realtime;
      breakdown.realtime = { seconds: usage.realtimeSeconds, cost: realtimeCost };
      totalCost += realtimeCost;
    }

    // Image processing cost
    if (usage.images && pricing.image) {
      const imageCost = usage.images * pricing.image;
      breakdown.images = { count: usage.images, cost: imageCost };
      totalCost += imageCost;
    }

    // Convert currency if requested
    const targetCurrency = options.currency || this.defaultCurrency;
    const convertedCost = this.convertCurrency(totalCost, 'CNY', targetCurrency);

    return {
      cost: convertedCost,
      currency: targetCurrency,
      originalCost: totalCost,
      originalCurrency: 'CNY',
      breakdown,
      model: modelName,
      tier: pricing.tier,
      timestamp: Date.now()
    };
  }

  /**
   * Convert between currencies
   */
  convertCurrency(amount, from, to) {
    if (from === to) return amount;

    // Convert from CNY to other currencies
    if (from === 'CNY') {
      switch (to) {
        case 'USD': return amount * CURRENCY_RATES.CNY_TO_USD;
        case 'EUR': return amount * CURRENCY_RATES.CNY_TO_EUR;
        case 'JPY': return amount * CURRENCY_RATES.CNY_TO_JPY;
        default: return amount;
      }
    }

    // Convert to CNY from other currencies
    if (to === 'CNY') {
      switch (from) {
        case 'USD': return amount / CURRENCY_RATES.CNY_TO_USD;
        case 'EUR': return amount / CURRENCY_RATES.CNY_TO_EUR;
        case 'JPY': return amount / CURRENCY_RATES.CNY_TO_JPY;
        default: return amount;
      }
    }

    // For other conversions, go through CNY
    const cnyAmount = this.convertCurrency(amount, from, 'CNY');
    return this.convertCurrency(cnyAmount, 'CNY', to);
  }

  /**
   * Record a request cost
   */
  recordCost(modelName, usage, sessionId = 'default') {
    const cost = this.calculateRequestCost(modelName, usage);

    // Update session costs
    if (!this.sessionCosts.has(sessionId)) {
      this.sessionCosts.set(sessionId, {
        totalCost: 0,
        requests: 0,
        models: new Map(),
        startTime: Date.now()
      });
    }

    const session = this.sessionCosts.get(sessionId);
    session.totalCost += cost.originalCost;
    session.requests++;

    // Track per-model costs in session
    const modelKey = modelName;
    if (!session.models.has(modelKey)) {
      session.models.set(modelKey, { cost: 0, requests: 0, tokens: { input: 0, output: 0 } });
    }

    const modelStats = session.models.get(modelKey);
    modelStats.cost += cost.originalCost;
    modelStats.requests++;
    if (usage.inputTokens) modelStats.tokens.input += usage.inputTokens;
    if (usage.outputTokens) modelStats.tokens.output += usage.outputTokens;

    // Update total costs
    this.totalCosts.CNY += cost.originalCost;
    this.totalCosts.USD += cost.cost; // Assuming cost is in target currency
    this.totalCosts.requests++;
    if (usage.inputTokens) this.totalCosts.tokens.input += usage.inputTokens;
    if (usage.outputTokens) this.totalCosts.tokens.output += usage.outputTokens;
    if (usage.realtimeSeconds) this.totalCosts.realtimeSeconds += usage.realtimeSeconds;
    if (usage.images) this.totalCosts.images += usage.images;

    this.logger.debug(`💰 Recorded cost for ${modelName}: ¥${cost.originalCost.toFixed(4)} (${cost.cost.toFixed(4)} ${cost.currency})`, {
      session: sessionId,
      tokens: usage,
      breakdown: cost.breakdown
    });

    return cost;
  }

  /**
   * Get session cost summary
   */
  getSessionSummary(sessionId = 'default') {
    const session = this.sessionCosts.get(sessionId);
    if (!session) return null;

    const duration = Date.now() - session.startTime;
    const modelBreakdown = Array.from(session.models.entries()).map(([model, stats]) => ({
      model,
      ...stats,
      costUSD: this.convertCurrency(stats.cost, 'CNY', 'USD')
    }));

    return {
      sessionId,
      duration,
      totalCost: {
        CNY: session.totalCost,
        USD: this.convertCurrency(session.totalCost, 'CNY', 'USD')
      },
      requests: session.requests,
      models: modelBreakdown,
      averageCostPerRequest: {
        CNY: session.totalCost / session.requests,
        USD: this.convertCurrency(session.totalCost / session.requests, 'CNY', 'USD')
      }
    };
  }

  /**
   * Get total cost summary
   */
  getTotalSummary(currency = 'CNY') {
    const total = this.convertCurrency(this.totalCosts.CNY, 'CNY', currency);

    return {
      totalCost: total,
      currency,
      originalCost: this.totalCosts.CNY,
      requests: this.totalCosts.requests,
      tokens: this.totalCosts.tokens,
      realtimeSeconds: this.totalCosts.realtimeSeconds,
      images: this.totalCosts.images,
      averageCostPerRequest: total / (this.totalCosts.requests || 1),
      activeSessions: this.sessionCosts.size
    };
  }

  /**
   * Reset all cost tracking
   */
  reset() {
    this.sessionCosts.clear();
    this.totalCosts = {
      CNY: 0,
      USD: 0,
      requests: 0,
      tokens: { input: 0, output: 0 },
      realtimeSeconds: 0,
      images: 0
    };
    this.logger.info('💰 Cost tracking reset');
  }

  /**
   * Estimate cost for planned usage
   */
  estimateCost(modelName, estimatedUsage) {
    return this.calculateRequestCost(modelName, estimatedUsage);
  }
}

/**
 * Default cost calculator instance
 */
export const defaultCostCalculator = new AliyunCostCalculator();

/**
 * Helper function to get model pricing info
 */
export function getModelPricing(modelName) {
  return ALIYUN_MODEL_PRICING[modelName] || null;
}

/**
 * Helper function to estimate request cost
 */
export function estimateRequestCost(modelName, inputTokens, outputTokens = 0, options = {}) {
  return defaultCostCalculator.calculateRequestCost(modelName, {
    inputTokens,
    outputTokens,
    ...options
  });
}

export default {
  ALIYUN_MODEL_PRICING,
  CURRENCY_RATES,
  AliyunCostCalculator,
  defaultCostCalculator,
  getModelPricing,
  estimateRequestCost
};