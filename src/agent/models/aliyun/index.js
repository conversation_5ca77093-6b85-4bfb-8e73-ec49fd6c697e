/**
 * <PERSON><PERSON>wen-Omni Real-time Models - Integrated Architecture
 * Consolidated architecture with integrated realtime functionality and state management
 */

// Core Aliyun Components
export { AliyunWebSocketChatModel } from './AliyunWebSocketChatModel.js';
// Legacy alias for backward compatibility
export { AliyunWebSocketChatModel as AliyunBailianChatModel } from './AliyunWebSocketChatModel.js';
export { AliyunHttpChatModel } from './AliyunHttpChatModel.js';
export { AliyunModelFactory } from './AliyunModelFactory.js';
// AliyunStateManager removed - functionality moved to ConnectionCoordinationService

// State Management Definitions

// Configuration and utilities (merged from types/index.js)
export * from './AliyunConfig.js';

// Import for default export
import { AliyunWebSocketChatModel, AliyunBailianChatModel } from './AliyunWebSocketChatModel.js';
import { AliyunHttpChatModel } from './AliyunHttpChatModel.js';
import { AliyunModelFactory } from './AliyunModelFactory.js';


// Default export for convenience
export default {
    AliyunWebSocketChatModel,
    AliyunBailianChatModel, // Backward compatibility
    AliyunHttpChatModel,
    AliyunModelFactory,
    // AliyunStateManager removed
}; 