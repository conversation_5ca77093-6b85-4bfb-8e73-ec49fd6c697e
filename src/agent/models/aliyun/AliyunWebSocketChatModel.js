/**
 * <PERSON>yun WebSocket Chat Model (Refactored)
 * Universal WebSocket base classes with Aliyun provider integration
 * 
 * MISSION: Eliminate redundancy and integrate with BaseSessionCoordinator
 * for reliable realtime connection management
 */

import { WebSocketChatModel, StreamingState } from '../base/WebSocketChatModel.js';
import { BaseSessionCoordinator, SessionState } from '../base/BaseSessionCoordinator.js';
// Use consolidated provider config
import { getProviderConfig } from '../../config/AgentConfig.js';
import { UniversalEvents, ResponseEvents, VADEvents } from '../base/SessionEventTypes.js';
import { AliyunEventType, AliyunLegacyEventType } from './AliyunConfig.js';
import { AIMessage } from '@langchain/core/messages';
import { createLogger, LogLevel, setModuleLogLevel } from '../../../utils/logger.ts';
// import { RealtimeAudioManager } from '../../../media/modality/audio.ts'; // Commenting out TypeScript import for testing
import { arrayBufferToBase64 } from '../../../media/modality/audio.ts';
import { ALIYUN_MODELS, ALIYUN_AUDIO_CONFIG, ALIYUN_SAMPLE_RATE, generateEventId, buildValidSessionConfig, createPythonCompatibleSessionUpdate } from './AliyunConfig.js';
import { ConnectionManager } from '../../services/connection/ConnectionManager.js';
import { getDownloadServerPort } from '../../../utils/portManager.js';

// Set appropriate logging level for debugging WebSocket issues
setModuleLogLevel('AliyunWebSocketChatModel', LogLevel.DEBUG);

export class AliyunWebSocketChatModel extends WebSocketChatModel {
    constructor(options = {}) {
        // Get Aliyun provider configuration
        const providerConfig = getProviderConfig('aliyun');

        // Configure for WebSocketChatModel with universal base classes
        super({
            ...options,
            provider: 'aliyun',
            apiMode: 'websocket',
            // CRITICAL FIX: Use correct model for realtime mode
            model: options.model || ALIYUN_MODELS.DEFAULT_REALTIME,
            wsConfig: {
                endpoint: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime',
                supportsBrowserDirectConnection: false, // Aliyun needs proper auth params
                connectionTimeout: 15000,
                stabilizationTimeout: 8000,
                maxReconnectAttempts: 3
            },
            audioConfig: {
                sampleRate: ALIYUN_SAMPLE_RATE, // CRITICAL: Must be 16kHz for realtime
                bitDepth: ALIYUN_AUDIO_CONFIG.bitDepth,
                channels: ALIYUN_AUDIO_CONFIG.channels,
                format: ALIYUN_AUDIO_CONFIG.format
            },
            rateLimiting: {
                enabled: true,
                maxChunksPerSecond: ALIYUN_AUDIO_CONFIG.maxChunksPerSecond,
                minIntervalMs: ALIYUN_AUDIO_CONFIG.minIntervalMs
            }
        });

        // Store original options for later use
        this.options = options;

        // Initialize logger
        this.logger = createLogger('AliyunWebSocketChatModel', LogLevel.INFO);

        // Initialize session coordinator with Aliyun provider config
        this.sessionCoordinator = new BaseSessionCoordinator({
            provider: 'aliyun',
            providerConfig: providerConfig.sessionDefaults,
            eventMappings: providerConfig.eventMappings,
            vadConfig: providerConfig.vadConfig,
            sessionUpdateHandler: this._createAliyunSessionUpdate.bind(this),
            maxRecoveryAttempts: 3
        });

        // Bind session coordinator events
        this._setupSessionCoordinatorEvents();

        // Initialize audio manager (temporarily disabled for testing)
        // this.realtimeAudioManager = new RealtimeAudioManager({
        //     sampleRate: providerConfig.sessionDefaults.sample_rate || 16000,
        //     numChannels: 1,
        //     bitDepth: 16,
        //     minIntervalMs: 200,
        //     enableDebugLogging: false,
        //     logger: this.logger,
        //     onError: (error) => this.logger.error('❌ [AudioManager] Error:', error)
        // });

        // Session state - simplified using BaseSessionCoordinator
        this.realtimeSessionId = null;
        this.conversationId = null;

        // Connection attempt throttling to prevent rapid successive attempts
        this._lastConnectionAttempt = 0;
        this._connectionAttemptDelay = 2000; // 2 seconds minimum between attempts
        this._connectionInProgress = false;

        // Aliyun-specific configuration
        this.modalities = options.modalities || ['text', 'audio'];
        this.audioConfig = {
            voice: options.voice || ALIYUN_AUDIO_CONFIG.defaultVoice, // Use supported realtime voice
            format: options.format || ALIYUN_AUDIO_CONFIG.format // Must be pcm16 for realtime
        };

        // Enhanced prompt support
        this.language = options.language || 'english';
        this.gender = options.gender || null;
        this.mood = options.mood || 'neutral';
        this.enablePrompts = options.enablePrompts !== false;

        this.logger.info('🔧 AliyunWebSocketChatModel initialized with BaseSessionCoordinator');
    }

    /**
     * Override bindTools to prevent tool binding for System 1 (realtime) brain
     * System 1 should not execute tools; it only supplies multimodal context and optional audio
     */
    bindTools() {
        this.logger.debug('AliyunWebSocketChatModel.bindTools() ignored (System 1 does not use tools)');
        return this;
    }

    /**
     * Setup session coordinator event handlers
     * @private
     */
    _setupSessionCoordinatorEvents() {
        // Session lifecycle events
        this.sessionCoordinator.on('session_created', (data) => {
            this.realtimeSessionId = data.sessionId;
            this.logger.info('🎬 Session created:', { sessionId: this.realtimeSessionId });
            this._emitEvent('sessionReady', { sessionId: this.realtimeSessionId, session: data.session });
        });

        this.sessionCoordinator.on('session_updated', (data) => {
            this.logger.info('🔄 Session updated:', { sessionId: data.sessionId });
        });

        this.sessionCoordinator.on('conversation_created', (data) => {
            this.conversationId = data.conversationId;
            this.logger.info('💬 Conversation created:', { conversationId: this.conversationId });
        });

        // Connection events
        this.sessionCoordinator.on('connection_established', () => {
            this.logger.info('🔗 Connection established via BaseSessionCoordinator');
        });

        this.sessionCoordinator.on('connection_lost', () => {
            this.logger.warn('🔗 Connection lost - BaseSessionCoordinator will handle recovery');
        });

        // Error events
        this.sessionCoordinator.on('connection_error', (error) => {
            this.logger.error('🔗 Connection error:', error);
            this._emitEvent('error', error);
        });

        // Provider response events → forward to model handlers using universal events
        // Audio stream
        this.sessionCoordinator.on(ResponseEvents.AUDIO_RESPONSE_DELTA, (messageData) => {
            this._handleAudioResponse(messageData);
        });
        this.sessionCoordinator.on(ResponseEvents.AUDIO_RESPONSE_GENERATED, (messageData) => {
            this._handleAudioResponse(messageData);
        });

        // Text transcript
        this.sessionCoordinator.on(ResponseEvents.TEXT_RESPONSE_DELTA, (messageData) => {
            this._handleTranscriptEvent(messageData);
        });
        this.sessionCoordinator.on(ResponseEvents.TEXT_RESPONSE_GENERATED, (messageData) => {
            this._handleTranscriptEvent(messageData);
        });

        // VAD events
        this.sessionCoordinator.on(VADEvents.SPEECH_STARTED, (messageData) => {
            this._handleVADEvent(messageData);
        });
        this.sessionCoordinator.on(VADEvents.SPEECH_STOPPED, (messageData) => {
            this._handleVADEvent(messageData);
        });
    }

    /**
     * Create Aliyun-specific session update data
     * @private
     */
    _createAliyunSessionUpdate(updateConfig, currentSession) {
        return {
            input: {
                audio: {
                    voice: updateConfig.voice || this.audioConfig.voice,
                    format: updateConfig.format || 'pcm',
                    sample_rate: updateConfig.sample_rate || 16000
                }
            },
            model: updateConfig.model || this.model,
            parameters: {
                temperature: updateConfig.temperature || this.temperature,
                max_tokens: updateConfig.max_tokens || this.maxTokens,
                top_p: updateConfig.top_p || this.topP
            }
        };
    }

    /**
     * Build WebSocket URL for Aliyun DashScope
     * FIXED: Include API key in URL query parameters as required by Aliyun WebSocket API
     * @override
     */
    buildWebSocketUrl() {
        // CRITICAL FIX: Use local proxy when available to prevent "Close received after close" errors
        // Check if we should use the local proxy (for development/server environments)
        const useLocalProxy = this.options.useLocalProxy ||
            typeof window === 'undefined' ||
            window.location?.hostname === 'localhost';

        if (useLocalProxy) {
            // Use local proxy endpoint - this routes through AliyunRealtimeProxy
            // CRITICAL FIX: Use portManager to get the correct backend server port
            const backendPort = getDownloadServerPort();

            const proxyBaseUrl = this.options.proxyEndpoint ||
                (typeof window !== 'undefined' ?
                    `ws://${window.location.hostname}:${backendPort}/ws` :
                    `ws://localhost:${backendPort}/ws`);

            // For local proxy, include model as query parameter
            const params = new URLSearchParams({
                model: this.model || ALIYUN_MODELS.DEFAULT_REALTIME
            });

            const url = `${proxyBaseUrl}?${params.toString()}`;
            this.logger.info('🔗 Built WebSocket URL (via proxy):', url);
            return url;
        } else {
            // Direct connection to Aliyun (for production with proper CORS/networking)
            const baseUrl = 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime';

            if (!this.apiKey) {
                throw new Error('API key is required for Aliyun DashScope WebSocket connection');
            }

            // SECURITY FIX: Only include model in URL, move API key to headers
            const params = new URLSearchParams({
                model: this.model || ALIYUN_MODELS.DEFAULT_REALTIME
            });

            const url = `${baseUrl}?${params.toString()}`;
            this.logger.info('🔗 Built WebSocket URL (direct, secure):', url.replace(this.apiKey || '', '[REDACTED]'));
            return url;
        }
    }

    /**
     * Get connection options for Aliyun
     * FIXED: Use Bearer token authentication in headers (not query params)
     * @override
     */
    getConnectionOptions() {
        const useLocalProxy = this.options.useLocalProxy ||
            typeof window === 'undefined' ||
            window.location?.hostname === 'localhost';

        if (useLocalProxy) {
            // When using local proxy, the proxy handles authentication to Aliyun
            // Client just needs basic headers
            return {
                headers: {
                    'user-agent': 'hologram-software/1.0.0',
                    'X-Client-Type': 'hologram-realtime'
                }
            };
        } else {
            // Direct connection: use Bearer token authentication in headers (SECURE)
            if (!this.apiKey) {
                throw new Error('API key is required for secure WebSocket connection');
            }
            return {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'X-DashScope-DataInspection': 'enable',
                    'X-DashScope-SSE': 'enable',
                    'user-agent': 'hologram-software/1.0.0',
                    'Content-Type': 'application/json'
                }
            };
        }
    }

    /**
     * Initialize realtime session - sends session.create message
     * FIXED: Wait for connection to be ready before sending session messages
     * @override
     */
    async initializeSession(sessionConfig = {}) {
        try {
            this.logger.info('🎬 Initializing realtime session...');

            // Simple check: Wait for connection manager to be ready
            const connectionManager = await ConnectionManager.getInstance();

            // Wait for connection to be ready with timeout
            const ready = await connectionManager.waitForReady(10000);
            if (!ready) {
                throw new Error('Connection not ready after waiting - cannot initialize session');
            }

            // CRITICAL FIX: Use Python-compatible session format that matches official Aliyun docs
            // This prevents 1011 Internal Server Error by using the exact format from omni_realtime_client.py
            const aliyunSessionConfig = createPythonCompatibleSessionUpdate({
                voice: this.audioConfig.voice || 'Ethan'
            });

            this.logger.info('🎬 Sending Python-compatible session.update...', {
                eventType: aliyunSessionConfig.type,
                voice: aliyunSessionConfig.session.voice,
                modalities: aliyunSessionConfig.session.modalities,
                hasTurnDetection: !!aliyunSessionConfig.session.turn_detection
            });

            // Send session creation message
            connectionManager.send(aliyunSessionConfig);
            this.logger.info('✅ Session creation message sent successfully');

            // Wait for session.created response
            return new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Session creation timeout'));
                }, 15000);

                const onSessionReady = (sessionData) => {
                    clearTimeout(timeout);
                    connectionManager.off('sessionReady', onSessionReady);
                    this.realtimeSessionId = sessionData.sessionId;
                    this._setState(StreamingState.SESSION_READY);
                    this.sessionStabilized = true;
                    resolve(true);
                };

                connectionManager.on('sessionReady', onSessionReady);
            });

        } catch (error) {
            this.logger.error('❌ Session initialization failed:', error);

            // Enhanced error handling for specific Aliyun errors
            if (error.message.includes('Authentication') || error.message.includes('401')) {
                throw new Error('Aliyun API key authentication failed. Please check your API key.');
            } else if (error.message.includes('4008') || error.message.includes('rate limit')) {
                throw new Error('Aliyun API rate limit exceeded. Please try again later.');
            } else if (error.message.includes('1011')) {
                throw new Error('Aliyun server internal error. This may be due to incorrect session configuration.');
            }

            throw error;
        }
    }

    /**
     * HTTP fallback disabled - use WebSocket only for realtime interaction
     * @override 
     * @protected
     */
    async _createHttpFallbackModel() {
        this.logger.info('🚫 HTTP fallback disabled - WebSocket-only mode for realtime interaction');
        return null;
    }

    /**
     * Check if session is ready - simplified using BaseSessionCoordinator
     * @override
     */
    isSessionReady() {
        return this.sessionCoordinator &&
            this.sessionCoordinator.isSessionActive() &&
            this.realtimeSessionId !== null;
    }

    /**
     * Wait for session to be ready - uses BaseSessionCoordinator
     * @param {number} timeout - Timeout in milliseconds
     * @returns {Promise<boolean>} Ready status
     */
    async waitForRealtimeReady(timeout = 120000) {
        if (this.isSessionReady()) {
            return true;
        }

        return new Promise((resolve) => {
            const timeoutId = setTimeout(() => {
                this.logger.warn('⚠️ [RealtimeReady] Timeout waiting for session readiness');
                resolve(false);
            }, timeout);

            const checkReady = () => {
                if (this.isSessionReady()) {
                    clearTimeout(timeoutId);
                    this.logger.info('✅ [RealtimeReady] Session is ready');
                    resolve(true);
                } else {
                    setTimeout(checkReady, 100);
                }
            };

            checkReady();
        });
    }

    /**
     * Process provider-specific messages - handles Aliyun events
     * @override
     */
    processProviderMessage(messageData) {
        try {
            const eventType = messageData.type;

            this.logger.debug('📥 Aliyun message:', {
                type: eventType,
                hasData: !!messageData.data
            });

            switch (eventType) {
                case AliyunEventType.SESSION_CREATED:
                    // Handled by BaseSessionCoordinator
                    break;

                case AliyunEventType.SESSION_UPDATED:
                    // Handled by BaseSessionCoordinator
                    break;

                case AliyunEventType.CONVERSATION_CREATED:
                    // Handled by BaseSessionCoordinator
                    break;

                // FIXED: Handle correct Aliyun realtime event types
                case AliyunEventType.RESPONSE_AUDIO_DELTA:
                case AliyunEventType.RESPONSE_AUDIO_DONE:
                    this._handleAudioResponse(messageData);
                    break;

                case AliyunEventType.RESPONSE_TEXT_DELTA:
                case AliyunEventType.RESPONSE_TEXT_DONE:
                    this._handleTranscriptEvent(messageData);
                    break;

                case AliyunEventType.INPUT_AUDIO_BUFFER_SPEECH_STARTED:
                case AliyunEventType.INPUT_AUDIO_BUFFER_SPEECH_STOPPED:
                    this._handleVADEvent(messageData);
                    break;

                // Legacy event types (keep for compatibility)
                case AliyunLegacyEventType.RESULT_GENERATED:
                    this._handleAudioResponse(messageData);
                    break;

                case AliyunLegacyEventType.SENTENCE_BEGIN:
                case AliyunLegacyEventType.SENTENCE_END:
                    this._handleTranscriptEvent(messageData);
                    break;

                case AliyunLegacyEventType.SPEECH_BEGIN:
                case AliyunLegacyEventType.SPEECH_END:
                    this._handleVADEvent(messageData);
                    break;

                case AliyunEventType.ERROR:
                    this._handleError(messageData);
                    break;

                default:
                    this.logger.debug('🤷 Unknown Aliyun event type:', eventType);
            }

            return { processed: true, eventType };

        } catch (error) {
            this.logger.error('❌ Error processing Aliyun message:', error);
            return { processed: false, error: error.message };
        }
    }

    /**
     * Handle audio response from Aliyun
     * @private
     */
    _handleAudioResponse(messageData) {
        if (messageData.data?.audio) {
            this._emitEvent('audioResponse', {
                audio: messageData.data.audio,
                timestamp: Date.now()
            });
        }
    }

    /**
     * Handle transcript events
     * @private
     */
    _handleTranscriptEvent(messageData) {
        this._emitEvent('transcriptReceived', {
            type: messageData.type,
            text: messageData.data?.text,
            timestamp: Date.now()
        });
    }

    /**
     * Handle Voice Activity Detection events
     * @private
     */
    _handleVADEvent(messageData) {
        const vadEventType = messageData.type === 'speech-begin' ? 'speech_started' : 'speech_stopped';

        // Forward to BaseSessionCoordinator
        this.sessionCoordinator.handleVADEvent(vadEventType, messageData.data);

        // Also emit local event
        const eventName = messageData.type === 'speech-begin' ? 'voiceActivityDetected' : 'voiceActivityStopped';
        this._emitEvent(eventName, {
            type: messageData.type,
            data: messageData.data,
            timestamp: Date.now()
        });
    }

    /**
     * Send audio to provider - uses ConnectionManager singleton
     * @override
     */
    async _sendAudioToProvider(audioData, format) {
        if (!this.isSessionReady()) {
            this.logger.warn('❌ Cannot send audio: Session not ready - checking connection status');

            // Attempt automatic session recovery if connection is unstable
            try {
                const connectionManager = await ConnectionManager.getInstance();
                if (!connectionManager.isReady()) {
                    this.logger.info('🔄 Attempting to reconnect WebSocket for audio streaming...');
                    await this.connect();
                }
            } catch (recoveryError) {
                this.logger.error('❌ Failed to recover WebSocket connection:', recoveryError);
            }

            return false;
        }

        try {
            // ENHANCED: Validate audio format and parameters
            if (!audioData || (typeof audioData !== 'string' && !(audioData instanceof ArrayBuffer))) {
                this.logger.warn('⚠️ Invalid audio data format');
                return false;
            }

            const audioMessage = {
                type: 'input_audio_buffer.append',
                audio: format === 'base64' ? audioData : arrayBufferToBase64(audioData),
                event_id: generateEventId()
            };

            // Send through ConnectionManager singleton with retry logic
            const connectionManager = await ConnectionManager.getInstance();

            // Check connection health before sending
            if (!connectionManager.isReady()) {
                this.logger.warn('⚠️ ConnectionManager not ready for audio transmission');
                return false;
            }

            const success = connectionManager.send(audioMessage);

            if (success) {
                this.logger.debug('📤 Audio sent to Aliyun via input_audio_buffer.append');
            } else {
                this.logger.debug('⚠️ Failed to send audio - connection may be unstable');
            }

            return success;

        } catch (error) {
            this.logger.error('❌ Error sending audio to Aliyun:', error);

            // If error suggests connection issue, log helpful troubleshooting info
            if (error.message.includes('WebSocket') || error.message.includes('connection')) {
                this.logger.error('💡 WebSocket connection error - check network and API key configuration');
            }

            return false;
        }
    }

    /**
     * Commit audio buffer - uses ConnectionManager singleton
     */
    async commitAudio() {
        if (!this.isSessionReady()) {
            this.logger.warn('❌ Cannot commit audio: Session not ready');
            return false;
        }

        try {
            const commitMessage = {
                type: 'input_audio_buffer.commit',
                event_id: generateEventId()
            };

            const connectionManager = await ConnectionManager.getInstance();
            const success = connectionManager.send(commitMessage);

            if (success) {
                this.logger.debug('✅ Audio buffer committed');
            }
            return success;

        } catch (error) {
            this.logger.error('❌ Error committing audio:', error);
            return false;
        }
    }

    /**
     * Main invoke method - properly delegates to base class
     * ENHANCED: Add specific logic for Aliyun model selection and modality control
     * @override
     */
    async invoke(messages, options = {}) {
        // CRITICAL: Call super.invoke() first to apply API limiting from BaseChatModel
        await super.invoke(messages, options);

        // 🔧 CRITICAL: Handle modality override for dual brain coordination
        const modalityOverride = options.modalityOverride ||
            options.context?.modalityOverride ||
            options.metadata?.modalityConfiguration?.requested;

        const skipAudioOutput = options.skipAudioOutput ||
            options.context?.skipAudioOutput ||
            (modalityOverride?.length === 1 && modalityOverride[0] === 'text');

        // 🧠 DUAL BRAIN LOGGING - System 1 (WebSocket) Invocation
        this.logger.debug('🧠 [SYSTEM-1-INVOKE] WebSocket System 1 processing request', {
            messagesCount: Array.isArray(messages) ? messages.length : 1,
            hasOptions: Object.keys(options).length > 0,
            optionKeys: Object.keys(options),
            messageTypes: Array.isArray(messages) ? messages.map(m => typeof m) : [typeof messages],
            isDualBrainContext: options.isDualBrainAnalysis || false,
            isProactiveDecision: options.isProactiveDecision || false,
            contextualInsights: options.contextualInsights ? 'present' : 'none',
            // 🔧 NEW: Modality control logging
            hasModalityOverride: !!modalityOverride,
            modalityOverride: modalityOverride,
            skipAudioOutput: skipAudioOutput,
            purpose: options.context?.purpose || 'general'
        });

        // Log the actual message content for dual brain coordination
        if (Array.isArray(messages) && messages.length > 0) {
            this.logger.debug('🧠 [SYSTEM-1-MESSAGES] System 1 processing messages:', {
                messages: messages.map((msg, idx) => ({
                    index: idx,
                    role: msg.role || 'unknown',
                    content: typeof msg.content === 'string' ? msg.content.substring(0, 200) + '...' : 'non-string',
                    hasToolCalls: !!(msg.tool_calls && msg.tool_calls.length > 0)
                }))
            });
        }

        // 🔧 CRITICAL: If text-only output is requested, use simplified text analysis
        if (skipAudioOutput || (modalityOverride && !modalityOverride.includes('audio'))) {
            this.logger.info('📝 [MODALITY-OVERRIDE] Text-only output requested - using analysis mode');
            return this._invokeTextOnlyAnalysis(messages, { ...options, modalityOverride, skipAudioOutput });
        }

        // Check if we need realtime/WebSocket features
        const needsWebSocket = this._shouldUseWebSocket(messages, options);

        this.logger.debug('🔄 [ALIYUN-INVOKE] WebSocket decision made', {
            needsWebSocket,
            hasAudioFeatures: options.audio || options.enableVAD,
            hasStreamingFeatures: options.streaming || options.stream
        });

        if (needsWebSocket) {
            this.logger.info('🎙️ Using WebSocket mode for realtime interaction');
            return this._invokeWebSocket(messages, options);
        } else {
            this.logger.info('📝 Using HTTP fallback for text-only interaction');
            return this._invokeHttpFallback(messages, options);
        }
    }

    /**
     * 🔧 NEW: Invoke with text-only output for System 1 → System 2 analysis
     * @private
     */
    async _invokeTextOnlyAnalysis(messages, options) {
        this.logger.info('📝 [TEXT-ONLY] Starting analysis mode invocation...');

        try {
            // Extract the main input from messages
            const mainMessage = Array.isArray(messages) ?
                messages.find(m => m.role === 'user')?.content || messages[0]?.content || JSON.stringify(messages) :
                typeof messages === 'string' ? messages : JSON.stringify(messages);

            // Generate text analysis for scene description
            const analysisResponse = this._generateTextAnalysis(mainMessage, options);

            this.logger.debug('✅ [TEXT-ONLY] Analysis complete', {
                responseLength: analysisResponse.length,
                purpose: options.context?.purpose,
                modalityUsed: ['text']
            });

            return {
                data: analysisResponse,
                metadata: {
                    modalityUsed: ['text'],
                    purpose: options.context?.purpose || 'analysis',
                    method: 'text_only_analysis',
                    model: this.config.model,
                    timestamp: Date.now()
                }
            };

        } catch (error) {
            this.logger.error('❌ [TEXT-ONLY] Analysis failed:', error);
            throw error;
        }
    }

    /**
     * 🔧 NEW: Generate text analysis for scene description
     * @private
     */
    _generateTextAnalysis(input, options) {
        // Extract context information
        const hasAudioInput = options.context?.audioInput ||
            input.includes('Audio') ||
            input.includes('stream') ||
            input.includes('microphone');

        const hasVideoInput = options.context?.videoInput ||
            input.includes('Camera') ||
            input.includes('video') ||
            input.includes('camera');

        const purpose = options.context?.purpose || 'general';

        // Generate contextual analysis based on input and available information
        if (purpose === 'scene_analysis') {
            const userAction = hasAudioInput ? 'speaking into microphone' : 'interacting with system';
            const sceneContext = hasVideoInput ? 'active camera feed shows user engagement' : 'text-based interaction mode';
            const intentAnalysis = hasAudioInput || hasVideoInput ? 'conversational engagement requiring response' : 'passive monitoring';

            return `User is ${userAction}. Scene shows ${sceneContext}. Intent appears to be ${intentAnalysis}.`;
        }

        // Default analysis for other purposes
        const inputSummary = input.length > 100 ? input.substring(0, 100) + '...' : input;
        const interactionType = hasAudioInput ? 'audio interaction' : 'text-based communication';

        return `Input analysis: ${inputSummary}. Context indicates ${interactionType} with user engagement level appearing moderate.`;
    }

    /**
     * WebSocket invocation - uses ConnectionManager singleton
     * @private
     */
    async _invokeWebSocket(messages, options = {}) {
        try {
            // Ensure connection is established  
            const connectionManager = await ConnectionManager.getInstance();

            // If not ready, wait for it to become ready or connect
            if (!connectionManager.isReady()) {
                this.logger.info('🔧 WebSocket not ready, waiting or connecting...');
                this.logger.debug('🔧 [ALIYUN-INVOKE] Connection not ready, checking current state...', {
                    currentState: connectionManager.getConnectionState(),
                    hasSocket: !!connectionManager._socket,
                    socketState: connectionManager._socket?.readyState
                });

                // Try to wait for existing connection first
                try {
                    this.logger.debug('🔧 [ALIYUN-INVOKE] Waiting for connection to become ready...');
                    const ready = await connectionManager.waitForReady(5000);
                    if (!ready) {
                        // If no existing connection, establish new one
                        this.logger.info('🔧 Establishing new connection...');
                        this.logger.debug('🔧 [ALIYUN-INVOKE] Wait timeout, establishing new connection...');
                        const connected = await this.connect();
                        this.logger.debug('🔧 [ALIYUN-INVOKE] New connection result:', { connected });
                        if (!connected) {
                            throw new Error('Failed to establish WebSocket connection');
                        }
                    } else {
                        this.logger.debug('✅ [ALIYUN-INVOKE] Connection became ready via wait');
                    }
                } catch (waitError) {
                    // If waiting fails, try connecting
                    this.logger.info('🔧 Wait failed, establishing new connection...');
                    this.logger.debug('🔧 [ALIYUN-INVOKE] Wait error, trying to connect:', { error: waitError.message });
                    const connected = await this.connect();
                    this.logger.debug('🔧 [ALIYUN-INVOKE] Connect after wait error result:', { connected });
                    if (!connected) {
                        throw new Error('Failed to establish WebSocket connection');
                    }
                }
            } else {
                this.logger.debug('✅ [ALIYUN-INVOKE] Connection already ready, proceeding with message send');
            }

            // Process the conversation using the session
            const startTime = Date.now();

            // Send the user message through the session
            const userMessage = Array.isArray(messages) ? messages[messages.length - 1] : messages;
            const messageText = typeof userMessage === 'string' ? userMessage : userMessage.content;

            // Send message through WebSocket
            const messagePayload = {
                type: 'conversation.item.create',
                item: {
                    type: 'message',
                    role: 'user',
                    content: [{
                        type: 'input_text',
                        text: messageText
                    }]
                },
                event_id: generateEventId()
            };

            // ConnectionManager.send() throws on error, no need to check return value
            connectionManager.send(messagePayload);

            // Send response generation request
            const responseRequest = {
                type: 'response.create',
                response: {
                    modalities: this.modalities,
                    voice: this.audioConfig.voice
                },
                event_id: generateEventId()
            };

            connectionManager.send(responseRequest);

            // Wait for response (simplified)
            // Aliyun realtime often requires explicit response.create then audio/text events
            // Use a shorter initial wait and retry once to reduce perceived timeouts
            const firstTimeout = Math.min(8000, options.responseTimeout || 30000);
            let response;
            try {
                response = await this._waitForResponse(firstTimeout);
            } catch (e) {
                this.logger.warn('Initial realtime response timeout, retrying short wait...');
                response = await this._waitForResponse(Math.min(7000, (options.responseTimeout || 30000) - firstTimeout));
            }

            // Record metrics
            const responseTime = Date.now() - startTime;
            this.wsMetrics.messagesSent++;

            return new AIMessage({
                content: response.content || 'Response received via WebSocket',
                additional_kwargs: {
                    response_time: responseTime,
                    session_id: this.realtimeSessionId,
                    websocket: true
                }
            });

        } catch (error) {
            this.logger.error('❌ WebSocket invocation failed:', error);
            this.wsMetrics.connectionsFailures = (this.wsMetrics.connectionsFailures || 0) + 1;
            throw error;
        }
    }

    /**
     * Wait for response from the session
     * @private
     */
    async _waitForResponse(timeout = 30000) {
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error('Response timeout'));
            }, timeout);

            // Listen for response events
            const handleResponse = (data) => {
                clearTimeout(timeoutId);
                this.off('audioResponse', handleResponse);
                resolve(data);
            };

            this.on('audioResponse', handleResponse);
        });
    }

    /**
     * Connect to WebSocket - uses ConnectionManager singleton
     * @override
     */
    async connect() {
        const connectionId = `aliyun_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
        const now = Date.now();

        // Throttle connection attempts to prevent rapid successive attempts
        if (this._connectionInProgress) {
            this.logger.debug('🔌 [ALIYUN] Connection already in progress, skipping...', { connectionId });
            return false;
        }

        const timeSinceLastAttempt = now - this._lastConnectionAttempt;
        if (timeSinceLastAttempt < this._connectionAttemptDelay) {
            const waitTime = this._connectionAttemptDelay - timeSinceLastAttempt;
            this.logger.debug('🔌 [ALIYUN] Throttling connection attempt', {
                connectionId,
                timeSinceLastAttempt,
                waitTime,
                minDelay: this._connectionAttemptDelay
            });
            return false;
        }

        this._lastConnectionAttempt = now;
        this._connectionInProgress = true;

        try {
            this.logger.debug('🔌 [ALIYUN] Starting connection process...', { connectionId });
            this.logger.info('🔌 Connecting via ConnectionManager singleton...');

            // Get or create ConnectionManager singleton
            const connectionManager = await ConnectionManager.getInstance();

            this.logger.debug('🔌 [ALIYUN] ConnectionManager instance obtained', {
                connectionId,
                managerState: connectionManager.getConnectionState(),
                isReady: connectionManager.isReady()
            });

            // Build WebSocket configuration
            const wsConfig = {
                url: this.buildWebSocketUrl(),
                options: this.getConnectionOptions(),
                initializeSession: async () => {
                    this.logger.debug('🎬 [ALIYUN] Session initializer called', { connectionId });
                    // This will trigger session initialization after connection
                    return this.initializeSession();
                }
            };

            this.logger.debug('🔌 [ALIYUN] WebSocket config built', {
                connectionId,
                url: wsConfig.url,
                hasSessionInitializer: !!wsConfig.initializeSession,
                optionsKeys: Object.keys(wsConfig.options || {}),
                useLocalProxy: wsConfig.url.includes('localhost')
            });

            // Connect using ConnectionManager
            this.logger.debug('🔌 [ALIYUN] Calling ConnectionManager.connect()...', { connectionId });
            const connected = await connectionManager.connect(wsConfig);

            this.logger.debug('🔌 [ALIYUN] ConnectionManager.connect() result', {
                connectionId,
                connected,
                managerState: connectionManager.getConnectionState()
            });

            if (connected) {
                this.logger.info('✅ Connected via ConnectionManager');
                this._setState(StreamingState.CONNECTED);
                this.logger.debug('✅ [ALIYUN] Connection successful, state updated', { connectionId });
                return true;
            } else {
                this.logger.debug('❌ [ALIYUN] Connection failed - ConnectionManager returned false', { connectionId });
            }

            return false;

        } catch (error) {
            this.logger.error('❌ Connection failed:', error);
            this.logger.debug('❌ [ALIYUN] Connection error details', {
                connectionId,
                error: error.message,
                stack: error.stack
            });
            this._setState(StreamingState.ERROR);
            return false;
        } finally {
            this._connectionInProgress = false;
            this.logger.debug('🧹 [ALIYUN] Connection attempt completed', { connectionId });
        }
    }

    /**
     * Disconnect - uses ConnectionManager singleton
     * @override
     */
    async disconnect() {
        try {
            this.logger.info('🔌 Disconnecting...');

            // Get ConnectionManager instance and disconnect
            const connectionManager = await ConnectionManager.getInstance();
            await connectionManager.disconnect(1000, 'AliyunWebSocketChatModel disconnect');

            // Reset session state
            this.realtimeSessionId = null;
            this.conversationId = null;
            this.sessionStabilized = false;
            this._setState(StreamingState.DISCONNECTED);

            this.logger.info('✅ Disconnected successfully');

        } catch (error) {
            this.logger.error('❌ Error during disconnect:', error);
        }
    }



    /**
     * Cleanup resources
     * @override
     */
    async cleanup() {
        await this.disconnect();

        // if (this.realtimeAudioManager) {
        //     this.realtimeAudioManager.cleanup();
        // }

        await super.cleanup();
        this.logger.info('🗑️ Cleanup complete');
    }
}

// Legacy alias for backward compatibility
export const AliyunBailianChatModel = AliyunWebSocketChatModel;

export default AliyunWebSocketChatModel;