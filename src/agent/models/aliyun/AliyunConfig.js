/**
 * Centralized configuration for <PERSON><PERSON> services
 * This file contains all configuration parameters for Aliyun services
 * to ensure consistency across the codebase.
 * 
 * IMPORTANT: This is the single source of truth for Aliyun audio configuration.
 * All components (MediaCaptureManager, AliyunBailianChatModel, etc.) should use this config.
 * 
 * Merged with types and interfaces from the modular architecture
 */

import { getApiConfig } from '@/config/env.js';

/**
 * Utility function to generate event IDs
 * Matches Python's implementation: "event_" + str(int(time.time() * 1000))
 * Enhanced with additional randomness for uniqueness
 * @returns {string} Unique event ID in format event_timestamp_random
 */
export const generateEventId = () => `event_${Math.floor(Date.now())}_${Math.random().toString(36).substr(2, 9)}`;

/**
 * WebSocket connection states
 * Single source of truth for connection state constants
 */
export const ConnectionState = {
    DISCONNECTED: 'disconnected',
    CONNECTING: 'connecting',
    CONNECTED: 'connected',
    AUTHENTICATED: 'authenticated',
    READY: 'ready',
    ERROR: 'error',
    CLOSING: 'closing'
};

/**
 * Session states
 * Single source of truth for session state constants
 */
export const SessionState = {
    NONE: 'none',
    CREATING: 'creating',
    CREATED: 'created',
    UPDATING: 'updating',
    READY: 'ready',
    ERROR: 'error'
};

/**
 * Connection readiness configuration
 * Constants for managing connection state and readiness checks
 */
export const CONNECTION_READINESS_CONFIG = {
    // Timeouts for connection establishment - SECURITY FIX: Aliyun-compliant timeouts
    CONNECTION_TIMEOUT: 120000,      // 120 seconds per Aliyun recommendations (was 10s)
    AUTHENTICATION_TIMEOUT: 30000,  // 30 seconds for authentication (was 5s)
    SESSION_CREATE_TIMEOUT: 45000,  // 45 seconds for session creation (was 8s)
    SESSION_UPDATE_TIMEOUT: 15000,  // 15 seconds for session updates (was 3s)
    READINESS_TIMEOUT: 180000,      // 180 seconds total readiness timeout (was 15s)
    
    // Retry configuration
    MAX_RETRY_ATTEMPTS: 3,
    RETRY_BASE_DELAY: 1000,         // 1 second base delay
    RETRY_MAX_DELAY: 10000,         // 10 second max delay
    
    // Health check intervals
    HEARTBEAT_INTERVAL: 30000,      // 30 seconds between heartbeats
    CONNECTION_CHECK_INTERVAL: 5000, // 5 seconds between connection checks
    
    // Session stability
    SESSION_STABILIZATION_DELAY: 500,  // 500ms delay after session creation
    MESSAGE_QUEUE_DELAY: 1000,         // 1000ms delay for queued messages
    
    // Connection state validation
    VALID_READY_STATES: ['CONNECTED', 'AUTHENTICATED', 'READY'],
    VALID_SESSION_STATES: ['CREATED', 'READY'],
    
    // Error recovery
    RECOVERABLE_ERROR_CODES: [1006, 1011],  // Abnormal closure, server error
    NON_RECOVERABLE_ERROR_CODES: [4001, 4008, 4009]  // Auth failed, rate limit, audio format
};

/**
 * Connection readiness validation functions
 */
export const ConnectionReadiness = {
    /**
     * Check if connection is ready for operations
     */
    isConnectionReady: (connectionState, sessionState) => {
        return CONNECTION_READINESS_CONFIG.VALID_READY_STATES.includes(connectionState) && 
               CONNECTION_READINESS_CONFIG.VALID_SESSION_STATES.includes(sessionState);
    },

    /**
     * Check if error is recoverable
     */
    isRecoverableError: (errorCode) => {
        return CONNECTION_READINESS_CONFIG.RECOVERABLE_ERROR_CODES.includes(errorCode);
    },

    /**
     * Calculate retry delay with exponential backoff
     */
    calculateRetryDelay: (attemptNumber) => {
        const delay = CONNECTION_READINESS_CONFIG.RETRY_BASE_DELAY * Math.pow(2, attemptNumber - 1);
        return Math.min(delay, CONNECTION_READINESS_CONFIG.RETRY_MAX_DELAY);
    },

    /**
     * Check if should attempt retry
     */
    shouldRetry: (attemptNumber, errorCode) => {
        if (attemptNumber >= CONNECTION_READINESS_CONFIG.MAX_RETRY_ATTEMPTS) {
            return false;
        }
        if (CONNECTION_READINESS_CONFIG.NON_RECOVERABLE_ERROR_CODES.includes(errorCode)) {
            return false;
        }
        return true;
    }
};

/**
 * VAD (Voice Activity Detection) states
 * Single source of truth for VAD state constants
 */
export const VADState = {
    IDLE: 'idle',
    LISTENING: 'listening',
    SPEECH_DETECTED: 'speech_detected',
    SPEECH_ENDED: 'speech_ended',
    TIMEOUT: 'timeout'
};

/**
 * CRITICAL AUDIO CONSTANTS - SINGLE SOURCE OF TRUTH
 * All Aliyun audio functionality depends on these exact values
 */
export const ALIYUN_AUDIO_CONSTANTS = {
    SAMPLE_RATE: 16000,              // 16kHz sample rate - REQUIRED FOR VAD (corrected from 24kHz mismatch)
    BIT_DEPTH: 16,                   // 16-bit PCM
    CHANNELS: 1,                     // Mono audio
    FORMAT: 'pcm16',                 // Audio format for VAD
    DEFAULT_VOICE: 'Chelsie',        // Default TTS voice
    TRANSCRIPTION_MODEL: 'gummy-realtime-v1',
    WEBSOCKET_ENDPOINT: 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime',
    TEMPERATURE: 0.7,                // Default model temperature
    MAX_TOKENS: 2000                 // Default max tokens
};

/**
 * Aliyun API event types
 * SINGLE SOURCE OF TRUTH for all event type constants
 * All event strings should be referenced from here, not hardcoded elsewhere
 */
export const AliyunEventType = {
    // Session events
    SESSION_CREATED: 'session.created',
    SESSION_UPDATED: 'session.updated',
    SESSION_UPDATE: 'session.update',

    // Conversation events (ensure single source of truth)
    CONVERSATION_CREATED: 'conversation.created',
    CONVERSATION_UPDATED: 'conversation.updated',

    // Audio buffer events
    INPUT_AUDIO_BUFFER_APPEND: 'input_audio_buffer.append',
    INPUT_AUDIO_BUFFER_COMMIT: 'input_audio_buffer.commit',
    INPUT_AUDIO_BUFFER_COMMITTED: 'input_audio_buffer.committed',
    INPUT_AUDIO_BUFFER_CLEAR: 'input_audio_buffer.clear',
    INPUT_AUDIO_BUFFER_CLEARED: 'input_audio_buffer.cleared',

    // VAD events - CRITICAL: Match exact Python event names from omni_realtime_client.py
    INPUT_AUDIO_BUFFER_SPEECH_STARTED: 'input_audio_buffer.speech_started',
    INPUT_AUDIO_BUFFER_SPEECH_STOPPED: 'input_audio_buffer.speech_stopped',

    // Image buffer events
    INPUT_IMAGE_BUFFER_APPEND: 'input_image_buffer.append',

    // Conversation events
    CONVERSATION_ITEM_CREATE: 'conversation.item.create',
    CONVERSATION_ITEM_CREATED: 'conversation.item.created',
    CONVERSATION_ITEM_INPUT_AUDIO_TRANSCRIPTION_COMPLETED: 'conversation.item.input_audio_transcription.completed',
    INPUT_AUDIO_TRANSCRIPTION_TRANSCRIPT: 'input_audio_transcription.transcript',

    // Response events
    RESPONSE_CREATE: 'response.create',
    RESPONSE_CREATED: 'response.created',
    RESPONSE_DONE: 'response.done',
    RESPONSE_AUDIO_DELTA: 'response.audio.delta',
    RESPONSE_AUDIO_DONE: 'response.audio.done',
    RESPONSE_TEXT_DELTA: 'response.text.delta',
    RESPONSE_TEXT_DONE: 'response.text.done',
    RESPONSE_CONTENT_PART_ADDED: 'response.content_part.added',
    RESPONSE_CONTENT_PART_DONE: 'response.content_part.done',
    RESPONSE_OUTPUT_ITEM_ADDED: 'response.output_item.added',
    RESPONSE_OUTPUT_ITEM_DONE: 'response.output_item.done',
    RESPONSE_AUDIO_TRANSCRIPT_DELTA: 'response.audio_transcript.delta',
    RESPONSE_AUDIO_TRANSCRIPT_DONE: 'response.audio_transcript.done',
    RESPONSE_CANCEL: 'response.cancel',

    // Error events
    ERROR: 'error'
};

// Legacy Aliyun-compatible events kept for backward compatibility in handlers/mappings
export const AliyunLegacyEventType = {
    RESULT_GENERATED: 'result-generated',
    SENTENCE_BEGIN: 'sentence-begin',
    SENTENCE_END: 'sentence-end',
    SPEECH_BEGIN: 'speech-begin',
    SPEECH_END: 'speech-end'
};

/**
 * VAD (Voice Activity Detection) configuration for Aliyun Qwen-Omni API
 * 
 * Supports two modes:
 * 1. SERVER_VAD - Uses server-side VAD detection (Parameters from vad_mode.py)
 * 2. MANUAL - Manual commit + create_response workflow for push-to-talk interface
 * 
 * IMPORTANT: VAD acts as DECISION FACTOR, not CONTROLLER
 * - speech_started/stopped events inform the system about user activity
 * - The client decides whether to speak based on VAD + other contextual factors
 * - VAD does not automatically trigger responses - it provides advisory input
 * 
 * @type {Object}
 */
export const ALIYUN_VAD_CONFIG = {
    // Mode selector - 'server_vad' or null (for manual)
    // Set to 'server_vad' to match vad_mode.py
    type: 'server_vad',

    // Server VAD parameters - FIXED: match Python example in omni_realtime_client.py
    threshold: 0.1,             // CRITICAL FIX: Much more sensitive (was 0.8, now matches official example)
    prefix_padding_ms: 500,     // Padding before speech detection (ms)
    silence_duration_ms: 900,   // FIXED: Match official example (was 1500, now 900)

    // Rate limiting configuration
    rateLimitingEnabled: true,

    // Aliyun recommends limiting audio sends to prevent 1011 errors
    minTimeBetweenSends: 90,    // Min time between audio sends in ms

    // Audio format - must be pcm16 for VAD
    audio_format: ALIYUN_AUDIO_CONSTANTS.FORMAT,

    // Transcription parameters
    transcription_model: ALIYUN_AUDIO_CONSTANTS.TRANSCRIPTION_MODEL,

    // Debug options
    debug: {
        logVADEvents: true,
        trackMessageTypes: true,
        traceCallbacks: false
    }
};

// Backward compatibility export
export const ALIYUN_SAMPLE_RATE = ALIYUN_AUDIO_CONSTANTS.SAMPLE_RATE;

/**
 * Audio configuration for Aliyun Qwen-Omni API
 * Based on WORKING Python implementation in omni_realtime_client.py
 * CRITICAL: VAD requires 16kHz PCM16 mono audio for proper speech detection
 */
export const ALIYUN_AUDIO_CONFIG = {
    // PCM requirements using centralized constants
    sampleRate: ALIYUN_AUDIO_CONSTANTS.SAMPLE_RATE,
    bitDepth: ALIYUN_AUDIO_CONSTANTS.BIT_DEPTH,
    numChannels: ALIYUN_AUDIO_CONSTANTS.CHANNELS,
    channels: ALIYUN_AUDIO_CONSTANTS.CHANNELS,      // Alias for numChannels

    // Voice configuration
    defaultVoice: ALIYUN_AUDIO_CONSTANTS.DEFAULT_VOICE,
    supportedVoices: ['Chelsie', 'Serena', 'Ethan', 'Cherry'],

    // Rate limiting to prevent 1011 errors (matches Python implementation)
    minIntervalMs: 200,     // 200ms = 5 chunks/sec (matches asyncio.sleep(0.2))
    maxChunksPerSecond: 5,  // 5 chunks per second (1000ms / 200ms)

    // Audio format settings using centralized constant
    inputFormat: ALIYUN_AUDIO_CONSTANTS.FORMAT,
    outputFormat: ALIYUN_AUDIO_CONSTANTS.FORMAT,
    format: ALIYUN_AUDIO_CONSTANTS.FORMAT,

    // Audio processing settings (matches vad_mode.py exactly)
    chunkSize: 3200,        // Audio chunk size in bytes (CHUNK = 3200 bytes = 1600 samples for 16-bit PCM)
    chunkDurationMs: 200,   // Duration of each chunk (asyncio.sleep(0.2))

    // Audio context settings for Web Audio API
    audioContextConfig: {
        sampleRate: ALIYUN_AUDIO_CONSTANTS.SAMPLE_RATE,
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
    },

    // Debug settings
    enableDebugLogging: true
};

/**
 * Session configuration for Aliyun Qwen-Omni API
 * @param {Object} config - Custom configuration overrides
 * @returns {Object} Valid session configuration
 */
export function buildValidTurnDetection(config = {}) {
    return {
        type: config.type || ALIYUN_VAD_CONFIG.type,
        threshold: config.threshold !== undefined ? config.threshold : ALIYUN_VAD_CONFIG.threshold,
        prefix_padding_ms: config.prefixPaddingMs || ALIYUN_VAD_CONFIG.prefix_padding_ms,
        silence_duration_ms: config.silenceDurationMs || ALIYUN_VAD_CONFIG.silence_duration_ms,
        create_response: config.createResponse !== false,
        interrupt_response: config.interruptResponse !== false
    };
}

/**
 * Build a valid session configuration for Aliyun Qwen-Omni API
 * @param {Object} session - Custom session configuration overrides
 * @returns {Object} Valid session configuration
 */
export function buildValidSessionConfig(session = {}) {
    return {
        modalities: session.modalities || ['text', 'audio'],
        voice: session.voice || ALIYUN_AUDIO_CONFIG.defaultVoice,
        input_audio_format: session.input_audio_format || ALIYUN_AUDIO_CONFIG.inputFormat,
        output_audio_format: session.output_audio_format || ALIYUN_AUDIO_CONFIG.outputFormat,
        input_audio_transcription: session.input_audio_transcription || {
            model: ALIYUN_AUDIO_CONSTANTS.TRANSCRIPTION_MODEL
        },
        turn_detection: session.turn_detection || buildValidTurnDetection(),
        tools: session.tools || [],
        tool_choice: session.tool_choice || 'auto',
        temperature: session.temperature || 0.8,
        max_response_output_tokens: session.max_response_output_tokens || 'inf'
    };
}

/**
 * Creates a properly formatted session.update event following the pattern in omni_realtime_client.py
 * This is the recommended way to create session update events for the Aliyun API
 * @param {Object} sessionConfig - Session configuration overrides
 * @returns {Object} Properly formatted session.update event
 */
export function createSessionUpdateEvent(sessionConfig = {}) {
    return {
        event_id: generateEventId(),
        type: AliyunEventType.SESSION_UPDATE,
        session: buildValidSessionConfig(sessionConfig)
    };
}

/**
 * Creates a properly formatted event object for Aliyun API, following Python's pattern:
 * 1. Create event with type and data
 * 2. Add event_id right before sending
 * 
 * @param {string} type - Event type from AliyunEventType
 * @param {Object} data - Event data
 * @returns {Object} Formatted event object
 */
export function createEvent(type, data = {}) {
    // Step 1: Create event with type and data
    const event = {
        type,
        ...data
    };

    // Step 2: Add event_id right before sending
    event.event_id = generateEventId();

    return event;
}

/**
 * Helper function to get audio configuration for MediaCaptureManager
 * Converts Aliyun config to MediaCaptureManager format
 * @param {Object} overrides - Custom configuration overrides
 * @returns {Object} MediaCaptureManager compatible audio configuration
 */
export function getMediaCaptureAudioConfig(overrides = {}) {
    return {
        audio: {
            sampleRate: overrides.sampleRate || ALIYUN_AUDIO_CONFIG.sampleRate,
            echoCancellation: overrides.echoCancellation !== undefined ?
                overrides.echoCancellation : ALIYUN_AUDIO_CONFIG.audioContextConfig.echoCancellation,
            noiseSuppression: overrides.noiseSuppression !== undefined ?
                overrides.noiseSuppression : ALIYUN_AUDIO_CONFIG.audioContextConfig.noiseSuppression,
            autoGainControl: overrides.autoGainControl !== undefined ?
                overrides.autoGainControl : ALIYUN_AUDIO_CONFIG.audioContextConfig.autoGainControl,
            channels: overrides.channels || ALIYUN_AUDIO_CONFIG.channels,
            bitDepth: overrides.bitDepth || ALIYUN_AUDIO_CONFIG.bitDepth
        },
        vadMode: overrides.vadMode || 'server',
        vadConfig: {
            threshold: overrides.vadThreshold || ALIYUN_VAD_CONFIG.threshold,
            silenceDurationMs: overrides.silenceDurationMs || ALIYUN_VAD_CONFIG.silence_duration_ms,
            createResponse: overrides.createResponse !== undefined ?
                overrides.createResponse : ALIYUN_VAD_CONFIG.create_response,
            interruptResponse: overrides.interruptResponse !== undefined ?
                overrides.interruptResponse : ALIYUN_VAD_CONFIG.interrupt_response
        },
        targetSampleRate: overrides.targetSampleRate || ALIYUN_AUDIO_CONFIG.sampleRate,
        targetBitDepth: overrides.targetBitDepth || ALIYUN_AUDIO_CONFIG.bitDepth,
        targetChannels: overrides.targetChannels || ALIYUN_AUDIO_CONFIG.numChannels,
        minIntervalMs: overrides.minIntervalMs || ALIYUN_AUDIO_CONFIG.minIntervalMs,
        chunkSize: overrides.chunkSize || ALIYUN_AUDIO_CONFIG.chunkSize
    };
}

/**
 * Helper function to get realtime client configuration
 * @param {Object} overrides - Custom configuration overrides
 * @returns {Object} Realtime client compatible configuration
 */
export function getRealtimeClientConfig(overrides = {}) {
    return {
        audioConfig: {
            sampleRate: overrides.sampleRate || ALIYUN_AUDIO_CONFIG.sampleRate,
            channels: overrides.channels || ALIYUN_AUDIO_CONFIG.channels,
            bitDepth: overrides.bitDepth || ALIYUN_AUDIO_CONFIG.bitDepth,
            echoCancellation: overrides.echoCancellation !== undefined ?
                overrides.echoCancellation : ALIYUN_AUDIO_CONFIG.audioContextConfig.echoCancellation,
            noiseSuppression: overrides.noiseSuppression !== undefined ?
                overrides.noiseSuppression : ALIYUN_AUDIO_CONFIG.audioContextConfig.noiseSuppression,
            autoGainControl: overrides.autoGainControl !== undefined ?
                overrides.autoGainControl : ALIYUN_AUDIO_CONFIG.audioContextConfig.autoGainControl
        },
        vadConfig: {
            threshold: overrides.vadThreshold || ALIYUN_VAD_CONFIG.threshold,
            prefixPaddingMs: overrides.prefixPaddingMs || ALIYUN_VAD_CONFIG.prefix_padding_ms,
            silenceDurationMs: overrides.silenceDurationMs || ALIYUN_VAD_CONFIG.silence_duration_ms,
            createResponse: overrides.createResponse !== undefined ?
                overrides.createResponse : ALIYUN_VAD_CONFIG.create_response,
            interruptResponse: overrides.interruptResponse !== undefined ?
                overrides.interruptResponse : ALIYUN_VAD_CONFIG.interrupt_response
        },
        model: overrides.model || ALIYUN_MODELS.DEFAULT_REALTIME,
        vadMode: overrides.vadMode || 'server'
    };
}

/**
 * Helper function to validate audio parameters against Aliyun requirements
 * @param {Object} audioConfig - Audio configuration to validate
 * @returns {Object} Validation result with isValid flag and errors
 */
export function validateAudioConfig(audioConfig) {
    const errors = [];
    const warnings = [];

    // Check sample rate
    if (audioConfig.sampleRate !== ALIYUN_AUDIO_CONFIG.sampleRate) {
        errors.push(`Sample rate must be ${ALIYUN_AUDIO_CONFIG.sampleRate}Hz, got ${audioConfig.sampleRate}Hz`);
    }

    // Check bit depth
    if (audioConfig.bitDepth !== ALIYUN_AUDIO_CONFIG.bitDepth) {
        errors.push(`Bit depth must be ${ALIYUN_AUDIO_CONFIG.bitDepth}-bit, got ${audioConfig.bitDepth}-bit`);
    }

    // Check channels
    if (audioConfig.channels !== ALIYUN_AUDIO_CONFIG.channels &&
        audioConfig.numChannels !== ALIYUN_AUDIO_CONFIG.numChannels) {
        errors.push(`Audio must be mono (1 channel), got ${audioConfig.channels || audioConfig.numChannels} channels`);
    }

    // Check VAD threshold range
    if (audioConfig.vadThreshold !== undefined &&
        (audioConfig.vadThreshold < -1.0 || audioConfig.vadThreshold > 1.0)) {
        warnings.push(`VAD threshold should be in range [-1.0, 1.0], got ${audioConfig.vadThreshold}`);
    }

    return {
        isValid: errors.length === 0,
        errors,
        warnings
    };
}


/**
 * Session configuration modes
 */
export const SessionMode = {
    SERVER_VAD: 'server_vad',      // Server detects speech automatically
    PUSH_TO_TALK: 'push_to_talk'   // Manual commit required
};

/**
 * Supported voices
 */
export const SupportedVoices = {
    CHELSIE: 'Chelsie',
    SERENA: 'Serena',
    ETHAN: 'Ethan',
    CHERRY: 'Cherry'
};

/**
 * System prompts and messages
 */
export const ALIYUN_SYSTEM_PROMPTS = {
    DEFAULT: "You are a helpful assistant.",
    VOICE_ASSISTANT: "You are a voice-enabled AI assistant that provides helpful responses through both text and speech.",
    REALTIME_ASSISTANT: "You are a real-time AI assistant that responds naturally to voice input and provides contextual responses."
};

/**
 * Model names and configurations - SINGLE SOURCE OF TRUTH
 */
export const ALIYUN_MODELS = {
    // WebSocket models (real-time voice)
    TRANSCRIPTION: ALIYUN_AUDIO_CONSTANTS.TRANSCRIPTION_MODEL,
    DEFAULT_REALTIME: 'qwen-omni-turbo-realtime-latest',  // Use latest to avoid version issues
    QWEN_OMNI_TURBO: 'qwen-omni-turbo',

    // HTTP models (function calling support) - qwen-turbo as default
    HTTP_PRIMARY: 'qwen-turbo',     // Default for cost-optimized performance
    HTTP_FALLBACK: 'qwen-plus',     // Balanced alternative
    HTTP_PREMIUM: 'qwen-max'        // High-complexity tasks
};

/**
 * Gender-based voice mapping
 */
export const VoiceGenderMap = {
    female: SupportedVoices.SERENA,
    male: SupportedVoices.ETHAN,
    f: SupportedVoices.SERENA,
    m: SupportedVoices.ETHAN
};

/**
 * WebSocket close codes and their meanings
 */
export const WebSocketCloseCodes = {
    1000: 'Normal closure',
    1006: 'Abnormal closure (connection lost)',
    1011: 'Server internal error',
    4001: 'Authentication failed',
    4008: 'Rate limit exceeded',
    4009: 'Audio format error'
};

/**
 * Configuration interface for AliyunBailianChatModel
 */
// Continuous analysis configuration
export const CONTINUOUS_ANALYSIS_CONFIG = {
    frequency: 2000,                    // 2 seconds interval for System 1 continuous monitoring
    contextualAnalysisFrequency: 2000,  // Same interval for contextual analysis
    audioAnalysisInterval: 1000,        // 1 second for audio context updates
    visualAnalysisInterval: 3000,       // 3 seconds for visual context updates
    proactiveDecisionInterval: 5000     // 5 seconds for proactive decision evaluation
};

// Cost tracking configuration
export const COST_TRACKING_CONFIG = {
    alertThresholds: {
        CNY: 10.0,           // Default CNY threshold
        USD: 1.5,            // Default USD threshold
        EUR: 1.3,            // Default EUR threshold
        JPY: 200             // Default JPY threshold
    },
    reportingInterval: 60000,            // 1 minute default reporting
    enableTracking: true,
    enableAlerts: true,
    currencies: ['CNY', 'USD', 'EUR', 'JPY'],
    defaultCurrency: 'CNY'
};

export const createDefaultConfig = () => ({
    // API Configuration using centralized constants
    apiKey: '',
    model: ALIYUN_MODELS.DEFAULT_REALTIME,
    realtimeEndpoint: ALIYUN_AUDIO_CONSTANTS.WEBSOCKET_ENDPOINT,

    // Model parameters using centralized constants
    temperature: ALIYUN_AUDIO_CONSTANTS.TEMPERATURE,
    maxTokens: ALIYUN_AUDIO_CONSTANTS.MAX_TOKENS,

    // Connection options
    useProxy: typeof window !== 'undefined', // Default to proxy in browser
    proxyEndpoint: null, // Auto-determined

    // Session options
    streaming: true,
    enableTranscription: false,
    requireServerVAD: true,
    vadTimeoutMs: 3000,

    // Audio configuration using centralized constants
    audioConfig: {
        sampleRate: ALIYUN_AUDIO_CONSTANTS.SAMPLE_RATE,
        channels: ALIYUN_AUDIO_CONSTANTS.CHANNELS,
        bitDepth: ALIYUN_AUDIO_CONSTANTS.BIT_DEPTH,
        format: ALIYUN_AUDIO_CONSTANTS.FORMAT
    },

    // Rate limiting
    maxAudioPerSecond: 5, // Updated to match ALIYUN_AUDIO_CONFIG
    minAudioIntervalMs: 200, // Updated to match ALIYUN_AUDIO_CONFIG
    maxAudioTokensPerSecond: 10,
    maxImagesPerSecond: 2,
    minImageIntervalMs: 500,

    // Callbacks
    onConnectionStateChange: null,
    onSessionStateChange: null,
    onVADStateChange: null,
    onError: null,
    onAudioReceived: null,
    onTranscriptReceived: null,
    onVoiceActivityDetected: null,
    onVoiceActivityStopped: null
});

/**
 * Media processing configuration using centralized constants
 */
export const createMediaConfig = () => ({
    // Audio requirements using centralized constants
    audio: {
        sampleRate: ALIYUN_AUDIO_CONSTANTS.SAMPLE_RATE,
        channels: ALIYUN_AUDIO_CONSTANTS.CHANNELS,
        bitDepth: ALIYUN_AUDIO_CONSTANTS.BIT_DEPTH,
        encoding: 'base64'
    },

    // Image requirements (Aliyun specific)  
    image: {
        formats: ['jpg', 'jpeg'], // JPG/JPEG only
        maxSizeKB: 500,          // 500KB max
        encoding: 'base64',
        rateLimit: 2             // 2fps
    },

    // Video processing (converted to image frames)
    video: {
        frameRate: 2,            // 2fps as per Aliyun docs
        maxFrames: 30,
        quality: 0.8
    }
});

/**
 * Rate limiting configuration
 */
export const createRateLimitConfig = () => ({
    audio: {
        maxChunksPerSecond: ALIYUN_AUDIO_CONFIG.maxChunksPerSecond,
        minIntervalMs: ALIYUN_AUDIO_CONFIG.minIntervalMs,
        maxTokensPerSecond: 10,
        windowSizeMs: 1000,
        maxQueueSize: 50
    },

    image: {
        maxImagesPerSecond: 2,
        minIntervalMs: 500,
        windowSizeMs: 1000,
        maxQueueSize: 10
    }
});

/**
 * WebSocket proxy configuration based on Python official example
 * Matches omni_realtime_client.py connection settings
 * Integrates with centralized API rate limiting from env.ts
 */
export const ALIYUN_WEBSOCKET_CONFIG = {
    // Connection timing - SECURITY FIX: Use Aliyun-compliant timeouts (120+ seconds)
    pingInterval: 30000,    // 30 seconds for more stable connections (was 20s)
    pingTimeout: 90000,     // SECURITY FIX: Increased to 90s for complex AI processing (was 45s)
    handshakeTimeout: 180000, // SECURITY FIX: Increased to 180s for reliable connections (was 120s)

    // Session stabilization delays to prevent 1011 errors
    sessionUpdateDelay: {
        client: 500,        // 500ms delay for client messages
        queued: 1000        // 1000ms delay for queued messages
    },

    // Default model configuration using centralized constants
    defaultModel: ALIYUN_MODELS.DEFAULT_REALTIME,
    endpoint: ALIYUN_AUDIO_CONSTANTS.WEBSOCKET_ENDPOINT,

    // Proxy logging configuration  
    logPrefix: 'AliyunProxy',

    // API key validation
    minApiKeyLength: 10,

    // Rate limiting - integrated with centralized API config
    rateLimiting: getApiConfig(),

    // Session configuration for queued messages - EXACT Python format
    defaultSessionConfig: {
        modalities: ["text", "audio"],   // Use exact array format from Python
        voice: ALIYUN_AUDIO_CONSTANTS.DEFAULT_VOICE,
        input_audio_format: ALIYUN_AUDIO_CONSTANTS.FORMAT,
        output_audio_format: ALIYUN_AUDIO_CONSTANTS.FORMAT,
        input_audio_transcription: {
            model: ALIYUN_AUDIO_CONSTANTS.TRANSCRIPTION_MODEL
        },
        turn_detection: {
            type: "server_vad",
            threshold: 0.1,               // EXACT Python example value
            prefix_padding_ms: 500,       // EXACT Python example value
            silence_duration_ms: 900      // EXACT Python example value
        }
    }
};

/**
 * Error recovery configuration
 */
export const createErrorRecoveryConfig = () => ({
    maxRetries: 3,
    retryDelayMs: 2000,
    exponentialBackoff: true,
    maxRetryDelayMs: 10000,

    // Error code specific recovery
    errorRecoveryStrategies: {
        1011: 'session_config_recovery', // Server internal error
        1006: 'connection_retry',        // Abnormal closure
        4001: 'auth_refresh',           // Authentication failed
        4008: 'rate_limit_backoff',     // Rate limit exceeded
        4009: 'audio_format_fix'        // Audio format error
    }
});

/**
 * Utility function to create error objects
 */
export const createError = (type, message, code = null, details = {}) => ({
    type,
    message,
    code,
    details,
    timestamp: Date.now()
});

/**
 * Utility function to validate configuration (for realtime/WebSocket models)
 */
export const validateConfig = (config) => {
    const errors = [];

    if (!config.apiKey) {
        errors.push('API key is required');
    }

    if (!config.model) {
        errors.push('Model name is required');
    }

    if (!config.realtimeEndpoint) {
        errors.push('Realtime endpoint is required');
    }

    if (config.temperature < 0 || config.temperature > 2) {
        errors.push('Temperature must be between 0 and 2');
    }

    if (config.maxTokens < 1) {
        errors.push('Max tokens must be positive');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Utility function to validate HTTP-specific configuration
 */
export const validateHttpConfig = (config) => {
    const errors = [];

    if (!config.apiKey) {
        errors.push('API key is required');
    }

    if (!config.model) {
        errors.push('Model name is required');
    }

    if (config.temperature !== undefined && (config.temperature < 0 || config.temperature > 2)) {
        errors.push('Temperature must be between 0 and 2');
    }

    if (config.maxTokens !== undefined && config.maxTokens < 1) {
        errors.push('Max tokens must be positive');
    }

    // HTTP-specific validations
    if (config.http) {
        if (!config.http.endpoint) {
            errors.push('HTTP endpoint is required');
        }

        if (config.http.timeout !== undefined && config.http.timeout < 100) {
            errors.push('HTTP timeout must be at least 100ms');
        }
    }

    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Audio transmission helper with Python-compatible rate limiting
 * @param {Function} sendFunction - Function to send audio data
 * @param {Object} audioData - Audio data to send
 * @param {Object} state - Rate limiting state object
 * @returns {boolean} Success status
 */
export function sendAudioWithRateLimit(sendFunction, audioData, state) {
    const now = Date.now();
    const minInterval = ALIYUN_AUDIO_CONFIG.minIntervalMs;

    if (state.lastSendTime && (now - state.lastSendTime) < minInterval) {
        return false; // Rate limited
    }

    const success = sendFunction(audioData);
    if (success) {
        state.lastSendTime = now;
    }

    return success;
}

/**
 * Clean connection lifecycle management
 * @param {Object} connection - WebSocket connection
 * @param {Object} state - Connection state object
 */
export function cleanupRealtimeConnection(connection, state) {
    // Send audio buffer clear if connection is active
    if (connection && connection.readyState === 1) {
        try {
            const clearEvent = createEvent(AliyunEventType.INPUT_AUDIO_BUFFER_CLEAR);
            connection.send(JSON.stringify(clearEvent));
        } catch (error) {
            // Silent cleanup - don't log errors during shutdown
        }
    }

    // Close connection
    if (connection) {
        connection.close(1000, 'User stopped listening');
    }

    // Reset state
    Object.assign(state, {
        sessionStabilized: false,
        sessionId: null,
        sessionConfig: null,
        hasReceivedVadEvents: false,
        lastAudioSendTime: 0
    });
}

/**
 * Create a session.update event that exactly matches Python omni_realtime_client.py
 * This is critical for VAD functionality to work correctly and prevent 1011 errors
 * 
 * @param {Object} overrides - Optional overrides for session config
 * @returns {Object} Session update event
 */
export function createPythonCompatibleSessionUpdate(overrides = {}) {
    // Use default voice from centralized constants
    const voice = overrides.voice || ALIYUN_AUDIO_CONSTANTS.DEFAULT_VOICE;

    // Create event structure that EXACTLY matches the official Python example
    const event = {
        type: "session.update",  // Use literal string, not enum to ensure exact match
        event_id: `event_${Date.now()}`,
        session: {
            modalities: ["text", "audio"],
            voice: voice,
            input_audio_format: ALIYUN_AUDIO_CONSTANTS.FORMAT,
            output_audio_format: ALIYUN_AUDIO_CONSTANTS.FORMAT,
            input_audio_transcription: {
                model: ALIYUN_AUDIO_CONSTANTS.TRANSCRIPTION_MODEL
            },
            // ALWAYS include turn_detection for server_vad mode with exact Python values
            turn_detection: {
                type: "server_vad",
                threshold: 0.1,           // EXACT match with Python example
                prefix_padding_ms: 500,   // EXACT match with Python example  
                silence_duration_ms: 900  // EXACT match with Python example
            }
        }
    };

    // Override turn_detection only if explicitly disabled
    if (overrides.disableVAD === true || ALIYUN_VAD_CONFIG.type !== 'server_vad') {
        event.session.turn_detection = null;
    }

    // Enhanced debugging with exact format validation
    console.debug('Created session.update event (EXACT Python format):', JSON.stringify(event, null, 2));

    // Validate against Python example structure
    const isValid = event.type === "session.update" &&
        event.session.modalities.includes("text") &&
        event.session.modalities.includes("audio") &&
        event.session.input_audio_format === ALIYUN_AUDIO_CONSTANTS.FORMAT &&
        event.session.output_audio_format === ALIYUN_AUDIO_CONSTANTS.FORMAT;

    if (!isValid) {
        console.error('❌ Session update format validation failed!');
    } else {
        console.debug('✅ Session update format validated against Python example');
    }

    return event;
}

/**
 * Get WebSocket proxy configuration from centralized config
 * @param {Object} overrides - Custom configuration overrides
 * @returns {Object} WebSocket proxy configuration
 */
export function getWebSocketProxyConfig(overrides = {}) {
    return {
        ...ALIYUN_WEBSOCKET_CONFIG,
        ...overrides
    };
}

/**
 * HTTP endpoint constants - SINGLE SOURCE OF TRUTH
 */
// Import port manager for dynamic server endpoint
import { getDownloadServerUrl } from '../../../utils/portManager.js';

export const ALIYUN_HTTP_ENDPOINTS = {
    get PROXY() { 
        // Dynamic proxy endpoint that respects actual server port
        return `${getDownloadServerUrl()}/api/llm`; 
    },
    DIRECT: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
    COMPATIBLE: 'https://dashscope.aliyuncs.com/compatible-mode/v1' // OpenAI SDK will append /chat/completions
};

/**
 * Get environment-aware HTTP endpoint
 * Browser context uses server proxy, server context uses direct API
 */
export const getHttpEndpoint = () => {
    if (typeof window !== 'undefined') {
        // Browser context - route through server proxy to avoid CORS
        return ALIYUN_HTTP_ENDPOINTS.PROXY;
    }
    // Server context - direct API calls
    return ALIYUN_HTTP_ENDPOINTS.DIRECT;
};

/**
 * Get server-specific HTTP endpoint (always direct API)
 * Used when we explicitly want direct API calls from server
 */
export const getServerHttpEndpoint = () => {
    return ALIYUN_HTTP_ENDPOINTS.DIRECT;
};

/**
 * HTTP API configuration for Aliyun DashScope
 * Supports qwen-plus, qwen-turbo, qwen-max models with function calling
 * Integrates with centralized API rate limiting from env.ts
 */
export const ALIYUN_HTTP_CONFIG = {
    // API endpoints using centralized constants
    endpoint: getHttpEndpoint(),
    compatibleEndpoint: ALIYUN_HTTP_ENDPOINTS.COMPATIBLE,

    // Model configuration
    models: {
        primary: ALIYUN_MODELS.HTTP_PRIMARY,        // qwen-turbo - default for cost-optimized performance
        fallback: ALIYUN_MODELS.HTTP_FALLBACK,      // qwen-plus - balanced alternative
        premium: ALIYUN_MODELS.HTTP_PREMIUM         // qwen-max - high complexity
    },

    // Performance settings for autonomous tools - Aliyun compliant timeouts
    timeout: 120000,                 // Aliyun minimum requirement: 120 seconds (was 8000ms)
    maxRetries: 2,                   // Quick retry for reliability
    retryDelay: 1000,                // Aliyun-compliant retry delay (was 100ms)
    dualBrainTimeout: 180000,        // Aliyun-compliant timeout for complex reasoning (was 10000ms)

    // Rate limiting - integrated with centralized API config
    rateLimiting: {
        requestsPerSecond: 10,
        burstLimit: 20,
        windowSize: 1000,            // 1 second window
        // Apply environment-specific limits
        get environmentSettings() {
            const apiConfig = getApiConfig();
            return {
                enabled: apiConfig.rateLimitEnabled,
                testMode: apiConfig.testMode,
                maxAttempts: apiConfig.maxAttempts,
                ...(apiConfig.testMode && {
                    requestsPerSecond: 2,    // Reduced for test mode
                    burstLimit: 4,           // Reduced for test mode
                    maxConcurrentRequests: apiConfig.maxConcurrentRequests
                })
            };
        }
    },

    // Default parameters using centralized constants
    defaultParameters: {
        temperature: ALIYUN_AUDIO_CONSTANTS.TEMPERATURE,
        maxTokens: ALIYUN_AUDIO_CONSTANTS.MAX_TOKENS,
        topP: 0.8,
        resultFormat: 'message'
    },

    // Tool calling configuration
    toolConfig: {
        toolChoice: 'auto',
        maxToolCalls: 5,
        toolTimeout: 300            // Individual tool timeout
    },

    // Connection pool configuration (Java SDK compatibility)
    connectionPool: {
        maxConnections: 100,
        connectionTimeout: 120000,    // Connection establishment timeout
        socketTimeout: 180000,        // Socket read timeout
        connectionRequestTimeout: 10000, // Request timeout from pool
        keepAliveTimeout: 300000,     // 5 minutes keep-alive
        maxIdleTime: 600000          // 10 minutes max idle
    }
};

/**
 * Create hybrid configuration supporting both HTTP and WebSocket models
 * @param {Object} options - Configuration overrides
 * @returns {Object} Hybrid configuration
 */
export function createHybridConfig(options = {}) {
    return {
        // API credentials - rely on options parameter to provide API key
        apiKey: options.apiKey || '',

        // Required fields for validation
        model: options.model || ALIYUN_MODELS.DEFAULT_REALTIME, // Default to qwen-omni-turbo-realtime for realtime
        realtimeEndpoint: options.realtimeEndpoint || ALIYUN_WEBSOCKET_CONFIG.endpoint,

        // HTTP configuration
        http: {
            ...ALIYUN_HTTP_CONFIG,
            ...options.http
        },

        // WebSocket configuration  
        websocket: {
            ...ALIYUN_WEBSOCKET_CONFIG,
            ...options.websocket
        },

        // Model selection strategy
        modelSelection: {
            autonomousTools: 'http',
            voiceInteraction: 'websocket',
            textAnalysis: 'http',
            defaultFallback: 'http',
            ...options.modelSelection
        },

        // Performance requirements
        performance: {
            maxResponseTime: 600,        // Sub-600ms requirement
            httpTimeout: 500,
            websocketTimeout: 1000,
            ...options.performance
        },

        // Audio configuration
        audioConfig: {
            ...ALIYUN_AUDIO_CONFIG,
            ...options.audioConfig
        }
    };
}

/**
 * Enhanced configuration validation for hybrid setup
 * @param {Object} config - Configuration to validate
 * @returns {Object} Validation result
 */
export function validateHybridConfig(config) {
    const errors = [];
    const warnings = [];

    // API key validation
    if (!config.apiKey) {
        errors.push('API key is required');
    } else if (config.apiKey.length < 10) {
        warnings.push('API key appears to be invalid or too short');
    }

    // HTTP configuration validation
    if (config.http) {
        if (config.http.timeout > config.performance?.maxResponseTime) {
            warnings.push(`HTTP timeout (${config.http.timeout}ms) exceeds max response time (${config.performance.maxResponseTime}ms)`);
        }

        if (!config.http.models?.primary) {
            errors.push('HTTP primary model is required');
        }
    }

    // WebSocket configuration validation  
    if (config.websocket && !config.websocket.endpoint) {
        errors.push('WebSocket endpoint is required');
    }

    // Performance validation
    if (config.performance?.maxResponseTime < 200) {
        warnings.push('Max response time below 200ms may be unrealistic');
    }

    return {
        isValid: errors.length === 0,
        errors,
        warnings
    };
}

export default {
    ALIYUN_AUDIO_CONFIG,
    ALIYUN_AUDIO_CONSTANTS,
    ALIYUN_VAD_CONFIG,
    ALIYUN_WEBSOCKET_CONFIG,
    ALIYUN_HTTP_CONFIG,
    ALIYUN_HTTP_ENDPOINTS,
    ALIYUN_SYSTEM_PROMPTS,
    ALIYUN_MODELS,
    ConnectionState,
    SessionState,
    VADState,
    CONNECTION_READINESS_CONFIG,
    ConnectionReadiness,
    AliyunEventType,
    generateEventId,
    buildValidTurnDetection,
    buildValidSessionConfig,
    createSessionUpdateEvent,
    createEvent,
    getMediaCaptureAudioConfig,
    getRealtimeClientConfig,
    validateAudioConfig,
    SessionMode,
    SupportedVoices,
    VoiceGenderMap,
    WebSocketCloseCodes,
    createDefaultConfig,
    createMediaConfig,
    createRateLimitConfig,
    createErrorRecoveryConfig,
    createError,
    validateConfig,
    validateHttpConfig,
    sendAudioWithRateLimit,
    cleanupRealtimeConnection,
    createPythonCompatibleSessionUpdate,
    getWebSocketProxyConfig,
    getHttpEndpoint,
    getServerHttpEndpoint,
    createHybridConfig,
    validateHybridConfig
};