# Aliyun Qwen-Omni Realtime Integration - Direct Connection Architecture

A production-ready realtime integration for Aliyun Qwen-Omni API with direct connection architecture, designed for Node.js server environments with seamless integration into the existing TalkingAvatar → AliyunBailianChatModel → LangGraph workflow.

## 🎯 **Architecture Overview**

**Purpose**: This is a server-side integration that connects directly to <PERSON>yun's Qwen-Omni realtime API.

**Functionality**: The Node.js application acts as the direct client to <PERSON><PERSON>, handling session management, audio processing, VAD (Voice Activity Detection), and real-time conversation flows.

## 🏗️ **Direct Connection Architecture**

The implementation follows a **Direct Connection** pattern for simplified architecture and reduced latency:

```
Frontend (Browser)                    Node.js Server                Aliyun API
┌─────────────────────────────┐      ┌──────────────────────┐      ┌─────────────┐
│ TalkingAvatar               │      │ AliyunBailianChatModel│      │ Qwen-Omni   │
│   ↓                         │      │   ↓                  │      │ Service     │
│ MediaCaptureManager         │──api─│ ConnectionManager    │──ws──│             │
│ (audio capture)             │      │ (direct connection)  │      │ - Server    │
│                             │      │   ↓                  │      │   VAD       │
│                             │      │ MediaCaptureManager  │      │ - Audio     │
│                             │      │ (audio processing)   │      │   synthesis │
└─────────────────────────────┘      └──────────────────────┘      └─────────────┘
```

### Integration Flow

1. **TalkingAvatar** receives user audio input via MediaCaptureManager
2. **AliyunBailianChatModel** manages the model lifecycle and coordinates realtime operations
3. **ConnectionManager** handles direct WebSocket connection to Aliyun API
4. **MediaCaptureManager** processes audio data and manages VAD events
5. All events flow natively through ConnectionManager without proxy layers

## 📂 Simplified Architecture

The Aliyun integration provides a streamlined, direct architecture for realtime audio/video AI interactions:

```
src/agent/models/aliyun/
├── README.md                           # This implementation guide
├── index.js                           # Unified exports  
├── config/
│   ├── AliyunConfig.js                # ⭐ CENTRALIZED Audio/VAD configuration
│   └── index.js                       # Config exports
├── connection/
│   └── ConnectionManager.js           # Direct WebSocket connection to Aliyun API
├── media/
│   └── (removed - use MediaCaptureManager) # Audio/image processing now in MediaCaptureManager.ts
└── types/
    └── index.js                       # Shared types, events, and constants
```

## 🚀 **Quick Start - Direct Integration**

### 1. Server Setup (Direct Connection)

The AliyunBailianChatModel connects directly to Aliyun API:

```javascript
// src/agent/models/AliyunBailianChatModel.js
// Integrated realtime functionality - no separate ConnectionManager needed

export class AliyunBailianChatModel {
    constructor(options) {
        // Direct integrated realtime connection to Aliyun API
        this.apiKey = options.apiKey;
        this.model = options.model;
        this.realtimeSocket = null;
        // Realtime functionality integrated directly
    }
}
```

### 2. Frontend Integration (Browser to Server API)

The frontend communicates with the Node.js server via HTTP/WebSocket API:

```javascript
// app/viewer/talkingavatar.js (CURRENT USAGE)
export class TalkingAvatar {
    async startListening() {
        // Your existing code uses server API endpoints
        if (this.agentService?.model?.model?.includes('realtime')) {
            // ✅ This communicates with AliyunBailianChatModel via API
            await this._initializeRealtimeMode();
            await this._startRealtimeListening();
        }
    }
    
    _onAudioData(audioData) {
        // ✅ This sends audio to server via API endpoint
        this._sendRealtimeAudio(audioData);
    }
}
```

### 3. Model Integration (Direct Connection)

```javascript
// src/agent/models/AliyunBailianChatModel.js (UPDATED)
export class AliyunBailianChatModel extends BaseChatModel {
    async initializeRealtimeMode(callbacks = {}) {
        // ✅ NOW USES: ConnectionManager for direct connection
        this.connectionManager = new ConnectionManager({
            apiKey: this.apiKey,
            model: this.model,
            realtimeEndpoint: this.config.realtimeEndpoint
        });
        
        // Setup event handlers for realtime events
        this.connectionManager.on('message', (data) => {
            this.handleRealtimeMessage(data, callbacks);
        });
        
        return await this.connectionManager.connect();
    }
    
    async sendRealtimeAudio(audioData) {
        // ✅ NOW DELEGATES TO: ConnectionManager directly
        if (this.connectionManager && this.connectionManager.isConnected()) {
            return await this.connectionManager.sendAudio(audioData);
        }
        return false;
    }
}
```

## 🎯 **Key Benefits Achieved**

1. **✅ Direct Connection**: Simplified architecture with direct Aliyun API connection
2. **✅ Reduced Latency**: No proxy overhead, direct WebSocket communication
3. **✅ Simplified Architecture**: Fewer moving parts, easier maintenance
4. **✅ Native Event Handling**: All events flow through ConnectionManager
5. **✅ Conservative Rate Limiting**: 5fps (200ms) following best practices

### ✅ **Advanced VAD Implementation**
- **Server-Side VAD**: Leverages Aliyun's built-in voice activity detection
- **Direct Event Handling**: VAD events processed natively by ConnectionManager
- **Interrupt Support**: Seamless conversation interruption through direct API
- **Session Management**: Direct session lifecycle management

### ✅ **Production-Ready Architecture**
- **Modern Audio Processing**: AudioWorklet with ScriptProcessor fallback
- **Robust Error Handling**: Comprehensive error recovery and diagnostics
- **Direct Connection**: Single WebSocket connection to Aliyun API
- **Integration Ready**: Works with existing LangGraph agent workflows

## ⚙️ Centralized Configuration

The Aliyun integration uses a **centralized configuration system** to ensure consistency across all components. All audio and VAD parameters are managed through `config/AliyunConfig.js`.

### 📋 Configuration Overview

```javascript
import { 
  ALIYUN_AUDIO_CONFIG, 
  ALIYUN_VAD_CONFIG,
  getMediaCaptureAudioConfig,
  getRealtimeClientConfig,
  validateAudioConfig
} from '@/agent/models/aliyun/AliyunConfig.js';
```

**Key Benefits:**
- ✅ **Single Source of Truth**: All audio settings in one place
- ✅ **API Compliance**: Enforces 16kHz PCM16 mono requirement
- ✅ **Type Safety**: Validation functions prevent configuration errors
- ✅ **Helper Functions**: Easy integration with MediaCaptureManager and ConnectionManager

### 🎵 Audio Configuration

```javascript
// Core audio settings (based on official API spec)
ALIYUN_AUDIO_CONFIG = {
  sampleRate: 16000,     // Required by Aliyun API
  bitDepth: 16,          // PCM16 format
  channels: 1,           // Mono audio only
  minIntervalMs: 200,    // Conservative rate limiting (5/sec)
  // ... additional settings
}
```

### 🎙️ VAD Configuration

```javascript
// VAD settings following Python reference implementation
ALIYUN_VAD_CONFIG = {
  threshold: 0.1,              // Speech detection threshold
  silenceDurationMs: 900,      // Silence duration for VAD
  prefixPaddingMs: 500,        // Audio prefix padding
  interruptResponse: true,     // Enable interrupt handling
  // ... additional settings
}
```

## 🔧 **ConnectionManager Usage**

### Basic Connection

```javascript
import { AliyunBailianChatModel } from '@/agent/models/aliyun/AliyunBailianChatModel.js';

const aliyunModel = new AliyunBailianChatModel({
    apiKey: 'your-api-key',
    model: 'qwen-omni-turbo-realtime',
    apiMode: 'websocket'
});

// Initialize realtime mode with callbacks
await aliyunModel.initializeRealtimeMode({
    onSessionReady: (session) => {
        console.log('Session ready:', session);
    },
    onAudioReceived: (audioData) => {
        console.log('Audio received:', audioData);
    },
    onError: (error) => {
        console.error('Connection error:', error);
    }
});

// Connect to Aliyun API (handled automatically)
await connectionManager.connect();
```

## 🎵 **MediaCaptureManager Usage**

### Browser Audio Capture

```javascript
import { MediaCaptureManager } from '@/media/capture/MediaCaptureManager.ts';

const mediaCaptureManager = new MediaCaptureManager({
    audio: {
        sampleRate: 16000,
        channels: 1,
        echoCancellation: true,
        noiseSuppression: true
    }
});

// Setup event listeners
mediaHandler.on('audioProcessed', (result) => {
    console.log('Processed audio:', result.base64Audio);
    // Send to ConnectionManager
});

// Initialize audio capture
await mediaHandler.initializeAudioCapture();

// Start capturing
mediaHandler.startAudioCapture();

// Stop when done
mediaHandler.stopAudioCapture();
mediaHandler.cleanupAudioCapture();
```

### Audio Processing

```javascript
// Process audio data manually
const result = await mediaHandler.processAudio(audioArrayBuffer);
if (result.success) {
    console.log('Base64 audio:', result.base64Audio);
    console.log('Event for Aliyun:', result.event);
}
```

## 🧪 **Testing**

### Unit Tests

```bash
npm test src/agent/models/aliyun/
```

### Integration Tests

```bash
npm run test:integration
```

### Manual Testing

Use the debug utilities to test individual components:

```bash
node debug/verify_complete_fix.js
```

## 📄 **API Reference**

### ConnectionManager

The main class for managing WebSocket connections to Aliyun API.

**Constructor Options:**
- `apiKey`: Aliyun API key
- `model`: Model name (default: 'qwen-omni-turbo-realtime')
- `realtimeEndpoint`: WebSocket endpoint URL

**Methods:**
- `connect()`: Establish connection
- `disconnect()`: Close connection
- `send(data)`: Send event data
- `sendAudio(audioData)`: Send audio buffer
- `isConnected()`: Check connection status

**Events:**
- `'stateChange'`: Connection state changes
- `'message'`: Incoming messages
- `'error'`: Error events
- `'close'`: Connection closed

### AliyunBailianChatModel

Extended chat model with realtime capabilities.

**Realtime Methods:**
- `initializeRealtimeMode(callbacks)`: Setup realtime mode
- `sendRealtimeAudio(audioData)`: Send audio data
- `commitRealtimeAudio()`: Commit audio buffer
- `clearRealtimeAudioBuffer()`: Clear audio queue
- `isRealtimeModeActive()`: Check realtime status

## 🔍 **Debugging**

### Debug Utilities

Several debug utilities are available in the `debug/` folder:

- `verify_complete_fix.js`: Test complete integration
- `test_session_stabilization.js`: Test session management
- `verify-audio-input-fix.js`: Test audio input handling

### Logging

The integration uses structured logging for better debugging:

```javascript
import { createLogger } from '@/utils/logger.js';

const logger = createLogger('AliyunRealtime');
logger.setLogLevel('DEBUG'); // Enable debug logs
```

## ✅ **Architecture Summary**

### **Direct Connection Architecture Achieved**

1. **✅ Simplified Structure**: 6 core files provide complete functionality
   - `index.js` - Clean exports, no proxy/client references
   - `ConnectionManager.js` - Direct WebSocket connection to Aliyun API
   - ❌ `MediaHandler.js` (removed - use MediaCaptureManager.ts)
   - `AliyunConfig.js` - Centralized configuration management
   - `types/index.js` - Shared types and constants

2. **✅ Integrated Audio Processing**: MediaCaptureManager includes:
   - Embedded AudioWorklet processor (no separate files)
   - ScriptProcessor fallback for older browsers
   - Real-time audio capture and processing
   - EventEmitter support for audio events

3. **✅ Removed Components**: 
   - ❌ QwenOmniRealtimeProxy.ts (removed)
   - ❌ QwenOmniRealtimeClient.ts (removed) 
   - ❌ QwenOmniSessionManager.js (removed)
   - ❌ worklets/ directory (integrated into MediaCaptureManager)

4. **✅ Production Ready**: All components work together seamlessly for direct Aliyun API integration

## 📚 **References**

- [Aliyun Qwen-Omni API Documentation](https://help.aliyun.com/zh/dashscope/)
- [WebSocket API Reference](https://help.aliyun.com/zh/dashscope/developer-reference/qwen-omni-realtime-api)
- [Audio Format Requirements](https://help.aliyun.com/zh/dashscope/developer-reference/audio-format)

---

This implementation provides a complete, production-ready solution for integrating Aliyun Qwen-Omni realtime capabilities with direct connection architecture, eliminating the need for proxy layers while maintaining full functionality and performance.
