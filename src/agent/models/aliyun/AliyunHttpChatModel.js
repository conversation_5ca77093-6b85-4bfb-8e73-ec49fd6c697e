/**
 * <PERSON><PERSON> HTTP Chat Model
 * Lang<PERSON>hain integration for <PERSON><PERSON>'s DashScope HTTP API with function calling support
 * Uses OpenAI SDK for compatibility with <PERSON><PERSON>'s OpenAI-compatible endpoints
 * Optimized for autonomous tools and sub-600ms response times
 * 
 * This model handles qwen-plus, qwen-turbo, and qwen-max models via HTTP API
 * Supports full function calling capabilities for autonomous decision-making tools
 */

import OpenAI from 'openai';
import { HttpChatModel } from '../base/HttpChatModel.js';
// Note: Context management handled at agent service level, not at model level
import { AIMessage, HumanMessage } from '@langchain/core/messages';
import { createLogger, LogLevel } from '../../../utils/logger.ts';
import { ProviderErrorFactory, ProviderTimeoutError, ProviderConnectionError, ProviderRateLimitError } from '../base/ProviderErrors.js';
import { getGlobalRateLimiterManager } from '../../services/rate-limiting/UniversalRateLimiter.js';
import { PerformanceMonitor } from '../../services/monitoring/PerformanceMonitor.js';
import { getApiConfig } from '@/config/env.ts';
import { getDownloadServerUrl } from '@/utils/portManager.js';
import { parseRobustJSON } from '../../../utils/jsonParser.js';
import { selectAnimationTool } from '../../tools/avatar/animation.js';
// Note: getEnvVar functionality will be replaced by direct process.env access

const logger = createLogger('AliyunHttpChatModel', LogLevel.INFO);
import {
    validateHttpConfig,
    ALIYUN_MODELS,
    ALIYUN_HTTP_CONFIG,
    ALIYUN_HTTP_ENDPOINTS,
    getHttpEndpoint
} from './AliyunConfig.js';



export class AliyunHttpChatModel extends HttpChatModel {
    constructor(options = {}) {
        // Set API mode and Aliyun-specific configurations
        super({
            ...options,
            apiMode: 'http',
            model: options.model || ALIYUN_MODELS.HTTP_PRIMARY, // Default to qwen-turbo
            apiKey: options.apiKey || process.env.VITE_DASHSCOPE_API_KEY || '',
            provider: 'Aliyun'
        });

        // MEMORY LEAK FIX: Enhanced memory management with proper cleanup
        this._temporaryRefs = new WeakMap();
        this._activeConnections = new Set();
        this._cleanupTasks = new Set();
        this._eventListeners = new Map(); // Track event listeners for cleanup
        this._timers = new Set(); // Track timers for cleanup
        this._abortControllers = new Set(); // Track abort controllers for cleanup

        // Environment-aware configuration
        this.apiConfig = getApiConfig();

        // Rate limiting integration (respects environment settings)
        this.rateLimiterManager = getGlobalRateLimiterManager();
        this.rateLimiter = this.apiConfig.rateLimitEnabled ?
            this.rateLimiterManager.getLimiter('http', {
                requestsPerSecond: options.rateLimitRPS || (this.apiConfig.testMode ? 2 : 10),
                burstLimit: options.rateLimitBurst || (this.apiConfig.testMode ? 4 : 20),
                maxQueueSize: options.rateLimitQueueSize || (this.apiConfig.testMode ? 10 : 50)
            }) : null;

        // Performance monitoring
        this.performanceMonitor = new PerformanceMonitor({
            alertThresholds: {
                p95Latency: this.apiConfig.testMode ? 2000 : 5000, // Stricter in test mode
                errorRate: 0.05,
                timeoutRate: 0.02
            }
        });

        // HTTP-specific configuration - environment-aware endpoint
        this.baseURL = options.baseURL || getHttpEndpoint();

        // Initialize OpenAI client for Aliyun's compatible endpoint
        // Use consistent endpoint configuration
        const isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined';
        const openAIBaseURL = isBrowser ?
            `${getDownloadServerUrl()}/api/llm` : // Use download server API route (portManager)
            ALIYUN_HTTP_ENDPOINTS.COMPATIBLE; // Direct in server

        this.openai = new OpenAI({
            apiKey: this.apiKey,
            baseURL: openAIBaseURL,
            timeout: 120000, // SECURITY FIX: 120 seconds per Aliyun compliance (was 10s)
            ...(isBrowser && { dangerouslyAllowBrowser: true })
        });

        this.logger.info(`🔗 OpenAI client configured with endpoint: ${openAIBaseURL}`);

        // Validate Aliyun-specific configuration
        const aliyunConfig = {
            apiKey: this.apiKey,
            model: this.model,
            http: {
                endpoint: this.baseURL,
                timeout: this.timeout
            }
        };

        const validation = validateHttpConfig(aliyunConfig);
        if (!validation.isValid) {
            throw new Error(`Invalid Aliyun configuration: ${validation.errors.join(', ')}`);
        }
        this.streamingTimeout = options.streamingTimeout || 300; // Aggressive for streaming

        // OpenAI compatibility settings
        this.useOpenAICompatible = options.useOpenAI !== false; // Default to true
        this.logger.info(`🔗 Aliyun HTTP Model initialized with OpenAI SDK compatibility: ${this.useOpenAICompatible}`);

        // Adaptive timeout configuration - SECURITY FIX: Aliyun-compliant values
        this.adaptiveTimeout = {
            enabled: options.adaptiveTimeout !== false,
            baseTimeout: Math.max(this.timeout, 120000), // SECURITY FIX: 120 seconds minimum (was 10s)
            streamingTimeout: this.streamingTimeout,
            toolCallTimeout: options.toolCallTimeout || 60000, // SECURITY FIX: 60s for tool calls (was 3s)
            fastModelTimeout: options.fastModelTimeout || 30000, // SECURITY FIX: 30s for qwen-turbo (was 2s)
            maxTimeout: options.maxTimeout || 300000 // SECURITY FIX: 300s for complex reasoning (was 30s)
        };

        // Note: Context management moved to agent service level for better separation of concerns
    }

    /**
     * Aliyun HTTP-specific initialization with OpenAI compatibility
     * @protected
     */
    async _initializeModeSpecific(options) {
        // Validate HTTP-specific configuration
        this._validateHttpConfiguration();

        // Set up OpenAI-compatible endpoint using consolidated configuration
        this.openAICompatibleEndpoint = options.openAICompatible !== false;
        if (this.openAICompatibleEndpoint) {
            // In browser, use download server API route from port manager (typically 2994)
            if (typeof window !== 'undefined' && typeof document !== 'undefined') {
                this.baseURL = `${getDownloadServerUrl()}/api/llm`;
                this.logger.debug('🔗 Using download server HTTP endpoint for System 2:', this.baseURL);
            } else {
                // Server-side: use direct Aliyun endpoint
                this.baseURL = 'https://dashscope.aliyuncs.com/compatible-mode/v1';
                this.logger.debug('🔗 Using direct Aliyun endpoint for System 2 (server)');
            }
        }
    }

    /**
     * Validate HTTP-specific configuration
     * @private
     */
    _validateHttpConfiguration() {
        const supportedModels = ['qwen-turbo', 'qwen-plus', 'qwen-max'];
        if (!supportedModels.includes(this.model)) {
            throw new Error(`Unsupported HTTP model: ${this.model}. Supported models: ${supportedModels.join(', ')}`);
        }

        logger.debug('✅ HTTP model configuration validated');
    }

    /**
     * HTTP-specific health check implementation
     * @protected
     */
    async _performHealthCheck() {
        const testMessage = [new HumanMessage('Test connection')];

        // Health check needs to include provider field for server validation
        const healthOptions = {
            maxTokens: 10,
            provider: 'aliyun',
            model: this.model
        };

        // Health checks should use a generous timeout to account for network latency
        const healthContext = {
            isHealthCheck: true,
            urgency: 'low' // Use generous timeout for health checks
        };

        await this._makeHttpRequest(testMessage, healthOptions, 0, healthContext);
        return { httpCheck: true };
    }

    /**
     * Calculate adaptive timeout based on request context
     * @private
     */
    _calculateTimeout(context = {}) {
        if (!this.adaptiveTimeout.enabled) {
            return context.streaming ? this.streamingTimeout : this.timeout;
        }

        // Health checks get generous timeout to avoid initialization failures
        // Aliyun recommends 120+ seconds connection timeout, using full compliance
        if (context.isHealthCheck) {
            const healthTimeout = Math.max(this.adaptiveTimeout.maxTimeout, 120000); // SECURITY FIX: 120s per Aliyun docs (was 30s)
            this.logger.debug(`Using health check timeout: ${healthTimeout}ms`);
            return healthTimeout;
        }

        let calculatedTimeout = this.adaptiveTimeout.baseTimeout;

        // Adjust for streaming
        if (context.streaming) {
            calculatedTimeout = this.adaptiveTimeout.streamingTimeout;
        }

        // Adjust for tool calls (autonomous tools need faster response)
        if (context.hasToolCalls) {
            calculatedTimeout = Math.min(calculatedTimeout, this.adaptiveTimeout.toolCallTimeout);
        }

        // Adjust for model type - qwen-plus needs more time than qwen-turbo
        if (this.model === 'qwen-turbo') {
            calculatedTimeout = Math.min(calculatedTimeout, this.adaptiveTimeout.fastModelTimeout);
        } else if (this.model === 'qwen-plus') {
            // qwen-plus typically needs 2-5 seconds for complex reasoning
            calculatedTimeout = Math.max(calculatedTimeout, 5000);
        } else if (this.model === 'qwen-max') {
            calculatedTimeout = Math.max(calculatedTimeout, calculatedTimeout * 1.2);
        }

        // Apply context-specific adjustments
        if (context.messageCount > 10) {
            calculatedTimeout *= 1.1; // Longer conversations need slightly more time
        }

        if (context.urgency === 'high') {
            calculatedTimeout *= 0.8; // Prioritize speed for urgent requests
        } else if (context.urgency === 'low') {
            calculatedTimeout *= 1.5; // Use generous timeout for low priority requests
        }

        // Special handling for dual brain system contextual analysis
        if (context.isDualBrainAnalysis || context.isContextualAnalysis) {
            calculatedTimeout = Math.max(calculatedTimeout, 8000); // Increased to 8 seconds for complex reasoning
            this.logger.debug('🧠 Using extended timeout for dual brain analysis');
        }

        // Ensure within bounds
        calculatedTimeout = Math.min(calculatedTimeout, this.adaptiveTimeout.maxTimeout);
        calculatedTimeout = Math.max(calculatedTimeout, 200); // Minimum 200ms

        this.logger.debug(`Calculated adaptive timeout: ${calculatedTimeout}ms`, {
            model: this.model,
            streaming: context.streaming,
            hasToolCalls: context.hasToolCalls,
            messageCount: context.messageCount,
            urgency: context.urgency,
            isHealthCheck: context.isHealthCheck
        });

        return calculatedTimeout;
    }

    /**
     * LangChain v0.3 compliance: Return HTTP-specific invocation parameters
     */
    invocationParams(options = {}) {
        return {
            ...super.invocationParams(options),
            base_url: this.baseURL,
            adaptive_timeout: this.adaptiveTimeout
        };
    }

    // invoke() method now inherited from HttpChatModel base class

    /**
     * Aliyun-specific HTTP request implementation using OpenAI SDK (simplified)
     * ✅ FIXED: Single API path to prevent double calling
     * @protected
     */
    async _performHttpRequest(messages, options = {}, context = {}) {
        // ✅ FIXED: Always use OpenAI SDK only - no double calling
        logger.debug('🔧 [AliyunHttpChatModel] Invoking with single OpenAI SDK path', {
            streaming: context.streaming || false,
            hasToolCalls: !!(this.boundTools && this.boundTools.length > 0),
            messageCount: messages.length,
            urgency: context.urgency || 'medium',
            isDualBrainAnalysis: context.isDualBrainAnalysis || false,
            singlePath: true
        });

        return await this._performOpenAISDKRequest(messages, options, context);
    }

    /**
     * Perform request using OpenAI SDK with Aliyun endpoint
     * Enhanced with rate limiting and input validation
     * @private
     */
    async _performOpenAISDKRequest(messages, options = {}, context = {}) {
        // Input validation
        this._validateRequestInputs(messages, options, context);

        // Generate unique request ID for tracking
        const requestId = `http_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // Start performance monitoring
        this.performanceMonitor.startRequest(requestId, {
            model: this.model,
            endpoint: this.openai?.baseURL || 'unknown',
            isDualBrainAnalysis: context.isDualBrainAnalysis,
            hasToolCalls: context.hasToolCalls,
            urgency: context.urgency || 'medium'
        });

        // Rate limiting - acquire permission before making request (if enabled)
        if (this.rateLimiter) {
            try {
                await this.rateLimiter.acquire({
                    model: this.model,
                    isDualBrainAnalysis: context.isDualBrainAnalysis,
                    queueTimeout: context.queueTimeout || 30000
                });
            } catch (rateLimitError) {
                this.performanceMonitor.endRequestWithError(requestId, rateLimitError);
                this.logger.warn('Request rate limited:', rateLimitError.message);
                throw rateLimitError;
            }
        }

        const startTime = Date.now();

        try {
            // Build request parameters for OpenAI SDK
            const requestParams = {
                model: options.model || this.model || 'qwen-plus',
                messages: this._formatMessages(messages),
                temperature: options.temperature || this.temperature || 0.7,
                max_tokens: options.max_tokens || options.maxTokens || this.httpConfig.maxTokens || 2000
            };

            // Add provider field for our unified service (required for validation)
            if (options.provider) {
                requestParams.provider = options.provider;
            }

            // Add tools with parallel execution support
            if (this.boundTools && this.boundTools.length > 0) {
                // Use existing _formatToolsForAPI method from BaseChatModel
                requestParams.tools = this._formatToolsForAPI(this.boundTools);
                requestParams.tool_choice = options.tool_choice || options.toolChoice || 'auto';
                // Enable parallel tool calls for better performance (Aliyun feature)
                requestParams.parallel_tool_calls = options.parallel_tool_calls !== false;
                this.logger.debug(`🔧 Added ${this.boundTools.length} tools to OpenAI request (parallel: ${requestParams.parallel_tool_calls})`);
            }

            // Add Aliyun-specific enhancements for deep thinking models
            // ✅ ENABLE by default for System 2 (HTTP) to ensure consistent thinking mode
            requestParams.enable_thinking = options.enable_thinking !== false; // Default to true unless explicitly disabled
            if (requestParams.enable_thinking) {
                this.logger.debug('🧠 Enabled deep thinking mode for Aliyun');
            }

            // ✅ ENABLE streaming by default for better responsiveness
            requestParams.stream = context.streaming !== false; // Default to true unless explicitly disabled
            if (requestParams.stream) {
                this.logger.debug('🌊 Enabled streaming mode for Aliyun');
            }

            if (options.thinking_budget) {
                requestParams.thinking_budget = options.thinking_budget;
                this.logger.debug(`🧠 Set thinking budget: ${options.thinking_budget} tokens`);
            }

            if (options.enable_search) {
                requestParams.enable_search = true;
                if (options.search_options) {
                    requestParams.search_options = options.search_options;
                }
                this.logger.debug('🔍 Enabled search capabilities');
            }

            // Aliyun JSON mode configuration (simple): only when caller explicitly requests it
            const formattedMessages = this._formatMessages(messages);
            if (options.response_format) {
                requestParams.response_format = options.response_format;
                // Ensure Aliyun requirement: prompts must contain the word 'json'
                const hasJsonWord = this._validateJsonPromptRequirement(formattedMessages);
                if (!hasJsonWord) {
                    requestParams.messages = [
                        { role: 'system', content: 'Return a valid JSON object (json) as content when structured output is requested.' },
                        ...formattedMessages
                    ];
                }
            }

            // 🧠 DUAL BRAIN LOGGING - System 2 (HTTP) Processing
            this.logger.debug('🧠 [SYSTEM-2-INVOKE] HTTP System 2 starting reasoning process', {
                model: requestParams.model,
                messageCount: messages.length,
                hasTools: !!requestParams.tools?.length,
                toolCount: requestParams.tools?.length || 0,
                enable_thinking: requestParams.enable_thinking,
                isDualBrainAnalysis: context.isDualBrainAnalysis || false,
                requestId: requestId,
                singleAPIPath: true,
                context: context
            });

            // Log the messages being processed by System 2
            this.logger.debug('🧠 [SYSTEM-2-MESSAGES] System 2 processing messages:', {
                messages: messages.map((msg, idx) => ({
                    index: idx,
                    role: msg.role || 'unknown',
                    content: typeof msg.content === 'string' ? msg.content.substring(0, 200) + '...' : 'non-string',
                    contentLength: typeof msg.content === 'string' ? msg.content.length : 0
                })),
                requestId
            });

            // Log available tools for System 2
            if (requestParams.tools?.length > 0) {
                this.logger.info('🧠 [SYSTEM-2-TOOLS] System 2 has access to tools:', {
                    toolNames: requestParams.tools.map(tool => tool.function?.name || 'unnamed'),
                    toolCount: requestParams.tools.length,
                    tool_choice: requestParams.tool_choice,
                    requestId
                });
            }

            // Calculate timeout for this request
            const timeout = this._calculateTimeout(context);

            // Create timeout promise
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => {
                    reject(new Error(`HTTP request timeout after ${timeout}ms`));
                }, timeout);
            });

            // Make request with timeout racing
            const response = await Promise.race([
                this.openai.chat.completions.create(requestParams),
                timeoutPromise
            ]);

            const processingTime = Date.now() - startTime;

            // Record successful completion
            this.performanceMonitor.endRequest(requestId, {
                processingTime,
                tokenUsage: response.usage
            });

            this.logger.info(`✅ HTTP API call completed in ${processingTime}ms`);

            // 🧠 DUAL BRAIN LOGGING - System 2 Response Analysis
            const parsedResponse = this._parseOpenAISDKResponse(response);

            this.logger.debug('🧠 [SYSTEM-2-RESPONSE] System 2 reasoning completed', {
                requestId,
                processingTime,
                tokenUsage: response.usage,
                hasChoices: response.choices?.length > 0,
                choiceCount: response.choices?.length || 0,
                finishReason: response.choices?.[0]?.finish_reason,
                hasToolCalls: response.choices?.[0]?.message?.tool_calls?.length > 0,
                toolCallCount: response.choices?.[0]?.message?.tool_calls?.length || 0
            });

            // Log the actual response content and any tool calls
            if (response.choices?.[0]?.message) {
                const message = response.choices[0].message;

                // Enhanced JSON parsing for tool calling integration
                let contentString = '';
                let parsedJSON = null;
                let animationToolData = null;

                if (message.content) {
                    contentString = typeof message.content === 'string'
                        ? message.content
                        : JSON.stringify(message.content);

                    // Use robust JSON parser for better extraction
                    const parseResult = parseRobustJSON(contentString, {
                        extractionStrategies: ['direct', 'codeblock', 'thinking', 'lastjson'],
                        allowFallback: false,
                        logLevel: 'debug'
                    });

                    if (parseResult.success) {
                        parsedJSON = parseResult.data;
                        this.logger.debug('parsedJSON', parsedJSON);
                        // Check if this looks like animation tool data
                        if (parsedJSON && this._isAnimationToolData(parsedJSON)) {
                            animationToolData = parsedJSON;
                            this.logger.debug('🎭 Animation tool data detected in System 2 response', {
                                hasAnimation: !!animationToolData.animation,
                                hasMessage: !!animationToolData.message,
                                responseType: animationToolData.responseType
                            });
                        }
                    } else {
                        // Fallback to simple JSON detection
                        if (contentString.trim().startsWith('{') && contentString.trim().endsWith('}')) {
                            try {
                                parsedJSON = JSON.parse(contentString);
                            } catch (e) {
                                parsedJSON = null;
                            }
                        }
                    }
                }

                this.logger.debug('🧠 [SYSTEM-2-CONTENT] System 2 generated content:', {
                    role: message.role,
                    hasContent: !!contentString,
                    contentLength: contentString.length,
                    content: contentString || 'none',
                    parsedJSON: parsedJSON || undefined,
                    animationToolData: animationToolData || undefined,
                    hasToolCalls: !!message.tool_calls?.length,
                    requestId
                });

                // Log tool calls if present
                if (message.tool_calls?.length > 0) {
                    this.logger.info('🧠 [SYSTEM-2-TOOL-CALLS] System 2 executed tool calls:', {
                        toolCalls: message.tool_calls.map(tc => ({
                            id: tc.id,
                            type: tc.type,
                            functionName: tc.function?.name,
                            argumentsLength: tc.function?.arguments ? tc.function.arguments.length : 0,
                            argumentsPreview: tc.function?.arguments ?
                                tc.function.arguments.substring(0, 200) + '...' : 'none'
                        })),
                        requestId
                    });
                }
            }

            return parsedResponse;

        } catch (error) {
            const processingTime = Date.now() - startTime;
            this.logger.error(`❌ OpenAI SDK request failed after ${processingTime}ms:`, {
                error: error.message,
                model: this.model,
                endpoint: this.openai?.baseURL,
                isDualBrain: context.isDualBrainAnalysis,
                timeout: this._calculateTimeout(context)
            });

            // Record error in performance monitoring
            this.performanceMonitor.endRequestWithError(requestId, error);

            // Enhanced error handling with structured error types
            const errorContext = {
                model: this.model,
                endpoint: this.openai?.baseURL,
                isDualBrain: context.isDualBrainAnalysis,
                timeout: this._calculateTimeout(context),
                requestId
            };

            // Use structured error factory for proper error types
            throw ProviderErrorFactory.createFromError(error, errorContext);
        }
    }

    /**
     * Parse OpenAI SDK response from Aliyun
     * @private
     */
    _parseOpenAISDKResponse(response) {
        const choice = response.choices?.[0];
        if (!choice) {
            throw new Error('Invalid response format: missing choices');
        }

        const message = choice.message;
        let content = message.content || '';

        // Parse tool calls if present (OpenAI format)
        const toolCalls = this._parseOpenAIToolCalls(message.tool_calls);
        if (toolCalls.length > 0) {
            this.logger.info(`Parsed ${toolCalls.length} tool calls from OpenAI SDK response`);
        }

        // Handle reasoning content if present (Aliyun deep thinking feature)
        let reasoningContent = '';
        if (message.reasoning_content) {
            reasoningContent = message.reasoning_content;
            this.logger.debug('📝 Reasoning content received from deep thinking model');
        }

        // Parse structured output if JSON mode was enabled
        let parsedStructuredOutput = null;
        if (this._isStructuredOutputEnabled(response)) {
            const parseResult = this._parseStructuredOutput(content);
            if (parseResult.parsed) {
                parsedStructuredOutput = parseResult.parsed;
                this.logger.debug('✅ Successfully parsed structured JSON output');
            } else if (parseResult.parseError) {
                this.logger.warn('⚠️ Structured output parsing failed:', parseResult.parseError);
            }
        }

        // Create AI message with tool calls
        const aiMessage = this._createAIMessage(content, toolCalls, {
            usage: response.usage,
            finish_reason: choice.finish_reason,
            reasoning_content: reasoningContent,
            structured_output: parsedStructuredOutput,
            model: response.model,
            id: response.id,
            created: response.created
        });

        return {
            generations: [{
                text: content,
                tool_calls: toolCalls,
                message: aiMessage,
                reasoning_content: reasoningContent,
                structured_output: parsedStructuredOutput
            }]
        };
    }

    /**
     * Build Aliyun-specific structured output configuration
     * @private
     */
    _buildAliyunStructuredOutputConfig(options = {}) {
        const responseFormat = options.response_format || options.responseFormat;

        if (!responseFormat) {
            return null;
        }

        const config = {};

        // Aliyun supports json_object mode
        if (responseFormat.type === 'json_object') {
            config.response_format = {
                type: 'json_object'
            };

            // Validate that messages contain 'json' as required by Aliyun
            const hasJsonInPrompt = this._validateJsonPromptRequirement(options.messages);
            if (!hasJsonInPrompt) {
                // Quietly ensure compliance without spamming warnings
                this.logger.debug('Aliyun JSON mode active: injecting minimal system instruction mentioning json');
            }

            this.logger.debug('🔧 Enabling Aliyun JSON mode');
        } else if (responseFormat.type === 'json_schema') {
            // For Aliyun, fall back to json_object 
            // Note: Aliyun doesn't support JSON Schema validation server-side
            config.response_format = {
                type: 'json_object'
            };
            this.logger.debug('🔧 Enabling Aliyun JSON mode (schema validation will be client-side)');
            this.logger.info('📋 Note: Aliyun does not support JSON Schema server-side validation. Consider providing JSON examples in your prompt.');
        }

        return config;
    }

    /**
     * Validate that messages contain 'json' word as required by Aliyun
     * @private
     */
    _validateJsonPromptRequirement(messages) {
        if (!messages || !Array.isArray(messages)) {
            return false;
        }

        // Check if any message contains the word 'json' (case insensitive)
        return messages.some(msg => {
            if (typeof msg.content === 'string') {
                return /json/i.test(msg.content);
            }
            return false;
        });
    }

    /**
     * Check if structured output was enabled for this response
     * @private
     */
    _isStructuredOutputEnabled(response) {
        // For Aliyun, we can check if the response content looks like JSON
        // and if JSON mode was likely enabled based on content structure
        const content = response.choices?.[0]?.message?.content?.trim();
        if (!content) {
            return false;
        }

        // Check if content is valid JSON structure
        return (content.startsWith('{') && content.endsWith('}')) ||
            (content.startsWith('[') && content.endsWith(']'));
    }

    /**
     * Parse structured output using robust JSON parser
     * @private
     */
    _parseStructuredOutput(content) {
        try {
            const parseResult = parseRobustJSON(content, {
                extractionStrategies: ['direct', 'codeblock', 'lastjson'],
                allowFallback: false,
                logLevel: 'debug'
            });

            return {
                parsed: parseResult.success ? parseResult.data : null,
                parseError: parseResult.success ? null : (parseResult.error || 'Unknown parsing error'),
                parseMethod: parseResult.method,
                wasRepaired: parseResult.wasRepaired
            };
        } catch (error) {
            return {
                parsed: null,
                parseError: error.message,
                parseMethod: 'failed',
                wasRepaired: false
            };
        }
    }

    /**
     * Heuristically determine if a parsed JSON object looks like animation tool data
     * Supports multiple shapes produced by tools, e.g. { animationId, success, ... } or { animation, message, ... }
     * @private
     */
    _isAnimationToolData(data) {
        if (!data || typeof data !== 'object') return false;

        // Common tool result shape from selectAnimationTool
        if (typeof data.animationId === 'string' && (data.success === true || typeof data.category === 'string')) {
            return true;
        }

        // Alternate shapes: explicit animation field or declared response type
        if (data.animation && (typeof data.animation === 'string' || typeof data.animation === 'object')) {
            return true;
        }

        if (data.responseType === 'animation' || data.response_type === 'animation') {
            return true;
        }

        // Minimal fallback: looks like a tool payload with message + some animation hint
        if (typeof data.message === 'string' && (data.animationName || data.animation_id)) {
            return true;
        }

        return false;
    }

    /**
     * Perform request using native Aliyun API format
     * @private
     */
    /**
     * Aliyun-specific streaming implementation
     * @protected
     */
    async *_performStreamingRequest(messages, options, context) {
        // Build request parameters for streaming
        const requestParams = {
            model: options.model || this.model || 'qwen-turbo',
            messages: this._formatMessages(messages),
            temperature: options.temperature || this.temperature || 0.7,
            max_tokens: options.max_tokens || options.maxTokens || this.httpConfig.maxTokens || 2000,
            stream: true
        };

        // Add provider field for our unified service (required for validation)
        if (options.provider) {
            requestParams.provider = options.provider;
        }

        // Add tools with parallel execution support for streaming
        if (this.boundTools && this.boundTools.length > 0) {
            // Use existing _formatToolsForAPI method from BaseChatModel
            requestParams.tools = this._formatToolsForAPI(this.boundTools);
            requestParams.tool_choice = options.tool_choice || options.toolChoice || 'auto';
            requestParams.parallel_tool_calls = options.parallel_tool_calls !== false;
            this.logger.debug(`🔧 Added ${this.boundTools.length} tools for streaming (parallel: ${requestParams.parallel_tool_calls})`);
        }

        // Add Aliyun-specific enhancements
        if (options.enable_thinking || context.isDualBrainAnalysis) {
            requestParams.enable_thinking = true;
        }

        // Use OpenAI SDK for streaming
        const stream = await this.openai.chat.completions.create(requestParams);

        let reasoningContent = '';
        let answerContent = '';
        let accumulatedToolCalls = new Map();

        for await (const chunk of stream) {
            if (!chunk.choices?.length) {
                if (chunk.usage) {
                    this.logger.debug('Usage:', chunk.usage);
                }
                continue;
            }

            const delta = chunk.choices[0].delta;

            // Handle reasoning content (deep thinking)
            if (delta.reasoning_content) {
                reasoningContent += delta.reasoning_content;
                yield {
                    message: this._createAIMessageChunk(delta.reasoning_content, [], {
                        reasoning_content: delta.reasoning_content,
                        isReasoning: true
                    }),
                    text: delta.reasoning_content
                };
            }

            // Handle regular content
            if (delta.content) {
                answerContent += delta.content;
                yield {
                    message: this._createAIMessageChunk(delta.content, [], {
                        incremental: true,
                        accumulated: answerContent
                    }),
                    text: delta.content
                };
            }

            // Handle streaming tool calls (Aliyun format)
            if (delta.tool_calls) {
                for (const toolCall of delta.tool_calls) {
                    const index = toolCall.index || 0;

                    if (!accumulatedToolCalls.has(index)) {
                        // First chunk for this tool call
                        accumulatedToolCalls.set(index, {
                            id: toolCall.id || `call_${Date.now()}_${index}`,
                            name: toolCall.function?.name || '',
                            args: toolCall.function?.arguments || ''
                        });

                        this.logger.debug(`🔧 Started streaming tool call ${index}: ${toolCall.function?.name}`);
                    } else {
                        // Subsequent chunks - accumulate arguments
                        const existing = accumulatedToolCalls.get(index);
                        if (toolCall.function?.arguments) {
                            existing.args += toolCall.function.arguments;
                        }
                    }
                }

                // Yield tool call chunk
                const toolCallsArray = Array.from(accumulatedToolCalls.values()).map(tc => ({
                    id: tc.id,
                    name: tc.name,
                    args: tc.args ? this._parseStreamingToolArguments(tc.args) : {}
                }));

                yield {
                    message: this._createAIMessageChunk('', toolCallsArray, {
                        tool_calls: toolCallsArray,
                        isPartial: true
                    }),
                    text: ''
                };
            }
        }
    }

    /**
     * Create AI message chunk for streaming (LangChain v0.3 compatible)
     * @private
     */
    _createAIMessageChunk(content, toolCalls = [], metadata = {}) {
        // Create the AIMessage in the exact format that LangGraph expects
        // Following VLLMChatModel pattern for compatibility
        const aiMessage = new AIMessage({
            content,
            ...(toolCalls.length > 0 && { tool_calls: toolCalls })
        });

        return aiMessage;
    }

    // Abstract methods from BaseChatModel that HTTP models don't support
    buildWebSocketUrl() { throw new Error('HttpChatModel does not support WebSocket connections'); }
    getConnectionOptions() { throw new Error('HttpChatModel does not support WebSocket connections'); }
    processProviderMessage(message) { throw new Error('HttpChatModel does not support WebSocket message processing'); }
    async initializeSession(sessionConfig = {}) { return Promise.resolve(true); }
    async _sendAudioToProvider(audioData, format) { throw new Error('Audio transmission not supported by HttpChatModel.'); }



    /**
     * Get HTTP-specific model performance metrics
     */
    getMetrics() {
        return {
            ...super.getMetrics(),
            baseURL: this.baseURL,
            streamingTimeout: this.streamingTimeout,
            adaptiveTimeout: this.adaptiveTimeout,
            provider: 'Aliyun',
            supportedModels: ['qwen-turbo', 'qwen-plus', 'qwen-max'],
            httpSpecific: {
                requestsSent: this.metrics.requests,
                averageLatency: this.metrics.averageResponseTime,
                errorRate: this.metrics.failures / Math.max(this.metrics.requests, 1)
            }
        };
    }

    /**
     * Parse OpenAI-formatted tool calls to LangChain format
     * @private
     */
    _parseOpenAIToolCalls(toolCallsData) {
        if (!Array.isArray(toolCallsData)) {
            return [];
        }

        return toolCallsData.map(call => ({
            id: call.id || `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            name: call.function?.name || call.name,
            args: typeof call.function?.arguments === 'string' ?
                JSON.parse(call.function.arguments) :
                call.function?.arguments || call.args || {}
        }));
    }

    /**
     * Validate request inputs to prevent malicious or malformed requests
     * @private
     */
    _validateRequestInputs(messages, options = {}, context = {}) {
        // Validate messages
        if (!messages || (Array.isArray(messages) && messages.length === 0)) {
            throw new Error('Messages cannot be empty');
        }

        // Validate message content
        const messageArray = Array.isArray(messages) ? messages : [messages];
        for (const message of messageArray) {
            if (typeof message === 'object' && message.content) {
                // Check for extremely long content that could cause issues
                if (typeof message.content === 'string' && message.content.length > 100000) {
                    throw new Error('Message content too long (max 100k characters)');
                }

                // Basic content sanitization
                if (typeof message.content === 'string') {
                    // Remove potential script injection attempts
                    const suspiciousPatterns = [
                        /<script[^>]*>/i,
                        /javascript:/i,
                        /data:text\/html/i,
                        /vbscript:/i
                    ];

                    for (const pattern of suspiciousPatterns) {
                        if (pattern.test(message.content)) {
                            this.logger.warn('Potentially malicious content detected and sanitized');
                            message.content = message.content.replace(pattern, '[SANITIZED]');
                        }
                    }
                }
            }
        }

        // Validate options
        if (options.temperature !== undefined) {
            if (typeof options.temperature !== 'number' ||
                options.temperature < 0 ||
                options.temperature > 2) {
                throw new Error('Temperature must be a number between 0 and 2');
            }
        }

        if (options.max_tokens !== undefined && options.maxTokens !== undefined) {
            const maxTokens = options.max_tokens || options.maxTokens;
            if (typeof maxTokens !== 'number' || maxTokens < 1 || maxTokens > 32000) {
                throw new Error('Max tokens must be between 1 and 32000');
            }
        }

        // Validate model name
        if (options.model && typeof options.model === 'string') {
            const validModels = ['qwen-turbo', 'qwen-plus', 'qwen-max'];
            if (!validModels.includes(options.model)) {
                this.logger.warn(`Unknown model: ${options.model}, using default`);
                delete options.model; // Remove invalid model to use default
            }
        }

        // Validate provider field if present
        if (options.provider && options.provider !== 'aliyun') {
            this.logger.warn(`Invalid provider: ${options.provider}, should be 'aliyun'`);
            options.provider = 'aliyun';
        }
    }

    /**
     * MEMORY LEAK FIX: Comprehensive cleanup with proper resource management
     */
    async cleanup() {
        try {
            this.logger.debug('🧹 Starting comprehensive cleanup...');

            // MEMORY LEAK FIX: Clear all abort controllers
            if (this._abortControllers) {
                for (const controller of this._abortControllers) {
                    try {
                        if (!controller.signal.aborted) {
                            controller.abort('Cleanup initiated');
                        }
                    } catch (error) {
                        this.logger.warn('Failed to abort controller:', error.message);
                    }
                }
                this._abortControllers.clear();
            }

            // MEMORY LEAK FIX: Clear all timers
            if (this._timers) {
                for (const timer of this._timers) {
                    try {
                        clearTimeout(timer);
                        clearInterval(timer);
                    } catch (error) {
                        this.logger.warn('Failed to clear timer:', error.message);
                    }
                }
                this._timers.clear();
            }

            // MEMORY LEAK FIX: Remove all event listeners
            if (this._eventListeners) {
                for (const [target, listeners] of this._eventListeners) {
                    for (const { event, handler } of listeners) {
                        try {
                            if (target && typeof target.removeEventListener === 'function') {
                                target.removeEventListener(event, handler);
                            }
                        } catch (error) {
                            this.logger.warn('Failed to remove event listener:', error.message);
                        }
                    }
                }
                this._eventListeners.clear();
            }

            // Clear rate limiter references
            if (this.rateLimiter) {
                // Note: Don't destroy global rate limiter, just clear reference
                this.rateLimiter = null;
            }

            // MEMORY LEAK FIX: Reset WeakMap properly
            if (this._temporaryRefs) {
                this._temporaryRefs = new WeakMap(); // Reset WeakMap
            }

            // MEMORY LEAK FIX: Clear active connections with proper cleanup
            if (this._activeConnections) {
                for (const connection of this._activeConnections) {
                    try {
                        if (connection && typeof connection.close === 'function') {
                            connection.close();
                        } else if (connection && typeof connection.destroy === 'function') {
                            connection.destroy();
                        }
                    } catch (error) {
                        this.logger.warn('Failed to close connection:', error.message);
                    }
                }
                this._activeConnections.clear();
            }

            // Run cleanup tasks with error handling
            if (this._cleanupTasks) {
                for (const task of this._cleanupTasks) {
                    try {
                        if (typeof task === 'function') {
                            await task(); // Support async cleanup tasks
                        }
                    } catch (error) {
                        this.logger.warn('Cleanup task failed:', error.message);
                    }
                }
                this._cleanupTasks.clear();
            }

            // MEMORY LEAK FIX: Clear OpenAI client references
            if (this.openai) {
                this.openai = null;
            }

            // MEMORY LEAK FIX: Clear performance monitor references
            if (this.performanceMonitor) {
                this.performanceMonitor = null;
            }

            // Call parent cleanup
            if (super.cleanup) {
                await super.cleanup();
            }

            this.logger.info('✅ AliyunHttpChatModel comprehensive cleanup completed');

        } catch (error) {
            this.logger.error('❌ Cleanup failed:', error.message);
            throw error;
        }
    }

    /**
     * MEMORY LEAK FIX: Helper method to register event listeners for proper cleanup
     */
    _addEventListenerWithCleanup(target, event, handler) {
        if (!this._eventListeners.has(target)) {
            this._eventListeners.set(target, []);
        }
        this._eventListeners.get(target).push({ event, handler });
        target.addEventListener(event, handler);
    }

    /**
     * MEMORY LEAK FIX: Helper method to register timers for proper cleanup
     */
    _setTimeoutWithCleanup(callback, delay) {
        const timer = setTimeout(() => {
            this._timers.delete(timer);
            callback();
        }, delay);
        this._timers.add(timer);
        return timer;
    }

    /**
     * MEMORY LEAK FIX: Helper method to register abort controllers for proper cleanup
     */
    _createAbortControllerWithCleanup() {
        const controller = new AbortController();
        this._abortControllers.add(controller);

        // Remove from set when aborted
        controller.signal.addEventListener('abort', () => {
            this._abortControllers.delete(controller);
        });

        return controller;
    }

    // Abstract methods now implemented by HttpChatModel base class
}

export default AliyunHttpChatModel;