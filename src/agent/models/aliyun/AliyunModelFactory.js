/**
 * Aliyun Model Factory
 * Extended factory pattern for managing HTTP and WebSocket models with Aliyun-specific optimizations
 * Implements automatic model selection based on task requirements
 * 
 * This factory provides intelligent routing between:
 * - HTTP models (qwen-plus, qwen-turbo, qwen-max) for autonomous tools
 * - WebSocket models (ALIYUN_MODELS.DEFAULT_REALTIME) for real-time voice
 * 
 * Enhanced Features:
 * - Intelligent routing based on performance metrics
 * - TTL-based caching with LRU eviction
 * - Circuit breaker pattern for service reliability
 * - Comprehensive performance tracking and monitoring
 */

import { createLogger, LogLevel } from '@/utils/logger';
// Import base model factory directly without relying on index re-exports to avoid cycles
import { ModelFactory, ModelType, createBaseModelConfig } from '../ModelFactory.js';
import { AliyunHttpChatModel } from './AliyunHttpChatModel.js';
import { AliyunWebSocketChatModel } from './AliyunWebSocketChatModel.js';
import {
    createHybridConfig,
    ALIYUN_HTTP_CONFIG,
    ALIYUN_WEBSOCKET_CONFIG,
    validateConfig,
    ALIYUN_MODELS,
    getHttpEndpoint
} from './AliyunConfig.js';
// Infrastructure services are now injected from core.js - no direct imports needed

export class AliyunModelFactory extends ModelFactory {
    constructor(config = {}) {
        // Create Aliyun-specific configuration
        const aliyunConfig = createHybridConfig(config);

        // Create base configuration for parent constructor
        const baseConfig = {
            ...createBaseModelConfig('Aliyun'),
            ...aliyunConfig,
            provider: 'Aliyun',
            defaultHttpModel: aliyunConfig.http?.defaultModel || ALIYUN_MODELS.HTTP_PRIMARY,
            defaultWebSocketModel: aliyunConfig.websocket?.defaultModel || ALIYUN_MODELS.DEFAULT_REALTIME,
            defaultRealtimeModel: aliyunConfig.websocket?.defaultModel || ALIYUN_MODELS.DEFAULT_REALTIME,
            httpEndpoint: aliyunConfig.http?.endpoint || ALIYUN_HTTP_CONFIG.endpoint,
            websocketEndpoint: aliyunConfig.websocket?.endpoint || ALIYUN_WEBSOCKET_CONFIG.endpoint
        };

        // Call parent constructor
        super(baseConfig);

        // Store Aliyun-specific configuration (merge with base config from parent)
        this.config = { ...this.config, ...aliyunConfig };

        // Receive infrastructure services from core.js via dependency injection
        this.infrastructureManager = config.infrastructureManager;

        if (!this.infrastructureManager) {
            throw new Error('AliyunModelFactory requires infrastructureManager to be injected from core.js');
        }

        // Memory-efficient model cache using WeakMap for temporary references
        this.httpModelCache = new Map();
        this.websocketModel = null;
        this._temporaryModelRefs = new WeakMap();
        this._cleanupTasks = new Set();

        // Enhanced performance tracking
        this.metrics = {
            httpRequests: 0,
            websocketRequests: 0,
            totalRequests: 0,
            averageResponseTime: 0,
            errors: 0,
            routingDecisions: 0,
            cacheHits: 0,
            circuitBreakerTrips: 0
        };

        // Add dual-brain context sharing
        this.contextBridge = {
            multimodalContext: null,
            audioAnalysis: null,
            visualContext: null,
            engagementState: null,
            lastUpdate: null
        };

        // Context sharing callbacks
        this.onContextUpdate = null;

        // Validate configuration
        const validation = validateConfig(this.config);
        if (!validation.isValid) {
            throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);
        }

        this.logger.info('AliyunModelFactory initialized with hybrid configuration');
    }

    /**
     * Create HTTP model instance (implements ModelFactory abstract method)
     * @param {string} modelName - Model name (qwen-plus, qwen-turbo, qwen-max)
     * @param {Object} options - Additional options
     * @returns {Promise<AliyunHttpChatModel>}
     */
    async createHttpModel(modelName = null, options = {}) {
        return this.getHttpModel(modelName, options);
    }

    /**
     * Create WebSocket model instance (implements ModelFactory abstract method)
     * @param {string} modelName - Model name (optional, uses default realtime model)
     * @param {Object} options - Additional options
     * @returns {Promise<AliyunWebSocketChatModel>}
     */
    async createWebSocketModel(modelName = null, options = {}) {
        return this.getWebSocketModel(options);
    }

    /**
     * Create Realtime model instance (implements ModelFactory abstract method)
     * @param {string} modelName - Model name (optional, uses default realtime model)
     * @param {Object} options - Additional options
     * @returns {Promise<AliyunWebSocketChatModel>}
     */
    async createRealtimeModel(modelName = null, options = {}) {
        // For Aliyun, realtime and websocket models are the same
        return this.getWebSocketModel(options);
    }

    /**
     * Get HTTP model instance with intelligent caching and routing
     * @param {string} modelName - Model name (qwen-plus, qwen-turbo, qwen-max)
     * @param {Object} options - Additional options
     * @returns {Promise<AliyunHttpChatModel>}
     */
    async getHttpModel(modelName = null, options = {}) {
        const startTime = Date.now();

        // API limits now handled by individual models in BaseChatModel.invoke()

        // Use intelligent routing if no specific model requested
        const selectedModel = modelName || await this._selectOptimalHttpModel(options);
        const cacheManager = await this.infrastructureManager.getCacheManager();
        const cacheKey = cacheManager.generateModelKey(selectedModel, options);

        // Check intelligent cache first
        const cachedModel = await cacheManager.getModel(cacheKey);
        if (cachedModel) {
            this.metrics.cacheHits++;
            this.logger.debug(`Retrieved cached HTTP model: ${selectedModel}`);
            return cachedModel;
        }

        // Check circuit breaker
        const circuitBreaker = await this.infrastructureManager.getCircuitBreaker();
        if (circuitBreaker.isOpen(selectedModel)) {
            this.logger.warn(`Circuit breaker open for ${selectedModel}, attempting fallback`);
            return this._handleCircuitBreakerFallback(selectedModel, options, circuitBreaker);
        }

        try {
            const httpModel = new AliyunHttpChatModel({
                model: selectedModel,
                apiKey: this.config.apiKey,
                baseURL: getHttpEndpoint(), // Use environment-aware endpoint
                timeout: await this._getAdaptiveTimeout(selectedModel, options),
                temperature: options.temperature || 0.7,
                maxTokens: options.maxTokens || 2000,
                provider: 'Aliyun',  // CRITICAL: Provider field required by AliyunHttpChatModel constructor
                ...options
            });

            // Wait for async initialization to complete
            if (httpModel._initPromise) {
                await httpModel._initPromise;
            }

            // Enhanced health check with performance tracking
            const healthCheck = await this._performEnhancedHealthCheck(httpModel, selectedModel);
            if (!healthCheck.healthy) {
                const circuitBreaker = await this.infrastructureManager.getCircuitBreaker();
                if (circuitBreaker) circuitBreaker.recordFailure(selectedModel);
                throw new Error(`HTTP model health check failed: ${healthCheck.error}`);
            }

            // Record successful creation using shared infrastructure
            circuitBreaker.recordSuccess(selectedModel);
            const performanceTracker = await this.infrastructureManager.getPerformanceTracker();
            if (performanceTracker && typeof performanceTracker.recordModelCreation === 'function') {
                performanceTracker.recordModelCreation(selectedModel, healthCheck.responseTime);
            }

            // Cache with TTL using shared cache manager
            await cacheManager.setModel(cacheKey, httpModel, selectedModel);

            // Legacy cache (for backward compatibility)
            this.httpModelCache.set(cacheKey, httpModel);

            const totalTime = Date.now() - startTime;
            this.logger.info(`✅ HTTP model created and cached: ${selectedModel} (health: ${healthCheck.responseTime}ms, total: ${totalTime}ms)`);

            return httpModel;

        } catch (error) {
            const totalTime = Date.now() - startTime;
            circuitBreaker.recordFailure(selectedModel);
            const performanceTracker = await this.infrastructureManager.getPerformanceTracker();
            if (performanceTracker && typeof performanceTracker.recordModelFailure === 'function') {
                performanceTracker.recordModelFailure(selectedModel, totalTime, error);
            }
            this.metrics.errors++;

            this.logger.error(`❌ Failed to create HTTP model ${selectedModel}:`, error.message);

            // Intelligent fallback using router
            const router = await this.infrastructureManager.getRouter();
            const fallbackChain = router ? await router.getFallbackChain(selectedModel, options) : [];
            if (fallbackChain && fallbackChain.length > 0) {
                const fallbackModel = fallbackChain[0];
                this.logger.warn(`Falling back from ${selectedModel} to ${fallbackModel}`);
                return this.getHttpModel(fallbackModel, options);
            }

            throw error;
        }
    }

    /**
     * Get WebSocket model instance
     * @param {Object} options - Additional options
     * @returns {Promise<AliyunWebSocketChatModel>}
     */
    async getWebSocketModel(options = {}) {
        if (this.websocketModel) {
            this.logger.debug('Retrieved cached WebSocket model');
            return this.websocketModel;
        }

        try {
            this.websocketModel = new AliyunWebSocketChatModel({
                apiKey: this.config.apiKey,
                model: this.config.websocket.defaultModel,
                modalities: ['text', 'audio'],
                audioConfig: {
                    voice: this.config.websocket.defaultSessionConfig.voice,
                    format: 'wav'
                },
                ...options
            });

            this.logger.info('✅ WebSocket model created');
            return this.websocketModel;

        } catch (error) {
            this.logger.error('❌ Failed to create WebSocket model:', error.message);
            throw error;
        }
    }

    /**
     * Enhanced model selection with contextual awareness
     * Now considers continuous context from System 1 (WebSocket) for System 2 (HTTP) decisions
     */
    selectModelType(taskType, context = {}) {
        const {
            requiresTools = false,
            inputType = 'text',
            complexity = 'medium',
            isProactiveDecision = false,
            contextualTriggers = [],
            realtime = false,
            modelType = null
        } = context;

        // CRITICAL FIX: Handle explicit realtime/websocket model requests from dual brain initialization
        if (taskType === 'realtime_processing' || realtime || modelType === 'websocket') {
            this.logger.debug('Selected WebSocket model (System 1) for realtime processing task');
            return 'websocket';
        }

        // Voice input always uses WebSocket (System 1 - Realtime Brain)
        if (inputType === 'voice' || inputType === 'audio') {
            this.logger.debug('Selected WebSocket model (System 1) for voice input');
            // Update context bridge with current audio analysis
            this._updateContextBridge('audio', context);
            return 'websocket';
        }

        // CRITICAL FIX: Handle autonomous_tools task type from dual brain initialization
        if (taskType === 'autonomous_tools' || modelType === 'http') {
            this.logger.debug('Selected HTTP model (System 2) for autonomous tools task');
            return 'http';
        }

        // Proactive contextual decisions use HTTP (System 2 - Thinking Brain)  
        // with context from System 1
        if (isProactiveDecision || contextualTriggers.length > 0) {
            this.logger.debug('Selected HTTP model (System 2) for proactive contextual decision', {
                triggers: contextualTriggers,
                hasMultimodalContext: !!this.contextBridge.multimodalContext
            });
            return 'http';
        }

        // Autonomous tools require HTTP models with function calling (System 2)
        if (requiresTools || this._isAutonomousTask(taskType)) {
            this.logger.debug('Selected HTTP model (System 2) for autonomous tools');
            return 'http';
        }

        // Complex analysis tasks prefer HTTP models (System 2) with System 1 context
        if (complexity === 'high' || this._requiresComplexReasoning(taskType)) {
            this.logger.debug('Selected HTTP model (System 2) for complex reasoning with System 1 context');
            return 'http';
        }

        // Default to HTTP for better function calling support
        this.logger.debug('Selected HTTP model (System 2) as default');
        return 'http';
    }

    /**
     * Select optimal HTTP model based on requirements
     * Uses centralized model constants from AliyunConfig.js
     * @param {Object} requirements - Task requirements
     * @returns {string} Model name
     */
    selectHttpModel(requirements = {}) {
        const {
            complexity = 'medium',
            responseTime = 'fast',
            costSensitive = false
        } = requirements;

        // Cost-sensitive tasks use turbo
        if (costSensitive) {
            this.logger.debug(`Selected ${ALIYUN_MODELS.HTTP_FALLBACK} for cost optimization`);
            return ALIYUN_MODELS.HTTP_FALLBACK;
        }

        // High complexity tasks use max (if response time allows)
        if (complexity === 'high' && responseTime !== 'fast') {
            this.logger.debug(`Selected ${ALIYUN_MODELS.HTTP_PREMIUM} for high complexity`);
            return ALIYUN_MODELS.HTTP_PREMIUM;
        }

        // Balanced approach - qwen-plus for most cases
        this.logger.debug(`Selected ${ALIYUN_MODELS.HTTP_PRIMARY} for balanced performance`);
        return ALIYUN_MODELS.HTTP_PRIMARY;
    }

    /**
     * Override parent getModelForTask with Aliyun-specific model routing
     * @param {string} taskType - Task type
     * @param {Object} context - Task context
     * @returns {Promise<AliyunHttpChatModel|AliyunWebSocketChatModel>} Model instance
     */
    async getModelForTask(taskType, context = {}) {
        const startTime = Date.now();
        this.metrics.totalRequests++;

        try {
            // Use parent's model type selection logic
            const modelType = this.selectModelType(taskType, context);

            // Aliyun-specific routing using existing methods
            switch (modelType) {
                case 'http':
                    this.metrics.httpRequests++;
                    const httpModelName = this.selectHttpModel(context);
                    const httpModel = await this.getHttpModel(httpModelName, context);
                    this.logger.info(`Selected Aliyun HTTP model ${httpModelName} for task: ${taskType}`);
                    return httpModel;

                case 'websocket':
                case 'realtime':
                    this.metrics.websocketRequests++;
                    const wsModel = await this.getWebSocketModel(context);
                    this.logger.info(`Selected Aliyun WebSocket model for task: ${taskType}`);
                    return wsModel;

                default:
                    throw new Error(`Unsupported model type: ${modelType}`);
            }

        } catch (error) {
            this.metrics.errors++;
            this.logger.error(`Aliyun model selection failed for task ${taskType}:`, error.message);
            throw error;

        } finally {
            const responseTime = Date.now() - startTime;
            await this._updateMetrics(responseTime);
        }
    }

    /**
     * Create optimized model for autonomous tools with intelligent routing
     * @param {Array} tools - Tools to bind
     * @param {Object} options - Additional options
     * @returns {Promise<AliyunHttpChatModel>} Model with bound tools
     */
    async createAutonomousModel(tools = [], options = {}) {
        const startTime = Date.now();

        // Intelligent routing for autonomous tools
        const routingContext = {
            taskType: 'autonomous_tools',
            toolCount: tools.length,
            urgency: 'high',
            requiresTools: true,
            performanceTarget: 'sub_600ms',
            ...options
        };

        const router = await this.infrastructureManager.getRouter();
        const modelName = router ? await router.selectOptimalModel(routingContext) : this.config.defaultHttpModel;

        const httpModel = await this.getHttpModel(modelName, {
            timeout: await this._getAdaptiveTimeout(modelName, routingContext),
            temperature: 0.1, // Lower temperature for consistent tool calling
            adaptiveTimeout: true,
            toolCallTimeout: 400, // Specialized timeout for tool calls
            ...options
        });

        if (tools.length > 0) {
            const boundModel = httpModel.bindTools(tools);
            const totalTime = Date.now() - startTime;

            // Track autonomous model performance
            try {
                const performanceTracker = await this.infrastructureManager.getPerformanceTracker();
                if (performanceTracker) {
                    performanceTracker.recordAutonomousModelCreation(modelName, tools.length, totalTime);
                }
            } catch (error) {
                this.logger.warn('Failed to record autonomous model creation:', error.message);
            }

            this.logger.info(`Created autonomous model ${modelName} with ${tools.length} tools (${totalTime}ms)`);
            return boundModel;
        }

        return httpModel;
    }

    /**
     * Execute with fallback strategy
     * @param {Function} taskFunction - Task to execute
     * @param {Array} fallbackChain - Fallback model chain
     * @returns {Promise<any>} Task result
     */
    async executeWithFallback(taskFunction, fallbackChain = null) {
        const chain = fallbackChain || this._getDefaultFallbackChain();

        for (const config of chain) {
            try {
                const model = config.type === 'http' ?
                    await this.getHttpModel(config.model, { timeout: config.timeout }) :
                    await this.getWebSocketModel();

                this.logger.debug(`Attempting task with model: ${config.model} (${config.type})`);
                return await taskFunction(model);

            } catch (error) {
                this.logger.warn(`Model ${config.model} failed:`, error.message);
                continue;
            }
        }

        throw new Error('All models in fallback chain failed');
    }

    /**
     * Get comprehensive factory metrics including routing and caching
     * @returns {Object} Enhanced performance metrics
     */
    async getMetrics() {
        try {
            const router = await this.infrastructureManager.getRouter();
            const cacheManager = await this.infrastructureManager.getCacheManager();
            const circuitBreaker = await this.infrastructureManager.getCircuitBreaker();
            const performanceTracker = await this.infrastructureManager.getPerformanceTracker();

            const routerMetrics = router?.getMetrics() || {};
            const cacheMetrics = cacheManager?.getMetrics() || {};
            const circuitBreakerMetrics = circuitBreaker?.getMetrics() || {};
            const performanceMetrics = performanceTracker?.getMetrics() || {};

            return {
                // Legacy metrics
                ...this.metrics,

                // Cache metrics
                cache: {
                    ...cacheMetrics,
                    legacyHttp: this.httpModelCache.size,
                    legacyWebsocket: this.websocketModel ? 1 : 0
                },

                // Routing metrics
                routing: routerMetrics,

                // Circuit breaker metrics
                circuitBreaker: circuitBreakerMetrics,

                // Performance tracking
                performance: performanceMetrics,

                // Model selection ratios
                modelSelection: {
                    httpRatio: this.metrics.httpRequests / Math.max(this.metrics.totalRequests, 1),
                    websocketRatio: this.metrics.websocketRequests / Math.max(this.metrics.totalRequests, 1),
                    routingDecisionRatio: this.metrics.routingDecisions / Math.max(this.metrics.totalRequests, 1)
                },

                // System health indicators
                health: {
                    averageResponseTime: performanceMetrics.averageResponseTime,
                    errorRate: this.metrics.errors / Math.max(this.metrics.totalRequests, 1),
                    cacheHitRate: this.metrics.cacheHits / Math.max(this.metrics.totalRequests, 1),
                    circuitBreakerState: circuitBreakerMetrics.state
                }
            };
        } catch (error) {
            this.logger.warn('Failed to get enhanced metrics, returning basic metrics:', error.message);
            return { ...this.metrics };
        }
    }

    /**
     * Clear model cache with proper resource cleanup
     */
    clearCache() {
        // Clear caches
        this.httpModelCache.clear();
        this.websocketModel = null;
        this._temporaryModelRefs = new WeakMap();

        // Run cleanup tasks
        this._cleanupTasks.forEach(task => {
            try {
                task();
            } catch (error) {
                this.logger.warn('Cleanup task failed:', error.message);
            }
        });
        this._cleanupTasks.clear();

        this.logger.info('Model cache cleared with resource cleanup');
    }

    /**
     * Shutdown factory and cleanup resources with memory leak prevention
     */
    async shutdown() {
        try {
            // Close WebSocket connections
            if (this.websocketModel) {
                this.websocketModel.closeRealtimeMode();
            }

            // Clear all references to prevent memory leaks
            this.clearCache();

            // Cleanup infrastructure services
            if (this.infrastructureManager) {
                try {
                    const cacheManager = await this.infrastructureManager.getCacheManager();
                    if (cacheManager && typeof cacheManager.clear === 'function') {
                        await cacheManager.clear();
                    }
                } catch (error) {
                    this.logger.warn('Failed to clear infrastructure cache:', error.message);
                }
            }

            // Clear all references
            this.httpModelCache = null;
            this.websocketModel = null;
            this._temporaryModelRefs = null;
            this.infrastructureManager = null;

            this.logger.info('AliyunModelFactory shutdown complete');

        } catch (error) {
            this.logger.error('Error during factory shutdown:', error.message);
        }
    }

    // Private helper methods

    /**
     * Check if task is autonomous
     * @private
     */
    _isAutonomousTask(taskType) {
        const autonomousTasks = [
            'analyze_conversation_context',
            'decide_communication_mode',
            'track_conversation_topics',
            'manage_conversation_memory',
            'autonomous_analysis',
            'decision_making',
            'context_analysis'
        ];

        return autonomousTasks.includes(taskType) ||
            taskType.includes('autonomous') ||
            taskType.includes('analyze') ||
            taskType.includes('decide');
    }

    /**
     * Check if task requires complex reasoning
     * @private
     */
    _requiresComplexReasoning(taskType) {
        const complexTasks = [
            'complex_analysis',
            'strategic_planning',
            'multi_step_reasoning',
            'context_synthesis'
        ];

        return complexTasks.includes(taskType) ||
            taskType.includes('complex') ||
            taskType.includes('strategic');
    }

    /**
     * Get default fallback chain using centralized model constants
     * @private
     */
    _getDefaultFallbackChain() {
        return [
            { model: ALIYUN_MODELS.HTTP_PRIMARY, type: 'http', timeout: 500 },
            { model: ALIYUN_MODELS.HTTP_FALLBACK, type: 'http', timeout: 300 },
            { model: ALIYUN_MODELS.DEFAULT_REALTIME, type: 'websocket', timeout: 1000 }
        ];
    }

    /**
     * Update performance metrics
     * @private
     */
    async _updateMetrics(responseTime) {
        const currentAvg = this.metrics.averageResponseTime;
        const totalRequests = this.metrics.totalRequests;

        this.metrics.averageResponseTime =
            ((currentAvg * (totalRequests - 1)) + responseTime) / totalRequests;

        // Update performance tracker using infrastructure manager
        try {
            const performanceTracker = await this.infrastructureManager.getPerformanceTracker();
            if (performanceTracker && typeof performanceTracker.recordResponseTime === 'function') {
                performanceTracker.recordResponseTime(responseTime);
            }
        } catch (error) {
            this.logger.warn('Failed to record response time in performance tracker:', error.message);
        }
    }

    /**
     * Create model configurations for the intelligent router
     * Uses AliyunConfig.js as single source of truth for model definitions
     * @protected
     * @returns {Object} Model configurations mapping model names to their configs
     */
    _createModelConfigs() {
        // Use centralized configuration from AliyunConfig.js
        const httpConfig = ALIYUN_HTTP_CONFIG;
        const websocketConfig = ALIYUN_WEBSOCKET_CONFIG;

        return {
            // HTTP Models - using ALIYUN_MODELS constants
            [ALIYUN_MODELS.HTTP_PRIMARY]: {
                type: 'http',
                capabilities: {
                    functionCalling: true,
                    autonomousTools: true,
                    streaming: true,
                    multimodal: false,
                    realtime: false
                },
                performance: {
                    expectedResponseTime: 400,
                    maxResponseTime: httpConfig.timeout,
                    costEfficiency: 0.8,
                    reliability: 0.9
                },
                limits: {
                    maxTokens: httpConfig.defaultParameters.maxTokens,
                    contextWindow: 32768,
                    rateLimitPerSecond: httpConfig.rateLimiting.requestsPerSecond
                },
                priority: 1
            },
            [ALIYUN_MODELS.HTTP_FALLBACK]: {
                type: 'http',
                capabilities: {
                    functionCalling: true,
                    autonomousTools: true,
                    streaming: true,
                    multimodal: false,
                    realtime: false
                },
                performance: {
                    expectedResponseTime: 300,
                    maxResponseTime: httpConfig.timeout * 0.6,
                    costEfficiency: 0.9,
                    reliability: 0.8
                },
                limits: {
                    maxTokens: httpConfig.defaultParameters.maxTokens,
                    contextWindow: 32768,
                    rateLimitPerSecond: httpConfig.rateLimiting.requestsPerSecond * 1.5
                },
                priority: 2
            },
            [ALIYUN_MODELS.HTTP_PREMIUM]: {
                type: 'http',
                capabilities: {
                    functionCalling: true,
                    autonomousTools: true,
                    streaming: true,
                    multimodal: false,
                    realtime: false
                },
                performance: {
                    expectedResponseTime: 600,
                    maxResponseTime: httpConfig.timeout * 1.6,
                    costEfficiency: 0.6,
                    reliability: 0.95
                },
                limits: {
                    maxTokens: httpConfig.defaultParameters.maxTokens,
                    contextWindow: 32768,
                    rateLimitPerSecond: httpConfig.rateLimiting.requestsPerSecond * 0.5
                },
                priority: 3
            },
            // WebSocket Models - using ALIYUN_MODELS constants
            [ALIYUN_MODELS.DEFAULT_REALTIME]: {
                type: 'websocket',
                capabilities: {
                    functionCalling: false,
                    streaming: true,
                    multimodal: true,
                    realtime: true,
                    voiceInput: true,
                    voiceOutput: true
                },
                performance: {
                    expectedResponseTime: 200,
                    maxResponseTime: 2000,
                    costEfficiency: 0.7,
                    reliability: 0.85
                },
                limits: {
                    maxTokens: 4096,
                    contextWindow: 16384,
                    rateLimitPerSecond: 20
                },
                priority: 1
            },
            [ALIYUN_MODELS.QWEN_OMNI_TURBO]: {
                type: 'websocket',
                capabilities: {
                    functionCalling: false,
                    streaming: true,
                    multimodal: true,
                    realtime: true,
                    voiceInput: true,
                    voiceOutput: true
                },
                performance: {
                    expectedResponseTime: 250,
                    maxResponseTime: 2500,
                    costEfficiency: 0.8,
                    reliability: 0.8
                },
                limits: {
                    maxTokens: 4096,
                    contextWindow: 16384,
                    rateLimitPerSecond: 18
                },
                priority: 2
            },
            [ALIYUN_MODELS.TRANSCRIPTION]: {
                type: 'websocket',
                capabilities: {
                    functionCalling: false,
                    streaming: true,
                    multimodal: false,
                    realtime: true,
                    voiceInput: true,
                    voiceOutput: false,
                    transcription: true
                },
                performance: {
                    expectedResponseTime: 150,
                    maxResponseTime: 1500,
                    costEfficiency: 0.9,
                    reliability: 0.9
                },
                limits: {
                    maxTokens: 2048,
                    contextWindow: 8192,
                    rateLimitPerSecond: 25
                },
                priority: 1
            }
        };
    }

    /**
     * Select optimal HTTP model using intelligent routing
     * @private
     */
    async _selectOptimalHttpModel(options = {}) {
        const routingContext = {
            taskType: options.taskType || 'general',
            complexity: options.complexity || 'medium',
            urgency: options.urgency || 'medium',
            costSensitive: options.costSensitive || false,
            requiresTools: options.requiresTools || false,
            expectedTokens: options.maxTokens || 2000,
            historicalPerformance: true
        };

        this.metrics.routingDecisions++;
        const router = await this.infrastructureManager.getRouter();
        return router ? await router.selectOptimalModel(routingContext) : this.config.defaultHttpModel;
    }

    /**
     * Get adaptive timeout based on model and context
     * @private
     */
    async _getAdaptiveTimeout(modelName, context = {}) {
        const baseTimeout = this.config.http.timeout;
        const router = await this.infrastructureManager.getRouter();
        const modelConfig = router ? router.getModelConfig(modelName) : null;

        if (!modelConfig) {
            return baseTimeout;
        }

        let timeout = modelConfig.performance.expectedResponseTime;

        // Adjust for urgency
        if (context.urgency === 'high') {
            timeout = Math.min(timeout, baseTimeout);
        } else if (context.urgency === 'low') {
            timeout = Math.min(timeout * 1.2, modelConfig.performance.maxResponseTime);
        }

        // Adjust for tool calls
        if (context.requiresTools || context.toolCallTimeout) {
            timeout = Math.min(timeout, context.toolCallTimeout || 400);
        }

        // Apply historical performance adjustments
        const performanceTracker = await this.infrastructureManager.getPerformanceTracker();
        const historicalPerformance = performanceTracker ? performanceTracker.getModelPerformance(modelName) : null;
        if (historicalPerformance && historicalPerformance.averageResponseTime > 0) {
            const performanceFactor = Math.min(historicalPerformance.averageResponseTime / timeout, 1.5);
            timeout = Math.floor(timeout * performanceFactor);
        }

        return Math.max(timeout, 200); // Minimum 200ms
    }

    /**
     * Perform enhanced health check with performance tracking
     * @private
     */
    async _performEnhancedHealthCheck(model, modelName) {
        const startTime = Date.now();

        try {
            // Basic health check
            const healthCheck = await model.healthCheck();
            const responseTime = Date.now() - startTime;

            if (healthCheck.healthy) {
                // Extended health validation
                const extendedCheck = await this._validateModelCapabilities(model, modelName);

                return {
                    healthy: extendedCheck.capable,
                    responseTime,
                    capabilities: extendedCheck.capabilities,
                    error: extendedCheck.error
                };
            }

            return {
                healthy: false,
                responseTime,
                error: healthCheck.error
            };

        } catch (error) {
            return {
                healthy: false,
                responseTime: Date.now() - startTime,
                error: error.message
            };
        }
    }

    /**
     * Validate model capabilities
     * @private
     */
    async _validateModelCapabilities(model, modelName) {
        const router = await this.infrastructureManager.getRouter();
        const modelConfig = router ? router.getModelConfig(modelName) : null;

        if (!modelConfig) {
            return { capable: true, capabilities: [] };
        }

        try {
            const capabilities = [];

            // Test basic text generation
            capabilities.push('text_generation');

            // Test function calling if supported
            if (modelConfig.capabilities.functionCalling ||
                modelConfig.capabilities.autonomousTools) {
                // Quick capability test - this would be more sophisticated in production
                if (typeof model.bindTools === 'function') {
                    capabilities.push('function_calling');
                }
            }

            return {
                capable: true,
                capabilities
            };

        } catch (error) {
            return {
                capable: false,
                capabilities: [],
                error: error.message
            };
        }
    }

    /**
     * Handle circuit breaker fallback
     * @private
     */
    async _handleCircuitBreakerFallback(failedModel, options = {}) {
        this.metrics.circuitBreakerTrips++;

        const router = await this.infrastructureManager.getRouter();
        const circuitBreaker = await this.infrastructureManager.getCircuitBreaker();
        const fallbackChain = router ? await router.getFallbackChain(failedModel, options) : [];

        for (const fallbackModel of fallbackChain) {
            if (!circuitBreaker || !circuitBreaker.isOpen(fallbackModel)) {
                this.logger.info(`Circuit breaker fallback: ${failedModel} → ${fallbackModel}`);
                return this.getHttpModel(fallbackModel, options);
            }
        }

        throw new Error(`All models in fallback chain are unavailable due to circuit breaker`);
    }

    /**
     * Update context bridge with multimodal information from System 1
     * This allows System 2 to make informed decisions based on real-time context
     */
    _updateContextBridge(type, context) {
        const now = Date.now();

        switch (type) {
            case 'audio':
                this.contextBridge.audioAnalysis = {
                    volume: context.audioVolume,
                    quality: context.audioQuality,
                    sentiment: context.audioSentiment,
                    vadState: context.vadState,
                    timestamp: now
                };
                break;

            case 'visual':
                this.contextBridge.visualContext = {
                    engagement: context.visualEngagement,
                    attention: context.visualAttention,
                    posture: context.posture,
                    timestamp: now
                };
                break;

            case 'multimodal':
                this.contextBridge.multimodalContext = {
                    ...context.multimodalAnalysis,
                    timestamp: now
                };
                break;
        }

        this.contextBridge.lastUpdate = now;

        // Notify listeners of context updates
        if (this.onContextUpdate) {
            this.onContextUpdate(this.contextBridge);
        }
    }

    /**
     * Get enriched context for System 2 HTTP model
     * Includes multimodal context from System 1 WebSocket model
     */
    getEnrichedContextForThinkingBrain() {
        return {
            multimodalContext: this.contextBridge.multimodalContext,
            audioAnalysis: this.contextBridge.audioAnalysis,
            visualContext: this.contextBridge.visualContext,
            engagementState: this.contextBridge.engagementState,
            contextAge: this.contextBridge.lastUpdate ? Date.now() - this.contextBridge.lastUpdate : null,
            hasRealtimeContext: !!this.contextBridge.lastUpdate
        };
    }

    /**
     * Set context update callback for real-time context sharing
     */
    setContextUpdateCallback(callback) {
        this.onContextUpdate = callback;
    }

    /**
     * Cleanup and shutdown enhanced factory components
     */
    async shutdown() {
        try {
            // Close WebSocket connections
            if (this.websocketModel) {
                this.websocketModel.closeRealtimeMode();
            }

            // Cleanup intelligent components
            try {
                const cacheManager = await this.infrastructureManager.getCacheManager();
                const circuitBreaker = await this.infrastructureManager.getCircuitBreaker();
                const performanceTracker = await this.infrastructureManager.getPerformanceTracker();
                const router = await this.infrastructureManager.getRouter();

                if (cacheManager) await cacheManager.clear();
                if (circuitBreaker) circuitBreaker.reset();
                if (performanceTracker) performanceTracker.reset();
                if (router) router.reset();
            } catch (error) {
                this.logger.warn('Failed to cleanup infrastructure components:', error.message);
            }

            this.clearCache();
            this.logger.info('Enhanced AliyunModelFactory shutdown complete');

        } catch (error) {
            this.logger.error('Error during enhanced factory shutdown:', error.message);
        }
    }
}

export default AliyunModelFactory;