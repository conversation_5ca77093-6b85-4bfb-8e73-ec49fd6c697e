/**
 * <PERSON>yun Session Coordinator
 * 
 * Extends BaseSessionCoordinator to provide Aliyun-specific session management
 * for WebSocket connections, realtime sessions, and provider-specific optimizations.
 * 
 * Moved from DualBrainCoordinator.js to provide proper separation of concerns
 * and follow the established pattern in BaseSessionCoordinator.js
 */

import { BaseSessionCoordinator } from '../base/BaseSessionCoordinator.js';
import { createLogger, LogLevel } from '../../../utils/logger.js';

const logger = createLogger('AliyunSessionCoordinator', LogLevel.DEBUG);

export class AliyunSessionCoordinator extends BaseSessionCoordinator {
  constructor(options = {}) {
    super(options);

    this.logger = logger;

    // Aliyun-specific options
    this.aliyunOptions = {
      maxConcurrentConnections: options.maxConcurrentConnections || 1,
      connectionCooldown: options.connectionCooldown || 2000,
      connectionTimeout: options.connectionTimeout || 5000,
      realtimeTimeout: options.realtimeTimeout || 3000,
      maxRetries: options.maxRetries || 3,
      retryBackoffMs: options.retryBackoffMs || 1000,
      ...options.aliyunOptions
    };

    // Connection management state (moved from DualBrainCoordinator)
    this._activeConnections = new Set();
    this._lastConnectionAttempt = 0;
    this._globalConnectionLock = false;
    this._connectionStats = {
      totalAttempts: 0,
      successfulConnections: 0,
      failedConnections: 0,
      lastSuccessfulConnection: null,
      lastFailedConnection: null
    };

    // Circuit breaker for failed connections
    this.circuitBreaker = {
      failures: 0,
      maxFailures: options.maxFailures || 3,
      resetTimeMs: options.resetTimeMs || 30000,
      lastReset: Date.now(),
      isOpen: false
    };

    this.logger.info('🔄 Aliyun Session Coordinator initialized', {
      maxConcurrentConnections: this.aliyunOptions.maxConcurrentConnections,
      connectionTimeout: this.aliyunOptions.connectionTimeout,
      realtimeTimeout: this.aliyunOptions.realtimeTimeout
    });
  }

  /**
   * Aliyun-specific session creation with WebSocket optimization
   * @override
   */
  async createSession(sessionConfig = {}) {
    try {
      this.logger.info('🚀 Creating Aliyun session with WebSocket optimization...');

      // Check circuit breaker first
      if (this._isCircuitBreakerOpen()) {
        throw new Error('Circuit breaker is open - too many recent failures');
      }

      // Ensure only one session creation at a time
      if (this._globalConnectionLock) {
        throw new Error('Another session creation is in progress');
      }

      // Apply connection rate limiting
      await this._applyConnectionRateLimit();

      this._globalConnectionLock = true;
      const connectionId = `aliyun_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;

      try {
        // Track this connection attempt
        this._activeConnections.add(connectionId);
        this._connectionStats.totalAttempts++;

        // Build Aliyun-specific session config
        const aliyunSessionConfig = this._buildAliyunSessionConfig(sessionConfig);

        // Create session using base implementation
        const session = await super.createSession(aliyunSessionConfig);

        // Aliyun-specific post-creation setup
        await this._setupAliyunSession(session, connectionId);

        // Update success stats
        this._connectionStats.successfulConnections++;
        this._connectionStats.lastSuccessfulConnection = Date.now();
        this._resetCircuitBreaker();

        this.logger.info('✅ Aliyun session created successfully', {
          sessionId: session.id,
          connectionId,
          totalAttempts: this._connectionStats.totalAttempts,
          successRate: this._getSuccessRate()
        });

        return session;

      } finally {
        // Always clean up connection tracking
        this._activeConnections.delete(connectionId);
        this._globalConnectionLock = false;
        this._lastConnectionAttempt = Date.now();
      }

    } catch (error) {
      // Update failure stats
      this._connectionStats.failedConnections++;
      this._connectionStats.lastFailedConnection = Date.now();
      this._recordCircuitBreakerFailure();

      this.logger.error('❌ Aliyun session creation failed:', {
        error: error.message,
        totalAttempts: this._connectionStats.totalAttempts,
        successRate: this._getSuccessRate(),
        circuitBreakerOpen: this._isCircuitBreakerOpen()
      });

      throw error;
    }
  }

  /**
   * Ensure WebSocket readiness with Aliyun-specific optimizations
   * Moved from DualBrainCoordinator._ensureWebSocketReadiness()
   */
  async ensureWebSocketReadiness(model, options = {}) {
    const modelName = model.constructor.name;
    const connectionId = `${modelName}_${Date.now()}`;

    try {
      this.logger.debug('🔍 Checking Aliyun WebSocket readiness...', {
        modelName,
        connectionId,
        isAliyunModel: modelName.includes('Aliyun')
      });

      // Check if this is an Aliyun WebSocket model
      if (!modelName.includes('WebSocket') || !modelName.includes('Aliyun')) {
        this.logger.debug(`Model ${modelName} is not Aliyun WebSocket-based, skipping specialized readiness check`);
        return { ready: true, model, skipAliyunOptimizations: true };
      }

      // Global connection lock check
      if (this._globalConnectionLock) {
        this.logger.warn('🔒 Global connection lock active - another connection in progress', {
          connectionId,
          activeConnections: this._activeConnections.size
        });
        return { ready: false, model, error: 'Global connection lock active' };
      }

      // Connection limit check
      if (this._activeConnections.size >= this.aliyunOptions.maxConcurrentConnections) {
        this.logger.warn('🚫 Maximum concurrent connections reached', {
          activeConnections: this._activeConnections.size,
          maxAllowed: this.aliyunOptions.maxConcurrentConnections
        });
        return { ready: false, model, error: 'Maximum concurrent connections reached' };
      }

      // Connection cooldown check
      const now = Date.now();
      if (now - this._lastConnectionAttempt < this.aliyunOptions.connectionCooldown) {
        const waitTime = this.aliyunOptions.connectionCooldown - (now - this._lastConnectionAttempt);
        this.logger.warn('🕐 Connection in cooldown period', {
          remainingTime: waitTime,
          lastAttempt: this._lastConnectionAttempt
        });
        return { ready: false, model, error: `Connection cooldown: ${waitTime}ms remaining` };
      }

      // Check WebSocket connection status
      if (typeof model.isConnected === 'function' && !model.isConnected()) {
        this.logger.info('🔄 WebSocket not connected, attempting Aliyun connection...', { connectionId });

        this._globalConnectionLock = true;
        this._activeConnections.add(connectionId);

        try {
          const connected = await this._attemptAliyunWebSocketConnection(model, options);
          if (!connected) {
            return { ready: false, model, error: 'Aliyun WebSocket connection failed' };
          }
        } finally {
          this._activeConnections.delete(connectionId);
          this._globalConnectionLock = false;
        }
      }

      // Check realtime mode readiness for Aliyun models
      if (typeof model.isRealtimeModeActive === 'function') {
        const isRealtimeActive = model.isRealtimeModeActive();
        if (!isRealtimeActive) {
          this.logger.info('⏳ Waiting for Aliyun realtime mode activation...');
          const ready = await this._waitForAliyunRealtimeReadiness(model, options);
          if (!ready) {
            return { ready: false, model, error: 'Aliyun realtime mode not ready' };
          }
        }
      }

      this.logger.info('✅ Aliyun WebSocket ready for usage', { connectionId, modelName });
      return { ready: true, model };

    } catch (error) {
      this.logger.warn('⚠️ Aliyun WebSocket readiness check failed:', error);
      return { ready: false, model, error: `Readiness check error: ${error.message}` };
    }
  }

  /**
   * Attempt Aliyun WebSocket connection with provider-specific optimizations
   * Moved from DualBrainCoordinator._attemptWebSocketConnection()
   * @private
   */
  async _attemptAliyunWebSocketConnection(model, options = {}) {
    const startTime = Date.now();
    const connectionTimeout = options.connectionTimeout || this.aliyunOptions.connectionTimeout;

    try {
      this.logger.debug('🔄 Attempting Aliyun WebSocket connection...', {
        timeout: connectionTimeout,
        modelType: model.constructor.name,
        aliyunOptimizations: true
      });

      // Aliyun-specific connection preparation
      if (typeof model.prepareConnection === 'function') {
        await model.prepareConnection();
      }

      // Race connection attempt against timeout
      const connected = await Promise.race([
        model.connect(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error(`Aliyun connection timeout after ${connectionTimeout}ms`)), connectionTimeout)
        )
      ]);

      const connectTime = Date.now() - startTime;

      if (connected) {
        this.logger.debug('✅ Aliyun WebSocket connection successful', {
          connectTimeMs: connectTime,
          modelType: model.constructor.name
        });

        // Aliyun-specific post-connection setup
        await this._postConnectionSetup(model);
        return true;
      } else {
        this.logger.warn('❌ Aliyun WebSocket connection failed - no connection returned', {
          connectTimeMs: connectTime
        });
        return false;
      }

    } catch (error) {
      const connectTime = Date.now() - startTime;
      this.logger.warn('❌ Aliyun WebSocket connection attempt failed', {
        error: error.message,
        connectTimeMs: connectTime,
        isTimeout: error.message.includes('timeout')
      });
      return false;
    }
  }

  /**
   * Wait for Aliyun realtime readiness with provider-specific optimizations
   * Moved from DualBrainCoordinator._waitForRealtimeReadiness()
   * @private
   */
  async _waitForAliyunRealtimeReadiness(model, options = {}) {
    const timeout = options.realtimeTimeout || this.aliyunOptions.realtimeTimeout;

    // Try Aliyun-specific readiness method first
    if (typeof model.waitForAliyunReady === 'function') {
      try {
        return await model.waitForAliyunReady(timeout);
      } catch (error) {
        this.logger.debug('waitForAliyunReady failed, falling back to standard method:', error.message);
      }
    }

    // Standard realtime readiness check
    if (typeof model.waitForRealtimeReady === 'function') {
      try {
        return await model.waitForRealtimeReady(timeout);
      } catch (error) {
        this.logger.debug('waitForRealtimeReady failed:', error.message);
      }
    }

    // Optimized polling with Aliyun-specific checks
    const startTime = Date.now();
    let checkCount = 0;

    while (Date.now() - startTime < timeout) {
      checkCount++;

      // Check both standard and Aliyun-specific readiness indicators
      const isStandardReady = typeof model.isRealtimeModeActive === 'function' && model.isRealtimeModeActive();
      const isAliyunReady = typeof model.isAliyunReady === 'function' ? model.isAliyunReady() : true;

      if (isStandardReady && isAliyunReady) {
        this.logger.debug('✅ Aliyun realtime mode became active', {
          elapsedMs: Date.now() - startTime,
          checks: checkCount
        });
        return true;
      }

      // Progressive backoff optimized for Aliyun
      const waitTime = checkCount < 5 ? 100 : checkCount < 15 ? 200 : 300;
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    this.logger.debug('⏰ Aliyun realtime readiness timeout', {
      timeoutMs: timeout,
      checks: checkCount
    });
    return false;
  }

  /**
   * Build Aliyun-specific session configuration
   * @private
   */
  _buildAliyunSessionConfig(baseConfig) {
    return {
      ...baseConfig,
      provider: 'aliyun',
      apiVersion: baseConfig.apiVersion || 'v1',
      region: baseConfig.region || 'cn-hangzhou',
      // Aliyun-specific WebSocket settings
      websocket: {
        enableCompression: true,
        maxMessageSize: 1024 * 1024, // 1MB
        heartbeatInterval: 30000, // 30 seconds
        reconnectAttempts: this.aliyunOptions.maxRetries,
        reconnectDelay: this.aliyunOptions.retryBackoffMs,
        ...baseConfig.websocket
      },
      // Aliyun realtime session settings - System 1 Context for fast response
      realtime: {
        enableVAD: baseConfig.enableVAD !== false,
        audioFormat: baseConfig.audioFormat || 'pcm16',
        sampleRate: baseConfig.sampleRate || 16000,
        channels: baseConfig.channels || 1,
        // 🔥 CRITICAL FIX: Primary output should be TEXT only for System 2
        modalities: baseConfig.forSystem2 ? ['text'] : ['text', 'audio'],
        inputModalities: ['text', 'audio'], // System 1 can receive both
        outputModalities: baseConfig.forSystem2 ? ['text'] : ['text', 'audio'], // System 2 only outputs text
        ...baseConfig.realtime
      }
    };
  }

  /**
   * Setup Aliyun-specific session features
   * @private
   */
  async _setupAliyunSession(session, connectionId) {
    try {
      // Register Aliyun-specific event handlers
      this._registerAliyunEventHandlers(session);

      // Setup Aliyun monitoring and health checks
      this._setupAliyunMonitoring(session);

      // Configure Aliyun-specific features
      await this._configureAliyunFeatures(session);

      this.logger.debug('🔧 Aliyun session setup completed', {
        sessionId: session.id,
        connectionId
      });

    } catch (error) {
      this.logger.warn('⚠️ Aliyun session setup failed:', error);
      // Non-fatal - session can still function
    }
  }

  /**
   * Apply connection rate limiting
   * @private
   */
  async _applyConnectionRateLimit() {
    const now = Date.now();
    const timeSinceLastAttempt = now - this._lastConnectionAttempt;

    if (timeSinceLastAttempt < this.aliyunOptions.connectionCooldown) {
      const waitTime = this.aliyunOptions.connectionCooldown - timeSinceLastAttempt;
      this.logger.debug('⏳ Applying connection rate limit', { waitTimeMs: waitTime });
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }

  /**
   * Post-connection setup for Aliyun models
   * @private
   */
  async _postConnectionSetup(model) {
    try {
      // Aliyun-specific connection validation
      if (typeof model.validateConnection === 'function') {
        await model.validateConnection();
      }

      // Setup Aliyun-specific features
      if (typeof model.enableAliyunFeatures === 'function') {
        await model.enableAliyunFeatures();
      }

    } catch (error) {
      this.logger.debug('Post-connection setup failed (non-fatal):', error);
    }
  }

  /**
   * Register Aliyun-specific event handlers
   * @private
   */
  _registerAliyunEventHandlers(session) {
    // Aliyun WebSocket specific events
    session.on('aliyun:connection_quality', (data) => {
      this.logger.debug('📊 Aliyun connection quality update', data);
    });

    session.on('aliyun:token_usage', (data) => {
      this.logger.debug('💰 Aliyun token usage update', data);
    });

    session.on('aliyun:rate_limit', (data) => {
      this.logger.warn('⚠️ Aliyun rate limit warning', data);
    });
  }

  /**
   * Setup Aliyun-specific monitoring
   * @private
   */
  _setupAliyunMonitoring(session) {
    // Connection health monitoring
    const healthCheck = setInterval(() => {
      if (session.isActive() && typeof session.checkHealth === 'function') {
        session.checkHealth().catch(error => {
          this.logger.warn('Aliyun session health check failed:', error);
        });
      }
    }, 60000); // Every minute

    session.on('terminated', () => {
      clearInterval(healthCheck);
    });
  }

  /**
   * Configure Aliyun-specific features
   * @private
   */
  async _configureAliyunFeatures(session) {
    // Enable Aliyun-specific optimizations
    if (typeof session.enableAliyunOptimizations === 'function') {
      await session.enableAliyunOptimizations();
    }

    // Configure Aliyun billing tracking
    if (typeof session.enableBillingTracking === 'function') {
      await session.enableBillingTracking();
    }
  }

  /**
   * Circuit breaker implementation
   * @private
   */
  _isCircuitBreakerOpen() {
    const now = Date.now();

    // Reset circuit breaker if enough time has passed
    if (now - this.circuitBreaker.lastReset > this.circuitBreaker.resetTimeMs) {
      this.circuitBreaker.failures = 0;
      this.circuitBreaker.lastReset = now;
      this.circuitBreaker.isOpen = false;
      return false;
    }

    const isOpen = this.circuitBreaker.failures >= this.circuitBreaker.maxFailures;
    this.circuitBreaker.isOpen = isOpen;
    return isOpen;
  }

  /**
   * Record circuit breaker failure
   * @private
   */
  _recordCircuitBreakerFailure() {
    this.circuitBreaker.failures++;
    this.logger.debug('📈 Circuit breaker failure recorded', {
      failures: this.circuitBreaker.failures,
      maxFailures: this.circuitBreaker.maxFailures,
      isOpen: this._isCircuitBreakerOpen()
    });
  }

  /**
   * Reset circuit breaker on success
   * @private
   */
  _resetCircuitBreaker() {
    if (this.circuitBreaker.failures > 0) {
      this.logger.debug('🔄 Circuit breaker reset on successful connection');
      this.circuitBreaker.failures = 0;
      this.circuitBreaker.isOpen = false;
    }
  }

  /**
   * Get connection success rate
   * @private
   */
  _getSuccessRate() {
    if (this._connectionStats.totalAttempts === 0) return 100;
    return Math.round((this._connectionStats.successfulConnections / this._connectionStats.totalAttempts) * 100);
  }

  /**
   * Get Aliyun session statistics
   */
  getAliyunStats() {
    return {
      ...super.getSessionInfo(),
      aliyunSpecific: {
        activeConnections: this._activeConnections.size,
        maxConcurrentConnections: this.aliyunOptions.maxConcurrentConnections,
        connectionStats: this._connectionStats,
        circuitBreaker: {
          failures: this.circuitBreaker.failures,
          isOpen: this.circuitBreaker.isOpen,
          maxFailures: this.circuitBreaker.maxFailures
        },
        globalConnectionLock: this._globalConnectionLock,
        successRate: this._getSuccessRate()
      }
    };
  }

  /**
   * Cleanup Aliyun-specific resources
   * @override
   */
  async dispose() {
    try {
      // Clear connection tracking
      this._activeConnections.clear();
      this._globalConnectionLock = false;

      // Reset stats
      this._connectionStats = {
        totalAttempts: 0,
        successfulConnections: 0,
        failedConnections: 0,
        lastSuccessfulConnection: null,
        lastFailedConnection: null
      };

      // Call parent cleanup
      await super.dispose();

      this.logger.info('🗑️ Aliyun Session Coordinator disposed');

    } catch (error) {
      this.logger.error('❌ Error disposing Aliyun Session Coordinator:', error);
    }
  }
}

export default AliyunSessionCoordinator;