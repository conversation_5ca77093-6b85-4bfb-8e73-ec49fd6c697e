/**
 * Model Factory
 * Unifies all model provider initialization logic eliminating duplication
 * Merges functionality from models/index.js, base/ModelFactory.js, and aliyun/AliyunModelFactory.js
 * 
 * Features:
 * - Universal provider support (Aliyun, vLLM, OpenAI)
 * - Intelligent model selection and routing
 * - Enterprise-grade reliability (circuit breakers, caching, fallbacks)
 * - Performance optimization and comprehensive metrics
 * - Infrastructure service integration
 */

import { createLogger, LogLevel } from '@/utils/logger';
import { VLLMChatModel } from './VLLMChatModel.js';
import { AliyunHttpChatModel } from './aliyun/AliyunHttpChatModel.js';
import { AliyunWebSocketChatModel } from './aliyun/AliyunWebSocketChatModel.js';
import { endpoints } from '../../config/endpoints.ts';
import { getDownloadServerUrl } from '../../utils/portManager.js';
import { getEnvVar } from '../../config/env.ts';
import {
    createHybridConfig,
    ALIYUN_HTTP_CONFIG,
    ALIYUN_WEBSOCKET_CONFIG,
    validateConfig,
    ALIYUN_MODELS,
    getHttpEndpoint
} from './aliyun/AliyunConfig.js';

/**
 * Model capability definitions
 */
export const ModelCapabilities = {
    TEXT_GENERATION: 'text_generation',
    FUNCTION_CALLING: 'function_calling',
    AUTONOMOUS_TOOLS: 'autonomous_tools',
    VOICE_PROCESSING: 'voice_processing',
    REAL_TIME: 'real_time',
    AUDIO_GENERATION: 'audio_generation',
    STREAMING: 'streaming',
    COMPLEX_REASONING: 'complex_reasoning',
    FAST_RESPONSE: 'fast_response',
    COST_EFFECTIVE: 'cost_effective'
};

/**
 * Model types for routing decisions
 */
export const ModelType = {
    HTTP: 'http',
    WEBSOCKET: 'websocket',
    REALTIME: 'realtime'
};

/**
 * Universal base model configuration
 */
export const createBaseModelConfig = (provider = 'universal') => ({
    // Provider identification
    provider,

    // Authentication
    apiKey: '',

    // Connection
    httpEndpoint: '',
    websocketEndpoint: '',

    // Model configuration
    defaultHttpModel: '',
    defaultWebSocketModel: '',
    defaultRealtimeModel: '',

    // Performance configuration
    timeout: 500,
    temperature: 0.7,
    maxTokens: 2000,
    topP: 0.8,

    // Tool calling configuration
    toolChoice: 'auto',

    // Retry and reliability configuration
    maxRetries: 2,
    retryDelay: 100,

    // Audio configuration (for realtime models)
    audioConfig: {
        sampleRate: 16000,
        bitDepth: 16,
        channels: 1,
        format: 'pcm16'
    },

    // Rate limiting
    rateLimiting: {
        enabled: true,
        maxChunksPerSecond: 5,
        minIntervalMs: 200
    },

    // Connection management
    connectionTimeout: 10000,
    maxReconnectAttempts: 3,
    reconnectBackoffMs: 1000,

    // Caching configuration
    cache: {
        modelInstanceTTL: 300000, // 5 minutes
        routingDecisionTTL: 60000, // 1 minute
        maxCacheSize: 100
    },

    // Circuit breaker configuration
    circuitBreaker: {
        failureThreshold: 5,
        recoveryTimeout: 30000,
        halfOpenRetries: 3
    },

    // Performance tracking
    performance: {
        maxResponseTime: 600,
        optimalResponseTime: 400,
        maxErrorRate: 0.05,
        historySize: 1000,
        metricsWindow: 300000 // 5 minutes
    },

    // Debugging
    enableDebugLogging: false
});

/**
 * Model Factory
 * Manages all model providers through a unified interface
 */
export class ModelFactory {
    constructor(config = {}) {
        this.config = { ...createBaseModelConfig(), ...config };
        this.logger = createLogger(`ModelFactory`, LogLevel.INFO);

        // CRITICAL: ToolManagementService integration for centralized tool binding
        this.toolManagementService = null;

        // Infrastructure services - injected from core.js
        this.infrastructureManager = config.infrastructureManager || null;

        // Model instances cache
        this.modelCache = new Map();
        this.providerFactories = new Map();

        // Performance tracking
        this.metrics = {
            totalRequests: 0,
            httpRequests: 0,
            websocketRequests: 0,
            realtimeRequests: 0,
            averageResponseTime: 0,
            errors: 0,
            cacheHits: 0,
            routingDecisions: 0
        };

        // Initialize provider-specific factories
        this._initializeProviderFactories();

        this.logger.info('ModelFactory initialized');
    }

    /**
     * Set ToolManagementService for centralized tool binding
     * CRITICAL: This must be called before creating models that need tools
     * @param {ToolManagementService} toolManagementService - Tool management service instance
     */
    setToolManagementService(toolManagementService) {
        this.toolManagementService = toolManagementService;
        this.logger.info('✅ ToolManagementService attached to ModelFactory');
    }

    /**
     * Initialize provider-specific factory instances
     * @private
     */
    _initializeProviderFactories() {
        // Aliyun provider factory
        this.providerFactories.set('aliyun', {
            createHttpModel: this._createAliyunHttpModel.bind(this),
            createWebSocketModel: this._createAliyunWebSocketModel.bind(this),
            createRealtimeModel: this._createAliyunWebSocketModel.bind(this),
            selectModelType: this._selectAliyunModelType.bind(this),
            getModelConfigs: this._getAliyunModelConfigs.bind(this)
        });

        // vLLM provider factory
        this.providerFactories.set('vllm', {
            createHttpModel: this._createVLLMHttpModel.bind(this),
            createWebSocketModel: this._createVLLMHttpModel.bind(this), // vLLM uses HTTP for all
            createRealtimeModel: this._createVLLMHttpModel.bind(this),
            selectModelType: this._selectVLLMModelType.bind(this),
            getModelConfigs: this._getVLLMModelConfigs.bind(this)
        });

        // OpenAI provider factory (placeholder)
        this.providerFactories.set('openai', {
            createHttpModel: this._createOpenAIHttpModel.bind(this),
            createWebSocketModel: this._createOpenAIWebSocketModel.bind(this),
            createRealtimeModel: this._createOpenAIRealtimeModel.bind(this),
            selectModelType: this._selectOpenAIModelType.bind(this),
            getModelConfigs: this._getOpenAIModelConfigs.bind(this)
        });
    }

    /**
     * Universal model creation interface
     * Automatically routes to appropriate provider and model type
     * @param {string} taskType - Type of task
     * @param {Object} context - Task context and requirements
     * @returns {Promise<any>} Appropriate model instance
     */
    async createModelForTask(taskType, context = {}) {
        const startTime = Date.now();
        this.metrics.totalRequests++;

        try {
            // Auto-detect provider if not specified
            const provider = context.provider || this.config.provider || this._autoDetectProvider(context);

            // Get provider factory
            const providerFactory = this.providerFactories.get(provider.toLowerCase());
            if (!providerFactory) {
                throw new Error(`Unsupported provider: ${provider}`);
            }

            // Select optimal model type for task
            const modelType = providerFactory.selectModelType(taskType, context);

            // Create model instance
            let model;
            switch (modelType) {
                case ModelType.HTTP:
                    this.metrics.httpRequests++;
                    model = await providerFactory.createHttpModel(context.modelName, context);
                    break;
                case ModelType.WEBSOCKET:
                    this.metrics.websocketRequests++;
                    model = await providerFactory.createWebSocketModel(context.modelName, context);
                    break;
                case ModelType.REALTIME:
                    this.metrics.realtimeRequests++;
                    model = await providerFactory.createRealtimeModel(context.modelName, context);
                    break;
                default:
                    throw new Error(`Unsupported model type: ${modelType}`);
            }

            this.logger.info(`Created ${provider} ${modelType} model for task: ${taskType}`);
            return model;

        } catch (error) {
            this.metrics.errors++;
            this.logger.error(`Model creation failed for task ${taskType}:`, error.message);
            throw error;
        } finally {
            this._updateMetrics(Date.now() - startTime);
        }
    }

    /**
     * CENTRALIZED TOOL BINDING: Create model with tools bound
     * This is the ONLY method that should be used for tool binding
     * Eliminates scattered tool binding across the codebase
     * @param {string} taskType - Type of task requiring tools
     * @param {Array} tools - Tools to bind to the model
     * @param {Object} context - Context for model creation
     * @returns {Promise<any>} Model with bound tools
     */
    async createModelWithTools(taskType, tools = [], context = {}) {
        if (!this.toolManagementService) {
            throw new Error('ToolManagementService must be set before creating models with tools. Call setToolManagementService() first.');
        }

        // Create the base model for the task
        const model = await this.createModelForTask(taskType, context);

        if (!Array.isArray(tools) || tools.length === 0) {
            this.logger.debug(`No tools to bind for task: ${taskType}`);
            return model;
        }

        // CENTRALIZED TOOL BINDING: Use ToolManagementService exclusively
        try {
            const boundModel = this.toolManagementService.bindToolsToModel(model, tools, context);

            this.logger.info(`✅ Created model with ${tools.length} tools for task: ${taskType}`, {
                modelType: model.constructor?.name,
                toolNames: tools.map(t => t.name),
                taskType
            });

            return boundModel;
        } catch (error) {
            this.logger.error(`❌ Failed to bind tools to model for task: ${taskType}`, error);
            throw error;
        }
    }

    /**
     * Create autonomous model with tools - delegates to centralized method
     * @param {Array} tools - Tools to bind to the model
     * @param {Object} options - Configuration options
     * @returns {Promise<any>} Model with bound tools
     */
    async createAutonomousModel(tools = [], options = {}) {
        return this.createModelWithTools('autonomous_tools', tools, {
            ...options,
            requiresTools: true,
            urgency: 'high'
        });
    }

    // Provider-specific implementations

    /**
     * Create Aliyun HTTP model
     * @private
     */
    async _createAliyunHttpModel(modelName = null, options = {}) {
        const selectedModel = modelName || ALIYUN_MODELS.HTTP_PRIMARY;
        const cacheKey = `aliyun-http-${selectedModel}`;

        // Check cache first
        const cached = await this._getCachedModel(cacheKey);
        if (cached) {
            this.metrics.cacheHits++;
            return cached;
        }

        const model = new AliyunHttpChatModel({
            model: selectedModel,
            apiKey: this.config.apiKey || getEnvVar('VITE_DASHSCOPE_API_KEY', ''),
            baseURL: getHttpEndpoint(),
            timeout: options.timeout || this.config.timeout,
            temperature: options.temperature || this.config.temperature,
            maxTokens: options.maxTokens || this.config.maxTokens,
            provider: 'Aliyun',
            ...options
        });

        // Cache the model
        await this._cacheModel(cacheKey, model);
        return model;
    }

    /**
     * Create Aliyun WebSocket model
     * @private
     */
    async _createAliyunWebSocketModel(modelName = null, options = {}) {
        const cacheKey = 'aliyun-websocket';

        const cached = await this._getCachedModel(cacheKey);
        if (cached) {
            this.metrics.cacheHits++;
            return cached;
        }

        const model = new AliyunWebSocketChatModel({
            apiKey: this.config.apiKey || getEnvVar('VITE_DASHSCOPE_API_KEY', ''),
            model: modelName || ALIYUN_MODELS.DEFAULT_REALTIME,
            modalities: ['text', 'audio'],
            audioConfig: this.config.audioConfig,
            ...options
        });

        await this._cacheModel(cacheKey, model);
        return model;
    }

    /**
     * Create vLLM HTTP model
     * @private
     */
    async _createVLLMHttpModel(modelName = null, options = {}) {
        const selectedModel = modelName || 'Qwen/Qwen2.5-Omni-7B';
        const cacheKey = `vllm-${selectedModel}`;

        const cached = await this._getCachedModel(cacheKey);
        if (cached) {
            this.metrics.cacheHits++;
            return cached;
        }

        // Get vLLM endpoint
        let vllmEndpoint;
        try {
            vllmEndpoint = endpoints.vllm;
        } catch (error) {
            vllmEndpoint = `${getDownloadServerUrl()}/vllm-proxy/`;
        }

        const model = new VLLMChatModel({
            model: selectedModel,
            temperature: options.temperature || this.config.temperature,
            maxTokens: options.maxTokens || this.config.maxTokens,
            vllmEndpoint: vllmEndpoint,
            streaming: true,
            verbose: options.verbose || false,
            ...options
        });

        await this._cacheModel(cacheKey, model);
        return model;
    }

    /**
     * OpenAI model implementations (placeholders)
     * @private
     */
    async _createOpenAIHttpModel(modelName = null, options = {}) {
        throw new Error('OpenAI provider not yet implemented');
    }

    async _createOpenAIWebSocketModel(modelName = null, options = {}) {
        throw new Error('OpenAI WebSocket provider not yet implemented');
    }

    async _createOpenAIRealtimeModel(modelName = null, options = {}) {
        throw new Error('OpenAI Realtime provider not yet implemented');
    }

    // Model type selection for each provider

    /**
     * Select Aliyun model type based on task requirements
     * @private
     */
    _selectAliyunModelType(taskType, context = {}) {
        const { requiresTools = false, inputType = 'text', realtime = false } = context;

        // Real-time requirements use WebSocket
        if (realtime || inputType === 'realtime' || inputType === 'voice' || inputType === 'audio') {
            return ModelType.WEBSOCKET;
        }

        // Tool-based and complex tasks use HTTP for better function calling
        if (requiresTools || this._isAutonomousTask(taskType) || this._requiresComplexReasoning(taskType)) {
            return ModelType.HTTP;
        }

        return ModelType.HTTP; // Default to HTTP
    }

    /**
     * Select vLLM model type (always HTTP)
     * @private
     */
    _selectVLLMModelType(taskType, context = {}) {
        return ModelType.HTTP; // vLLM only supports HTTP
    }

    /**
     * Select OpenAI model type
     * @private
     */
    _selectOpenAIModelType(taskType, context = {}) {
        const { realtime = false, inputType = 'text' } = context;

        if (realtime || inputType === 'realtime') {
            return ModelType.REALTIME;
        }

        return ModelType.HTTP;
    }

    // Model configuration getters

    /**
     * Get Aliyun model configurations
     * @private
     */
    _getAliyunModelConfigs() {
        return {
            [ALIYUN_MODELS.HTTP_PRIMARY]: {
                type: ModelType.HTTP,
                capabilities: [ModelCapabilities.FUNCTION_CALLING, ModelCapabilities.AUTONOMOUS_TOOLS],
                performance: { expectedResponseTime: 400, maxResponseTime: 600 }
            },
            [ALIYUN_MODELS.DEFAULT_REALTIME]: {
                type: ModelType.WEBSOCKET,
                capabilities: [ModelCapabilities.REAL_TIME, ModelCapabilities.VOICE_PROCESSING],
                performance: { expectedResponseTime: 200, maxResponseTime: 2000 }
            }
        };
    }

    /**
     * Get vLLM model configurations
     * @private
     */
    _getVLLMModelConfigs() {
        return {
            'Qwen/Qwen2.5-Omni-7B': {
                type: ModelType.HTTP,
                capabilities: [ModelCapabilities.TEXT_GENERATION, ModelCapabilities.FUNCTION_CALLING],
                performance: { expectedResponseTime: 500, maxResponseTime: 800 }
            }
        };
    }

    /**
     * Get OpenAI model configurations
     * @private
     */
    _getOpenAIModelConfigs() {
        return {
            'gpt-4': {
                type: ModelType.HTTP,
                capabilities: [ModelCapabilities.FUNCTION_CALLING, ModelCapabilities.COMPLEX_REASONING],
                performance: { expectedResponseTime: 600, maxResponseTime: 1000 }
            }
        };
    }

    // Utility methods

    /**
     * Auto-detect provider based on configuration
     * @private
     */
    _autoDetectProvider(context) {
        // Check for provider-specific API keys
        if (context.apiKey || getEnvVar('VITE_DASHSCOPE_API_KEY', '')) {
            return 'aliyun';
        }

        if (getEnvVar('OPENAI_API_KEY', '')) {
            return 'openai';
        }

        // Default to vLLM if no specific provider detected
        return 'vllm';
    }

    /**
     * Check if task is autonomous
     * @private
     */
    _isAutonomousTask(taskType) {
        const autonomousTasks = [
            'analyze_conversation_context',
            'decide_communication_mode',
            'autonomous_analysis',
            'decision_making'
        ];

        return autonomousTasks.includes(taskType) ||
            taskType.includes('autonomous') ||
            taskType.includes('analyze');
    }

    /**
     * Check if task requires complex reasoning
     * @private
     */
    _requiresComplexReasoning(taskType) {
        const complexTasks = [
            'complex_analysis',
            'strategic_planning',
            'multi_step_reasoning'
        ];

        return complexTasks.includes(taskType) ||
            taskType.includes('complex') ||
            taskType.includes('strategic');
    }

    /**
     * Cache management
     * @private
     */
    async _getCachedModel(cacheKey) {
        if (this.infrastructureManager) {
            try {
                const cacheManager = await this.infrastructureManager.getCacheManager();
                return await cacheManager.getModel(cacheKey);
            } catch (error) {
                this.logger.warn('Cache retrieval failed:', error.message);
            }
        }

        return this.modelCache.get(cacheKey);
    }

    /**
     * Cache a model instance
     * @private
     */
    async _cacheModel(cacheKey, model) {
        if (this.infrastructureManager) {
            try {
                const cacheManager = await this.infrastructureManager.getCacheManager();
                await cacheManager.setModel(cacheKey, model);
            } catch (error) {
                this.logger.warn('Cache storage failed:', error.message);
            }
        }

        this.modelCache.set(cacheKey, model);
    }

    /**
     * Update performance metrics
     * @private
     */
    _updateMetrics(responseTime) {
        const currentAvg = this.metrics.averageResponseTime;
        const totalRequests = this.metrics.totalRequests;

        this.metrics.averageResponseTime =
            ((currentAvg * (totalRequests - 1)) + responseTime) / totalRequests;
    }

    /**
     * Get comprehensive factory metrics
     * @returns {Object} Performance and usage metrics
     */
    getMetrics() {
        return {
            ...this.metrics,
            cache: {
                size: this.modelCache.size,
                hitRate: this.metrics.cacheHits / Math.max(this.metrics.totalRequests, 1)
            },
            modelSelection: {
                httpRatio: this.metrics.httpRequests / Math.max(this.metrics.totalRequests, 1),
                websocketRatio: this.metrics.websocketRequests / Math.max(this.metrics.totalRequests, 1),
                realtimeRatio: this.metrics.realtimeRequests / Math.max(this.metrics.totalRequests, 1)
            }
        };
    }

    /**
     * Clear all model caches
     */
    clearCache() {
        this.modelCache.clear();
        this.logger.info('Model cache cleared');
    }

    /**
     * Shutdown factory and cleanup resources
     */
    async shutdown() {
        try {
            // Close all cached model connections
            for (const model of this.modelCache.values()) {
                if (typeof model.cleanup === 'function') {
                    await model.cleanup();
                } else if (typeof model.disconnect === 'function') {
                    await model.disconnect();
                }
            }

            this.clearCache();
            this.logger.info('ModelFactory shutdown complete');
        } catch (error) {
            this.logger.error('Error during factory shutdown:', error.message);
        }
    }
}

// Legacy compatibility exports - maintain backward compatibility
export async function createModelProvider(provider, options = {}) {
    const factory = new ModelFactory({ provider, ...options });
    return factory.createModelForTask('general', { provider, ...options });
}

export async function initializeVLLMProvider(options = {}) {
    const factory = new ModelFactory({ provider: 'vllm', ...options });
    return factory.createModelForTask('general', { provider: 'vllm', ...options });
}

export async function initializeAliyunProvider(options = {}) {
    const factory = new ModelFactory({ provider: 'aliyun', ...options });
    return factory.createModelForTask('general', { provider: 'aliyun', ...options });
}

export async function initializeOpenAIProvider(options = {}) {
    const factory = new ModelFactory({ provider: 'openai', ...options });
    return factory.createModelForTask('general', { provider: 'openai', ...options });
}

export function getAvailableProviders() {
    return ['aliyun', 'vllm', 'openai'];
}

export function isProviderAvailable(provider) {
    return getAvailableProviders().includes(provider.toLowerCase());
}

// Re-export models for direct access
export { VLLMChatModel } from './VLLMChatModel.js';
export { AliyunWebSocketChatModel } from './aliyun/AliyunWebSocketChatModel.js';
export { AliyunHttpChatModel } from './aliyun/AliyunHttpChatModel.js';

// Re-export base classes for extensibility
export { BaseChatModel } from './base/BaseChatModel.js';

// Note: Avoid re-exporting from './aliyun/index.js' to prevent circular deps

// Default export
export default ModelFactory;