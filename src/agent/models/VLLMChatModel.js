/**
 * Custom vLLM Chat Model for LangChain
 * Unified support for Qwen2.5-VL and Qwen2.5-Omni models with automatic detection
 */

import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { AIMessage, HumanMessage } from '@langchain/core/messages';
import { createLogger } from '@/utils/logger';
import { getEnvVar } from '../../config/env.ts';

/**
 * Custom Chat Model implementation for vLLM with unified multimodal support
 * Automatically detects and adapts to Qwen2.5-VL and Qwen2.5-Omni models
 */
export class VLLMChatModel extends BaseChatModel {
    static lc_name() {
        return "VLLMChatModel";
    }

    constructor(options = {}) {
        super({
            callbacks: options.callbacks,
            verbose: options.verbose || false,
            maxRetries: options.maxRetries || 2,
        });

        this.logger = createLogger('VLLMChatModel');
        // this.logger.setLogLevel(LogLevel.DEBUG);

        // Configuration with environment variable support
        this.vllmEndpoint = options.vllmEndpoint || 'http://localhost:2994/vllm-proxy';
        this.model = options.model || getEnvVar('VITE_LLM_MODEL', 'Qwen/Qwen2.5-Omni-7B');
        this.temperature = options.temperature ?? 0.7;
        this.maxTokens = options.maxTokens ?? 2048;
        this.streaming = options.streaming !== false; // Default to streaming enabled

        // Store bound tools for LangGraph compatibility
        this.boundTools = [];

        // Detect model capabilities
        this.isOmniModel = this.model.includes('Omni');
        this.isVLModel = this.model.includes('VL');

        this.logger.info(`Initialized VLLMChatModel with endpoint: ${this.vllmEndpoint}`);
        this.logger.info(`Using model: ${this.model} (Omni: ${this.isOmniModel}, VL: ${this.isVLModel})`);
        this.logger.debug(`Streaming enabled by default: ${this.streaming}`);
    }

    /**
     * Required LangChain method: Get the type of language model
     */
    _llmType() {
        return 'vllm_custom';
    }

    /**
     * Required LangChain method: Get identifying parameters
     */
    get _identifyingParams() {
        return {
            model_name: this.model,
            endpoint: this.vllmEndpoint,
            temperature: this.temperature,
            max_tokens: this.maxTokens
        };
    }

    /**
     * LangChain compliance: Return invocation parameters for tracing
     * This method is called by LangChain to log metadata in traces
     */
    invocationParams(options) {
        return {
            model_name: this.model,
            endpoint: this.vllmEndpoint,
            temperature: this.temperature,
            max_tokens: this.maxTokens,
            streaming: this.streaming,
            tools: options?.tools || this.boundTools,
            tool_choice: options?.tool_choice,
            is_omni_model: this.isOmniModel,
            is_vl_model: this.isVLModel
        };
    }

    /**
     * LangGraph compatibility: Bind tools to this model instance
     */
    bindTools(tools, options = {}) {
        this.logger.debug(`Binding ${tools.length} tools to VLLMChatModel`);

        const formattedTools = tools.map(tool => {
            if (typeof tool === 'object' && tool.function) {
                return tool;
            } else if (tool.name && tool.description) {
                return {
                    type: "function",
                    function: {
                        name: tool.name,
                        description: tool.description,
                        parameters: tool.schema || {
                            type: "object",
                            properties: {},
                            required: []
                        }
                    }
                };
            } else {
                this.logger.warn('Unknown tool format:', tool);
                return tool;
            }
        });

        const boundModel = new VLLMChatModel({
            vllmEndpoint: this.vllmEndpoint,
            model: this.model,
            temperature: this.temperature,
            maxTokens: this.maxTokens,
            streaming: this.streaming,
            callbacks: this.callbacks,
            verbose: this.verbose,
            maxRetries: this.maxRetries
        });

        boundModel.boundTools = formattedTools;
        return boundModel;
    }

    /**
     * Core Chat Model method: Generate chat completion from messages
     */
    async _generate(messages, options, runManager) {
        try {
            this.logger.debug(`Generating chat completion for ${messages.length} messages`);

            // Log invocation parameters for tracing
            const additionalParams = this.invocationParams(options);
            this.logger.debug('vLLM generation invocation params:', additionalParams);

            const vllmMessages = this._convertLangChainMessagesToVLLM(messages);

            const requestBody = {
                model: this.model,
                messages: vllmMessages,
                temperature: this.temperature,
                max_tokens: this.maxTokens,
                stream: false // Non-streaming for _generate method (required by LangChain)
            };

            // Add bound tools if available
            if (this.boundTools?.length > 0) {
                requestBody.tools = this.boundTools;
                requestBody.tool_choice = "auto";
                this.logger.debug(`Including ${this.boundTools.length} tools in request`);

                // Debug: Log tool details to help diagnose tool calling issues
                this.logger.debug('📋 Tools being sent to vLLM:', {
                    toolCount: this.boundTools.length,
                    toolNames: this.boundTools.map(t => t.function?.name || t.name),
                    toolsStructure: this.boundTools.map(t => ({
                        name: t.function?.name || t.name,
                        hasDescription: !!(t.function?.description || t.description),
                        hasParameters: !!(t.function?.parameters || t.schema)
                    }))
                });
            }

            // Add additional options
            if (options?.stop) requestBody.stop = options.stop;
            if (options?.tools) requestBody.tools = options.tools;
            if (options?.tool_choice) requestBody.tool_choice = options.tool_choice;

            this.logger.debug(`Making POST request to: ${this.vllmEndpoint}/v1/chat/completions`);

            // Debug: Log full request for tool calling diagnosis
            this.logger.debug('🚀 Full vLLM request:', {
                url: `${this.vllmEndpoint}/v1/chat/completions`,
                method: 'POST',
                hasTools: !!(requestBody.tools && requestBody.tools.length > 0),
                toolCount: requestBody.tools?.length || 0,
                messageCount: requestBody.messages?.length || 0,
                model: requestBody.model,
                temperature: requestBody.temperature,
                tool_choice: requestBody.tool_choice,
                isMultimodal: requestBody.messages?.some(msg => Array.isArray(msg.content)),
                modelType: this.isOmniModel ? 'Omni' : this.isVLModel ? 'VL' : 'Text'
            });

            const response = await this._makeRequest('/v1/chat/completions', requestBody, {
                method: 'POST',
                signal: this._createTimeoutSignal(30000)
            });

            if (!response.ok) {
                throw new Error(`vLLM API error: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();

            if (!data.choices || data.choices.length === 0) {
                throw new Error('No choices returned from vLLM API');
            }

            const choice = data.choices[0];
            const content = choice.message?.content || choice.text || '';
            const toolCalls = choice.message?.tool_calls || [];

            this.logger.debug(`Generated completion: ${content.substring(0, 100)}...`);

            const aiMessage = new AIMessage({
                content,
                additional_kwargs: {
                    tool_calls: toolCalls
                }
            });

            if (toolCalls.length > 0) {
                aiMessage.tool_calls = toolCalls.map(toolCall => ({
                    id: toolCall.id,
                    name: toolCall.function?.name,
                    args: JSON.parse(toolCall.function?.arguments || '{}')
                }));
            }

            return {
                generations: [{
                    message: aiMessage,
                    text: content
                }],
                llmOutput: {
                    tokenUsage: data.usage,
                    modelName: this.model
                }
            };

        } catch (error) {
            this.logger.error('Error in _generate:', error);
            throw new Error(`vLLM chat generation failed: ${error.message}`);
        }
    }

    /**
     * Streaming implementation for chat models
     */
    async *_streamResponseChunks(messages, options, runManager) {
        try {
            this.logger.debug(`Starting streaming chat completion for ${messages.length} messages`);

            const vllmMessages = this._convertLangChainMessagesToVLLM(messages);

            const requestBody = {
                model: this.model,
                messages: vllmMessages,
                temperature: this.temperature,
                max_tokens: this.maxTokens,
                stream: true
            };

            if (this.boundTools?.length > 0) {
                requestBody.tools = this.boundTools;
                requestBody.tool_choice = "auto";
                this.logger.debug(`Including ${this.boundTools.length} tools in streaming request`);

                // Debug: Log tool details for streaming requests
                this.logger.debug('📋 Streaming Tools:', {
                    toolCount: this.boundTools.length,
                    toolNames: this.boundTools.map(t => t.function?.name || t.name)
                });
            }

            if (options?.stop) requestBody.stop = options.stop;
            if (options?.tools) requestBody.tools = options.tools;
            if (options?.tool_choice) requestBody.tool_choice = options.tool_choice;

            // Debug: Log full streaming request
            this.logger.debug('🌊 Full vLLM streaming request:', {
                url: `${this.vllmEndpoint}/v1/chat/completions`,
                method: 'POST',
                streaming: true,
                hasTools: !!(requestBody.tools && requestBody.tools.length > 0),
                toolCount: requestBody.tools?.length || 0,
                messageCount: requestBody.messages?.length || 0,
                model: requestBody.model,
                isMultimodal: requestBody.messages?.some(msg => Array.isArray(msg.content)),
                modelType: this.isOmniModel ? 'Omni' : this.isVLModel ? 'VL' : 'Text'
            });

            const response = await this._makeRequest('/v1/chat/completions', requestBody, {
                method: 'POST',
                signal: this._createTimeoutSignal(120000),
                headers: {
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache'
                }
            });

            if (!response.ok) {
                throw new Error(`vLLM API error: ${response.status} ${response.statusText}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            try {
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || '';

                    for (const line of lines) {
                        if (line.trim() === '' || !line.startsWith('data: ')) continue;

                        const dataStr = line.slice(6);
                        if (dataStr === '[DONE]') return;

                        try {
                            const data = JSON.parse(dataStr);
                            const deltaContent = data.choices?.[0]?.delta?.content || data.choices?.[0]?.text || '';

                            if (deltaContent) {
                                const chunk = {
                                    message: new AIMessage({ content: deltaContent }),
                                    text: deltaContent
                                };

                                if (runManager) {
                                    await runManager.handleLLMNewToken(chunk.text, chunk);
                                }

                                yield chunk;
                            }
                        } catch (parseError) {
                            this.logger.warn('Failed to parse streaming chunk:', parseError);
                        }
                    }
                }
            } finally {
                reader.releaseLock();
            }

        } catch (error) {
            this.logger.error('Error in _streamResponseChunks:', error);
            throw new Error(`vLLM chat streaming failed: ${error.message}`);
        }
    }

    /**
     * LangGraph compatibility: Override stream method
     */
    async stream(messages, options = {}) {
        this.logger.debug('Stream method called (LangGraph compatibility)');
        const self = this;

        return {
            async *[Symbol.asyncIterator]() {
                try {
                    for await (const chunk of self._streamResponseChunks(messages, options)) {
                        yield {
                            content: chunk.text || chunk.message?.content || '',
                            message: chunk.message,
                            ...chunk
                        };
                    }
                } catch (error) {
                    self.logger.error('Error in stream async iterator:', error);
                    throw error;
                }
            }
        };
    }

    /**
     * Convert LangChain messages to vLLM format with unified multimodal support
     * Automatically adapts format based on model type (VL vs Omni)
     */
    _convertLangChainMessagesToVLLM(messages) {
        return messages.map(msg => {
            if (msg instanceof HumanMessage) {
                // Handle multimodal content with model-specific formatting
                if (Array.isArray(msg.content)) {
                    return {
                        role: 'user',
                        content: this._formatMultimodalContent(msg.content)
                    };
                } else {
                    return { role: 'user', content: msg.content };
                }
            } else if (msg instanceof AIMessage) {
                const message = { role: 'assistant', content: msg.content };
                if (msg.additional_kwargs?.tool_calls) {
                    message.tool_calls = msg.additional_kwargs.tool_calls;
                }
                return message;
            } else if (msg._getType() === 'system') {
                return { role: 'system', content: msg.content };
            } else if (msg._getType() === 'tool') {
                return {
                    role: 'tool',
                    content: msg.content,
                    tool_call_id: msg.tool_call_id
                };
            } else {
                return { role: 'user', content: msg.content };
            }
        });
    }

    /**
     * Format multimodal content based on model capabilities
     * Qwen2.5-Omni: Supports text, image_url, audio_url
     * Qwen2.5-VL: Supports text + images (custom format)
     */
    _formatMultimodalContent(content) {
        if (this.isOmniModel) {
            // Qwen2.5-Omni supports standard OpenAI format
            return content.map(item => {
                if (typeof item === 'string') {
                    return { type: 'text', text: item };
                } else if (item.type === 'image_url') {
                    return {
                        type: 'image_url',
                        image_url: { url: item.image_url.url }
                    };
                } else if (item.type === 'audio_url') {
                    return {
                        type: 'audio_url',
                        audio_url: { url: item.audio_url.url }
                    };
                } else if (item.type === 'text') {
                    return { type: 'text', text: item.text };
                }
                return item;
            });
        } else {
            // Qwen2.5-VL uses a different format - convert to text + image references
            let textContent = '';
            const images = [];

            content.forEach(item => {
                if (typeof item === 'string') {
                    textContent += item;
                } else if (item.type === 'text') {
                    textContent += item.text;
                } else if (item.type === 'image_url') {
                    images.push(item.image_url.url);
                    textContent += `[Image: ${item.image_url.url}]`;
                }
            });

            // For VL models, we return the text content and let vLLM handle images separately
            // This is a simplified approach - actual VL model integration may require different handling
            return textContent;
        }
    }

    /**
     * Test connection to vLLM API
     */
    async testConnection() {
        try {
            this.logger.info('Testing connection to vLLM API...');

            const testMessages = [new HumanMessage('Hello, this is a test.')];
            const testResult = await this._generate(testMessages, { max_tokens: 10 });

            this.logger.info('Connection test successful:', testResult.generations[0].text);
            return true;
        } catch (error) {
            this.logger.error('Connection test failed:', error);
            return false;
        }
    }

    /**
     * Make HTTP request to vLLM API through proxy
     */
    async _makeRequest(path, body = {}, options = {}) {
        const url = `${this.vllmEndpoint.replace(/\/$/, '')}${path}`;

        const requestOptions = {
            method: options.method || 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                ...options.headers
            },
            signal: options.signal
        };

        if (requestOptions.method !== 'GET' && Object.keys(body).length > 0) {
            requestOptions.body = JSON.stringify(body);
        }

        this.logger.debug(`Making ${requestOptions.method} request to: ${url}`);

        try {
            return await fetch(url, requestOptions);
        } catch (fetchError) {
            this.logger.error('Fetch error:', fetchError.message);
            throw fetchError;
        }
    }

    /**
     * Update configuration
     */
    updateConfig(newConfig) {
        if (newConfig.vllmEndpoint !== undefined) this.vllmEndpoint = newConfig.vllmEndpoint;
        if (newConfig.model !== undefined) {
            this.model = newConfig.model;
            // Update model capabilities
            this.isOmniModel = this.model.includes('Omni');
            this.isVLModel = this.model.includes('VL');
        }
        if (newConfig.temperature !== undefined) this.temperature = newConfig.temperature;
        if (newConfig.maxTokens !== undefined) this.maxTokens = newConfig.maxTokens;

        this.logger.info('Configuration updated:', this._identifyingParams);
    }

    /**
     * Get current configuration
     */
    getConfig() {
        return {
            vllmEndpoint: this.vllmEndpoint,
            model: this.model,
            temperature: this.temperature,
            maxTokens: this.maxTokens,
            streaming: this.streaming,
            isOmniModel: this.isOmniModel,
            isVLModel: this.isVLModel
        };
    }

    /**
     * Create a timeout signal for requests
     */
    _createTimeoutSignal(timeout) {
        try {
            if (typeof AbortSignal !== 'undefined' && AbortSignal.timeout) {
                return AbortSignal.timeout(timeout);
            }
        } catch (error) {
            this.logger.debug('AbortSignal.timeout not available, using fallback');
        }

        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
            controller.abort(new Error(`Request timeout after ${timeout}ms`));
        }, timeout);

        controller.signal.addEventListener('abort', () => {
            clearTimeout(timeoutId);
        });

        return controller.signal;
    }
}