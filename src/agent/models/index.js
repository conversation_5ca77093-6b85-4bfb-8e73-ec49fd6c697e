/**
 * Model System - Unified Entry Point
 * Exports the consolidated model factory and all related functionality
 */

// Export the consolidated model factory and its functions
export { 
    ModelFactory as default,
    ModelFactory,
    createModelProvider,
    initializeVLLMProvider,
    initializeAliyunProvider,
    initializeOpenAIProvider,
    getAvailableProviders,
    isProviderAvailable,
    ModelCapabilities,
    ModelType,
    createBaseModelConfig
} from './ModelFactory.js';

// Re-export individual model classes for direct access
export { VLLMChatModel } from './VLLMChatModel.js';
export { AliyunWebSocketChatModel } from './aliyun/AliyunWebSocketChatModel.js';
export { AliyunHttpChatModel } from './aliyun/AliyunHttpChatModel.js';
export { AliyunModelFactory } from './aliyun/AliyunModelFactory.js';

// Re-export base classes for extensibility
export { BaseChatModel } from './base/BaseChatModel.js';

// Re-export Aliyun components
export * from './aliyun/index.js';