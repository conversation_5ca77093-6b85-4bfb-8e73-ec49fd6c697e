/**
 * Provider Configuration System
 * Consolidates all provider configuration logic into a single source of truth
 * Eliminates duplication between core.js, interfaces/ProviderConfig.js, and models/base/ProviderConfig.js
 * 
 * Features:
 * - Universal provider support (Aliyun, vLLM, OpenAI, Anthropic, etc.)
 * - Auto-detection based on environment variables and configuration
 * - Standardized configuration interface across all providers
 * - Validation and error handling
 * - Configuration templates and factories
 */

import { createLogger, LogLevel } from '@/utils/logger';
import { getEnvVar } from '@/config/env';
import { endpoints } from '@/config/endpoints';
import { getDownloadServerUrl } from '@/utils/portManager';

/**
 * Supported provider types
 */
export const ProviderTypes = {
    ALIYUN: 'aliyun',
    VLLM: 'vllm',
    OPENAI: 'openai',
    ANTHROPIC: 'anthropic',
    AZURE: 'azure'
};

/**
 * Provider capability flags
 */
export const ProviderCapabilities = {
    HTTP_MODELS: 'http_models',
    WEBSOCKET_MODELS: 'websocket_models',
    REALTIME_MODELS: 'realtime_models',
    FUNCTION_CALLING: 'function_calling',
    STREAMING: 'streaming',
    VOICE_PROCESSING: 'voice_processing',
    MULTIMODAL: 'multimodal'
};

/**
 * Base configuration template for all providers
 */
export const createBaseProviderConfig = (provider = 'unknown') => ({
    // Provider identification
    provider,
    displayName: provider.toUpperCase(),
    
    // Authentication
    apiKey: '',
    apiSecret: '',
    
    // Endpoints
    httpEndpoint: '',
    websocketEndpoint: '',
    realtimeEndpoint: '',
    
    // Default models
    defaultHttpModel: '',
    defaultWebSocketModel: '',
    defaultRealtimeModel: '',
    
    // Connection settings
    timeout: 30000,
    connectionTimeout: 10000,
    maxRetries: 3,
    retryDelay: 1000,
    
    // Rate limiting
    rateLimiting: {
        enabled: true,
        requestsPerSecond: 10,
        burstLimit: 20
    },
    
    // Model parameters
    defaultParameters: {
        temperature: 0.7,
        maxTokens: 2000,
        topP: 0.8,
        stream: true
    },
    
    // Audio configuration (for realtime providers)
    audioConfig: {
        sampleRate: 16000,
        bitDepth: 16,
        channels: 1,
        format: 'pcm16'
    },
    
    // Capabilities
    capabilities: [],
    
    // Feature flags
    features: {
        toolCalling: false,
        streaming: false,
        realtime: false,
        multimodal: false
    },
    
    // Validation rules
    validation: {
        requireApiKey: true,
        requireEndpoint: true,
        allowedModels: []
    }
});

/**
 * Provider-specific configuration factories
 */
export const ProviderConfigFactories = {
    /**
     * Aliyun/Bailian provider configuration
     */
    [ProviderTypes.ALIYUN]: (options = {}) => {
        const config = createBaseProviderConfig(ProviderTypes.ALIYUN);
        
        return {
            ...config,
            displayName: 'Aliyun DashScope',
            
            // Authentication
            apiKey: options.apiKey || 
                    getEnvVar('VITE_DASHSCOPE_API_KEY', '') || 
                    getEnvVar('VITE_ALIYUN_API_KEY', ''),
            
            // Endpoints
            httpEndpoint: options.httpEndpoint || 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
            websocketEndpoint: options.websocketEndpoint || 'wss://dashscope.aliyuncs.com/api/v1/realtime',
            realtimeEndpoint: options.realtimeEndpoint || 'wss://dashscope.aliyuncs.com/api/v1/realtime',
            
            // Default models
            defaultHttpModel: options.defaultHttpModel || 'qwen-plus',
            defaultWebSocketModel: options.defaultWebSocketModel || 'qwen-audio-turbo',
            defaultRealtimeModel: options.defaultRealtimeModel || 'qwen-audio-turbo',
            
            // Connection settings optimized for Aliyun
            timeout: options.timeout || 30000,
            connectionTimeout: options.connectionTimeout || 10000,
            
            // Rate limiting (Aliyun specific limits)
            rateLimiting: {
                enabled: true,
                requestsPerSecond: options.rateLimiting?.requestsPerSecond || 20,
                burstLimit: options.rateLimiting?.burstLimit || 40
            },
            
            // Capabilities
            capabilities: [
                ProviderCapabilities.HTTP_MODELS,
                ProviderCapabilities.WEBSOCKET_MODELS,
                ProviderCapabilities.REALTIME_MODELS,
                ProviderCapabilities.FUNCTION_CALLING,
                ProviderCapabilities.STREAMING,
                ProviderCapabilities.VOICE_PROCESSING,
                ProviderCapabilities.MULTIMODAL
            ],
            
            // Feature flags
            features: {
                toolCalling: true,
                streaming: true,
                realtime: true,
                multimodal: true
            },
            
            // Validation
            validation: {
                requireApiKey: true,
                requireEndpoint: false, // Uses default endpoints
                allowedModels: ['qwen-plus', 'qwen-turbo', 'qwen-max', 'qwen-audio-turbo']
            }
        };
    },

    /**
     * vLLM provider configuration
     */
    [ProviderTypes.VLLM]: (options = {}) => {
        const config = createBaseProviderConfig(ProviderTypes.VLLM);
        
        // Auto-detect vLLM endpoint
        let vllmEndpoint;
        try {
            vllmEndpoint = endpoints.vllm;
        } catch (error) {
            vllmEndpoint = `${getDownloadServerUrl()}/vllm-proxy/`;
        }
        
        return {
            ...config,
            displayName: 'vLLM',
            
            // Authentication (usually not required for local vLLM)
            apiKey: options.apiKey || getEnvVar('VLLM_API_KEY', ''),
            
            // Endpoints (vLLM only supports HTTP)
            httpEndpoint: options.httpEndpoint || vllmEndpoint,
            websocketEndpoint: '', // Not supported
            realtimeEndpoint: '', // Not supported
            
            // Default models
            defaultHttpModel: options.defaultHttpModel || 'Qwen/Qwen2.5-Omni-7B',
            defaultWebSocketModel: '', // Not supported
            defaultRealtimeModel: '', // Not supported
            
            // Connection settings optimized for local vLLM
            timeout: options.timeout || 60000, // Higher timeout for local inference
            connectionTimeout: options.connectionTimeout || 15000,
            
            // Rate limiting (more lenient for local)
            rateLimiting: {
                enabled: false, // Usually disabled for local
                requestsPerSecond: options.rateLimiting?.requestsPerSecond || 100,
                burstLimit: options.rateLimiting?.burstLimit || 200
            },
            
            // Capabilities
            capabilities: [
                ProviderCapabilities.HTTP_MODELS,
                ProviderCapabilities.FUNCTION_CALLING,
                ProviderCapabilities.STREAMING
            ],
            
            // Feature flags
            features: {
                toolCalling: true,
                streaming: true,
                realtime: false,
                multimodal: false
            },
            
            // Validation
            validation: {
                requireApiKey: false,
                requireEndpoint: true,
                allowedModels: [] // Any model can be loaded
            }
        };
    },

    /**
     * OpenAI provider configuration
     */
    [ProviderTypes.OPENAI]: (options = {}) => {
        const config = createBaseProviderConfig(ProviderTypes.OPENAI);
        
        return {
            ...config,
            displayName: 'OpenAI',
            
            // Authentication
            apiKey: options.apiKey || getEnvVar('OPENAI_API_KEY', ''),
            
            // Endpoints
            httpEndpoint: options.httpEndpoint || 'https://api.openai.com/v1/chat/completions',
            websocketEndpoint: '', // Not supported
            realtimeEndpoint: options.realtimeEndpoint || 'wss://api.openai.com/v1/realtime',
            
            // Default models
            defaultHttpModel: options.defaultHttpModel || 'gpt-4',
            defaultWebSocketModel: '', // Not supported
            defaultRealtimeModel: options.defaultRealtimeModel || 'gpt-4-realtime-preview',
            
            // Connection settings optimized for OpenAI
            timeout: options.timeout || 60000,
            connectionTimeout: options.connectionTimeout || 10000,
            
            // Rate limiting (OpenAI specific limits)
            rateLimiting: {
                enabled: true,
                requestsPerSecond: options.rateLimiting?.requestsPerSecond || 5,
                burstLimit: options.rateLimiting?.burstLimit || 10
            },
            
            // Capabilities
            capabilities: [
                ProviderCapabilities.HTTP_MODELS,
                ProviderCapabilities.REALTIME_MODELS,
                ProviderCapabilities.FUNCTION_CALLING,
                ProviderCapabilities.STREAMING,
                ProviderCapabilities.VOICE_PROCESSING,
                ProviderCapabilities.MULTIMODAL
            ],
            
            // Feature flags
            features: {
                toolCalling: true,
                streaming: true,
                realtime: true,
                multimodal: true
            },
            
            // Validation
            validation: {
                requireApiKey: true,
                requireEndpoint: false,
                allowedModels: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo', 'gpt-4-realtime-preview']
            }
        };
    },

    /**
     * Anthropic provider configuration
     */
    [ProviderTypes.ANTHROPIC]: (options = {}) => {
        const config = createBaseProviderConfig(ProviderTypes.ANTHROPIC);
        
        return {
            ...config,
            displayName: 'Anthropic Claude',
            
            // Authentication
            apiKey: options.apiKey || getEnvVar('ANTHROPIC_API_KEY', ''),
            
            // Endpoints
            httpEndpoint: options.httpEndpoint || 'https://api.anthropic.com/v1/messages',
            websocketEndpoint: '', // Not supported
            realtimeEndpoint: '', // Not supported
            
            // Default models
            defaultHttpModel: options.defaultHttpModel || 'claude-3-5-sonnet-20241022',
            defaultWebSocketModel: '', // Not supported
            defaultRealtimeModel: '', // Not supported
            
            // Connection settings
            timeout: options.timeout || 60000,
            connectionTimeout: options.connectionTimeout || 10000,
            
            // Rate limiting
            rateLimiting: {
                enabled: true,
                requestsPerSecond: options.rateLimiting?.requestsPerSecond || 5,
                burstLimit: options.rateLimiting?.burstLimit || 10
            },
            
            // Capabilities
            capabilities: [
                ProviderCapabilities.HTTP_MODELS,
                ProviderCapabilities.FUNCTION_CALLING,
                ProviderCapabilities.STREAMING,
                ProviderCapabilities.MULTIMODAL
            ],
            
            // Feature flags
            features: {
                toolCalling: true,
                streaming: true,
                realtime: false,
                multimodal: true
            },
            
            // Validation
            validation: {
                requireApiKey: true,
                requireEndpoint: false,
                allowedModels: ['claude-3-5-sonnet-20241022', 'claude-3-haiku-20240307']
            }
        };
    },

    /**
     * Azure OpenAI provider configuration
     */
    [ProviderTypes.AZURE]: (options = {}) => {
        const config = createBaseProviderConfig(ProviderTypes.AZURE);
        
        return {
            ...config,
            displayName: 'Azure OpenAI',
            
            // Authentication
            apiKey: options.apiKey || getEnvVar('AZURE_OPENAI_API_KEY', ''),
            
            // Endpoints (requires custom Azure endpoint)
            httpEndpoint: options.httpEndpoint || getEnvVar('AZURE_OPENAI_ENDPOINT', ''),
            websocketEndpoint: '',
            realtimeEndpoint: '',
            
            // Default models (deployment names in Azure)
            defaultHttpModel: options.defaultHttpModel || 'gpt-4',
            defaultWebSocketModel: '',
            defaultRealtimeModel: '',
            
            // Connection settings
            timeout: options.timeout || 60000,
            connectionTimeout: options.connectionTimeout || 10000,
            
            // Rate limiting
            rateLimiting: {
                enabled: true,
                requestsPerSecond: options.rateLimiting?.requestsPerSecond || 10,
                burstLimit: options.rateLimiting?.burstLimit || 20
            },
            
            // Capabilities
            capabilities: [
                ProviderCapabilities.HTTP_MODELS,
                ProviderCapabilities.FUNCTION_CALLING,
                ProviderCapabilities.STREAMING,
                ProviderCapabilities.MULTIMODAL
            ],
            
            // Feature flags
            features: {
                toolCalling: true,
                streaming: true,
                realtime: false,
                multimodal: true
            },
            
            // Validation
            validation: {
                requireApiKey: true,
                requireEndpoint: true,
                allowedModels: [] // Deployment names are custom
            }
        };
    }
};

/**
 * Provider Configuration Manager
 */
export class ProviderConfigManager {
    constructor() {
        this.logger = createLogger('ProviderConfigManager', LogLevel.DEBUG);
        this.configurations = new Map();
        this.defaultProvider = null;
    }

    /**
     * Auto-detect best available provider based on environment
     * Replaces autoDetectProvider logic from core.js
     * @param {Object} options - Additional options for detection
     * @returns {string} - Detected provider type
     */
    autoDetectProvider(options = {}) {
        this.logger.debug('Auto-detecting best available provider...');

        // Check for explicit provider preference
        if (options.provider) {
            this.logger.debug(`Using explicit provider preference: ${options.provider}`);
            return options.provider.toLowerCase();
        }

        // Check environment variables for API keys
        const providerChecks = [
            { 
                provider: ProviderTypes.ALIYUN, 
                check: () => getEnvVar('VITE_DASHSCOPE_API_KEY', '') || getEnvVar('VITE_ALIYUN_API_KEY', ''),
                priority: 1
            },
            { 
                provider: ProviderTypes.OPENAI, 
                check: () => getEnvVar('OPENAI_API_KEY', ''),
                priority: 2
            },
            { 
                provider: ProviderTypes.ANTHROPIC, 
                check: () => getEnvVar('ANTHROPIC_API_KEY', ''),
                priority: 3
            },
            { 
                provider: ProviderTypes.AZURE, 
                check: () => getEnvVar('AZURE_OPENAI_API_KEY', '') && getEnvVar('AZURE_OPENAI_ENDPOINT', ''),
                priority: 4
            },
            { 
                provider: ProviderTypes.VLLM, 
                check: () => true, // Always available as fallback
                priority: 5
            }
        ];

        // Sort by priority and find first available
        providerChecks.sort((a, b) => a.priority - b.priority);
        
        for (const { provider, check } of providerChecks) {
            if (check()) {
                this.logger.info(`Auto-detected provider: ${provider}`);
                return provider;
            }
        }

        // Fallback to vLLM
        this.logger.warn('No provider auto-detected, falling back to vLLM');
        return ProviderTypes.VLLM;
    }

    /**
     * Create provider configuration
     * @param {string} provider - Provider type
     * @param {Object} options - Provider-specific options
     * @returns {Object} - Provider configuration
     */
    createProviderConfig(provider, options = {}) {
        const providerType = provider.toLowerCase();
        
        if (!ProviderConfigFactories[providerType]) {
            throw new Error(`Unsupported provider: ${provider}`);
        }

        const config = ProviderConfigFactories[providerType](options);
        this.logger.debug(`Created ${provider} provider configuration`);
        
        return config;
    }

    /**
     * Validate provider configuration
     * @param {Object} config - Provider configuration to validate
     * @returns {Object} - Validation result
     */
    validateProviderConfig(config) {
        const errors = [];
        const warnings = [];

        // Basic validation
        if (!config.provider) {
            errors.push('Provider type is required');
        }

        // API key validation
        if (config.validation.requireApiKey && !config.apiKey) {
            errors.push(`API key is required for ${config.provider} provider`);
        }

        // Endpoint validation
        if (config.validation.requireEndpoint && !config.httpEndpoint) {
            errors.push(`HTTP endpoint is required for ${config.provider} provider`);
        }

        // Model validation
        if (config.validation.allowedModels.length > 0) {
            const models = [config.defaultHttpModel, config.defaultWebSocketModel, config.defaultRealtimeModel]
                .filter(Boolean);
            
            for (const model of models) {
                if (!config.validation.allowedModels.includes(model)) {
                    warnings.push(`Model ${model} may not be supported by ${config.provider}`);
                }
            }
        }

        // Timeout validation
        if (config.timeout < 1000) {
            warnings.push(`Timeout ${config.timeout}ms is very low and may cause frequent timeouts`);
        }

        const isValid = errors.length === 0;
        
        this.logger.debug(`Validation result for ${config.provider}:`, { 
            isValid, 
            errors, 
            warnings 
        });

        return {
            isValid,
            errors,
            warnings
        };
    }

    /**
     * Get or create provider configuration
     * @param {string} provider - Provider type
     * @param {Object} options - Configuration options
     * @returns {Object} - Provider configuration
     */
    getProviderConfig(provider, options = {}) {
        const cacheKey = `${provider}-${JSON.stringify(options)}`;
        
        if (this.configurations.has(cacheKey)) {
            return this.configurations.get(cacheKey);
        }

        const config = this.createProviderConfig(provider, options);
        const validation = this.validateProviderConfig(config);
        
        if (!validation.isValid) {
            throw new Error(`Invalid ${provider} configuration: ${validation.errors.join(', ')}`);
        }

        if (validation.warnings.length > 0) {
            this.logger.warn(`${provider} configuration warnings:`, validation.warnings);
        }

        // Cache the configuration
        this.configurations.set(cacheKey, config);
        
        return config;
    }

    /**
     * Set default provider
     * @param {string} provider - Provider type to set as default
     */
    setDefaultProvider(provider) {
        this.defaultProvider = provider.toLowerCase();
        this.logger.info(`Set default provider to: ${this.defaultProvider}`);
    }

    /**
     * Get default provider
     * @returns {string|null} - Default provider type
     */
    getDefaultProvider() {
        return this.defaultProvider;
    }

    /**
     * Check if provider is available
     * @param {string} provider - Provider type to check
     * @returns {boolean} - True if provider is available
     */
    isProviderAvailable(provider) {
        return Object.values(ProviderTypes).includes(provider.toLowerCase());
    }

    /**
     * Get all available providers
     * @returns {Array} - List of available provider types
     */
    getAvailableProviders() {
        return Object.values(ProviderTypes);
    }

    /**
     * Get provider capabilities
     * @param {string} provider - Provider type
     * @returns {Array} - List of provider capabilities
     */
    getProviderCapabilities(provider) {
        try {
            const config = this.createProviderConfig(provider);
            return config.capabilities;
        } catch (error) {
            this.logger.warn(`Failed to get capabilities for ${provider}:`, error.message);
            return [];
        }
    }

    /**
     * Clear cached configurations
     */
    clearCache() {
        this.configurations.clear();
        this.logger.debug('Provider configuration cache cleared');
    }
}

// Export singleton instance
export const providerConfigManager = new ProviderConfigManager();

// Legacy compatibility functions
export function createProviderConfig(provider, options = {}) {
    return providerConfigManager.createProviderConfig(provider, options);
}

export function validateProviderConfig(config) {
    return providerConfigManager.validateProviderConfig(config);
}

export function autoDetectProvider(options = {}) {
    return providerConfigManager.autoDetectProvider(options);
}

export default ProviderConfigManager;