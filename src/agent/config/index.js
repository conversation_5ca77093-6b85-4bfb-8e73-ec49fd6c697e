/**
 * Agent Configuration Index
 *
 * Centralized exports for all agent configuration modules.
 * This is the single source of truth for agent-related configurations.
 *
 * Import pattern: import { AgentConfig, DUAL_BRAIN_CONFIG } from '@/agent/config';
 */

// Centralized Agent Configuration (Single source of truth)
// CONSOLIDATED: All configurations now in AgentConfig.js
export {
    default as AgentConfig,
    getCompleteAgentConfig,
    getStreamingConfig,
    getAgentConfig,
    getProviderConfig,
    getLangGraphConfig,
    getPerformanceConfig,
    getApplicationConfig,
    validateAgentConfig,
    DEFAULT_STREAMING_CONFIG,
    DEFAULT_AGENT_CONFIG,
    PROVIDER_CONFIGS,
    LANGGRAPH_CONFIG as CENTRALIZED_LANGGRAPH_CONFIG,
    PERFORMANCE_CONFIG,
    DEBUG_CONFIG,
    APPLICATION_CONFIGS,
    // CONSOLIDATED from LangGraphConfig.js
    DUAL_BRAIN_CONFIG,
    SYSTEM_INVOKER_CONFIG,
    ALIYUN_INTEGRATION_CONFIG,
    // Utility functions
    validateToolsForLangGraph,
    getToolChoiceStrategy,
    determineSystemRouting,
    createLangGraphOptions,
    determineSystem1Modalities,
    createAliyunModalityUpdate
} from './AgentConfig.js';

// CONSOLIDATED: All configurations and functions now exported from AgentConfig.js above
// Legacy exports removed to eliminate redundancy

// Re-export commonly used configurations for convenience
export const AGENT_CONFIG = {
    // Backward compatibility - use centralized config instead
    get streaming() {
        return getStreamingConfig();
    },
    get agent() {
        return getAgentConfig();
    },
    get performance() {
        return getPerformanceConfig();
    },
    get dualBrain() {
        return getCompleteDualBrainConfig();
    }
};

/**
 * Get all configuration objects (Updated to use consolidated dual brain config)
 */
export function getAllConfigs() {
    return {
        // New centralized configurations
        agent: getAgentConfig(),
        streaming: getStreamingConfig(),
        performance: getPerformanceConfig(),

        // Consolidated dual brain configuration
        dualBrain: getCompleteDualBrainConfig(),

        // Legacy configurations (for backward compatibility only)
        langgraph: CENTRALIZED_LANGGRAPH_CONFIG
    };
}

/**
 * Validate all configurations on import (Enhanced with dual brain validation)
 */
export function validateConfigurations() {
    const configs = getAllConfigs();
    const errors = [];

    // Basic validation
    Object.entries(configs).forEach(([name, config]) => {
        if (!config || typeof config !== 'object') {
            errors.push(`Invalid configuration: ${name}`);
        }
    });

    // Validate centralized agent config
    try {
        const testConfig = getCompleteAgentConfig();
        const validation = validateAgentConfig(testConfig);
        if (!validation.isValid) {
            errors.push(`Agent config validation failed: ${validation.errors.join(', ')}`);
        }
    } catch (error) {
        errors.push(`Failed to validate centralized agent config: ${error.message}`);
    }

    // 🔧 NEW: Validate consolidated dual brain config
    try {
        const dualBrainValidation = validateDualBrainConfig();
        if (!dualBrainValidation.isValid) {
            errors.push(`Dual brain config validation failed: ${dualBrainValidation.errors.join(', ')}`);
        }
    } catch (error) {
        errors.push(`Failed to validate dual brain config: ${error.message}`);
    }

    if (errors.length > 0) {
        throw new Error(`Configuration validation failed: ${errors.join(', ')}`);
    }

    return true;
}

// Validate on import
validateConfigurations();

export default {
    // New centralized configuration system
    AgentConfig,
    getCompleteAgentConfig,
    getStreamingConfig,
    getAgentConfig,
    getProviderConfig,
    getLangGraphConfig,
    getPerformanceConfig,
    getApplicationConfig,
    validateAgentConfig,

    // Consolidated dual brain configuration
    DUAL_BRAIN_CONFIG,
    getSystemConfig,
    getSystemModalities,
    getRoutingConfig,
    getTwoPhaseConfig,
    getNoActivityConfig,
    validateDualBrainConfig,
    getCompleteDualBrainConfig,
    DualBrainUtils,

    // Legacy functions (for backward compatibility)
    validateToolsForLangGraph,
    getToolChoiceStrategy,
    createLangGraphOptions,
    createAliyunModalityUpdate,

    // Utility functions
    getAllConfigs,
    validateConfigurations
};