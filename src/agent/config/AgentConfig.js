/**
 * Centralized Agent Configuration
 * Single source of truth for all agent-related configurations
 * 
 * This file consolidates configurations from:
 * - StreamingManager
 * - Core.js
 * - LangGraph settings
 * - Provider settings
 * - Performance settings
 */

import { createLogger } from '@/utils/logger';
import { getEnvVar } from '@/config/env';

const logger = createLogger('AgentConfig');

/**
 * Default Streaming Configuration
 * Consolidates StreamingManager options
 */
export const DEFAULT_STREAMING_CONFIG = {
    // Stream Mode Configuration (Fixed for LangGraph JS name property issue)
    preferredMode: 'values', // Use 'values' instead of 'messages' to avoid undefined name property
    enableMultiMode: true,
    
    // Stream Types
    enableValueStream: true,
    enableUpdateStream: true,
    enableMessageStream: true,
    
    // Performance Settings
    chunkBuffer: 50,
    bufferSize: 1000,
    targetLatency: 600, // Sub-600ms response time target
    adaptiveThrottling: true,
    
    // Stream Modes (avoid 'messages' mode due to LangGraph JS bug)
    defaultModes: ['values', 'updates'],
    fallbackModes: ['values'],
    
    // Token Streaming
    tokenDelay: 20, // ms between tokens for natural streaming effect
    enableTokenization: true,
    
    // Multimodal Support
    enableMultimodal: false,
    audioFormat: 'float32',
    videoFormat: 'base64',
    
    // Error Handling
    maxRetries: 3,
    retryDelay: 1000,
    enableFallback: true
};

/**
 * Default Agent Configuration
 * Consolidates core agent settings
 */
export const DEFAULT_AGENT_CONFIG = {
    // Model Configuration
    temperature: 0.7,
    maxTokens: 4096,
    topP: 0.9,
    timeout: 30000,
    
    // Tool Configuration
    toolChoiceStrategy: 'auto',
    maxToolsPerRequest: 10,
    enableToolValidation: true,
    skipInvalidTools: true,
    
    // Memory Configuration
    maxHistoryLength: 10,
    summarizeAfter: 20,
    enableSummary: true,
    enableMemoryContext: true,
    
    // Dual Brain Configuration
    enableDualBrain: false,
    system1Model: 'websocket', // Fast brain
    system2Model: 'http',      // Thinking brain
    
    // Realtime Configuration
    enableRealtime: false,
    sampleRate: 24000,
    realtimeFormat: 'pcm16',
    enableVAD: true,
    
    // Session Configuration
    defaultSessionId: 'default',
    sessionTimeout: 300000, // 5 minutes
    maxRetries: 3,
    reconnectDelay: 2000,
    
    // UI Integration
    enableUICoordination: true,
    enableConnectionCoordination: true,
    enableContextualAnalysis: true,
    
    // Performance
    enablePerformanceOptimization: true,
    enableMetrics: true,
    enableCircuitBreaker: true,
    enableCaching: true
};

/**
 * Provider-Specific Configurations
 * Centralized provider settings
 */
export const PROVIDER_CONFIGS = {
    aliyun: {
        name: 'aliyun',
        modelName: 'qwen-turbo',
        supportsRealtime: true,
        supportsStreaming: true,
        supportsToolCalling: true,
        requiresApiKey: true,
        endpoints: {
            http: getEnvVar('VITE_ALIYUN_HTTP_ENDPOINT', 'http://localhost:2994/api/llm'),
            websocket: getEnvVar('VITE_ALIYUN_WEBSOCKET_ENDPOINT', 'wss://dashscope.aliyuncs.com/api/v1/apps/YOUR_APP_ID/completion'),
            realtime: getEnvVar('VITE_ALIYUN_REALTIME_ENDPOINT', 'wss://dashscope.aliyuncs.com/api/v1/inference')
        }
    },
    openai: {
        name: 'openai',
        modelName: 'gpt-4-turbo-preview',
        supportsRealtime: true,
        supportsStreaming: true,
        supportsToolCalling: true,
        requiresApiKey: true,
        endpoints: {
            http: 'https://api.openai.com/v1',
            realtime: 'wss://api.openai.com/v1/realtime'
        }
    },
    vllm: {
        name: 'vllm',
        modelName: 'meta-llama/Llama-2-7b-chat-hf',
        supportsRealtime: false,
        supportsStreaming: true,
        supportsToolCalling: true,
        requiresApiKey: false,
        endpoints: {
            http: getEnvVar('VITE_VLLM_ENDPOINT', 'http://localhost:8000/v1')
        }
    }
};

/**
 * LangGraph Specific Configuration
 * Settings for LangGraph agents and workflows
 */
export const LANGGRAPH_CONFIG = {
    // Agent Types
    agentType: 'react', // 'react', 'plan_and_execute', 'custom'
    
    // React Agent Configuration
    react: {
        enableInterrupts: true,
        enableCheckpointing: true,
        enableStore: true,
        messageModifier: null, // Will be set dynamically
        toolChoice: 'auto'
    },
    
    // State Configuration
    stateAnnotation: 'messages', // 'messages', 'custom'
    enableStateValidation: true,
    
    // Checkpointing
    checkpointConfig: {
        type: 'memory', // 'memory', 'sqlite', 'postgres'
        options: {
            saver: null // Will be initialized dynamically
        }
    },
    
    // Store Configuration
    storeConfig: {
        type: 'memory', // 'memory', 'redis'
        options: {}
    },
    
    // Thread Configuration
    threadConfig: {
        configurable: {
            thread_id: 'default'
        }
    }
};

/**
 * Performance Configuration
 * Settings for performance optimization
 */
export const PERFORMANCE_CONFIG = {
    // Circuit Breaker
    circuitBreaker: {
        failureThreshold: 5,
        resetTimeout: 60000,
        monitorTimeout: 30000
    },
    
    // Performance Tracking
    performanceTracker: {
        historySize: 1000,
        metricsWindow: 300000,
        component: 'universal'
    },
    
    // Performance Coordinator
    performanceCoordinator: {
        targetLatency: 600,
        enableAdaptiveOptimization: true,
        optimizationInterval: 10000
    },
    
    // Cache Manager
    cacheManager: {
        defaultTTL: 300000, // 5 minutes
        maxSize: 1000,
        enableCompression: true
    },
    
    // Intelligent Router
    intelligentRouter: {
        enableLoadBalancing: true,
        enableFailover: true,
        healthCheckInterval: 30000
    }
};

/**
 * Debug and Logging Configuration
 */
export const DEBUG_CONFIG = {
    enableDebugLogging: getEnvVar('VITE_DEBUG', 'false') === 'true',
    logLevel: getEnvVar('VITE_LOG_LEVEL', 'INFO'),
    enablePerformanceLogging: true,
    enableErrorTracking: true,
    enableMetrics: getEnvVar('VITE_ENABLE_METRICS', 'true') === 'true',
    
    // Component-specific logging
    components: {
        streamingManager: 'DEBUG',
        langGraphAgent: 'DEBUG',
        dualBrainCoordinator: 'DEBUG',
        modelFactory: 'INFO',
        toolManager: 'INFO'
    }
};

/**
 * Application-Specific Configuration
 * Settings for different application types
 */
export const APPLICATION_CONFIGS = {
    avatar: {
        applicationType: 'avatar',
        assistantName: 'Javis',
        assistantRole: 'AI Avatar Assistant',
        enableTTS: true,
        enableAnimation: true,
        enableVoiceCloning: true,
        communicationStyle: 'friendly',
        responseLength: 'conversational'
    },
    
    codeAssistant: {
        applicationType: 'codeAssistant',
        assistantName: 'Code Assistant',
        assistantRole: 'Programming Helper',
        enableCodeGeneration: true,
        programmingLanguages: ['javascript', 'typescript', 'python', 'java'],
        communicationStyle: 'technical',
        responseLength: 'detailed'
    },
    
    textAssistant: {
        applicationType: 'textAssistant',
        assistantName: 'Assistant',
        assistantRole: 'Text-based Helper',
        textOnly: true,
        communicationStyle: 'professional',
        responseLength: 'concise'
    },
    
    generic: {
        applicationType: 'generic',
        assistantName: 'AI Assistant',
        assistantRole: 'General Purpose Helper',
        communicationStyle: 'adaptable',
        responseLength: 'balanced'
    }
};

/**
 * Utility Functions for Configuration Management
 */

/**
 * Get streaming configuration with overrides
 * @param {Object} overrides - Configuration overrides
 * @returns {Object} Merged streaming configuration
 */
export function getStreamingConfig(overrides = {}) {
    return {
        ...DEFAULT_STREAMING_CONFIG,
        ...overrides
    };
}

/**
 * Get agent configuration with overrides
 * @param {Object} overrides - Configuration overrides
 * @returns {Object} Merged agent configuration
 */
export function getAgentConfig(overrides = {}) {
    return {
        ...DEFAULT_AGENT_CONFIG,
        ...overrides
    };
}

/**
 * Get provider configuration by name
 * @param {string} providerName - Provider name (aliyun, openai, vllm)
 * @param {Object} overrides - Configuration overrides
 * @returns {Object} Provider configuration
 */
export function getProviderConfig(providerName, overrides = {}) {
    const baseConfig = PROVIDER_CONFIGS[providerName.toLowerCase()];
    if (!baseConfig) {
        throw new Error(`Unknown provider: ${providerName}`);
    }
    
    return {
        ...baseConfig,
        ...overrides
    };
}

/**
 * Get LangGraph configuration with overrides
 * @param {Object} overrides - Configuration overrides
 * @returns {Object} Merged LangGraph configuration
 */
export function getLangGraphConfig(overrides = {}) {
    return {
        ...LANGGRAPH_CONFIG,
        ...overrides
    };
}

/**
 * Get performance configuration with overrides
 * @param {Object} overrides - Configuration overrides
 * @returns {Object} Merged performance configuration
 */
export function getPerformanceConfig(overrides = {}) {
    return {
        ...PERFORMANCE_CONFIG,
        ...overrides
    };
}

/**
 * Get application configuration by type
 * @param {string} appType - Application type (avatar, codeAssistant, textAssistant, generic)
 * @param {Object} overrides - Configuration overrides
 * @returns {Object} Application configuration
 */
export function getApplicationConfig(appType, overrides = {}) {
    const baseConfig = APPLICATION_CONFIGS[appType] || APPLICATION_CONFIGS.generic;
    
    return {
        ...baseConfig,
        ...overrides
    };
}

/**
 * Get complete agent configuration for initialization
 * @param {Object} options - Initialization options
 * @returns {Object} Complete configuration object
 */
export function getCompleteAgentConfig(options = {}) {
    const {
        provider = 'aliyun',
        applicationType = 'avatar',
        agentOverrides = {},
        streamingOverrides = {},
        performanceOverrides = {},
        langGraphOverrides = {}
    } = options;

    const config = {
        // Core configuration
        agent: getAgentConfig(agentOverrides),
        
        // Provider configuration
        provider: getProviderConfig(provider, options.providerOverrides),
        
        // Application configuration
        application: getApplicationConfig(applicationType, options.applicationOverrides),
        
        // Streaming configuration
        streaming: getStreamingConfig(streamingOverrides),
        
        // LangGraph configuration
        langGraph: getLangGraphConfig(langGraphOverrides),
        
        // Performance configuration
        performance: getPerformanceConfig(performanceOverrides),
        
        // Debug configuration
        debug: DEBUG_CONFIG
    };

    logger.debug('Generated complete agent configuration:', {
        provider,
        applicationType,
        hasAgentOverrides: Object.keys(agentOverrides).length > 0,
        hasStreamingOverrides: Object.keys(streamingOverrides).length > 0
    });

    return config;
}

/**
 * Validate configuration object
 * @param {Object} config - Configuration to validate
 * @returns {Object} Validation result with errors array
 */
export function validateAgentConfig(config) {
    const errors = [];
    
    // Validate required fields
    if (!config.provider) {
        errors.push('Provider configuration is required');
    }
    
    if (!config.agent) {
        errors.push('Agent configuration is required');
    }
    
    if (!config.streaming) {
        errors.push('Streaming configuration is required');
    }
    
    // Validate provider-specific requirements
    if (config.provider && config.provider.requiresApiKey) {
        const apiKey = getEnvVar(`VITE_${config.provider.name.toUpperCase()}_API_KEY`);
        if (!apiKey) {
            errors.push(`API key required for provider: ${config.provider.name}`);
        }
    }
    
    // Validate streaming mode (ensure we don't use 'messages' mode)
    if (config.streaming && config.streaming.preferredMode === 'messages') {
        errors.push('Streaming mode "messages" is not recommended due to LangGraph JS name property issues');
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
}

// Export default configuration for easy access
export default {
    getCompleteAgentConfig,
    getStreamingConfig,
    getAgentConfig,
    getProviderConfig,
    getLangGraphConfig,
    getPerformanceConfig,
    getApplicationConfig,
    validateAgentConfig,
    
    // Direct access to configurations
    DEFAULT_STREAMING_CONFIG,
    DEFAULT_AGENT_CONFIG,
    PROVIDER_CONFIGS,
    LANGGRAPH_CONFIG,
    PERFORMANCE_CONFIG,
    DEBUG_CONFIG,
    APPLICATION_CONFIGS
};