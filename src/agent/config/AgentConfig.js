/**
 * Centralized Agent Configuration
 * Single source of truth for all agent-related configurations
 *
 * This file consolidates configurations from:
 * - StreamingManager
 * - Core.js
 * - LangGraph settings
 * - Provider settings
 * - Performance settings
 */

import { createLogger } from '@/utils/logger';
import { getEnvVar } from '@/config/env';

const logger = createLogger('AgentConfig');

/**
 * Default Streaming Configuration
 * Consolidates StreamingManager options
 */
export const DEFAULT_STREAMING_CONFIG = {
    // Stream Mode Configuration (Fixed for LangGraph JS name property issue)
    preferredMode: 'values', // Use 'values' instead of 'messages' to avoid undefined name property
    enableMultiMode: true,

    // Stream Types
    enableValueStream: true,
    enableUpdateStream: true,
    enableMessageStream: true,

    // Performance Settings
    chunkBuffer: 50,
    bufferSize: 1000,
    targetLatency: 600, // Sub-600ms response time target
    adaptiveThrottling: true,

    // Stream Modes (avoid 'messages' mode due to LangGraph JS bug)
    defaultModes: ['values', 'updates'],
    fallbackModes: ['values'],

    // Token Streaming
    tokenDelay: 20, // ms between tokens for natural streaming effect
    enableTokenization: true,

    // Multimodal Support
    enableMultimodal: false,
    audioFormat: 'float32',
    videoFormat: 'base64',

    // Error Handling
    maxRetries: 3,
    retryDelay: 1000,
    enableFallback: true
};

/**
 * Default Agent Configuration
 * Consolidates core agent settings
 */
export const DEFAULT_AGENT_CONFIG = {
    // Model Configuration
    temperature: 0.7,
    maxTokens: 4096,
    topP: 0.9,
    timeout: 30000,

    // Tool Configuration
    toolChoiceStrategy: 'auto',
    maxToolsPerRequest: 10,
    enableToolValidation: true,
    skipInvalidTools: true,

    // Memory Configuration
    maxHistoryLength: 10,
    summarizeAfter: 20,
    enableSummary: true,
    enableMemoryContext: true,

    // Dual Brain Configuration (CONSOLIDATED from LangGraphConfig.js)
    enableDualBrain: false,
    system1Model: 'websocket', // Fast brain
    system2Model: 'http',      // Thinking brain

    // Realtime Configuration
    enableRealtime: false,
    sampleRate: 24000,
    realtimeFormat: 'pcm16',
    enableVAD: true,

    // Session Configuration
    defaultSessionId: 'default',
    sessionTimeout: 300000, // 5 minutes
    maxRetries: 3,
    reconnectDelay: 2000,

    // UI Integration
    enableUICoordination: true,
    enableConnectionCoordination: true,
    enableContextualAnalysis: true,

    // Performance
    enablePerformanceOptimization: true,
    enableMetrics: true,
    enableCircuitBreaker: true,
    enableCaching: true
};

/**
 * Provider-Specific Configurations
 * Centralized provider settings
 */
export const PROVIDER_CONFIGS = {
    aliyun: {
        name: 'aliyun',
        modelName: 'qwen-turbo',
        supportsRealtime: true,
        supportsStreaming: true,
        supportsToolCalling: true,
        requiresApiKey: true,
        endpoints: {
            http: getEnvVar('VITE_ALIYUN_HTTP_ENDPOINT', 'http://localhost:2994/api/llm'),
            websocket: getEnvVar('VITE_ALIYUN_WEBSOCKET_ENDPOINT', 'wss://dashscope.aliyuncs.com/api/v1/apps/YOUR_APP_ID/completion'),
            realtime: getEnvVar('VITE_ALIYUN_REALTIME_ENDPOINT', 'wss://dashscope.aliyuncs.com/api/v1/inference')
        }
    },
    openai: {
        name: 'openai',
        modelName: 'gpt-4-turbo-preview',
        supportsRealtime: true,
        supportsStreaming: true,
        supportsToolCalling: true,
        requiresApiKey: true,
        endpoints: {
            http: 'https://api.openai.com/v1',
            realtime: 'wss://api.openai.com/v1/realtime'
        }
    },
    vllm: {
        name: 'vllm',
        modelName: 'meta-llama/Llama-2-7b-chat-hf',
        supportsRealtime: false,
        supportsStreaming: true,
        supportsToolCalling: true,
        requiresApiKey: false,
        endpoints: {
            http: getEnvVar('VITE_VLLM_ENDPOINT', 'http://localhost:8000/v1')
        }
    }
};

/**
 * LangGraph Specific Configuration
 * Settings for LangGraph agents and workflows
 * CONSOLIDATED: Merged from LangGraphConfig.js
 */
export const LANGGRAPH_CONFIG = {
    // Agent Types
    agentType: 'react', // 'react', 'plan_and_execute', 'custom'

    // React Agent Configuration
    react: {
        enableInterrupts: true,
        enableCheckpointing: true,
        enableStore: true,
        messageModifier: null, // Will be set dynamically
        toolChoice: 'auto'
    },

    // State Configuration
    stateAnnotation: 'messages', // 'messages', 'custom'
    enableStateValidation: true,

    // Checkpointing
    checkpointConfig: {
        type: 'memory', // 'memory', 'sqlite', 'postgres'
        options: {
            saver: null // Will be initialized dynamically
        }
    },

    // Store Configuration
    storeConfig: {
        type: 'memory', // 'memory', 'redis'
        options: {}
    },

    // Tool validation settings (from LangGraphConfig.js)
    toolValidation: {
        enabled: true,
        requireName: true,
        requireDescription: true,
        requireSchema: true,
        requireInvokeMethod: true,
        defaultSchema: { type: 'object', properties: {} },
        skipInvalidTools: true
    },

    // ReactAgent creation settings (from LangGraphConfig.js)
    reactAgent: {
        enableRetry: true,
        maxRetries: 3,
        retryDelay: 1000,
        validateToolBinding: true,
        gracefulDegradation: true
    },

    // Tool choice strategies (from LangGraphConfig.js)
    toolChoice: {
        default: 'auto',
        avatarMode: 'auto',  // For avatar applications with speaking tools
        requiredFallback: 'auto'  // Convert 'required' to 'auto' for compatibility
    },

    // Error handling configuration (from LangGraphConfig.js)
    errorHandling: {
        mode: 'graceful',
        enableRetry: true,
        maxRetries: 3,
        retryDelay: 1000,
        fallbackToBasicAgent: true,
        logErrors: true
    },

    // Thread Configuration
    threadConfig: {
        configurable: {
            thread_id: 'default'
        }
    }
};

/**
 * Performance Configuration
 * Settings for performance optimization
 */
export const PERFORMANCE_CONFIG = {
    // Circuit Breaker
    circuitBreaker: {
        failureThreshold: 5,
        resetTimeout: 60000,
        monitorTimeout: 30000
    },

    // Performance Tracking
    performanceTracker: {
        historySize: 1000,
        metricsWindow: 300000,
        component: 'universal'
    },

    // Performance Coordinator
    performanceCoordinator: {
        targetLatency: 600,
        enableAdaptiveOptimization: true,
        optimizationInterval: 10000
    },

    // Cache Manager
    cacheManager: {
        defaultTTL: 300000, // 5 minutes
        maxSize: 1000,
        enableCompression: true
    },

    // Intelligent Router
    intelligentRouter: {
        enableLoadBalancing: true,
        enableFailover: true,
        healthCheckInterval: 30000
    }
};

/**
 * Dual Brain System Configuration
 * CONSOLIDATED: Merged from LangGraphConfig.js
 */
export const DUAL_BRAIN_CONFIG = {
    // System routing configuration
    routing: {
        // System 1 (Fast Brain) - WebSocket realtime with dynamic modalities
        system1: {
            capabilities: ['multimodalOutput', 'realtime', 'fastResponse'],
            useFor: ['simple_queries', 'quick_interactions', 'text_analysis_for_system2'],
            skipMemoryContext: true,
            enableToolCalling: false,  // System 1 focuses on speed
            // Modality control
            defaultModalities: ['text'],  // Default to text-only for System 2 analysis
            audioModalities: ['text', 'audio'],  // When audio output is needed
            nativeAudioCapability: true  // System 1 can generate audio natively
        },

        // System 2 (Thinking Brain) - HTTP API with tool calling
        system2: {
            capabilities: ['toolCalling', 'complexReasoning', 'memoryContext'],
            useFor: ['complex_queries', 'tool_calling', 'reasoning_tasks'],
            skipMemoryContext: false,
            enableToolCalling: true,  // System 2 handles tools
            defaultModalities: ['text'],  // System 2 is text-only, delegates audio to System 1
            requiresAnalysis: true
        }
    },

    // Simple modality control (System 1 has audio capability, System 2 controls when to use it)
    modalityControl: {
        // Default: text-only for System 2 analysis, text+audio when speaking tools activate
        defaultModalities: ['text'],           // Default for analysis
        speakingModalities: ['text', 'audio'],  // When speaking is needed
        defaultVoice: 'Chelsie'
    },

    // Complexity analysis thresholds
    complexity: {
        simple: {
            maxLength: 50,
            keywords: [],
            hasQuestions: false
        },
        medium: {
            maxLength: 200,
            keywords: ['analyze', 'explain', 'compare', 'plan'],
            hasQuestions: true
        },
        high: {
            minLength: 200,
            keywords: ['think', 'reason', 'complex', 'detailed'],
            requiresThinking: true
        }
    },

    // No-activity monitoring
    noActivity: {
        enabled: true,
        patterns: {
            quiet_period: {
                threshold: 10000,    // 10 seconds
                action: 'contextual_engagement',
                triggers: ['user-profile-analysis', 'avatar-expression', 'proactive-conversation']
            },
            extended_quiet: {
                threshold: 30000,    // 30 seconds
                action: 'ambient_animation',
                triggers: ['character-idle-behavior', 'environmental-awareness']
            },
            long_silence: {
                threshold: 120000,   // 2 minutes
                action: 'check_in',
                triggers: ['user-wellness-check', 'topic-suggestion']
            }
        }
    }
};

/**
 * SystemInvoker Configuration
 * CONSOLIDATED: Merged from LangGraphConfig.js
 */
export const SYSTEM_INVOKER_CONFIG = {
    // Method invocation preferences
    methods: {
        preferredAgentMethod: 'generateResponse',  // Use generateResponse over processInput
        fallbackToDirectModel: true,
        validateInputs: true,
        validateOutputs: true
    },

    // Retry and timeout settings (Aliyun-compliant)
    reliability: {
        defaultTimeout: 120000,      // 2 minutes (Aliyun minimum)
        retryAttempts: 3,
        retryDelayMs: 1000,          // 1 second between retries
        dualBrainTimeout: 180000     // 3 minutes for complex reasoning
    },

    // Tool calling configuration
    toolCalling: {
        enabled: true,
        useAgentService: true,
        fallbackToDirectModel: true,
        validateTools: true,
        enhancedLogging: true
    }
};

/**
 * Aliyun-Specific Configuration Overrides
 * CONSOLIDATED: Merged from LangGraphConfig.js
 */
export const ALIYUN_INTEGRATION_CONFIG = {
    // Timeout overrides for Aliyun compliance
    timeouts: {
        connection: 120000,          // 2 minutes minimum
        read: 300000,               // 5 minutes for complex operations
        handshake: 180000,          // 3 minutes for WebSocket
        dualBrain: 180000           // 3 minutes for System 2 reasoning
    },

    // Retry settings
    retry: {
        delay: 1000,                // 1 second (was 100ms)
        maxAttempts: 2,
        backoffMultiplier: 2
    },

    // Model selection for different capabilities
    models: {
        toolCalling: 'qwen-turbo',                    // HTTP API for tools
        realtimeAudio: 'qwen-omni-turbo-realtime-latest',  // WebSocket for audio
        complexReasoning: 'qwen-plus'                 // HTTP API for advanced analysis
    },

    // Connection pool settings (Java SDK compatible)
    connectionPool: {
        maxConnections: 100,
        connectionTimeout: 120000,
        socketTimeout: 300000,
        keepAliveTimeout: 300000,
        maxIdleTime: 600000
    }
};

/**
 * Debug and Logging Configuration
 */
export const DEBUG_CONFIG = {
    enableDebugLogging: getEnvVar('VITE_DEBUG', 'false') === 'true',
    logLevel: getEnvVar('VITE_LOG_LEVEL', 'INFO'),
    enablePerformanceLogging: true,
    enableErrorTracking: true,
    enableMetrics: getEnvVar('VITE_ENABLE_METRICS', 'true') === 'true',

    // Component-specific logging
    components: {
        streamingManager: 'DEBUG',
        langGraphAgent: 'DEBUG',
        dualBrainCoordinator: 'DEBUG',
        modelFactory: 'INFO',
        toolManager: 'INFO'
    }
};

/**
 * Application-Specific Configuration
 * Settings for different application types
 */
export const APPLICATION_CONFIGS = {
    avatar: {
        applicationType: 'avatar',
        assistantName: 'Javis',
        assistantRole: 'AI Avatar Assistant',
        enableTTS: true,
        enableAnimation: true,
        enableVoiceCloning: true,
        communicationStyle: 'friendly',
        responseLength: 'conversational'
    },

    codeAssistant: {
        applicationType: 'codeAssistant',
        assistantName: 'Code Assistant',
        assistantRole: 'Programming Helper',
        enableCodeGeneration: true,
        programmingLanguages: ['javascript', 'typescript', 'python', 'java'],
        communicationStyle: 'technical',
        responseLength: 'detailed'
    },

    textAssistant: {
        applicationType: 'textAssistant',
        assistantName: 'Assistant',
        assistantRole: 'Text-based Helper',
        textOnly: true,
        communicationStyle: 'professional',
        responseLength: 'concise'
    },

    generic: {
        applicationType: 'generic',
        assistantName: 'AI Assistant',
        assistantRole: 'General Purpose Helper',
        communicationStyle: 'adaptable',
        responseLength: 'balanced'
    }
};

/**
 * Utility Functions for Configuration Management
 */

/**
 * Get streaming configuration with overrides
 * @param {Object} overrides - Configuration overrides
 * @returns {Object} Merged streaming configuration
 */
export function getStreamingConfig(overrides = {}) {
    return {
        ...DEFAULT_STREAMING_CONFIG,
        ...overrides
    };
}

/**
 * Get agent configuration with overrides
 * @param {Object} overrides - Configuration overrides
 * @returns {Object} Merged agent configuration
 */
export function getAgentConfig(overrides = {}) {
    return {
        ...DEFAULT_AGENT_CONFIG,
        ...overrides
    };
}

/**
 * Get provider configuration by name
 * @param {string} providerName - Provider name (aliyun, openai, vllm)
 * @param {Object} overrides - Configuration overrides
 * @returns {Object} Provider configuration
 */
export function getProviderConfig(providerName, overrides = {}) {
    const baseConfig = PROVIDER_CONFIGS[providerName.toLowerCase()];
    if (!baseConfig) {
        throw new Error(`Unknown provider: ${providerName}`);
    }

    return {
        ...baseConfig,
        ...overrides
    };
}

/**
 * Get LangGraph configuration with overrides
 * @param {Object} overrides - Configuration overrides
 * @returns {Object} Merged LangGraph configuration
 */
export function getLangGraphConfig(overrides = {}) {
    return {
        ...LANGGRAPH_CONFIG,
        ...overrides
    };
}

/**
 * Get performance configuration with overrides
 * @param {Object} overrides - Configuration overrides
 * @returns {Object} Merged performance configuration
 */
export function getPerformanceConfig(overrides = {}) {
    return {
        ...PERFORMANCE_CONFIG,
        ...overrides
    };
}

/**
 * Get application configuration by type
 * @param {string} appType - Application type (avatar, codeAssistant, textAssistant, generic)
 * @param {Object} overrides - Configuration overrides
 * @returns {Object} Application configuration
 */
export function getApplicationConfig(appType, overrides = {}) {
    const baseConfig = APPLICATION_CONFIGS[appType] || APPLICATION_CONFIGS.generic;

    return {
        ...baseConfig,
        ...overrides
    };
}

/**
 * Get complete agent configuration for initialization
 * @param {Object} options - Initialization options
 * @returns {Object} Complete configuration object
 */
export function getCompleteAgentConfig(options = {}) {
    const {
        provider = 'aliyun',
        applicationType = 'avatar',
        agentOverrides = {},
        streamingOverrides = {},
        performanceOverrides = {},
        langGraphOverrides = {}
    } = options;

    const config = {
        // Core configuration
        agent: getAgentConfig(agentOverrides),

        // Provider configuration
        provider: getProviderConfig(provider, options.providerOverrides),

        // Application configuration
        application: getApplicationConfig(applicationType, options.applicationOverrides),

        // Streaming configuration
        streaming: getStreamingConfig(streamingOverrides),

        // LangGraph configuration
        langGraph: getLangGraphConfig(langGraphOverrides),

        // Performance configuration
        performance: getPerformanceConfig(performanceOverrides),

        // Debug configuration
        debug: DEBUG_CONFIG
    };

    logger.debug('Generated complete agent configuration:', {
        provider,
        applicationType,
        hasAgentOverrides: Object.keys(agentOverrides).length > 0,
        hasStreamingOverrides: Object.keys(streamingOverrides).length > 0
    });

    return config;
}

/**
 * Validate configuration object
 * @param {Object} config - Configuration to validate
 * @returns {Object} Validation result with errors array
 */
export function validateAgentConfig(config) {
    const errors = [];

    // Validate required fields
    if (!config.provider) {
        errors.push('Provider configuration is required');
    }

    if (!config.agent) {
        errors.push('Agent configuration is required');
    }

    if (!config.streaming) {
        errors.push('Streaming configuration is required');
    }

    // Validate provider-specific requirements
    if (config.provider && config.provider.requiresApiKey) {
        const apiKey = getEnvVar(`VITE_${config.provider.name.toUpperCase()}_API_KEY`);
        if (!apiKey) {
            errors.push(`API key required for provider: ${config.provider.name}`);
        }
    }

    // Validate streaming mode (ensure we don't use 'messages' mode)
    if (config.streaming && config.streaming.preferredMode === 'messages') {
        errors.push('Streaming mode "messages" is not recommended due to LangGraph JS name property issues');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Utility Functions from LangGraphConfig.js
 * CONSOLIDATED: Merged utility functions
 */

/**
 * Validate tools for LangGraph ReactAgent compatibility
 */
export function validateToolsForLangGraph(tools) {
    if (!Array.isArray(tools)) {
        logger.warn('Tools must be an array');
        return [];
    }

    return tools.filter(tool => {
        if (!tool || typeof tool !== 'object') {
            logger.warn('Invalid tool: must be an object', tool);
            return false;
        }

        // Check required properties for LangGraph ReactAgent
        const requiredProps = ['name', 'description', 'invoke'];
        const missingProps = requiredProps.filter(prop => !tool[prop]);

        if (missingProps.length > 0) {
            logger.warn(`Tool missing required properties: ${missingProps.join(', ')}`, tool);
            return false;
        }

        // Ensure schema exists (add default if missing)
        if (!tool.schema) {
            tool.schema = { type: 'object', properties: {} };
            logger.debug('Added default schema to tool:', tool.name);
        }

        return true;
    });
}

/**
 * Get tool choice strategy based on context
 */
export function getToolChoiceStrategy(context = {}) {
    const { targetSystem, enableToolCalling, capabilities } = context;

    // System 1 (Fast Brain) - no tools for speed
    if (targetSystem === 'system1') {
        return 'none';
    }

    // System 2 (Thinking Brain) - enable tools
    if (targetSystem === 'system2' || enableToolCalling || capabilities?.includes('tools')) {
        return DUAL_BRAIN_CONFIG.routing.system2.enableToolCalling ? 'auto' : 'none';
    }

    return LANGGRAPH_CONFIG.toolChoice.default;
}

/**
 * Determine system routing based on input complexity
 */
export function determineSystemRouting(input, context = {}) {
    const { complexity } = DUAL_BRAIN_CONFIG;
    const inputLength = typeof input === 'string' ? input.length : JSON.stringify(input).length;

    // Check for tool calling requirements
    if (context.requiresTools || context.capabilities?.includes('tools')) {
        return 'system2';
    }

    // Simple queries go to System 1
    if (inputLength <= complexity.simple.maxLength) {
        return 'system1';
    }

    // Complex queries go to System 2
    if (inputLength >= complexity.high.minLength) {
        return 'system2';
    }

    // Medium complexity - check for reasoning keywords
    const hasReasoningKeywords = complexity.medium.keywords.some(keyword =>
        input.toLowerCase().includes(keyword)
    );

    return hasReasoningKeywords ? 'system2' : 'system1';
}

/**
 * Create LangGraph-compatible agent options
 */
export function createLangGraphOptions(context = {}) {
    return {
        useSystem2: context.targetSystem === 'system2',
        enableTools: context.enableToolCalling || context.capabilities?.includes('tools'),
        context: context.context || {},
        streaming: context.streaming || false,

        // LangGraph-specific configuration
        langgraph: {
            mode: 'react_agent',
            toolBinding: 'enhanced',
            errorHandling: LANGGRAPH_CONFIG.errorHandling.mode,
            toolValidation: LANGGRAPH_CONFIG.toolValidation.enabled
        },

        // Timeout configuration (Aliyun-compliant)
        timeout: context.targetSystem === 'system2'
            ? ALIYUN_INTEGRATION_CONFIG.timeouts.dualBrain
            : ALIYUN_INTEGRATION_CONFIG.timeouts.connection
    };
}

/**
 * Determine System 1 modalities based on context
 */
export function determineSystem1Modalities(context = {}) {
    const { modalityControl } = DUAL_BRAIN_CONFIG;

    // If speaking tools are active, use audio modalities
    if (context.enableSpeaking || context.capabilities?.includes('speaking')) {
        return {
            modalities: modalityControl.speakingModalities,
            voice: context.voice || modalityControl.defaultVoice,
            output_audio_format: 'pcm16'
        };
    }

    // Default to text-only for System 2 analysis
    return {
        modalities: modalityControl.defaultModalities,
        output_audio_format: 'pcm16'
    };
}

/**
 * Create Aliyun session.update event for modality control
 */
export function createAliyunModalityUpdate(modalities, options = {}) {
    const baseConfig = {
        type: "session.update",
        event_id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        session: {
            modalities: modalities.modalities,
            input_audio_format: "pcm16",
            output_audio_format: modalities.output_audio_format || "pcm16",
            input_audio_transcription: {
                model: "gummy-realtime-v1"
            },
            turn_detection: {
                type: "server_vad",
                threshold: 0.1,
                prefix_padding_ms: 500,
                silence_duration_ms: 900
            },
            ...options
        }
    };

    // Add voice only if audio is enabled
    if (modalities.modalities.includes('audio') && modalities.voice) {
        baseConfig.session.voice = modalities.voice;
    }

    return baseConfig;
}

// Export default configuration for easy access
export default {
    // Configuration getter functions
    getCompleteAgentConfig,
    getStreamingConfig,
    getAgentConfig,
    getProviderConfig,
    getLangGraphConfig,
    getPerformanceConfig,
    getApplicationConfig,
    validateAgentConfig,

    // Utility functions (CONSOLIDATED from LangGraphConfig.js)
    validateToolsForLangGraph,
    getToolChoiceStrategy,
    determineSystemRouting,
    createLangGraphOptions,
    determineSystem1Modalities,
    createAliyunModalityUpdate,

    // Direct access to configurations
    DEFAULT_STREAMING_CONFIG,
    DEFAULT_AGENT_CONFIG,
    PROVIDER_CONFIGS,
    LANGGRAPH_CONFIG,
    PERFORMANCE_CONFIG,
    DEBUG_CONFIG,
    APPLICATION_CONFIGS,

    // CONSOLIDATED configurations from LangGraphConfig.js
    DUAL_BRAIN_CONFIG,
    SYSTEM_INVOKER_CONFIG,
    ALIYUN_INTEGRATION_CONFIG
};