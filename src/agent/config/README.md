# Agent Configuration - Single Source of Truth

This directory contains centralized configuration for the LangGraph and Dual Brain agent system.

## Configuration Files

### LangGraphConfig.js
**Primary configuration file** containing:
- LangGraph ReactAgent settings
- Dual Brain routing configuration  
- Tool validation parameters
- Aliyun API integration settings
- SystemInvoker configuration

### index.js
Configuration exports and validation utilities.

## Fixed Issues

### 🔴 Issue 1: LangGraph Tool Calling Error
**Problem**: `Cannot set properties of undefined (setting 'name')` at `@langchain_langgraph_prebuilt.js:667:29`

**Root Cause**: Tools missing required properties for LangGraph ReactAgent

**Solution**: 
- Added comprehensive tool validation in `validateToolsForLangGraph()`
- Ensures all tools have `name`, `description`, `schema`, and `invoke` properties
- Normalizes tool interface for LangGraph compatibility

### 🔴 Issue 2: Aliyun API Connection Errors  
**Problem**: Connection timeouts and authentication failures

**Root Cause**: Timeout configurations (8000ms) below Aliyun minimum requirements (120000ms)

**Solution**:
- Updated `AliyunConfig.js` timeouts: 8000ms → 120000ms
- Updated retry delays: 100ms → 1000ms  
- Updated dual brain timeout: 10000ms → 180000ms
- Updated WebSocket ready timeout: 8000ms → 120000ms

### 🔴 Issue 3: Dual Brain Architecture Conflicts
**Problem**: SystemInvoker fallback patterns conflicting with LangGraph

**Root Cause**: Inconsistent configuration across components

**Solution**:
- Centralized all configuration in `/src/agent/config/`
- Updated core.js, SystemInvoker.js, and DualBrainCoordinator.js to use centralized config
- Clarified that speaking is part of tool calling (System 2)

## Architecture Decisions

### Dynamic Modality Control
- **System 1** (WebSocket) has native audio output capability but is controlled by System 2
- **When tool calling is NOT activated**: System 1 provides text-only output for System 2 analysis
- **When speaking tools are activated**: System 2 triggers System 1 to use text+audio modalities
- **System 2** (HTTP) makes tool calling decisions and controls System 1's modality configuration

### Modality Flow
1. **Default State**: System 1 uses `["text"]` modalities for System 2 analysis
2. **Tool Activation**: System 2 determines when speaking is needed
3. **Modality Switch**: System 2 sends `session.update` to change System 1 to `["text", "audio"]`
4. **Audio Output**: System 1 generates both text and audio response
5. **Reset**: System 1 returns to text-only for next System 2 analysis cycle

### Configuration Hierarchy
1. **Centralized defaults** in LangGraphConfig.js
2. **Component-specific overrides** in constructor options
3. **Runtime overrides** in method parameters

### Validation Strategy
- **Graceful degradation**: Skip invalid tools with warnings
- **Fallback values**: Provide defaults for missing properties
- **Error boundaries**: Catch and handle validation failures

## Usage Examples

```javascript
// Import centralized configuration
import { 
  validateToolsForLangGraph, 
  determineSystemRouting,
  determineSystem1Modalities,
  createAliyunModalityUpdate 
} from '@/agent/config';

// Validate tools before LangGraph binding
const validatedTools = validateToolsForLangGraph(tools);

// Determine system routing
const routing = determineSystemRouting(input, { enableToolCalling: true });

// Control System 1 modalities based on context
const modalityConfig = determineSystem1Modalities({
  enableToolCalling: true,
  speakingToolActivated: false,  // text-only for analysis
  isSystem2Analysis: true
});

// Create Aliyun session.update for modality control
const sessionUpdate = createAliyunModalityUpdate(modalityConfig);
```

## Performance Improvements

- **Tool validation**: Prevents LangGraph crashes
- **Proper timeouts**: Reduces connection failures  
- **Centralized config**: Eliminates configuration drift
- **Graceful fallbacks**: Maintains system stability

## Testing

The configuration includes validation functions to ensure all settings are correct before system startup.

Run `validateConfigurations()` to check all config objects.

## Future Enhancements

1. **Runtime configuration updates**: Hot-reload config changes
2. **Environment-specific configs**: Dev/staging/prod variations
3. **Performance monitoring**: Track config effectiveness
4. **Auto-tuning**: Adaptive timeout and retry settings