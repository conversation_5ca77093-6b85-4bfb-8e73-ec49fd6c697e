/**
 * LangGraph Tool Debugging Utilities
 * 
 * Comprehensive debugging tools for identifying and fixing LangGraph ReactAgent issues,
 * specifically the "Cannot set properties of undefined (setting 'name')" error.
 */

import { createLogger } from '../../../utils/logger.ts';

const logger = createLogger('ToolDebugger');

/**
 * Debug tools for LangGraph ReactAgent compatibility
 * Identifies undefined/null tools that cause "Cannot set properties of undefined" errors
 */
export function debugToolsForLangGraph(tools) {
    const report = {
        totalTools: 0,
        validTools: [],
        undefinedTools: [],
        nullTools: [],
        invalidTools: [],
        missingProperties: [],
        recommendations: []
    };
    
    // Check if tools is a valid array
    if (!Array.isArray(tools)) {
        report.recommendations.push(`❌ Tools is not an array (type: ${typeof tools}). Expected array of tool objects.`);
        if (tools === undefined) {
            report.recommendations.push('💡 Fix: Ensure tools are properly imported/registered before agent creation.');
        } else if (tools === null) {
            report.recommendations.push('💡 Fix: Check tool registration - tools array should not be null.');
        }
        return report;
    }
    
    report.totalTools = tools.length;
    
    if (tools.length === 0) {
        report.recommendations.push('⚠️ No tools provided - ReactAgent will have no tool calling capability.');
        return report;
    }
    
    for (let i = 0; i < tools.length; i++) {
        const tool = tools[i];
        
        // Check for undefined tools
        if (tool === undefined) {
            report.undefinedTools.push({
                index: i,
                issue: 'Tool is undefined',
                recommendation: 'Check tool registration/import process'
            });
            continue;
        }
        
        // Check for null tools
        if (tool === null) {
            report.nullTools.push({
                index: i,
                issue: 'Tool is null',
                recommendation: 'Verify tool creation succeeded'
            });
            continue;
        }
        
        // Check if tool is an object
        if (typeof tool !== 'object') {
            report.invalidTools.push({
                index: i,
                issue: `Tool is not an object (type: ${typeof tool})`,
                value: tool,
                recommendation: 'Tools must be objects with name, description, schema, and invoke properties'
            });
            continue;
        }
        
        // Check for missing critical properties
        const missingProps = [];
        if (!tool.name) missingProps.push('name');
        if (!tool.description) missingProps.push('description');
        if (!tool.schema) missingProps.push('schema');
        if (!tool.invoke && !tool.func) missingProps.push('invoke/func');
        
        if (missingProps.length > 0) {
            report.missingProperties.push({
                index: i,
                toolName: tool.name || `tool_${i}`,
                missingProperties: missingProps,
                issues: [`Missing properties: ${missingProps.join(', ')}`],
                recommendation: `Add missing properties: ${missingProps.join(', ')}`
            });
        }
        
        // Validate invoke method type
        const invokeMethod = tool.invoke || tool.func;
        if (invokeMethod && typeof invokeMethod !== 'function') {
            report.invalidTools.push({
                index: i,
                toolName: tool.name || `tool_${i}`,
                issue: `invoke method is not a function (type: ${typeof invokeMethod})`,
                issues: [`invoke method is not a function (type: ${typeof invokeMethod})`],
                recommendation: 'The invoke property must be a function'
            });
            continue;
        }
        
        // If we get here, tool is valid
        report.validTools.push({
            index: i,
            name: tool.name,
            hasDescription: !!tool.description,
            hasSchema: !!tool.schema,
            hasInvoke: !!(tool.invoke || tool.func)
        });
    }
    
    // Generate specific recommendations
    if (report.undefinedTools.length > 0) {
        report.recommendations.push('🚨 CRITICAL: Found undefined tools - this will cause "Cannot set properties of undefined (setting \'name\')" error');
        report.recommendations.push('💡 Fix: Check async tool imports and ensure all tools are properly initialized');
    }
    
    if (report.nullTools.length > 0) {
        report.recommendations.push('⚠️ Found null tools - these will be filtered out');
        report.recommendations.push('💡 Fix: Check tool creation functions for null returns');
    }
    
    if (report.invalidTools.length > 0) {
        report.recommendations.push('⚠️ Found invalid tools - these will be filtered out');
        report.recommendations.push('💡 Fix: Ensure all tools have proper object structure');
    }
    
    if (report.missingProperties.length > 0) {
        report.recommendations.push('⚠️ Found tools with missing properties - these may be filtered out');
        report.recommendations.push('💡 Fix: Add missing name, description, schema, and invoke properties');
    }
    
    if (report.validTools.length === 0) {
        report.recommendations.push('🚨 NO VALID TOOLS - ReactAgent will have no tool calling capability');
        report.recommendations.push('💡 Fix: Review tool registration process and ensure at least one valid tool');
    } else {
        report.recommendations.push(`✅ Found ${report.validTools.length} valid tools for ReactAgent`);
    }
    
    return report;
}

/**
 * Debug model tool compatibility
 */
export function debugModelToolCompatibility(model) {
    const report = {
        modelInfo: {},
        compatibility: {},
        recommendations: []
    };
    
    if (!model) {
        report.recommendations.push('❌ Model is undefined/null');
        return report;
    }
    
    report.modelInfo = {
        name: model.constructor?.name || 'Unknown',
        hasBindTools: typeof model.bindTools === 'function',
        hasInvoke: typeof model.invoke === 'function',
        type: typeof model
    };
    
    // Check for required methods
    if (!report.modelInfo.hasBindTools) {
        report.compatibility.bindTools = false;
        report.recommendations.push('❌ Model does not support bindTools() - tool calling will fail');
        report.recommendations.push('💡 Fix: Ensure model supports tool binding (e.g., ChatOpenAI, AliyunHttpChatModel)');
    } else {
        report.compatibility.bindTools = true;
        report.recommendations.push('✅ Model supports tool binding');
    }
    
    if (!report.modelInfo.hasInvoke) {
        report.compatibility.invoke = false;
        report.recommendations.push('❌ Model does not support invoke() - basic functionality will fail');
    } else {
        report.compatibility.invoke = true;
        report.recommendations.push('✅ Model supports invoke method');
    }
    
    return report;
}

/**
 * Debug ReactAgent creation configuration
 */
export function debugReactAgentCreation(tools, model, config = {}) {
    const report = {
        toolsReport: null,
        modelReport: null,
        configReport: {},
        overallStatus: 'unknown',
        recommendations: []
    };
    
    // Debug tools
    report.toolsReport = debugToolsForLangGraph(tools);
    
    // Debug model
    report.modelReport = debugModelToolCompatibility(model);
    
    // Debug configuration
    report.configReport = {
        hasCheckpointer: !!config.checkpointSaver,
        hasStore: !!config.store,
        hasMessageModifier: !!config.messageModifier,
        toolCount: Array.isArray(tools) ? tools.length : 0
    };
    
    // Determine overall status
    const hasValidTools = report.toolsReport.validTools.length > 0;
    const modelSupportsTools = report.modelReport.compatibility.bindTools;
    const noUndefinedTools = report.toolsReport.undefinedTools.length === 0;
    
    if (hasValidTools && modelSupportsTools && noUndefinedTools) {
        report.overallStatus = 'ready';
        report.recommendations.push('✅ ReactAgent creation should succeed');
    } else if (!noUndefinedTools) {
        report.overallStatus = 'critical_error';
        report.recommendations.push('🚨 CRITICAL: Undefined tools will cause ReactAgent creation to fail');
        report.recommendations.push('💡 IMMEDIATE FIX: Use skipInvalidTools: true in validation config');
    } else if (!hasValidTools) {
        report.overallStatus = 'no_tools';
        report.recommendations.push('⚠️ ReactAgent will work but have no tool calling capability');
    } else if (!modelSupportsTools) {
        report.overallStatus = 'model_incompatible';
        report.recommendations.push('❌ Model does not support tool binding - use different model');
    } else {
        report.overallStatus = 'degraded';
        report.recommendations.push('⚠️ ReactAgent may work with reduced functionality');
    }
    
    return report;
}

/**
 * Generate comprehensive debug report
 */
export function generateComprehensiveDebugReport(tools, model, config = {}) {
    const timestamp = new Date().toISOString();
    const report = {
        timestamp,
        summary: {},
        details: {},
        actionItems: []
    };
    
    // Get detailed reports
    const agentReport = debugReactAgentCreation(tools, model, config);
    
    report.details = agentReport;
    
    // Generate summary
    report.summary = {
        status: agentReport.overallStatus,
        totalTools: agentReport.toolsReport?.totalTools || 0,
        validTools: agentReport.toolsReport?.validTools?.length || 0,
        undefinedTools: agentReport.toolsReport?.undefinedTools?.length || 0,
        modelCompatible: agentReport.modelReport?.compatibility?.bindTools || false
    };
    
    // Generate action items
    if (agentReport.overallStatus === 'critical_error') {
        report.actionItems.push({
            priority: 'CRITICAL',
            action: 'Fix undefined tools',
            description: 'Add null checks and tool validation before ReactAgent creation',
            code: `// Add this validation:\nconst validatedTools = validateToolsForLangGraph(tools, { skipInvalidTools: true });`
        });
    }
    
    if (agentReport.toolsReport?.undefinedTools?.length > 0) {
        report.actionItems.push({
            priority: 'HIGH',
            action: 'Debug tool registration',
            description: 'Check which tools are failing to initialize',
            code: `// Debug tool registration:\nconsole.log('Tools:', tools.map((t, i) => ({ index: i, name: t?.name, type: typeof t })));`
        });
    }
    
    if (agentReport.toolsReport?.validTools?.length === 0) {
        report.actionItems.push({
            priority: 'MEDIUM',
            action: 'Add fallback tools',
            description: 'Ensure at least one basic tool is available',
            code: `// Add fallback tool:\nconst fallbackTool = { name: 'basic_response', description: 'Basic response tool', schema: {}, invoke: async () => 'I understand.' };`
        });
    }
    
    return report;
}

export default {
    debugToolsForLangGraph,
    debugModelToolCompatibility,
    debugReactAgentCreation,
    generateComprehensiveDebugReport
};