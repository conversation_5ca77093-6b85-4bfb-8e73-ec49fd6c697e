/**
 * LangGraph Integration Configuration
 * 
 * IMPORTANT: This is the single source of truth for LangGraph and Dual Brain configuration.
 * All components (core.js, SystemInvoker.js, DualBrainCoordinator.js) should use this config.
 * 
 * Centralized configuration for:
 * - LangGraph tool calling and ReactAgent setup
 * - Dual brain coordination and routing
 * - System invoker configuration
 * - Aliyun API integration settings
 * 
 * Addresses critical issues:
 * - "Cannot set properties of undefined (setting 'name')" errors
 * - Connection timeout mismatches with Aliyun API requirements
 * - Tool validation failures in LangGraph ReactAgent
 * - Dual brain routing conflicts
 */

import { createLogger } from '../../utils/logger.ts';

const logger = createLogger('LangGraphConfig');

/**
 * LangGraph ReactAgent Configuration
 */
export const LANGGRAPH_CONFIG = {
    // Tool validation settings
    toolValidation: {
        enabled: true,
        requireName: true,
        requireDescription: true,
        requireSchema: true,
        requireInvokeMethod: true,
        defaultSchema: { type: 'object', properties: {} },
        skipInvalidTools: true
    },

    // ReactAgent creation settings
    reactAgent: {
        enableRetry: true,
        maxRetries: 3,
        retryDelay: 1000,
        validateToolBinding: true,
        gracefulDegradation: true
    },

    // Tool choice strategies
    toolChoice: {
        default: 'auto',
        avatarMode: 'auto',  // For avatar applications with speaking tools
        requiredFallback: 'auto'  // Convert 'required' to 'auto' for compatibility
    },

    // Error handling
    errorHandling: {
        mode: 'graceful',
        fallbackToDirectModel: true,
        logValidationWarnings: true,
        throwOnCriticalErrors: true
    }
};

/**
 * Dual Brain System Configuration
 */
export const DUAL_BRAIN_CONFIG = {
    // System routing configuration
    routing: {
        // System 1 (Fast Brain) - WebSocket realtime with dynamic modalities
        system1: {
            capabilities: ['multimodalOutput', 'realtime', 'fastResponse'],
            useFor: ['simple_queries', 'quick_interactions', 'text_analysis_for_system2'],
            skipMemoryContext: true,
            enableToolCalling: false,  // System 1 focuses on speed
            // Modality control
            defaultModalities: ['text'],  // Default to text-only for System 2 analysis
            audioModalities: ['text', 'audio'],  // When audio output is needed
            nativeAudioCapability: true  // System 1 can generate audio natively
        },

        // System 2 (Reasoning Brain) - HTTP with tools (controls System 1 audio)
        system2: {
            capabilities: ['tools', 'thinking', 'complexReasoning', 'modalityControl'], 
            useFor: ['complex_queries', 'tool_calling', 'analysis', 'proactive_actions'],
            includeMemoryContext: true,
            enableToolCalling: true, // Controls when System 1 should output audio
            enableThinking: true,
            // Audio control responsibility
            controlsSystem1Audio: true,  // System 2 decides when audio is needed
            canTriggerAudioResponse: true  // Can trigger System 1 audio via tools
        }
    },

    // Simple modality control (System 1 has audio capability, System 2 controls when to use it)
    modalityControl: {
        // Default: text-only for System 2 analysis, text+audio when speaking tools activate
        defaultModalities: ['text'],           // Default for analysis
        speakingModalities: ['text', 'audio'],  // When speaking is needed
        defaultVoice: 'Chelsie'
    },

    // Complexity analysis thresholds
    complexity: {
        simple: {
            maxLength: 50,
            keywords: [],
            hasQuestions: false
        },
        medium: {
            maxLength: 200,
            keywords: ['analyze', 'explain', 'compare', 'plan'],
            hasQuestions: true
        },
        high: {
            minLength: 200,
            keywords: ['think', 'reason', 'complex', 'detailed'],
            requiresThinking: true
        }
    },

    // No-activity monitoring
    noActivity: {
        enabled: true,
        patterns: {
            quiet_period: {
                threshold: 10000,    // 10 seconds
                action: 'contextual_engagement',
                triggers: ['user-profile-analysis', 'avatar-expression', 'proactive-conversation']
            },
            extended_quiet: {
                threshold: 30000,    // 30 seconds
                action: 'ambient_animation',
                triggers: ['character-idle-behavior', 'environmental-awareness']
            },
            long_silence: {
                threshold: 120000,   // 2 minutes
                action: 'check_in',
                triggers: ['user-wellness-check', 'topic-suggestion']
            }
        }
    }
};

/**
 * SystemInvoker Configuration
 */
export const SYSTEM_INVOKER_CONFIG = {
    // Method invocation preferences
    methods: {
        preferredAgentMethod: 'generateResponse',  // Use generateResponse over processInput
        fallbackToDirectModel: true,
        validateInputs: true,
        validateOutputs: true
    },

    // Retry and timeout settings (Aliyun-compliant)
    reliability: {
        defaultTimeout: 120000,      // 2 minutes (Aliyun minimum)
        retryAttempts: 3,
        retryDelayMs: 1000,          // 1 second between retries
        dualBrainTimeout: 180000     // 3 minutes for complex reasoning
    },

    // Tool calling configuration
    toolCalling: {
        enabled: true,
        useAgentService: true,
        fallbackToDirectModel: true,
        validateTools: true,
        enhancedLogging: true
    }
};

/**
 * Aliyun-Specific Configuration Overrides
 */
export const ALIYUN_INTEGRATION_CONFIG = {
    // Timeout overrides for Aliyun compliance
    timeouts: {
        connection: 120000,          // 2 minutes minimum
        read: 300000,               // 5 minutes for complex operations
        handshake: 180000,          // 3 minutes for WebSocket
        dualBrain: 180000           // 3 minutes for System 2 reasoning
    },

    // Retry settings
    retry: {
        delay: 1000,                // 1 second (was 100ms)
        maxAttempts: 2,
        backoffMultiplier: 2
    },

    // Model selection for different capabilities
    models: {
        toolCalling: 'qwen-turbo',                    // HTTP API for tools
        realtimeAudio: 'qwen-omni-turbo-realtime-latest',  // WebSocket for audio
        complexReasoning: 'qwen-plus'                 // HTTP API for advanced analysis
    },

    // Connection pool settings (Java SDK compatible)
    connectionPool: {
        maxConnections: 100,
        connectionTimeout: 120000,
        socketTimeout: 300000,
        keepAliveTimeout: 300000,
        maxIdleTime: 600000
    }
};

/**
 * Simple tool validation for LangGraph compatibility
 * Prevents "Cannot set properties of undefined (setting 'name')" errors
 */
export function validateToolsForLangGraph(tools) {
    if (!Array.isArray(tools)) {
        logger.warn('Tools is not an array, returning empty array');
        return [];
    }
    
    const validTools = tools.filter(tool => {
        if (!tool || typeof tool !== 'object') return false;
        if (!tool.name || typeof tool.name !== 'string') return false;
        if (!tool.invoke && !tool.func) return false;
        if (typeof (tool.invoke || tool.func) !== 'function') return false;
        return true;
    });
    
    logger.info(`✅ Validated ${validTools.length}/${tools.length} tools for LangGraph`);
    return validTools;
}

/**
 * Get tool choice strategy based on context
 */
export function getToolChoiceStrategy(tools, context = {}) {
    const hasSpeakingTool = tools.some(tool => tool.name === 'control_avatar_speech');
    const config = LANGGRAPH_CONFIG.toolChoice;
    
    // For avatar applications, use 'auto' for contextual decisions
    if (hasSpeakingTool || context.avatarMode) {
        return config.avatarMode;
    }
    
    // Convert 'required' to 'auto' for compatibility
    if (context.toolChoiceStrategy === 'required') {
        return config.requiredFallback;
    }
    
    return context.toolChoiceStrategy || config.default;
}

/**
 * Determine system routing based on input complexity
 */
export function determineSystemRouting(input, options = {}) {
    const { complexity: configComplexity } = DUAL_BRAIN_CONFIG;
    
    if (!input || typeof input !== 'string') {
        return { targetSystem: 'system2', reason: 'Invalid input requires reasoning' };
    }
    
    const length = input.length;
    const hasQuestions = /\?/.test(input);
    const hasComplexKeywords = new RegExp(
        configComplexity.medium.keywords.join('|'), 'i'
    ).test(input);
    const hasHighComplexityKeywords = new RegExp(
        configComplexity.high.keywords.join('|'), 'i'
    ).test(input);
    
    // Force System 2 for tool calling (including speaking) and reasoning
    if (options.isProactive || options.requiresReasoning || options.enableToolCalling || options.needsSpeaking) {
        return {
            targetSystem: 'system2',
            reason: `Tool calling required (${options.isProactive ? 'proactive' : options.requiresReasoning ? 'reasoning' : options.needsSpeaking ? 'speaking' : 'tool-calling'})`,
            capabilities: DUAL_BRAIN_CONFIG.routing.system2.capabilities
        };
    }
    
    // Simple queries to System 1
    if (length <= configComplexity.simple.maxLength && !hasQuestions && !hasComplexKeywords) {
        return {
            targetSystem: 'system1',
            reason: 'Simple query suitable for fast response',
            capabilities: DUAL_BRAIN_CONFIG.routing.system1.capabilities
        };
    }
    
    // Complex queries to System 2
    if (length >= configComplexity.high.minLength || hasHighComplexityKeywords || options.complexity === 'high') {
        return {
            targetSystem: 'system2',
            reason: 'Complex analysis required',
            capabilities: DUAL_BRAIN_CONFIG.routing.system2.capabilities
        };
    }
    
    // Default to System 2 for medium complexity
    return {
        targetSystem: 'system2',
        reason: 'Medium complexity requires reasoning',
        capabilities: DUAL_BRAIN_CONFIG.routing.system2.capabilities
    };
}

/**
 * Create LangGraph-compatible agent options
 */
export function createLangGraphOptions(context = {}) {
    return {
        useSystem2: context.targetSystem === 'system2',
        enableTools: context.enableToolCalling || context.capabilities?.includes('tools'), // Tools include speaking
        context: context.context || {},
        streaming: context.streaming || false,
        
        // LangGraph-specific configuration
        langgraph: {
            mode: 'react_agent',
            toolBinding: 'enhanced',
            errorHandling: LANGGRAPH_CONFIG.errorHandling.mode,
            toolValidation: LANGGRAPH_CONFIG.toolValidation.enabled
        },
        
        // Dual brain context
        dualBrainContext: {
            isSystem2: context.targetSystem === 'system2',
            supervisorRouting: true,
            enableToolCalling: context.enableToolCalling || false,
            toolValidation: true,
            capabilities: context.capabilities || []
        }
    };
}

/**
 * Simple modality determination: text-only by default, text+audio when speaking tools activate
 */
export function determineSystem1Modalities(speakingToolActivated = false) {
    const { modalityControl } = DUAL_BRAIN_CONFIG;
    
    // Simple logic: audio only when speaking tools are explicitly activated
    if (speakingToolActivated) {
        return {
            modalities: modalityControl.speakingModalities,
            voice: modalityControl.defaultVoice,
            reason: 'Speaking tool activated'
        };
    } else {
        return {
            modalities: modalityControl.defaultModalities,
            voice: null,
            reason: 'Default text-only for analysis'
        };
    }
}

/**
 * Create Aliyun session.update event for modality control
 */
export function createAliyunModalityUpdate(modalities, options = {}) {
    const baseConfig = {
        type: "session.update",
        event_id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        session: {
            modalities: modalities.modalities,
            input_audio_format: "pcm16",
            output_audio_format: modalities.output_audio_format || "pcm16",
            input_audio_transcription: {
                model: "gummy-realtime-v1"
            },
            turn_detection: {
                type: "server_vad",
                threshold: 0.1,
                prefix_padding_ms: 500,
                silence_duration_ms: 900
            },
            ...options
        }
    };
    
    // Add voice only if audio is enabled
    if (modalities.modalities.includes('audio') && modalities.voice) {
        baseConfig.session.voice = modalities.voice;
    }
    
    return baseConfig;
}

export default {
    LANGGRAPH_CONFIG,
    DUAL_BRAIN_CONFIG,
    SYSTEM_INVOKER_CONFIG,
    ALIYUN_INTEGRATION_CONFIG,
    validateToolsForLangGraph,
    getToolChoiceStrategy,
    determineSystemRouting,
    createLangGraphOptions,
    determineSystem1Modalities,
    createAliyunModalityUpdate
};