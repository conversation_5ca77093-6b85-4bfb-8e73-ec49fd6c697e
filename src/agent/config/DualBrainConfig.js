/**
 * CONSOLIDATED Dual Brain Configuration
 * SINGLE GROUND TRUTH for all dual brain system settings
 * 
 * This file replaces scattered dual brain configurations from:
 * - AgentConfig.js (modalityControl section) 
 * - LangGraphConfig.js (DUAL_BRAIN_CONFIG section)
 * - Individual component configurations
 * 
 * CRITICAL: All dual brain components MUST use this configuration
 */

import { createLogger, LogLevel } from '../../utils/logger.ts';

const logger = createLogger('DualBrainConfig');

/**
 * UNIFIED Dual Brain System Configuration
 * Contains ALL settings for System 1, System 2, and coordination
 */
export const DUAL_BRAIN_CONFIG = {
    // === CORE SYSTEM DEFINITIONS ===
    systems: {
        // System 1 (Fast Brain) - WebSocket realtime with controlled modalities
        system1: {
            type: 'websocket',
            model: 'qwen-turbo',
            capabilities: ['audioInput', 'textOutput', 'realtime', 'sceneAnalysis'],
            useFor: ['audio_analysis', 'scene_description', 'fast_responses'],
            
            // Modality Control (CONSOLIDATED)
            modalities: {
                // Phase 1: Analysis mode (text-only output for System 2)
                analysis: ['text'],
                // Phase 2: Response mode (full audio output when triggered)
                response: ['text', 'audio']
            },
            
            // Performance settings
            skipMemoryContext: true,
            enableToolCalling: false,  // System 1 focuses on speed and analysis
            timeout: 5000,            // Fast response requirement
            maxRetries: 2
        },

        // System 2 (Reasoning Brain) - HTTP with tool calling (controls audio decisions)
        system2: {
            type: 'http', 
            model: 'qwen-turbo',
            capabilities: ['tools', 'thinking', 'complexReasoning', 'audioControl'],
            useFor: ['tool_calling', 'complex_analysis', 'decision_making', 'audio_activation'],
            
            // Tool control and audio decisions
            enableToolCalling: true,   // Controls when System 1 should output audio
            enableThinking: true,      // Deep reasoning mode
            controlsAudioOutput: true, // Can trigger System 1 audio via tools
            
            // Performance settings
            includeMemoryContext: true,
            timeout: 30000,
            maxRetries: 3
        }
    },

    // === TWO-PHASE PROCESSING CONFIGURATION ===
    twoPhaseProcessing: {
        enabled: true,
        
        // Phase 1: Audio Input → System 1 Text Analysis
        phase1: {
            target: 'system1',
            purpose: 'scene_analysis',
            modalities: ['text'],      // CRITICAL: Text-only for System 2 analysis
            skipAudioOutput: true,
            timeout: 5000,
            promptTemplate: `CONTEXT: Analyze the current scene and user interaction.

AUDIO INPUT: {audioStatus}
VIDEO CONTEXT: {videoStatus} 
ENVIRONMENT: Real-time environmental context analysis

TASK: Generate a concise text description of:
1. What the user is doing/saying (if audio detected)
2. Current scene context and environment  
3. User interaction intent and engagement level
4. Relevant contextual factors for decision-making

OUTPUT: Return ONLY a clear, factual description for System 2 decision-making.
Format: "User is [action]. Scene shows [context]. Intent appears to be [purpose]."`
        },

        // Phase 2: System 2 Decision Making → Tool Calling
        phase2: {
            target: 'system2',
            purpose: 'speaking_decision',
            enableToolCalling: true,
            enableSpeaking: true,
            timeout: 15000,
            promptTemplate: `SCENE ANALYSIS: {sceneDescription}

CONTEXT: {originalContext}

DECISION REQUIRED:
Based on this scene analysis, should the avatar respond with speech or actions?

Consider:
- User engagement level and interaction intent
- Appropriateness of audio response timing  
- Content relevance and environmental factors
- Whether user seems to expect a response

If speaking/action is appropriate:
1. Use available tools (avatar_speak, speech_control, select_animation)
2. Provide appropriate content and tone
3. Consider user context and preferences

If no action needed:
- Respond with brief acknowledgment only
- Do not activate speaking tools unnecessarily

TOOLS AVAILABLE: {availableTools}`
        }
    },

    // === ROUTING CONFIGURATION ===
    routing: {
        // Audio input routing (triggers two-phase processing)
        audioInput: {
            targetSystem: 'system1',
            reason: 'audio_input_routing',
            capabilities: ['audioInput', 'textOutput'],
            modalityOverride: ['text'],  // Force text-only for analysis
            forAnalysis: true,
            useRealtime: true
        },

        // Complexity-based routing thresholds
        complexity: {
            simple: {
                maxLength: 50,
                keywords: [],
                hasQuestions: false,
                targetSystem: 'system1'
            },
            medium: {
                maxLength: 200, 
                keywords: ['analyze', 'explain', 'compare', 'plan'],
                hasQuestions: true,
                targetSystem: 'system2'
            },
            high: {
                minLength: 200,
                keywords: ['think', 'reason', 'complex', 'detailed'],
                requiresThinking: true,
                targetSystem: 'system2'
            }
        }
    },

    // === NO-ACTIVITY MONITORING ===
    noActivity: {
        enabled: true,
        patterns: {
            quiet_period: {
                threshold: 10000,    // 10 seconds
                action: 'contextual_engagement',
                triggers: ['user-profile-analysis', 'avatar-expression', 'proactive-conversation'],
                usesTwoPhase: true   // Routes through two-phase processing
            },
            extended_quiet: {
                threshold: 30000,    // 30 seconds  
                action: 'ambient_animation',
                triggers: ['character-idle-behavior', 'environmental-awareness'],
                usesTwoPhase: false  // Direct animation, no audio needed
            },
            long_silence: {
                threshold: 120000,   // 2 minutes
                action: 'check_in', 
                triggers: ['user-wellness-check', 'topic-suggestion'],
                usesTwoPhase: true   // May need audio response
            }
        }
    },

    // === COORDINATION SETTINGS ===
    coordination: {
        // SystemInvoker configuration
        systemInvoker: {
            validateInputs: true,
            validateOutputs: true,
            retryAttempts: 2,
            retryDelayMs: 1000,
            dualBrainTimeout: 30000
        },

        // Context bridging between systems
        contextBridge: {
            enabled: true,
            maxContextSize: 2000,
            includeMetadata: true,
            preserveOriginalInput: true
        },

        // Decision processor settings
        decisionProcessor: {
            enabled: true,
            processToolCalls: true,
            processSpeaking: true,
            timeout: 10000
        }
    },

    // === PERFORMANCE & RELIABILITY ===
    performance: {
        // Circuit breaker for system health
        circuitBreaker: {
            failureThreshold: 5,
            resetTimeout: 60000,
            monitorTimeout: 30000
        },

        // Performance targets
        targets: {
            phase1Latency: 5000,     // System 1 analysis target
            phase2Latency: 15000,    // System 2 decision target  
            totalLatency: 20000,     // Overall two-phase target
            successRate: 0.95        // Target success rate
        },

        // Optimization settings
        optimization: {
            enableAdaptiveRouting: true,
            enableLoadBalancing: false,  // Single brain systems
            enableCaching: true,
            cacheSize: 100
        }
    }
};

/**
 * UTILITY FUNCTIONS for Configuration Access
 */

/**
 * Get system configuration by type
 * @param {string} systemType - 'system1' or 'system2'
 * @returns {Object} System configuration
 */
export function getSystemConfig(systemType) {
    const config = DUAL_BRAIN_CONFIG.systems[systemType];
    if (!config) {
        throw new Error(`Unknown system type: ${systemType}`);
    }
    return { ...config };
}

/**
 * Get modality configuration for specific system and phase
 * @param {string} systemType - 'system1' or 'system2'  
 * @param {string} phase - 'analysis' or 'response'
 * @returns {Array} Modality array
 */
export function getSystemModalities(systemType, phase = 'response') {
    const system = getSystemConfig(systemType);
    
    if (systemType === 'system1' && system.modalities) {
        return system.modalities[phase] || system.modalities.response;
    }
    
    // System 2 uses text by default, audio controlled via tools
    return ['text'];
}

/**
 * Get routing configuration for input type
 * @param {string} inputType - 'audio', 'text', etc.
 * @returns {Object} Routing configuration
 */
export function getRoutingConfig(inputType) {
    if (inputType === 'audio' || inputType === 'audioInput') {
        return { ...DUAL_BRAIN_CONFIG.routing.audioInput };
    }
    
    // Default routing based on complexity
    return {
        targetSystem: 'system2',
        reason: 'default_routing',
        capabilities: ['tools', 'thinking'],
        useRealtime: false
    };
}

/**
 * Get two-phase processing configuration
 * @param {string} phase - 'phase1' or 'phase2' 
 * @returns {Object} Phase configuration
 */
export function getTwoPhaseConfig(phase) {
    const config = DUAL_BRAIN_CONFIG.twoPhaseProcessing[phase];
    if (!config) {
        throw new Error(`Unknown phase: ${phase}`);
    }
    return { ...config };
}

/**
 * Get no-activity pattern configuration
 * @param {string} patternName - Pattern name 
 * @returns {Object} Pattern configuration
 */
export function getNoActivityConfig(patternName) {
    const pattern = DUAL_BRAIN_CONFIG.noActivity.patterns[patternName];
    if (!pattern) {
        throw new Error(`Unknown no-activity pattern: ${patternName}`);
    }
    return { ...pattern };
}

/**
 * Validate dual brain configuration
 * @returns {Object} Validation result
 */
export function validateDualBrainConfig() {
    const errors = [];
    
    // Validate system configurations
    ['system1', 'system2'].forEach(systemType => {
        try {
            const config = getSystemConfig(systemType);
            if (!config.capabilities || !Array.isArray(config.capabilities)) {
                errors.push(`${systemType} must have capabilities array`);
            }
            if (!config.useFor || !Array.isArray(config.useFor)) {
                errors.push(`${systemType} must have useFor array`);
            }
        } catch (error) {
            errors.push(`${systemType} configuration invalid: ${error.message}`);
        }
    });

    // Validate two-phase processing
    if (!DUAL_BRAIN_CONFIG.twoPhaseProcessing.enabled) {
        errors.push('Two-phase processing must be enabled for proper modality control');
    }

    // Validate modality consistency
    const system1Analysis = getSystemModalities('system1', 'analysis');
    if (system1Analysis.includes('audio')) {
        errors.push('System 1 analysis phase must not include audio modality');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Get complete dual brain configuration with validation
 * @param {Object} overrides - Configuration overrides
 * @returns {Object} Complete configuration
 */
export function getCompleteDualBrainConfig(overrides = {}) {
    // Validate configuration
    const validation = validateDualBrainConfig();
    if (!validation.isValid) {
        logger.warn('Dual brain configuration validation failed:', validation.errors);
    }

    // Apply overrides
    const config = {
        ...DUAL_BRAIN_CONFIG,
        ...overrides
    };

    logger.debug('Generated complete dual brain configuration:', {
        twoPhaseEnabled: config.twoPhaseProcessing.enabled,
        system1Capabilities: config.systems.system1.capabilities,
        system2Capabilities: config.systems.system2.capabilities,
        hasOverrides: Object.keys(overrides).length > 0
    });

    return config;
}

// Export utility functions for easy access
export const DualBrainUtils = {
    getSystemConfig,
    getSystemModalities, 
    getRoutingConfig,
    getTwoPhaseConfig,
    getNoActivityConfig,
    validateDualBrainConfig,
    getCompleteDualBrainConfig
};

export default {
    DUAL_BRAIN_CONFIG,
    ...DualBrainUtils
};