/**
 * AnimationCore.js
 * Core animation functionality and constants
 * Centralizes common animation utilities and configuration
 */

import * as THREE from 'three';
import { createLogger } from '@/utils/logger';

const logger = createLogger('AnimationCore');

// Animation timing constants
export const ANIMATION_TIMING = {
    FRAME_DURATION: 16.67, // ~60fps
    BLEND_SPEEDS: {
        FAST: 0.15,
        NORMAL: 0.08,
        SLOW: 0.04,
        RETURN_TO_NEUTRAL: 0.12, // Faster return to prevent "hanging" hands
    },
    GESTURE_DURATIONS: {
        WAVE_RAISE: 800,
        WAVE_HOLD: 400,
        WAVE_RETURN: 600, // Shorter return for quicker hand drop
        SPEAKING_TRANSITION: 1000,
    }
};

// IK target validation
export const IK_VALIDATION = {
    STRICT: {
        ARM_LIMIT: Math.PI / 2.5,     // 72°
        FOREARM_LIMIT: Math.PI * 0.6, // 108°
        HAND_LIMIT: Math.PI / 4       // 45°
    },
    RELAXED: {
        ARM_LIMIT: Math.PI * 0.8,     // 144°
        FOREARM_LIMIT: Math.PI * 0.9, // 162°
        HAND_LIMIT: Math.PI / 3       // 60°
    }
};

// Avatar proportions for better IK targeting
export const AVATAR_PROPORTIONS = {
    SHOULDER_WIDTH: 0.4,
    ARM_LENGTH: 0.6,
    FOREARM_LENGTH: 0.5,
    COMFORTABLE_REACH: 0.8, // Multiplier for natural reach distance
};

/**
 * Calculate natural IK targets based on avatar proportions
 * @param {string} side - 'left' or 'right'
 * @param {THREE.Vector3} shoulderPos - Shoulder world position
 * @param {string} gestureType - Type of gesture ('wave', 'speak', 'point')
 * @param {number} intensity - Gesture intensity (0-1)
 * @returns {THREE.Vector3} - Natural target position
 */
export function calculateNaturalTarget(side, shoulderPos, gestureType, intensity = 0.7) {
    const baseOffset = new THREE.Vector3();
    const sideMultiplier = side === 'left' ? -1 : 1;

    switch (gestureType) {
        case 'wave':
            // More conservative waving targets
            baseOffset.set(
                sideMultiplier * 0.15 * intensity, // Closer to body
                0.05 + (0.1 * intensity),          // Slight upward
                0.2 * intensity                     // Forward but not too much
            );
            break;

        case 'speak':
            // Natural speaking gesture positions
            baseOffset.set(
                sideMultiplier * 0.1 * intensity,
                -0.05 + (0.08 * intensity),
                0.15 * intensity
            );
            break;

        case 'point':
            // Pointing gesture
            baseOffset.set(
                sideMultiplier * 0.2 * intensity,
                0.1 * intensity,
                0.3 * intensity
            );
            break;

        default:
            // Neutral/rest position
            baseOffset.set(
                sideMultiplier * 0.05,
                -0.1,
                0.05
            );
    }

    return shoulderPos.clone().add(baseOffset);
}

/**
 * Calculate natural elbow pole target
 * @param {string} side - 'left' or 'right'
 * @param {THREE.Vector3} shoulderPos - Shoulder position
 * @param {THREE.Vector3} targetPos - Hand target position
 * @returns {THREE.Vector3} - Pole target position
 */
export function calculateElbowPoleTarget(side, shoulderPos, targetPos) {
    const shoulderToTarget = new THREE.Vector3().subVectors(targetPos, shoulderPos);
    const distance = shoulderToTarget.length() * 0.4; // Reduced multiplier for more natural elbow

    // More conservative elbow positioning
    const offset = new THREE.Vector3(
        side === 'left' ? -0.15 : 0.15,  // Less extreme outward offset
        -0.05,                           // Slightly down
        0.1                              // Less forward
    );

    const midPoint = new THREE.Vector3()
        .addVectors(shoulderPos, targetPos)
        .multiplyScalar(0.5);

    return midPoint.add(offset.multiplyScalar(distance));
}

/**
 * Validate if a quaternion represents a natural pose
 * @param {THREE.Quaternion} quaternion - Quaternion to validate
 * @param {string} boneType - Type of bone ('arm', 'forearm', 'hand')
 * @param {boolean} useRelaxed - Use relaxed validation limits
 * @returns {boolean} - Whether the pose is natural
 */
export function validateNaturalPose(quaternion, boneType, useRelaxed = false) {
    const euler = new THREE.Euler().setFromQuaternion(quaternion);
    const limits = useRelaxed ? IK_VALIDATION.RELAXED : IK_VALIDATION.STRICT;

    let limit;
    switch (boneType) {
        case 'arm':
            limit = limits.ARM_LIMIT;
            break;
        case 'forearm':
            limit = limits.FOREARM_LIMIT;
            break;
        case 'hand':
            limit = limits.HAND_LIMIT;
            break;
        default:
            return true;
    }

    // Check if any rotation exceeds natural limits
    return Math.abs(euler.x) <= limit &&
        Math.abs(euler.y) <= limit &&
        Math.abs(euler.z) <= limit;
}

/**
 * Create smooth animation curve for natural movement
 * @param {number} t - Progress (0-1)
 * @param {string} curveType - Type of easing curve
 * @returns {number} - Eased progress value
 */
export function createAnimationCurve(t, curveType = 'easeInOutCubic') {
    switch (curveType) {
        case 'easeInOutCubic':
            return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
        case 'easeOutQuart':
            return 1 - Math.pow(1 - t, 4);
        case 'easeInOutSine':
            return -(Math.cos(Math.PI * t) - 1) / 2;
        case 'linear':
            return t;
        default:
            return t;
    }
}

export default {
    ANIMATION_TIMING,
    IK_VALIDATION,
    AVATAR_PROPORTIONS,
    calculateNaturalTarget,
    calculateElbowPoleTarget,
    validateNaturalPose,
    createAnimationCurve
}; 