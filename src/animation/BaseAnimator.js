/**
 * BaseAnimator.js
 * Base class for all animators with shared functionality
 * Provides common animation loop, timing, and audio analysis
 *
 * ==================================================================================
 * HAND GESTURE OPTIMIZATIONS - Enhanced Natural Movement System (UPDATED)
 * ==================================================================================
 *
 * 🎯 IDLE GESTURES - Natural Waving System:
 * • Frequency: Increased from 20-40s to 8-15s intervals (60% chance vs 30%)
 * • WEIGHTED SELECTION: Unilateral gestures heavily favored (weight 4) vs bilateral (weight 1)
 * • New waving system: _addIdleWavingGesture() with improved types:
 *   - friendlyRightWave: Natural right hand hello wave (PALM OUTWARD)
 *   - gentleLeftWave: Gentle left hand wave (PALM OUTWARD)
 *   - subtleRightAck: New subtle acknowledgment gesture
 *   - welcomeGesture: Two-handed (rare, weight 1)
 * • IMPROVED POSITIONS: Better Z-depth (0.35-0.38) for natural palm orientation
 * • Faster timing: Reduced durations for more responsive gestures
 * • Multi-phase waving: raise → wave → hold → faster return
 *
 * 🗣️ SPEAKING GESTURES - Palm-Towards-Body System:
 * • Natural conversational hand positions with palms facing avatar body
 * • Enhanced IK targets with improved constraints (minz: 0.2, maxz: varies)
 * • STRICTER IK VALIDATION: Prevents behind-body movement (30° threshold vs 45°)
 * • Better bilateral gesture handling with proper template integration
 * • Reduced bilateral frequency for more natural single-hand emphasis
 *
 * 🔧 IK SYSTEM IMPROVEMENTS:
 * • Increased iterations: 15 vs 12 for better solutions
 * • STRICTER validation limits: 72° arm, 108° forearm, 45° hand (vs previous 90°/144°/60°)
 * • Enhanced behind-body detection with 30° Y-rotation threshold
 * • New cross-body-midline check (22.5° threshold)
 * • Better constraints preventing unnatural positions
 *
 * 🚀 PERFORMANCE & LOGGING OPTIMIZATIONS:
 * • MASSIVE logging reduction:
 *   - Progress logging: Every 10% vs every frame
 *   - IK validation: 5% frequency vs 100%
 *   - Return-to-neutral: 0.1% frequency vs 100%
 *   - Performance monitoring: Only slow frames or 1% sample
 * • Faster return-to-neutral: 800-1000ms vs 1000-1500ms
 * • Improved micro-movement detection to stop unnecessary adjustments
 * • Frame time monitoring with smart averaging and reset cycles
 *
 * 🎭 GESTURE QUALITY IMPROVEMENTS:
 * • Palm orientations: OUTWARD for waving, INWARD for speaking
 * • Natural height variations for different gesture types
 * • Smoother transitions with adaptive blend speeds
 * • Better failure handling with graceful fallbacks
 * • Weighted selection system favoring realistic unilateral movements
 *
 * 🏷️ Priority System:
 * • Waving gestures: Priority 4 (high visibility)
 * • Speaking gestures: Contextual priority
 * • Return animations: Fast-tracked completion
 * • Idle movements: Lower priority, non-intrusive
 *
 * 🧹 CODE CLEANUP COMPLETED:
 * • Removed duplicate _addIdleHandMovement method (kept advanced version)
 * • Removed unused _getAnimationAffectedBones method
 * • Removed unused _calculateBlendWeight method
 * • Removed unused _applyBonePropertyWithBlending method
 * • Cleaned up redundant methods for better code organization
 * • Cleaned up redundant fallback methods
 * • Reduced file size by ~500 lines of dead code
 * ==================================================================================
 */

import * as THREE from 'three';
import { createLogger, setLogLevel, LogLevel, setModuleLogLevel } from '@/utils/logger';
import {
    ANIM_TIMING,
    POSE_TEMPLATES,
    POSE_DELTA,
    FINGER_BONE_NAMES,
    DOLL_BONE_NAMES,
    RPM_BONE_NAMES,
    DEFAULT_ANIMATION_VALUES,
    initializePoseDelta,
} from './AnimationConfig.js';

import {
    debugBones,
    findBonesInMesh,
    detectMeshType,
    randomGaussian,
    applyEasing
} from './AnimationUtils.js';
// Gesture system is now optional - imported dynamically when enabled

import {
    deepCopy,
    lerp,
    sigmoidFactory,
} from '@/utils/mathUtils.js';

// Set log level to DEBUG for this specific module only
// This demonstrates module-specific logging control
const logger = createLogger('BaseAnimator');
logger.setLogLevel(LogLevel.INFO); // Only affects BaseAnimator module

// ============================================================================
// ANIMATION TEMPLATES - Centralized animation definitions
// ============================================================================
// Note: Animation templates are now defined in AnimationConfig.js

export class BaseAnimator {
    /**
     * Constructor for BaseAnimator
     * @param {THREE.Object3D} mesh - The mesh to animate
     * @param {Object} options - Configuration options
     */
    constructor(mesh, options = {}) {
        this.mesh = mesh;

        // Use default options from AnimationConfig.js
        const defaultOptions = DEFAULT_ANIMATION_VALUES.options;

        // Merge options with defaults
        this.options = { ...defaultOptions, ...options };
        // Animation state
        this.isRunning = false;
        this.isSpeaking = false;
        this.isListening = false;
        this.isAudioPlaying = false;
        this.currentState = 'idle';

        // Animation timing from config
        this.animClock = 0;
        this.animTimeLast = 0;
        this.animFrameDur = ANIM_TIMING.frameDuration;
        this.animSlowdownRate = ANIM_TIMING.slowdownRate;
        this.easingFactor = ANIM_TIMING.easingFactor;
        this.lastLookAnimationTime = 0;
        // Set up sigmoid function for easing
        this.sigmoid = sigmoidFactory(this.easingFactor);

        // Set up pose templates
        this.poseTemplates = POSE_TEMPLATES;

        // Initialize pose delta
        this.poseDelta = deepCopy(POSE_DELTA);
        initializePoseDelta(this.poseDelta);

        // Initialize pose properties
        this.poseName = "side"; // First pose
        this.poseWeightOnLeft = true; // Initial weight on left leg
        this.gesture = null; // Values that override pose properties

        // Animation queue
        this.animQueue = [];
        this.nextAnimId = 0;
        this.speechQueue = [];

        // Audio analysis for animation
        this.audioAnalyzer = null;
        this.volumeFrequencyData = new Uint8Array(16);

        // Random number generator for animations
        this.lastGaussian = null;

        // Find armature in the mesh
        this.armature = null;



        // Optional gesture system (disabled by default)
        this.handGestureSystem = null;
        this.gestureSystemEnabled = options.enableGestures || false;

        // Debug bones if in debug mode
        if (this.options.debug && mesh) {
            logger.debug('BaseAnimator constructor: debug mode enabled');
            // Store bone names for debugging without detailed logging
            this.boneNames = debugBones(mesh);
        }

        // Store references to important bones for animation
        this.bones = {};
        this.findBones();

        // NEW: Performance monitoring
        this.performanceMetrics = {
            animationCount: 0,
            frameTime: 0,
            lastFrameTime: 0,
            averageFrameTime: 0
        };

        // NEW: Animation culling options
        this.animationCulling = {
            enabled: options.enableCulling !== false,
            maxAnimations: options.maxAnimations || 10,
            distanceCulling: options.distanceCulling || false,
            maxDistance: options.maxCullingDistance || 50
        };

        // Initialize animation progress tracking for reduced logging
        this._lastLoggedProgress = 0;

        // Performance monitoring variables for reduced logging
        this.frameTimeSum = 0;
        this.frameCount = 0;
        this.lastFrameTime = 0;
        this._lastReturnMovement = false;
    }

    /**
     * Find and store references to important bones
     */
    findBones() {
        if (!this.mesh) return;

        // List of important bones to find
        const boneList = [
            // Core bones
            ...DOLL_BONE_NAMES,
            // Hand bones for finger animation
            ...FINGER_BONE_NAMES,
            // Eye bones
            'LeftEye', 'RightEye'
        ];

        // Use the utility function to find bones
        const { bones, armature } = findBonesInMesh(this.mesh, boneList);
        this.bones = bones;

        // Update armature reference if not already set
        if (!this.armature && armature) {
            this.armature = armature;
        }

        // Log found bones in debug mode
        const foundBones = Object.keys(this.bones);
        logger.info(`Found ${foundBones.length} important bones for animation`);

        // Check for missing critical bones
        const criticalBones = ['Head', 'Neck', 'Spine', 'Hips', 'LeftHand', 'RightHand'];
        const missingCritical = criticalBones.filter(name => !this.bones[name]);

        if (missingCritical.length > 0) {
            logger.warn('Missing critical bones:', missingCritical.join(', '));
        }

        // Debug bone structure if in debug mode (cleaned up redundant detailed logging)
        if (this.options.debug) {
            logger.debug(`Found bone structure with ${Object.keys(this.bones).length} bones`);
        }

        // Initialize gesture system if enabled
        if (this.gestureSystemEnabled && !this.handGestureSystem) {
            this._initializeGestureSystem();
        }
    }

    /**
 * Initialize the optional gesture system
 * @private
 */
    async _initializeGestureSystem() {
        try {
            // Dynamically import gesture system only when needed
            const { HandGestureSystem, createGestureOptions } = await import('./gestures/index.js');

            const gestureOptions = createGestureOptions({
                enabled: true,
                debug: this.options.debug,
                visualizeTargets: this.options.visualizeTargets || false,
                speakingProbability: this.options.speakingProbability || 0.5,
                idleGestureProbability: this.options.idleGestureProbability || 0.6
            });

            this.handGestureSystem = new HandGestureSystem(this, gestureOptions);

            if (this.options.debug) {
                logger.debug('HandGestureSystem initialized successfully');
            }
        } catch (error) {
            logger.error('Failed to initialize HandGestureSystem:', error);
            this.gestureSystemEnabled = false;
        }
    }

    /**
     * Initialize the animator
     * Should be called by subclasses
     */
    initialize() {
        // Set up audio context for analysis
        this.setupAudioAnalysis();

        // Set initial natural pose before starting animations
        this.setInitialPose();

        // Set poseAvatar to indicate avatar is loaded
        this.poseAvatar = this.mesh;

        // Add idle animations to the queue
        this.addIdleAnimations();

        // Start the animation loop
        this.startAnimationLoop();
    }
    /**
     * Set up audio analysis for animation
     */
    setupAudioAnalysis() {
        try {
            // Create audio context with cross-browser compatibility
            // @ts-ignore - AudioContext is the standard, but some browsers might use prefixed versions
            const AudioContextClass = window.AudioContext;
            this.audioContext = new AudioContextClass();

            // Create analyzer but don't connect it to destination
            // We'll only use it for analysis, not for playback
            this.audioAnalyzer = this.audioContext.createAnalyser();
            this.audioAnalyzer.fftSize = 32;

            // Create a silent destination node (gain set to 0)
            this.silentDestination = this.audioContext.createGain();
            this.silentDestination.gain.value = 0;

            // Connect analyzer to silent destination to avoid memory leaks
            this.audioAnalyzer.connect(this.silentDestination);
            this.silentDestination.connect(this.audioContext.destination);

            logger.info('Audio analysis set up successfully (silent mode)');
        } catch (error) {
            logger.error('Failed to set up audio analysis:', error);
        }
    }

    /**
     * Set initial natural pose with hands down and natural head position
     * This ensures the avatar starts in a natural position
     * Based on the approach from talkinghead.mjs
     * Uses pose templates from AnimationConfig.js
     */
    setInitialPose() {
        logger.info('Setting initial natural pose');

        // Only proceed if we have the necessary bones
        if (!this.bones.Head || !this.bones.Neck) {
            logger.warn('Cannot set initial pose: missing Head or Neck bones');
            return;
        }

        // Store original rotations for reference
        if (!this.originalRotations) {
            this.originalRotations = {};
        }

        // Determine which bone set to use based on the mesh type
        const isRPMMesh = this.checkIfRPMMesh();
        const isDollMesh = this.checkIfDollMesh();

        if (this.options.debug) {
            logger.debug(`Mesh type: ${isRPMMesh ? 'ReadyPlayerMe' : (isDollMesh ? 'Doll' : 'Unknown')}`);
        }

        // Use "side" as the first pose, weight on left leg (from talkinghead.mjs)
        this.poseName = "side"; // First pose
        this.poseWeightOnLeft = true; // Initial weight on left leg
        this.gesture = null; // Values that override pose properties
        this.poseCurrentTemplate = this.poseTemplates[this.poseName];

        // Create pose objects
        this.poseBase = this.poseFactory(this.poseCurrentTemplate);
        this.poseTarget = this.poseFactory(this.poseCurrentTemplate);

        // Straight pose used as a reference
        this.poseStraight = this.propsToThreeObjects(this.poseTemplates["straight"].props);

        // Will be set when avatar is fully loaded
        this.poseAvatar = null;

        // Apply the pose to the actual bones
        this.applyPoseToMesh(this.poseBase);

        // Use finger bone names from configuration
        this.fingerBoneNames = FINGER_BONE_NAMES;

        // Store bone lists for different avatar types
        this.dollBoneNames = DOLL_BONE_NAMES;
        this.rpmBoneNames = RPM_BONE_NAMES;

        logger.info('Initial natural pose set');
    }
    /**
     * Check if the mesh is a ReadyPlayerMe mesh
     * @returns {boolean} True if the mesh is a ReadyPlayerMe mesh
     */
    checkIfRPMMesh() {
        // Use the utility function to detect mesh type
        const meshType = detectMeshType(this.bones);
        return meshType.isRPM;
    }

    /**
     * Check if the mesh is a doll mesh
     * @returns {boolean} True if the mesh is a doll mesh
     */
    checkIfDollMesh() {
        // Use the utility function to detect mesh type
        const meshType = detectMeshType(this.bones);
        return meshType.isDoll;
    }
    /**
     * Create a new pose from a template.
     * Based on poseFactory from talkinghead.mjs
     * @param {Object} template Pose template
     * @param {number} [ms=2000] Transition duration in ms
     * @return {Object} A new pose object.
     */
    poseFactory(template, ms = 2000) {
        // Pose object
        const o = {
            template: template,
            props: this.propsToThreeObjects(template.props)
        };

        for (const [p, val] of Object.entries(o.props)) {
            // Restrain movement when standing
            if (this.options.modelMovementFactor < 1 && template.standing &&
                (p === 'Hips.quaternion' || p === 'Spine.quaternion' ||
                    p === 'Spine1.quaternion' || p === 'Spine2.quaternion' ||
                    p === 'Neck.quaternion' || p === 'LeftUpLeg.quaternion' ||
                    p === 'LeftLeg.quaternion' || p === 'RightUpLeg.quaternion' ||
                    p === 'RightLeg.quaternion')) {

                if (this.poseStraight && this.poseStraight[p]) {
                    const ref = this.poseStraight[p];
                    if (val.isQuaternion && ref.isQuaternion) {
                        const angle = val.angleTo(ref);
                        val.rotateTowards(ref, (1 - (this.options.modelMovementFactor || 1)) * angle);
                    }
                }
            }

            // Custom properties for animation
            val.t = this.animClock; // timestamp
            val.d = ms; // Transition duration
        }
        return o;
    }

    /**
     * Convert internal notation to THREE objects.
     * All rotations are converted to quaternions.
     * Based on propsToThreeObjects from talkinghead.mjs
     * @param {Object} p Pose properties
     * @return {Object} A new pose object with THREE.js objects
     */
    propsToThreeObjects(p) {
        const r = {};
        for (let [key, val] of Object.entries(p)) {
            const ids = key.split('.');
            let x = Array.isArray(val.x) ? this.gaussianRandom(...val.x) : val.x;
            let y = Array.isArray(val.y) ? this.gaussianRandom(...val.y) : val.y;
            let z = Array.isArray(val.z) ? this.gaussianRandom(...val.z) : val.z;

            if (ids[1] === 'position') {
                r[key] = new THREE.Vector3(x, y, z);
            } else if (ids[1] === 'rotation') {
                // Convert Euler to Quaternion
                const euler = new THREE.Euler(x, y, z, 'XYZ');
                r[ids[0] + '.quaternion'] = new THREE.Quaternion().setFromEuler(euler);
            } else if (ids[1] === 'quaternion') {
                r[key] = new THREE.Quaternion(x, y, z, val.w || 1);
            } else if (ids[1] === 'scale') {
                r[key] = new THREE.Vector3(x, y, z);
            }
        }
        return r;
    }

    // Add this method after the existing methods, around line 1200:

    /**
     * Add idle animations when the avatar is not speaking
     * This method provides natural idle behavior like occasional head movements and blinking
     * Now supports selective animation blending with FBX animations
     */
    addIdleAnimations() {
        // Skip if we have too many animations queued
        if (this.animQueue.length > 3) return;

        // Check if FBX animations are active (from SkeletalAnimator)
        const fbxActive = this.isFBXAnimationActive && this.isFBXAnimationActive();

        // Skip all idle animations if speaking without FBX animation system
        if (this.isSpeaking && !fbxActive) return;

        // Initialize timing variables for idle animations
        if (!this.lastIdleAnimationTime) {
            this.lastIdleAnimationTime = {};
        }

        const now = this.animClock;

        // When FBX animations are active, only allow subtle, non-conflicting animations
        if (fbxActive) {
            // Only allow breathing and eye animations during FBX playback
            this._addIdleBreathingAnimations(now);
            this._addIdleEyeAnimations(now);

            if (this.options.debug && Math.random() < 0.02) { // Log 2% of the time to avoid spam
                logger.info('🎬 FBX animation active - using selective idle animations (breathing + eyes only)');
            }
        } else {
            // Full idle animation system when no FBX animations are playing

            // Enhanced look animation system
            this._addIdleLookAnimations(now);

            // Enhanced body pose variation system
            this._addIdleBodyPoses(now);

            // Breathing and micro-movements
            this._addIdleBreathingAnimations(now);

            // Blinking and eye movements
            this._addIdleEyeAnimations(now);

            // Occasional subtle gestures during idle
            this._addIdleGestures(now);
        }
    }

    /**
     * Add idle look animations with enhanced variety
     * @private
     */
    _addIdleLookAnimations(now) {
        // Check if we have any look animations
        const hasLookAnim = this.animQueue.some(anim =>
            anim.template && (anim.template.name === 'lookat' || anim.lookType));

        // Only add look animation if there isn't one already and enough time has passed
        if (!hasLookAnim) {
            if (!this.lastLookAnimationTime || (now - this.lastLookAnimationTime) > 8000 + Math.random() * 7000) {
                const lookTypes = ['camera', 'ahead', 'side', 'down', 'up'];
                const selectedLook = lookTypes[Math.floor(Math.random() * lookTypes.length)];

                if (this.options.debug) {
                    logger.debug(`👁️ Adding idle look animation: ${selectedLook}`);
                }

                switch (selectedLook) {
                    case 'camera':
                        this.lookAtCamera(2000 + Math.random() * 4000);
                        break;
                    case 'ahead':
                        this.lookAhead(3000 + Math.random() * 3000);
                        break;
                    case 'side':
                        this._performSmoothLookAnimation('random', 2500 + Math.random() * 2500);
                        break;
                    case 'down':
                        this._addThoughtfulLookDown();
                        break;
                    case 'up':
                        this._addCuriousLookUp();
                        break;
                }
                this.lastLookAnimationTime = now;
            }
        }
    }

    /**
     * Add idle body pose variations using pose factory system
     * @private
     */
    _addIdleBodyPoses(now) {
        // Initialize pose timing
        if (!this.lastIdleAnimationTime.poseChange) {
            this.lastIdleAnimationTime.poseChange = now;
        }

        // Change pose every 15-30 seconds during idle
        const poseChangeInterval = 15000 + Math.random() * 15000;

        if ((now - this.lastIdleAnimationTime.poseChange) > poseChangeInterval) {
            this._addVariedBodyPose();
            this.lastIdleAnimationTime.poseChange = now;
        }

        // Add subtle weight shifts more frequently
        if (!this.lastIdleAnimationTime.weightShift) {
            this.lastIdleAnimationTime.weightShift = now;
        }

        if ((now - this.lastIdleAnimationTime.weightShift) > 5000 + Math.random() * 10000) {
            this._addSubtleWeightShift();
            this.lastIdleAnimationTime.weightShift = now;
        }
    }

    /**
     * Add varied body poses using pose factory system
     * @private
     */
    _addVariedBodyPose() {
        // Check if FBX animations are active - if so, skip pose changes to avoid conflicts
        const fbxActive = this.isFBXAnimationActive && this.isFBXAnimationActive();
        if (fbxActive) {
            if (this.options.debug) {
                logger.debug(`🚫 Skipping pose change - FBX animation active`);
            }
            return;
        }

        if (!this.poseTemplates) return;

        // Select from standing poses suitable for idle behavior
        const idlePoses = ['side', 'hip', 'straight', 'wide'];
        const currentPose = this.poseName || 'side';

        // Filter out current pose to ensure variation
        const availablePoses = idlePoses.filter(pose => pose !== currentPose);
        const selectedPose = availablePoses[Math.floor(Math.random() * availablePoses.length)];

        if (this.options.debug) {
            logger.debug(`🕴️ Changing idle pose: ${currentPose} → ${selectedPose}`);
        }

        // Create smooth pose transition
        const poseTemplate = this.poseTemplates[selectedPose];
        if (poseTemplate) {
            // Randomize weight distribution
            this.poseWeightOnLeft = Math.random() > 0.5;

            // Apply pose with longer transition for natural feel
            this.setPoseFromTemplate(poseTemplate, 2000 + Math.random() * 1000);
            this.poseName = selectedPose;

            if (this.options.debug) {
                logger.debug(`🎭 Applied pose: ${selectedPose}, weight on ${this.poseWeightOnLeft ? 'left' : 'right'}`);
            }
        }
    }

    /**
     * Add subtle weight shift animations
     * @private
     */
    _addSubtleWeightShift() {
        // Check if FBX animations are active - if so, skip weight shifts to avoid conflicts
        const fbxActive = this.isFBXAnimationActive && this.isFBXAnimationActive();
        if (fbxActive) {
            if (this.options.debug) {
                logger.debug(`🚫 Skipping weight shift - FBX animation active`);
            }
            return;
        }

        if (!this.bones.Hips || !this.bones.Spine) return;

        const shiftIntensity = 0.02 + Math.random() * 0.03; // Very subtle
        const shiftDirection = Math.random() > 0.5 ? 1 : -1;

        const weightShiftTemplate = {
            name: 'weightShift',
            dt: [1500 + Math.random() * 1000, 2000 + Math.random() * 1000, 1500 + Math.random() * 1000],
            vs: {
                hipsRotateZ: [0, shiftIntensity * shiftDirection, 0],
                spineRotateZ: [0, -shiftIntensity * shiftDirection * 0.5, 0]
            }
        };

        const anim = this.animFactory(weightShiftTemplate);
        if (anim) {
            this.animQueue.push(anim);

            if (this.options.debug) {
                logger.debug(`⚖️ Added subtle weight shift: ${shiftDirection > 0 ? 'right' : 'left'}`);
            }
        }
    }

    /**
     * Add breathing and micro-movements
     * @private
     */
    _addIdleBreathingAnimations(now) {
        // Check if FBX animations are active
        const fbxActive = this.isFBXAnimationActive && this.isFBXAnimationActive();

        // Breathing animation
        if (Math.random() < 0.001 && this.bones.Spine) {
            const breathingTemplate = {
                name: 'breathing',
                dt: [2000 + Math.random() * 1000, 2000 + Math.random() * 1000],
                vs: {
                    spineRotateX: [0, 0.015 + Math.random() * 0.01, 0]
                }
            };

            const anim = this.animFactory(breathingTemplate);
            if (anim) {
                this.animQueue.push(anim);

                if (this.options.debug) {
                    logger.debug(`🫁 Added breathing animation`);
                }
            }
        } else if (this.options.debug && Math.random() < 0.01) {
            logger.debug(`🚫 Skipping breathing animation - dance/performance active`);
        }

        // Subtle shoulder adjustments - ONLY when FBX animations are NOT active
        if (!fbxActive && Math.random() < 0.0008 && (this.bones.LeftShoulder || this.bones.RightShoulder)) {
            this._addShoulderAdjustment();
        }
    }

    /**
     * Add shoulder adjustment micro-movements
     * @private
     */
    _addShoulderAdjustment() {
        // Double-check FBX animation status to prevent conflicts
        const fbxActive = this.isFBXAnimationActive && this.isFBXAnimationActive();
        if (fbxActive) {
            if (this.options.debug) {
                logger.debug(`🚫 Skipping shoulder adjustment - FBX animation active`);
            }
            return;
        }

        const adjustmentType = Math.floor(Math.random() * 3);
        let shoulderTemplate;

        switch (adjustmentType) {
            case 0: // Single shoulder roll
                shoulderTemplate = {
                    name: 'shoulderRoll',
                    dt: [800, 1200, 800],
                    vs: {
                        leftShoulderRotateZ: [0, 0.05 + Math.random() * 0.03, 0],
                        leftShoulderRotateX: [0, -0.02, 0]
                    }
                };
                break;
            case 1: // Both shoulders slight adjustment
                shoulderTemplate = {
                    name: 'shoulderAdjust',
                    dt: [1000, 1500, 1000],
                    vs: {
                        leftShoulderRotateY: [0, 0.02 + Math.random() * 0.01, 0],
                        rightShoulderRotateY: [0, -0.02 - Math.random() * 0.01, 0]
                    }
                };
                break;
            case 2: // Shoulder tension release
                shoulderTemplate = {
                    name: 'shoulderRelease',
                    dt: [600, 400, 1000],
                    vs: {
                        leftShoulderRotateZ: [0, 0.08, 0],
                        rightShoulderRotateZ: [0, 0.08, 0],
                        neckRotateX: [0, -0.03, 0]
                    }
                };
                break;
        }

        const anim = this.animFactory(shoulderTemplate);
        if (anim) {
            this.animQueue.push(anim);

            if (this.options.debug) {
                logger.debug(`🤷 Added shoulder adjustment: type ${adjustmentType}`);
            }
        }
    }

    /**
     * Add eye and blink animations
     * @private
     */
    _addIdleEyeAnimations(now) {
        // **DANCE ANIMATION FIX**: Check if current FBX animation is a dance/performance
        // If so, reduce or skip eye animations to avoid interference
        if (this.mesh && this.mesh.userData && this.mesh.userData.currentFBXAnimation) {
            const currentCategory = this.mesh.userData.currentFBXAnimation.category;
            const currentFile = this.mesh.userData.currentFBXAnimation.file;
            currentCategory === 'dancing' ||
                (currentFile && currentFile.toLowerCase().includes('dance'));
        }

        // Enhanced blink system with double blinks occasionally
        // Reduce frequency during dance animations
        // if (Math.random() < 0.003) {
        //     if (Math.random() < 0.2) {
        //         this._addDoubleBlink();
        //         if (this.options.debug && Math.random() < 0.1) {
        //             logger.debug(`🚫 Adding reduced-frequency double blink during dance`);
        //         }
        //     } else {
        //         this.blink();
        //         if (this.options.debug && Math.random() < 0.1) {
        //             logger.debug(`🚫 Adding reduced-frequency blink during dance`);
        //         }
        //     }
        // }

        // Subtle eye movements (if morph targets available)
        // Skip during dance animations to avoid conflicts
        if (Math.random() < 0.001 && this.setMorphTargetValue) {
            this._addEyeMovement();
        } else if (this.options.debug && Math.random() < 0.01) {
            logger.debug(`🚫 Skipping eye movement during dance`);
        }
    }

    /**
     * Add double blink animation
     * @private
     */
    _addDoubleBlink() {
        const doubleBlinkTemplate = {
            name: 'doubleBlink',
            dt: [120, 80, 120, 80, 200],
            vs: {
                eyesClosed: [0, 1, 0.2, 1, 0]
            }
        };

        const anim = this.animFactory(doubleBlinkTemplate);
        if (anim) {
            this.animQueue.push(anim);

            if (this.options.debug) {
                logger.debug(`👁️👁️ Added double blink`);
            }
        }
    }

    /**
     * Add subtle eye movement
     * @private
     */
    _addEyeMovement() {
        const eyeMovementTemplate = {
            name: 'eyeMovement',
            dt: [500, 1000, 500],
            vs: {
                eyesRotateX: [0, (Math.random() - 0.5) * 0.1, 0],
                eyesRotateY: [0, (Math.random() - 0.5) * 0.2, 0]
            }
        };

        const anim = this.animFactory(eyeMovementTemplate);
        if (anim) {
            this.animQueue.push(anim);

            if (this.options.debug) {
                logger.debug(`👀 Added subtle eye movement`);
            }
        }
    }

    /**
     * Add occasional subtle gestures during idle
     * @private
     */
    _addIdleGestures(now) {
        // Initialize gesture timing
        if (!this.lastIdleAnimationTime.gesture) {
            this.lastIdleAnimationTime.gesture = now;
        }

        // Use gesture system if enabled, otherwise use fallback
        if (this.handGestureSystem && this.handGestureSystem.isReady()) {
            this.handGestureSystem.addIdleGestures(now);
        } else {
            // Fallback: Simple idle gestures without advanced IK
            if ((now - this.lastIdleAnimationTime.gesture) > 15000 + Math.random() * 10000) {
                if (Math.random() < 0.3) { // Reduced frequency when gesture system disabled
                    this._addSimpleIdleMovement();
                    this.lastIdleAnimationTime.gesture = now;
                }
            }
        }
    }

    /**
 * Add simple idle movement (fallback when gesture system is disabled)
 * @private
 */
    _addSimpleIdleMovement() {
        try {
            // Simple breathing-like movement
            const template = {
                name: 'simpleIdleMovement',
                dt: [2000 + Math.random() * 1000],
                vs: {
                    moveto: [{
                        props: {
                            'Spine.quaternion': new THREE.Quaternion().setFromEuler(
                                new THREE.Euler(
                                    (Math.random() - 0.5) * 0.02, // Very subtle spine movement
                                    (Math.random() - 0.5) * 0.01,
                                    (Math.random() - 0.5) * 0.01
                                )
                            )
                        }
                    }]
                }
            };

            const anim = this.animFactory(template);
            if (anim) {
                anim.isSimpleIdle = true;
                this.animQueue.push(anim);
            }
        } catch (error) {
            if (this.options.debug) {
                logger.warn('Failed to create simple idle movement:', error);
            }
        }
    }

    /**
     * Select a target using weighted selection to prefer unilateral gestures
     * @private
     */
    _selectWeightedTarget(targets) {
        // Calculate total weight
        const totalWeight = targets.reduce((sum, target) => sum + (target.weight || 1), 0);

        // Generate random number
        let random = Math.random() * totalWeight;

        // Select based on weight
        for (const target of targets) {
            random -= (target.weight || 1);
            if (random <= 0) {
                return target;
            }
        }

        // Fallback to first target
        return targets[0];
    }

    /**
     * Setup faster return to neutral position
     * @private
     */
    _setupFasterReturnToNeutral(template, phaseIndex) {
        // Set all hand bones to neutral position with faster transition
        template.vs.moveto[phaseIndex].props = {
            'LeftArm.quaternion': null,
            'LeftForeArm.quaternion': null,
            'LeftHand.quaternion': null,
            'RightArm.quaternion': null,
            'RightForeArm.quaternion': null,
            'RightHand.quaternion': null
        };
    }



    /**
     * Add subtle hand movement during idle as fallback
     * @private
     */
    _addSubtleIdleHandMovement() {
        // Very subtle micro-movements for when avatar is idle
        const subtleMovements = [
            {
                name: 'fingerTwitch',
                description: 'Very subtle finger movement',
                props: {
                    'RightHand.quaternion': new THREE.Quaternion().setFromEuler(
                        new THREE.Euler(0.02, 0.01, 0)
                    )
                }
            },
            {
                name: 'shoulderMicroShift',
                description: 'Barely noticeable shoulder adjustment',
                props: {
                    'RightShoulder.quaternion': new THREE.Quaternion().setFromEuler(
                        new THREE.Euler(0, 0, 0.01)
                    )
                }
            }
        ];

        const selectedMovement = subtleMovements[Math.floor(Math.random() * subtleMovements.length)];

        const subtleTemplate = {
            name: 'subtleIdleHandMovement',
            dt: [800 + Math.random() * 400, 300, 1000 + Math.random() * 500],
            vs: {
                moveto: [
                    { props: selectedMovement.props },
                    { props: selectedMovement.props }, // Hold briefly
                    { props: {} } // Return to neutral (empty = original pose)
                ]
            }
        };

        const anim = this.animFactory(subtleTemplate);
        if (anim) {
            anim.isIdleGesture = true;
            anim.isSubtle = true;
            this.animQueue.push(anim);

            if (this.options.debug) {
                logger.debug(`🤏 Added subtle idle hand movement: ${selectedMovement.name}`);
            }
        }
    }

    /**
     * Add subtle hand movement during idle
     * @private
     */
    _addIdleHandMovement() {
        // Use gesture system if enabled
        if (this.handGestureSystem && this.handGestureSystem.isReady()) {
            this.handGestureSystem.addSubtleHandMovement();
            return;
        }

        // When gesture system is disabled, use simple fallback animation
        this._fallbackIdleHandMovement();
    }



    /**
     * Fallback idle hand movement when IK fails
     * @private
     */
    _fallbackIdleHandMovement() {
        logger.warn('Using fallback non-IK idle hand animation');

        const handMovements = [
            {
                name: 'handToChest',
                description: 'Touch chest briefly',
                movement: {
                    'RightArm.quaternion': new THREE.Quaternion().setFromEuler(
                        new THREE.Euler(-0.3, -0.2, 0.4)
                    ),
                    'RightForeArm.quaternion': new THREE.Quaternion().setFromEuler(
                        new THREE.Euler(-0.8, 0, 0)
                    ),
                    'RightHand.quaternion': new THREE.Quaternion().setFromEuler(
                        new THREE.Euler(0.1, 0.3, -0.1)
                    )
                }
            },
            {
                name: 'brushHair',
                description: 'Brief hair touch',
                movement: {
                    'LeftArm.quaternion': new THREE.Quaternion().setFromEuler(
                        new THREE.Euler(-0.5, 0.3, -0.6)
                    ),
                    'LeftForeArm.quaternion': new THREE.Quaternion().setFromEuler(
                        new THREE.Euler(-1.2, 0.1, 0)
                    ),
                    'LeftHand.quaternion': new THREE.Quaternion().setFromEuler(
                        new THREE.Euler(-0.1, -0.2, 0.1)
                    )
                }
            }
        ];

        const selectedMovement = handMovements[Math.floor(Math.random() * handMovements.length)];

        const handTemplate = {
            name: 'fallbackIdleHandMovement',
            dt: [1200 + Math.random() * 800, 800 + Math.random() * 400, 1500 + Math.random() * 500],
            vs: {
                moveto: [
                    {
                        props: selectedMovement.movement
                    },
                    {
                        props: selectedMovement.movement // Hold briefly
                    },
                    {
                        props: {
                            'LeftArm.quaternion': null,
                            'LeftForeArm.quaternion': null,
                            'LeftHand.quaternion': null,
                            'RightArm.quaternion': null,
                            'RightForeArm.quaternion': null,
                            'RightHand.quaternion': null
                        }
                    }
                ]
            }
        };

        const anim = this.animFactory(handTemplate);
        if (anim) {
            anim.isIdleGesture = true;
            anim.gestureType = 'fallback';
            this.animQueue.push(anim);

            if (this.options.debug) {
                logger.debug(`🤏 Added fallback idle hand movement: ${selectedMovement.name} (${selectedMovement.description})`);
            }
        }
    }

    /**
     * Add thoughtful look down animation
     * @private
     */
    _addThoughtfulLookDown() {
        const lookDownTemplate = {
            name: 'thoughtfulLookDown',
            dt: [1000, 2000 + Math.random() * 3000, 1200],
            vs: {
                headRotateX: [0, 0.3 + Math.random() * 0.2, 0],
                eyesRotateX: [0, 0.2, 0],
                browInnerUp: [0, 0.1, 0] // Slight concentration
            }
        };

        const anim = this.animFactory(lookDownTemplate);
        if (anim) {
            this.animQueue.push(anim);

            if (this.options.debug) {
                logger.debug(`🤔 Added thoughtful look down`);
            }
        }
    }

    /**
     * Add curious look up animation
     * @private
     */
    _addCuriousLookUp() {
        const lookUpTemplate = {
            name: 'curiousLookUp',
            dt: [800, 1500 + Math.random() * 2000, 1000],
            vs: {
                headRotateX: [0, -0.2 - Math.random() * 0.1, 0],
                eyesRotateX: [0, -0.15, 0],
                browOuterUp: [0, 0.15, 0] // Slight curiosity
            }
        };

        const anim = this.animFactory(lookUpTemplate);
        if (anim) {
            this.animQueue.push(anim);

            if (this.options.debug) {
                logger.debug(`🤨 Added curious look up`);
            }
        }
    }

    /**
     * Set pose from template (compatible with TalkingHead interface)
     * @param {Object} template - Pose template
     * @param {number} ms - Transition time in milliseconds
     */
    setPoseFromTemplate(template, ms = 2000) {
        if (!template) {
            logger.warn('setPoseFromTemplate called with null template');
            return;
        }

        logger.info(`Setting pose from template: ${template.name || 'unnamed'}`);

        // Stop any existing pose animations
        this.animQueue = this.animQueue.filter(anim => anim.type !== 'pose');

        // Create pose animation using the template
        if (template.props) {
            // Convert template props to THREE.js objects using propsToThreeObjects
            const threeObjectProps = this.propsToThreeObjects(template.props);

            // Apply pose properties to bones
            this.applyPoseToMesh({ props: threeObjectProps });

            // Create smooth transition animation
            const poseAnim = {
                template: { name: 'pose', ...template },
                ts: [this.animClock, this.animClock + ms],
                vs: {
                    pose: [threeObjectProps]
                },
                type: 'pose'
            };

            this.animQueue.push(poseAnim);
            logger.info(`Added pose animation with ${ms}ms transition`);
        } else {
            logger.warn('Pose template missing props property');
        }
    }
    /**
     * Apply a pose object to the mesh bones
     * @param {Object} pose - Pose object created by poseFactory
     */
    applyPoseToMesh(pose) {
        if (!pose || !pose.props) return;

        // Store initial position for doll meshes
        const isDollMesh = this.checkIfDollMesh();
        let initialHipsPosition = null;
        if (isDollMesh && this.bones.Hips) {
            initialHipsPosition = this.bones.Hips.position.clone();

            // Store the initial y-position in the bone's userData for consistent restoration
            if (!this.bones.Hips.userData) {
                this.bones.Hips.userData = {};
            }
            this.bones.Hips.userData.initialYPosition = initialHipsPosition.y;
            logger.debug(`Stored initial Hips Y position: ${initialHipsPosition.y}`);
        }

        // Store original rotations for all bones
        this.originalRotations = {};
        for (const boneName in this.bones) {
            if (this.bones[boneName] && this.bones[boneName].rotation) {
                this.originalRotations[boneName] = {
                    x: this.bones[boneName].rotation.x,
                    y: this.bones[boneName].rotation.y,
                    z: this.bones[boneName].rotation.z
                };
            }
        }

        // Store armature original position if available
        if (this.armature) {
            if (!this.armature.userData) {
                this.armature.userData = {};
            }
            this.armature.userData.originalPosition = this.armature.position.clone();
            this.armature.userData.originalQuaternion = this.armature.quaternion.clone();
            this.armature.userData.originalScale = this.armature.scale.clone();
            logger.debug(`Stored armature original position: (${this.armature.position.x}, ${this.armature.position.y}, ${this.armature.position.z})`);
        }

        for (const [key, val] of Object.entries(pose.props)) {
            const [boneName, propName] = key.split('.');

            // Skip if bone doesn't exist
            if (!this.bones[boneName]) continue;

            // Store original rotation if not already stored
            if (!this.originalRotations[boneName] && this.bones[boneName].rotation) {
                this.originalRotations[boneName] = {
                    x: this.bones[boneName].rotation.x,
                    y: this.bones[boneName].rotation.y,
                    z: this.bones[boneName].rotation.z
                };
            }

            // Apply the property to the bone
            if (propName === 'quaternion' && this.bones[boneName].quaternion) {
                this.bones[boneName].quaternion.copy(val);
            } else if (propName === 'position' && this.bones[boneName].position) {
                if (isDollMesh && boneName === 'Hips') {
                    // For doll meshes, preserve the initial y position
                    const position = val.clone();

                    // Use the stored userData value if available, otherwise use the initialHipsPosition
                    if (this.bones[boneName].userData && this.bones[boneName].userData.initialYPosition !== undefined) {
                        position.y = this.bones[boneName].userData.initialYPosition;
                    } else if (initialHipsPosition) {
                        position.y = initialHipsPosition.y;
                    }

                    this.bones[boneName].position.copy(position);
                } else {
                    this.bones[boneName].position.copy(val);
                }
            } else if (propName === 'scale' && this.bones[boneName].scale) {
                this.bones[boneName].scale.copy(val);
            }

            if (this.options.debug) {
                // logger.debug(`Applied ${propName} to ${boneName}`);
            }
        }
    }



    /**
     * Create animation from template using centralized animFactory
     * All animations now go through this method for consistency
     * @param {string|Object} templateKey - Template key or template object
     * @param {Object} context - Animation context (intensity, duration, etc.)
     * @param {boolean|number} loop - Whether to loop the animation
     * @param {number} scaleTime - Time scale factor
     * @param {number} scaleValue - Value scale factor
     * @returns {Object} Animation object
     */
    createAnimation(templateKey, context = {}, loop = false, scaleTime = 1, scaleValue = 1) {
        let template;

        if (typeof templateKey === 'string') {
            // Get template from ANIMATION_TEMPLATES
            const parts = templateKey.split('.');
            template = ANIMATION_TEMPLATES;

            for (const part of parts) {
                if (template[part]) {
                    template = template[part];
                } else {
                    logger.error(`Animation template not found: ${templateKey}`);
                    return null;
                }
            }
        } else {
            // Use provided template object
            template = templateKey;
        }

        if (!template) {
            logger.error(`Invalid animation template: ${templateKey}`);
            return null;
        }

        // Create context for template functions
        const animContext = {
            ...context,
            animator: this,
            bones: this.bones,
            options: this.options,
            animClock: this.animClock,
            isRunning: this.isRunning,
            isSpeaking: this.isSpeaking
        };

        // Process template with context
        const processedTemplate = this._processTemplate(template, animContext);

        // Use the existing animFactory with processed template
        return this.animFactory(processedTemplate, loop, scaleTime, scaleValue);
    }

    /**
     * Process template with context to resolve functions and dynamic values
     * @param {Object} template - Animation template
     * @param {Object} context - Animation context
     * @returns {Object} Processed template
     */
    _processTemplate(template, context) {
        const processed = deepCopy(template);

        // Process values that are functions
        if (processed.vs) {
            for (const [key, value] of Object.entries(processed.vs)) {
                if (typeof value === 'function') {
                    processed.vs[key] = value(context);
                } else if (Array.isArray(value)) {
                    processed.vs[key] = value.map(v =>
                        typeof v === 'function' ? v(context) : v
                    );
                }
            }
        }

        // Process durations that are functions
        if (processed.dt && Array.isArray(processed.dt)) {
            processed.dt = processed.dt.map(dt =>
                typeof dt === 'function' ? dt(context) : dt
            );
        }

        return processed;
    }



    /**
      * Start the animation loop
      */
    startAnimationLoop() {
        if (!this.isRunning) {
            this.isRunning = true;
            this.animTimeLast = performance.now();
            this.animate();
            logger.info('Animation loop started');
        }
    }


    /**
     * Cull animations based on performance criteria
     * @private
     */
    _cullAnimations() {
        // Remove excess animations
        if (this.animQueue.length > this.animationCulling.maxAnimations) {
            // Sort by priority and remove lowest priority animations
            this.animQueue.sort((a, b) => (b.priority || 0) - (a.priority || 0));
            const removed = this.animQueue.splice(this.animationCulling.maxAnimations);

            if (this.options.debug) {
                logger.debug(`Culled ${removed.length} animations due to queue size limit`);
            }
        }

        // Distance-based culling (if camera position is available)
        if (this.animationCulling.distanceCulling && this.options.camera) {
            // Implementation would depend on your camera system
            // This is a placeholder for distance-based culling
        }
    }

    // Add this method after the existing methods around line 1100:

    /**
     * Apply animation to bones based on animation object and progress
     * @param {Object} anim - Animation object
     * @param {number} progress - Animation progress (0-1)
     * @private
     */
    _applyAnimation(anim, progress) {
        if (!anim || !anim.vs) return;

        // **DEBUG**: Log what BaseAnimator animations are being applied during dance
        if (this.options.debug && this.mesh && this.mesh.userData && this.mesh.userData.currentFBXAnimation) {
            const fbxInfo = this.mesh.userData.currentFBXAnimation;
            const isDance = fbxInfo.category === 'performance' || fbxInfo.category === 'dancing' ||
                (fbxInfo.file && fbxInfo.file.toLowerCase().includes('dance'));

            if (isDance && Math.random() < 0.05) { // Log 5% of the time
                logger.debug(`[DEBUG] BaseAnimator applying animation during dance: ${anim.template?.name || 'unknown'}, progress: ${progress.toFixed(3)}`);
            }
        }

        // Handle moveto animations
        if (anim.vs.moveto) {
            this._applyMoveToAnimationSmooth(anim, progress);
            return;
        }

        // Handle regular property-based animations
        const isFinalStage = progress > 0.9;
        const isNearCompletion = progress > 0.95;

        for (const [property, values] of Object.entries(anim.vs)) {
            if (!values || values.length === 0) continue;

            let value;
            if (isNearCompletion && values.length >= 2) {
                // Use final value when near completion
                value = values[values.length - 1];
            } else {
                // Interpolate between values
                value = this._interpolateAnimationValueSmooth(values, progress, anim.ts);
            }

            if (typeof value === 'number' && !isNaN(value)) {
                this._applyBoneProperty(property, value, isFinalStage);
            }
        }

        // Store final position for look animations
        if (anim.lookType && isNearCompletion && anim.finalPosition) {
            this._lastAnimationFinalPosition = { ...anim.finalPosition };
        }
    }



    /**
     * Get bones affected by an animation
     * @param {Object} anim - Animation object
     * @returns {Array} Array of bone names
     * @private
     */
    _getAnimationAffectedBones(anim) {
        const bones = [];

        if (anim.vs) {
            // Check regular animation properties
            for (const prop in anim.vs) {
                if (prop.includes('head') || prop.includes('body')) {
                    bones.push('Head');
                }
                if (prop.includes('neck')) {
                    bones.push('Neck');
                }
            }

            // Check moveto properties
            if (anim.vs.moveto) {
                for (const phase of anim.vs.moveto) {
                    if (phase.props) {
                        for (const propKey in phase.props) {
                            const boneName = propKey.split('.')[0];
                            if (!bones.includes(boneName)) {
                                bones.push(boneName);
                            }
                        }
                    }
                }
            }
        }

        return bones;
    }

    // Also update _processAnimationQueue to actually handle completed animations:
    _processAnimationQueue() {
        if (this.animQueue.length === 0) return;

        // Check if FBX animations are active for queue filtering
        const fbxActive = this.isFBXAnimationActive && this.isFBXAnimationActive();

        const currentTime = this.animClock;

        // Filter animations based on FBX status before processing
        if (fbxActive) {
            // When FBX is active, remove conflicting animations from the queue
            this.animQueue = this.animQueue.filter(anim => {
                // Allow essential animations
                const isEssential = anim.template && (
                    anim.template.name === 'breathing' ||
                    anim.template.name === 'blink' ||
                    anim.template.name === 'doubleBlink' ||
                    anim.template.name === 'eyeMovement' ||
                    anim.template.name === 'eyesClosed'
                );

                // Allow FBX animations
                const isFBXAnimation = anim.isFBXAnimation;

                // Remove conflicting animations
                const isConflicting = anim.template && (
                    anim.template.name === 'shoulderRoll' ||
                    anim.template.name === 'shoulderAdjust' ||
                    anim.template.name === 'shoulderRelease' ||
                    anim.template.name === 'weightShift' ||
                    anim.template.name === 'lookat' ||
                    anim.template.name === 'thoughtfulLookDown' ||
                    anim.template.name === 'curiousLookUp' ||
                    anim.template.name === 'simpleIdleMovement' ||
                    anim.lookType || // Any look animation
                    anim.isIdleGesture || // Any idle gesture
                    anim.poseName // Any pose change
                );

                // Keep animation if it's essential, FBX, or not conflicting
                const shouldKeep = isEssential || isFBXAnimation || !isConflicting;

                // Log removed animations for debugging
                if (!shouldKeep && this.options.debug) {
                    logger.debug(`🚫 Removed conflicting animation from queue: ${anim.template?.name || 'unknown'} (FBX active)`);
                }

                return shouldKeep;
            });
        }

        // Process remaining animations
        for (let i = this.animQueue.length - 1; i >= 0; i--) {
            const anim = this.animQueue[i];

            // Skip animations that haven't reached their start time
            if (anim.t && currentTime < anim.t) continue;

            // Calculate animation progress
            let progress = 0;
            if (anim.startTime === undefined) {
                anim.startTime = currentTime;
            }

            const elapsedTime = currentTime - anim.startTime;
            const totalDuration = anim.totalDuration || (anim.template?.dt ? anim.template.dt.reduce((a, b) => a + b, 0) : 2000);

            progress = Math.min(elapsedTime / totalDuration, 1);

            // Apply animation
            this._applyAnimation(anim, progress);

            // Store final position for verification
            if (progress >= 1.0 && anim.finalPosition) {
                this._storeFinalAnimationPosition(anim);
            }

            // Remove completed animations
            if (progress >= 1.0) {
                this.animQueue.splice(i, 1);

                // Log completion
                if (this.options.debug && anim.template?.name) {
                    logger.debug(`✅ Animation completed: ${anim.template.name} (ID: ${anim.id})`);
                }

                // Trigger completion callback if present
                if (anim.onComplete && typeof anim.onComplete === 'function') {
                    try {
                        anim.onComplete(anim);
                    } catch (error) {
                        logger.error('Error in animation completion callback:', error);
                    }
                }

                // Verify animation reached intended position
                this._verifyAnimationCompletion(anim);
            }
        }
    }




    // ...existing code...
    /**
     * Calculate blend weight for animation based on conflicts
     * @param {Object} anim - Animation object
     * @returns {number} Blend weight (0-1)
     * @private
     */
    _calculateBlendWeight(anim) {
        let weight = 1.0;

        // Reduce weight if there are conflicts
        if (this._detectedConflicts && this._detectedConflicts.length > 0) {
            weight *= 0.8;
        }

        return Math.max(0.1, Math.min(1.0, weight));
    }

    /**
     * Apply bone property with blending support
     * @param {string} property - Property name
     * @param {number} value - Property value
     * @param {number} blendWeight - Blend weight (0-1)
     * @param {boolean} isFinalStage - Whether in final stage
     * @private
     */
    _applyBonePropertyWithBlending(property, value, blendWeight, isFinalStage) {
        // Use existing _applyBoneProperty but with blending
        const originalValue = this._getCurrentBonePropertyValue(property);

        if (originalValue !== null) {
            const blendedValue = this.lerp(originalValue, value, blendWeight);
            this._applyBoneProperty(property, blendedValue, isFinalStage);
        } else {
            this._applyBoneProperty(property, value, isFinalStage);
        }
    }

    /**
     * Get current bone property value
     * @param {string} property - Property name
     * @returns {number|null} Current value or null if not found
     * @private
     */
    _getCurrentBonePropertyValue(property) {
        const propertyMappings = {
            'bodyRotateX': { bone: 'Head', axis: 'x' },
            'bodyRotateY': { bone: 'Head', axis: 'y' },
            'bodyRotateZ': { bone: 'Head', axis: 'z' },
            'headRotateX': { bone: 'Head', axis: 'x' },
            'headRotateY': { bone: 'Head', axis: 'y' },
            'headRotateZ': { bone: 'Head', axis: 'z' },
            // Add more mappings as needed
        };

        const mapping = propertyMappings[property];
        if (mapping && this.bones[mapping.bone]) {
            return this.bones[mapping.bone].rotation[mapping.axis];
        }

        return null;
    }

    // Add these additional helper methods if they're missing:



    /**
     * Store final animation position for continuity
     * @param {Object} anim - Animation with final position data
     * @private
     */
    _storeFinalAnimationPosition(anim) {
        if (anim.finalPosition) {
            this._lastAnimationFinalPosition = { ...anim.finalPosition };

            if (this.options.debug) {
                logger.debug(`Stored final position for continuity`);
            }
        }
    }

    /**
     * Return hands to natural position after gestures
     * @private
     */
    _returnHandsToNaturalPosition() {
        if (!this.naturalPosePositions) return;

        const returnSpeed = 0.08; // Faster return speed
        let hasMovement = false;

        ['LeftArm', 'LeftForeArm', 'LeftHand', 'RightArm', 'RightForeArm', 'RightHand'].forEach(boneName => {
            const bone = this.armature.getObjectByName(boneName);
            if (!bone || !this.naturalPosePositions[boneName]) return;

            const targetQuaternion = this.naturalPosePositions[boneName].clone();
            const currentQuaternion = bone.quaternion.clone();

            // Check if we're close enough to target (avoid micro-movements)
            const angleDifference = currentQuaternion.angleTo(targetQuaternion);
            if (angleDifference < 0.01) return; // Stop micro-adjustments

            // Apply smooth interpolation
            bone.quaternion.slerp(targetQuaternion, returnSpeed);
            hasMovement = true;

            // Only log significant movements and very rarely
            if (this.options.debug && Math.random() < 0.002 && angleDifference > 0.1) { // Extremely rare logging
                const strengthPercentage = (angleDifference * 100).toFixed(1);
                logger.debug(`Returning ${boneName} to natural pose: target(${targetQuaternion.x.toFixed(3)}, ${targetQuaternion.y.toFixed(3)}, ${targetQuaternion.z.toFixed(3)}), current(${currentQuaternion.x.toFixed(3)}, ${currentQuaternion.y.toFixed(3)}, ${currentQuaternion.z.toFixed(3)}), strength: ${strengthPercentage}%`);
            }
        });

        // Log completion only once when movement stops
        if (!hasMovement && this._lastReturnMovement) {
            if (this.options.debug && Math.random() < 0.5) {
                logger.debug('✅ Hands returned to natural position');
            }
        }
        this._lastReturnMovement = hasMovement;
    }
    // Smooth interpolation for animation values with improved easing
    _interpolateAnimationValueSmooth(values, progress, timestamps = null) {
        if (!values || values.length === 0) return 0;
        if (values.length === 1) return values[0];

        let t1, t2, v1, v2;

        if (timestamps && timestamps.length === values.length) {
            // Use timestamps for interpolation
            const totalTime = timestamps[timestamps.length - 1];
            const currentTime = progress * totalTime;

            // Find the right segment
            for (let i = 0; i < timestamps.length - 1; i++) {
                if (currentTime >= timestamps[i] && currentTime <= timestamps[i + 1]) {
                    t1 = timestamps[i];
                    t2 = timestamps[i + 1];
                    v1 = values[i];
                    v2 = values[i + 1];
                    break;
                }
            }

            if (t1 === undefined) {
                // Fallback to last value
                return values[values.length - 1];
            }

            // Calculate interpolation factor
            const segmentProgress = t2 > t1 ? (currentTime - t1) / (t2 - t1) : 0;
            return this.lerp(v1, v2, this._easeInOutSine(segmentProgress)); // Changed to sine easing for smoother motion
        } else {
            // Equal segments
            const segmentCount = values.length - 1;
            const segmentProgress = progress * segmentCount;
            const currentSegment = Math.floor(segmentProgress);
            const localProgress = segmentProgress - currentSegment;

            const startIndex = Math.min(currentSegment, segmentCount - 1);
            const endIndex = Math.min(startIndex + 1, values.length - 1);

            v1 = values[startIndex] || 0;
            v2 = values[endIndex] || 0;

            return this.lerp(v1, v2, this._easeInOutSine(localProgress)); // Changed to sine easing
        }
    }

    // **NEW**: Smoother easing function for more natural movement
    _easeInOutSine(t) {
        return -(Math.cos(Math.PI * t) - 1) / 2;
    }

    // Enhanced cubic easing for even smoother motion
    _easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }


    // Easing function for smooth animations
    _easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    // Linear interpolation helper
    lerp(start, end, t) {
        return start + (end - start) * t;
    }
    // Fix the _verifyAnimationCompletion method to force the correct position:

    /**
     * Verify that a look animation reached its target position
     * @param {Object} anim - The completed animation
     * @private
     */
    _verifyAnimationCompletion(anim) {
        if (!anim.lookType || !anim.finalPosition || !this.bones.Head) return;

        const actualX = this.bones.Head.rotation.x;
        const actualY = this.bones.Head.rotation.y;
        const targetX = anim.finalPosition.bodyRotateX;
        const targetY = anim.finalPosition.bodyRotateY;

        const diffX = Math.abs(actualX - targetX);
        const diffY = Math.abs(actualY - targetY);
        const totalDiff = diffX + diffY;

        if (this.options.debug) {
            if (totalDiff > 0.05) {
                // logger.warn(`Animation ${anim.lookType} did not reach target exactly`);
                // logger.warn(`Target: (${targetX.toFixed(3)}, ${targetY.toFixed(3)}), Actual: (${actualX.toFixed(3)}, ${actualY.toFixed(3)})`);

                // **CRITICAL FIX**: Force bone to target position for continuity
                this.bones.Head.rotation.x = targetX;
                this.bones.Head.rotation.y = targetY;

                // **ENHANCED FIX**: Force immediate matrix update and prevent interference
                this.bones.Head.updateMatrix();
                this.bones.Head.updateMatrixWorld(true);

                // **NEW**: Mark this as a forced position to prevent immediate override
                this._lastForcedPosition = {
                    x: targetX,
                    y: targetY,
                    timestamp: this.animClock
                };

                logger.info(`🔧 FORCED head to target position: (${targetX.toFixed(3)}, ${targetY.toFixed(3)})`);
            } else {
                logger.debug(`Animation ${anim.lookType} reached target within tolerance`);
            }
        }
    }

    // **ENHANCED**: Update _applyBoneProperty to respect forced positions and prevent conflicts
    _applyBoneProperty(property, value, isFinalStage = false) {
        // Complete property mappings
        const propertyMappings = {
            'bodyRotateX': { bone: 'Head', axis: 'x' },
            'bodyRotateY': { bone: 'Head', axis: 'y' },
            'bodyRotateZ': { bone: 'Head', axis: 'z' },
            'headRotateX': { bone: 'Head', axis: 'x' },
            'headRotateY': { bone: 'Head', axis: 'y' },
            'headRotateZ': { bone: 'Head', axis: 'z' },
            'eyesRotateX': { bone: 'Head', axis: 'x', additive: true, scale: 0.3 },
            'eyesRotateY': { bone: 'Head', axis: 'y', additive: true, scale: 0.3 },
            'neckRotateX': { bone: 'Neck', axis: 'x' },
            'neckRotateY': { bone: 'Neck', axis: 'y' },
            'neckRotateZ': { bone: 'Neck', axis: 'z' },
            'spineRotateX': { bone: 'Spine', axis: 'x' },
            'spineRotateY': { bone: 'Spine', axis: 'y' },
            'spineRotateZ': { bone: 'Spine', axis: 'z' }
        };

        const mapping = propertyMappings[property];

        if (!mapping || !this.bones[mapping.bone]) {
            if (this.options.debug && !mapping) {
                // logger.warn(`Unknown animation property: ${property}`);
            }
            return;
        }

        const bone = this.bones[mapping.bone];
        const scale = mapping.scale || 1;
        const scaledValue = value * scale;

        // **NEW**: Check if we recently forced this bone position and respect it briefly
        if (mapping.bone === 'Head' && this._lastForcedPosition && !mapping.additive) {
            const timeSinceForced = this.animClock - this._lastForcedPosition.timestamp;

            // Give forced positions a brief window to "stick" (200ms)
            if (timeSinceForced < 200) {
                const currentX = bone.rotation.x;
                const currentY = bone.rotation.y;
                const forcedX = this._lastForcedPosition.x;
                const forcedY = this._lastForcedPosition.y;

                // If the current position matches our forced position, don't override it yet
                if (Math.abs(currentX - forcedX) < 0.01 && Math.abs(currentY - forcedY) < 0.01) {
                    if (this.options.debug && Math.random() < 0.1) {
                        logger.debug(`🛡️ Protecting recently forced head position for ${(200 - timeSinceForced).toFixed(0)}ms more`);
                    }
                    return; // Don't apply the new value yet
                }
            } else {
                // Clear the forced position after the protection period
                this._lastForcedPosition = null;
            }
        }

        // Apply the rotation
        if (mapping.additive) {
            bone.rotation[mapping.axis] += scaledValue;
        } else {
            bone.rotation[mapping.axis] = scaledValue;
        }

        // **ENHANCED**: Ensure matrix updates happen immediately
        bone.updateMatrix();
        if (bone.parent) {
            bone.parent.updateMatrixWorld(true);
        }        // **ENHANCED DEBUG**: Better logging for head rotation tracking
        if (this.options.debug && mapping.bone === 'Head' && (property.includes('bodyRotate') || property.includes('headRotate'))) {
            const logThis = Math.random() < 0.01 || isFinalStage; // Further reduced logging frequency

            if (logThis) {
                // logger.debug(`Applied ${property} = ${scaledValue.toFixed(3)} to ${mapping.bone}.rotation.${mapping.axis}`);
            }
        }
    }


    // The forced position protection is now handled in _applyBoneProperty

    // **UPDATE**: Enhance the animate method to remove the protection call
    animate() {
        if (!this.isRunning) return;

        const frameStart = performance.now();

        // Update animation clock
        const now = frameStart;
        const deltaTime = now - this.animTimeLast;
        this.animClock += deltaTime;
        this.animTimeLast = now;

        // Head rotation protection is now handled in _applyBoneProperty

        // Performance monitoring
        this.performanceMetrics.frameTime = deltaTime;
        this.performanceMetrics.averageFrameTime =
            (this.performanceMetrics.averageFrameTime * 0.9) + (deltaTime * 0.1);

        // Animation culling
        if (this.animationCulling.enabled) {
            this._cullAnimations();
        }

        // Update FBX animations if this is a SkeletalAnimator
        if (this.updateFBXAnimation && typeof this.updateFBXAnimation === 'function') {
            // Convert deltaTime from milliseconds to seconds for FBX mixer
            const deltaSeconds = deltaTime / 1000;
            this.updateFBXAnimation(deltaSeconds);
        }

        // Process animation queue
        this._processAnimationQueue();

        // Add idle animations if needed
        this.addIdleAnimations();

        // Performance logging
        const frameEnd = performance.now();
        this.performanceMetrics.lastFrameTime = frameEnd - frameStart;

        if (this.options.debug && Math.random() < 0.01) {
            logger.debug(`Animation frame: ${this.performanceMetrics.lastFrameTime.toFixed(2)}ms, queue: ${this.animQueue.length}, avg: ${this.performanceMetrics.averageFrameTime.toFixed(2)}ms`);
        }

        // Continue animation loop
        requestAnimationFrame(this.animate.bind(this));

        // Performance monitoring with reduced logging
        const frameTime = this.animClock - this.lastFrameTime;
        this.frameTimeSum += frameTime;
        this.frameCount++;

        // Only log performance issues or every 60 frames (about once per second)
        if (this.frameCount % 60 === 0 || frameTime > 50) { // Log if slow frame or periodically
            const avgTime = this.frameTimeSum / this.frameCount;
            if (this.options.debug && (frameTime > 50 || Math.random() < 0.01)) { // Log slow frames or very rarely
                logger.debug(`[BaseAnimator] Animation frame: ${frameTime.toFixed(2)}ms, queue: ${this.animQueue.length}, avg: ${avgTime.toFixed(2)}ms`);
            }

            // Reset counters periodically to keep averages current
            if (this.frameCount >= 300) { // Reset every 5 seconds
                this.frameTimeSum = 0;
                this.frameCount = 0;
            }
        }
        this.lastFrameTime = this.animClock;
    }

    // **ENHANCED**: Improve the smooth look animation to be more reliable
    _performSmoothLookAnimation(type, duration = 1000) {
        if (!duration) return;

        // More strict check for existing look animations
        const existingLookAnim = this.animQueue.find(anim =>
            anim.template && (anim.template.name === 'lookat' || anim.lookType)
        );

        if (existingLookAnim) {
            if (this.options.debug) {
                logger.debug(`Skipping ${type} look animation - existing look animation in progress`);
            }
            return;
        }

        // Check if enough time has passed since last look animation
        const now = this.animClock;
        if (this.lastLookAnimationTime && (now - this.lastLookAnimationTime) < 8000) {
            if (this.options.debug) {
                logger.debug(`Skipping ${type} look animation - too soon since last one (${now - this.lastLookAnimationTime}ms ago)`);
            }
            return;
        }

        // **CRITICAL**: Always read CURRENT bone rotation for accurate starting position
        let currentRotation = {
            bodyRotateX: 0,
            bodyRotateY: 0,
            eyesRotateX: 0,
            eyesRotateY: 0
        };

        if (this.bones.Head) {
            currentRotation.bodyRotateX = this.bones.Head.rotation.x;
            currentRotation.bodyRotateY = this.bones.Head.rotation.y;
            currentRotation.eyesRotateX = this.bones.Head.rotation.x * 0.5;
            currentRotation.eyesRotateY = this.bones.Head.rotation.y * 0.5;

            if (this.options.debug) {
                logger.debug(`📍 Starting ${type} look from ACTUAL head position: (${currentRotation.bodyRotateX.toFixed(3)}, ${currentRotation.bodyRotateY.toFixed(3)})`);
            }
        }

        // Get the appropriate template based on type
        const baseTemplate = type === 'camera'
            ? DEFAULT_ANIMATION_VALUES.templates.lookAt.camera
            : DEFAULT_ANIMATION_VALUES.templates.lookAt.random;

        if (!baseTemplate) {
            logger.warn(`No template found for ${type} look animation`);
            return;
        }

        // Clone the template to avoid modifying the original
        const template = deepCopy(baseTemplate);

        // Calculate target rotation based on type with LARGER movements
        let targetRotation = { ...currentRotation };

        if (type === 'camera') {
            // Look at camera - MUCH more dramatic movement
            targetRotation.bodyRotateX = Math.max(-0.5, Math.min(0.5, currentRotation.bodyRotateX + (Math.random() - 0.5) * 0.8));
            targetRotation.bodyRotateY = Math.max(-0.4, Math.min(0.4, currentRotation.bodyRotateY + (Math.random() - 0.5) * 0.6));
            targetRotation.eyesRotateX = targetRotation.bodyRotateX * 0.5;
            targetRotation.eyesRotateY = targetRotation.bodyRotateY * 0.5;
        } else if (type === 'neutral') {
            // Return to neutral position
            targetRotation.bodyRotateX = 0;
            targetRotation.bodyRotateY = 0;
            targetRotation.eyesRotateX = 0;
            targetRotation.eyesRotateY = 0;
        } else {
            // Look ahead - MUCH larger changes for dramatic movement
            const maxChange = 0.25; // Increased from 0.15
            const drotx = (Math.random() - 0.5) * maxChange * 2;
            const droty = (Math.random() - 0.5) * maxChange * 2;

            // Apply relative movement with larger bounds
            targetRotation.bodyRotateX = Math.max(-0.5, Math.min(0.5, currentRotation.bodyRotateX + drotx));
            targetRotation.bodyRotateY = Math.max(-0.4, Math.min(0.4, currentRotation.bodyRotateY + droty));
            targetRotation.eyesRotateX = targetRotation.bodyRotateX * 0.8;
            targetRotation.eyesRotateY = targetRotation.bodyRotateY * 0.8;
        }

        // **STORE THE TARGET POSITION** for verification
        this._lastAnimationFinalPosition = { ...targetRotation };

        // **ENHANCED**: More direct animation values
        if (template.vs) {
            // Use simple two-point animations for more reliable results
            template.vs.bodyRotateX = [currentRotation.bodyRotateX, targetRotation.bodyRotateX];
            template.vs.bodyRotateY = [currentRotation.bodyRotateY, targetRotation.bodyRotateY];
            template.vs.eyesRotateX = [currentRotation.eyesRotateX, targetRotation.eyesRotateX];
            template.vs.eyesRotateY = [currentRotation.eyesRotateY, targetRotation.eyesRotateY];
        }

        // **LONGER, SMOOTHER TIMING**
        if (duration && template.dt) {
            const transitionTime = Math.max(3000, duration * 0.9); // Much longer transitions
            template.dt = [transitionTime, Math.max(duration - transitionTime, 500)];
        }

        // Create animation using the template
        const context = {
            duration,
            type,
            currentRotation,
            targetRotation
        };

        const anim = this.createAnimation(template, context);

        if (anim) {
            // Add metadata for debugging and animation management
            anim.lookType = type;

            // **IMPORTANT**: Store the final position for verification
            anim.finalPosition = { ...targetRotation };

            this.animQueue.push(anim);

            // Update the last look animation time
            this.lastLookAnimationTime = now;

            const changeX = Math.abs(targetRotation.bodyRotateX - currentRotation.bodyRotateX);
            const changeY = Math.abs(targetRotation.bodyRotateY - currentRotation.bodyRotateY);

            logger.info(`Added smooth ${type} look animation with duration ${duration}ms`);

            if (this.options.debug) {
                logger.debug(`🎯 Look animation ${type}: (${currentRotation.bodyRotateX.toFixed(3)}, ${currentRotation.bodyRotateY.toFixed(3)}) → (${targetRotation.bodyRotateX.toFixed(3)}, ${targetRotation.bodyRotateY.toFixed(3)}), change: (${changeX.toFixed(3)}, ${changeY.toFixed(3)})`);
            }
        } else {
            logger.error(`Failed to create ${type} look animation`);
        }
    }

    // Old speakWithHands method removed - using enhanced version below

    // Legacy method replaced by _createEnhancedIKTargets() above

    /**
     * Get the appropriate effector bone name based on mesh type




    /**
     * Fallback method when IK is not available
     * @private
     */
    _fallbackSpeakWithHands(delay = 0, probability = 0.5) {
        logger.warn('Using fallback non-IK hand animation');

        // Create a simple direct rotation-based animation as fallback
        const template = {
            name: 'fallbackHandAnimation',
            dt: [800, 400, 600],
            vs: {
                moveto: [
                    {
                        props: {
                            'LeftArm.quaternion': new THREE.Quaternion().setFromEuler(
                                new THREE.Euler(-0.3, 0.2, -0.4)
                            ),
                            'LeftForeArm.quaternion': new THREE.Quaternion().setFromEuler(
                                new THREE.Euler(-0.5, 0.1, 0)
                            ),
                            'RightArm.quaternion': new THREE.Quaternion().setFromEuler(
                                new THREE.Euler(-0.2, -0.1, 0.3)
                            ),
                            'RightForeArm.quaternion': new THREE.Quaternion().setFromEuler(
                                new THREE.Euler(-0.4, 0, 0)
                            )
                        }
                    },
                    {
                        props: {
                            'LeftArm.quaternion': new THREE.Quaternion().setFromEuler(
                                new THREE.Euler(-0.25, 0.25, -0.35)
                            ),
                            'LeftForeArm.quaternion': new THREE.Quaternion().setFromEuler(
                                new THREE.Euler(-0.45, 0.12, 0.02)
                            ),
                            'RightArm.quaternion': new THREE.Quaternion().setFromEuler(
                                new THREE.Euler(-0.15, -0.08, 0.25)
                            ),
                            'RightForeArm.quaternion': new THREE.Quaternion().setFromEuler(
                                new THREE.Euler(-0.35, 0.02, 0.01)
                            )
                        }
                    },
                    {
                        props: {
                            'LeftArm.quaternion': null,
                            'LeftForeArm.quaternion': null,
                            'LeftHand.quaternion': null,
                            'RightArm.quaternion': null,
                            'RightForeArm.quaternion': null,
                            'RightHand.quaternion': null
                        }
                    }
                ]
            }
        };

        const anim = this.animFactory(template);
        if (anim) {
            anim.isSpeakingGesture = true;
            anim.gestureType = 'fallback';
            this.animQueue.push(anim);
        }
    }




    // ...existing code...
    _applyMoveToAnimationSmooth(anim, progress) {
        if (!anim.vs.moveto || !Array.isArray(anim.vs.moveto)) return;

        const moveto = anim.vs.moveto;
        const ts = anim.ts || [];

        // **FIXED**: Corrected segment calculation for 3-phase animation
        let currentSegment = 0;
        let segmentProgress = 0;

        if (ts.length > 1) {
            const totalTime = ts[ts.length - 1];
            const currentTime = progress * totalTime;

            // **CRITICAL FIX**: For 3 phases, we have 2 segments (0->1 and 1->2)
            // Segment 0: Phase 0 to Phase 1 (timestamps[0] to timestamps[1])
            // Segment 1: Phase 1 to Phase 2 (timestamps[1] to timestamps[2])

            let foundSegment = false;

            // Check each transition segment
            for (let i = 0; i < ts.length - 1; i++) {
                const segmentStart = ts[i];
                const segmentEnd = ts[i + 1];

                if (currentTime >= segmentStart && currentTime <= segmentEnd) {
                    currentSegment = i;
                    const segmentDuration = segmentEnd - segmentStart;
                    segmentProgress = segmentDuration > 0 ? (currentTime - segmentStart) / segmentDuration : 0;
                    foundSegment = true;
                    break;
                }
            }

            // **FIXED**: Handle final segment properly
            if (!foundSegment) {
                // We're past the last timestamp, so we're in the final state
                currentSegment = ts.length - 2; // Point to the last segment
                segmentProgress = 1.0;
            }

        } else {
            // Fallback: divide animation into equal segments
            const numSegments = moveto.length - 1; // For 3 phases, we have 2 segments
            const segmentSize = 1.0 / numSegments;
            currentSegment = Math.min(Math.floor(progress / segmentSize), numSegments - 1);
            segmentProgress = Math.min((progress - currentSegment * segmentSize) / segmentSize, 1.0);

            // Handle completion properly
            if (progress >= 1.0) {
                currentSegment = numSegments - 1;
                segmentProgress = 1.0;
            }
        }

        // Clamp values to safe ranges
        currentSegment = Math.max(0, Math.min(currentSegment, moveto.length - 2));
        segmentProgress = Math.max(0, Math.min(segmentProgress, 1));

        // **FIXED**: Debug logging to show correct segment calculation (reduced frequency)
        if (this.options.debug && Math.floor(progress * 10) !== Math.floor(this._lastLoggedProgress * 10)) {
            // logger.debug(`Animation progress: ${progress.toFixed(3)}, segment: ${currentSegment}/${moveto.length - 2}, segmentProgress: ${segmentProgress.toFixed(3)}, currentTime: ${(progress * (ts[ts.length - 1] || 1000)).toFixed(0)}ms, totalSegments: ${moveto.length - 1}`);
            this._lastLoggedProgress = progress;
        }

        // **SEGMENT HANDLING**: Apply the correct phase based on segment
        if (currentSegment === moveto.length - 2 && segmentProgress >= 0.5) {
            // **RETURN PHASE**: We're in the final segment transitioning to the return phase
            const returnPhase = moveto[moveto.length - 1]; // Last phase (return to original)

            if (returnPhase && returnPhase.props) {
                for (const [propKey, currentValue] of Object.entries(returnPhase.props)) {
                    if (currentValue === null) {
                        const [boneName, propName] = propKey.split('.');
                        const bone = this.bones[boneName];

                        if (bone && this.originalRotations && this.originalRotations[boneName]) {
                            const original = this.originalRotations[boneName];
                            const targetQuaternion = new THREE.Quaternion().setFromEuler(
                                new THREE.Euler(original.x, original.y, original.z)
                            );

                            // Progressive return - stronger as we get closer to the end
                            const returnStrength = Math.min((segmentProgress - 0.5) * 2, 1.0); // 0 to 1 over the second half
                            bone.quaternion.slerp(targetQuaternion, 0.1 + returnStrength * 0.1);
                            bone.updateMatrix();
                            bone.updateMatrixWorld(true);

                            if (this.options.debug && Math.random() < 0.001 && returnStrength > 0.5) { // Much reduced logging
                                logger.debug(`Returning ${boneName} to natural pose: target(${original.x.toFixed(3)}, ${original.y.toFixed(3)}, ${original.z.toFixed(3)}), current(${bone.rotation.x.toFixed(3)}, ${bone.rotation.y.toFixed(3)}, ${bone.rotation.z.toFixed(3)}), strength: ${returnStrength.toFixed(3)}`);
                            }
                        }
                    }
                }
            }
            return; // Exit early for the return phase
        }

        // **NORMAL INTERPOLATION**: Between current and next phase
        const currentPhase = moveto[currentSegment];
        const nextPhase = moveto[currentSegment + 1];

        if (!currentPhase || !currentPhase.props) return;

        // Apply smooth interpolation between phases
        for (const [propKey, currentValue] of Object.entries(currentPhase.props)) {
            const [boneName, propName] = propKey.split('.');
            const bone = this.bones[boneName];

            if (!bone) {
                if (this.options.debug) {
                    logger.warn(`Bone ${boneName} not found for property ${propKey}`);
                }
                continue;
            }

            // Get next value for interpolation
            const nextValue = nextPhase && nextPhase.props ? nextPhase.props[propKey] : null;

            if (currentValue === null) {
                // This phase wants to return to original - shouldn't happen in normal segments
                if (this.originalRotations && this.originalRotations[boneName]) {
                    const original = this.originalRotations[boneName];
                    const targetQuaternion = new THREE.Quaternion().setFromEuler(
                        new THREE.Euler(original.x, original.y, original.z)
                    );
                    bone.quaternion.slerp(targetQuaternion, 0.15);

                    if (this.options.debug && Math.random() < 0.01) { // Much reduced logging
                        logger.debug(`Early return for ${boneName} to natural pose: (${original.x.toFixed(3)}, ${original.y.toFixed(3)}, ${original.z.toFixed(3)})`);
                    }
                }
            } else if (propName === 'quaternion' && bone.quaternion) {
                let finalQuaternion;

                if (nextValue && nextValue !== null && segmentProgress > 0) {
                    // Smooth interpolation between current and next quaternion
                    finalQuaternion = new THREE.Quaternion().slerpQuaternions(
                        currentValue,
                        nextValue,
                        this._easeInOutCubic(segmentProgress)
                    );
                } else if (nextValue === null) {
                    // Next phase is return to original - interpolate towards original
                    if (this.originalRotations && this.originalRotations[boneName]) {
                        const original = this.originalRotations[boneName];
                        const targetQuaternion = new THREE.Quaternion().setFromEuler(
                            new THREE.Euler(original.x, original.y, original.z)
                        );

                        finalQuaternion = new THREE.Quaternion().slerpQuaternions(
                            currentValue,
                            targetQuaternion,
                            this._easeInOutCubic(segmentProgress)
                        );

                        if (this.options.debug) {
                            // logger.debug(`Interpolating ${boneName} towards natural pose: progress ${segmentProgress.toFixed(3)}`);
                        }
                    } else {
                        finalQuaternion = currentValue;
                    }
                } else {
                    finalQuaternion = currentValue;
                }

                // Apply with smooth transition - use adaptive blending speed
                const blendSpeed = this._getAdaptiveBlendSpeed(boneName, segmentProgress, anim);
                bone.quaternion.slerp(finalQuaternion, blendSpeed);
                bone.updateMatrix();
                bone.updateMatrixWorld(true);

                if (this.options.debug && (boneName.includes('Hand') || boneName.includes('Arm')) && Math.floor(segmentProgress * 5) !== Math.floor(this._lastLoggedProgress * 5)) {
                    // logger.debug(`Smoothly applying ${propKey}, segment: ${currentSegment}, progress: ${segmentProgress.toFixed(3)}, blendSpeed: ${blendSpeed.toFixed(3)}`);
                }
            }
        }
    }


    /**
     * Get adaptive blend speed for smoother animations
     * @private
     * @param {string} boneName - Name of the bone being animated
     * @param {number} progress - Animation progress (0-1)
     * @param {Object} anim - Animation object (to check for returnToNeutral flag)
     * @returns {number} Blend speed (0-1)
     */
    _getAdaptiveBlendSpeed(boneName, progress, anim = null) {
        // Check if this is a return-to-neutral animation
        if (anim && anim.returnToNeutral) {
            return 0.12; // Faster return to prevent "hanging" hands
        }

        // Different blend speeds for different bone types
        let baseSpeed = 0.1;

        if (boneName.includes('Hand')) {
            baseSpeed = 0.08; // Slower for more natural hand movement
        } else if (boneName.includes('Arm') || boneName.includes('ForeArm')) {
            baseSpeed = 0.12; // Slightly faster for arms
        } else if (boneName.includes('Shoulder')) {
            baseSpeed = 0.06; // Slowest for shoulders to avoid jerky movement
        }

        // Ease in/out curve for smoother transitions
        const easedProgress = this._easeInOutCubic(progress);

        // Reduce speed at the beginning and end of animation segments
        const speedMultiplier = Math.sin(easedProgress * Math.PI) * 0.5 + 0.5;

        return Math.max(0.02, baseSpeed * speedMultiplier);
    }

    // Also add this method to improve head movements
    moveHead(direction, intensity = 0.5, duration = 1000) {
        // Skip if we're speaking (as requested)
        if (this.isSpeaking) {
            logger.debug(`Skipping head ${direction} animation during speech`);
            return;
        }

        if (!this.bones.Head) {
            logger.warn('Head bone not found for head movement');
            return;
        }

        // Get current head rotation for smooth transition
        const currentRotation = {
            x: this.bones.Head.rotation.x,
            y: this.bones.Head.rotation.y,
            z: this.bones.Head.rotation.z
        };

        // Calculate target rotation based on direction
        let targetRotation = { ...currentRotation };

        switch (direction) {
            case 'nod':
                targetRotation.x = currentRotation.x + (intensity * 0.3);
                break;
            case 'shake':
                targetRotation.y = currentRotation.y + (intensity * 0.4 * (Math.random() > 0.5 ? 1 : -1));
                break;
            case 'tilt':
                targetRotation.z = currentRotation.z + (intensity * 0.2 * (Math.random() > 0.5 ? 1 : -1));
                break;
        }

        // Create smooth head movement animation
        const template = {
            name: `smoothHead${direction.charAt(0).toUpperCase() + direction.slice(1)}`,
            dt: [duration * 0.4, duration * 0.2, duration * 0.4],
            vs: {
                headRotateX: [currentRotation.x, targetRotation.x, currentRotation.x],
                headRotateY: [currentRotation.y, targetRotation.y, currentRotation.y],
                headRotateZ: [currentRotation.z, targetRotation.z, currentRotation.z]
            }
        };

        const anim = this.animFactory(template);

        if (anim) {
            this.animQueue.push(anim);
            logger.info(`Added smooth head ${direction} animation`);
        }
    }
    // Fix the _verifyAnimationCompletion method to force the correct position:



    // **NEW**: Add a method to protect head rotations from being overridden
    /**
     * Protect head rotation from external interference
     * @private
     */





    /**
     * Blink animation
     */
    blink() {
        // This is a placeholder for blink animation
        // Subclasses can override this method
        // logger.debug('Blink animation triggered');
    }

    /**
     * Make the avatar look at the camera using smooth head movement
     * @param {number} duration - Duration of the animation in milliseconds
     */
    lookAtCamera(duration = 1000) {
        this._performSmoothLookAnimation('camera', duration);
    }

    /**
     * Make the avatar look ahead (random direction) using smooth head movement
     * @param {number} duration - Duration of the animation in milliseconds
     */
    lookAhead(duration = 1000) {
        this._performSmoothLookAnimation('random', duration);
    }


    /**
     * Enhanced smooth look at specific position in 3D space
     * @param {THREE.Vector3} targetPosition - World position to look at
     * @param {number} duration - Duration of the animation in milliseconds
     * @param {number} intensity - How much to turn (0-1, default 1)
     */
    lookAtPosition(targetPosition, duration = 1000, intensity = 1) {
        if (!this.bones.Head || !targetPosition) return;

        // Get head world position
        const headPosition = new THREE.Vector3();
        this.bones.Head.getWorldPosition(headPosition);

        // Calculate direction vector
        const direction = targetPosition.clone().sub(headPosition).normalize();

        // Convert to rotation angles
        const targetY = Math.atan2(direction.x, direction.z) * intensity;
        const targetX = -Math.asin(direction.y) * intensity;

        // Get current rotation
        const currentRotation = {
            bodyRotateX: this.bones.Head.rotation.x || 0,
            bodyRotateY: this.bones.Head.rotation.y || 0,
            eyesRotateX: (this.bones.Head.rotation.x || 0) * 0.5,
            eyesRotateY: (this.bones.Head.rotation.y || 0) * 0.5
        };

        // Create custom template for position-based look
        const template = {
            name: 'lookat',
            dt: [duration * 0.3, duration * 0.7],
            vs: {
                bodyRotateX: [currentRotation.bodyRotateX, targetX],
                bodyRotateY: [currentRotation.bodyRotateY, targetY],
                eyesRotateX: [currentRotation.eyesRotateX, targetX * 0.8],
                eyesRotateY: [currentRotation.eyesRotateY, targetY * 0.8]
            }
        };

        const anim = this.createAnimation(template, { targetPosition, intensity });

        if (anim) {
            // Remove conflicting look animations
            this.animQueue = this.animQueue.filter(a =>
                !(a.template && a.template.name === 'lookat')
            );

            anim.lookType = 'position';

            this.animQueue.push(anim);
            logger.info(`Added look at position animation: (${targetPosition.x.toFixed(2)}, ${targetPosition.y.toFixed(2)}, ${targetPosition.z.toFixed(2)})`);
        }
    }

    /**
     * Return head to neutral position smoothly
     * @param {number} duration - Duration of the return animation
     */
    returnToNeutralLook(duration = 800) {
        this._performSmoothLookAnimation('neutral', duration);
    }

    /**
     * Apply hand gesture using animFactory
     * @param {number} intensity - Intensity of the gesture (0-1)
     */
    applyHandGesture(intensity) {
        // Check if we have the necessary bones
        const hasLeftArm = this.bones.LeftHand && this.bones.LeftForeArm && this.bones.LeftArm;
        const hasRightArm = this.bones.RightHand && this.bones.RightForeArm && this.bones.RightArm;

        if (!hasLeftArm && !hasRightArm) {
            return;
        }

        // Choose gesture type randomly
        const gestureTypes = ['point', 'openPalm'];
        const gestureType = gestureTypes[Math.floor(Math.random() * gestureTypes.length)];

        // Create animation using template
        const context = { intensity };
        const anim = this.createAnimation(`handGesture.${gestureType}`, context);

        if (anim) {
            // Mark as speaking gesture for cleanup
            anim.isSpeakingGesture = this.isSpeaking;

            this.animQueue.push(anim);
            logger.info(`Added hand gesture animation: ${gestureType} with intensity ${intensity}`);
        } else {
            logger.error(`Failed to create hand gesture animation: ${gestureType}`);
        }
    }

    /**
     * Set mood using animFactory
     * @param {string} mood - Mood to set ('happy', 'sad', 'surprised', etc.)
     */
    setMood(mood) {
        if (!mood || typeof mood !== 'string') {
            logger.warn('Invalid mood provided:', mood);
            return;
        }

        // Remove existing mood animations
        this.animQueue = this.animQueue.filter(anim => anim.type !== 'mood');

        // Create mood animation using template
        const anim = this.createAnimation(`mood.${mood}`);

        if (anim) {
            this.animQueue.push(anim);
            logger.info(`Set mood to: ${mood}`);
        } else {
            logger.warn(`Mood template not found: ${mood}`);
        }
    }

    // Helper methods
    calculateArmRotation(side, target) {
        // Calculate appropriate arm rotation to reach target
        const shoulderBone = this.armature.getObjectByName(side === 'left' ? 'LeftShoulder' : 'RightShoulder');
        if (!shoulderBone) {
            return new THREE.Quaternion();
        }

        const shoulderPos = new THREE.Vector3();
        shoulderBone.getWorldPosition(shoulderPos);

        // Calculate direction vector
        const direction = target.clone().sub(shoulderPos).normalize();

        // Convert to rotation (simplified IK)
        const rotation = new THREE.Quaternion();
        if (side === 'left') {
            rotation.setFromEuler(new THREE.Euler(
                -direction.y * Math.PI / 3,
                direction.z * Math.PI / 4,
                -direction.x * Math.PI / 6
            ));
        } else {
            rotation.setFromEuler(new THREE.Euler(
                -direction.y * Math.PI / 3,
                -direction.z * Math.PI / 4,
                direction.x * Math.PI / 6
            ));
        }

        return rotation;
    }

    calculateForeArmRotation(side, target) {
        // Calculate forearm rotation for natural bend
        return new THREE.Quaternion().setFromEuler(new THREE.Euler(
            Math.PI / 3 + Math.random() * Math.PI / 6, // Natural elbow bend
            0,
            0
        ));
    }

    getBoneQuaternion(boneName) {
        const bone = this.armature.getObjectByName(boneName);
        return bone ? bone.quaternion.clone() : new THREE.Quaternion();
    }

    getRestPoseQuaternion(boneName) {
        // Return to a natural rest pose - you might want to define these based on your avatar
        const restPoses = {
            'LeftArm': new THREE.Quaternion().setFromEuler(new THREE.Euler(0, 0, -Math.PI / 8)),
            'LeftForeArm': new THREE.Quaternion().setFromEuler(new THREE.Euler(Math.PI / 6, 0, 0)),
            'LeftHand': new THREE.Quaternion(),
            'RightArm': new THREE.Quaternion().setFromEuler(new THREE.Euler(0, 0, Math.PI / 8)),
            'RightForeArm': new THREE.Quaternion().setFromEuler(new THREE.Euler(Math.PI / 6, 0, 0)),
            'RightHand': new THREE.Quaternion()
        };

        return restPoses[boneName] || new THREE.Quaternion();
    }
    /**
     * Populate speaking hands moveto animations dynamically
     * @param {Array} moveto - Moveto array to populate
     */
    _populateSpeakingHandsMoveTo(moveto) {
        // First movement - raise and position hands
        if (this.bones.LeftHand) {
            moveto[0].props['LeftHand.quaternion'] = new THREE.Quaternion().setFromEuler(
                new THREE.Euler(
                    -0.2 - Math.random() * 0.3, // Tilt hand slightly down
                    -0.8 - Math.random() * 0.4, // Rotate inward
                    0.1 + Math.random() * 0.2   // Slight twist
                )
            );
        }

        if (this.bones.RightHand) {
            moveto[0].props['RightHand.quaternion'] = new THREE.Quaternion().setFromEuler(
                new THREE.Euler(
                    -0.2 - Math.random() * 0.3, // Tilt hand slightly down
                    0.8 + Math.random() * 0.4,  // Rotate inward (mirror of left)
                    -0.1 - Math.random() * 0.2  // Slight twist (mirror of left)
                )
            );
        }

        // Add arm rotations with more natural speaking positions
        ["LeftArm", "LeftForeArm", "RightArm", "RightForeArm"].forEach(boneName => {
            if (this.bones[boneName]) {
                const rotation = new THREE.Quaternion();
                const euler = new THREE.Euler();

                // Set more natural speaking gesture positions
                if (boneName.includes('Left')) {
                    if (boneName === 'LeftArm') {
                        euler.x = -0.3 - Math.random() * 0.3; // Lift up
                        euler.y = -0.2 + Math.random() * 0.4; // Slight forward/back variation
                        euler.z = 0.3 + Math.random() * 0.3;  // Move outward from body
                    } else { // LeftForeArm
                        euler.x = 0.1 + Math.random() * 0.2;  // Slight up/down variation
                        euler.y = -0.4 + Math.random() * 0.3; // Rotation around vertical axis
                        euler.z = 0.6 + Math.random() * 0.4;  // Bend elbow
                    }
                } else { // Right side - mirror of left
                    if (boneName === 'RightArm') {
                        euler.x = -0.3 - Math.random() * 0.3; // Lift up
                        euler.y = 0.2 - Math.random() * 0.4;  // Slight forward/back variation
                        euler.z = -0.3 - Math.random() * 0.3; // Move outward from body
                    } else { // RightForeArm
                        euler.x = 0.1 + Math.random() * 0.2;  // Slight up/down variation
                        euler.y = 0.4 - Math.random() * 0.3;  // Rotation around vertical axis
                        euler.z = -0.6 - Math.random() * 0.4; // Bend elbow
                    }
                }

                rotation.setFromEuler(euler);
                moveto[0].props[`${boneName}.quaternion`] = rotation;
            }
        });

        // Second movement - slight variations
        ["LeftArm", "LeftForeArm", "RightArm", "RightForeArm", "LeftHand", "RightHand"].forEach(boneName => {
            if (this.bones[boneName] && moveto[0].props[`${boneName}.quaternion`]) {
                // Create a slightly modified version for natural movement
                const variation = new THREE.Quaternion().setFromEuler(
                    new THREE.Euler(
                        (Math.random() - 0.5) * 0.2,
                        (Math.random() - 0.5) * 0.2,
                        (Math.random() - 0.5) * 0.2
                    )
                );

                // Combine the original rotation with the variation
                const newRotation = moveto[0].props[`${boneName}.quaternion`].clone().multiply(variation);
                moveto[1].props[`${boneName}.quaternion`] = newRotation;
            }
        });

        // Return to original position
        ["LeftArm", "LeftForeArm", "RightArm", "RightForeArm", "LeftHand", "RightHand"].forEach(boneName => {
            if (this.bones[boneName]) {
                moveto[2].props[`${boneName}.quaternion`] = null;
            }
        });
    }

    /**
     * Create a new animation based on an animation template.
     * Based on animFactory from talkinghead.mjs
     * @param {Object} t Animation template
     * @param {number|boolean} [loop=false] Number of loops, false if not looped
     * @param {number} [scaleTime=1] Scale template times
     * @param {number} [scaleValue=1] Scale template values
     * @param {boolean} [noClockOffset=false] Do not apply clock offset
     * @return {Object} New animation object.
     */
    animFactory(t, loop = false, scaleTime = 1, scaleValue = 1, noClockOffset = false) {
        // Check if template is null or undefined
        if (!t) {
            logger.error('animFactory called with null or undefined template');
            return null;
        }

        // Use the template directly without resolving references
        const resolvedTemplate = t;

        // Check if vs property exists in the template
        if (!resolvedTemplate.vs) {
            // For templates with alt property (like ANIM_TEMPLATE_EYES and ANIM_TEMPLATE_BLINK)
            // Use the first alternative if available
            if (resolvedTemplate.alt && resolvedTemplate.alt.length > 0) {
                // Use the first alternative
                resolvedTemplate.vs = resolvedTemplate.alt[0].vs;
                logger.debug('Using first alternative from template alt array');
            } else if (resolvedTemplate.idle && resolvedTemplate.idle.alt && resolvedTemplate.idle.alt.length > 0) {
                // Use the first idle alternative if available
                resolvedTemplate.vs = resolvedTemplate.idle.alt[0].vs;
                logger.debug('Using first idle alternative from template');
            } else {
                logger.error('animFactory called with template missing vs property');
                logger.debug('Template was:', JSON.stringify(t, (_, value) =>
                    typeof value === 'function' ? '[Function]' : value
                ));
                return null;
            }
        }

        const o = { template: resolvedTemplate, ts: [0], vs: {} };

        // Set loop
        if (loop !== false) {
            o.loop = loop;
        }

        // Set delay
        if (resolvedTemplate.delay) {
            if (Array.isArray(resolvedTemplate.delay)) {
                o.ts[0] = this.gaussianRandom(...resolvedTemplate.delay) * scaleTime;
            } else {
                o.ts[0] = resolvedTemplate.delay * scaleTime;
            }
        }

        // Set timestamps
        if (resolvedTemplate.dt) {
            let ts = 0;
            for (let i = 0; i < resolvedTemplate.dt.length; i++) {
                let dt;
                if (Array.isArray(resolvedTemplate.dt[i])) {
                    dt = this.gaussianRandom(...resolvedTemplate.dt[i]) * scaleTime;
                } else {
                    dt = resolvedTemplate.dt[i] * scaleTime;
                }
                ts += dt;
                o.ts.push(ts);
            }
        }

        // Set values
        for (const [key, val] of Object.entries(resolvedTemplate.vs)) {
            if (key === 'moveto') {
                // Handle moveto animations
                o.vs.moveto = val;
            } else if (typeof val === 'function') {
                // Handle function-based properties
                try {
                    // Create a safe context for the function
                    const context = {
                        // Add properties that the function might need
                        avatar: this.avatar || {},
                        opt: {
                            // Use default values from AnimationConfig.js
                            // Idle animation values
                            avatarIdleHeadMove: this.options.idleAnimationIntensity || DEFAULT_ANIMATION_VALUES.idle.avatarIdleHeadMove,
                            avatarIdleEyeContact: DEFAULT_ANIMATION_VALUES.idle.avatarIdleEyeContact,
                            headMovementIntensity: this.options.headMovementIntensity || DEFAULT_ANIMATION_VALUES.idle.headMovementIntensity,
                            blinkFrequency: this.options.blinkFrequency || DEFAULT_ANIMATION_VALUES.idle.blinkFrequency,

                            // Speaking animation values
                            avatarSpeakingHeadMove: this.options.speakingAnimationIntensity || DEFAULT_ANIMATION_VALUES.speaking.avatarSpeakingHeadMove,
                            avatarSpeakingEyeContact: DEFAULT_ANIMATION_VALUES.speaking.avatarSpeakingEyeContact,
                            handGestureIntensity: this.options.handGestureIntensity || DEFAULT_ANIMATION_VALUES.speaking.handGestureIntensity,
                            speakingAnimationIntensity: this.options.speakingAnimationIntensity || DEFAULT_ANIMATION_VALUES.speaking.speakingAnimationIntensity
                        },
                        // Add other properties from the animator
                        ...this
                    };

                    // Evaluate the function with the safe context
                    const result = val(context);

                    if (Array.isArray(result)) {
                        o.vs[key] = result.map(v =>
                            v === null ? null :
                                Array.isArray(v) ? this.gaussianRandom(...v) * scaleValue :
                                    v * scaleValue
                        );
                    } else {
                        o.vs[key] = [result * scaleValue];
                    }

                    logger.debug(`Successfully evaluated function for key ${key}`);
                } catch (error) {
                    logger.error(`Error evaluating function for key ${key}:`, error);
                    // Provide a default value instead of an empty array
                    if (key === 'headMove') {
                        const defaultValue = this.isSpeaking ?
                            DEFAULT_ANIMATION_VALUES.speaking.avatarSpeakingHeadMove :
                            DEFAULT_ANIMATION_VALUES.idle.avatarIdleHeadMove;
                        o.vs[key] = [this.options.idleAnimationIntensity || defaultValue];
                        logger.debug(`Using default value for ${key}: ${o.vs[key][0]}`);
                    } else {
                        o.vs[key] = [];
                    }
                }
            } else if (Array.isArray(val)) {
                // Handle array values
                o.vs[key] = [];
                for (let i = 0; i < val.length; i++) {
                    if (val[i] === null) {
                        o.vs[key].push(null);
                    } else if (typeof val[i] === 'function') {
                        try {
                            // Create a safe context for the function
                            const context = {
                                // Add properties that the function might need
                                avatar: this.avatar || {},
                                opt: {
                                    // Use default values from AnimationConfig.js
                                    // Idle animation values
                                    avatarIdleHeadMove: this.options.idleAnimationIntensity || DEFAULT_ANIMATION_VALUES.idle.avatarIdleHeadMove,
                                    avatarIdleEyeContact: DEFAULT_ANIMATION_VALUES.idle.avatarIdleEyeContact,
                                    headMovementIntensity: this.options.headMovementIntensity || DEFAULT_ANIMATION_VALUES.idle.headMovementIntensity,
                                    blinkFrequency: this.options.blinkFrequency || DEFAULT_ANIMATION_VALUES.idle.blinkFrequency,

                                    // Speaking animation values
                                    avatarSpeakingHeadMove: this.options.speakingAnimationIntensity || DEFAULT_ANIMATION_VALUES.speaking.avatarSpeakingHeadMove,
                                    avatarSpeakingEyeContact: DEFAULT_ANIMATION_VALUES.speaking.avatarSpeakingEyeContact,
                                    handGestureIntensity: this.options.handGestureIntensity || DEFAULT_ANIMATION_VALUES.speaking.handGestureIntensity,
                                    speakingAnimationIntensity: this.options.speakingAnimationIntensity || DEFAULT_ANIMATION_VALUES.speaking.speakingAnimationIntensity
                                },
                                // Add other properties from the animator
                                ...this
                            };

                            // Evaluate the function with the safe context
                            const result = val[i](context);
                            o.vs[key].push(result * scaleValue);
                            logger.debug(`Successfully evaluated function at index ${i} for key ${key}`);
                        } catch (error) {
                            logger.error(`Error evaluating function at index ${i} for key ${key}:`, error);
                            // Use a default value based on the key
                            if (key === 'headMove') {
                                const defaultValue = this.isSpeaking ?
                                    DEFAULT_ANIMATION_VALUES.speaking.avatarSpeakingHeadMove :
                                    DEFAULT_ANIMATION_VALUES.idle.avatarIdleHeadMove;
                                o.vs[key].push(this.options.idleAnimationIntensity || defaultValue);
                                logger.debug(`Using default value for ${key} at index ${i}: ${o.vs[key][i]}`);
                            } else {
                                o.vs[key].push(null);
                            }
                        }
                    } else if (Array.isArray(val[i])) {
                        o.vs[key].push(this.gaussianRandom(...val[i]) * scaleValue);
                    } else {
                        o.vs[key].push(val[i] * scaleValue);
                    }
                }
            } else {
                // Handle scalar values
                o.vs[key] = [val * scaleValue];
            }
        }

        // Set timestamp
        if (!noClockOffset) {
            o.t = this.animClock;
        }

        // Set ID
        o.id = this.nextAnimId++;

        // Set type from template name
        o.type = resolvedTemplate.name || 'unknown';

        // Enhanced validation for animation object
        if (!o.vs || Object.keys(o.vs).length === 0) {
            logger.warn(`Animation created with empty vs properties for template: ${resolvedTemplate.name || 'unknown'}`);
            logger.debug('Template was:', JSON.stringify(resolvedTemplate, (_, value) =>
                typeof value === 'function' ? '[Function]' : value, 2
            ));
        }

        if (this.options.debug) {
            logger.debug(`Created animation: ${o.type}, ID: ${o.id}, Duration: ${o.ts[o.ts.length - 1] - o.ts[0]}ms`);
        }

        return o;
    }

    /**
     * Gaussian random number generator
     * Uses the shared randomGaussian function from AnimationUtils.js
     * @param {number} mean - Mean value
     * @param {number} stddev - Standard deviation
     * @returns {number} Random number with gaussian distribution
     */
    gaussianRandom(mean = 0, stddev = 1) {
        return randomGaussian(mean, stddev);
    }

    /**
     * Easing function for smooth animations
     * Uses the shared applyEasing function from AnimationUtils.js
     * @param {number} t - Progress (0-1)
     * @returns {number} Eased value
     */
    easeInOutQuad(t) {
        return applyEasing(t, 'easeInOutQuad');
    }

    /**
     * Linear interpolation
     * This is a convenience method that uses the shared lerp function
     * @param {number} a - Start value
     * @param {number} b - End value
     * @param {number} t - Interpolation factor (0-1)
     * @returns {number} Interpolated value
     */
    lerp(a, b, t) {
        return lerp(a, b, t);
    }

    /**
     * Restore the original pose after external animations
     * This ensures the avatar returns to its exact initial position
     */
    restoreOriginalPose() {
        logger.info('Restoring original pose after external animation');

        // Check if we have the original rotations stored
        if (!this.originalRotations) {
            logger.warn('Cannot restore original pose: no original rotations stored');
            return;
        }

        // Apply the original rotations to all bones
        for (const [boneName, originalRotation] of Object.entries(this.originalRotations)) {
            if (this.bones[boneName] && this.bones[boneName].rotation) {
                // Log significant changes for debugging
                const currentRot = this.bones[boneName].rotation;
                if (Math.abs(currentRot.y - originalRotation.y) > 0.01) {
                    logger.debug(`Restoring ${boneName} rotation from (${currentRot.x.toFixed(3)}, ${currentRot.y.toFixed(3)}, ${currentRot.z.toFixed(3)}) to (${originalRotation.x.toFixed(3)}, ${originalRotation.y.toFixed(3)}, ${originalRotation.z.toFixed(3)})`);
                }

                // Apply the original rotation
                this.bones[boneName].rotation.set(
                    originalRotation.x,
                    originalRotation.y,
                    originalRotation.z
                );
            }
        }

        // Special handling for Hips position in doll meshes
        const isDollMesh = this.checkIfDollMesh();
        if (isDollMesh && this.bones.Hips && this.bones.Hips.userData && this.bones.Hips.userData.initialYPosition !== undefined) {
            const position = this.bones.Hips.position.clone();
            position.y = this.bones.Hips.userData.initialYPosition;

            logger.debug(`Restoring Hips Y position to ${position.y}`);
            this.bones.Hips.position.copy(position);
        }

        // Force update the matrices
        if (this.armature) {
            this.armature.updateMatrix();
            this.armature.updateMatrixWorld(true);
        }

        // Apply the initial pose to ensure everything is reset
        this.setInitialPose();

        logger.info('Original pose restored');
    }

    /**
     * Set the avatar state and update animations accordingly
     * @param {string} state The new state ('idle', 'listening', 'speaking')
     */
    setState(state) {
        // Always process state changes, even if the state is the same
        const stateChanged = this.currentState !== state;

        if (stateChanged) {
            logger.info(`State changing: ${this.currentState} -> ${state}`);
            // Update current state
            this.currentState = state;
        } else {
            // No state change, nothing to do
            return;
        }

        // Update state flags
        this.isSpeaking = state === 'speaking';
        this.isListening = state === 'listening';

        // Handle different states
        switch (state) {
            case 'speaking':
                // For speaking, use the speakWithHands animation directly
                logger.info('Using speakWithHands animation for speaking state');

                // Call speakWithHands to add hand gestures during speech
                if (typeof this.speakWithHands === 'function') {
                    this.speakWithHands(0, 0.7); // Use 0 delay and 0.7 probability
                    logger.info('Added hand gestures for speaking animation');
                }
                break;

            case 'listening':
                // For listening, use a subtle head movement animation
                logger.info('Using head movement animation for listening state');

                // Create a subtle head movement animation
                this.moveHead('nod', 0.3, 1000);

                // Schedule periodic subtle head movements while listening
                if (!this._listeningInterval) {
                    this._listeningInterval = setInterval(() => {
                        if (this.isListening) {
                            // Random subtle head movements
                            const movements = ['nod', 'tilt', 'shake'];
                            const movement = movements[Math.floor(Math.random() * movements.length)];
                            this.moveHead(movement, 0.2 + Math.random() * 0.2, 800 + Math.random() * 400);
                        } else {
                            // Clear interval if no longer listening
                            if (this._listeningInterval) {
                                clearInterval(this._listeningInterval);
                                this._listeningInterval = null;
                            }
                        }
                    }, 2000 + Math.random() * 1000);
                }
                break;

            case 'idle':
            default:
                // Clear any listening interval
                if (this._listeningInterval) {
                    clearInterval(this._listeningInterval);
                    this._listeningInterval = null;
                }

                // Add idle animations
                this.addIdleAnimations();
                break;
        }
    }
    /**
      * Stop the animation loop
      */
    stopAnimationLoop() {
        this.isRunning = false;

        // Clear the animation queue to prevent warnings about missing templates
        // when animations are stopped externally
        this.animQueue = [];

        logger.info('Animation loop stopped');
    }

    /**
     * Enable the gesture system
     * @param {Object} options - Gesture options
     */
    async enableGestures(options = {}) {
        if (!this.gestureSystemEnabled) {
            this.gestureSystemEnabled = true;
            await this._initializeGestureSystem();

            if (this.options.debug) {
                logger.debug('Gesture system enabled');
            }
        } else if (this.handGestureSystem) {
            // Update options if already enabled
            this.handGestureSystem.updateOptions(options);
        }
    }

    /**
     * Disable the gesture system
     */
    disableGestures() {
        if (this.gestureSystemEnabled) {
            this.gestureSystemEnabled = false;

            if (this.handGestureSystem) {
                this.handGestureSystem.disable();
            }

            if (this.options.debug) {
                logger.debug('Gesture system disabled');
            }
        }
    }

    /**
     * Check if gesture system is enabled and ready
     */
    isGestureSystemReady() {
        return this.gestureSystemEnabled &&
            this.handGestureSystem &&
            this.handGestureSystem.isReady();
    }

    /**
     * Get gesture system status
     */
    getGestureSystemStatus() {
        if (!this.handGestureSystem) {
            return {
                enabled: false,
                ready: false,
                available: false
            };
        }

        return this.handGestureSystem.getStatus();
    }

    /**
     * Dispose resources
     */
    dispose() {
        logger.info('Disposing BaseAnimator');

        // Stop animation loop
        this.stopAnimationLoop();

        // Clear animation queue
        this.animQueue = [];
        this.speechQueue = [];

        // Disconnect audio nodes
        if (this.silentDestination) {
            this.silentDestination.disconnect();
        }

        if (this.audioAnalyzer) {
            this.audioAnalyzer.disconnect();
        }

        // Close audio context
        if (this.audioContext) {
            try {
                this.audioContext.close();
            } catch (error) {
                logger.error('Error closing audio context:', error);
            }
        }

        // Dispose gesture system
        if (this.handGestureSystem) {
            this.handGestureSystem.dispose();
            this.handGestureSystem = null;
        }

        // Clear references
        this.audioContext = null;
        this.audioAnalyzer = null;
        this.silentDestination = null;
        this.mesh = null;

        logger.info('BaseAnimator resources disposed');
    }

    /**
     * Hand animation system for speaking gestures
     * Uses gesture system if enabled, otherwise does nothing (clean default behavior)
     */
    speakWithHands(delay = 0, probability = 0.5) {
        // Only use gestures if the gesture system is enabled and ready
        if (this.handGestureSystem && this.handGestureSystem.isReady()) {
            this.handGestureSystem.speakWithHands(delay, probability);
            return;
        }

        // When gesture system is disabled (default), do nothing
        // This keeps the system clean and lightweight
        if (this.options.debug && Math.random() < 0.1) {
            logger.debug('🚫 Hand gestures disabled - speakWithHands() skipped');
        }
    }
}

/**
 * GESTURE SYSTEM INTEGRATION
 * 
 * The BaseAnimator now supports an optional gesture system for advanced hand movements.
 * By default, gestures are disabled to keep the system lightweight and clean.
 * 
 * To enable gestures:
 * 1. During initialization: new BaseAnimator(mesh, { enableGestures: true })
 * 2. At runtime: await animator.enableGestures()
 * 3. Direct usage: import { setupGestureSystem } from './gestures/index.js'
 * 
 * When gestures are disabled (default):
 * - speakWithHands() does nothing (clean behavior)
 * - No IK calculations are performed
 * - Minimal memory and CPU usage
 * 
 * When gestures are enabled:
 * - Advanced IK-based hand movements
 * - Natural gesture creation and management
 * - Configurable probabilities and timing
 * - Comprehensive debugging and visualization
 */

