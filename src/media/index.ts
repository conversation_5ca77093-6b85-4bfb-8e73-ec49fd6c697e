/**
 * Media module exports
 * Provides a unified API for media capture, processing, and multimodal interactions
 */

// Export core types
export type {
    MediaType,
    CaptureInfo,
    MediaCaptureOptions,
    VADOptions,
    VADResult
} from './utils/mediaTypes';

// Export core utilities (legacy - gradually moving to modality structure)
export { formatMediaForVLLM } from './utils/mediaUtils';
export { extractFrames, extractFramesInNode } from './modality/video.js';
export { createVLLMMediaPayload } from './utils/vllmUtils';

// Export legacy audio utilities (for backward compatibility)
export {
    AudioFormat,
    DEFAULT_AUDIO_CONFIG as DEFAULT_AUDIO_OPTIONS
} from './modality/audio.js';

// Export NEW MODALITY STRUCTURE - preferred imports
// Audio processing classes from new modality structure
export {
    AudioProcessor,
    RealtimeAudioManager,
    UniversalAudioConverter,
    StreamingAudioProcessor,
    AudioResponseProcessor,
    AudioConfig,
    DEFAULT_AUDIO_CONFIG
} from './modality/audio.js';

// Video processing classes from new modality structure
export {
    VideoProcessor,
    VideoElementManager,
    WebCodecsVideoProcessor,
    VideoConfig,
    DEFAULT_VIDEO_CONFIG,
    FrameInfo
} from './modality/video.js';

// Audio processing functions from new modality structure
export {
    processRealtimeAudio,
    validateAudioData,
    createFallbackBase64Audio,
    convertFloat32ToWav,
    detectAudioFormat,
    base64ToBlob,
    blobToBase64,
    checkAudioBuffer,
    createFallbackTone,
    createAudioFromBlob,
    playAudioWithPromise,
    splitAudioIntoChunks,
    b64ToArrayBuffer,
    concatArrayBuffers,
    pcmToAudioBuffer,
    setReverb,
    setMixerGain,
    extractRoleNameFromAudioFile,
    prepareTTSRequestPayload
} from './modality/audio.js';

// Export audio analysis utilities (consolidated into audio.ts)
export {
    extractAudioAnalysis,
    processAudioDataWithAnalysis,
    createAudioProcessingConfig,
    type AudioAnalysisResult,
    type AudioProcessingOptions,
    DEFAULT_AUDIO_PROCESSING_OPTIONS,
    // Re-export volume calculation from audio.ts for consistency
    calculateAudioVolume,
    estimateAudioQuality
} from './modality/audio.js';

// Legacy compatibility export
export { handleAudioData } from './modality/audio.js';

// Video processing from new modality structure  
export {
    processVideoForRealtime,
    extractFramesInBrowser,
    createVideoThumbnail,
    detectVideoFormat,
    validateVideoInput
} from './modality/video.js';

// Export multimodal processing utilities
export {
    normalizeInput,
    validateMultimodalInput,
    // Individual utilities that exist in modality structure
    InputCoordination,
    InputEventType,
    type InputEvent,
    type AudioInputEvent,
    type VideoInputEvent,
    type TextInputEvent,
    type MultimodalInputEvent,
    type InputEventListener,
    type InputCoordinationOptions
} from './modality/index.js';

// Export additional utilities from video processing
export {
    compressVideoFrame
} from './modality/video.js';

// Export capture components
export * from './capture';

// Export state management
export * from './state';


// Export core camera management
export { CameraManager } from './core/CameraManager.js';

// Note: API layer removed - use fetchLLMApi from src/utils/apiProxy.ts instead
