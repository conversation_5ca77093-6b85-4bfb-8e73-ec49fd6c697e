/**
 * CameraManager
 *
 * A unified camera management system that handles:
 * - Single stream management across all components
 * - Stream sharing between gesture control and capture functionalities
 * - Efficient streaming for video sampling and recording
 * - Delegates UI concerns to CameraViewer component
 */

import { MediaCaptureManager } from '../capture/MediaCaptureManager.ts';
import { mediaPermissionManager } from '../MediaPermissionManager.js';

export class CameraManager {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            streamQuality: {
                video: {
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    frameRate: { ideal: 30 },
                    facingMode: 'user'
                },
                audio: false
            },
            ...options
        };

        // Core state
        this.isInitialized = false;
        this.isActive = false;
        this.mediaStream = null;
        this.videoElement = null;

        // Callbacks for different components
        this.gestureCallbacks = new Set();
        this.captureCallbacks = new Set();
        this.frameCallbacks = new Set();

        // Media capture manager for advanced features
        this.mediaCaptureManager = new MediaCaptureManager({
            video: this.options.streamQuality.video,
            audio: this.options.streamQuality.audio,
            onCaptureStart: () => this._notifyCallbacks('streamStart'),
            onCaptureStop: () => this._notifyCallbacks('streamStop'),
            onFrame: (frameInfo) => this._notifyFrameCallbacks(frameInfo)
        });

        // Bind methods
        this._handleVisibilityChange = this._handleVisibilityChange.bind(this);
        this._handleResize = this._handleResize.bind(this);
    }

    /**
     * Initialize the camera manager
     */
    async initialize() {
        if (this.isInitialized) {
            console.log('[CameraManager] Already initialized');
            return true;
        }

        try {
            console.log('[CameraManager] Initializing...');

            // Initialize media capture manager - CameraManager is for video/camera functionality only
            // Don't try to fallback to audio since CameraManager is configured with audio: false
            try {
                const initSuccess = await this.mediaCaptureManager.startCapture('video');
                if (!initSuccess) {
                    console.warn('[CameraManager] Failed to initialize video capture - continuing without camera access');
                    // Don't throw error - main system can work without camera
                } else {
                    console.log('[CameraManager] Successfully initialized video capture');
                }
            } catch (error) {
                console.warn('[CameraManager] Video capture initialization failed:', error.message);
                // Continue without throwing - camera functionality is optional
            }

            // Set up event listeners
            document.addEventListener('visibilitychange', this._handleVisibilityChange);
            window.addEventListener('resize', this._handleResize);

            this.isInitialized = true;
            console.log('[CameraManager] Initialized successfully');
            return true;
        } catch (error) {
            console.error('[CameraManager] Initialization failed:', error);
            return false;
        }
    }

    /**
     * Start camera stream using centralized permission manager
     */
    async startCamera() {
        if (this.isActive) {
            console.log('[CameraManager] Camera already active');
            return this.mediaStream;
        }

        try {
            console.log('[CameraManager] Starting camera via MediaCaptureManager...');

            // Start video capture through MediaCaptureManager (handles permissions internally)
            const captureStarted = await this.mediaCaptureManager.startCameraCapture();
            if (!captureStarted) {
                throw new Error('Failed to start video capture');
            }

            // Get the stream from MediaCaptureManager
            this.mediaStream = this.mediaCaptureManager.getMediaStream();
            if (!this.mediaStream) {
                throw new Error('No media stream available from MediaCaptureManager');
            }

            // Get the video element from the capture manager
            this.videoElement = this.mediaCaptureManager.getVideoElement();
            if (!this.videoElement) {
                console.warn('[CameraManager] No video element from capture manager, creating basic element');
                this._createBasicVideoElement();
            }

            this.isActive = true;

            // Notify callbacks
            this._notifyCallbacks('streamStart', { stream: this.mediaStream });

            console.log('[CameraManager] Camera started successfully via MediaCaptureManager');
            return this.mediaStream;
        } catch (error) {
            console.error('[CameraManager] Failed to start camera:', error);
            this.isActive = false;
            throw error;
        }
    }

    /**
     * Stop camera stream and release permissions
     */
    stopCamera() {
        if (!this.isActive) {
            return;
        }

        try {
            console.log('[CameraManager] Stopping camera via MediaCaptureManager...');

            // Stop media capture (handles stream cleanup and permission release)
            this.mediaCaptureManager.stopCameraCapture();

            // Clean up local references
            this.videoElement = null;
            this.mediaStream = null;
            this.isActive = false;

            // Notify callbacks
            this._notifyCallbacks('streamStop');

            console.log('[CameraManager] Camera stopped via MediaCaptureManager');
        } catch (error) {
            console.error('[CameraManager] Error stopping camera:', error);
        }
    }

    /**
     * Check if camera stream is currently active
     */
    isStreamActive() {
        return this.mediaCaptureManager.isStreamActive() && this.isActive;
    }

    /**
     * Check if camera manager is active (alternative method name)
     */
    isCameraActive() {
        return this.mediaCaptureManager.isCameraActive();
    }

    /**
     * Get current stream status
     */
    getStreamStatus() {
        return {
            isActive: this.isActive,
            hasStream: !!this.mediaStream,
            streamActive: this.mediaStream?.active || false,
            isInitialized: this.isInitialized
        };
    }

    /**
     * Show camera UI - delegates to CameraViewer
     * This method is kept for compatibility but should use CameraViewer directly
     */
    async showCamera(mode = null) {
        console.log('[CameraManager] showCamera() called - UI should use CameraViewer.openViewer() directly');

        if (!this.isActive) {
            await this.startCamera();
        }

        // Notify callbacks that view should be shown
        this._notifyCallbacks('viewShow', { mode: mode || 'default' });

        console.log('[CameraManager] For UI display, please use CameraViewer component instead');
    }

    /**
     * Hide camera UI - delegates to CameraViewer  
     * This method is kept for compatibility but should use CameraViewer directly
     */
    async hideCamera() {
        console.log('[CameraManager] hideCamera() called - UI should use CameraViewer.closeViewer() directly');

        // Notify callbacks that view should be hidden
        this._notifyCallbacks('viewHide');

        console.log('[CameraManager] For UI control, please use CameraViewer component instead');
    }

    /**
     * Toggle camera mode - delegates to CameraViewer
     * This method is kept for compatibility but should use CameraViewer directly
     */
    async toggleMode() {
        console.log('[CameraManager] toggleMode() called - UI should use CameraViewer methods directly');

        // For compatibility, just notify of mode change
        this._notifyCallbacks('modeToggle');

        console.log('[CameraManager] For UI control, please use CameraViewer component instead');
    }

    /**
     * Capture a photo from the current stream
     */
    async capturePhoto(options = {}) {
        if (!this.isActive || !this.videoElement) {
            throw new Error('Camera not active or video element not available');
        }

        try {
            const frameDataUrl = await this.mediaCaptureManager.captureFrame();
            if (!frameDataUrl) {
                throw new Error('Failed to capture frame');
            }

            // Notify capture callbacks
            this._notifyCallbacks('photoCapture', {
                dataUrl: frameDataUrl,
                options
            });

            return frameDataUrl;
        } catch (error) {
            console.error('[CameraManager] Photo capture failed:', error);
            throw error;
        }
    }

    /**
     * Get the current video element for external use
     */
    getVideoElement() {
        return this.videoElement;
    }

    /**
     * Get the current media stream for external use
     */
    getMediaStream() {
        return this.mediaCaptureManager.getMediaStream();
    }

    /**
     * Register callbacks for gesture control
     */
    registerGestureCallbacks(callbacks) {
        this.gestureCallbacks.add(callbacks);
        return () => this.gestureCallbacks.delete(callbacks);
    }

    /**
     * Register callbacks for capture functionality
     */
    registerCaptureCallbacks(callbacks) {
        this.captureCallbacks.add(callbacks);
        return () => this.captureCallbacks.delete(callbacks);
    }

    /**
     * Register callbacks for video frame processing
     */
    registerFrameCallbacks(callback) {
        this.frameCallbacks.add(callback);
        return () => this.frameCallbacks.delete(callback);
    }

    /**
     * Extract frames from video data for multimodal processing
     * @param {any} videoData - Video data (Blob, File, ArrayBuffer, etc.)
     * @param {number} fps - Frames per second to extract (default: 2)
     * @returns {Promise<string[]>} Array of base64 encoded frame data URLs
     */
    async extractFramesFromVideo(videoData, fps = 2) {
        try {
            // Import the shared implementation from video modality
            const { extractFrames } = await import('../modality/video.ts');

            // Use the shared implementation
            return await extractFrames(videoData, fps);
        } catch (error) {
            console.error('[CameraManager] Error extracting frames from video:', error);
            throw error;
        }
    }

    /**
     * Capture current video frames from the active camera stream
     * @param {number} maxFrames - Maximum number of frames to capture (default: 3)
     * @returns {Promise<string[]>} Array of base64 encoded frame data URLs
     */
    async captureCurrentFrames(maxFrames = 3) {
        try {
            if (!this.isCameraActive()) {
                console.warn('[CameraManager] Cannot capture frames - camera not active');
                return [];
            }

            const videoElement = this.getVideoElement();
            if (!videoElement || videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
                console.warn('[CameraManager] Cannot capture frames - video element not ready');
                return [];
            }

            const frames = [];
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            canvas.width = videoElement.videoWidth;
            canvas.height = videoElement.videoHeight;

            // Capture frames (for live stream, we can only capture current frame)
            for (let i = 0; i < maxFrames; i++) {
                // Draw current video frame to canvas
                ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

                // Convert canvas to base64 data URL
                const dataUrl = canvas.toDataURL('image/jpeg', 0.8);
                frames.push(dataUrl);

                // Small delay between captures for different frames
                if (i < maxFrames - 1) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            console.log(`[CameraManager] Captured ${frames.length} frames from active camera stream`);
            return frames;
        } catch (error) {
            console.error('[CameraManager] Error capturing current frames:', error);
            return [];
        }
    }





    /**
     * Create a basic video element when MediaCaptureManager doesn't provide one
     * @private
     */
    _createBasicVideoElement() {
        this.videoElement = document.createElement('video');
        this.videoElement.srcObject = this.mediaStream;
        this.videoElement.autoplay = true;
        this.videoElement.playsInline = true;
        this.videoElement.muted = true;
        this.videoElement.style.display = 'none';
        document.body.appendChild(this.videoElement);
        console.log('[CameraManager] Created basic video element');
    }

    /**
     * Notify all registered callbacks
     * @private
     */
    _notifyCallbacks(event, data = {}) {
        const allCallbacks = new Set([
            ...this.gestureCallbacks,
            ...this.captureCallbacks
        ]);

        allCallbacks.forEach(callbackSet => {
            if (callbackSet[event] && typeof callbackSet[event] === 'function') {
                try {
                    callbackSet[event](data);
                } catch (error) {
                    console.error(`[CameraManager] Error in ${event} callback:`, error);
                }
            }
        });
    }

    /**
     * Notify frame processing callbacks
     * @private
     */
    _notifyFrameCallbacks(frameInfo) {
        this.frameCallbacks.forEach(callback => {
            try {
                callback(this.videoElement, frameInfo);
            } catch (error) {
                console.error('[CameraManager] Error in frame callback:', error);
            }
        });
    }

    /**
     * Handle visibility change
     * @private
     */
    _handleVisibilityChange() {
        if (document.hidden && this.isActive) {
            console.log('[CameraManager] Page hidden, pausing camera');
            // Optionally pause camera when page is hidden
        } else if (!document.hidden && this.isActive) {
            console.log('[CameraManager] Page visible, resuming camera');
            // Optionally resume camera when page is visible
        }
    }

    /**
     * Handle window resize
     * @private
     */
    _handleResize() {
        if (this.cornerContainer) {
            // Adjust corner position on resize if needed
        }
    }

    /**
     * Dispose and clean up all resources
     */
    dispose() {
        console.log('[CameraManager] Disposing...');

        // Stop camera and release permissions
        this.stopCamera();

        // Clean up event listeners
        document.removeEventListener('visibilitychange', this._handleVisibilityChange);
        window.removeEventListener('resize', this._handleResize);

        // Dispose media capture manager
        if (this.mediaCaptureManager) {
            this.mediaCaptureManager.dispose();
        }

        // UI cleanup is now handled by CameraViewer component

        // Clear all callbacks
        this.gestureCallbacks.clear();
        this.captureCallbacks.clear();
        this.frameCallbacks.clear();

        this.isInitialized = false;

        console.log('[CameraManager] Disposal complete');
    }
}
