/**
 * Media Permission Manager Service
 * 
 * Provides centralized media (camera & microphone) permission management 
 * to prevent multiple permission requests and provide graceful fallback handling.
 * 
 * Features:
 * - Single point of media access control (camera, microphone, or both)
 * - Permission state caching and persistence  
 * - User-friendly permission request flow
 * - Graceful fallback when media access is denied
 * - Consolidated error handling and user feedback
 * - Prevention of redundant permission requests
 * - Unified interface for all media device types
 */

import { createLogger } from '../utils/logger';

export type MediaType = 'camera' | 'microphone' | 'both';

export interface MediaPermissionState {
  granted: boolean;
  denied: boolean;
  pending: boolean;
  error?: string;
  lastRequestTime?: number;
  userDeniedPermanently?: boolean;
  mediaType?: MediaType;
}

export interface MediaPermissionOptions {
  video?: MediaTrackConstraints | boolean;
  audio?: MediaTrackConstraints | boolean;
  mediaType?: MediaType;
  timeout?: number;
  retryDelay?: number;
  maxRetries?: number;
  showUserFeedback?: boolean;
  requestReason?: string;
}

export interface PermissionRequestResult {
  success: boolean;
  stream?: MediaStream;
  error?: string;
  permission: MediaPermissionState;
  userAction?: 'allow' | 'deny' | 'dismiss';
}

/**
 * Centralized Media Permission Manager
 * Prevents multiple simultaneous permission requests and provides unified fallback handling
 * for camera, microphone, and combined media access
 */
export class MediaPermissionManager {
  private logger = createLogger('MediaPermissionManager');

  // Permission state tracking
  private permissionState: MediaPermissionState = {
    granted: false,
    denied: false,
    pending: false
  };

  // Active request tracking
  private activeRequest: Promise<PermissionRequestResult> | null = null;
  private requestQueue: Array<{
    options: MediaPermissionOptions;
    resolve: (result: PermissionRequestResult) => void;
    reject: (error: Error) => void;
  }> = [];

  // Stream management
  private currentStream: MediaStream | null = null;
  private streamReferences = new Set<string>();

  // UI feedback elements
  private permissionModal: HTMLElement | null = null;
  private statusIndicator: HTMLElement | null = null;

  // Configuration
  private readonly MAX_RETRY_ATTEMPTS = 3;
  private readonly RETRY_DELAY = 2000; // 2 seconds
  private readonly PERMISSION_TIMEOUT = 10000; // 10 seconds
  private readonly PERMISSION_CACHE_KEY = 'media_permission_state';

  constructor() {
    this.logger.info('🎙️🎥 MediaPermissionManager initialized');
    this.loadPermissionState();
    this.setupGlobalEventListeners();
  }

  /**
   * Request media permission and access (camera, microphone, or both)
   * This is the main entry point for all media access requests
   */
  async requestMediaAccess(
    requesterId: string,
    options: MediaPermissionOptions = {}
  ): Promise<PermissionRequestResult> {
    // Determine media type from options
    const mediaType: MediaType = options.mediaType || (
      (options.video && options.audio) ? 'both' :
        options.audio ? 'microphone' : 'camera'
    );

    const finalOptions = {
      video: mediaType === 'microphone' ? false :
        (options.video || { width: { ideal: 640 }, height: { ideal: 480 }, facingMode: 'user' }),
      audio: mediaType === 'camera' ? false :
        (options.audio || { echoCancellation: true, noiseSuppression: true }),
      mediaType,
      timeout: this.PERMISSION_TIMEOUT,
      retryDelay: this.RETRY_DELAY,
      maxRetries: this.MAX_RETRY_ATTEMPTS,
      showUserFeedback: false, // Disabled to remove intrusive notifications
      requestReason: `This application needs ${mediaType} access to provide ${mediaType === 'both' ? 'video and audio' :
        mediaType === 'camera' ? 'video' : 'audio'
        } functionality.`,
      ...options
    };

    this.logger.info(`🎙️🎥 ${mediaType} access requested by: ${requesterId}`, {
      currentState: this.permissionState,
      options: finalOptions
    });

    // If there's already an active request, queue this one
    if (this.activeRequest) {
      this.logger.info(`📋 Queuing ${mediaType} request (another request in progress)`);
      return this.queueRequest(finalOptions);
    }

    // If we already have a granted stream, check if it matches the requested mediaType
    if (this.permissionState.granted && this.currentStream && this.currentStream.active) {
      const hasVideo = this.currentStream.getVideoTracks().length > 0;
      const hasAudio = this.currentStream.getAudioTracks().length > 0;

      // Check if existing stream satisfies the request
      const streamMatches = (
        (mediaType === 'camera' && hasVideo) ||
        (mediaType === 'microphone' && hasAudio) ||
        (mediaType === 'both' && hasVideo && hasAudio)
      );

      if (streamMatches) {
        this.streamReferences.add(requesterId);
        this.logger.info(`✅ Reusing existing ${mediaType} stream for: ${requesterId}`, {
          hasVideo, hasAudio, mediaType
        });
        return {
          success: true,
          stream: this.currentStream,
          permission: { ...this.permissionState },
          userAction: 'allow'
        };
      } else {
        this.logger.info(`🔄 Existing stream doesn't match ${mediaType} requirements, getting new stream`, {
          existingHasVideo: hasVideo, existingHasAudio: hasAudio,
          requestedMediaType: mediaType
        });
        // Stop current stream and get a new one
        this.stopCurrentStream();
      }
    }

    // If permission was permanently denied, check actual browser permission first
    if (this.permissionState.userDeniedPermanently) {
      this.logger.info('🔄 Checking actual browser permission status...');
      const actualPermissionStatus = await this.checkActualBrowserPermission(mediaType);

      if (actualPermissionStatus === 'granted') {
        this.logger.info('✅ Browser permission is now granted, resetting denial flag');
        this.resetPermanentDenialFlag();
      } else {
        this.logger.warn(`❌ ${mediaType} access still permanently denied by user`);
        return this.handlePermanentDenial();
      }
    }

    // Make the actual permission request
    this.activeRequest = this.performPermissionRequest(requesterId, finalOptions);

    try {
      const result = await this.activeRequest;
      this.logger.info(`🎙️🎥 ${mediaType} request completed for ${requesterId}:`, {
        success: result.success,
        hasStream: !!result.stream,
        permission: result.permission
      });
      return result;
    } finally {
      this.activeRequest = null;
      this.processRequestQueue();
    }
  }

  /**
   * Release media access for a specific requester
   */
  releaseMediaAccess(requesterId: string): void {
    this.streamReferences.delete(requesterId);
    this.logger.info(`🎙️🎥 Media access released by: ${requesterId}`, {
      remainingReferences: this.streamReferences.size
    });

    // If no more references, stop the stream
    if (this.streamReferences.size === 0 && this.currentStream) {
      this.stopCurrentStream();
    }
  }

  /**
   * Check current media permission status without requesting
   */
  async checkPermissionStatus(mediaType: MediaType = 'camera'): Promise<MediaPermissionState> {
    try {
      if (!navigator.permissions) {
        return { ...this.permissionState };
      }

      // Handle different media types
      let permissionResults: PermissionStatus[] = [];

      if (mediaType === 'both') {
        const [cameraPermission, microphonePermission] = await Promise.all([
          navigator.permissions.query({ name: 'camera' as PermissionName }),
          navigator.permissions.query({ name: 'microphone' as PermissionName })
        ]);
        permissionResults = [cameraPermission, microphonePermission];
      } else {
        const permissionName = mediaType === 'camera' ? 'camera' : 'microphone';
        const permission = await navigator.permissions.query({ name: permissionName as PermissionName });
        permissionResults = [permission];
      }

      // Aggregate permission states (all must be granted for overall granted state)
      const allGranted = permissionResults.every(p => p.state === 'granted');
      const anyDenied = permissionResults.some(p => p.state === 'denied');
      const anyPrompt = permissionResults.some(p => p.state === 'prompt');

      // Update our cached state
      this.permissionState = {
        granted: allGranted,
        denied: anyDenied,
        pending: anyPrompt,
        mediaType,
        lastRequestTime: this.permissionState.lastRequestTime,
        userDeniedPermanently: this.permissionState.userDeniedPermanently
      };

      this.savePermissionState();
      return { ...this.permissionState };
    } catch (error) {
      this.logger.warn('⚠️ Could not check media permission status:', error);
      return { ...this.permissionState };
    }
  }

  /**
   * Get current media stream if available
   */
  getCurrentStream(): MediaStream | null {
    return this.currentStream;
  }

  /**
   * Check if media is currently active
   */
  isMediaActive(): boolean {
    return !!(this.currentStream && this.currentStream.active && this.permissionState.granted);
  }

  /**
   * Reset permission state (useful for testing or manual reset)
   */
  resetPermissionState(): void {
    this.permissionState = {
      granted: false,
      denied: false,
      pending: false
    };
    this.savePermissionState();
    this.logger.info('🔄 Media permission state reset');
  }

  /**
   * Show user-friendly permission guidance (disabled to reduce notifications)
   */
  showPermissionGuidance(reason?: string): void {
    return; // Disabled to reduce user interruption
    if (this.permissionModal) {
      return; // Already showing
    }

    const modal = this.createPermissionModal(reason);
    document.body.appendChild(modal);
    this.permissionModal = modal;

    // Auto-hide after 10 seconds
    setTimeout(() => {
      this.hidePermissionGuidance();
    }, 10000);
  }

  /**
   * Hide permission guidance modal
   */
  hidePermissionGuidance(): void {
    if (this.permissionModal) {
      this.permissionModal.remove();
      this.permissionModal = null;
    }
  }

  /**
   * Get user-friendly error message for permission errors
   */
  getUserFriendlyErrorMessage(error: any): string {
    if (!error) return 'Unknown camera error';

    const errorName = error.name || error.code || '';
    const errorMessage = error.message || '';

    switch (errorName) {
      case 'NotAllowedError':
      case 'PermissionDeniedError':
        return 'Camera access was denied. Please allow camera access in your browser settings and refresh the page.';

      case 'NotFoundError':
      case 'DevicesNotFoundError':
        return 'No camera device found. Please connect a camera and try again.';

      case 'NotReadableError':
      case 'TrackStartError':
        return 'Camera is already in use by another application. Please close other applications using the camera.';

      case 'OverconstrainedError':
      case 'ConstraintNotSatisfiedError':
        return 'Camera settings are not supported. Trying with default settings.';

      case 'NotSupportedError':
        return 'Camera access is not supported in this browser.';

      case 'SecurityError':
        return 'Camera access blocked due to security restrictions. Please use HTTPS.';

      default:
        if (errorMessage.includes('Permission denied') || errorMessage.includes('denied')) {
          return 'Camera permission denied. Please check your browser settings.';
        }
        return `Camera error: ${errorMessage || 'Unknown error occurred'}`;
    }
  }

  /**
   * Dispose and cleanup all resources
   */
  dispose(): void {
    this.logger.info('🧹 Disposing MediaPermissionManager...');

    // Stop current stream
    this.stopCurrentStream();

    // Clear queued requests
    this.requestQueue.forEach(({ reject }) => {
      reject(new Error('CameraPermissionManager disposed'));
    });
    this.requestQueue = [];

    // Hide UI elements
    this.hidePermissionGuidance();
    if (this.statusIndicator) {
      this.statusIndicator.remove();
      this.statusIndicator = null;
    }

    // Clear references
    this.streamReferences.clear();
    this.activeRequest = null;

    this.logger.info('✅ MediaPermissionManager disposed');
  }

  // Private methods

  /**
   * Perform the actual permission request
   */
  private async performPermissionRequest(
    requesterId: string,
    options: MediaPermissionOptions
  ): Promise<PermissionRequestResult> {
    this.permissionState.pending = true;
    this.permissionState.lastRequestTime = Date.now();

    // Permission guidance disabled to reduce user interruption
    // if (options.showUserFeedback) {
    //   this.showPermissionGuidance(options.requestReason);
    // }

    try {
      const constraints: MediaStreamConstraints = {
        video: options.video || { width: { ideal: 640 }, height: { ideal: 480 }, facingMode: 'user' },
        audio: options.audio || false
      };

      this.logger.info('📷 Requesting getUserMedia:', constraints);

      // Use Promise.race to add timeout
      const streamPromise = navigator.mediaDevices.getUserMedia(constraints);
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Permission request timeout')), options.timeout || this.PERMISSION_TIMEOUT);
      });

      const stream = await Promise.race([streamPromise, timeoutPromise]);

      // Success!
      this.currentStream = stream;
      this.streamReferences.add(requesterId);
      this.permissionState = {
        granted: true,
        denied: false,
        pending: false,
        lastRequestTime: Date.now(),
        userDeniedPermanently: false
      };

      this.savePermissionState();
      this.hidePermissionGuidance();

      this.logger.info('✅ Camera permission granted successfully');

      return {
        success: true,
        stream,
        permission: { ...this.permissionState },
        userAction: 'allow'
      };

    } catch (error) {
      this.logger.error('❌ Camera permission request failed:', error);
      const err = error as Error;

      // Update permission state based on error
      const isPermissionDenied = err.name === 'NotAllowedError' ||
        err.name === 'PermissionDeniedError' ||
        Boolean(err.message && err.message.includes('Permission denied'));

      this.permissionState = {
        granted: false,
        denied: isPermissionDenied,
        pending: false,
        error: err.message,
        lastRequestTime: Date.now(),
        userDeniedPermanently: isPermissionDenied
      };

      this.savePermissionState();

      // User feedback disabled to reduce notifications
      // if (options.showUserFeedback) {
      //   this.showPermissionError(this.getUserFriendlyErrorMessage(error));
      // }

      return {
        success: false,
        error: err.message,
        permission: { ...this.permissionState },
        userAction: isPermissionDenied ? 'deny' : 'dismiss'
      };
    }
  }

  /**
   * Queue a permission request when another is active
   */
  private queueRequest(options: MediaPermissionOptions): Promise<PermissionRequestResult> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({ options, resolve, reject });
    });
  }

  /**
   * Process queued permission requests
   */
  private processRequestQueue(): void {
    if (this.requestQueue.length === 0 || this.activeRequest) {
      return;
    }

    const next = this.requestQueue.shift();
    if (next) {
      this.requestMediaAccess('queued-request', next.options)
        .then(next.resolve)
        .catch(next.reject);
    }
  }

  /**
   * Handle permanent denial case
   */
  private handlePermanentDenial(): PermissionRequestResult {
    const errorMessage = 'Media access was previously denied. Please enable media permissions in your browser settings and refresh the page to use media features.';

    // Error notifications disabled to reduce user interruption
    // this.showPermissionError(errorMessage);

    return {
      success: false,
      error: errorMessage,
      permission: { ...this.permissionState },
      userAction: 'deny'
    };
  }

  /**
   * Stop the current stream and clean up
   */
  private stopCurrentStream(): void {
    if (this.currentStream) {
      this.currentStream.getTracks().forEach(track => {
        track.stop();
      });
      this.currentStream = null;
      this.streamReferences.clear();
      this.logger.info('🛑 Media stream stopped');
    }
  }

  /**
   * Load permission state from storage
   */
  private loadPermissionState(): void {
    try {
      const stored = localStorage.getItem(this.PERMISSION_CACHE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Only restore non-sensitive state info
        this.permissionState = {
          granted: false, // Always re-check granted status
          denied: parsed.denied || false,
          pending: false,
          userDeniedPermanently: parsed.userDeniedPermanently || false,
          lastRequestTime: parsed.lastRequestTime
        };
      }
    } catch (error) {
      this.logger.warn('⚠️ Could not load permission state from storage:', error);
    }
  }

  /**
   * Save permission state to storage
   */
  private savePermissionState(): void {
    try {
      localStorage.setItem(this.PERMISSION_CACHE_KEY, JSON.stringify(this.permissionState));
    } catch (error) {
      this.logger.warn('⚠️ Could not save permission state to storage:', error);
    }
  }

  /**
   * Setup global event listeners
   */
  private setupGlobalEventListeners(): void {
    // In non-browser environments (e.g., unit tests), document/window may not exist
    if (typeof document === 'undefined' || typeof window === 'undefined') {
      return;
    }

    // Listen for page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && this.currentStream) {
        this.logger.info('📱 Page hidden, maintaining camera stream');
      }
    });

    // Listen for before unload to clean up
    window.addEventListener('beforeunload', () => {
      this.dispose();
    });
  }

  /**
   * Create permission guidance modal
   */
  private createPermissionModal(reason?: string): HTMLElement {
    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    const content = document.createElement('div');
    content.style.cssText = `
      background: white;
      padding: 30px;
      border-radius: 12px;
      max-width: 500px;
      margin: 20px;
      text-align: center;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    `;

    content.innerHTML = `
      <div style="margin-bottom: 20px;">
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" style="color: #4CAF50;">
          <path d="M17 10.5V7a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h12a1 1 0 001-1v-3.5l4 4v-11l-4 4z" 
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="currentColor"/>
        </svg>
      </div>
      <h3 style="margin: 0 0 15px 0; color: #333;">Camera Access Requested</h3>
      <p style="margin: 0 0 20px 0; color: #666; line-height: 1.5;">
        ${reason || 'This application needs camera access to provide video functionality.'}
      </p>
      <p style="margin: 0 0 20px 0; color: #888; font-size: 14px;">
        Please click "Allow" when your browser asks for camera permission.
      </p>
      <button id="permission-dismiss" style="
        background: #f5f5f5;
        border: none;
        padding: 10px 20px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
      ">Dismiss</button>
    `;

    // Add dismiss functionality
    const dismissBtn = content.querySelector('#permission-dismiss') as HTMLButtonElement;
    dismissBtn.addEventListener('click', () => {
      this.hidePermissionGuidance();
    });

    modal.appendChild(content);
    return modal;
  }

  /**
   * Show permission error message (disabled to reduce notifications) 
   */
  private showPermissionError(message: string): void {
    return; // Disabled to reduce user interruption
    this.hidePermissionGuidance(); // Hide existing modal first

    const errorModal = document.createElement('div');
    errorModal.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #f44336;
      color: white;
      padding: 15px 20px;
      border-radius: 8px;
      max-width: 400px;
      z-index: 10001;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
    `;

    errorModal.innerHTML = `
      <div style="display: flex; align-items: flex-start;">
        <div style="margin-right: 10px;">⚠️</div>
        <div style="flex: 1; font-size: 14px; line-height: 1.4;">${message}</div>
        <button onclick="this.parentElement.parentElement.remove()" style="
          background: none;
          border: none;
          color: white;
          cursor: pointer;
          margin-left: 10px;
          font-size: 18px;
        ">×</button>
      </div>
    `;

    document.body.appendChild(errorModal);

    // Auto-hide after 8 seconds
    setTimeout(() => {
      if (errorModal.parentNode) {
        errorModal.remove();
      }
    }, 8000);
  }

  /**
   * Check actual browser media permission status using Permissions API
   * @private
   */
  private async checkActualBrowserPermission(mediaType: MediaType = 'camera'): Promise<string> {
    try {
      // Check if Permissions API is available
      if (!navigator.permissions || !navigator.permissions.query) {
        this.logger.warn('Permissions API not available, cannot check actual permission status');
        return 'unknown';
      }

      // Handle different media types
      if (mediaType === 'both') {
        const [cameraPermission, microphonePermission] = await Promise.all([
          navigator.permissions.query({ name: 'camera' as PermissionName }),
          navigator.permissions.query({ name: 'microphone' as PermissionName })
        ]);

        // Both must be granted for "granted" status
        if (cameraPermission.state === 'granted' && microphonePermission.state === 'granted') {
          return 'granted';
        } else if (cameraPermission.state === 'denied' || microphonePermission.state === 'denied') {
          return 'denied';
        } else {
          return 'prompt';
        }
      } else {
        // Query specific permission status
        const permissionName = mediaType === 'camera' ? 'camera' : 'microphone';
        const permissionResult = await navigator.permissions.query({ name: permissionName as PermissionName });

        this.logger.debug(`Actual browser ${mediaType} permission: ${permissionResult.state}`);
        return permissionResult.state;
      }

    } catch (error) {
      this.logger.warn('Failed to check actual browser permission:', error);
      return 'unknown';
    }
  }

  /**
   * Reset the permanent denial flag and update permission state
   * @private
   */
  private resetPermanentDenialFlag(): void {
    this.permissionState = {
      ...this.permissionState,
      granted: false, // Will be set to true after successful getUserMedia call
      denied: false,
      userDeniedPermanently: false,
      error: undefined,
      lastRequestTime: Date.now()
    };

    // Save the updated state
    this.savePermissionState();

    this.logger.info('🔄 Permission denial flag reset, ready to request media access again');
  }
}

// Export singleton instance
export const mediaPermissionManager = new MediaPermissionManager();
export default mediaPermissionManager;