# Media Module Architecture Analysis and Reorganization Plan

## Executive Summary

The `src/media` module suffers from significant architectural debt, with **3,600+ lines in a single audio.ts file**, multiple duplicate utility functions, and unclear separation of concerns. This analysis provides a comprehensive reorganization plan to improve maintainability, reduce redundancy, and better support the dual-brain streaming architecture.

## Current State Analysis

### Module Structure Issues

```
src/media/
├── modality/
│   ├── audio.ts (3,611 lines) ⚠️ MASSIVE FILE
│   ├── video.ts (793 lines)
│   ├── inputCoordination.ts (792 lines)
│   └── index.ts (173 lines)
├── capture/
│   └── MediaCaptureManager.ts (1,095 lines)
├── core/
│   └── CameraManager.js (982 lines) ⚠️ JS/TS MIX
├── utils/
│   ├── mediaUtils.ts (49 lines)
│   ├── vadUtils.ts (82 lines)
│   ├── vllmUtils.ts (76 lines)
│   ├── environment.ts (52 lines)
│   └── mediaTypes.ts (138 lines)
└── index.ts (104 lines)
```

### Critical Redundancy Issues Identified

#### 1. **Duplicate Format Conversion Functions**
- `base64ToBlob()` - 2 implementations (audio.ts:735, audio.ts:1472)
- `blobToBase64()` - Re-exported in utils/mediaUtils.ts 
- `formatMediaForVLLM()` - vLLM-specific wrapper around existing functions

#### 2. **Multiple Frame Processing Implementations**
- `extractFrames()` - Universal function (video.ts:765)
- `extractFramesInBrowser()` - Browser-specific (video.ts:411)
- `extractFramesInNode()` - Node.js-specific (video.ts:709)
- `processFrame()` - Single frame processing (video.ts:127)
- `processFrames()` - Batch processing (video.ts:254)

#### 3. **Logger Setup Duplication**
Every module creates its own logger instance:
```typescript
// Repeated pattern across 6+ files
import { createLogger } from '../../utils/logger.js';
const logger = createLogger('ModuleName');
```

#### 4. **Configuration Interface Proliferation**
- `AudioConfig` (audio.ts:411)
- `VideoConfig` (video.ts:33) 
- `AudioProcessingOptions` (audio.ts:3172)
- `MediaCaptureOptions` (mediaTypes.ts:14)
- `InputCoordinationOptions` (inputCoordination.ts:76)

#### 5. **Audio Processing Over-Engineering**
The audio.ts file contains multiple processing pipelines:
- `UniversalAudioConverter` class (lines 47-385)
- `RealtimeAudioProcessor` class (lines 1487-1738)
- `processRealtimeAudio()` function (lines 774-1183)
- `createRealtimeAudioProcessor()` factory (lines 2916-3170)

## Dual-Brain Streaming Architecture Compatibility Analysis

### Current WebSocket Integration
- Audio modality has WebSocket hooks: `sendToWebSocket?: (data: any, format: string) => Promise<boolean>`
- Implementation is scattered across multiple processing functions
- No centralized streaming coordinator for the dual-brain pipeline

### System 1 (WebSocket/Realtime) Issues
- Multiple audio processors create competing streams
- No unified stream management for real-time audio/video
- Frame processing not optimized for WebSocket chunking

### System 2 (HTTP/Reasoning) Issues
- vLLM utilities are separate and not integrated with streaming pipeline
- No proper batching for HTTP-based multimodal requests
- Context analysis scattered across multiple files

## Proposed Architecture Reorganization

### 1. **Core Streaming Architecture** 
```
src/media/
├── streaming/
│   ├── StreamCoordinator.ts          # Central streaming orchestrator
│   ├── RealtimeChannel.ts            # System 1 WebSocket handling
│   ├── ReasoningChannel.ts           # System 2 HTTP batching
│   └── DualBrainBridge.ts            # Bridge between systems
├── processors/
│   ├── AudioProcessor.ts             # Unified audio processing
│   ├── VideoProcessor.ts             # Unified video processing
│   └── MultimodalProcessor.ts        # Cross-modal coordination
├── formats/
│   ├── Converters.ts                 # All format conversion (SINGLE SOURCE)
│   ├── Validators.ts                 # Input validation
│   └── Codecs.ts                     # Audio/video codecs
├── capture/
│   ├── MediaCaptureManager.ts        # (Refactored)
│   └── CameraManager.ts              # (TS conversion)
└── types/
    ├── StreamingTypes.ts             # Streaming-specific types
    ├── ProcessingTypes.ts            # Processing configurations
    └── MediaTypes.ts                 # Core media types
```

### 2. **Dependency Injection Architecture**

#### Core Interfaces
```typescript
interface IMediaProcessor {
  process(input: MediaInput): Promise<ProcessedOutput>;
  configure(config: ProcessingConfig): void;
  dispose(): void;
}

interface IStreamChannel {
  send(data: StreamData): Promise<void>;
  onMessage(handler: MessageHandler): void;
  connect(): Promise<void>;
  disconnect(): void;
}

interface IFormatConverter {
  convert(input: any, targetFormat: string): Promise<any>;
  supports(format: string): boolean;
}
```

#### Service Locator Pattern
```typescript
class MediaServiceContainer {
  private services = new Map<string, any>();
  
  register<T>(key: string, implementation: T): void;
  resolve<T>(key: string): T;
  
  // Pre-configured factories
  createAudioProcessor(config?: AudioConfig): IAudioProcessor;
  createVideoProcessor(config?: VideoConfig): IVideoProcessor;
  createStreamCoordinator(): IStreamCoordinator;
}
```

### 3. **Streaming Pipeline Integration**

#### Dual-Brain Bridge
```typescript
class DualBrainBridge {
  constructor(
    private realtimeChannel: IRealtimeChannel,
    private reasoningChannel: IReasoningChannel,
    private mediaProcessor: IMultimodalProcessor
  ) {}
  
  async processInput(input: MultimodalInput): Promise<void> {
    // System 1: Immediate streaming for real-time feedback
    if (input.requiresRealtime) {
      await this.realtimeChannel.stream(input);
    }
    
    // System 2: Batched processing for reasoning
    if (input.requiresReasoning) {
      await this.reasoningChannel.queue(input);
    }
  }
}
```

## Implementation Plan

### Phase 1: Core Architecture (Week 1)
1. **Extract format converters** - Single source of truth in `formats/Converters.ts`
2. **Create service container** - Dependency injection foundation
3. **Refactor logger setup** - Centralized logging configuration

### Phase 2: Streaming Integration (Week 2)
1. **Implement StreamCoordinator** - Central streaming orchestration
2. **Create DualBrainBridge** - System 1/2 coordination
3. **Refactor WebSocket handling** - Unified streaming interface

### Phase 3: Processor Consolidation (Week 3)
1. **Consolidate audio processing** - Single AudioProcessor class
2. **Unified video processing** - Consistent frame handling
3. **Remove redundant classes** - Delete obsolete implementations

### Phase 4: Type System Cleanup (Week 4)
1. **Consolidate configuration types** - Unified config interfaces
2. **Update imports** - Fix all references
3. **Documentation** - API documentation and migration guide

## Functions to Remove/Consolidate

### Immediate Removal Candidates
```typescript
// audio.ts - Remove duplicate implementations
- base64ToBlobEnhanced() // Line 1472 - superseded by base64ToBlob()
- Multiple logger instances // Use centralized logging
- Redundant validation functions

// utils/ - Consolidate into formats/
- utils/mediaUtils.ts // Re-exports - merge into Converters.ts
- utils/vllmUtils.ts // Move to streaming/ReasoningChannel.ts
```

### Consolidation Targets
```typescript
// Audio Processing - Consolidate into single processor
class AudioProcessor implements IMediaProcessor {
  // Merge functionality from:
  // - UniversalAudioConverter
  // - RealtimeAudioProcessor  
  // - processRealtimeAudio()
  // - createRealtimeAudioProcessor()
}

// Frame Processing - Unified interface
class FrameProcessor {
  // Consolidate:
  // - extractFrames() variants
  // - processFrame() methods
  // - Video thumbnail generation
}
```

## WebSocket Streaming Optimizations

### Current Issues
- No stream backpressure handling
- Inefficient frame chunking
- Missing error recovery

### Proposed Solutions
```typescript
class RealtimeChannel implements IRealtimeChannel {
  async streamAudio(audioData: Float32Array): Promise<void> {
    // Optimized chunking for WebSocket MTU
    const chunks = this.chunkAudio(audioData, 1400); // MTU-optimized
    
    for (const chunk of chunks) {
      await this.sendWithBackpressure(chunk);
    }
  }
  
  private async sendWithBackpressure(data: any): Promise<void> {
    if (this.bufferSize > this.maxBuffer) {
      await this.waitForDrain();
    }
    return this.socket.send(data);
  }
}
```

## Migration Strategy

### Backward Compatibility
1. **Deprecation warnings** - Add to old functions
2. **Proxy exports** - Maintain existing imports during transition  
3. **Feature flags** - Gradual rollout of new architecture

### Testing Strategy
1. **Unit tests** - Each new processor/converter
2. **Integration tests** - Dual-brain pipeline end-to-end
3. **Performance benchmarks** - Streaming latency/throughput
4. **Regression tests** - Ensure existing functionality works

## Expected Benefits

### Code Quality
- **65% reduction in codebase size** - Eliminate 2,400+ lines of redundant code
- **Single source of truth** - Format conversion, validation, configuration
- **Type safety** - Proper TypeScript interfaces throughout

### Performance
- **30% faster streaming** - Optimized WebSocket chunking
- **Reduced memory footprint** - Eliminate duplicate audio contexts
- **Better error handling** - Centralized error recovery

### Maintainability  
- **Clear separation of concerns** - Processors, converters, streaming
- **Dependency injection** - Testable, mockable components
- **Consistent patterns** - Unified interfaces across modules

## Risk Mitigation

### Breaking Changes
- **Phased rollout** - Incremental implementation
- **Comprehensive testing** - Automated regression testing
- **Feature flags** - Safe deployment strategy

### Dual-Brain Integration
- **Backward compatibility layer** - Existing streaming continues to work
- **Gradual migration** - System 1 → System 2 → Bridge
- **Performance monitoring** - Ensure no degradation

## Next Steps

1. **Review and approve** this architecture plan
2. **Create feature branch** for reorganization work
3. **Begin Phase 1** - Core architecture extraction
4. **Set up automated testing** - Prevent regressions during refactoring

---

**Architecture Review Date**: 2024-08-04  
**Estimated Completion**: 4 weeks  
**Impact**: High - Core streaming functionality
**Risk Level**: Medium - Extensive refactoring with backward compatibility