/**
 * Modern Video Processing Module
 * Centralizes all video-related functionality with WebGL acceleration and modern format detection
 * Uses latest APIs for better performance and maintainability
 */

import { createLogger } from '@/utils/logger';
import MediaInfo from 'mediainfo.js';

// Create dedicated logger for video modality
const logger = createLogger('VideoModality');

/**
 * Video format constants - expanded for modern format support
 */
export const VideoFormat = {
    MP4: 'mp4',
    WEBM: 'webm',
    AVI: 'avi',
    MOV: 'mov',
    MKV: 'mkv',
    FLV: 'flv',
    OGV: 'ogv',
    WEBP: 'webp', // For frame output
    UNKNOWN: 'unknown'
} as const;

export type VideoFormatType = typeof VideoFormat[keyof typeof VideoFormat];

/**
 * Video processing configuration
 */
export interface VideoConfig {
    maxFrames?: number;
    frameRate?: number;
    quality?: number;
    maxFileSize?: number;
    format?: string;
}

/**
 * Default video processing options
 */
export const DEFAULT_VIDEO_CONFIG: VideoConfig = {
    maxFrames: 30,
    frameRate: 2, // 2fps for real-time APIs as per Aliyun docs
    quality: 0.8,
    maxFileSize: 500000, // 500KB as per Aliyun docs
    format: 'jpeg'
};

/**
 * Frame extraction result
 */
export interface FrameExtractionResult {
    success: boolean;
    frames?: string[]; // Base64 encoded frames
    error?: string;
    metadata?: {
        totalFrames: number;
        extractedFrames: number;
        processingTimeMs: number;
    };
}

/**
 * Video validation result
 */
export interface VideoValidationResult {
    isValid: boolean;
    format?: VideoFormatType;
    error?: string;
    metadata?: {
        duration?: number;
        width?: number;
        height?: number;
        size?: number;
    };
}

/**
 * Modern WebGL-based Video Processor
 * Handles video processing using WebGL for better performance
 * Provides unified interface for all video processing operations
 */
export class VideoProcessor {
    private gl: WebGLRenderingContext | null = null;
    private canvas: HTMLCanvasElement | null = null;
    private logger: any;
    private mediaInfo: any = null;

    constructor(logger?: any) {
        this.logger = logger || createLogger('VideoProcessor');
        this.initializeWebGL();
        this.initializeMediaInfo();
    }

    private initializeWebGL(): void {
        try {
            if (typeof window === 'undefined') return; // Skip in Node.js environment

            this.canvas = document.createElement('canvas');
            this.gl = this.canvas.getContext('webgl') || this.canvas.getContext('experimental-webgl') as WebGLRenderingContext;

            if (!this.gl) {
                this.logger.warn('WebGL not supported, falling back to Canvas 2D');
            } else {
                this.logger.debug('WebGL initialized successfully');
            }
        } catch (error) {
            this.logger.error('Failed to initialize WebGL:', error);
        }
    }

    private async initializeMediaInfo(): Promise<void> {
        try {
            this.mediaInfo = await MediaInfo({ format: 'object' });
            this.logger.debug('MediaInfo initialized successfully');
        } catch (error) {
            this.logger.warn('MediaInfo initialization failed:', error);
        }
    }

    /**
     * Process video frame with WebGL acceleration
     */
    async processFrame(
        video: HTMLVideoElement,
        options: {
            format?: string;
            quality?: number;
            effects?: string[];
        } = {}
    ): Promise<string | null> {
        if (!this.canvas || !video) return null;

        try {
            const { format = 'jpeg', quality = 0.8, effects = [] } = options;

            // Set canvas size to match video
            this.canvas.width = video.videoWidth || video.width;
            this.canvas.height = video.videoHeight || video.height;

            if (this.gl && effects.length > 0) {
                // Use WebGL for effects processing
                return this.processWithWebGL(video, format, quality, effects);
            } else {
                // Fallback to Canvas 2D
                return this.processWithCanvas2D(video, format, quality);
            }
        } catch (error) {
            this.logger.error('Frame processing failed:', error);
            return null;
        }
    }

    private processWithWebGL(video: HTMLVideoElement, format: string, quality: number, effects: string[]): string | null {
        if (!this.gl || !this.canvas) return null;

        try {
            // Create texture from video
            const texture = this.gl.createTexture();
            this.gl.bindTexture(this.gl.TEXTURE_2D, texture);
            this.gl.texImage2D(this.gl.TEXTURE_2D, 0, this.gl.RGBA, this.gl.RGBA, this.gl.UNSIGNED_BYTE, video);

            // Set texture parameters
            this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_S, this.gl.CLAMP_TO_EDGE);
            this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_T, this.gl.CLAMP_TO_EDGE);
            this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MIN_FILTER, this.gl.LINEAR);

            // Apply effects (simplified - would need full shader implementation)
            this.applyWebGLEffects(effects);

            // Render to canvas
            this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);
            this.gl.drawArrays(this.gl.TRIANGLE_STRIP, 0, 4);

            return this.canvas.toDataURL(`image/${format}`, quality);
        } catch (error) {
            this.logger.error('WebGL processing failed:', error);
            return this.processWithCanvas2D(video, format, quality);
        }
    }

    private processWithCanvas2D(video: HTMLVideoElement, format: string, quality: number): string | null {
        if (!this.canvas) return null;

        try {
            const ctx = this.canvas.getContext('2d');
            if (!ctx) return null;

            ctx.drawImage(video, 0, 0, this.canvas.width, this.canvas.height);
            return this.canvas.toDataURL(`image/${format}`, quality);
        } catch (error) {
            this.logger.error('Canvas 2D processing failed:', error);
            return null;
        }
    }

    private applyWebGLEffects(effects: string[]): void {
        // Placeholder for WebGL effects implementation
        // In a full implementation, this would apply various shaders
        effects.forEach(effect => {
            this.logger.debug(`Applying effect: ${effect}`);
        });
    }

    /**
 * Extract comprehensive metadata using MediaInfo.js
 */
    async extractMetadata(file: File): Promise<{
        success: boolean;
        metadata?: any;
        error?: string;
    }> {
        try {
            if (!this.mediaInfo) {
                await this.initializeMediaInfo();
            }

            if (!this.mediaInfo) {
                throw new Error('MediaInfo not available');
            }

            const result = await this.mediaInfo.analyzeData(() => file.size, (chunkSize: number, offset: number) =>
                new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = (event: any) => {
                        if (event.target?.error) {
                            reject(event.target.error);
                        }
                        resolve(new Uint8Array(event.target.result));
                    };
                    reader.readAsArrayBuffer(file.slice(offset, offset + chunkSize));
                })
            );

            return {
                success: true,
                metadata: result
            };
        } catch (error) {
            this.logger.error('Metadata extraction failed:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }

    /**
     * Process multiple video frames efficiently
     */
    async processFrames(
        video: HTMLVideoElement,
        frameCount: number,
        options: {
            format?: string;
            quality?: number;
            effects?: string[];
            interval?: number;
        } = {}
    ): Promise<{
        success: boolean;
        frames?: string[];
        error?: string;
        metadata?: {
            totalFrames: number;
            processingTimeMs: number;
        };
    }> {
        const startTime = Date.now();
        const frames: string[] = [];

        try {
            const { interval = 100 } = options; // ms between frames

            for (let i = 0; i < frameCount; i++) {
                const frame = await this.processFrame(video, options);
                if (frame) {
                    frames.push(frame);
                }

                // Wait between frames if specified
                if (interval > 0 && i < frameCount - 1) {
                    await new Promise(resolve => setTimeout(resolve, interval));
                }
            }

            return {
                success: true,
                frames,
                metadata: {
                    totalFrames: frameCount,
                    processingTimeMs: Date.now() - startTime
                }
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error),
                metadata: {
                    totalFrames: frameCount,
                    processingTimeMs: Date.now() - startTime
                }
            };
        }
    }

    dispose(): void {
        if (this.canvas) {
            this.canvas = null;
        }
        this.gl = null;
    }
}

/**
 * Detect video format from file extension or MIME type with enhanced detection
 */
export function detectVideoFormat(input: string | File): VideoFormatType {
    let mimeType = '';
    let extension = '';

    if (typeof input === 'string') {
        extension = input.toLowerCase().split('.').pop() || '';
    } else if (input instanceof File) {
        mimeType = input.type.toLowerCase();
        extension = input.name.toLowerCase().split('.').pop() || '';
    }

    // Enhanced MIME type detection
    if (mimeType.includes('mp4') || mimeType.includes('mpeg4')) return VideoFormat.MP4;
    if (mimeType.includes('webm')) return VideoFormat.WEBM;
    if (mimeType.includes('avi') || mimeType.includes('msvideo')) return VideoFormat.AVI;
    if (mimeType.includes('mov') || mimeType.includes('quicktime')) return VideoFormat.MOV;
    if (mimeType.includes('mkv') || mimeType.includes('matroska')) return VideoFormat.MKV;
    if (mimeType.includes('flv') || mimeType.includes('flash')) return VideoFormat.FLV;
    if (mimeType.includes('ogg') || mimeType.includes('ogv')) return VideoFormat.OGV;

    // Enhanced extension detection
    switch (extension) {
        case 'mp4':
        case 'm4v':
            return VideoFormat.MP4;
        case 'webm':
            return VideoFormat.WEBM;
        case 'avi':
            return VideoFormat.AVI;
        case 'mov':
        case 'qt':
            return VideoFormat.MOV;
        case 'mkv':
        case 'mka':
        case 'mks':
            return VideoFormat.MKV;
        case 'flv':
        case 'f4v':
            return VideoFormat.FLV;
        case 'ogv':
        case 'ogg':
            return VideoFormat.OGV;
        case 'webp':
            return VideoFormat.WEBP;
        default:
            return VideoFormat.UNKNOWN;
    }
}

/**
 * Validate video input
 */
export function validateVideoInput(videoInput: any): VideoValidationResult {
    if (!videoInput) {
        return {
            isValid: false,
            error: 'Video input is null or undefined'
        };
    }

    if (videoInput instanceof File) {
        const format = detectVideoFormat(videoInput);
        return {
            isValid: format !== VideoFormat.UNKNOWN,
            format,
            error: format === VideoFormat.UNKNOWN ? 'Unsupported video format' : undefined,
            metadata: {
                size: videoInput.size
            }
        };
    }

    if (typeof videoInput === 'string') {
        // Assume it's a URL or base64
        return {
            isValid: videoInput.length > 0,
            format: detectVideoFormat(videoInput),
            error: videoInput.length === 0 ? 'Empty video string' : undefined
        };
    }

    return {
        isValid: false,
        error: `Unsupported video input type: ${typeof videoInput}`
    };
}

/**
 * Video Element Manager - Centralized video element and canvas management
 * Moved from MediaCaptureManager for better separation of concerns
 */
export class VideoElementManager {
    private videoElement: HTMLVideoElement | null = null;
    private canvasElement: HTMLCanvasElement | null = null;
    private canvasContext: CanvasRenderingContext2D | null = null;
    private logger: any;
    private animationFrameId: number | null = null;
    private lastCaptureTime: number = 0;
    private frameCount: number = 0;
    private onFrameCallback?: (frameInfo: FrameInfo) => void;

    constructor(logger?: any) {
        this.logger = logger || createLogger('VideoElementManager');
    }

    /**
     * Setup video element and canvas for frame extraction
     */
    async setupVideoElements(mediaStream: MediaStream): Promise<void> {
        // Create video element if it doesn't exist
        if (!this.videoElement) {
            this.videoElement = document.createElement('video');
            this.videoElement.autoplay = true;
            this.videoElement.playsInline = true;
            this.videoElement.muted = true;
            this.videoElement.style.display = 'none';
            document.body.appendChild(this.videoElement);
        }

        // Create canvas element if it doesn't exist
        if (!this.canvasElement) {
            this.canvasElement = document.createElement('canvas');
            this.canvasElement.style.display = 'none';
            document.body.appendChild(this.canvasElement);
            this.canvasContext = this.canvasElement.getContext('2d', { willReadFrequently: true });
        }

        // Connect media stream to video element
        this.videoElement.srcObject = mediaStream;

        return new Promise((resolve) => {
            this.videoElement!.onloadedmetadata = () => {
                if (this.canvasElement && this.videoElement) {
                    this.canvasElement.width = this.videoElement.videoWidth;
                    this.canvasElement.height = this.videoElement.videoHeight;
                    this.logger.debug(`Video dimensions: ${this.videoElement.videoWidth}x${this.videoElement.videoHeight}`);
                }

                if (this.videoElement) {
                    this.videoElement.play().then(() => {
                        this.logger.debug('Video playback started');
                        resolve();
                    }).catch(error => {
                        this.logger.error('Error starting video playback:', error);
                        resolve();
                    });
                } else {
                    resolve();
                }
            };

            this.videoElement!.onerror = (error) => {
                this.logger.error('Video element error:', error);
                resolve();
            };
        });
    }

    /**
     * Start continuous frame capture
     */
    startFrameCapture(config: { 
        maxFrames: number;
        captureRateMs: number;
        frameQuality: number;
        onFrame: (frameInfo: FrameInfo) => void;
    }): void {
        this.frameCount = 0;
        this.lastCaptureTime = 0;
        this.onFrameCallback = config.onFrame;

        if (this.animationFrameId !== null) {
            cancelAnimationFrame(this.animationFrameId);
        }

        this.captureFrameLoop(config);
    }

    /**
     * Frame capture loop using requestAnimationFrame
     */
    private captureFrameLoop(config: { maxFrames: number; captureRateMs: number; frameQuality: number }): void {
        this.animationFrameId = requestAnimationFrame((timestamp) => {
            if (!this.lastCaptureTime || (timestamp - this.lastCaptureTime) >= config.captureRateMs) {
                this.lastCaptureTime = timestamp;
                this.captureFrameAndNotify(config.frameQuality);

                if (this.frameCount >= config.maxFrames) {
                    this.stopFrameCapture();
                    return;
                }
            }
            this.captureFrameLoop(config);
        });
    }

    /**
     * Capture a single frame and notify callback
     */
    private async captureFrameAndNotify(frameQuality: number): Promise<void> {
        if (!this.canvasContext || !this.videoElement || !this.onFrameCallback || !this.canvasElement) {
            return;
        }

        try {
            const width = this.videoElement.videoWidth;
            const height = this.videoElement.videoHeight;

            if (this.canvasElement.width !== width || this.canvasElement.height !== height) {
                this.canvasElement.width = width;
                this.canvasElement.height = height;
            }

            this.canvasContext.drawImage(this.videoElement, 0, 0, width, height);
            const dataUrl = this.canvasElement.toDataURL('image/jpeg', frameQuality);

            this.onFrameCallback({
                dataUrl,
                timestamp: Date.now(),
                width,
                height
            });

            this.frameCount++;
        } catch (error) {
            this.logger.error('Error capturing frame:', error);
        }
    }

    /**
     * Capture a single frame manually
     */
    async captureFrame(frameQuality: number = 0.8): Promise<string | null> {
        if (!this.videoElement || !this.canvasElement || !this.canvasContext ||
            !this.videoElement.videoWidth || this.videoElement.readyState < this.videoElement.HAVE_CURRENT_DATA) {
            this.logger.warn('Video not ready for capture');
            return null;
        }

        try {
            this.canvasContext.drawImage(
                this.videoElement,
                0, 0,
                this.canvasElement.width,
                this.canvasElement.height
            );

            return this.canvasElement.toDataURL('image/jpeg', frameQuality);
        } catch (error) {
            this.logger.error('Error capturing frame:', error);
            return null;
        }
    }

    /**
     * Stop frame capture
     */
    stopFrameCapture(): void {
        if (this.animationFrameId !== null) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
    }

    /**
     * Get video element
     */
    getVideoElement(): HTMLVideoElement | null {
        return this.videoElement;
    }

    /**
     * Create a video preview element
     */
    createVideoPreview(container: HTMLElement): HTMLVideoElement | null {
        if (!this.videoElement || !this.videoElement.srcObject) {
            this.logger.error('Cannot create preview: no video element or media stream');
            return null;
        }

        const videoPreview = document.createElement('video');
        videoPreview.srcObject = this.videoElement.srcObject;
        videoPreview.autoplay = true;
        videoPreview.muted = true;
        videoPreview.style.width = '100px';
        videoPreview.style.height = '100px';
        videoPreview.style.position = 'absolute';
        videoPreview.style.bottom = '10px';
        videoPreview.style.right = '10px';
        videoPreview.style.borderRadius = '50%';
        videoPreview.style.objectFit = 'cover';
        videoPreview.style.border = '2px solid #fff';
        videoPreview.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';

        if (container) {
            container.appendChild(videoPreview);
        }

        return videoPreview;
    }

    /**
     * Dispose resources
     */
    dispose(): void {
        this.stopFrameCapture();

        if (this.videoElement) {
            try {
                this.videoElement.pause();
                this.videoElement.srcObject = null;
                if (this.videoElement.parentNode) {
                    this.videoElement.parentNode.removeChild(this.videoElement);
                }
            } catch (error) {
                this.logger.warn('Error disposing video element:', error);
            } finally {
                this.videoElement = null;
            }
        }

        if (this.canvasElement) {
            try {
                if (this.canvasElement.parentNode) {
                    this.canvasElement.parentNode.removeChild(this.canvasElement);
                }
            } catch (error) {
                this.logger.warn('Error disposing canvas element:', error);
            } finally {
                this.canvasElement = null;
                this.canvasContext = null;
            }
        }
    }
}

/**
 * Type definitions for video frame info
 */
export type FrameInfo = {
    dataUrl: string;
    timestamp: number;
    width: number;
    height: number;
};

/**
 * Extract frames from video file (browser environment)
 */
export async function extractFramesInBrowser(
    videoFile: File | string | Blob,
    config: VideoConfig = {}
): Promise<FrameExtractionResult> {
    const startTime = Date.now();
    const {
        maxFrames = DEFAULT_VIDEO_CONFIG.maxFrames!,
        frameRate = DEFAULT_VIDEO_CONFIG.frameRate!,
        quality = DEFAULT_VIDEO_CONFIG.quality!
    } = config;

    try {
        logger.debug('🎬 Starting frame extraction in browser', {
            maxFrames,
            frameRate,
            quality
        });

        // Create video element
        const video = document.createElement('video');
        video.crossOrigin = 'anonymous';
        video.muted = true;

        // Create canvas for frame capture
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
            throw new Error('Failed to get 2D canvas context');
        }

        return new Promise((resolve) => {
            const frames: string[] = [];
            let currentFrame = 0;

            video.onloadedmetadata = () => {
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;

                const duration = video.duration;
                const frameInterval = 1 / frameRate;
                const totalPossibleFrames = Math.floor(duration / frameInterval);
                const framesToExtract = Math.min(maxFrames, totalPossibleFrames);

                logger.debug('📊 Video metadata loaded', {
                    duration,
                    width: video.videoWidth,
                    height: video.videoHeight,
                    framesToExtract
                });

                extractNextFrame();
            };

            const extractNextFrame = () => {
                if (currentFrame >= maxFrames) {
                    finishExtraction();
                    return;
                }

                const timePosition = currentFrame / frameRate;
                video.currentTime = timePosition;
            };

            video.onseeked = () => {
                // Draw current frame to canvas
                ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

                // Convert to base64 JPEG
                const frameData = canvas.toDataURL('image/jpeg', quality);
                const base64Data = frameData.split(',')[1];

                frames.push(base64Data);
                currentFrame++;

                // Extract next frame
                setTimeout(extractNextFrame, 50); // Small delay to ensure seek completes
            };

            const finishExtraction = () => {
                const processingTime = Date.now() - startTime;

                logger.debug('✅ Frame extraction completed', {
                    extractedFrames: frames.length,
                    processingTimeMs: processingTime
                });

                resolve({
                    success: true,
                    frames,
                    metadata: {
                        totalFrames: maxFrames,
                        extractedFrames: frames.length,
                        processingTimeMs: processingTime
                    }
                });
            };

            video.onerror = (error) => {
                const processingTime = Date.now() - startTime;
                resolve({
                    success: false,
                    error: `Video loading failed: ${error}`,
                    metadata: {
                        totalFrames: 0,
                        extractedFrames: 0,
                        processingTimeMs: processingTime
                    }
                });
            };

            // Start loading video
            if (typeof videoFile === 'string') {
                video.src = videoFile;
            } else if (videoFile instanceof File || videoFile instanceof Blob) {
                video.src = URL.createObjectURL(videoFile);
            } else {
                throw new Error('Unsupported video file type for browser extraction');
            }
        });

    } catch (error) {
        const processingTime = Date.now() - startTime;
        logger.error('❌ Frame extraction failed', error);

        return {
            success: false,
            error: `Frame extraction failed: ${error instanceof Error ? error.message : String(error)}`,
            metadata: {
                totalFrames: 0,
                extractedFrames: 0,
                processingTimeMs: processingTime
            }
        };
    }
}

/**
 * Compress video frame for real-time transmission
 */
export async function compressVideoFrame(
    frameData: string,
    config: VideoConfig = {}
): Promise<{ success: boolean; compressedFrame?: string; error?: string }> {
    const {
        quality = DEFAULT_VIDEO_CONFIG.quality!,
        maxFileSize = DEFAULT_VIDEO_CONFIG.maxFileSize!
    } = config;

    try {
        // Create a canvas to recompress the image
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
            throw new Error('Failed to get 2D canvas context');
        }

        const img = new Image();

        return new Promise((resolve) => {
            img.onload = () => {
                canvas.width = img.width;
                canvas.height = img.height;

                // Draw and recompress
                ctx.drawImage(img, 0, 0);

                let compressedFrame = canvas.toDataURL('image/jpeg', quality).split(',')[1];

                // Check size and reduce quality if needed
                let currentQuality = quality;
                while (compressedFrame.length * 0.75 > maxFileSize && currentQuality > 0.1) {
                    currentQuality -= 0.1;
                    compressedFrame = canvas.toDataURL('image/jpeg', currentQuality).split(',')[1];
                }

                resolve({
                    success: true,
                    compressedFrame
                });
            };

            img.onerror = () => {
                resolve({
                    success: false,
                    error: 'Failed to load image for compression'
                });
            };

            img.src = `data:image/jpeg;base64,${frameData}`;
        });

    } catch (error) {
        return Promise.resolve({
            success: false,
            error: `Frame compression failed: ${error instanceof Error ? error.message : String(error)}`
        });
    }
}

/**
 * Process video for real-time streaming (extracts frames at optimal rate)
 */
export async function processVideoForRealtime(
    videoInput: File | string,
    config: VideoConfig = {}
): Promise<FrameExtractionResult> {
    const validation = validateVideoInput(videoInput);

    if (!validation.isValid) {
        return {
            success: false,
            error: validation.error,
            metadata: {
                totalFrames: 0,
                extractedFrames: 0,
                processingTimeMs: 0
            }
        };
    }

    // Use Aliyun-optimized settings for real-time
    const realtimeConfig: VideoConfig = {
        ...DEFAULT_VIDEO_CONFIG,
        ...config,
        frameRate: 2, // 2fps as recommended by Aliyun
        maxFileSize: 500000 // 500KB limit
    };

    return extractFramesInBrowser(videoInput as File | string, realtimeConfig);
}

/**
 * Create video thumbnail
 */
export async function createVideoThumbnail(
    videoInput: File | string,
    timePosition: number = 1.0
): Promise<{ success: boolean; thumbnail?: string; error?: string }> {
    try {
        const video = document.createElement('video');
        video.crossOrigin = 'anonymous';
        video.muted = true;

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
            throw new Error('Failed to get 2D canvas context');
        }

        return new Promise((resolve) => {
            video.onloadedmetadata = () => {
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                video.currentTime = Math.min(timePosition, video.duration);
            };

            video.onseeked = () => {
                ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
                const thumbnail = canvas.toDataURL('image/jpeg', 0.8).split(',')[1];

                resolve({
                    success: true,
                    thumbnail
                });
            };

            video.onerror = (error) => {
                resolve({
                    success: false,
                    error: `Video loading failed: ${error}`
                });
            };

            // Start loading
            if (typeof videoInput === 'string') {
                video.src = videoInput;
            } else {
                video.src = URL.createObjectURL(videoInput);
            }
        });

    } catch (error) {
        return {
            success: false,
            error: `Thumbnail creation failed: ${error instanceof Error ? error.message : String(error)}`
        };
    }
}

/**
 * WebCodecs-based Video Processor for modern browsers
 * Uses WebCodecs API for better performance when available
 */
export class WebCodecsVideoProcessor {
    private logger: any;
    private decoder: VideoDecoder | null = null;
    private encoder: VideoEncoder | null = null;
    private isSupported: boolean;

    constructor(logger?: any) {
        this.logger = logger || createLogger('WebCodecsVideoProcessor');
        this.isSupported = typeof window !== 'undefined' && 'VideoDecoder' in window && 'VideoEncoder' in window;
        
        if (this.isSupported) {
            this.logger.debug('WebCodecs API is supported');
        } else {
            this.logger.warn('WebCodecs API not supported, falling back to traditional methods');
        }
    }

    /**
     * Check if WebCodecs is supported
     */
    isWebCodecsSupported(): boolean {
        return this.isSupported;
    }

    /**
     * Process video using WebCodecs for better performance
     */
    async processVideoWithWebCodecs(
        videoData: ArrayBuffer,
        config: VideoConfig = {}
    ): Promise<FrameExtractionResult> {
        if (!this.isSupported) {
            throw new Error('WebCodecs not supported in this environment');
        }

        const startTime = Date.now();
        const frames: string[] = [];
        const maxFrames = config.maxFrames || DEFAULT_VIDEO_CONFIG.maxFrames!;
        const frameRate = config.frameRate || DEFAULT_VIDEO_CONFIG.frameRate!;

        try {
            // Initialize VideoDecoder
            this.decoder = new VideoDecoder({
                output: (frame: VideoFrame) => {
                    try {
                        // Convert VideoFrame to canvas and then to base64
                        const canvas = new OffscreenCanvas(frame.displayWidth, frame.displayHeight);
                        const ctx = canvas.getContext('2d');
                        if (ctx) {
                            ctx.drawImage(frame, 0, 0);
                            canvas.convertToBlob({ type: 'image/jpeg', quality: config.quality || 0.8 })
                                .then(blob => blob.arrayBuffer())
                                .then(buffer => {
                                    const base64 = btoa(String.fromCharCode(...new Uint8Array(buffer)));
                                    frames.push(base64);
                                });
                        }
                        frame.close();
                    } catch (error) {
                        this.logger.error('Error processing VideoFrame:', error);
                    }
                },
                error: (error: Error) => {
                    this.logger.error('VideoDecoder error:', error);
                }
            });

            // Configure decoder (would need proper codec detection in real implementation)
            this.decoder.configure({
                codec: 'mp4v.20.0',  // This would need to be determined from the video data
                codedWidth: 1920,     // These would need to be extracted from video metadata
                codedHeight: 1080
            });

            // In a real implementation, you'd need to:
            // 1. Parse the video container format (MP4, WebM, etc.)
            // 2. Extract codec information
            // 3. Split into encoded chunks
            // 4. Feed chunks to decoder
            
            this.logger.warn('WebCodecs processing is a placeholder - full implementation requires video container parsing');

            return {
                success: true,
                frames,
                metadata: {
                    totalFrames: maxFrames,
                    extractedFrames: frames.length,
                    processingTimeMs: Date.now() - startTime
                }
            };

        } catch (error) {
            return {
                success: false,
                error: `WebCodecs processing failed: ${error instanceof Error ? error.message : String(error)}`,
                metadata: {
                    totalFrames: 0,
                    extractedFrames: 0,
                    processingTimeMs: Date.now() - startTime
                }
            };
        } finally {
            if (this.decoder) {
                this.decoder.close();
                this.decoder = null;
            }
        }
    }

    /**
     * Dispose resources
     */
    dispose(): void {
        if (this.decoder) {
            this.decoder.close();
            this.decoder = null;
        }
        if (this.encoder) {
            this.encoder.close();
            this.encoder = null;
        }
    }
}

/**
 * Extract frames from a video in a Node.js environment
 * @param videoData Video data as Buffer
 * @param fps Frames per second to extract
 * @returns Array of base64-encoded image strings
 */
export async function extractFramesInNode(videoData: Buffer, fps: number = 2): Promise<string[]> {
    try {
        // Dynamic import Node.js modules to prevent bundling in browser
        const [os, path, fs, { exec }, { promisify }] = await Promise.all([
            import('os'),
            import('path'),
            import('fs'),
            import('child_process'),
            import('util')
        ]);

        const execAsync = promisify(exec);

        // Create temporary directory for frames
        const tempDir = path.join(os.tmpdir(), `video-frames-${Date.now()}`);
        fs.mkdirSync(tempDir, { recursive: true });

        // Create temporary video file
        const videoPath = path.join(tempDir, 'input.mp4');
        fs.writeFileSync(videoPath, videoData);

        // Extract frames using ffmpeg
        const outputPattern = path.join(tempDir, 'frame-%04d.jpg');
        await execAsync(`ffmpeg -i ${videoPath} -vf "fps=${fps}" -q:v 1 ${outputPattern}`);

        // Read the extracted frames
        const frameFiles = fs.readdirSync(tempDir)
            .filter((file: string) => file.startsWith('frame-') && file.endsWith('.jpg'))
            .sort(); // Ensure frames are in order

        // Convert frames to base64
        const frames: string[] = [];
        for (const file of frameFiles) {
            const framePath = path.join(tempDir, file);
            const frameData = fs.readFileSync(framePath);
            const base64Frame = `data:image;base64,${frameData.toString('base64')}`;
            frames.push(base64Frame);
        }

        // Clean up temporary files
        fs.rmSync(tempDir, { recursive: true, force: true });

        return frames;
    } catch (error) {
        logger.error('Error extracting frames in Node.js:', error);
        throw error;
    }
}

/**
 * Extract frames from a video (universal function)
 * Automatically detects environment and uses the appropriate implementation
 * @param videoData Video data as Buffer, Blob, File, or string URL
 * @param fps Frames per second to extract
 * @returns Array of base64-encoded image strings
 */
export async function extractFrames(videoData: Buffer | Blob | File | string, fps: number = 2): Promise<string[]> {
    try {
        // Check if we're in a browser environment
        if (typeof window !== 'undefined') {
            // Browser implementation - handle different input types
            if (typeof videoData === 'string') {
                // URL string - convert to blob first
                const response = await fetch(videoData);
                const blob = await response.blob();
                const result = await extractFramesInBrowser(blob, { frameRate: fps });
                return result.success ? result.frames || [] : [];
            } else if (videoData instanceof File || videoData instanceof Blob) {
                const result = await extractFramesInBrowser(videoData, { frameRate: fps });
                return result.success ? result.frames || [] : [];
            } else {
                throw new Error('Unsupported video data type for browser environment');
            }
        } else {
            // Node.js implementation
            if (Buffer.isBuffer(videoData)) {
                return await extractFramesInNode(videoData, fps);
            } else {
                throw new Error('Node.js environment requires Buffer input');
            }
        }
    } catch (error) {
        logger.error('Error extracting frames:', error);
        throw error;
    }
}