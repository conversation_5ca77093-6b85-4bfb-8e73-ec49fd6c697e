/**
 * Media Modality System - Main Exports
 * 
 * Centralized exports for the media modality system with clean organization:
 * - Input coordination and event management
 * - Context analysis utilities (single source of truth) 
 * - Audio and video processing utilities
 * - Format conversion and validation
 */

// === MAIN COORDINATION ===
export {
  InputCoordination,
  InputEventType,
  type InputEvent,
  type AudioInputEvent,
  type VideoInputEvent,
  type TextInputEvent,
  type MultimodalInputEvent,
  type InputEventListener,
  type InputCoordinationOptions
} from './inputCoordination.js';

// === CONTEXT ANALYSIS ===
// Note: ContextAnalysisUtils has been consolidated into ContextualService
// Import from: @/agent/services/conversation/ContextualService.js

// === AUDIO PROCESSING ===
export {
  calculateAudioVolume,
  estimateAudioQuality,
  convertFloat32ToWav,
  validateAudioData,
  processRealtimeAudio,
  createRealtimeAudioProcessor,
  extractAudioAnalysis,
  handleAudioData,
  createAudioProcessingConfig,
  type AudioAnalysisResult,
  type AudioProcessingOptions
} from './audio.js';

// === VIDEO PROCESSING ===
export {
  detectVideoFormat,
  validateVideoInput,
  extractFramesInBrowser,
  compressVideoFrame,
  processVideoForRealtime
} from './video.js';

// === AVATAR SERVICES ===
// Note: Avatar-specific services have been moved to app/viewer/services/
// - AvatarMediaCoordinator → app/viewer/services/MediaCoordinator
// - AvatarAudioProcessor → app/viewer/services/AudioProcessor

// === NORMALIZATION UTILITIES ===
export function normalizeInput(input: string | Float32Array | any, options: {
  currentVideoFrames?: any[] | null;
  enableVideoInput?: boolean;
  isVideoStreaming?: boolean;
} = {}): {
  text: string | null;
  audio: Float32Array | null;
  video: string[] | null;
  metadata: {
    timestamp: number;
    inputType: string;
    isMultimodal: boolean;
  };
} {
  const metadata = {
    timestamp: Date.now(),
    inputType: 'unknown',
    isMultimodal: false
  };

  let text: string | null = null;
  let audio: Float32Array | null = null;
  let video: string[] | null = null;

  // Handle text input
  if (typeof input === 'string') {
    text = input;
    metadata.inputType = 'text';
  }
  // Handle audio input
  else if (input instanceof Float32Array) {
    audio = input;
    metadata.inputType = 'audio';
  }
  // Handle multimodal object
  else if (input && typeof input === 'object') {
    if (input.text) text = input.text;
    if (input.audio instanceof Float32Array) audio = input.audio;
    if (input.video || input.videoFrames) {
      video = input.video || input.videoFrames || [];
    }

    // Check if we have multiple modalities
    const modalityCount = [text, audio, video].filter(Boolean).length;
    metadata.isMultimodal = modalityCount > 1;
    metadata.inputType = metadata.isMultimodal ? 'multimodal' :
      text ? 'text' :
        audio ? 'audio' :
          video ? 'video' : 'unknown';
  }

  // Include current video frames if available and enabled
  if (options.enableVideoInput && options.currentVideoFrames && options.currentVideoFrames.length > 0) {
    video = video || [];
    video.push(...options.currentVideoFrames);

    if (!metadata.isMultimodal && video.length > 0) {
      metadata.isMultimodal = [text, audio].filter(Boolean).length > 0;
      metadata.inputType = metadata.isMultimodal ? 'multimodal' : 'video';
    }
  }

  return { text, audio, video, metadata };
}

// === VALIDATION UTILITIES ===
export function validateMultimodalInput(normalizedInput: {
  text: string | null;
  audio: Float32Array | null;
  video: string[] | null;
}): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check if we have at least one input
  const hasAnyInput = !!(normalizedInput.text || normalizedInput.audio || normalizedInput.video);
  if (!hasAnyInput) {
    errors.push('No valid input provided (text, audio, or video)');
  }

  // Validate audio
  if (normalizedInput.audio) {
    if (!(normalizedInput.audio instanceof Float32Array)) {
      errors.push('Audio data must be Float32Array');
    } else if (normalizedInput.audio.length === 0) {
      warnings.push('Audio data is empty');
    }
  }

  // Validate video
  if (normalizedInput.video) {
    if (!Array.isArray(normalizedInput.video)) {
      errors.push('Video data must be an array of base64 strings');
    } else if (normalizedInput.video.length === 0) {
      warnings.push('Video frames array is empty');
    }
  }

  // Validate text
  if (normalizedInput.text) {
    if (typeof normalizedInput.text !== 'string') {
      errors.push('Text input must be a string');
    } else if (normalizedInput.text.trim().length === 0) {
      warnings.push('Text input is empty or whitespace only');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}