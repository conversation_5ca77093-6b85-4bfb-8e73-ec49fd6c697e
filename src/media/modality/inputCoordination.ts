/**
 * Input Coordination System
 * 
 * Unified input coordination combining orchestration and event-driven processing.
 * This consolidates UnifiedInputManager + InputCoordinationManager functionality
 * for better maintainability and cleaner separation of concerns.
 * 
 * ARCHITECTURE:
 * - MediaCaptureManager: Hardware interface (microphone, camera)  
 * - InputCoordination: Unified coordination and LangGraph integration
 * - Clean separation: Hardware capture → Event coordination → App interface
 */

import { createLogger } from '@/utils/logger';
import { MediaCaptureManager } from '../capture/MediaCaptureManager';
import { HumanMessage } from '@langchain/core/messages';

const logger = createLogger('InputCoordination');

// === EVENT TYPES ===–
export enum InputEventType {
  AUDIO_DATA = 'audio_data',
  VIDEO_FRAME = 'video_frame',
  TEXT_INPUT = 'text_input',
  MULTIMODAL_INPUT = 'multimodal_input',
  INPUT_STARTED = 'input_started',
  INPUT_STOPPED = 'input_stopped',
  INPUT_ERROR = 'input_error',
  QUALITY_CHANGED = 'quality_changed'
}

// === INTERFACES ===
export interface InputEvent {
  type: InputEventType;
  timestamp: number;
  metadata?: Record<string, any>;
}

export interface AudioInputEvent extends InputEvent {
  type: InputEventType.AUDIO_DATA;
  data: Float32Array;
  format: {
    sampleRate: number;
    channels: number;
    bitDepth: number;
  };
}

export interface VideoInputEvent extends InputEvent {
  type: InputEventType.VIDEO_FRAME;
  data: string; // Base64 encoded frame
  format: {
    width: number;
    height: number;
    format: string;
    quality: number;
  };
}

export interface TextInputEvent extends InputEvent {
  type: InputEventType.TEXT_INPUT;
  text: string;
  language?: string;
}

export interface MultimodalInputEvent extends InputEvent {
  type: InputEventType.MULTIMODAL_INPUT;
  audio?: Float32Array;
  video?: string[];
  text?: string;
  combinedContext?: any;
}

export type InputEventListener = (event: InputEvent) => void | Promise<void>;

export type InputCoordinationOptions = {
  enableAudio?: boolean;
  enableVideo?: boolean;
  enableText?: boolean;
  bufferSize?: number;
  qualitySettings?: Record<string, any>;
  maxAudioChunkMs?: number;
  audioConfig?: any;
  videoConfig?: any;
  captureConfig?: any;
  autoStartCapture?: boolean; // Whether to automatically start media capture on initialization
  requireTalkingHeadMode?: boolean; // Only start capture when in TalkingHead mode
};

// === MAIN COORDINATION CLASS ===
/**
 * Unified Input Coordination
 * 
 * Combines high-level orchestration with event-driven coordination
 * for a single, maintainable input management system.
 */
export class InputCoordination {
  // Event coordination properties
  private listeners: Map<InputEventType, Set<InputEventListener>> = new Map();
  private inputBuffer: Map<InputEventType, any[]> = new Map();
  private mediaBuffer: Map<string, { audio: any[], video: any[] }> = new Map();
  private processingTimeouts: Map<string, NodeJS.Timeout> = new Map();

  // Orchestration properties
  private mediaCapture: MediaCaptureManager;
  private isActive: boolean = false;
  private options: InputCoordinationOptions;

  constructor(options: InputCoordinationOptions = {}) {
    this.options = {
      enableAudio: true,
      enableVideo: true,
      enableText: true,
      bufferSize: 10,
      maxAudioChunkMs: 5000,
      autoStartCapture: false, // Don't auto-start by default
      requireTalkingHeadMode: true, // Require TalkingHead mode by default
      ...options
    };

    // Initialize capture layer (hardware interface)
    this.mediaCapture = new MediaCaptureManager({
      ...options.captureConfig,
      onAudioData: this.handleAudioCapture.bind(this),
      onFrame: this.handleVideoCapture.bind(this)
    });

    // Initialize event type buffers
    Object.values(InputEventType).forEach(eventType => {
      this.inputBuffer.set(eventType, []);
    });

    logger.info('Unified Input Coordination initialized', {
      enableAudio: this.options.enableAudio,
      enableVideo: this.options.enableVideo,
      enableText: this.options.enableText,
      bufferSize: this.options.bufferSize
    });
  }

  // === LIFECYCLE MANAGEMENT ===

  /**
   * Start unified input processing
   */
  async start(): Promise<boolean> {
    if (this.isActive) {
      logger.warn('Input coordination already active');
      return true;
    }

    try {
      // Start event coordination
      this.isActive = true;
      this.emitEvent({
        type: InputEventType.INPUT_STARTED,
        timestamp: Date.now()
      });

      // Only start capture if autoStartCapture is enabled
      if (this.options.autoStartCapture) {
        // Start capture layer based on enabled modalities
        let mediaType: 'audio' | 'video' | 'audio-video' = 'audio';
        if (this.options.enableAudio && this.options.enableVideo) {
          mediaType = 'audio-video';
        } else if (this.options.enableVideo) {
          mediaType = 'video';
        }

        const captureSuccess = await this.mediaCapture.startCapture(mediaType);
        if (!captureSuccess) {
          logger.warn('⚠️ Failed to start media capture - continuing without active capture');
        } else {
          logger.info('📹 Media capture started successfully');
        }
      } else {
        logger.info('📹 Media capture initialization delayed - waiting for explicit start or TalkingHead mode');
      }

      logger.info('✅ Unified input coordination started');
      return true;

    } catch (error) {
      logger.error('❌ Failed to start input coordination:', error);
      this.isActive = false;
      return false;
    }
  }

  /**
   * Stop unified input processing
   */
  stop(): void {
    if (!this.isActive) {
      logger.warn('Input coordination not active');
      return;
    }

    // Stop capture layer
    this.mediaCapture.stopCapture();

    // Stop event coordination
    this.isActive = false;
    this.flushAllBuffers();

    this.emitEvent({
      type: InputEventType.INPUT_STOPPED,
      timestamp: Date.now()
    });

    logger.info('✅ Unified input coordination stopped');
  }

  /**
   * Start media capture when TalkingHead mode is activated
   */
  async startMediaCaptureForTalkingHead(): Promise<boolean> {
    try {
      // Check if media capture is already active by checking if it has an active stream
      if (this.mediaCapture.getMediaStream()?.active) {
        logger.debug('📹 Media capture already active');
        return true;
      }

      logger.info('🎭 Starting media capture for TalkingHead mode...');

      // Start capture layer based on enabled modalities
      let mediaType: 'audio' | 'video' | 'audio-video' = 'audio';
      if (this.options.enableAudio && this.options.enableVideo) {
        mediaType = 'audio-video';
      } else if (this.options.enableVideo) {
        mediaType = 'video';
      }

      const captureSuccess = await this.mediaCapture.startCapture(mediaType);
      if (!captureSuccess) {
        logger.error('❌ Failed to start media capture for TalkingHead mode');
        return false;
      }

      logger.info('✅ Media capture started for TalkingHead mode');
      return true;

    } catch (error) {
      logger.error('❌ Error starting media capture for TalkingHead mode:', error);
      return false;
    }
  }

  // === EVENT MANAGEMENT ===

  /**
   * Add event listener for specific input type
   */
  addEventListener(eventType: InputEventType, listener: InputEventListener): void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    this.listeners.get(eventType)!.add(listener);
    logger.debug(`Added listener for ${eventType}`);
  }

  /**
   * Remove event listener
   */
  removeEventListener(eventType: InputEventType, listener: InputEventListener): void {
    const listeners = this.listeners.get(eventType);
    if (listeners) {
      listeners.delete(listener);
      if (listeners.size === 0) {
        this.listeners.delete(eventType);
      }
    }
    logger.debug(`Removed listener for ${eventType}`);
  }

  /**
   * Emit event to all registered listeners
   */
  private async emitEvent(event: InputEvent): Promise<void> {
    const listeners = this.listeners.get(event.type);
    if (!listeners || listeners.size === 0) return;

    const promises = Array.from(listeners).map(async (listener) => {
      try {
        await listener(event);
      } catch (error) {
        logger.error(`Error in event listener for ${event.type}:`, error);
      }
    });

    await Promise.all(promises);
  }

  // === INPUT PROCESSING ===

  /**
   * Process audio input
   */
  async processAudioInput(audioData: Float32Array, format: {
    sampleRate: number;
    channels: number;
    bitDepth: number;
  }): Promise<void> {
    if (!this.isActive || !this.options.enableAudio) return;

    const event: AudioInputEvent = {
      type: InputEventType.AUDIO_DATA,
      timestamp: Date.now(),
      data: audioData,
      format,
      metadata: {
        duration: audioData.length / format.sampleRate,
        samples: audioData.length
      }
    };

    await this.bufferAndEmitEvent(event);
  }

  /**
   * Process video input
   */
  async processVideoInput(frameData: string, format: {
    width: number;
    height: number;
    format: string;
    quality: number;
  }): Promise<void> {
    if (!this.isActive || !this.options.enableVideo) return;

    const event: VideoInputEvent = {
      type: InputEventType.VIDEO_FRAME,
      timestamp: Date.now(),
      data: frameData,
      format,
      metadata: {
        size: frameData.length,
        estimatedKB: (frameData.length * 0.75) / 1024
      }
    };

    await this.bufferAndEmitEvent(event);
  }

  /**
   * Process text input
   */
  async processTextInput(text: string, language?: string): Promise<void> {
    if (!this.isActive || !this.options.enableText) return;

    const event: TextInputEvent = {
      type: InputEventType.TEXT_INPUT,
      timestamp: Date.now(),
      text,
      language,
      metadata: {
        length: text.length,
        wordCount: text.split(/\s+/).length
      }
    };

    await this.bufferAndEmitEvent(event);
  }

  // === CAPTURE INTEGRATION ===

  /**
   * Handle audio capture from MediaCaptureManager
   */
  private async handleAudioCapture(audioData: any, format: any): Promise<void> {
    if (!this.options.enableAudio) return;

    // Convert to coordination layer format
    if (audioData instanceof Float32Array) {
      await this.processAudioInput(audioData, {
        sampleRate: format.sampleRate || 16000,
        channels: format.channels || 1,
        bitDepth: format.bitDepth || 16
      });
    }
  }

  /**
   * Handle video capture from MediaCaptureManager
   */
  private async handleVideoCapture(frameInfo: any): Promise<void> {
    if (!this.options.enableVideo) return;

    // Convert to coordination layer format
    await this.processVideoInput(frameInfo.dataUrl, {
      width: frameInfo.width,
      height: frameInfo.height,
      format: 'jpeg',
      quality: 0.8
    });
  }

  // === MULTIMODAL PROCESSING ===

  /**
   * Create combined multimodal input from buffered data
   */
  async createMultimodalInput(options: {
    includeAudio?: boolean;
    includeVideo?: boolean;
    includeText?: boolean;
    timeWindow?: number; // milliseconds
  } = {}): Promise<MultimodalInputEvent | null> {
    const {
      includeAudio = true,
      includeVideo = true,
      includeText = true,
      timeWindow = 1000
    } = options;

    const now = Date.now();
    const cutoffTime = now - timeWindow;

    let audio: Float32Array | undefined;
    let video: string[] | undefined;
    let text: string | undefined;

    // Collect recent audio data
    if (includeAudio) {
      const audioBuffer = this.inputBuffer.get(InputEventType.AUDIO_DATA) || [];
      const recentAudio = audioBuffer.filter((event: AudioInputEvent) =>
        event.timestamp >= cutoffTime
      );

      if (recentAudio.length > 0) {
        audio = this.combineAudioChunks(recentAudio.map(e => e.data));
      }
    }

    // Collect recent video frames
    if (includeVideo) {
      const videoBuffer = this.inputBuffer.get(InputEventType.VIDEO_FRAME) || [];
      const recentVideo = videoBuffer.filter((event: VideoInputEvent) =>
        event.timestamp >= cutoffTime
      );

      if (recentVideo.length > 0) {
        video = recentVideo.map(e => e.data);
      }
    }

    // Collect recent text
    if (includeText) {
      const textBuffer = this.inputBuffer.get(InputEventType.TEXT_INPUT) || [];
      const recentText = textBuffer.filter((event: TextInputEvent) =>
        event.timestamp >= cutoffTime
      );

      if (recentText.length > 0) {
        text = recentText.map(e => e.text).join(' ');
      }
    }

    // Only create event if we have some data
    if (!audio && !video && !text) {
      return null;
    }

    const multimodalEvent: MultimodalInputEvent = {
      type: InputEventType.MULTIMODAL_INPUT,
      timestamp: now,
      audio,
      video,
      text,
      metadata: {
        timeWindow,
        hasAudio: !!audio,
        hasVideo: !!video,
        hasText: !!text,
        videoFrameCount: video?.length || 0,
        textLength: text?.length || 0
      }
    };

    await this.emitEvent(multimodalEvent);
    return multimodalEvent;
  }

  // === LANGRAPH INTEGRATION ===

  /**
   * Handle media input with LangGraph integration
   */
  async handleMediaInputForLangGraph(mediaEvent: any, agentService: any, sessionId: string = 'default'): Promise<any> {
    try {
      logger.debug('Processing media input for LangGraph:', {
        type: mediaEvent.type,
        sessionId,
        hasAudio: !!mediaEvent.audio,
        hasVideo: !!mediaEvent.video,
        hasText: !!mediaEvent.text
      });

      // Convert media to LangGraph-compatible format
      const langGraphMessage = await this.convertToLangGraphMessage(mediaEvent);

      if (!langGraphMessage) {
        logger.warn('No valid content to send to LangGraph');
        return null;
      }

      // Send as regular input to agent.stream()
      const streamOptions = {
        sessionId,
        enableMultimodal: true,
        modes: ['messages'],
        onMessage: this.handleStreamResponse.bind(this, sessionId),
        onComplete: this.handleStreamComplete.bind(this, sessionId),
        onError: this.handleStreamError.bind(this)
      };

      // Use streaming manager for processing
      const response = await agentService.streamingManager.startNativeStream(
        agentService.agent,
        { messages: [langGraphMessage] },
        streamOptions
      );

      return response;

    } catch (error) {
      logger.error('Error handling media input for LangGraph:', error);
      throw error;
    }
  }

  /**
   * Convert media event to LangGraph HumanMessage
   */
  async convertToLangGraphMessage(mediaEvent: any): Promise<HumanMessage | null> {
    const content: any[] = [];

    // Add text content
    if (mediaEvent.text) {
      content.push({
        type: 'text',
        text: mediaEvent.text
      });
    }

    // Add audio content (convert to base64)
    if (mediaEvent.audio) {
      const audioBase64 = await this.convertAudioToBase64(mediaEvent.audio);
      content.push({
        type: 'audio',
        audio: audioBase64,
        format: mediaEvent.audioFormat || 'wav'
      });
    }

    // Add video content (convert to base64)
    if (mediaEvent.video) {
      const videoBase64 = await this.convertVideoToBase64(mediaEvent.video);
      content.push({
        type: 'image',
        image_url: {
          url: `data:image/jpeg;base64,${videoBase64}`
        }
      });
    }

    if (content.length === 0) {
      return null;
    }

    // Return proper LangGraph HumanMessage
    return new HumanMessage({
      content: content.length === 1 && content[0].type === 'text'
        ? content[0].text
        : content,
      additional_kwargs: {
        sessionId: mediaEvent.sessionId,
        timestamp: mediaEvent.timestamp,
        multimodal: true
      }
    });
  }

  // === UTILITY METHODS ===

  /**
   * Buffer event and emit if needed
   */
  private async bufferAndEmitEvent(event: InputEvent): Promise<void> {
    // Add to buffer
    const buffer = this.inputBuffer.get(event.type);
    if (buffer) {
      buffer.push(event);

      // Maintain buffer size
      while (buffer.length > (this.options.bufferSize || 10)) {
        buffer.shift();
      }
    }

    // Emit event immediately
    await this.emitEvent(event);
  }

  /**
   * Combine multiple audio chunks into one
   */
  private combineAudioChunks(chunks: Float32Array[]): Float32Array {
    if (chunks.length === 0) return new Float32Array(0);
    if (chunks.length === 1) return chunks[0];

    const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
    const combined = new Float32Array(totalLength);

    let offset = 0;
    for (const chunk of chunks) {
      combined.set(chunk, offset);
      offset += chunk.length;
    }

    return combined;
  }

  /**
   * Flush all input buffers
   */
  private flushAllBuffers(): void {
    this.inputBuffer.forEach((buffer, eventType) => {
      buffer.length = 0;
    });
    logger.debug('All input buffers flushed');
  }

  // === FORMAT CONVERSION ===

  /**
   * Convert Float32Array audio to base64
   */
  private async convertAudioToBase64(audioData: any): Promise<string> {
    if (audioData instanceof Float32Array) {
      const wavBuffer = this.float32ToWav(audioData);
      return this.arrayBufferToBase64(wavBuffer);
    }

    if (audioData instanceof ArrayBuffer) {
      return this.arrayBufferToBase64(audioData);
    }

    if (typeof audioData === 'string') {
      return audioData; // Assume already base64
    }

    throw new Error('Unsupported audio data format');
  }

  /**
   * Convert video frame to base64
   */
  private async convertVideoToBase64(videoData: any): Promise<string> {
    if (typeof videoData === 'string') {
      return videoData; // Assume already base64
    }

    if (videoData instanceof ArrayBuffer) {
      return this.arrayBufferToBase64(videoData);
    }

    throw new Error('Unsupported video data format');
  }

  /**
   * Convert Float32Array to WAV format
   */
  private float32ToWav(float32Array: Float32Array, sampleRate: number = 16000): ArrayBuffer {
    const length = float32Array.length;
    const arrayBuffer = new ArrayBuffer(44 + length * 2);
    const view = new DataView(arrayBuffer);

    // WAV header
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, 1, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * 2, true);
    view.setUint16(32, 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * 2, true);

    // Convert float32 to int16
    let offset = 44;
    for (let i = 0; i < length; i++) {
      const sample = Math.max(-1, Math.min(1, float32Array[i]));
      view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
      offset += 2;
    }

    return arrayBuffer;
  }

  /**
   * Convert ArrayBuffer to base64
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  // === STREAM HANDLERS ===

  private handleStreamResponse(sessionId: string, data: any): void {
    logger.debug('Multimodal stream response:', {
      sessionId,
      token: data.token,
      hasFullMessage: !!data.fullMessage
    });

    this.emitEvent({
      type: InputEventType.MULTIMODAL_INPUT,
      timestamp: Date.now(),
      metadata: {
        sessionId,
        responseData: data,
        isStreamResponse: true
      }
    });
  }

  private handleStreamComplete(sessionId: string, data: any): void {
    logger.info('Multimodal stream completed:', {
      sessionId,
      fullMessage: data.fullMessage,
      duration: data.duration
    });

    this.emitEvent({
      type: InputEventType.INPUT_STOPPED,
      timestamp: Date.now(),
      metadata: {
        sessionId,
        completionData: data
      }
    });
  }

  private handleStreamError(error: Error): void {
    logger.error('Multimodal stream error:', error);

    this.emitEvent({
      type: InputEventType.INPUT_ERROR,
      timestamp: Date.now(),
      metadata: {
        error: error.message
      }
    });
  }

  // === STATUS & CLEANUP ===

  /**
   * Get current buffer status
   */
  getBufferStatus(): Record<string, number> {
    const status: Record<string, number> = {};
    this.inputBuffer.forEach((buffer, eventType) => {
      status[eventType] = buffer.length;
    });
    return status;
  }

  /**
   * Update quality settings
   */
  updateQualitySettings(settings: Record<string, any>): void {
    this.options.qualitySettings = { ...this.options.qualitySettings, ...settings };

    this.emitEvent({
      type: InputEventType.QUALITY_CHANGED,
      timestamp: Date.now(),
      metadata: { qualitySettings: this.options.qualitySettings }
    });

    logger.info('Quality settings updated', this.options.qualitySettings);
  }

  /**
   * Get current status
   */
  getStatus() {
    return {
      isActive: this.isActive,
      mediaCapture: {
        // Note: will fix isCapturing access in MediaCaptureManager
        isAudioStreaming: this.mediaCapture.isAudioStreamingActive()
      },
      bufferStatus: this.getBufferStatus(),
      options: this.options
    };
  }

  /**
   * Dispose resources
   */
  dispose(): void {
    this.stop();

    // Clear all timeouts
    for (const timeout of Array.from(this.processingTimeouts.values())) {
      clearTimeout(timeout);
    }

    // Clear all buffers and state
    this.listeners.clear();
    this.flushAllBuffers();
    this.processingTimeouts.clear();
    this.mediaBuffer.clear();

    // Dispose media capture
    this.mediaCapture.dispose();

    logger.info('Unified Input Coordination disposed');
  }
}

export default InputCoordination;