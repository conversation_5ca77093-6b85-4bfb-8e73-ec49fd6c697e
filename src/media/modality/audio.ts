/**
 * Modern Audio Processing Module
 * Centralizes all audio-related functionality with universal format conversion
 * Uses modern libraries for better maintainability and performance
 */

import { createLogger, LogLevel, setModuleLogLevel } from '@/utils/logger';
import * as wavefile from 'wavefile';
const { WaveFile } = wavefile;

// Set logging level to WARN to minimize audio processing spam while showing errors
setModuleLogLevel('StreamingAudioProcessor', LogLevel.WARN);
setModuleLogLevel('AudioModality', LogLevel.WARN);

// Create dedicated logger for audio modality
const logger = createLogger('AudioModality');

// Audio format constants - expanded for better format support
export const AudioFormat = {
    WAV: 'wav',
    MP3: 'mp3',
    PCM: 'pcm',
    OGG: 'ogg',
    WEBM: 'webm',
    FLAC: 'flac',
    AAC: 'aac',
    M4A: 'm4a',
    // Bit depth formats
    '8': '8',
    '16': '16',
    '24': '24',
    '32': '32',
    '32f': '32f',
    '64': '64',
    // Compression formats
    '8a': '8a',  // A-Law
    '8m': '8m',  // μ-Law
    '4': '4'     // ADPCM
} as const;

export type AudioFormatType = typeof AudioFormat[keyof typeof AudioFormat];

/**
 * Universal Audio Converter using modern libraries
 * Replaces separate format conversion functions with a unified approach
 * Handles all audio format conversions through a single, consistent interface
 */
export class UniversalAudioConverter {
    private logger: any;
    private waverileCache: Map<string, any> = new Map();

    constructor(logger?: any) {
        this.logger = logger || createLogger('UniversalAudioConverter');
    }

    /**
     * Universal audio format conversion method
     * Handles all supported format conversions through a single interface
     * @param audioData - Input audio data (ArrayBuffer, Float32Array, Int16Array, Uint8Array, or base64 string)
     * @param fromFormat - Source format
     * @param toFormat - Target format
     * @param config - Audio configuration
     * @returns Converted audio data with comprehensive metadata
     */
    async convert(
        audioData: ArrayBuffer | Float32Array | Int16Array | Uint8Array | string,
        fromFormat: AudioFormatType,
        toFormat: AudioFormatType,
        config: Partial<AudioConfig> = {}
    ): Promise<{
        success: boolean;
        data?: ArrayBuffer | string | Float32Array | Int16Array;
        error?: string;
        metadata?: {
            originalSize: number;
            convertedSize: number;
            processingTimeMs: number;
            originalFormat: AudioFormatType;
            targetFormat: AudioFormatType;
            sampleRate?: number;
            channels?: number;
            bitDepth?: number;
        };
    }> {
        const startTime = Date.now();

        try {
            this.logger.debug('Converting audio format', {
                fromFormat,
                toFormat,
                inputType: typeof audioData,
                inputSize: audioData instanceof ArrayBuffer ? audioData.byteLength :
                    audioData instanceof Float32Array ? audioData.length :
                        audioData.length
            });

            // Create WaveFile instance for universal conversion
            const wav = new WaveFile();

            // Load input data based on format
            await this._loadInputData(wav, audioData, fromFormat, config);

            // Apply format conversions
            if (config.sampleRate && config.sampleRate !== (wav as any).fmt?.sampleRate) {
                wav.toSampleRate(config.sampleRate);
            }

            if (config.bitDepth && config.bitDepth !== (wav as any).bitDepth) {
                wav.toBitDepth(config.bitDepth.toString() as any);
            }

            if (config.numChannels && config.numChannels !== (wav as any).fmt?.numChannels) {
                // Convert to mono/stereo as needed
                if (config.numChannels === 1 && (wav as any).fmt?.numChannels > 1) {
                    // Convert to mono - this would need custom implementation
                    this.logger.warn('Channel conversion not fully implemented in wavefile');
                }
            }

            // Export in target format
            const result = await this._exportToFormat(wav, toFormat);

            const processingTime = Date.now() - startTime;

            this.logger.debug('Audio conversion completed', {
                processingTimeMs: processingTime,
                originalSize: audioData instanceof ArrayBuffer ? audioData.byteLength :
                    audioData instanceof Float32Array ? audioData.length * 4 :
                        audioData.length,
                convertedSize: result instanceof ArrayBuffer ? result.byteLength : result.length
            });

            return {
                success: true,
                data: result,
                metadata: {
                    originalSize: audioData instanceof ArrayBuffer ? audioData.byteLength :
                        audioData instanceof Float32Array ? audioData.length * 4 :
                            audioData.length,
                    convertedSize: result instanceof ArrayBuffer ? result.byteLength : result.length,
                    processingTimeMs: processingTime,
                    originalFormat: fromFormat,
                    targetFormat: toFormat
                }
            };

        } catch (error) {
            const processingTime = Date.now() - startTime;
            const errorMsg = `Audio conversion failed: ${error instanceof Error ? error.message : String(error)}`;

            this.logger.error('Audio conversion error', {
                error: errorMsg,
                fromFormat,
                toFormat,
                processingTimeMs: processingTime
            });

            return {
                success: false,
                error: errorMsg,
                metadata: {
                    originalSize: 0,
                    convertedSize: 0,
                    processingTimeMs: processingTime,
                    originalFormat: fromFormat,
                    targetFormat: toFormat
                }
            };
        }
    }

    /**
     * Helper method to get data size consistently
     */
    private _getDataSize(data: any): number {
        if (data instanceof ArrayBuffer) return data.byteLength;
        if (data instanceof Float32Array || data instanceof Int16Array || data instanceof Uint8Array) return data.length * data.BYTES_PER_ELEMENT;
        if (typeof data === 'string') return data.length;
        return 0;
    }

    /**
     * Check if format is a compression format
     */
    private _isCompressionFormat(format: AudioFormatType): boolean {
        return ['8a', '8m', '4'].includes(format); // A-Law, μ-Law, ADPCM
    }

    /**
     * Check if format is a raw PCM format
     */
    private _isPCMFormat(format: AudioFormatType): boolean {
        return ['pcm', '8', '16', '24', '32', '32f', '64'].includes(format);
    }

    /**
     * Handle compression format conversions (A-Law, μ-Law, ADPCM)
     */
    private async _handleCompressionConversion(
        audioData: any,
        fromFormat: AudioFormatType,
        toFormat: AudioFormatType,
        config: Partial<AudioConfig>
    ): Promise<ArrayBuffer | string | Float32Array | Int16Array> {
        const wav = new WaveFile();

        // Load data
        await this._loadInputData(wav, audioData, fromFormat, config);

        // Apply compression/decompression
        if (fromFormat === '8a') {
            wav.fromALaw(toFormat === 'pcm' ? '16' : toFormat);
        } else if (fromFormat === '8m') {
            wav.fromMuLaw(toFormat === 'pcm' ? '16' : toFormat);
        } else if (fromFormat === '4') {
            wav.fromIMAADPCM(toFormat === 'pcm' ? '16' : toFormat);
        }

        if (toFormat === '8a') {
            wav.toALaw();
        } else if (toFormat === '8m') {
            wav.toMuLaw();
        } else if (toFormat === '4') {
            wav.toIMAADPCM();
        }

        return this._exportToFormat(wav, toFormat, config);
    }

    /**
     * Handle PCM format conversions
     */
    private async _handlePCMConversion(
        audioData: any,
        fromFormat: AudioFormatType,
        toFormat: AudioFormatType,
        config: Partial<AudioConfig>
    ): Promise<ArrayBuffer | string | Float32Array | Int16Array> {
        // Direct PCM conversions for better performance
        if (audioData instanceof Float32Array) {
            switch (toFormat) {
                case '16':
                case 'pcm':
                    return this._float32ToInt16(audioData);
                case '8':
                    return this._float32ToUint8(audioData);
                case '32':
                    return this._float32ToInt32(audioData);
                default:
                    return audioData; // Already Float32
            }
        }

        // Fallback to WaveFile for complex conversions
        const wav = new WaveFile();
        await this._loadInputData(wav, audioData, fromFormat, config);

        if (config.sampleRate) wav.toSampleRate(config.sampleRate);
        if (config.bitDepth) wav.toBitDepth(config.bitDepth.toString());
        if (config.numChannels) {
            // Note: Channel conversion would need additional implementation
            this.logger.warn('Channel conversion not fully implemented');
        }

        return this._exportToFormat(wav, toFormat, config);
    }

    /**
     * Handle container format conversions (WAV, MP3, etc.)
     */
    private async _handleContainerConversion(
        audioData: any,
        fromFormat: AudioFormatType,
        toFormat: AudioFormatType,
        config: Partial<AudioConfig>
    ): Promise<ArrayBuffer | string | Float32Array | Int16Array> {
        const wav = new WaveFile();
        await this._loadInputData(wav, audioData, fromFormat, config);

        // Apply configuration changes
        if (config.sampleRate) wav.toSampleRate(config.sampleRate);
        if (config.bitDepth) wav.toBitDepth(config.bitDepth.toString());

        return this._exportToFormat(wav, toFormat, config);
    }

    /**
     * Load input data into WaveFile instance
     */
    private async _loadInputData(
        wav: any,
        audioData: ArrayBuffer | Float32Array | Int16Array | Uint8Array | string,
        format: AudioFormatType,
        config: Partial<AudioConfig>
    ): Promise<void> {
        if (typeof audioData === 'string') {
            // Base64 input
            const binaryString = atob(audioData);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }
            wav.fromBuffer(bytes.buffer);
        } else if (audioData instanceof ArrayBuffer) {
            wav.fromBuffer(audioData);
        } else if (audioData instanceof Float32Array || audioData instanceof Int16Array || audioData instanceof Uint8Array) {
            // Convert typed arrays to WAV
            const sampleRate = config.sampleRate || DEFAULT_AUDIO_CONFIG.sampleRate;
            const numChannels = config.numChannels || DEFAULT_AUDIO_CONFIG.numChannels;
            const bitDepth = config.bitDepth || DEFAULT_AUDIO_CONFIG.bitDepth;

            wav.fromScratch(numChannels, sampleRate, bitDepth.toString() as any, audioData);
        } else {
            throw new Error('Unsupported input audio data type');
        }
    }

    /**
     * Export WaveFile to target format
     */
    private async _exportToFormat(wav: any, format: AudioFormatType, config?: Partial<AudioConfig>): Promise<ArrayBuffer | string | Float32Array | Int16Array> {
        switch (format) {
            case AudioFormat.WAV:
                return wav.toBuffer();
            case AudioFormat.PCM:
            case '16':
                return wav.getSamples(false, Int16Array);
            case '32f':
                return wav.getSamples(false, Float32Array);
            case '8':
                return wav.getSamples(false, Uint8Array);
            default:
                // For unsupported formats, return WAV buffer
                return wav.toBuffer();
        }
    }

    /**
     * Optimized Float32 to Int16 conversion
     */
    private _float32ToInt16(float32Array: Float32Array): Int16Array {
        const int16Array = new Int16Array(float32Array.length);
        for (let i = 0; i < float32Array.length; i++) {
            const sample = Math.max(-1, Math.min(1, float32Array[i]));
            int16Array[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
        }
        return int16Array;
    }

    /**
     * Optimized Float32 to Uint8 conversion
     */
    private _float32ToUint8(float32Array: Float32Array): Uint8Array {
        const uint8Array = new Uint8Array(float32Array.length);
        for (let i = 0; i < float32Array.length; i++) {
            const sample = Math.max(-1, Math.min(1, float32Array[i]));
            uint8Array[i] = Math.round((sample + 1) * 127.5);
        }
        return uint8Array;
    }

    /**
     * Optimized Float32 to Int32 conversion
     */
    private _float32ToInt32(float32Array: Float32Array): Int32Array {
        const int32Array = new Int32Array(float32Array.length);
        for (let i = 0; i < float32Array.length; i++) {
            const sample = Math.max(-1, Math.min(1, float32Array[i]));
            int32Array[i] = sample < 0 ? sample * 0x80000000 : sample * 0x7FFFFFFF;
        }
        return int32Array;
    }

    /**
     * Quick conversion methods for common use cases
     */
    async floatArrayToWav(audioData: Float32Array, config: Partial<AudioConfig> = {}): Promise<ArrayBuffer | null> {
        const result = await this.convert(audioData, AudioFormat.PCM, AudioFormat.WAV, config);
        return result.success ? result.data as ArrayBuffer : null;
    }

    async bufferToBase64(audioBuffer: ArrayBuffer): Promise<string | null> {
        try {
            const uint8Array = new Uint8Array(audioBuffer);
            let binary = '';
            for (let i = 0; i < uint8Array.length; i++) {
                binary += String.fromCharCode(uint8Array[i]);
            }
            return btoa(binary);
        } catch (error) {
            this.logger.error('Buffer to base64 conversion failed:', error);
            return null;
        }
    }

    async base64ToBuffer(base64: string): Promise<ArrayBuffer | null> {
        try {
            const binaryString = atob(base64);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }
            return bytes.buffer;
        } catch (error) {
            this.logger.error('Base64 to buffer conversion failed:', error);
            return null;
        }
    }
}

export interface AudioConfig {
    sampleRate: number;
    numChannels: number;
    bitDepth: number;
    chunkDurationMs?: number;
    chunkSize?: number;  // Audio chunk size in bytes
}

// Default audio configuration based on Aliyun requirements (16-bit 24kHz mono PCM)
// CRITICAL: Matches Python working implementation exactly (RATE = 24000)
// Reference: debug/vad_mode.py - RATE = 24000, CHUNK = 3200, asyncio.sleep(0.2)
export const DEFAULT_AUDIO_CONFIG: AudioConfig = {
    sampleRate: 24000,    // 24kHz sample rate (matches Python working implementation)
    numChannels: 1,       // Mono audio (required by Aliyun)
    bitDepth: 16,         // 16-bit PCM (required by Aliyun)
    chunkDurationMs: 200  // 200ms chunks = 5 chunks/sec (matches Python rate limiting)
};

/**
 * Audio processing result interface
 */
export interface AudioProcessingResult {
    success: boolean;
    base64Audio?: string;
    error?: string;
    metadata?: {
        inputType: string;
        inputSize: number;
        outputSize: number;
        processingTimeMs: number;
    };
}

/**
 * Audio validation result interface
 */
export interface AudioValidationResult {
    isValid: boolean;
    type?: string;
    size?: number;
    error?: string;
}

/**
 * Detect audio format from header bytes
 */
export function detectAudioFormat(data: Uint8Array): AudioFormatType {
    if (!data || data.length < 4) {
        return AudioFormat.WAV; // Default to WAV if no data or too short
    }

    // Check for MP3 format (ID3 tag)
    if (data[0] === 0x49 && data[1] === 0x44 && data[2] === 0x33) {
        return AudioFormat.MP3;
    }

    // Check for MP3 format (MPEG frame sync)
    if (data[0] === 0xFF && (data[1] & 0xE0) === 0xE0) {
        return AudioFormat.MP3;
    }

    // Check for WAV format (RIFF header)
    if (data[0] === 0x52 && data[1] === 0x49 && data[2] === 0x46 && data[3] === 0x46) {
        return AudioFormat.WAV;
    }

    // Check for OGG format (OggS header)
    if (data[0] === 0x4F && data[1] === 0x67 && data[2] === 0x67 && data[3] === 0x53) {
        return AudioFormat.OGG;
    }

    return AudioFormat.WAV; // Default to WAV if unknown format
}

/**
 * Convert Float32Array to WAV format
 */
export function convertFloat32ToWav(
    samples: Float32Array,
    config: Partial<AudioConfig> = {}
): Blob {
    // Validate input
    if (!samples || !(samples instanceof Float32Array)) {
        const typeName = (samples as any)?.constructor?.name || typeof samples;
        throw new Error(`Invalid input: expected Float32Array, got ${typeName}`);
    }

    // Allow empty arrays - they will produce a minimal WAV file

    // Use centralized config for defaults
    const sampleRate = config.sampleRate ?? DEFAULT_AUDIO_CONFIG.sampleRate;
    const numChannels = config.numChannels ?? DEFAULT_AUDIO_CONFIG.numChannels;
    const bitDepth = config.bitDepth ?? DEFAULT_AUDIO_CONFIG.bitDepth;

    const bytesPerSample = bitDepth / 8;
    const blockAlign = numChannels * bytesPerSample;
    const dataLength = samples.length * bytesPerSample;
    const buffer = new ArrayBuffer(44 + dataLength);
    const view = new DataView(buffer);

    // Write WAV header
    writeString(view, 0, 'RIFF');
    view.setUint32(4, 36 + dataLength, true);
    writeString(view, 8, 'WAVE');
    writeString(view, 12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * blockAlign, true);
    view.setUint16(32, blockAlign, true);
    view.setUint16(34, bitDepth, true);
    writeString(view, 36, 'data');
    view.setUint32(40, dataLength, true);

    // Write audio data
    if (bitDepth === 16) {
        floatTo16BitPCM(view, 44, samples);
    } else if (bitDepth === 8) {
        floatTo8BitPCM(view, 44, samples);
    } else if (bitDepth === 32) {
        floatTo32BitPCM(view, 44, samples);
    }

    return new Blob([buffer], { type: 'audio/wav' });
}

/**
 * Validate audio data before processing
 */
export function validateAudioData(audioData: any): AudioValidationResult {
    logger.debug('Validating audio data', {
        type: typeof audioData,
        isNull: audioData === null,
        isUndefined: audioData === undefined
    });

    if (!audioData) {
        return {
            isValid: false,
            error: 'Audio data is null or undefined'
        };
    }

    if (typeof audioData === 'string') {
        return {
            isValid: audioData.length > 0,
            type: 'base64_string',
            size: audioData.length,
            error: audioData.length === 0 ? 'String is empty' : undefined
        };
    }

    if (audioData instanceof ArrayBuffer) {
        return {
            isValid: audioData.byteLength > 0,
            type: 'ArrayBuffer',
            size: audioData.byteLength,
            error: audioData.byteLength === 0 ? 'ArrayBuffer is empty' : undefined
        };
    }

    if (audioData instanceof Float32Array || audioData instanceof Int16Array) {
        return {
            isValid: audioData.length > 0,
            type: audioData.constructor.name,
            size: audioData.length,
            error: audioData.length === 0 ? 'Audio array is empty' : undefined
        };
    }

    return {
        isValid: false,
        error: `Unsupported audio data type: ${typeof audioData}`
    };
}

/**
 * Process audio data for real-time streaming APIs (consolidated from realtimeAudioProcessor)
 */
export async function processRealtimeAudio(
    audioData: ArrayBuffer | Float32Array | Int16Array | string,
    options: Partial<AudioConfig & { enableDebugLogging?: boolean }> = {}
): Promise<AudioProcessingResult> {
    const startTime = Date.now();

    const {
        sampleRate = DEFAULT_AUDIO_CONFIG.sampleRate,
        numChannels = DEFAULT_AUDIO_CONFIG.numChannels,
        bitDepth = DEFAULT_AUDIO_CONFIG.bitDepth,
        enableDebugLogging = false
    } = options;

    try {
        // Validate input
        const validation = validateAudioData(audioData);
        if (!validation.isValid) {
            return {
                success: false,
                error: validation.error
            };
        }

        if (enableDebugLogging) {
            logger.debug('🔄 Processing realtime audio data', {
                dataType: validation.type,
                dataSize: validation.size,
                config: { sampleRate, numChannels, bitDepth }
            });
        }

        // If already base64 string, return as-is
        if (typeof audioData === 'string') {
            const processingTime = Date.now() - startTime;
            return {
                success: true,
                base64Audio: audioData,
                metadata: {
                    inputType: 'base64_string',
                    inputSize: audioData.length,
                    outputSize: audioData.length,
                    processingTimeMs: processingTime
                }
            };
        }

        // Convert to Float32Array
        let float32Audio: Float32Array;

        if (audioData instanceof ArrayBuffer) {
            const int16Data = new Int16Array(audioData);
            float32Audio = new Float32Array(int16Data.length);

            for (let i = 0; i < int16Data.length; i++) {
                float32Audio[i] = int16Data[i] / (int16Data[i] < 0 ? 0x8000 : 0x7FFF);
            }

            if (enableDebugLogging) {
                logger.debug('🔄 Converted ArrayBuffer to Float32Array', {
                    originalLength: int16Data.length,
                    convertedLength: float32Audio.length
                });
            }
        } else if (audioData instanceof Float32Array) {
            float32Audio = audioData;
        } else if (audioData instanceof Int16Array) {
            float32Audio = new Float32Array(audioData.length);
            for (let i = 0; i < audioData.length; i++) {
                float32Audio[i] = audioData[i] / (audioData[i] < 0 ? 0x8000 : 0x7FFF);
            }
        } else {
            throw new Error(`Unsupported audio data type: ${typeof audioData}`);
        }

        // Convert to WAV
        const wavBlob = convertFloat32ToWav(float32Audio, {
            sampleRate,
            numChannels,
            bitDepth
        });

        // Convert to base64
        const arrayBuffer = await wavBlob.arrayBuffer();
        const uint8Array = new Uint8Array(arrayBuffer);
        const base64Audio = btoa(String.fromCharCode.apply(null, Array.from(uint8Array)));

        const processingTime = Date.now() - startTime;

        if (enableDebugLogging) {
            logger.debug('✅ Audio processing completed', {
                wavBlobSize: wavBlob.size,
                base64Length: base64Audio.length,
                processingTimeMs: processingTime
            });
        }

        return {
            success: true,
            base64Audio,
            metadata: {
                inputType: validation.type || 'unknown',
                inputSize: validation.size || 0,
                outputSize: base64Audio.length,
                processingTimeMs: processingTime
            }
        };

    } catch (error) {
        const processingTime = Date.now() - startTime;
        const errorMsg = `Audio processing failed: ${error instanceof Error ? error.message : String(error)}`;

        if (enableDebugLogging) {
            logger.error('❌ Audio processing error', { error: errorMsg, processingTimeMs: processingTime });
        }

        return {
            success: false,
            error: errorMsg,
            metadata: {
                inputType: typeof audioData,
                inputSize: 0,
                outputSize: 0,
                processingTimeMs: processingTime
            }
        };
    }
}

/**
 * Create fallback base64 audio from raw data
 */
export function createFallbackBase64Audio(audioData: ArrayBuffer | Uint8Array): string {
    try {
        const uint8Array = new Uint8Array(audioData instanceof ArrayBuffer ? audioData : audioData.buffer);
        return btoa(String.fromCharCode.apply(null, Array.from(uint8Array)));
    } catch (error) {
        logger.warn('Fallback base64 conversion failed:', error);
        return '';
    }
}

/**
 * Convert base64 string to Blob
 */
export function base64ToBlob(base64: string, mimeType: string = 'audio/wav'): Blob {
    const byteCharacters = atob(base64);
    const byteArrays: Uint8Array[] = [];

    for (let offset = 0; offset < byteCharacters.length; offset += 1024) {
        const slice = byteCharacters.slice(offset, offset + 1024);
        const byteNumbers = new Array(slice.length);

        for (let i = 0; i < slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i);
        }

        byteArrays.push(new Uint8Array(byteNumbers));
    }

    return new Blob(byteArrays, { type: mimeType });
}

/**
 * Convert Blob to base64 string
 */
export function blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
            const result = reader.result as string;
            const base64 = result.split(',')[1];
            resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
    });
}

/**
 * Check if an AudioBuffer contains actual audio data
 */
export function checkAudioBuffer(
    buffer: AudioBuffer,
    options: {
        minAmplitude?: number;
        checkMultipleRanges?: boolean;
        logger?: any;
    } = {}
): { hasAudio: boolean; maxValue: number; percentNonZero: number } {
    const {
        minAmplitude = 0.0001,
        checkMultipleRanges = true,
        logger: loggerOverride = logger
    } = options;

    if (!buffer || !buffer.numberOfChannels || buffer.numberOfChannels < 1) {
        return { hasAudio: false, maxValue: 0, percentNonZero: 0 };
    }

    const data = buffer.getChannelData(0);
    let nonZeroCount = 0;
    let maxValue = 0;
    let samplesChecked = 0;

    // Function to check a specific range of the buffer
    const checkRange = (start: number, end: number) => {
        let rangeNonZeroCount = 0;
        let rangeMaxValue = 0;
        const rangeLength = end - start;
        const step = Math.max(1, Math.floor(rangeLength / 333));

        for (let i = start; i < end; i += step) {
            const absValue = Math.abs(data[i]);
            if (absValue > minAmplitude) {
                rangeNonZeroCount++;
            }
            rangeMaxValue = Math.max(rangeMaxValue, absValue);
        }

        return {
            nonZeroCount: rangeNonZeroCount,
            maxValue: rangeMaxValue,
            samplesChecked: Math.ceil(rangeLength / step)
        };
    };

    // Check multiple ranges if enabled and buffer is long enough
    if (checkMultipleRanges && data.length > 3000) {
        const ranges = [
            { start: 0, end: Math.min(1000, data.length) },
            { start: Math.floor(data.length / 2) - 500, end: Math.floor(data.length / 2) + 500 },
            { start: Math.max(0, data.length - 1000), end: data.length }
        ];

        for (const range of ranges) {
            const result = checkRange(range.start, range.end);
            nonZeroCount += result.nonZeroCount;
            maxValue = Math.max(maxValue, result.maxValue);
            samplesChecked += result.samplesChecked;
        }
    } else {
        const step = Math.max(1, Math.floor(data.length / 1000));
        for (let i = 0; i < data.length; i += step) {
            const absValue = Math.abs(data[i]);
            if (absValue > minAmplitude) {
                nonZeroCount++;
            }
            maxValue = Math.max(maxValue, absValue);
        }
        samplesChecked = Math.ceil(data.length / step);
    }

    const percentNonZero = (nonZeroCount / samplesChecked) * 100;
    const hasAudio = percentNonZero >= 1.0 && maxValue >= minAmplitude;

    return { hasAudio, maxValue, percentNonZero };
}

/**
 * Create a fallback tone generator
 */
export function createFallbackTone(options: {
    frequency?: number;
    duration?: number;
    sampleRate?: number;
    amplitude?: number;
    audioContext?: AudioContext;
} = {}): AudioBuffer {
    const frequency = options.frequency || 440;
    const duration = options.duration || 1.0;
    const sampleRate = options.sampleRate || 22050;
    const amplitude = options.amplitude || 0.5;

    let audioContext: AudioContext;
    let shouldCloseContext = false;

    if (options.audioContext) {
        audioContext = options.audioContext;
    } else {
        if (typeof window !== 'undefined' && window.AudioContext) {
            audioContext = new AudioContext();
            shouldCloseContext = true;
        } else {
            throw new Error('AudioContext not available');
        }
    }

    const buffer = audioContext.createBuffer(1, Math.ceil(duration * sampleRate), sampleRate);
    const channelData = buffer.getChannelData(0);

    for (let i = 0; i < channelData.length; i++) {
        channelData[i] = amplitude * Math.sin(2 * Math.PI * frequency * i / sampleRate);
    }

    if (shouldCloseContext) {
        audioContext.close().catch(console.warn);
    }

    return buffer;
}

/**
 * Create an Audio element from a blob
 */
export function createAudioFromBlob(blob: Blob): Promise<HTMLAudioElement> {
    return new Promise((resolve, reject) => {
        const audio = new Audio();
        const url = URL.createObjectURL(blob);

        audio.onloadeddata = () => {
            URL.revokeObjectURL(url);
            resolve(audio);
        };

        audio.onerror = (error) => {
            URL.revokeObjectURL(url);
            reject(error);
        };

        audio.src = url;
    });
}

/**
 * Play audio with promise
 */
export function playAudioWithPromise(audio: HTMLAudioElement): Promise<void> {
    return new Promise((resolve, reject) => {
        const onEnded = () => {
            audio.removeEventListener('ended', onEnded);
            audio.removeEventListener('error', onError);
            resolve();
        };

        const onError = (error: any) => {
            audio.removeEventListener('ended', onEnded);
            audio.removeEventListener('error', onError);
            reject(error);
        };

        audio.addEventListener('ended', onEnded);
        audio.addEventListener('error', onError);

        audio.play().catch(reject);
    });
}

/**
 * Split audio into chunks
 */
export function splitAudioIntoChunks(
    audio: HTMLAudioElement,
    chunkDurationMs: number = DEFAULT_AUDIO_CONFIG.chunkDurationMs!
): Array<{ duration: number; isLast: boolean }> {
    const numChunks = Math.ceil(audio.duration / (chunkDurationMs / 1000));
    const chunks = [];

    for (let i = 0; i < numChunks; i++) {
        const isLast = i === numChunks - 1;
        const chunkDuration = isLast ?
            audio.duration - (i * chunkDurationMs / 1000) :
            chunkDurationMs / 1000;

        chunks.push({
            duration: chunkDuration,
            isLast
        });
    }

    return chunks;
}

/**
 * Convert Base64 encoded string to ArrayBuffer
 * @deprecated Use base64ToArrayBuffer instead for consistency
 */
export function b64ToArrayBuffer(b64String: string): ArrayBuffer {
    return base64ToArrayBuffer(b64String);
}

/**
 * Concatenate an array of ArrayBuffers
 */
export function concatArrayBuffers(buffers: ArrayBuffer[]): ArrayBuffer {
    const totalLength = buffers.reduce((acc, buffer) => acc + buffer.byteLength, 0);
    const result = new Uint8Array(totalLength);
    let offset = 0;

    for (const buffer of buffers) {
        result.set(new Uint8Array(buffer), offset);
        offset += buffer.byteLength;
    }

    return result.buffer;
}

/**
 * Convert PCM buffer to AudioBuffer (signed 16bit little endian)
 */
export function pcmToAudioBuffer(
    buffer: ArrayBuffer,
    sampleRate: number = 22050
): AudioBuffer | null {
    if (typeof window === 'undefined' || !window.AudioContext) {
        console.warn('AudioContext not available in this environment');
        return null;
    }

    try {
        const audioContext = new AudioContext();
        const pcmData = new Int16Array(buffer);
        const audioBuffer = audioContext.createBuffer(1, pcmData.length, sampleRate);
        const channelData = audioBuffer.getChannelData(0);

        // Convert 16-bit PCM to float32 (-1.0 to 1.0)
        for (let i = 0; i < pcmData.length; i++) {
            channelData[i] = pcmData[i] / (pcmData[i] < 0 ? 0x8000 : 0x7FFF);
        }

        audioContext.close().catch(console.warn);
        return audioBuffer;
    } catch (error) {
        console.error('Failed to create AudioBuffer:', error);
        return null;
    }
}

/**
 * Set reverb for audio context
 */
export async function setReverb(
    audioContext: AudioContext,
    reverbNode: ConvolverNode,
    impulseResponse: ArrayBuffer | null = null
): Promise<void> {
    if (!impulseResponse) {
        reverbNode.buffer = null;
        return;
    }

    try {
        const audioBuffer = await audioContext.decodeAudioData(impulseResponse);
        reverbNode.buffer = audioBuffer;
    } catch (error) {
        console.error('Failed to set reverb:', error);
    }
}

/**
 * Set mixer gain levels
 */
export function setMixerGain(
    speechGainNode: GainNode,
    backgroundGainNode: GainNode,
    speechGain: number | null = null,
    backgroundGain: number | null = null
): void {
    if (speechGain !== null) {
        speechGainNode.gain.value = Math.max(0, Math.min(1, speechGain));
    }
    if (backgroundGain !== null) {
        backgroundGainNode.gain.value = Math.max(0, Math.min(1, backgroundGain));
    }
}

/**
 * Extract role name from audio file path
 */
export function extractRoleNameFromAudioFile(
    audioFilePath: string,
    options: { logger?: any } = {}
): string {
    const logger = options.logger || console;

    try {
        // Extract filename from path
        const filename = audioFilePath.split('/').pop() || audioFilePath;

        // Remove extension
        const nameWithoutExt = filename.replace(/\.[^/.]+$/, '');

        // Extract role name (assuming format like "role_timestamp.wav")
        const parts = nameWithoutExt.split('_');

        if (parts.length > 0) {
            return parts[0];
        }

        return nameWithoutExt;
    } catch (error) {
        logger.warn('Failed to extract role name:', error);
        return 'unknown';
    }
}

/**
 * Prepare request payload for TTS services
 */
export function prepareTTSRequestPayload(
    text: string,
    options: {
        voice?: string;
        speed?: number;
        pitch?: number;
        volume?: number;
        format?: string;
        sampleRate?: number;
    } = {}
): { payload: any; headers: Record<string, string> } {
    const {
        voice = 'default',
        speed = 1.0,
        pitch = 1.0,
        volume = 1.0,
        format = 'wav',
        sampleRate = 22050
    } = options;

    const payload = {
        text,
        voice,
        speed: Math.max(0.25, Math.min(4.0, speed)),
        pitch: Math.max(0.5, Math.min(2.0, pitch)),
        volume: Math.max(0.0, Math.min(1.0, volume)),
        format,
        sampleRate
    };

    const headers = {
        'Content-Type': 'application/json',
        'Accept': `audio/${format}`
    };

    return { payload, headers };
}

// Helper functions for WAV encoding
function writeString(view: DataView, offset: number, string: string): void {
    for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
    }
}

function floatTo16BitPCM(view: DataView, offset: number, samples: Float32Array): void {
    for (let i = 0; i < samples.length; i++) {
        const sample = Math.max(-1, Math.min(1, samples[i]));
        view.setInt16(offset + i * 2, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
    }
}

function floatTo8BitPCM(view: DataView, offset: number, samples: Float32Array): void {
    for (let i = 0; i < samples.length; i++) {
        const sample = Math.max(-1, Math.min(1, samples[i]));
        view.setUint8(offset + i, (sample + 1) * 128);
    }
}

function floatTo32BitPCM(view: DataView, offset: number, samples: Float32Array): void {
    for (let i = 0; i < samples.length; i++) {
        view.setFloat32(offset + i * 4, samples[i], true);
    }
}

/**
 * Convert Float32Array to Int16Array PCM (for realtime streaming)
 */
export function float32ToInt16PCM(floatBuffer: Float32Array): Int16Array {
    const int16 = new Int16Array(floatBuffer.length);
    for (let i = 0; i < floatBuffer.length; i++) {
        let s = Math.max(-1, Math.min(1, floatBuffer[i]));
        int16[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
    }
    return int16;
}

/**
 * Upsample a Float32Array audio buffer to 24kHz using linear interpolation.
 * @param input Float32Array input audio samples
 * @param inputSampleRate Input sample rate (e.g., 16000)
 * @returns Float32Array resampled to 24000Hz
 */
export function upsampleTo24kHz(input: Float32Array, inputSampleRate: number): Float32Array {
    if (inputSampleRate === 24000) return input;
    const outputLength = Math.round(input.length * 24000 / inputSampleRate);
    const output = new Float32Array(outputLength);
    for (let i = 0; i < outputLength; i++) {
        const t = i * (input.length - 1) / (outputLength - 1);
        const idx = Math.floor(t);
        const frac = t - idx;
        const s0 = input[idx];
        const s1 = idx + 1 < input.length ? input[idx + 1] : input[idx];
        output[i] = s0 + (s1 - s0) * frac;
    }
    return output;
}

/**
 * Robust base64 to Uint8Array conversion with validation
 */
export function base64ToUint8Array(base64: string): Uint8Array {
    try {
        // Clean and validate base64 string
        if (!base64 || typeof base64 !== 'string') {
            throw new Error('Invalid base64 audio data: must be a non-empty string');
        }

        // Remove any whitespace, line breaks, and data URL prefix if present
        let cleanBase64 = base64.trim();
        if (cleanBase64.startsWith('data:audio/')) {
            cleanBase64 = cleanBase64.split(',')[1];
        }
        cleanBase64 = cleanBase64.replace(/\s/g, ''); // Remove all whitespace

        // Check for empty string after cleaning
        if (!cleanBase64) {
            throw new Error('Invalid base64 audio data: empty string after cleaning');
        }

        // Validate base64 format
        if (!/^[A-Za-z0-9+/]*={0,2}$/.test(cleanBase64)) {
            throw new Error('Invalid base64 format');
        }

        // Convert to Uint8Array
        const binaryString = atob(cleanBase64);
        const bytes = new Uint8Array(binaryString.length);

        for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }

        return bytes;
    } catch (error) {
        throw new Error(`Base64 to Uint8Array conversion failed: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * Detect if base64 data contains a complete WAV file or raw PCM data
 */
export function detectAudioFormatFromBase64(base64: string): {
    isComplete: boolean;
    format: 'complete_wav' | 'raw_pcm' | 'unknown';
    needsHeaders: boolean;
    audioInfo?: {
        sampleRate?: number;
        channels?: number;
        bitDepth?: number;
    };
} {
    try {
        // Validate input
        if (!base64 || typeof base64 !== 'string') {
            logger.warn('Audio format detection: invalid base64 input');
            return {
                isComplete: false,
                format: 'unknown',
                needsHeaders: true
            };
        }

        // Clean base64 string
        let cleanBase64 = base64.trim();
        if (cleanBase64.startsWith('data:audio/')) {
            cleanBase64 = cleanBase64.split(',')[1];
        }
        cleanBase64 = cleanBase64.replace(/\s/g, '');

        // Check for empty string after cleaning
        if (!cleanBase64) {
            logger.warn('Audio format detection: empty base64 string');
            return {
                isComplete: false,
                format: 'unknown',
                needsHeaders: true
            };
        }

        // Decode first 44 bytes (WAV header size) to check format
        const headerBytes = atob(cleanBase64.slice(0, Math.min(60, cleanBase64.length))); // ~44 bytes in base64
        const headerView = new Uint8Array(Math.min(44, headerBytes.length));

        for (let i = 0; i < headerView.length && i < headerBytes.length; i++) {
            headerView[i] = headerBytes.charCodeAt(i);
        }

        // Check for RIFF header (0x52494646 = "RIFF")
        const hasRIFF = headerView.length >= 4 &&
            headerView[0] === 0x52 && headerView[1] === 0x49 &&
            headerView[2] === 0x46 && headerView[3] === 0x46;

        // Check for WAVE format (0x57415645 = "WAVE" at offset 8)
        const hasWAVE = headerView.length >= 12 &&
            headerView[8] === 0x57 && headerView[9] === 0x41 &&
            headerView[10] === 0x56 && headerView[11] === 0x45;

        // Check for fmt chunk (0x666d7420 = "fmt " at offset 12)
        const hasFMT = headerView.length >= 16 &&
            headerView[12] === 0x66 && headerView[13] === 0x6d &&
            headerView[14] === 0x74 && headerView[15] === 0x20;

        const isCompleteWAV = hasRIFF && hasWAVE && hasFMT;

        let audioInfo = {};
        if (isCompleteWAV && headerView.length >= 44) {
            // Extract audio parameters from WAV header
            const view = new DataView(headerView.buffer);
            audioInfo = {
                channels: view.getUint16(22, true),
                sampleRate: view.getUint32(24, true),
                bitDepth: view.getUint16(34, true)
            };
        }

        logger.debug('Audio format detection:', {
            base64Length: base64.length,
            headerPreview: Array.from(headerView.slice(0, 16)).map(b => b.toString(16).padStart(2, '0')).join(' '),
            hasRIFF,
            hasWAVE,
            hasFMT,
            isCompleteWAV,
            audioInfo
        });

        return {
            isComplete: isCompleteWAV,
            format: isCompleteWAV ? 'complete_wav' : 'raw_pcm',
            needsHeaders: !isCompleteWAV,
            audioInfo: Object.keys(audioInfo).length > 0 ? audioInfo : undefined
        };

    } catch (error) {
        logger.warn('Audio format detection failed:', error);
        return {
            isComplete: false,
            format: 'unknown',
            needsHeaders: true
        };
    }
}

/**
 * Convert raw PCM base64 data to complete WAV format with headers
 * Uses the standard configuration for LLM audio responses (24kHz, 16-bit, mono)
 */
export function convertPCMToWAVBlob(base64PCM: string, config: Partial<AudioConfig> = {}): Blob {
    try {
        // Use standard LLM audio configuration
        const {
            sampleRate = 24000,  // Standard for most LLM audio responses
            numChannels = 1,     // Mono audio
            bitDepth = 16        // 16-bit PCM
        } = config;

        // Decode PCM data
        const pcmData = atob(base64PCM);
        const pcmBytes = new Uint8Array(pcmData.length);
        for (let i = 0; i < pcmData.length; i++) {
            pcmBytes[i] = pcmData.charCodeAt(i);
        }

        const bytesPerSample = bitDepth / 8;
        const blockAlign = numChannels * bytesPerSample;
        const dataSize = pcmBytes.length;
        const fileSize = 36 + dataSize;

        // Create WAV header (44 bytes)
        const header = new ArrayBuffer(44);
        const view = new DataView(header);

        // RIFF header
        view.setUint32(0, 0x46464952, true);   // "RIFF" in little-endian
        view.setUint32(4, fileSize, true);     // File size - 8
        view.setUint32(8, 0x45564157, true);   // "WAVE" in little-endian

        // fmt chunk
        view.setUint32(12, 0x20746d66, true);  // "fmt " in little-endian
        view.setUint32(16, 16, true);          // fmt chunk size
        view.setUint16(20, 1, true);           // PCM format
        view.setUint16(22, numChannels, true); // Number of channels
        view.setUint32(24, sampleRate, true);  // Sample rate
        view.setUint32(28, sampleRate * blockAlign, true); // Byte rate
        view.setUint16(32, blockAlign, true);  // Block align
        view.setUint16(34, bitDepth, true);    // Bits per sample

        // data chunk
        view.setUint32(36, 0x61746164, true);  // "data" in little-endian
        view.setUint32(40, dataSize, true);    // Data size

        // Combine header and PCM data
        const wavBuffer = new Uint8Array(44 + dataSize);
        wavBuffer.set(new Uint8Array(header), 0);
        wavBuffer.set(pcmBytes, 44);

        logger.debug('PCM to WAV conversion completed:', {
            pcmSize: dataSize,
            wavSize: wavBuffer.length,
            sampleRate,
            numChannels,
            bitDepth,
            estimatedDuration: dataSize / (sampleRate * blockAlign)
        });

        return new Blob([wavBuffer], { type: 'audio/wav' });

    } catch (error) {
        logger.error('PCM to WAV conversion failed:', error);
        throw new Error(`Failed to convert PCM to WAV: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * Validate audio blob by checking WAV header structure
 */
export async function validateAudioBlob(audioBlob: Blob): Promise<{
    isValid: boolean;
    audioInfo?: {
        channels: number;
        sampleRate: number;
        bitDepth: number;
        dataSize: number;
        duration: number;
    };
    error?: string;
}> {
    try {
        if (audioBlob.size < 44) {
            return { isValid: false, error: 'Audio blob too small to contain WAV header' };
        }

        const arrayBuffer = await audioBlob.arrayBuffer();
        const view = new DataView(arrayBuffer);

        // Check WAV header components
        const riff = view.getUint32(0, true) === 0x46464952; // "RIFF"
        const wave = view.getUint32(8, true) === 0x45564157; // "WAVE"
        const fmt = view.getUint32(12, true) === 0x20746d66; // "fmt "
        const data = view.getUint32(36, true) === 0x61746164; // "data"

        const isValidWAV = riff && wave && fmt && data;

        if (!isValidWAV) {
            const missing = [];
            if (!riff) missing.push('RIFF header');
            if (!wave) missing.push('WAVE format');
            if (!fmt) missing.push('fmt chunk');
            if (!data) missing.push('data chunk');

            return {
                isValid: false,
                error: `Invalid WAV structure. Missing: ${missing.join(', ')}`
            };
        }

        // Extract audio information
        const audioInfo = {
            channels: view.getUint16(22, true),
            sampleRate: view.getUint32(24, true),
            bitDepth: view.getUint16(34, true),
            dataSize: view.getUint32(40, true),
            duration: 0
        };

        // Calculate duration
        const bytesPerSample = audioInfo.bitDepth / 8;
        const samplesPerSecond = audioInfo.sampleRate * audioInfo.channels;
        audioInfo.duration = audioInfo.dataSize / (samplesPerSecond * bytesPerSample);

        return { isValid: true, audioInfo };

    } catch (error) {
        return {
            isValid: false,
            error: `Audio validation failed: ${error instanceof Error ? error.message : String(error)}`
        };
    }
}

/**
 * Enhanced base64 to Blob conversion with automatic format detection and processing
 */
export function base64ToBlobEnhanced(base64: string, mimeType: string = 'audio/wav'): Blob {
    try {
        // Validate input
        if (!base64 || typeof base64 !== 'string') {
            throw new Error('Invalid base64 audio data: must be a non-empty string');
        }

        // First, detect what kind of audio data we have
        const formatInfo = detectAudioFormatFromBase64(base64);

        if (formatInfo.format === 'unknown') {
            throw new Error('Unable to detect audio format from base64 data');
        }

        if (formatInfo.isComplete) {
            // Already a complete WAV file, convert directly
            logger.debug('Audio is complete WAV, converting directly to blob');
            const uint8Array = base64ToUint8Array(base64);
            return new Blob([uint8Array], { type: mimeType });
        } else if (formatInfo.format === 'raw_pcm') {
            // Raw PCM data, need to add WAV headers
            logger.debug('Audio is raw PCM, adding WAV headers');
            return convertPCMToWAVBlob(base64, {
                sampleRate: formatInfo.audioInfo?.sampleRate || 24000,
                numChannels: formatInfo.audioInfo?.channels || 1,
                bitDepth: formatInfo.audioInfo?.bitDepth || 16
            });
        } else {
            // Unknown format, try direct conversion and hope for the best
            logger.warn('Unknown audio format, attempting direct conversion');
            const uint8Array = base64ToUint8Array(base64);
            return new Blob([uint8Array], { type: mimeType });
        }

    } catch (error) {
        logger.error('Enhanced base64 to blob conversion failed:', error);
        throw new Error(`Failed to convert base64 to audio blob: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * Convert Float32Array audio to base64 PCM16 format
 * @param audioData - Audio samples
 * @returns Base64 encoded PCM16 data
 */
export function audioToBase64PCM16(audioData: Float32Array | ArrayBuffer): string | null {
    try {
        if (!audioData) return null;

        // Convert to Int16 PCM format
        let int16Data: Int16Array;
        if (audioData instanceof Float32Array) {
            // Convert Float32Array to Int16Array
            int16Data = new Int16Array(audioData.length);
            for (let i = 0; i < audioData.length; i++) {
                // Convert float in range [-1, 1] to int16 in range [-32768, 32767]
                int16Data[i] = Math.max(-32768, Math.min(32767, Math.floor(audioData[i] * 32767)));
            }
        } else if (audioData instanceof ArrayBuffer) {
            // Assume ArrayBuffer is already in Int16 format
            int16Data = new Int16Array(audioData);
        } else {
            throw new Error('Unsupported audio data format');
        }

        // Convert Int16Array to Base64
        const uint8Array = new Uint8Array(int16Data.buffer);
        let binary = '';
        for (let i = 0; i < uint8Array.length; i++) {
            binary += String.fromCharCode(uint8Array[i]);
        }
        return btoa(binary);
    } catch (error) {
        logger.error('Error converting audio to base64 PCM16:', error);
        return null;
    }
}

/**
 * Real-time Streaming Audio Processor
//  * Handles real-time streaming of PCM audio chunks (like Qwen-Omni 方式2)
//  */
export class StreamingAudioProcessor {
    private audioContext: AudioContext | null = null;
    private audioQueue: AudioBuffer[] = [];
    private isPlaying: boolean = false;
    private nextBufferTime: number = 0;
    private logger: any;
    private sampleRate: number;
    private channels: number;
    private bitDepth: number;
    private chunkBuffer: number[] = [];
    private chunkProcessedCount: number = 0;

    constructor(options: {
        sampleRate?: number;
        channels?: number;
        bitDepth?: number;
        logger?: any;
    } = {}) {
        this.sampleRate = options.sampleRate || 24000;  // Qwen-Omni default
        this.channels = options.channels || 1;           // Mono
        this.bitDepth = options.bitDepth || 16;          // 16-bit
        this.logger = options.logger || logger;

        if (typeof window !== 'undefined') {
            this.initWebAudio();
        }
    }

    private async initWebAudio() {
        try {
            this.audioContext = await createAudioContext({
                sampleRate: this.sampleRate,
                numChannels: this.channels,
                bitDepth: this.bitDepth
            });
            this.logger.debug('Web Audio initialized using centralized factory:', {
                sampleRate: this.audioContext.sampleRate,
                channels: this.channels,
                bitDepth: this.bitDepth
            });
        } catch (error) {
            this.logger.error('Failed to initialize Web Audio:', error instanceof Error ? error.message : String(error));
        }
    }

    /**
     * Process a single streaming audio chunk in real-time
     * Following Qwen-Omni 方式2 pattern: 边生成边实时播放
     */
    async processStreamingChunk(base64PCMChunk: string): Promise<void> {
        try {
            if (!base64PCMChunk) return;

            // Reduce logging frequency - only log every 10th chunk to prevent spam
            this.chunkProcessedCount = (this.chunkProcessedCount || 0) + 1;
            if (this.chunkProcessedCount % 10 === 0) {
                this.logger.debug('Processing streaming PCM chunk (batch):', {
                    totalProcessed: this.chunkProcessedCount,
                    chunkLength: base64PCMChunk.length,
                    isPlaying: this.isPlaying
                });
            }

            if (typeof window !== 'undefined' && this.audioContext) {
                // Browser: Use Web Audio API for real-time playback
                await this.playWebAudioChunk(base64PCMChunk);
            } else {
                // Node.js: Would use node-speaker (commented out for browser compatibility)
                this.logger.debug('Node.js environment - streaming audio would use node-speaker');
                // const pcmBuffer = Buffer.from(base64PCMChunk, 'base64');
                // this.speaker.write(pcmBuffer);
            }

        } catch (error) {
            this.logger.error('Error processing streaming chunk:', error);
        }
    }

    private async playWebAudioChunk(base64PCMChunk: string): Promise<void> {
        if (!this.audioContext) return;

        try {
            // Decode PCM data from base64
            const pcmData = atob(base64PCMChunk);
            const pcmBytes = new Uint8Array(pcmData.length);
            for (let i = 0; i < pcmData.length; i++) {
                pcmBytes[i] = pcmData.charCodeAt(i);
            }

            // Convert to 16-bit signed integers
            const int16Data = new Int16Array(pcmBytes.buffer);

            // Convert to Float32Array for Web Audio
            const floatData = new Float32Array(int16Data.length);
            for (let i = 0; i < int16Data.length; i++) {
                floatData[i] = int16Data[i] / (int16Data[i] < 0 ? 0x8000 : 0x7FFF);
            }

            // Create AudioBuffer
            const audioBuffer = this.audioContext.createBuffer(
                this.channels,
                floatData.length / this.channels,
                this.sampleRate
            );

            // Copy data to AudioBuffer
            audioBuffer.getChannelData(0).set(floatData);

            // Schedule playback
            this.scheduleAudioBuffer(audioBuffer);

        } catch (error) {
            this.logger.error('Error playing web audio chunk:', error);
        }
    }

    private scheduleAudioBuffer(audioBuffer: AudioBuffer): void {
        if (!this.audioContext) return;

        const source = this.audioContext.createBufferSource();
        source.buffer = audioBuffer;
        source.connect(this.audioContext.destination);

        // Schedule at the next available time
        const currentTime = this.audioContext.currentTime;
        const scheduledTime = Math.max(currentTime, this.nextBufferTime);

        source.start(scheduledTime);
        this.nextBufferTime = scheduledTime + audioBuffer.duration;

        if (!this.isPlaying) {
            this.isPlaying = true;
            this.logger.debug('Started streaming audio playback');
        }

        // Clean up when audio ends
        source.onended = () => {
            if (this.nextBufferTime <= this.audioContext!.currentTime + 0.1) {
                this.isPlaying = false;
                this.logger.debug('Streaming audio playback ended');
            }
        };
    }

    /**
     * Signal end of streaming
     */
    endStream(): void {
        this.isPlaying = false;
        this.nextBufferTime = 0;
        this.audioQueue = [];
        this.chunkBuffer = [];
        this.logger.debug('Audio streaming ended');
    }

    /**
     * Get streaming status
     */
    getStatus(): { isPlaying: boolean; queueLength: number } {
        return {
            isPlaying: this.isPlaying,
            queueLength: this.audioQueue.length
        };
    }
}

/**
 * Audio Response Processor
 * Handles LangChain/LangGraph audio responses from LLMs
 */
export class AudioResponseProcessor {
    private logger: any;
    private streamingProcessor: StreamingAudioProcessor | null = null;

    constructor(options: { logger?: any } = {}) {
        this.logger = options.logger || logger;
    }

    /**
     * Enable streaming audio processing mode
     * Creates a streaming processor for real-time audio chunk playback
     */
    enableStreaming(options: {
        sampleRate?: number;
        channels?: number;
        bitDepth?: number;
    } = {}): void {
        this.streamingProcessor = new StreamingAudioProcessor({
            sampleRate: options.sampleRate || 24000,
            channels: options.channels || 1,
            bitDepth: options.bitDepth || 16,
            logger: this.logger
        });
        this.logger.info('Streaming audio processing enabled');
    }

    /**
     * Process streaming audio chunk in real-time (Qwen-Omni 方式2)
     * Call this method for each streaming chunk as it arrives
     */
    async processStreamingChunk(base64PCMChunk: string): Promise<void> {
        if (!this.streamingProcessor) {
            this.logger.warn('Streaming not enabled. Call enableStreaming() first.');
            return;
        }

        await this.streamingProcessor.processStreamingChunk(base64PCMChunk);
    }

    /**
     * End streaming audio session
     */
    endStreaming(): void {
        if (this.streamingProcessor) {
            this.streamingProcessor.endStream();
            this.streamingProcessor = null;
            this.logger.info('Streaming audio session ended');
        }
    }

    /**
     * Get streaming status
     */
    getStreamingStatus(): { isStreaming: boolean; isPlaying: boolean; queueLength: number } {
        if (!this.streamingProcessor) {
            return { isStreaming: false, isPlaying: false, queueLength: 0 };
        }

        const status = this.streamingProcessor.getStatus();
        return {
            isStreaming: true,
            isPlaying: status.isPlaying,
            queueLength: status.queueLength
        };
    }

    /**
     * Process LangChain/LangGraph response with audio content
     * Based on OpenAI ChatGPT pattern from docs
     * Now supports both complete audio and streaming audio processing
     */
    async processLLMResponse(response: any, options: {
        enableStreaming?: boolean;
        streamingConfig?: {
            sampleRate?: number;
            channels?: number;
            bitDepth?: number;
        }
    } = {}): Promise<{ audioPlayed: boolean; audioLength?: number; error?: string; isStreaming?: boolean }> {
        try {
            this.logger.debug('Processing LLM response for audio content', {
                hasResponse: !!response,
                responseKeys: response ? Object.keys(response) : null
            });

            let audioData = null;
            let audioFormat = 'wav';

            // Check multiple possible locations for audio data based on LangChain docs
            if (response?.additional_kwargs?.audio) {
                // OpenAI ChatGPT pattern: additional_kwargs.audio
                const audioContent = response.additional_kwargs.audio;
                audioData = audioContent.data;
                this.logger.debug('Found audio in additional_kwargs.audio', {
                    hasData: !!audioData,
                    dataLength: audioData?.length
                });
            } else if (response?.content?.[0]?.type === 'audio') {
                // Content array pattern: content[0] with type audio
                const audioContent = response.content[0];
                audioData = audioContent.data;
                this.logger.debug('Found audio in content[0]', {
                    hasData: !!audioData,
                    dataLength: audioData?.length
                });
            } else if (response?.audio) {
                // Direct audio property
                audioData = response.audio;
                this.logger.debug('Found audio in direct property', {
                    hasData: !!audioData,
                    dataLength: audioData?.length
                });
            } else if (typeof response === 'string' && response.startsWith('UklGR')) {
                // Raw base64 WAV data (starts with 'RIFF' encoded)
                audioData = response;
                this.logger.debug('Response appears to be raw base64 audio data');
            }

            if (audioData) {
                this.logger.info('🔊 Audio content detected in LLM response, playing...');
                await this.playAudioData(audioData, audioFormat);
                return { audioPlayed: true, audioLength: audioData.length };
            } else {
                this.logger.debug('No audio content found in LLM response');
                return { audioPlayed: false };
            }

        } catch (error) {
            this.logger.error('Error processing LLM response audio:', error);
            return { audioPlayed: false, error: error instanceof Error ? error.message : String(error) };
        }
    }

    /**
     * Play base64 audio data with enhanced format detection and error handling
     */
    async playAudioData(base64Audio: string, format: string = 'wav'): Promise<{ success: boolean; duration?: number; simulated?: boolean }> {
        try {
            if (typeof window !== 'undefined' && window.Audio) {
                // Browser environment
                this.logger.debug('Playing audio in browser environment');

                // Detect and log audio format information
                const formatInfo = detectAudioFormatFromBase64(base64Audio);
                this.logger.debug('Audio format analysis:', {
                    originalLength: base64Audio.length,
                    format: formatInfo.format,
                    isComplete: formatInfo.isComplete,
                    needsHeaders: formatInfo.needsHeaders,
                    audioInfo: formatInfo.audioInfo,
                    preview: base64Audio.slice(0, 50) + '...'
                });

                // Create blob using enhanced conversion with format detection
                const blob = base64ToBlobEnhanced(base64Audio, `audio/${format}`);

                if (!blob || blob.size === 0) {
                    throw new Error('Failed to create audio blob from base64 data');
                }

                // Validate the audio blob structure
                const validation = await validateAudioBlob(blob);
                if (!validation.isValid) {
                    this.logger.warn('Audio blob validation failed:', {
                        error: validation.error,
                        blobSize: blob.size,
                        blobType: blob.type
                    });
                    // Continue anyway - some browsers might be more forgiving
                }

                this.logger.debug('Audio blob created and validated:', {
                    size: blob.size,
                    type: blob.type,
                    validation: validation.isValid ? 'passed' : 'failed',
                    audioInfo: validation.audioInfo
                });

                const audioUrl = URL.createObjectURL(blob);

                // Create and play audio element
                const audio = new Audio(audioUrl);

                return new Promise((resolve, reject) => {
                    // Enhanced error handling with specific error codes
                    audio.onerror = (event) => {
                        URL.revokeObjectURL(audioUrl);
                        const error = audio.error;
                        let errorMessage = 'Unknown audio error';
                        let errorCode = 'UNKNOWN';

                        if (error) {
                            switch (error.code) {
                                case error.MEDIA_ERR_ABORTED:
                                    errorMessage = 'Audio playback aborted by user or system';
                                    errorCode = 'ABORTED';
                                    break;
                                case error.MEDIA_ERR_NETWORK:
                                    errorMessage = 'Network error during audio playback';
                                    errorCode = 'NETWORK';
                                    break;
                                case error.MEDIA_ERR_DECODE:
                                    errorMessage = 'Audio decode error - invalid format or corrupted data';
                                    errorCode = 'DECODE';
                                    break;
                                case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
                                    errorMessage = 'Audio format not supported by browser';
                                    errorCode = 'NOT_SUPPORTED';
                                    break;
                                default:
                                    errorMessage = `Audio error code: ${error.code}`;
                                    errorCode = `CODE_${error.code}`;
                            }
                        }

                        this.logger.error('Audio playback failed:', {
                            errorCode,
                            errorMessage,
                            browserError: error?.code,
                            blobSize: blob.size,
                            blobType: blob.type,
                            audioSrc: audio.src,
                            formatInfo,
                            validation: validation.isValid
                        });

                        reject(new Error(`Audio playback failed (${errorCode}): ${errorMessage}`));
                    };

                    audio.onended = () => {
                        URL.revokeObjectURL(audioUrl);
                        this.logger.info('✅ Audio playback completed successfully');
                        resolve({ success: true, duration: audio.duration });
                    };

                    // Enhanced loading event handling
                    audio.onloadeddata = () => {
                        this.logger.debug('Audio loaded successfully:', {
                            duration: audio.duration,
                            readyState: audio.readyState,
                            networkState: audio.networkState
                        });
                    };

                    audio.oncanplaythrough = () => {
                        this.logger.debug('Audio can play through without buffering');
                    };

                    // Start playback with better error handling
                    audio.play().catch((playError) => {
                        URL.revokeObjectURL(audioUrl);
                        this.logger.error('Audio play() method failed:', {
                            error: playError.message,
                            errorName: playError.name,
                            blobInfo: {
                                size: blob.size,
                                type: blob.type
                            },
                            formatInfo
                        });
                        reject(new Error(`Audio play failed: ${playError.message}`));
                    });
                });

            } else {
                // Node.js environment - simulate playback
                this.logger.info('[Node.js] Audio playback simulated', {
                    format,
                    dataLength: base64Audio.length
                });
                return { success: true, simulated: true };
            }
        } catch (error) {
            this.logger.error('Error in audio playback process:', {
                error: error instanceof Error ? error.message : String(error),
                errorType: error instanceof Error ? error.constructor.name : typeof error,
                base64Length: base64Audio?.length || 0
            });
            throw error;
        }
    }
}

/**
 * Create a global audio processor instance
 */
export function createAudioProcessor(options: { logger?: any } = {}): AudioResponseProcessor {
    return new AudioResponseProcessor(options);
}

/**
 * Convert ArrayBuffer to base64 string
 */
export function arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
        binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
}

/**
 * Convert base64 string to ArrayBuffer
 */
export function base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes.buffer;
}

/**
 * Create AudioBuffer from PCM data
 */
export function createAudioBufferFromPCM(pcmData: Uint8Array | Int16Array | Float32Array, sampleRate: number = 24000, channels: number = 1): AudioBuffer {
    if (typeof window === 'undefined' || !window.AudioContext) {
        throw new Error('AudioContext not available');
    }

    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const audioBuffer = audioContext.createBuffer(channels, pcmData.length / channels, sampleRate);

    // Convert PCM data to Float32Array if needed
    let floatData: Float32Array;
    if (pcmData instanceof Int16Array) {
        floatData = new Float32Array(pcmData.length);
        for (let i = 0; i < pcmData.length; i++) {
            floatData[i] = pcmData[i] / 32768.0; // Convert 16-bit to float
        }
    } else if (pcmData instanceof Float32Array) {
        floatData = pcmData;
    } else {
        // Assume raw bytes, convert to 16-bit PCM then to float
        const int16Data = new Int16Array(pcmData.buffer || pcmData);
        floatData = new Float32Array(int16Data.length);
        for (let i = 0; i < int16Data.length; i++) {
            floatData[i] = int16Data[i] / 32768.0;
        }
    }

    audioBuffer.copyToChannel(floatData, 0);
    audioContext.close().catch(console.warn);
    return audioBuffer;
}

/**
 * Process ArrayBuffer audio data and convert to playable blob
 */
export async function processArrayBufferAudio(arrayBuffer: ArrayBuffer, sampleRate: number = 24000, channels: number = 1): Promise<Blob> {
    try {
        logger.debug('Processing ArrayBuffer audio:', {
            size: arrayBuffer.byteLength,
            sampleRate,
            channels
        });

        // Try to decode as audio first (for complete audio files)
        if (typeof window !== 'undefined' && window.AudioContext) {
            const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

            try {
                // Try direct decoding first (for WAV, MP3, etc.)
                const audioBuffer = await audioContext.decodeAudioData(arrayBuffer.slice());
                const wavBlob = convertFloat32ToWav(audioBuffer.getChannelData(0), { sampleRate: audioBuffer.sampleRate, numChannels: audioBuffer.numberOfChannels });
                logger.debug('Successfully decoded as complete audio file');
                audioContext.close().catch(console.warn);
                return wavBlob;
            } catch (decodeError) {
                logger.debug('Direct decode failed, treating as raw PCM:', decodeError);

                // Treat as raw PCM data
                const pcmData = new Uint8Array(arrayBuffer);
                const audioBuffer = createAudioBufferFromPCM(pcmData, sampleRate, channels);
                const wavBlob = convertFloat32ToWav(audioBuffer.getChannelData(0), { sampleRate, numChannels: channels });
                logger.debug('Successfully converted PCM to WAV');
                audioContext.close().catch(console.warn);
                return wavBlob;
            }
        } else {
            throw new Error('AudioContext not available');
        }
    } catch (error) {
        logger.error('Failed to process ArrayBuffer audio:', error);
        throw error;
    }
}

/**
 * Process base64 audio data and convert to playable blob
 */
export async function processBase64Audio(base64Audio: string, sampleRate: number = 24000, channels: number = 1): Promise<Blob> {
    try {
        if (!base64Audio || typeof base64Audio !== 'string') {
            throw new Error('Invalid base64 audio data');
        }

        // Clean base64 string
        let cleanBase64 = base64Audio.trim();
        if (cleanBase64.startsWith('data:audio/')) {
            cleanBase64 = cleanBase64.split(',')[1];
        }
        cleanBase64 = cleanBase64.replace(/\s/g, '');

        if (!cleanBase64) {
            throw new Error('Empty base64 audio data');
        }

        logger.debug('Processing base64 audio:', {
            originalLength: base64Audio.length,
            cleanLength: cleanBase64.length,
            sampleRate,
            channels
        });

        const arrayBuffer = base64ToArrayBuffer(cleanBase64);
        return await processArrayBufferAudio(arrayBuffer, sampleRate, channels);
    } catch (error) {
        logger.error('Failed to process base64 audio:', error);
        throw error;
    }
}

/**
 * Play audio blob in browser
 */
export async function playAudioBlob(blob: Blob): Promise<{ success: boolean; duration?: number; simulated?: boolean }> {
    try {
        if (typeof window === 'undefined' || !window.Audio) {
            logger.warn('Audio playback not available in this environment');
            return { success: false, simulated: true };
        }

        const audio = new Audio();
        const url = URL.createObjectURL(blob);

        return new Promise((resolve, reject) => {
            audio.addEventListener('loadedmetadata', () => {
                logger.debug('Audio loaded successfully:', {
                    duration: audio.duration,
                    readyState: audio.readyState
                });
            });

            audio.addEventListener('ended', () => {
                URL.revokeObjectURL(url);
                resolve({
                    success: true,
                    duration: audio.duration
                });
            });

            audio.addEventListener('error', (e: any) => {
                URL.revokeObjectURL(url);
                reject(new Error(`Audio playback error: ${e.message || 'Unknown error'}`));
            });

            audio.src = url;
            audio.play().catch(reject);
        });
    } catch (error) {
        logger.error('Failed to play audio blob:', error);
        throw error;
    }
}

/**
 * Simple Audio Processor (renamed from SimpleRealtimeAudioProcessor for compatibility)
 * Real-time Audio Queue Processor for streaming audio playback
 * Based on Python reference implementation pattern for immediate audio playback
 * Follows the working implementation from AliyunBailianChatModel
 */
export class AudioProcessor {
    private logger: any;
    private sampleRate: number;
    private channels: number;
    private audioContext: AudioContext | null = null;
    private audioQueue: ArrayBuffer[] = [];
    private isAudioPlaying: boolean = false;

    constructor(options: { logger?: any; sampleRate?: number; channels?: number } = {}) {
        this.logger = options.logger || logger;
        this.sampleRate = options.sampleRate || DEFAULT_AUDIO_CONFIG.sampleRate;
        this.channels = options.channels || DEFAULT_AUDIO_CONFIG.numChannels;
    }

    /**
     * Initialize audio context for playback using centralized factory
     */
    private async initializeAudioContext(): Promise<boolean> {
        if (this.audioContext) {
            return true;
        }

        try {
            this.audioContext = await createAudioContext({
                sampleRate: this.sampleRate,
                numChannels: this.channels
            });

            this.logger.debug('🔊 Audio context initialized for real-time playback using centralized factory');
            return true;
        } catch (error) {
            this.logger.error('❌ Failed to initialize audio context:', error);
            return false;
        }
    }

    /**
     * Process streaming audio delta (base64 PCM16 from Aliyun)
     */
    async processAudioDelta(base64AudioDelta: string): Promise<boolean> {
        try {
            if (!base64AudioDelta) {
                return false;
            }

            // Initialize audio context on first use
            if (!await this.initializeAudioContext()) {
                return false;
            }

            // Decode base64 to binary data (following Python reference: base64.b64decode)
            const binaryString = atob(base64AudioDelta);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }

            // Add to queue and start playback
            this.audioQueue.push(bytes.buffer);
            this.playNextAudio();

            return true;
        } catch (error) {
            this.logger.error('❌ Error processing audio delta:', error);
            return false;
        }
    }

    /**
     * Play next audio in queue (following HTML reference implementation pattern)
     */
    private async playNextAudio(): Promise<void> {
        if (this.isAudioPlaying || this.audioQueue.length === 0 || !this.audioContext) {
            return;
        }

        this.isAudioPlaying = true;

        try {
            while (this.audioQueue.length > 0 && this.audioContext) {
                const audioBuffer = this.audioQueue.shift()!;
                await this.playAudioBuffer(audioBuffer);
            }
        } catch (error) {
            this.logger.error('❌ Error playing audio queue:', error);
        } finally {
            this.isAudioPlaying = false;
        }
    }

    /**
     * Play single audio buffer (following HTML reference implementation)
     */
    private async playAudioBuffer(buffer: ArrayBuffer): Promise<void> {
        return new Promise((resolve, reject) => {
            try {
                if (!this.audioContext) {
                    reject(new Error('No audio context available'));
                    return;
                }

                // Convert ArrayBuffer to Int16Array (PCM16 format from Aliyun)
                const int16 = new Int16Array(buffer);

                // Convert to Float32Array for Web Audio API
                const float32 = new Float32Array(int16.length);
                for (let i = 0; i < int16.length; i++) {
                    float32[i] = int16[i] / 32768; // Convert from 16-bit signed to float (-1.0 to 1.0)
                }

                // Create audio buffer with correct sample rate
                const audioBuffer = this.audioContext.createBuffer(this.channels, float32.length, this.sampleRate);
                audioBuffer.copyToChannel(float32, 0);

                // Create buffer source and play
                const source = this.audioContext.createBufferSource();
                source.buffer = audioBuffer;
                source.connect(this.audioContext.destination);

                source.onended = () => {
                    this.logger.debug('🔊 Audio buffer played successfully');
                    resolve();
                };

                // AudioBufferSourceNode doesn't have onerror, use onended for cleanup
                source.addEventListener('ended', () => {
                    this.logger.debug('🔊 Audio buffer playback ended');
                });

                // Handle potential errors during start()
                try {
                    source.start();
                } catch (error) {
                    this.logger.error('❌ Audio buffer playback failed');
                    reject(new Error('Audio playback failed'));
                    return;
                }
                this.logger.debug('🔊 Playing audio buffer:', {
                    duration: audioBuffer.duration,
                    sampleRate: audioBuffer.sampleRate,
                    length: audioBuffer.length
                });

            } catch (error) {
                this.logger.error('❌ Error in playAudioBuffer:', error);
                reject(error);
            }
        });
    }

    /**
     * Clear audio queue and stop playback
     */
    clearQueue(): void {
        this.audioQueue = [];
        this.isAudioPlaying = false;
    }

    /**
     * Get current queue status
     */
    getStatus(): { queueLength: number; isPlaying: boolean; hasAudioContext: boolean } {
        return {
            queueLength: this.audioQueue.length,
            isPlaying: this.isAudioPlaying,
            hasAudioContext: !!this.audioContext
        };
    }

    /**
     * Process and play audio data (compatibility method for TTS tools)
     * @param audioData - Base64 audio string or LLM response object
     * @returns Processing result with playback status
     */
    async processAndPlay(audioData: string | any): Promise<{
        audioPlayed: boolean;
        audioLength?: number;
        duration?: number;
        simulated?: boolean;
        error?: string;
    }> {
        try {
            if (!audioData) {
                return { audioPlayed: false, error: 'No audio data provided' };
            }

            // Handle string data (base64 audio)
            if (typeof audioData === 'string') {
                // Convert base64 to ArrayBuffer and play through processor
                const binaryString = atob(audioData);
                const bytes = new Uint8Array(binaryString.length);
                for (let i = 0; i < binaryString.length; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                }

                const success = await this.processAudioDelta(audioData);
                return {
                    audioPlayed: success,
                    audioLength: audioData.length,
                    error: success ? undefined : 'Failed to process audio delta'
                };
            }

            // Handle LLM response objects (check various patterns)
            let base64Audio = '';

            if (audioData?.additional_kwargs?.audio?.data) {
                base64Audio = audioData.additional_kwargs.audio.data;
            } else if (audioData?.content?.[0]?.type === 'audio' && audioData.content[0].data) {
                base64Audio = audioData.content[0].data;
            } else if (audioData?.audio) {
                base64Audio = audioData.audio;
            } else if (typeof audioData === 'string' && audioData.startsWith('UklGR')) {
                base64Audio = audioData;
            }

            if (base64Audio) {
                const success = await this.processAudioDelta(base64Audio);
                return {
                    audioPlayed: success,
                    audioLength: base64Audio.length,
                    error: success ? undefined : 'Failed to process LLM response audio'
                };
            }

            return { audioPlayed: false, error: 'No audio content found in input' };

        } catch (error) {
            this.logger.error('❌ Error in processAndPlay:', error);
            return {
                audioPlayed: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }

    /**
     * Cleanup resources
     */
    dispose(): void {
        this.clearQueue();

        if (this.audioContext) {
            this.audioContext.close().catch(() => { });
            this.audioContext = null;
        }
    }
}

/**
 * Create a real-time audio processor for streaming audio (like Aliyun Qwen-Omni)
 */
export function createRealtimeAudioProcessor(options: { logger?: any; sampleRate?: number; channels?: number } = {}): AudioProcessor {
    return new AudioProcessor(options);
}

/**
 * Calculate audio volume from various audio data formats
 * Centralized implementation for consistent volume calculation across the app
 * @param audioData - Audio data in various formats
 * @returns Normalized volume (0-1)
 */
export function calculateAudioVolume(audioData: ArrayBuffer | Float32Array | Uint8Array): number {
    try {
        if (audioData instanceof Float32Array) {
            let sum = 0;
            for (let i = 0; i < audioData.length; i++) {
                sum += Math.abs(audioData[i]);
            }
            return sum / audioData.length;
        } else if (audioData instanceof ArrayBuffer) {
            const float32View = new Float32Array(audioData);
            return calculateAudioVolume(float32View);
        } else if (audioData instanceof Uint8Array) {
            // Convert Uint8Array to normalized float for consistent analysis
            const uint8Array = audioData;
            let sum = 0;
            for (let i = 0; i < uint8Array.length; i++) {
                const sample = uint8Array[i] / 255.0; // Normalize to 0-1
                sum += sample;
            }
            return sum / uint8Array.length;
        }
        return 0;
    } catch (error) {
        console.debug('Error calculating audio volume:', error);
        return 0;
    }
}

/**
 * Estimate audio quality using multiple factors from audio analysis
 * Centralized implementation for consistent quality estimation across the app
 * @param audioData - Audio data to analyze
 * @returns Quality score (0-1)
 */
export function estimateAudioQuality(audioData: ArrayBuffer | Float32Array | Uint8Array): number {
    try {
        // Use comprehensive audio analysis
        const analysis = analyzeAudioDataForQuality(audioData);

        // Quality based on multiple factors from audio analysis
        const volumeScore = Math.min(1, analysis.amplitude * 2);
        const activityScore = analysis.activeSamples / analysis.sampleCount;
        const peakScore = Math.min(1, analysis.peakAmplitude);

        // Weighted quality estimation
        return (volumeScore * 0.4 + activityScore * 0.3 + peakScore * 0.3);
    } catch (error) {
        console.debug('Error estimating audio quality:', error);
        return 0.5;
    }
}

/**
 * Analyze audio data for quality estimation
 * Internal helper that uses the existing analyzeAudioChunk function
 * @param audioData - Audio data to analyze
 * @returns Analysis results
 */
function analyzeAudioDataForQuality(audioData: ArrayBuffer | Float32Array | Uint8Array): {
    amplitude: number;
    peakAmplitude: number;
    hasVoiceActivity: boolean;
    sampleCount: number;
    silentSamples: number;
    activeSamples: number;
    analysis: string;
} {
    try {
        // Convert to Uint8Array for consistent analysis
        let uint8Array: Uint8Array;

        if (audioData instanceof Uint8Array) {
            uint8Array = audioData;
        } else if (audioData instanceof Float32Array) {
            // Convert Float32Array to Uint8Array for consistent analysis
            uint8Array = new Uint8Array(audioData.length);
            for (let i = 0; i < audioData.length; i++) {
                uint8Array[i] = Math.max(0, Math.min(255, (audioData[i] + 1) * 127.5));
            }
        } else if (audioData instanceof ArrayBuffer) {
            uint8Array = new Uint8Array(audioData);
        } else {
            throw new Error('Unsupported audio data format');
        }

        // Use existing analyzeAudioChunk function
        return analyzeAudioChunk(uint8Array, {
            vadThreshold: 0.1
        });

    } catch (error) {
        console.debug('Error in audio analysis:', error);
        return {
            amplitude: 0,
            peakAmplitude: 0,
            hasVoiceActivity: false,
            sampleCount: 0,
            silentSamples: 0,
            activeSamples: 0,
            analysis: 'error'
        };
    }
}

/**
 * Helper function to play base64 audio directly
 * Can be called from anywhere in the codebase
 */
export async function playBase64Audio(base64Audio: string, _format: string = 'wav', options: { logger?: any } = {}): Promise<{ success: boolean; duration?: number; simulated?: boolean }> {
    const processor = new AudioProcessor(options);
    const result = await processor.processAndPlay(base64Audio);
    return {
        success: result.audioPlayed,
        duration: result.duration,
        simulated: result.simulated
    };
}

/**
 * Realtime Audio Manager for managing audio streaming sessions
 * Handles rate limiting, session management, and audio processing for realtime streaming
 */
export class RealtimeAudioManager {
    private logger: any;
    private sampleRate: number;
    private numChannels: number;
    private bitDepth: number;
    private minIntervalMs: number;
    private enableDebugLogging: boolean;

    // Session management
    private sessionState: {
        isActive: boolean;
        isReady: boolean;
        createdAt: number | null;
    } = {
            isActive: false,
            isReady: false,
            createdAt: null
        };

    // Rate limiting and tracking
    public audioRateTracker = {
        lastSendTime: 0,
        totalChunks: 0,
        sessionCreatedAt: null as number | null,
        chunksSentInSession: 0
    };

    // Callback for sending audio data
    public audioSendCallback: ((base64Audio: string) => Promise<boolean>) | null = null;

    // Session readiness timer
    private sessionReadyTimer: NodeJS.Timeout | null = null;

    constructor(options: {
        sampleRate?: number;
        numChannels?: number;
        bitDepth?: number;
        minIntervalMs?: number;
        enableDebugLogging?: boolean;
        logger?: any;
    } = {}) {
        this.sampleRate = options.sampleRate || DEFAULT_AUDIO_CONFIG.sampleRate;
        this.numChannels = options.numChannels || DEFAULT_AUDIO_CONFIG.numChannels;
        this.bitDepth = options.bitDepth || DEFAULT_AUDIO_CONFIG.bitDepth;
        this.minIntervalMs = options.minIntervalMs || DEFAULT_AUDIO_CONFIG.chunkDurationMs || 200;
        this.enableDebugLogging = options.enableDebugLogging || false;
        this.logger = options.logger || logger;

        this.logger.debug('RealtimeAudioManager initialized:', {
            sampleRate: this.sampleRate,
            numChannels: this.numChannels,
            bitDepth: this.bitDepth,
            minIntervalMs: this.minIntervalMs
        });
    }

    /**
     * Set the callback function for sending audio data
     */
    setSendAudioCallback(callback: (base64Audio: string) => Promise<boolean>): void {
        this.audioSendCallback = callback;
        this.logger.debug('Audio send callback set');
    }

    /**
     * Initialize a new audio session
     */
    initSession(): { isActive: boolean; isReady: boolean; createdAt: number } {
        const now = Date.now();
        this.sessionState = {
            isActive: true,
            isReady: false,
            createdAt: now
        };

        this.audioRateTracker.sessionCreatedAt = now;
        this.audioRateTracker.chunksSentInSession = 0;
        this.audioRateTracker.totalChunks = 0;

        // Set session as ready after a short delay (1000ms as per test)
        this.sessionReadyTimer = setTimeout(() => {
            this.sessionState.isReady = true;
            this.logger.debug('Session marked as ready');
        }, 1000);

        this.logger.debug('Audio session initialized:', {
            createdAt: now,
            sessionId: 'default'
        });

        return {
            isActive: this.sessionState.isActive,
            isReady: this.sessionState.isReady,
            createdAt: this.sessionState.createdAt!
        };
    }

    /**
     * Get current session state
     */
    getSessionState(): { isActive: boolean; isReady: boolean; createdAt: number | null } {
        return { ...this.sessionState };
    }

    /**
     * Reset the audio session
     */
    resetSession(): void {
        this.sessionState = {
            isActive: false,
            isReady: false,
            createdAt: null
        };

        this.audioRateTracker = {
            lastSendTime: 0,
            totalChunks: 0,
            sessionCreatedAt: null,
            chunksSentInSession: 0
        };

        if (this.sessionReadyTimer) {
            clearTimeout(this.sessionReadyTimer);
            this.sessionReadyTimer = null;
        }

        this.logger.debug('Audio session reset');
    }

    /**
     * Process and send audio data with rate limiting and session management
     */
    async processAndSendAudio(audioData: ArrayBuffer | Float32Array | Int16Array | string): Promise<boolean> {
        try {
            // Check if callback is set
            if (!this.audioSendCallback) {
                this.logger.warn('No audio send callback set');
                return false;
            }

            // Check if session is ready
            if (!this.sessionState.isReady) {
                this.logger.debug('Session not ready, skipping audio chunk');
                return false;
            }

            const now = Date.now();

            // Calculate required delay
            let requiredDelay = 0;

            // Add extra delay for first chunk after session creation
            if (this.audioRateTracker.chunksSentInSession === 0 && this.audioRateTracker.sessionCreatedAt) {
                const timeSinceSessionCreated = now - this.audioRateTracker.sessionCreatedAt;
                const minFirstChunkDelay = 1000; // 1 second extra delay for first chunk
                if (timeSinceSessionCreated < minFirstChunkDelay) {
                    requiredDelay = Math.max(requiredDelay, minFirstChunkDelay - timeSinceSessionCreated);
                }
            }

            // Regular rate limiting
            const timeSinceLastSend = now - this.audioRateTracker.lastSendTime;
            if (timeSinceLastSend < this.minIntervalMs) {
                requiredDelay = Math.max(requiredDelay, this.minIntervalMs - timeSinceLastSend);
            }

            // Apply delay if needed
            if (requiredDelay > 0) {
                await new Promise(resolve => setTimeout(resolve, requiredDelay));
            }

            // Process audio data to base64
            const result = await processRealtimeAudio(audioData, {
                sampleRate: this.sampleRate,
                numChannels: this.numChannels,
                bitDepth: this.bitDepth,
                enableDebugLogging: this.enableDebugLogging
            });

            if (!result.success || !result.base64Audio) {
                this.logger.error('Failed to process audio data:', result.error);
                return false;
            }

            // Send audio data
            const sendSuccess = await this.audioSendCallback(result.base64Audio);

            if (sendSuccess) {
                this.audioRateTracker.lastSendTime = Date.now();
                this.audioRateTracker.totalChunks++;
                this.audioRateTracker.chunksSentInSession++;

                if (this.enableDebugLogging) {
                    this.logger.debug('Audio chunk sent successfully:', {
                        chunkNumber: this.audioRateTracker.totalChunks,
                        sessionChunk: this.audioRateTracker.chunksSentInSession,
                        base64Length: result.base64Audio.length
                    });
                }
            }

            return sendSuccess;

        } catch (error) {
            this.logger.error('Error processing and sending audio:', error);
            return false;
        }
    }

    /**
     * Start realtime audio processing with a media stream
     */
    async startRealtime(mediaStream: MediaStream): Promise<void> {
        if (!mediaStream) {
            throw new Error('MediaStream is required for realtime audio processing');
        }

        const audioTracks = mediaStream.getAudioTracks();
        if (audioTracks.length === 0) {
            throw new Error('No audio tracks found in MediaStream');
        }

        // Initialize session if not already active
        if (!this.sessionState.isActive) {
            this.initSession();
        }

        this.logger.debug('Started realtime audio processing with MediaStream');
    }

    /**
     * Stop realtime audio processing
     */
    stopRealtime(): void {
        this.resetSession();
        this.logger.debug('Stopped realtime audio processing');
    }

    /**
     * Check if realtime audio processing is active
     */
    isActive(): boolean {
        return this.sessionState.isActive;
    }

    /**
     * Dispose of resources
     */
    dispose(): void {
        this.resetSession();
        this.audioSendCallback = null;

        if (this.sessionReadyTimer) {
            clearTimeout(this.sessionReadyTimer);
            this.sessionReadyTimer = null;
        }

        this.logger.debug('RealtimeAudioManager disposed');
    }
}

/**
 * Create a streaming audio processor for real-time audio playback
 * Following Qwen-Omni 方式2 pattern: 边生成边实时播放
 */
export function createStreamingAudioProcessor(options: {
    sampleRate?: number;
    channels?: number;
    bitDepth?: number;
    logger?: any;
} = {}): StreamingAudioProcessor {
    return new StreamingAudioProcessor(options);
}

/**
 * Calculate average amplitude of audio data for debugging VAD issues
 * @param {Uint8Array} audioData - PCM16 audio data
 * @returns {number} Average amplitude (0.0 to 1.0)
 */
export function calculateAudioAmplitude(audioData: Uint8Array): number {
    if (!audioData || audioData.length === 0) return 0;

    let sum = 0;
    // PCM16 data is 2 bytes per sample
    for (let i = 0; i < audioData.length - 1; i += 2) {
        // Convert PCM16 to signed integer (little-endian)
        const sample = (audioData[i] | (audioData[i + 1] << 8));
        const signed = sample > 32767 ? sample - 65536 : sample;
        sum += Math.abs(signed);
    }

    const avgAmplitude = sum / (audioData.length / 2);
    return avgAmplitude / 32768; // Normalize to 0.0-1.0
}

/**
 * Analyze audio chunk for debugging VAD and speech detection issues
 * @param {Uint8Array} audioData - PCM16 audio data  
 * @param {Object} options - Analysis options
 * @returns {Object} Analysis results including amplitude, peak detection, etc.
 */
export function analyzeAudioChunk(audioData: Uint8Array, options: {
    vadThreshold?: number;
    logger?: any;
} = {}): {
    amplitude: number;
    peakAmplitude: number;
    hasVoiceActivity: boolean;
    sampleCount: number;
    silentSamples: number;
    activeSamples: number;
    analysis: string;
} {
    const { vadThreshold = 0.3, logger: loggerOverride = logger } = options;

    if (!audioData || audioData.length === 0) {
        return {
            amplitude: 0,
            peakAmplitude: 0,
            hasVoiceActivity: false,
            sampleCount: 0,
            silentSamples: 0,
            activeSamples: 0,
            analysis: 'No audio data'
        };
    }

    let sum = 0;
    let peak = 0;
    let activeSamples = 0;
    const sampleCount = Math.floor(audioData.length / 2);

    // Analyze PCM16 samples
    for (let i = 0; i < audioData.length - 1; i += 2) {
        // Convert PCM16 to signed integer (little-endian)
        const sample = (audioData[i] | (audioData[i + 1] << 8));
        const signed = sample > 32767 ? sample - 65536 : sample;
        const normalized = Math.abs(signed) / 32768;

        sum += normalized;
        peak = Math.max(peak, normalized);

        if (normalized > vadThreshold) {
            activeSamples++;
        }
    }

    const amplitude = sum / sampleCount;
    const silentSamples = sampleCount - activeSamples;
    const activePercentage = (activeSamples / sampleCount) * 100;
    const hasVoiceActivity = activePercentage > 5; // Require at least 5% active samples

    let analysis = '';
    if (amplitude < 0.01) {
        analysis = 'Very quiet/silent';
    } else if (amplitude < 0.1) {
        analysis = 'Low volume - may not trigger VAD';
    } else if (amplitude < 0.3) {
        analysis = 'Moderate volume - should trigger VAD';
    } else {
        analysis = 'Good volume - should definitely trigger VAD';
    }

    if (loggerOverride) {
        loggerOverride.debug('🎤 [AudioAnalysis]', {
            amplitude: amplitude.toFixed(4),
            peakAmplitude: peak.toFixed(4),
            sampleCount,
            activeSamples,
            activePercentage: activePercentage.toFixed(1) + '%',
            hasVoiceActivity,
            analysis,
            vadThreshold
        });
    }

    return {
        amplitude,
        peakAmplitude: peak,
        hasVoiceActivity,
        sampleCount,
        silentSamples,
        activeSamples,
        analysis
    };
}

/**
 * Helper function to process streaming audio chunks in real-time
 * Usage example:
 * 
 * const processor = createStreamingAudioProcessor({ sampleRate: 24000 });
 * 
 * // For each streaming chunk from LLM (like Qwen-Omni):
 * for await (const chunk of completion) {
 *     if (chunk.choices[0].delta.audio?.data) {
 *         await processStreamingAudioChunk(processor, chunk.choices[0].delta.audio.data);
 *     }
 * }
 * 
 * processor.endStream();
 */
export async function processStreamingAudioChunk(
    processor: StreamingAudioProcessor,
    base64PCMChunk: string
): Promise<void> {
    return await processor.processStreamingChunk(base64PCMChunk);
}

/**
 * AudioWorkletProcessor Interface (TypeScript-only)
 * The actual AudioWorkletProcessor implementation is in the createAudioProcessorScript() function below.
 * This interface is only for type safety and is not executed in the main thread.
 */
export interface AudioCaptureProcessorInterface {
    targetSamples: number;
    minIntervalMs: number;
    numChannels: number;
    audioBuffer: Float32Array;
    bufferPosition: number;
    lastSendTime: number;
    processCount: number;
}


/**
 * Create and configure AudioContext with optimal settings
 * Centralized factory to avoid duplicated AudioContext setup logic
 */
export async function createAudioContext(config: Partial<AudioConfig> = {}): Promise<AudioContext> {
    const audioConfig = { ...DEFAULT_AUDIO_CONFIG, ...config };

    if (typeof window === 'undefined') {
        throw new Error('AudioContext not available in non-browser environment');
    }

    if (!window.AudioContext && !(window as any).webkitAudioContext) {
        throw new Error('AudioContext not supported by this browser');
    }

    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: audioConfig.sampleRate
    });

    // Resume AudioContext if suspended (autoplay policy)
    if (audioContext.state === 'suspended') {
        await audioContext.resume();
    }

    logger.debug('🔊 AudioContext created and configured', {
        sampleRate: audioContext.sampleRate,
        state: audioContext.state,
        baseLatency: audioContext.baseLatency
    });

    return audioContext;
}

/**
 * Create MediaStreamAudioSourceNode from MediaStream
 * Centralized factory to ensure consistent setup
 */
export function createMediaStreamSource(
    audioContext: AudioContext,
    mediaStream: MediaStream
): MediaStreamAudioSourceNode {
    if (!mediaStream) {
        throw new Error('MediaStream is required');
    }

    const audioTracks = mediaStream.getAudioTracks();
    if (audioTracks.length === 0) {
        throw new Error('No audio tracks found in MediaStream');
    }

    const audioSource = audioContext.createMediaStreamSource(mediaStream);

    logger.debug('🎤 MediaStreamAudioSourceNode created', {
        audioTracks: audioTracks.length,
        trackLabel: audioTracks[0]?.label || 'Unknown'
    });

    return audioSource;
}

/**
 * Create the AudioWorklet script content as a string
 * This allows the processor to be loaded dynamically without a separate file
 */
export function createAudioProcessorScript(): string {
    return `
/**
 * AudioWorkletProcessor for MediaCaptureManager
 * Replaces deprecated ScriptProcessorNode with modern AudioWorkletNode
 */

class AudioCaptureProcessor extends AudioWorkletProcessor {
    constructor(options) {
        super();
        
        // Get configuration from options
        const { processorOptions } = options;
        this.targetSamples = processorOptions?.targetSamples || 1600;  // Default for 3200 bytes = 1600 samples
        this.minIntervalMs = processorOptions?.minIntervalMs || 200;
        this.numChannels = processorOptions?.numChannels || 1;
        
        // Buffer accumulation for collecting multiple 128-sample blocks
        this.audioBuffer = new Float32Array(this.targetSamples);
        this.bufferPosition = 0;
        
        // Rate limiting
        this.lastSendTime = 0;
        
        // Debug logging
        this.processCount = 0;
        
        console.log('[AudioCaptureProcessor] Initialized with config:', {
            targetSamples: this.targetSamples,
            minIntervalMs: this.minIntervalMs,
            numChannels: this.numChannels
        });
    }
    
    process(inputs, outputs, parameters) {
        const input = inputs[0];
        
        // Check if we have input
        if (!input || input.length === 0) {
            return true; // Keep processor alive
        }
        
        const inputChannel = input[0]; // First channel
        if (!inputChannel || inputChannel.length === 0) {
            return true;
        }
        
        // Accumulate audio samples in buffer (Web Audio processes 128-sample blocks)
        const samplesToCopy = Math.min(inputChannel.length, this.targetSamples - this.bufferPosition);
        
        // Copy samples to accumulation buffer
        for (let i = 0; i < samplesToCopy; i++) {
            this.audioBuffer[this.bufferPosition + i] = inputChannel[i];
        }
        this.bufferPosition += samplesToCopy;
        
        // Check if we've accumulated enough samples and rate limiting allows sending
        const now = currentTime * 1000; // Convert to milliseconds
        const timeSinceLastSend = now - this.lastSendTime;
        const bufferFull = this.bufferPosition >= this.targetSamples;
        const rateLimitAllows = timeSinceLastSend >= this.minIntervalMs;
        
        if (bufferFull && rateLimitAllows) {
            try {
                // Create a copy of the accumulated audio data
                const audioData = new Float32Array(this.bufferPosition);
                for (let i = 0; i < this.bufferPosition; i++) {
                    audioData[i] = this.audioBuffer[i];
                }
                
                // Send to main thread via MessagePort
                this.port.postMessage({
                    type: 'audioData',
                    data: audioData,
                    timestamp: now,
                    sampleLength: this.bufferPosition
                });
                
                // Reset buffer for next accumulation
                this.bufferPosition = 0;
                this.lastSendTime = now;
                this.processCount++;
                
                // Periodic debug logging
                if (this.processCount % 25 === 0) { // Every ~5 seconds
                    console.log(\`[AudioCaptureProcessor] Processed \${this.processCount} chunks, last chunk: \${audioData.length} samples\`);
                }
            } catch (error) {
                console.error('[AudioCaptureProcessor] Error processing audio:', error);
                this.port.postMessage({
                    type: 'error',
                    error: error.message
                });
            }
        }
        
        return true; // Keep processor alive
    }
    
    static get parameterDescriptors() {
        return [];
    }
}

// Register the processor
registerProcessor('audio-capture-processor', AudioCaptureProcessor);
`;
}

/**
 * Send audio data to realtime API (centralized audio utility)
 * Generic method that delegates to the model's realtime audio functionality
 * @param {any} model - The model instance with realtime audio support
 * @param {ArrayBuffer|Uint8Array|string} audioData - Audio data
 * @param {string} format - Audio format ('arraybuffer', 'uint8array', 'base64')
 * @param {any} logger - Logger instance
 * @returns {Promise<boolean>} Success status
 */
/**
 * CONSOLIDATED AUDIO ANALYSIS (from audioAnalysis.ts)
 * Merged for better organization and reduced file count
 */

// Audio analysis result interface (unified from audioAnalysis.ts)
export interface AudioAnalysisResult {
    volume: number;
    hasSignificantAudio?: boolean;
    quality: 'good' | 'silent' | 'error' | string | number; // Support both string and number for backward compatibility
    sentiment?: 'neutral' | 'positive' | 'negative' | string;
    vadState?: 'speaking' | 'silent';
    timestamp?: number;
    // Additional properties from audioAnalysis.ts for compatibility
    sampleRate?: number;
    channels?: number;
}

// Audio processing options (unified from audioAnalysis.ts)
export interface AudioProcessingOptions {
    // Original audioAnalysis.ts options
    enableAnalysis?: boolean;
    enableQualityAnalysis?: boolean;
    enableVolumeAnalysis?: boolean;
    sampleRate?: number;
    channels?: number;
    bufferSize?: number;
    qualityThreshold?: number;
    // Existing audio.ts options
    volumeThreshold?: number;
    significantAudioThreshold?: number;
    maxSampleThreshold?: number;
    enableSentimentAnalysis?: boolean;
}

// Default audio processing options (unified)
export const DEFAULT_AUDIO_PROCESSING_OPTIONS: AudioProcessingOptions = {
    // From audioAnalysis.ts
    enableAnalysis: true,
    enableQualityAnalysis: true,
    enableVolumeAnalysis: true,
    sampleRate: 16000,
    channels: 1,
    bufferSize: 4096,
    qualityThreshold: 0.7,
    // From audio.ts
    volumeThreshold: 0.01,
    significantAudioThreshold: 0.01,
    maxSampleThreshold: 1000,
    enableSentimentAnalysis: true
};

/**
 * Extract audio analysis for contextual decision making
 * CONSOLIDATED from audioAnalysis.ts for better organization
 * Supports both async and sync usage patterns for backward compatibility
 */
export function extractAudioAnalysis(
    audioData: ArrayBuffer | Float32Array | Uint8Array,
    format: string = 'float32',
    options: AudioProcessingOptions = {}
): AudioAnalysisResult {
    const opts = { ...DEFAULT_AUDIO_PROCESSING_OPTIONS, ...options };

    try {
        let volume = 0;
        let hasSignificantAudio = false;

        // Route to appropriate analysis based on data type (from audioAnalysis.ts pattern)
        if (audioData instanceof Float32Array) {
            volume = calculateAudioVolume(audioData);
            const quality = estimateAudioQuality(audioData);
            hasSignificantAudio = volume > (opts.volumeThreshold || 0.01);

            return {
                volume,
                quality: typeof quality === 'number' ? (quality > (opts.qualityThreshold || 0.7) ? 'good' : 'silent') : quality,
                sampleRate: opts.sampleRate || 16000,
                channels: opts.channels || 1,
                hasSignificantAudio,
                sentiment: opts.enableSentimentAnalysis ? determineSentiment(volume, hasSignificantAudio) : 'neutral',
                vadState: hasSignificantAudio ? 'speaking' : 'silent',
                timestamp: Date.now()
            };
        }

        // Analyze audio data based on format (original audio.ts logic)
        if (format === 'pcm16' && audioData instanceof ArrayBuffer) {
            const analysis = analyzePCM16Audio(audioData, opts);
            volume = analysis.volume;
            hasSignificantAudio = analysis.hasSignificantAudio;

        } else if (format === 'float32' && audioData instanceof Float32Array) {
            const analysis = analyzeFloat32Audio(audioData, opts);
            volume = analysis.volume;
            hasSignificantAudio = analysis.hasSignificantAudio;

        } else if (audioData instanceof Uint8Array || audioData instanceof ArrayBuffer) {
            const analysis = analyzeUint8Audio(audioData, opts);
            volume = analysis.volume;
            hasSignificantAudio = analysis.hasSignificantAudio;

        } else {
            console.warn('Unsupported audio format:', format, typeof audioData);
            return createErrorAudioResult(opts);
        }

        // Determine quality based on audio characteristics
        const quality = hasSignificantAudio ? 'good' : 'silent';

        // Simple sentiment analysis
        const sentiment = opts.enableSentimentAnalysis
            ? determineSentiment(volume, hasSignificantAudio)
            : 'neutral';

        // VAD state based on audio activity
        const vadState = hasSignificantAudio ? 'speaking' : 'silent';

        return {
            volume,
            hasSignificantAudio,
            quality,
            sentiment,
            vadState,
            timestamp: Date.now(),
            // Include audioAnalysis.ts compatibility fields
            sampleRate: opts.sampleRate || 16000,
            channels: opts.channels || 1
        };
    } catch (error) {
        console.debug('Error analyzing audio:', error);
        return createErrorAudioResult(opts);
    }
}

/**
 * Analyze PCM16 audio data
 */
function analyzePCM16Audio(audioData: ArrayBuffer, options: AudioProcessingOptions) {
    const samples = new Int16Array(audioData);
    let sum = 0;
    let maxSample = 0;

    for (let i = 0; i < samples.length; i++) {
        const abs = Math.abs(samples[i]);
        sum += abs;
        if (abs > maxSample) maxSample = abs;
    }

    const volume = sum / samples.length / 32768; // Normalize to 0-1
    const hasSignificantAudio = volume > (options.volumeThreshold ?? 0.01) || maxSample > (options.maxSampleThreshold ?? 1000);

    return { volume, hasSignificantAudio };
}

/**
 * Analyze Float32 audio data
 */
function analyzeFloat32Audio(audioData: Float32Array, options: AudioProcessingOptions) {
    let sum = 0;
    let maxSample = 0;

    for (let i = 0; i < audioData.length; i++) {
        const abs = Math.abs(audioData[i]);
        sum += abs;
        if (abs > maxSample) maxSample = abs;
    }

    const volume = sum / audioData.length;
    const hasSignificantAudio = volume > (options.volumeThreshold ?? 0.01) || maxSample > 0.1;

    return { volume, hasSignificantAudio };
}

/**
 * Analyze Uint8 audio data
 */
function analyzeUint8Audio(audioData: ArrayBuffer | Uint8Array, options: AudioProcessingOptions) {
    const samples = new Uint8Array(audioData);
    let sum = 0;
    let maxSample = 0;

    for (let i = 0; i < samples.length; i++) {
        const abs = Math.abs(samples[i] - 128);
        sum += abs;
        if (abs > maxSample) maxSample = abs;
    }

    const volume = sum / samples.length / 128;
    const hasSignificantAudio = volume > (options.volumeThreshold ?? 0.01) || maxSample > 50;

    return { volume, hasSignificantAudio };
}

/**
 * Determine sentiment from audio characteristics
 */
function determineSentiment(volume: number, hasSignificantAudio: boolean): 'neutral' | 'positive' | 'negative' {
    if (!hasSignificantAudio) return 'neutral';

    // Simple heuristic - could be enhanced with ML models
    if (volume > 0.3) return 'positive';
    if (volume < 0.1) return 'negative';
    return 'neutral';
}

/**
 * Create error result for failed analysis
 */
function createErrorAudioResult(options: AudioProcessingOptions = {}): AudioAnalysisResult {
    return {
        volume: 0,
        hasSignificantAudio: false,
        quality: 'error',
        sentiment: 'neutral',
        vadState: 'silent',
        timestamp: Date.now(),
        sampleRate: options.sampleRate || 16000,
        channels: options.channels || 1
    };
}

/**
 * Handle audio data with contextual analysis
 * CONSOLIDATED from audioAnalysis.ts
 */
export async function handleAudioData(
    audioData: ArrayBuffer | Float32Array | Uint8Array,
    format: string,
    options: {
        sendToWebSocket?: (data: any, format: string) => Promise<boolean>;
        updateContext?: (type: string, contextData: any) => void;
        logger?: any;
    } = {}
): Promise<AudioAnalysisResult | null> {
    try {
        const { sendToWebSocket, updateContext, logger } = options;

        // Send audio to WebSocket immediately if handler provided
        if (sendToWebSocket) {
            const audioSent = await sendToWebSocket(audioData, format);
            if (!audioSent && logger) {
                logger.debug('⚠️ Failed to send audio to WebSocket');
            }
        }

        // Extract audio analysis for context bridge
        const audioAnalysis = extractAudioAnalysis(audioData, format);

        // Update dual-brain context bridge if handler provided and audio is significant
        if (updateContext && audioAnalysis.hasSignificantAudio) {
            updateContext('audio', {
                volume: audioAnalysis.volume,
                quality: audioAnalysis.quality,
                sentiment: audioAnalysis.sentiment,
                vadState: audioAnalysis.vadState,
                hasAudio: true,
                audioFormat: format,
                timestamp: audioAnalysis.timestamp
            });
        }

        return audioAnalysis;

    } catch (error) {
        if (options.logger) {
            options.logger.debug('❌ Error handling audio data:', error);
        }
        return null;
    }
}

/**
 * Create audio processing configuration for MediaCaptureManager
 * CONSOLIDATED from audioAnalysis.ts
 */
export function createAudioProcessingConfig(options: {
    audioHandler?: (audioData: any, format: string) => Promise<void>;
    contextUpdater?: (type: string, data: any) => void;
    webSocketSender?: (data: any, format: string) => Promise<boolean>;
    logger?: any;
} = {}) {
    return {
        onAudioData: async (audioData: any, format: string) => {
            const result = await handleAudioData(audioData, format, {
                sendToWebSocket: options.webSocketSender,
                updateContext: options.contextUpdater,
                logger: options.logger
            });

            // Call custom handler if provided
            if (options.audioHandler) {
                await options.audioHandler(audioData, format);
            }

            return result;
        }
    };
}

// END CONSOLIDATED AUDIO ANALYSIS

export async function sendRealtimeAudio(
    model: any,
    audioData: ArrayBuffer | Uint8Array | string,
    format: string = 'arraybuffer',
    logger?: any
): Promise<boolean> {
    try {
        // Check if model supports realtime audio
        if (!model || typeof model.sendRealtimeAudio !== 'function') {
            if (logger) {
                logger.warn('⚠️ Model does not support realtime audio');
            }
            return false;
        }

        // Send audio through the model
        return await model.sendRealtimeAudio(audioData, format);
    } catch (error) {
        if (logger) {
            logger.error('❌ Error sending realtime audio:', error);
        }
        return false;
    }
}

/**
 * Wait for realtime session to be ready with configurable timeout
 * Centralized session readiness checking for any realtime provider
 * @param {any} model - The model instance with realtime support
 * @param {Object} options - Configuration options
 * @param {number} options.timeoutMs - Maximum wait time in milliseconds (default: 10000)
 * @param {number} options.checkIntervalMs - Check interval in milliseconds (default: 500)
 * @param {Function} options.onProgress - Progress callback for debugging
 * @param {any} options.logger - Logger instance
 * @returns {Promise<boolean>} True if session is ready, false if timeout
 */
export async function waitForRealtimeSessionReady(
    model: any,
    options: {
        timeoutMs?: number;
        checkIntervalMs?: number;
        onProgress?: (progress: any) => void;
        logger?: any;
    } = {}
): Promise<boolean> {
    const {
        timeoutMs = 10000,
        checkIntervalMs = 500,
        onProgress = null,
        logger
    } = options;

    // Check if model has realtime mode capability
    if (!model || typeof model.isRealtimeModeActive !== 'function') {
        if (logger) {
            logger.debug('ℹ️ Model does not support realtime mode');
        }
        return true; // Not a realtime model, consider it "ready"
    }

    // If realtime mode is not active, return immediately
    if (!model.isRealtimeModeActive()) {
        if (logger) {
            logger.debug('ℹ️ Realtime mode not active, no need to wait for session');
        }
        return true;
    }

    if (logger) {
        logger.debug('⏳ Waiting for realtime session to be ready...', {
            timeoutMs,
            checkIntervalMs,
            provider: model?.constructor?.name || 'Unknown'
        });
    }

    const maxAttempts = Math.ceil(timeoutMs / checkIntervalMs);
    let attempts = 0;

    while (attempts < maxAttempts) {
        const isReady = model.isRealtimeSessionReady ? model.isRealtimeSessionReady() : model.isRealtimeModeActive();

        if (isReady) {
            if (logger) {
                logger.debug('✅ Realtime session is ready', {
                    attemptsUsed: attempts + 1,
                    timeElapsed: (attempts + 1) * checkIntervalMs
                });
            }
            return true;
        }

        // Call progress callback if provided
        if (onProgress) {
            onProgress({
                attempt: attempts + 1,
                maxAttempts,
                timeElapsed: (attempts + 1) * checkIntervalMs,
                timeRemaining: timeoutMs - ((attempts + 1) * checkIntervalMs),
                isReady: false
            });
        }

        if (logger) {
            logger.debug(`🔄 Session not ready yet (attempt ${attempts + 1}/${maxAttempts})`, {
                hasModel: !!model,
                hasRealtimeClient: !!model?.realtimeClient,
                isSessionReady: isReady
            });
        }

        await new Promise(resolve => setTimeout(resolve, checkIntervalMs));
        attempts++;
    }

    if (logger) {
        logger.warn('⚠️ Timeout waiting for realtime session to be ready', {
            attemptsUsed: attempts,
            timeoutMs,
            provider: model?.constructor?.name || 'Unknown'
        });
        logger.warn('🔧 This may indicate a connection issue. Check if the realtime WebSocket connection is working properly.');
    }

    return false;
}

/**
 * Handle generic audio data processing with analysis
 * Provides consistent interface for different audio formats
 * CONSOLIDATED from audioAnalysis.ts
 */
export async function processAudioDataWithAnalysis(
    audioData: any,
    format: string,
    options: AudioProcessingOptions = {}
): Promise<AudioAnalysisResult> {
    const config = { ...DEFAULT_AUDIO_PROCESSING_OPTIONS, ...options };

    try {
        // Simple analysis for generic audio data
        return {
            volume: 0.1,
            quality: format === 'float32' ? 'good' : 'unknown',
            sampleRate: config.sampleRate || 16000,
            channels: config.channels || 1,
            hasSignificantAudio: true
        };
    } catch (error) {
        console.warn('Error handling audio data:', error);
        return createErrorAudioResult(config);
    }
}

/**
 * Advanced Audio Analysis for System2 Integration
 * Moved from System2Interface for better separation of concerns
 */

/**
 * Assess background noise levels from audio analysis
 * @param analysis - Analysis data containing audio context
 * @returns Background noise assessment
 */
export function assessBackgroundNoise(analysis: any): {
    level: string;
    confidence: number;
    volume: number;
    quality: string;
    timestamp: number;
} {
    try {
        const audioContext = analysis?.contextualFeatures?.audio || analysis?.audioContext || {};
        const volume = audioContext.volume || 0;
        const quality = audioContext.quality || 'unknown';

        // Assess noise level based on volume and quality indicators
        let noiseLevel = 'low';
        let confidence = 0.7;

        if (quality === 'noisy' || volume > 0.8) {
            noiseLevel = 'high';
            confidence = 0.9;
        } else if (quality === 'muffled' || (volume > 0.3 && volume < 0.6)) {
            noiseLevel = 'medium';
            confidence = 0.8;
        } else if (quality === 'clear' && volume < 0.2) {
            noiseLevel = 'low';
            confidence = 0.85;
        }

        return {
            level: noiseLevel,
            confidence,
            volume,
            quality,
            timestamp: Date.now()
        };
    } catch (error) {
        console.warn('Error assessing background noise:', error);
        return {
            level: 'unknown',
            confidence: 0.3,
            volume: 0,
            quality: 'unknown',
            timestamp: Date.now()
        };
    }
}

/**
 * Calculate silence duration from context data
 * @param contextData - Context data containing audio and conversation info
 * @returns Silence duration in milliseconds
 */
export function calculateSilenceDuration(contextData: any): number {
    try {
        const audioContext = contextData?.audioContext || contextData?.audio || {};
        const conversationState = contextData?.conversationState || contextData?.conversation || {};

        // Check VAD activity for current silence
        const vadActivity = audioContext.vadActivity || {};
        if (vadActivity.isActive) {
            return 0; // Currently speaking, no silence
        }

        // Calculate silence based on pause duration or last interaction
        let silenceDuration = 0;

        if (audioContext.pauseDuration) {
            silenceDuration = audioContext.pauseDuration;
        } else if (conversationState.lastUserMessage?.timestamp) {
            silenceDuration = Date.now() - conversationState.lastUserMessage.timestamp;
        } else if (conversationState.lastInteractionTime) {
            silenceDuration = Date.now() - conversationState.lastInteractionTime;
        } else {
            // Default assumption for unknown silence
            silenceDuration = audioContext.volume < 0.1 ? 2000 : 0;
        }

        // Cap silence duration at reasonable maximum (e.g., 30 seconds)
        return Math.min(silenceDuration, 30000);

    } catch (error) {
        console.warn('Error calculating silence duration:', error);
        return 1000; // Default 1 second if calculation fails
    }
}

/**
 * Calculate engagement level from audio and context data
 * @param contextData - Context data containing engagement indicators
 * @returns Engagement level from 0.0 to 1.0
 */
export function calculateEngagementLevel(contextData: any): number {
    try {
        const audioContext = contextData?.audioContext || contextData?.audio || {};
        const visualContext = contextData?.visualContext || contextData?.visual || {};
        const conversationState = contextData?.conversationState || contextData?.conversation || {};

        let engagementScore = 0.5; // Base engagement level

        // Audio engagement indicators
        if (audioContext.vadActivity?.isActive) {
            engagementScore += 0.2;
        }
        if (audioContext.volume > 0.3) {
            engagementScore += 0.1;
        }

        // Visual engagement indicators (if available)
        if (visualContext?.faceDetected) {
            engagementScore += 0.1;
        }
        if (visualContext?.attention?.eyeContact > 0.5) {
            engagementScore += 0.2;
        }

        // Conversation engagement
        if (conversationState?.turnCount > 0) {
            engagementScore += 0.1;
        }

        // Recent activity engagement
        const lastInteractionAge = conversationState?.lastInteractionTime
            ? Date.now() - conversationState.lastInteractionTime
            : Infinity;

        if (lastInteractionAge < 30000) { // Less than 30 seconds
            engagementScore += 0.1;
        } else if (lastInteractionAge > 120000) { // More than 2 minutes
            engagementScore -= 0.2;
        }

        return Math.max(0, Math.min(1.0, engagementScore));

    } catch (error) {
        console.warn('Error calculating engagement level:', error);
        return 0.5; // Default moderate engagement
    }
}

// ================================================================
// END OF CONSOLIDATED AUDIO ANALYSIS
// ================================================================
