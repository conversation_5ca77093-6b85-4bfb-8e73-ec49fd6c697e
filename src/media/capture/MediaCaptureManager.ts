/**
 * MediaCaptureManager - Device Access & Capture Operations Only
 * 
 * Focused responsibility: Device access, stream management, and raw capture operations
 * Processing delegated to modality services (audio.ts, video.ts)
 * State management handled by MediaStateManager
 * 
 * Core capabilities:
 * - Media device access and stream initialization
 * - Basic capture coordination
 * - Integration with audio/video modality services
 */

import { createLogger, LogLevel, setModuleLogLevel } from '@/utils/logger';

// Set logging level to INFO to show capture events while reducing noise
setModuleLogLevel('MediaCaptureManager', LogLevel.INFO);

// Import centralized camera permission manager
import { mediaPermissionManager } from '../MediaPermissionManager.js';

// Import modality services
import { VideoElementManager, FrameInfo } from '../modality/video.js';
import { AudioProcessor, RealtimeAudioManager, DEFAULT_AUDIO_CONFIG, AudioConfig } from '../modality/audio.js';

// Type definitions
export type MediaType = 'audio' | 'video' | 'audio-video';
export type AudioOutputFormat = 'pcm16' | 'float32' | 'base64' | 'wav';

export type MediaCaptureOptions = {
  audio?: MediaStreamConstraints['audio'];
  video?: MediaStreamConstraints['video'];
  onCaptureStart?: () => void;
  onCaptureStop?: () => void;
  onCaptureError?: (error: Error) => void;
  onFrame?: (frameInfo: FrameInfo) => void;
  maxFrames?: number;
  captureRateMs?: number;
  frameQuality?: number;
  onAudioData?: (audioData: ArrayBuffer | Float32Array | string, format: AudioOutputFormat) => void;
  // Audio configuration - use AudioConfig from audio.ts
  audioConfig?: Partial<AudioConfig>;
  audioOutputFormat?: AudioOutputFormat;
  // Deprecated - use audioConfig instead
  targetSampleRate?: number;
  targetBitDepth?: number;
  targetChannels?: number;
  chunkSize?: number;
  minIntervalMs?: number;
  enableDebugLogging?: boolean;
  [key: string]: any;
};

/**
 * MediaCaptureManager
 * Manages media capture with delegation to modality services
 */
export class MediaCaptureManager {
  // Logger
  private logger = createLogger('MediaCaptureManager');

  // Device and stream management (not processing)
  private mediaStream: MediaStream | null = null;
  private currentMediaType: MediaType = 'audio-video';

  // Operational state (not user activation state)
  private streamActive: boolean = false;
  private processingActive: boolean = false;

  // Modality services
  private videoElementManager: VideoElementManager | null = null;
  private audioProcessor: AudioProcessor | null = null;
  private realtimeAudioManager: RealtimeAudioManager | null = null;

  // Configuration
  private options: MediaCaptureOptions;
  private audioConfig: AudioConfig;
  private audioOutputFormat: AudioOutputFormat;

  // Direct StreamingManager integration
  private streamingManager?: any;

  /**
   * Create a new MediaCaptureManager
   * @param options Configuration options
   */
  constructor(options: MediaCaptureOptions = {}) {
    // Store options directly
    this.options = {
      audio: options.audio || true,
      video: options.video || false,
      maxFrames: options.maxFrames || 10,
      captureRateMs: options.captureRateMs || 500,
      frameQuality: options.frameQuality || 0.8,
      ...options
    };

    // Set up audio configuration using centralized AudioConfig
    this.audioConfig = {
      ...DEFAULT_AUDIO_CONFIG,
      ...options.audioConfig,
      // Support legacy options for backward compatibility
      ...(options.targetSampleRate && { sampleRate: options.targetSampleRate }),
      ...(options.targetBitDepth && { bitDepth: options.targetBitDepth }),
      ...(options.targetChannels && { numChannels: options.targetChannels }),
      ...(options.minIntervalMs && { chunkDurationMs: options.minIntervalMs })
    };

    // Set audio output format
    this.audioOutputFormat = options.audioOutputFormat || 'pcm16';

    this.logger.debug('MediaCaptureManager initialized with modality delegation:', {
      audio: !!this.options.audio,
      video: !!this.options.video,
      audioConfig: this.audioConfig,
      audioOutputFormat: this.audioOutputFormat
    });
  }

  /**
   * Set or update the audio data callback for realtime streaming
   * @param callback Audio data callback function
   * @param format Audio output format to use
   */
  setAudioCallback(callback: (audioData: ArrayBuffer | Float32Array | string, format: AudioOutputFormat) => void, format: AudioOutputFormat = 'pcm16'): void {
    this.audioOutputFormat = format;

    if (this.realtimeAudioManager) {
      // Set callback on audio manager to send processed audio
      this.realtimeAudioManager.setSendAudioCallback(async (base64Audio: string) => {
        try {
          // Convert base64 back to desired format for callback
          if (format === 'base64') {
            callback(base64Audio, format);
          } else if (format === 'pcm16') {
            const binaryString = atob(base64Audio);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
              bytes[i] = binaryString.charCodeAt(i);
            }
            callback(bytes.buffer, format);
          } else if (format === 'float32') {
            // Convert base64 PCM to Float32Array
            const binaryString = atob(base64Audio);
            const int16Data = new Int16Array(binaryString.length / 2);
            for (let i = 0; i < int16Data.length; i++) {
              const byte1 = binaryString.charCodeAt(i * 2);
              const byte2 = binaryString.charCodeAt(i * 2 + 1);
              int16Data[i] = byte1 | (byte2 << 8);
            }
            const float32Data = new Float32Array(int16Data.length);
            for (let i = 0; i < int16Data.length; i++) {
              float32Data[i] = int16Data[i] / (int16Data[i] < 0 ? 0x8000 : 0x7FFF);
            }
            callback(float32Data, format);
          }
          return true;
        } catch (error) {
          this.logger.error('Error in audio callback:', error);
          return false;
        }
      });
    }

    this.logger.debug(`Audio callback set with format: ${format}`);
  }

  /**
   * Set StreamingManager for direct LangGraph integration
   * This replaces the MediaInputBridge pattern for better performance
   */
  setStreamingManager(streamingManager: any): void {
    this.streamingManager = streamingManager;
    this.logger.info('StreamingManager connected for direct LangGraph integration');
  }

  /**
   * Start media capture operations
   * Note: User activation validation is handled by MediaStateManager
   * This method focuses purely on device access and capture setup
   * @param mediaType Type of media to capture ('audio', 'video', 'audio-video')
   * @returns Promise<boolean> Success status
   */
  async startCapture(mediaType: MediaType = 'audio-video', userActivated: boolean = true): Promise<boolean> {
    // Enforce explicit user activation as per privacy controls
    if (!userActivated) {
      this.logger.info('[MediaCaptureManager] Capture not started - requires explicit user activation');
      return false;
    }
    if (this.streamActive) {
      console.log('[MediaCaptureManager] Stream already active, skipping startCapture');
      return true;
    }

    try {
      console.log('[MediaCaptureManager] Starting capture for mediaType:', mediaType);
      this.currentMediaType = mediaType;

      // Initialize media devices
      const initialized = await this._initializeMediaDevices(mediaType);
      if (!initialized) {
        throw new Error(`Failed to initialize ${mediaType} devices`);
      }

      // Initialize modality services
      await this._initializeModalityServices(mediaType);

      this.streamActive = true;
      this.options.onCaptureStart?.();

      console.log('[MediaCaptureManager] Capture started successfully');
      return true;

    } catch (error) {
      console.error('[MediaCaptureManager] Error starting capture:', error);
      this.options.onCaptureError?.(error instanceof Error ? error : new Error(String(error)));
      return false;
    }
  }

  /**
   * Initialize modality services based on media type
   */
  private async _initializeModalityServices(mediaType: MediaType): Promise<void> {
    // Initialize video processing if needed
    if (mediaType === 'video' || mediaType === 'audio-video') {
      if (!this.videoElementManager) {
        this.videoElementManager = new VideoElementManager(this.logger);
      }

      if (this.mediaStream) {
        await this.videoElementManager.setupVideoElements(this.mediaStream);

        if (this.options.onFrame) {
          this.videoElementManager.startFrameCapture({
            maxFrames: this.options.maxFrames || 10,
            captureRateMs: this.options.captureRateMs || 500,
            frameQuality: this.options.frameQuality || 0.8,
            onFrame: this.options.onFrame
          });
        }
      }
    }

    // Initialize audio processing if needed
    if (mediaType === 'audio' || mediaType === 'audio-video') {
      if (!this.audioProcessor) {
        this.audioProcessor = new AudioProcessor({
          logger: this.logger,
          sampleRate: this.audioConfig.sampleRate,
          channels: this.audioConfig.numChannels
        });
      }

      if (!this.realtimeAudioManager) {
        this.realtimeAudioManager = new RealtimeAudioManager({
          logger: this.logger,
          sampleRate: this.audioConfig.sampleRate,
          numChannels: this.audioConfig.numChannels,
          bitDepth: this.audioConfig.bitDepth,
          minIntervalMs: this.audioConfig.chunkDurationMs || 200,
          enableDebugLogging: this.options.enableDebugLogging || false
        });

        // Initialize session
        this.realtimeAudioManager.initSession();

        // Start realtime processing with the media stream
        if (this.mediaStream && this.options.onAudioData) {
          await this.realtimeAudioManager.startRealtime(this.mediaStream);
          // Set up the callback to handle processed audio
          this.setAudioCallback(this.options.onAudioData, this.audioOutputFormat);
        }
      }
    }
  }

  /**
   * Initialize the MediaCaptureManager
   * This method is expected by CameraManager and other components
   * @param mediaType Type of media to initialize ('audio', 'video', 'audio-video')
   * @returns Promise<boolean> Success status
   */
  async initialize(mediaType: MediaType = 'audio-video'): Promise<boolean> {
    this.logger.info(`[MediaCaptureManager] Initializing for ${mediaType}...`);

    try {
      // Initialize media devices but don't start capture yet
      const initialized = await this._initializeMediaDevices(mediaType);

      if (initialized) {
        this.logger.info(`[MediaCaptureManager] Successfully initialized for ${mediaType}`);
        return true;
      } else {
        this.logger.warn(`[MediaCaptureManager] Failed to initialize for ${mediaType}`);
        return false;
      }
    } catch (error) {
      this.logger.error(`[MediaCaptureManager] Error during initialization:`, error);
      return false;
    }
  }

  /**
   * Initialize media devices (private helper)
   * Uses centralized camera permission manager to prevent multiple requests
   * @param mediaType Type of media to initialize
   * @returns Success status
   */
  private async _initializeMediaDevices(mediaType: MediaType): Promise<boolean> {
    try {
      // For video-related media types, use centralized media permission manager
      if (mediaType === 'video' || mediaType === 'audio-video') {
        console.log('[MediaCaptureManager] Using centralized media permission manager for media access');

        const permissionResult = await mediaPermissionManager.requestMediaAccess(
          'MediaCaptureManager',
          {
            video: this.options.video,
            audio: mediaType === 'audio-video' ? !!this.options.audio : false,
            mediaType: mediaType === 'video' ? 'camera' : 'both',
            requestReason: `Media capture needs ${mediaType} access for streaming and processing.`
          }
        );

        if (permissionResult.success && permissionResult.stream) {
          this.mediaStream = permissionResult.stream;
          console.log('[MediaCaptureManager] Successfully obtained stream from media permission manager');
        } else {
          console.warn('[MediaCaptureManager] Media permission manager failed:', permissionResult.error);

          if (mediaType === 'audio-video') {
            return this._attemptAudioOnlyFallback();
          }
          return false;
        }
      } else {
        // For audio-only, use direct getUserMedia
        const audioConstraints: MediaStreamConstraints = {
          audio: this.options.audio || {
            sampleRate: this.audioConfig.sampleRate,
            channelCount: this.audioConfig.numChannels,
            echoCancellation: true,
            noiseSuppression: true
          }
        };

        console.log('[MediaCaptureManager] Requesting audio-only with constraints:', audioConstraints);
        this.mediaStream = await navigator.mediaDevices.getUserMedia(audioConstraints);
      }

      // Validate that we got the expected tracks
      if (mediaType !== 'video' && this.mediaStream.getAudioTracks().length === 0) {
        throw new Error('No audio track available in media stream');
      }

      if (mediaType !== 'audio' && this.mediaStream.getVideoTracks().length === 0) {
        throw new Error('No video track available in media stream');
      }

      console.log('[MediaCaptureManager] Media devices initialized successfully', {
        audioTracks: this.mediaStream.getAudioTracks().length,
        videoTracks: this.mediaStream.getVideoTracks().length
      });

      return true;
    } catch (error) {
      console.error('[MediaCaptureManager] Error initializing media devices:', error);

      if (mediaType === 'audio-video' && error instanceof Error && error.name === 'NotAllowedError') {
        return this._attemptAudioOnlyFallback();
      }

      return false;
    }
  }

  /**
   * Attempt audio-only fallback when video permission is denied
   * @private
   */
  private async _attemptAudioOnlyFallback(): Promise<boolean> {
    console.warn('[MediaCaptureManager] Video permission denied, attempting audio-only fallback...');

    try {
      const audioConstraints: MediaStreamConstraints = {
        audio: this.options.audio || {
          sampleRate: this.audioConfig.sampleRate,
          channelCount: this.audioConfig.numChannels,
          echoCancellation: true,
          noiseSuppression: true
        }
      };

      console.log('[MediaCaptureManager] Requesting audio-only with constraints:', audioConstraints);
      this.mediaStream = await navigator.mediaDevices.getUserMedia(audioConstraints);

      if (this.mediaStream.getAudioTracks().length === 0) {
        throw new Error('No audio track available in fallback stream');
      }

      console.log('[MediaCaptureManager] Audio-only fallback successful', {
        audioTracks: this.mediaStream.getAudioTracks().length,
        videoTracks: 0
      });

      this.currentMediaType = 'audio';
      return true;
    } catch (fallbackError) {
      console.error('[MediaCaptureManager] Audio-only fallback also failed:', fallbackError);
      return false;
    }
  }

  /**
   * Setup audio streaming with configurable format - delegated to RealtimeAudioManager
   */
  private async _setupAudioStreaming(): Promise<void> {
    this.logger.debug(`Setting up audio streaming (format: ${this.audioOutputFormat})`);

    if (this.realtimeAudioManager && this.mediaStream) {
      await this.realtimeAudioManager.startRealtime(this.mediaStream);
      this.processingActive = true;
      this.logger.debug('Audio streaming delegated to RealtimeAudioManager');
    } else {
      throw new Error('RealtimeAudioManager not initialized or no media stream');
    }
  }





  /**
   * Process audio chunk - delegated to RealtimeAudioManager
   */
  private async _processAudioChunk(audioData: Float32Array): Promise<void> {
    if (this.realtimeAudioManager) {
      // Delegate to RealtimeAudioManager for processing
      // The callback is already set via setAudioCallback method
      this.logger.debug('Audio chunk processing delegated to RealtimeAudioManager');
    } else {
      this.logger.warn('No RealtimeAudioManager available for audio processing');
    }
  }

  /**
   * Convert processed audio to LangGraph-compatible format
   * This removes the need for MediaInputBridge intermediary
   */
  private _convertToLangGraphFormat(audioData: Float32Array, format: AudioOutputFormat) {
    return {
      content: {
        audio: audioData,
        metadata: {
          format,
          sampleRate: this.audioConfig.sampleRate,
          channels: this.audioConfig.numChannels,
          timestamp: Date.now()
        }
      },
      streamingContext: {
        inputType: 'audio',
        timestamp: Date.now(),
        metadata: {
          captureManager: 'MediaCaptureManager',
          audioFormat: format,
          directLangGraphIntegration: true
        }
      }
    };
  }
  /**
   * Stop audio capture specifically
   */
  stopAudioCapture(): void {
    if (!this.streamActive || this.currentMediaType === 'video') {
      console.log('[MediaCaptureManager] No active audio stream, skipping stopAudioCapture');
      return;
    }

    console.log('[MediaCaptureManager] Stopping audio capture...');

    try {
      // Stop audio streaming
      this._stopAudioStreaming();

      // If we were only capturing audio, stop the full stream
      if (this.currentMediaType === 'audio') {
        this.streamActive = false;
        this.options.onCaptureStop?.();
      }

      console.log('[MediaCaptureManager] Audio capture stopped successfully');
    } catch (error) {
      console.error('[MediaCaptureManager] Error stopping audio capture:', error);
      this.options.onCaptureError?.(error instanceof Error ? error : new Error(String(error)));
    }
  }


  /**
   * Stop capturing media
   */
  stopCapture(): void {
    if (!this.streamActive) {
      console.log('[MediaCaptureManager] No active stream, skipping stopCapture');
      return;
    }

    console.log('[MediaCaptureManager] Stopping capture...');

    try {
      // Stop audio streaming first
      this._stopAudioStreaming();

      // Stop frame capture if active
      this._stopFrameCapture();

      this.streamActive = false;
      this.options.onCaptureStop?.();

      console.log('[MediaCaptureManager] Capture stopped successfully');
    } catch (error) {
      console.error('[MediaCaptureManager] Error stopping capture:', error);
      this.options.onCaptureError?.(error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Stop audio streaming - delegated to modality services
   */
  private _stopAudioStreaming(): void {
    this.logger.debug(`Stopping audio streaming (${this.audioOutputFormat})...`);

    this.processingActive = false;

    // Delegate to RealtimeAudioManager
    if (this.realtimeAudioManager) {
      this.realtimeAudioManager.stopRealtime();
      this.logger.debug('Audio streaming stop delegated to RealtimeAudioManager');
    }

    this.logger.debug(`Audio streaming stopped (${this.audioOutputFormat})`);
  }


  /**
   * Get the audio track from the media stream
   * @returns Audio track or null if not available
   */
  getAudioTrack(): MediaStreamTrack | null {
    if (!this.mediaStream) return null;

    const audioTracks = this.mediaStream.getAudioTracks();
    return audioTracks.length > 0 ? audioTracks[0] : null;
  }

  /**
   * Get the video track from the media stream
   * @returns Video track or null if not available
   */
  getVideoTrack(): MediaStreamTrack | null {
    if (!this.mediaStream) return null;

    const videoTracks = this.mediaStream.getVideoTracks();
    return videoTracks.length > 0 ? videoTracks[0] : null;
  }

  /**
   * Get the media stream
   * @returns Media stream or null if not available
   */
  getMediaStream(): MediaStream | null {
    return this.mediaStream;
  }

  /**
   * Check if audio streaming is active - delegates to RealtimeAudioManager
   * @returns Boolean indicating if audio is being streamed in configured format
   */
  isAudioStreamingActive(): boolean {
    return !!(this.realtimeAudioManager?.isActive()) && !!this.mediaStream && this.mediaStream.getAudioTracks().length > 0;
  }

  /**
   * Check if stream is active (replaces isCapturing for state clarity)
   */
  isStreamActive(): boolean {
    return this.streamActive;
  }

  /**
   * Check if processing is active
   */
  isProcessingActive(): boolean {
    return this.processingActive;
  }

  /**
   * Get the video element - delegated to VideoElementManager
   */
  getVideoElement(): HTMLVideoElement | null {
    return this.videoElementManager?.getVideoElement() || null;
  }

  /**
   * Create a video preview element - delegated to VideoElementManager
   */
  createVideoPreview(container: HTMLElement): HTMLVideoElement | null {
    if (!this.videoElementManager) {
      this.logger.error('Cannot create preview: VideoElementManager not initialized');
      return null;
    }
    return this.videoElementManager.createVideoPreview(container);
  }

  /**
   * Capture a single frame - delegated to VideoElementManager
   */
  async captureFrame(): Promise<string | null> {
    if (!this.videoElementManager) {
      this.logger.warn('Cannot capture frame: VideoElementManager not initialized');
      return null;
    }
    return this.videoElementManager.captureFrame(this.options.frameQuality || 0.8);
  }

  /**
   * Clean up resources - delegates to modality services
   */
  dispose(): void {
    try {
      this.logger.debug('Disposing MediaCaptureManager resources');

      // Stop capturing if active
      this.stopCapture();

      // Stop all tracks
      if (this.mediaStream) {
        this.mediaStream.getTracks().forEach(track => {
          try {
            track.stop();
          } catch (error) {
            this.logger.warn('Error stopping track:', error);
          }
        });
        this.mediaStream = null;
      }

      // Dispose modality services
      if (this.videoElementManager) {
        this.videoElementManager.dispose();
        this.videoElementManager = null;
      }

      if (this.realtimeAudioManager) {
        this.realtimeAudioManager.dispose?.();
        this.realtimeAudioManager = null;
      }

      if (this.audioProcessor) {
        this.audioProcessor.dispose?.();
        this.audioProcessor = null;
      }

      this.logger.debug('MediaCaptureManager resources disposed successfully');
    } catch (error) {
      this.logger.error('Error during MediaCaptureManager disposal:', error);
    }
  }


  /**
   * Stop frame capture - delegated to VideoElementManager
   */
  private _stopFrameCapture(): void {
    if (this.videoElementManager) {
      this.videoElementManager.stopFrameCapture();
      this.logger.debug('Frame capture stop delegated to VideoElementManager');
    }
  }

  /**
   * Start camera capture specifically (convenience method for CameraManager)
   * @returns Promise<boolean> Success status
   */
  async startCameraCapture(): Promise<boolean> {
    this.logger.info('[MediaCaptureManager] Starting camera capture...');
    return await this.startCapture('video');
  }

  /**
   * Stop camera capture specifically (convenience method for CameraManager)
   */
  stopCameraCapture(): void {
    if (this.currentMediaType === 'video' || this.currentMediaType === 'audio-video') {
      this.logger.info('[MediaCaptureManager] Stopping camera capture...');
      this.stopCapture();
    }
  }

  /**
   * Check if camera is currently active
   * @returns Boolean indicating if camera is active
   */
  isCameraActive(): boolean {
    return this.streamActive &&
      (this.currentMediaType === 'video' || this.currentMediaType === 'audio-video') &&
      !!this.mediaStream &&
      this.mediaStream.getVideoTracks().length > 0;
  }

}
