# Media Utilities

This directory contains shared utilities for media processing across the application.

## Audio Utilities

The `audioUtils.js` file provides a comprehensive set of utilities for audio processing:

### Audio Format Detection

```javascript
import { AudioFormat, detectAudioFormat } from './audioUtils.js';

// Detect audio format from header bytes
const format = detectAudioFormat(headerBytes);
if (format === AudioFormat.MP3) {
  console.log('MP3 format detected');
} else if (format === AudioFormat.WAV) {
  console.log('WAV format detected');
}
```

### Audio Conversion

```javascript
import { convertFloat32ToWav, base64ToBlob, blobToBase64 } from './audioUtils.js';

// Convert Float32Array to WAV
const wavBlob = convertFloat32ToWav(audioSamples, {
  sampleRate: 16000,
  numChannels: 1,
  bitDepth: 16
});

// Convert base64 to Blob
const audioBlob = base64ToBlob(base64Audio, 'audio/wav');

// Convert Blob to base64
const base64 = await blobToBase64(audioBlob);
```

### Audio Processing

```javascript
import { createAudioFromBlob, playAudioWithPromise, checkAudioBuffer } from './audioUtils.js';

// Create an Audio element from a blob
const audio = await createAudioFromBlob(audioBlob);

// Play audio with promise
await playAudioWithPromise(audio);

// Check if an AudioBuffer contains actual audio data
const { hasAudio, maxValue } = checkAudioBuffer(audioBuffer);
if (!hasAudio) {
  console.warn('Silent audio detected');
}
```

### Fallback Audio Generation

```javascript
import { createFallbackTone, splitAudioIntoChunks } from './audioUtils.js';

// Create a fallback tone
const toneBuffer = createFallbackTone(1.0, 22050);

// Split audio into chunks
const chunks = splitAudioIntoChunks(audio, 800);
```

## Video Utilities

The video utilities have been moved to `src/media/modality/video.ts`:

```typescript
import { extractFrames, extractFramesInBrowser, extractFramesInNode } from '../modality/video';

// Extract frames from a video (auto-detects environment)
const frames = await extractFrames(videoData, 2); // 2 frames per second

// Extract frames in browser environment
const browserFrames = await extractFramesInBrowser(videoBlob, 2);

// Extract frames in Node.js environment
const nodeFrames = await extractFramesInNode(videoBuffer, 2);
```

The video utilities automatically detect the environment (browser or Node.js) and use the appropriate implementation:

- In the browser: Uses HTML5 video and canvas elements
- In Node.js: Uses ffmpeg via child_process

### Consolidated Implementation

This is a shared implementation used by multiple components:

- `src/server/algorithms/vllm-llm.ts` - For processing video inputs to LLMs
- `src/media/processing/VideoProcessor.ts` - For general video processing

By using the shared implementation, we ensure consistent behavior and reduce code duplication.

## Media Utilities

The `mediaUtils.ts` file provides utilities for general media processing:

```typescript
import { formatMediaForVLLM } from './mediaUtils';

// Format media data for vLLM
const formattedData = await formatMediaForVLLM(mediaData, 'audio');
```

## Benefits of Consolidated Utilities

1. **Environment Agnostic**: Works in both browser and server environments
2. **Reduced Code Duplication**: Common functionality is shared across the application
3. **Consistent Behavior**: All components use the same implementation for media processing
4. **Easier Maintenance**: Updates and fixes only need to be applied in one place
5. **Better Performance**: Optimized implementations can be shared across the application
6. **Improved Testing**: Centralized utilities are easier to test thoroughly
