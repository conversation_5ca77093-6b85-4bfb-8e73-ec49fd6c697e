/**
 * Shared utility functions for media processing
 * 
 * NOTE: Audio conversion functions have been moved to src/media/modality/audio.ts
 * This file now re-exports them to maintain backward compatibility while 
 * encouraging migration to the new modality structure.
 */

// Import from the new modality structure - these are the canonical implementations
import {
  base64ToBlob as modalityBase64ToBlob,
  blobToBase64 as modalityBlobToBase64
} from '../modality/audio.js';

// Re-export for backward compatibility
export const base64ToBlob = modalityBase64ToBlob;
export const blobToBase64 = modalityBlobToBase64;

/**
 * Format media data for vLLM
 * @param mediaData Media data as Blob, ArrayBuffer, or base64 string
 * @param mediaType Media type ('audio' or 'video')
 * @returns Formatted data URL
 */
export async function formatMediaForVLLM(mediaData: Blob | ArrayBuffer | string, mediaType: string): Promise<string> {
  // If it's already a string, assume it's a base64 string
  if (typeof mediaData === 'string') {
    // If it's already a data URL, return it
    if (mediaData.startsWith('data:')) {
      return mediaData;
    }
    // Otherwise, format it as a data URL
    return `data:${mediaType === 'audio' ? 'audio/wav' : 'image/jpeg'};base64,${mediaData}`;
  }

  // If it's a Blob, convert to base64
  if (mediaData instanceof Blob) {
    const base64 = await blobToBase64(mediaData);
    return `data:${mediaType === 'audio' ? 'audio/wav' : 'image/jpeg'};base64,${base64}`;
  }

  // If it's an ArrayBuffer, convert to base64
  const blob = new Blob([mediaData], {
    type: mediaType === 'audio' ? 'audio/wav' : 'image/jpeg'
  });
  const base64 = await blobToBase64(blob);
  return `data:${mediaType === 'audio' ? 'audio/wav' : 'image/jpeg'};base64,${base64}`;
}
