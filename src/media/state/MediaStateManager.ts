/**
 * Unified Media State Manager
 * 
 * Consolidates audio and video state management with proper user activation validation.
 * Single source of truth for all media capture states and transitions.
 */

import { createLogger, LogLevel } from '@/utils/logger';
import { MediaCaptureManager, MediaType } from '../capture/MediaCaptureManager';

export interface MediaActivationState {
  active: boolean;
  userActivated: boolean;
  processing: boolean;
  lastActivation: number | null;
  lastDeactivation: number | null;
}

export interface MediaState {
  audio: MediaActivationState;
  video: MediaActivationState;
  initialized: boolean;
  sessionId: string;
}

export interface MediaStateOptions {
  logger?: any;
  sessionTimeoutMs?: number;
}

export type MediaTypeKey = 'audio' | 'video';

/**
 * Unified Media State Manager
 * Manages all audio/video activation states with proper user consent validation
 */
export class MediaStateManager {
  private logger: any;
  private options: MediaStateOptions;
  private mediaState: MediaState;
  private mediaCaptureManager: MediaCaptureManager | null = null;
  
  // Event callbacks
  private onStateChange?: (mediaType: MediaType<PERSON>ey, state: MediaActivationState) => void;
  private onActivationError?: (mediaType: MediaTypeKey, error: Error) => void;

  constructor(options: MediaStateOptions = {}) {
    this.logger = options.logger || createLogger('MediaStateManager');
    this.logger.setLogLevel(LogLevel.DEBUG);
    this.options = options;

    // Initialize default media state
    this.mediaState = {
      audio: {
        active: false,
        userActivated: false,
        processing: false,
        lastActivation: null,
        lastDeactivation: null
      },
      video: {
        active: false,
        userActivated: false,
        processing: false,
        lastActivation: null,
        lastDeactivation: null
      },
      initialized: false,
      sessionId: `media_session_${Date.now()}`
    };

    this.logger.info('🎯 MediaStateManager initialized', {
      sessionId: this.mediaState.sessionId
    });
  }

  /**
   * Initialize the MediaStateManager with MediaCaptureManager
   */
  async initialize(mediaCaptureManager?: MediaCaptureManager): Promise<boolean> {
    try {
      this.logger.info('🚀 Initializing MediaStateManager...');

      if (mediaCaptureManager) {
        this.mediaCaptureManager = mediaCaptureManager;
      } else {
        // Create default MediaCaptureManager if not provided
        this.mediaCaptureManager = new MediaCaptureManager({
          audio: true,
          video: true,
          onCaptureStart: () => this.handleCaptureStart(),
          onCaptureStop: () => this.handleCaptureStop(),
          onCaptureError: (error) => this.handleCaptureError(error)
        });
      }

      this.mediaState.initialized = true;
      this.logger.info('✅ MediaStateManager initialized successfully');
      return true;

    } catch (error) {
      this.logger.error('❌ Failed to initialize MediaStateManager:', error);
      return false;
    }
  }

  /**
   * Unified method to set media state (audio or video)
   * This replaces separate startListening/stopListening, startVideoStreaming/stopVideoStreaming
   */
  async setMediaState(
    mediaType: MediaTypeKey, 
    active: boolean, 
    userActivated: boolean = true
  ): Promise<boolean> {
    try {
      const currentState = this.mediaState[mediaType];
      
      this.logger.info(`🎯 Setting ${mediaType} state`, {
        mediaType,
        active,
        userActivated,
        currentlyActive: currentState.active,
        sessionId: this.mediaState.sessionId
      });

      // Validate user activation for starting media
      if (active && !userActivated) {
        const error = new Error(`${mediaType} activation requires explicit user interaction`);
        this.logger.error(`❌ ${mediaType} activation denied - no user interaction`, {
          mediaType,
          userActivated,
          sessionId: this.mediaState.sessionId
        });
        this.onActivationError?.(mediaType, error);
        return false;
      }

      // No change needed
      if (currentState.active === active) {
        this.logger.debug(`📍 ${mediaType} already in requested state:`, active);
        return true;
      }

      // Execute state change
      const success = active 
        ? await this.startMedia(mediaType, userActivated)
        : await this.stopMedia(mediaType);

      if (success) {
        // Update state
        const timestamp = Date.now();
        this.mediaState[mediaType] = {
          ...this.mediaState[mediaType],
          active,
          userActivated: active ? userActivated : false,
          processing: active,
          ...(active ? { lastActivation: timestamp } : { lastDeactivation: timestamp })
        };

        // Notify listeners
        this.onStateChange?.(mediaType, this.mediaState[mediaType]);

        this.logger.info(`✅ ${mediaType} state changed successfully`, {
          mediaType,
          active,
          timestamp,
          sessionId: this.mediaState.sessionId
        });
      }

      return success;

    } catch (error) {
      this.logger.error(`❌ Error setting ${mediaType} state:`, error);
      this.onActivationError?.(mediaType, error instanceof Error ? error : new Error(String(error)));
      return false;
    }
  }

  /**
   * Start media capture (audio or video)
   */
  private async startMedia(mediaType: MediaTypeKey, userActivated: boolean): Promise<boolean> {
    if (!this.mediaCaptureManager) {
      throw new Error('MediaCaptureManager not initialized');
    }

    try {
      this.logger.info(`🎬 Starting ${mediaType} capture with user activation:`, userActivated);

      // Convert to MediaType for MediaCaptureManager
      const captureType: MediaType = mediaType === 'audio' ? 'audio' : 
                                    mediaType === 'video' ? 'video' : 'audio-video';

      // Start capture with proper user activation validation
      const success = await this.mediaCaptureManager.startCapture(captureType);

      if (success) {
        this.logger.info(`✅ ${mediaType} capture started successfully`);
      } else {
        this.logger.warn(`⚠️ ${mediaType} capture failed to start`);
      }

      return success;

    } catch (error) {
      this.logger.error(`❌ Error starting ${mediaType} capture:`, error);
      throw error;
    }
  }

  /**
   * Stop media capture (audio or video)
   */
  private async stopMedia(mediaType: MediaTypeKey): Promise<boolean> {
    if (!this.mediaCaptureManager) {
      throw new Error('MediaCaptureManager not initialized');
    }

    try {
      this.logger.info(`🛑 Stopping ${mediaType} capture`);

      // For audio-only or video-only, we can stop the specific type
      // For now, we'll stop all capture (can be optimized later)
      this.mediaCaptureManager.stopCapture();

      this.logger.info(`✅ ${mediaType} capture stopped successfully`);
      return true;

    } catch (error) {
      this.logger.error(`❌ Error stopping ${mediaType} capture:`, error);
      return false;
    }
  }

  /**
   * Get current media state
   */
  getMediaState(): MediaState {
    return { ...this.mediaState };
  }

  /**
   * Get state for specific media type
   */
  getMediaTypeState(mediaType: MediaTypeKey): MediaActivationState {
    return { ...this.mediaState[mediaType] };
  }

  /**
   * Check if user activation is valid for media type
   */
  isUserActivated(mediaType: MediaTypeKey): boolean {
    return this.mediaState[mediaType].userActivated && this.mediaState[mediaType].active;
  }

  /**
   * Check if media type is currently active
   */
  isActive(mediaType: MediaTypeKey): boolean {
    return this.mediaState[mediaType].active;
  }

  /**
   * Check if any media is currently active
   */
  isAnyMediaActive(): boolean {
    return this.mediaState.audio.active || this.mediaState.video.active;
  }

  /**
   * Set state change callback
   */
  onStateChanged(callback: (mediaType: MediaTypeKey, state: MediaActivationState) => void): void {
    this.onStateChange = callback;
  }

  /**
   * Set activation error callback
   */
  onActivationErrorOccurred(callback: (mediaType: MediaTypeKey, error: Error) => void): void {
    this.onActivationError = callback;
  }

  /**
   * Get session information
   */
  getSessionInfo(): { sessionId: string; initialized: boolean; activeMedia: MediaTypeKey[] } {
    const activeMedia: MediaTypeKey[] = [];
    if (this.mediaState.audio.active) activeMedia.push('audio');
    if (this.mediaState.video.active) activeMedia.push('video');

    return {
      sessionId: this.mediaState.sessionId,
      initialized: this.mediaState.initialized,
      activeMedia
    };
  }

  /**
   * Reset media state (emergency stop)
   */
  async resetMediaState(): Promise<void> {
    try {
      this.logger.info('🔄 Resetting media state...');

      // Stop all media
      await this.setMediaState('audio', false, false);
      await this.setMediaState('video', false, false);

      // Reset state
      this.mediaState.audio = {
        active: false,
        userActivated: false,
        processing: false,
        lastActivation: null,
        lastDeactivation: Date.now()
      };

      this.mediaState.video = {
        active: false,
        userActivated: false,
        processing: false,
        lastActivation: null,
        lastDeactivation: Date.now()
      };

      this.logger.info('✅ Media state reset successfully');

    } catch (error) {
      this.logger.error('❌ Error resetting media state:', error);
    }
  }

  /**
   * Handle capture start event
   */
  private handleCaptureStart(): void {
    this.logger.debug('📹 Media capture started');
  }

  /**
   * Handle capture stop event  
   */
  private handleCaptureStop(): void {
    this.logger.debug('🛑 Media capture stopped');
  }

  /**
   * Handle capture error event
   */
  private handleCaptureError(error: Error): void {
    this.logger.error('❌ Media capture error:', error);
  }

  /**
   * Get MediaCaptureManager instance
   */
  getMediaCaptureManager(): MediaCaptureManager | null {
    return this.mediaCaptureManager;
  }

  /**
   * Dispose resources
   */
  async dispose(): Promise<void> {
    try {
      this.logger.info('🧹 Disposing MediaStateManager...');

      // Reset all media states
      await this.resetMediaState();

      // Dispose MediaCaptureManager
      if (this.mediaCaptureManager) {
        this.mediaCaptureManager.dispose();
        this.mediaCaptureManager = null;
      }

      // Clear callbacks
      this.onStateChange = undefined;
      this.onActivationError = undefined;

      // Reset state
      this.mediaState.initialized = false;

      this.logger.info('✅ MediaStateManager disposed successfully');

    } catch (error) {
      this.logger.error('❌ Error disposing MediaStateManager:', error);
    }
  }
}

export default MediaStateManager;