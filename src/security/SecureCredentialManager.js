/**
 * Secure Credential Management System
 * Handles API keys and sensitive credentials with security best practices
 * Prevents credential exposure in logs, error messages, and debugging output
 */

import { createLogger } from '@/utils/logger';

export class SecureCredentialManager {
    constructor(apiKey, options = {}) {
        this.logger = createLogger('SecureCredentialManager');
        
        if (!apiKey) {
            throw new Error('API key is required');
        }
        
        // Store credential securely (consider encryption in production)
        this._credential = Object.freeze(apiKey);
        this._keyId = this._generateKeyId(apiKey);
        this.provider = options.provider || 'unknown';
        this._isValidated = false;
        
        // Validate on creation
        this._validateCredential();
    }

    /**
     * Generate a secure key identifier for logging
     * @private
     */
    _generateKeyId(apiKey) {
        if (!apiKey || apiKey.length < 8) {
            return 'invalid-key';
        }
        return `${apiKey.slice(0, 4)}***${apiKey.slice(-4)}`;
    }

    /**
     * Validate credential format without exposing the actual key
     * @private
     */
    _validateCredential() {
        const key = this._credential;
        
        if (!key || typeof key !== 'string') {
            this.logger.error(`❌ [${this.provider}] Invalid credential format`);
            this._isValidated = false;
            return;
        }

        if (key.length < 10) {
            this.logger.warn(`⚠️ [${this.provider}] Credential appears too short (${this._keyId})`);
            this._isValidated = false;
            return;
        }

        // Check for common invalid patterns without logging the key
        if (key.includes('your-api-key') || key === 'test' || key === 'demo') {
            this.logger.error(`❌ [${this.provider}] Placeholder credential detected (${this._keyId})`);
            this._isValidated = false;
            return;
        }

        this.logger.info(`✅ [${this.provider}] Credential validation passed (${this._keyId})`);
        this._isValidated = true;
    }

    /**
     * Get the actual credential for API calls
     * Only use this for actual API authentication
     */
    getCredential() {
        if (!this._isValidated) {
            throw new Error(`Invalid ${this.provider} credential - cannot authenticate`);
        }
        return this._credential;
    }

    /**
     * Get masked credential for logging
     */
    getMaskedCredential() {
        return this._keyId;
    }

    /**
     * Get authorization header for API calls
     */
    getAuthorizationHeader() {
        return `Bearer ${this.getCredential()}`;
    }

    /**
     * Check if credential is valid
     */
    isValid() {
        return this._isValidated;
    }

    /**
     * Get credential metadata for logging (safe)
     */
    getMetadata() {
        return {
            provider: this.provider,
            keyId: this._keyId,
            isValid: this._isValidated,
            keyLength: this._credential?.length || 0
        };
    }

    /**
     * Override toString to prevent accidental exposure
     */
    toString() {
        return `[SecureCredentialManager: ${this.provider} (${this._keyId})]`;
    }

    /**
     * Override valueOf to prevent accidental exposure
     */
    valueOf() {
        return this.toString();
    }

    /**
     * Override JSON serialization to prevent exposure
     */
    toJSON() {
        return {
            provider: this.provider,
            keyId: this._keyId,
            isValid: this._isValidated
        };
    }
}

/**
 * Safe JSON parsing with schema validation
 * Prevents JSON injection and prototype pollution attacks
 */
export class SafeJsonParser {
    constructor(maxSize = 1024 * 1024) { // 1MB default limit
        this.maxSize = maxSize;
        this.logger = createLogger('SafeJsonParser');
    }

    /**
     * Safely parse JSON with validation
     * @param {string} jsonString - JSON string to parse
     * @param {object} schema - Optional JSON schema for validation
     * @returns {object} Parsed and validated JSON object
     */
    parse(jsonString, schema = null) {
        if (!jsonString || typeof jsonString !== 'string') {
            throw new SecurityError('Invalid JSON input: must be a string');
        }

        if (jsonString.length > this.maxSize) {
            throw new SecurityError(`JSON size exceeds limit: ${jsonString.length} > ${this.maxSize}`);
        }

        // Check for potential prototype pollution patterns
        if (this._containsPrototypePollution(jsonString)) {
            throw new SecurityError('Potential prototype pollution detected in JSON');
        }

        let parsed;
        try {
            parsed = JSON.parse(jsonString);
        } catch (error) {
            this.logger.error('JSON parsing failed:', error.message);
            throw new SecurityError('Invalid JSON format', { cause: error });
        }

        // Validate against schema if provided
        if (schema && !this._validateSchema(parsed, schema)) {
            throw new SecurityError('JSON does not match required schema');
        }

        // Remove dangerous properties
        return this._sanitizeObject(parsed);
    }

    /**
     * Check for prototype pollution patterns
     * @private
     */
    _containsPrototypePollution(jsonString) {
        const dangerousPatterns = [
            '__proto__',
            'constructor',
            'prototype',
            'constructor.prototype'
        ];
        
        return dangerousPatterns.some(pattern => 
            jsonString.includes(`"${pattern}"`) || 
            jsonString.includes(`'${pattern}'`)
        );
    }

    /**
     * Basic schema validation
     * @private
     */
    _validateSchema(obj, schema) {
        if (!schema || !schema.required) return true;
        
        return schema.required.every(field => obj.hasOwnProperty(field));
    }

    /**
     * Sanitize object by removing dangerous properties
     * @private
     */
    _sanitizeObject(obj) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }

        if (Array.isArray(obj)) {
            return obj.map(item => this._sanitizeObject(item));
        }

        const sanitized = {};
        for (const [key, value] of Object.entries(obj)) {
            // Skip dangerous properties
            if (key === '__proto__' || key === 'constructor' || key === 'prototype') {
                continue;
            }
            sanitized[key] = this._sanitizeObject(value);
        }

        return sanitized;
    }
}

/**
 * Custom security error class
 */
export class SecurityError extends Error {
    constructor(message, options = {}) {
        super(message);
        this.name = 'SecurityError';
        this.cause = options.cause;
        this.timestamp = new Date().toISOString();
    }
}

/**
 * Create secure credential manager for any provider
 */
export function createCredentialManager(apiKey, provider = 'universal') {
    return new SecureCredentialManager(apiKey, { provider });
}


/**
 * Create safe JSON parser with WebSocket message schema
 */
export function createWebSocketJsonParser() {
    const parser = new SafeJsonParser(10 * 1024); // 10KB limit for WebSocket messages
    
    // Define WebSocket message schema
    parser.webSocketSchema = {
        required: ['type'],
        properties: {
            type: { type: 'string' },
            event_id: { type: 'string' },
            session: { type: 'object' }
        }
    };
    
    return parser;
}