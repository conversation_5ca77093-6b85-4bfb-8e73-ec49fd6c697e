/**
 * TalkingHeadUI.js
 * UI components for the TalkingHead feature
 */

// Voice cloning functionality now handled by @/agent/tools/tts.js
// Keeping compatibility stubs for existing UI code
const FAVORITE_AUDIO_FOLDER = '__favorite__';

// Compatibility stubs for voice cloning functions - now handled by agent system
const uploadAudioFile = async (blob, path) => {
    console.log('[TalkingHeadUI] Audio upload handled by agent system');
    return { success: true, path };
};

const moveToFavoriteFolder = async (originalPath, avatarName) => {
    console.log('[TalkingHeadUI] Move to favorite handled by agent system');
    return originalPath;
};

const moveFromFavoriteFolder = async (favoritePath, avatarName) => {
    console.log('[TalkingHeadUI] Move from favorite handled by agent system');
    return favoritePath;
};

const isInFavoriteFolder = (path) => {
    return path && path.includes(`/audio/${FAVORITE_AUDIO_FOLDER}/`);
};

const optimizeAudioForVoiceCloning = async (audioBlob) => {
    console.log('[TalkingHeadUI] Audio optimization handled by agent system');
    return audioBlob;
};

const createWaveformVisualizer = (canvas, analyser) => {
    console.log('[TalkingHeadUI] Waveform visualization handled by agent system');
    return () => { }; // Return empty animation function
};
import { SkeletalAnimator } from '../../../animation/SkeletalAnimator.js';
import { getDownloadServerPort } from '@/utils/portManager.js';
import { ANIMATION_REGISTRY } from '../../../animation/AnimationConfig.js';
// Transcription utilities removed - AliyunBailianChatModel handles audio transcription natively

export class TalkingHeadUI {
    constructor(avatar) {
        console.log('[TalkingHeadUI] Constructor called for avatar:', avatar?.constructor?.name);

        this.avatar = avatar;
        this.controls = null;
        this.voiceFile = null;
        this.debugMode = true; // Add debug mode flag
        this.voiceBuffer = []; // Store voice buffer items
        this.currentPreviewAudio = null; // Track currently playing preview audio
        this.cameraPermissionDenied = false; // Track camera permission status
        this.addVoiceCloningStyles();

        // Initialize skeletal animation manager as null - will be created when needed
        // with the scene from the talking head
        this.skeletalAnimationManager = null;

        // Initialize cleanup handling
        this.initializeCleanup();

        // Load saved voice configuration if avatar is available
        if (this.avatar) {
            // Use setTimeout to ensure the avatar is fully initialized
            setTimeout(() => {
                this.loadSavedVoice().catch(error => {
                    console.error('[TalkingHeadUI] Error loading saved voice:', error);
                });
            }, 1000);
        }
    }

    /**
     * Initialize cleanup handling
     */
    initializeCleanup() {
        console.log('[TalkingHeadUI] Initializing cleanup handling...');

        // Clean up temporary audio files on initialization
        console.log('[TalkingHeadUI] Starting initial cleanup...');
        this.cleanupTemporaryAudioFiles().catch(error => {
            console.error('[TalkingHeadUI] Error during initial cleanup:', error);
        });

        // Add event listener for page unload to clean up temporary files
        console.log('[TalkingHeadUI] Setting up beforeunload cleanup handler...');
        window.addEventListener('beforeunload', () => {
            console.log('[TalkingHeadUI] Page unloading, starting cleanup...');
            this.cleanupTemporaryAudioFiles().catch(error => {
                console.error('[TalkingHeadUI] Error during unload cleanup:', error);
            });
        });

        // Add event listener for visibility change to clean up when page is hidden
        console.log('[TalkingHeadUI] Setting up visibility change cleanup handler...');
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'hidden') {
                console.log('[TalkingHeadUI] Page hidden, starting cleanup...');
                this.cleanupTemporaryAudioFiles().catch(error => {
                    console.error('[TalkingHeadUI] Error during visibility change cleanup:', error);
                });
            }
        });
    }

    /**
     * Clean up temporary audio files
     */
    async cleanupTemporaryAudioFiles() {
        console.log('[TalkingHeadUI] Starting temporary audio files cleanup...');
        console.log('[TalkingHeadUI] Current avatar:', this.avatar);
        console.log('[TalkingHeadUI] Avatar name:', this.avatar?.avatar?.name);
        console.log('[TalkingHeadUI] TalkingAvatar.name:', this.avatar?.name);
        console.log('[TalkingHeadUI] TalkingHead mesh userData:', this.avatar?.talkingHead?.mesh?.userData);
        console.log('[TalkingHeadUI] AvatarMesh userData:', this.avatar?.avatarMesh?.userData);
        console.log('[TalkingHeadUI] Viewer avatar userData:', this.avatar?.viewer?.objects?.get('avatar')?.userData);

        try {
            // Get the current avatar name - use mesh name directly as the most reliable source
            let avatarName = null;

            // Priority 1: TalkingHead mesh userData (most reliable after mesh loading)
            if (this.avatar?.talkingHead?.mesh?.userData?.fileName) {
                const fileName = this.avatar.talkingHead.mesh.userData.fileName;
                avatarName = fileName.replace(/\.[^/.]+$/, '');
                console.log(`[TalkingHeadUI] Found avatar name from mesh userData: ${avatarName}`);
            }
            // Priority 2: TalkingAvatar.avatar.name (set during transformation)
            else if (this.avatar?.avatar?.name) {
                avatarName = this.avatar.avatar.name;
                console.log(`[TalkingHeadUI] Found avatar name from avatar.avatar.name: ${avatarName}`);
            }
            // Priority 3: Direct TalkingAvatar.name
            else if (this.avatar?.name) {
                avatarName = this.avatar.name;
                console.log(`[TalkingHeadUI] Found avatar name from TalkingAvatar.name: ${avatarName}`);
            }

            // Only use timestamp fallback if no name found
            if (!avatarName) {
                avatarName = `avatar_${Date.now()}`;
                console.warn(`[TalkingHeadUI] No avatar name found, using timestamp fallback: ${avatarName}`);
            }

            // No sanitization - preserve the original name including Chinese characters
            console.log(`[TalkingHeadUI] Using original avatar name: ${avatarName}`);

            console.log(`[TalkingHeadUI] Using avatar name for cleanup: ${avatarName}`);

            // Create the audio directories if they don't exist
            try {
                const port = window.downloadServerPort || 2994;
                const host = window.config?.host || 'localhost';
                const downloadServerUrl = `http://${host}:${port}`;
                console.log(`[TalkingHeadUI] Using download server URL: ${downloadServerUrl}`);

                // First ensure the parent directory exists
                const parentDir = 'assets/audio';
                console.log(`[TalkingHeadUI] Ensuring parent directory exists: ${parentDir}`);

                try {
                    const encodedParentDir = encodeURIComponent(parentDir);
                    const parentDirResponse = await fetch(`${downloadServerUrl}/create-dir?dir=${encodedParentDir}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    if (!parentDirResponse.ok) {
                        console.warn(`[TalkingHeadUI] Failed to create parent directory: ${parentDirResponse.statusText}`);

                        // Try an alternative approach - use the upload endpoint with an empty file
                        try {
                            console.log(`[TalkingHeadUI] Trying alternative method to create parent directory: ${parentDir}`);
                            const formData = new FormData();
                            formData.append('path', parentDir);
                            formData.append('name', '.keep');
                            formData.append('file', new Blob([''], { type: 'text/plain' }), '.keep');

                            const uploadResponse = await fetch(`${downloadServerUrl}/upload`, {
                                method: 'POST',
                                body: formData
                            });

                            if (uploadResponse.ok) {
                                console.log(`[TalkingHeadUI] Created parent directory via file upload: ${parentDir}`);
                            } else {
                                console.warn(`[TalkingHeadUI] Alternative method also failed: ${uploadResponse.statusText}`);
                            }
                        } catch (altError) {
                            console.warn(`[TalkingHeadUI] Error with alternative directory creation: ${altError.message}`);
                        }
                    } else {
                        console.log(`[TalkingHeadUI] Parent directory created/verified: ${parentDir}`);
                    }
                } catch (parentDirError) {
                    console.warn(`[TalkingHeadUI] Error creating parent directory: ${parentDirError.message}`);
                }

                // Ensure both avatar-specific and favorite directories exist
                const directories = [
                    `assets/audio/${avatarName}`,
                    `assets/audio/${FAVORITE_AUDIO_FOLDER}`
                ];
                console.log('[TalkingHeadUI] Will create/check directories:', directories);

                for (const dir of directories) {
                    console.log(`[TalkingHeadUI] Creating/checking directory: ${dir}`);

                    try {
                        // Properly encode the directory path for URLs with Chinese characters
                        const encodedDir = encodeURIComponent(dir);
                        const createDirResponse = await fetch(`${downloadServerUrl}/create-dir?dir=${encodedDir}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });

                        if (createDirResponse.ok) {
                            console.log(`[TalkingHeadUI] Directory created/verified successfully: ${dir}`);
                        } else {
                            console.warn(`[TalkingHeadUI] Failed to create/verify directory: ${dir} - ${createDirResponse.statusText}`);

                            // Try an alternative approach - use the upload endpoint with an empty file
                            try {
                                console.log(`[TalkingHeadUI] Trying alternative method to create directory: ${dir}`);
                                const formData = new FormData();
                                formData.append('path', dir);
                                formData.append('name', '.keep');
                                formData.append('file', new Blob([''], { type: 'text/plain' }), '.keep');

                                const uploadResponse = await fetch(`${downloadServerUrl}/upload`, {
                                    method: 'POST',
                                    body: formData
                                });

                                if (uploadResponse.ok) {
                                    console.log(`[TalkingHeadUI] Created directory via file upload: ${dir}`);
                                } else {
                                    console.warn(`[TalkingHeadUI] Alternative method also failed: ${uploadResponse.statusText}`);
                                }
                            } catch (altError) {
                                console.warn(`[TalkingHeadUI] Error with alternative directory creation: ${altError.message}`);
                            }
                        }
                    } catch (dirCreateError) {
                        console.warn(`[TalkingHeadUI] Error creating directory ${dir}:`, dirCreateError);
                    }
                }
            } catch (dirError) {
                console.warn('[TalkingHeadUI] Error creating directories:', dirError);
            }

            // Only keep files that are referenced in seed files (favorites)
            const { getStoredSeed, generateCacheKey } = await import('@/utils/cache.js');

            // Generate cache keys for both default and avatar-specific voices
            const voiceCacheKeys = [];

            // Add default voice cache key
            voiceCacheKeys.push('default');

            // Add avatar-specific voice cache key if not default
            if (avatarName !== 'default') {
                try {
                    const avatarVoiceCacheKey = await generateCacheKey('voice', avatarName, null);
                    voiceCacheKeys.push(avatarVoiceCacheKey);
                    console.log(`[TalkingHeadUI] Added avatar voice cache key: ${avatarVoiceCacheKey}`);
                } catch (error) {
                    console.warn('[TalkingHeadUI] Error generating avatar voice cache key:', error);
                }
            }

            // Get the current seed data for all cache keys
            const filesToKeep = new Set();
            for (const voiceCacheKey of voiceCacheKeys) {
                console.log(`[TalkingHeadUI] Checking seed data for key: ${voiceCacheKey}`);
                const seedData = await getStoredSeed(voiceCacheKey);

                if (seedData) {
                    let parsedSeedData;
                    try {
                        if (typeof seedData === 'string') {
                            // Check if the string is URL-encoded
                            if (seedData.startsWith('%')) {
                                try {
                                    // Try to decode the URI component first
                                    const decodedData = decodeURIComponent(seedData);
                                    parsedSeedData = JSON.parse(decodedData);
                                } catch (decodeError) {
                                    // If decoding fails, try parsing directly
                                    parsedSeedData = JSON.parse(seedData);
                                }
                            } else {
                                // Regular JSON string
                                parsedSeedData = JSON.parse(seedData);
                            }
                        } else {
                            // Already an object
                            parsedSeedData = seedData;
                        }

                        // Add the reference audio file to the keep list
                        if (parsedSeedData.reference_audio_file) {
                            filesToKeep.add(parsedSeedData.reference_audio_file);
                            console.log(`[TalkingHeadUI] Keeping reference audio file: ${parsedSeedData.reference_audio_file}`);
                        }
                    } catch (error) {
                        console.error('[TalkingHeadUI] Error parsing seed data:', error);
                    }
                }
            }

            // Also keep any files that are currently in use in the voice buffer
            if (this.voiceBuffer && Array.isArray(this.voiceBuffer)) {
                for (const voiceData of this.voiceBuffer) {
                    if (voiceData.reference_audio_file) {
                        filesToKeep.add(voiceData.reference_audio_file);
                        console.log(`[TalkingHeadUI] Keeping voice buffer reference audio file: ${voiceData.reference_audio_file}`);
                    }
                }
            }

            console.log('[TalkingHeadUI] Files to keep:', Array.from(filesToKeep));

            // Delete all files in both default and avatar-specific folders except those in filesToKeep
            try {
                const port = window.downloadServerPort || 2994;
                const host = window.config?.host || 'localhost';
                const downloadServerUrl = `http://${host}:${port}`;

                // Clean up the avatar-specific directory
                for (const dir of [`assets/audio/${avatarName}`]) {
                    // Skip the favorite folder itself
                    if (dir === `assets/audio/${FAVORITE_AUDIO_FOLDER}`) {
                        console.log(`[TalkingHeadUI] Skipping favorite folder: ${dir}`);
                        continue;
                    }

                    // Get a list of all files in the directory
                    // Properly encode the directory path for URLs with Chinese characters
                    const encodedDir = encodeURIComponent(dir);
                    const response = await fetch(`${downloadServerUrl}/list-files?dir=${encodedDir}`);
                    if (!response.ok) {
                        console.warn(`[TalkingHeadUI] Failed to get file list for ${dir}: ${response.statusText}`);
                        continue;
                    }

                    const files = await response.json();
                    console.log(`[TalkingHeadUI] Found ${files?.files?.length || 0} files in ${dir}`);

                    if (files && files.files && Array.isArray(files.files)) {
                        // Delete each file that's not in filesToKeep
                        let deletedCount = 0;
                        let keptCount = 0;

                        for (const file of files.files) {
                            const filePath = `/${dir}/${file}`;
                            if (!filesToKeep.has(filePath)) {
                                console.log(`[TalkingHeadUI] Deleting temporary file: ${filePath}`);

                                // First check if the file exists
                                try {
                                    const checkResponse = await fetch(filePath, { method: 'HEAD' });

                                    if (checkResponse.ok) {
                                        // File exists, try to delete it
                                        try {
                                            // Use the correct URL format for file deletion
                                            const deleteResponse = await fetch(`${downloadServerUrl}/delete-file?path=${encodeURIComponent(filePath)}`, {
                                                method: 'DELETE'
                                            });

                                            if (deleteResponse.ok) {
                                                deletedCount++;
                                                console.log(`[TalkingHeadUI] Successfully deleted file: ${filePath}`);
                                            } else {
                                                console.warn(`[TalkingHeadUI] Failed to delete file ${file}: ${deleteResponse.statusText}`);
                                                // Even if deletion fails, count it as "processed"
                                                deletedCount++;
                                            }
                                        } catch (deleteError) {
                                            console.warn(`[TalkingHeadUI] Error deleting file ${file}:`, deleteError);
                                            // Count as processed even if there's an error
                                            deletedCount++;
                                        }
                                    } else {
                                        console.log(`[TalkingHeadUI] File doesn't exist, skipping deletion: ${filePath}`);
                                        // Count as processed since it doesn't exist
                                        deletedCount++;
                                    }
                                } catch (checkError) {
                                    console.warn(`[TalkingHeadUI] Error checking file existence ${file}:`, checkError);
                                    // Count as processed even if there's an error checking
                                    deletedCount++;
                                }
                            } else {
                                keptCount++;
                                console.log(`[TalkingHeadUI] Keeping file: ${filePath}`);
                            }
                        }

                        console.log(`[TalkingHeadUI] Cleanup summary for ${dir}: ${deletedCount} files deleted, ${keptCount} files kept`);
                    }
                }

                // Create the favorite folder if it doesn't exist, but DO NOT clean it up
                // Favorite files should be preserved regardless of whether they're in filesToKeep
                const favoriteDir = `assets/audio/${FAVORITE_AUDIO_FOLDER}`;
                console.log(`[TalkingHeadUI] Ensuring favorite folder exists: ${favoriteDir}`);

                try {
                    // First check if the favorite directory exists
                    try {
                        const dirCheckResponse = await fetch(`${downloadServerUrl}/create-dir?dir=${encodeURIComponent(favoriteDir)}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });

                        if (!dirCheckResponse.ok) {
                            console.warn(`[TalkingHeadUI] Favorite directory doesn't exist or couldn't be created: ${dirCheckResponse.statusText}`);
                            // Try to create it with the alternative method
                            try {
                                console.log(`[TalkingHeadUI] Trying alternative method to create favorite directory: ${favoriteDir}`);
                                const formData = new FormData();
                                formData.append('path', favoriteDir);
                                formData.append('name', '.keep');
                                formData.append('file', new Blob([''], { type: 'text/plain' }), '.keep');

                                const uploadResponse = await fetch(`${downloadServerUrl}/upload`, {
                                    method: 'POST',
                                    body: formData
                                });

                                if (uploadResponse.ok) {
                                    console.log(`[TalkingHeadUI] Created favorite directory via file upload: ${favoriteDir}`);
                                } else {
                                    console.warn(`[TalkingHeadUI] Alternative method also failed: ${uploadResponse.statusText}`);
                                }
                            } catch (altError) {
                                console.warn(`[TalkingHeadUI] Error with alternative directory creation: ${altError.message}`);
                            }
                        }
                    } catch (dirCheckError) {
                        console.warn(`[TalkingHeadUI] Error checking/creating favorite directory: ${dirCheckError.message}`);
                    }

                    // List files in the favorite folder for logging purposes only
                    const encodedFavoriteDir = encodeURIComponent(favoriteDir);
                    const favoriteResponse = await fetch(`${downloadServerUrl}/list-files?dir=${encodedFavoriteDir}`);

                    if (favoriteResponse.ok) {
                        const favoriteFiles = await favoriteResponse.json();
                        console.log(`[TalkingHeadUI] Found ${favoriteFiles?.files?.length || 0} files in favorite folder`);

                        if (favoriteFiles && favoriteFiles.files && Array.isArray(favoriteFiles.files)) {
                            // Add all favorite files to filesToKeep to ensure they're preserved
                            for (const file of favoriteFiles.files) {
                                const filePath = `/${favoriteDir}/${file}`;
                                if (!filesToKeep.has(filePath)) {
                                    filesToKeep.add(filePath);
                                    console.log(`[TalkingHeadUI] Added favorite file to keep list: ${filePath}`);
                                }
                            }

                            console.log(`[TalkingHeadUI] All ${favoriteFiles.files.length} favorite files preserved`);
                        }
                    } else {
                        console.warn(`[TalkingHeadUI] Failed to get file list for favorite folder: ${favoriteResponse.statusText}`);
                    }
                } catch (favoriteError) {
                    console.warn('[TalkingHeadUI] Error ensuring favorite folder exists:', favoriteError);
                }
            } catch (listError) {
                console.warn('[TalkingHeadUI] Error listing files for cleanup:', listError);
            }

            console.log('[TalkingHeadUI] Temporary audio files cleanup completed successfully');
        } catch (error) {
            console.error('[TalkingHeadUI] Error in cleanupTemporaryAudioFiles:', error);
            throw error; // Re-throw to be caught by the caller
        }
    }

    /**
     * Compact MediaCoordinator fallback initialization
     */
    async initializeMediaCoordinator() {
        if (!this.avatar) return null;
        
        // Try avatar initialization first
        if (typeof this.avatar.initialize === 'function') {
            await this.avatar.initialize();
            if (this.avatar.mediaCoordinator) return this.avatar.mediaCoordinator;
        }
        
        // Fallback: create minimal MediaCoordinator
        try {
            const { MediaCoordinator } = await import('@/../app/viewer/services/mediaCoordinator.js');
            const { createLogger } = await import('@/utils/logger.ts');
            const logger = createLogger('MediaCoordinator');
            this.avatar.mediaCoordinator = new MediaCoordinator({ logger });
            await this.avatar.mediaCoordinator.initialize();
            return this.avatar.mediaCoordinator;
        } catch (error) {
            console.error('[TalkingHeadUI] Failed to initialize MediaCoordinator:', error);
            return null;
        }
    }

    /**
     * Check camera permission status
     * @private
     * @returns {Promise<boolean>} True if camera permission is available
     */
    async _checkCameraPermission() {
        try {
            // First check if mediaDevices is available
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                console.warn('[TalkingHeadUI] Media devices not available - camera features will be disabled');
                this.cameraPermissionDenied = true;
                return false;
            }

            // Try to request camera access to check permissions
            const testStream = await navigator.mediaDevices.getUserMedia({
                video: { width: 1, height: 1 },
                audio: false
            });

            // If successful, immediately stop the test stream
            if (testStream) {
                testStream.getTracks().forEach(track => track.stop());
                console.log('[TalkingHeadUI] Camera permission available');
                this.cameraPermissionDenied = false;
                return true;
            }

            return false;
        } catch (error) {
            console.warn('[TalkingHeadUI] Camera permission denied or not available:', error);

            // Check for specific permission errors
            if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
                console.log('[TalkingHeadUI] Camera permission explicitly denied - video features will be disabled');
                this.cameraPermissionDenied = true;
            } else if (error.name === 'NotFoundError' || error.name === 'DevicesNotFoundError') {
                console.log('[TalkingHeadUI] No camera device found - video features will be disabled');
                this.cameraPermissionDenied = true;
            } else {
                console.log('[TalkingHeadUI] Camera access failed - video features will be disabled');
                this.cameraPermissionDenied = true;
            }

            return false;
        }
    }

    /**
     * Create and connect TalkingHead UI controls
     * @param {Object} talkingHead - TalkingHead instance
     */
    async createControls(talkingHead) {
        if (!talkingHead) return;

        console.log('[TalkingHeadUI] Creating controls for TalkingHead...');

        // Check camera permissions before creating UI
        await this._initializeControlsAsync(talkingHead);

        console.log('[TalkingHeadUI] Controls created successfully:', {
            hasControls: !!this.controls,
            hasStatusSpan: !!this.controls?.statusSpan,
            hasContainer: !!this.controls?.container
        });

        return this.controls;
    }

    /**
     * Initialize controls asynchronously after checking camera permissions
     * @param {Object} talkingHead - TalkingHead instance
     * @private
     */
    async _initializeControlsAsync(talkingHead) {
        // === BUTTON CONFIGURATION === 
        // Configure which buttons to show in the control panel
        const buttonConfig = {
            speech: false,      // 🎤 Speak button
            poses: false,       // 🧍 Poses button  
            animations: false,  // 🏃 Animations button
            voice: true,        // 🔊 Voice button
            listen: true,       // 👂 Listen button
            video: true         // 📹 Video button
        };

        console.log('[TalkingHeadUI] Button configuration:', buttonConfig);

        // Check camera permission status
        const cameraAvailable = await this._checkCameraPermission();

        // Create control container
        const controlContainer = document.createElement('div');
        controlContainer.className = 'talking-head-controls';

        // === CONDITIONAL BUTTON CREATION ===
        let speechButton = null;
        let poseButton = null;
        let poseMenu = null;
        let animationButton = null;
        let animationMenu = null;

        // Create speech button (conditionally)
        if (buttonConfig.speech) {
            speechButton = document.createElement('button');
            speechButton.className = 'talking-head-speech-button';
            speechButton.innerHTML = '🎤 Speak';

            // Add speak functionality
            speechButton.addEventListener('click', () => {
                const text = prompt('What would you like the avatar to say?');
                if (text && text.trim().length > 0) {
                    this.avatar.speakText(text);
                }
            });
        }

        // Create listen button (conditionally)
        let listenButton = null;
        if (buttonConfig.listen) {
            listenButton = document.createElement('button');
            listenButton.className = 'talking-head-listen-button';
            listenButton.innerHTML = '👂 Listen';
        }

        // Create video streaming button (conditionally)
        let videoButton = null;
        if (buttonConfig.video) {
            if (cameraAvailable && !this.cameraPermissionDenied) {
                videoButton = document.createElement('button');
                videoButton.className = 'talking-head-video-button';
                videoButton.innerHTML = '📹 Video';
            } else {
                console.log('[TalkingHeadUI] Camera not available - video button disabled');
                // Create a disabled video button to show it's not available
                videoButton = document.createElement('button');
                videoButton.className = 'talking-head-video-button disabled';
                videoButton.innerHTML = '📹 Video (N/A)';
                videoButton.disabled = true;
                videoButton.title = 'Camera permission denied or no camera available';
            }
        }

        // Create pose button (conditionally)
        if (buttonConfig.poses) {
            poseButton = document.createElement('button');
            poseButton.className = 'talking-head-pose-button';
            poseButton.innerHTML = '🧍 Poses';

            // Create dropdown menu for poses
            poseMenu = document.createElement('div');
            poseMenu.className = 'talking-head-dropdown';

            // Add available poses to menu
            if (talkingHead.poseTemplates) {
                Object.keys(talkingHead.poseTemplates).forEach(poseName => {
                    const poseItem = document.createElement('button');
                    poseItem.textContent = poseName.charAt(0).toUpperCase() + poseName.slice(1);
                    poseItem.className = 'talking-head-dropdown-item';
                    poseItem.addEventListener('click', () => {
                        console.log('[TalkingHeadUI] Setting pose:', poseName);
                        // Use the correct method: setPoseFromTemplate instead of playPose
                        talkingHead.setPoseFromTemplate(talkingHead.poseTemplates[poseName], 1000);
                        poseMenu.classList.remove('show');
                    });
                    poseMenu.appendChild(poseItem);
                });
            } else {
                // Add a message if no poses available
                const noPosesItem = document.createElement('div');
                noPosesItem.textContent = 'No poses available';
                noPosesItem.className = 'talking-head-dropdown-item';
                noPosesItem.style.opacity = '0.7';
                noPosesItem.style.cursor = 'default';
                poseMenu.appendChild(noPosesItem);
            }

            // Enhanced toggle menu display with comprehensive visibility forcing
            poseButton.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                console.log('[TalkingHeadUI] Poses button clicked - starting comprehensive visibility fix');

                // Close other menus first
                this.hideAllDropdowns();

                // Toggle poses menu with comprehensive visibility forcing
                const isShowing = poseMenu.classList.contains('show');
                console.log('[TalkingHeadUI] Poses menu currently showing:', isShowing);

                if (isShowing) {
                    this.hideDropdown(poseMenu);
                    console.log('[TalkingHeadUI] Poses menu hidden');
                } else {
                    this.hideAllDropdowns();
                    this.showDropdown(poseMenu, poseButton);
                    console.log('[TalkingHeadUI] Poses menu shown');

                    // Show success notification
                    this.showNotification('Poses menu opened!');
                }
            });

            // Enhanced close menu when clicking outside with comprehensive approach
            document.addEventListener('click', (e) => {
                if (!poseButton.contains(e.target) && !poseMenu.contains(e.target)) {
                    this.hideDropdown(poseMenu);
                }
            });
        }

        // Create animation button (conditionally)
        if (buttonConfig.animations) {
            animationButton = document.createElement('button');
            animationButton.className = 'talking-head-animation-button';
            animationButton.innerHTML = '🏃 Animations';

            // Create dropdown menu for animations
            animationMenu = document.createElement('div');
            animationMenu.className = 'talking-head-dropdown';

            // Initialize skeletal animation manager
            if (!this.skeletalAnimationManager) {
                window.talkingHead = talkingHead;
                this.skeletalAnimationManager = new SkeletalAnimator(talkingHead.armature, {
                    llmService: talkingHead.llmService,
                    debug: true
                });
            }

            // Function to load animations from public/assets/animations
            this.loadAvailableAnimations(animationMenu, talkingHead);

            // Enhanced toggle menu display with comprehensive visibility forcing
            animationButton.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                console.log('[TalkingHeadUI] Animations button clicked - starting comprehensive visibility fix');

                // Close other menus first
                this.hideAllDropdowns();

                // Toggle animations menu with comprehensive visibility forcing
                const isShowing = animationMenu.classList.contains('show');
                console.log('[TalkingHeadUI] Animations menu currently showing:', isShowing);

                if (isShowing) {
                    this.hideDropdown(animationMenu);
                    console.log('[TalkingHeadUI] Animations menu force hidden');
                } else {
                    this.showDropdown(animationMenu, animationButton);
                    console.log('[TalkingHeadUI] Animations menu force shown');

                    // Show success notification
                    this.showNotification('Animations menu opened - comprehensive visibility applied!');
                }
            });

            // Enhanced close menu when clicking outside with comprehensive approach
            document.addEventListener('click', (e) => {
                if (!animationButton.contains(e.target) && !animationMenu.contains(e.target)) {
                    this.hideDropdown(animationMenu);
                }
            });
        }

        // Create conversation status FIRST (before referencing it)
        const statusSpan = document.createElement('span');
        statusSpan.className = 'talking-head-status';
        statusSpan.textContent = 'Ready';

        // SIMPLIFIED INPUT CONTROL: Buttons control input capture directly
        // No complex state management - realtime connection stays persistent

        // Setup listen button handler - integrated with MediaCoordinator
        if (listenButton) {
            listenButton.addEventListener('click', async () => {
                try {
                    // Get MediaCoordinator from avatar with fallback
                    const mediaCoordinator = this.avatar?.mediaCoordinator || 
                        (await this.initializeMediaCoordinator());
                    if (!mediaCoordinator) {
                        throw new Error('MediaCoordinator not available');
                    }

                    const isCurrentlyActive = mediaCoordinator.isAudioActive();

                    if (isCurrentlyActive) {
                        console.log('[TalkingHeadUI] Stopping audio input via MediaCoordinator');
                        const success = await mediaCoordinator.stopAudioInput();

                        if (success) {
                            listenButton.innerHTML = '👂 Listen';
                            listenButton.classList.remove('active');
                            listenButton.style.backgroundColor = 'var(--accent-color)';
                            statusSpan.textContent = 'Audio Input Off';
                        } else {
                            throw new Error('Failed to stop audio input');
                        }
                    } else {
                        console.log('[TalkingHeadUI] Starting audio input via MediaCoordinator');
                        const success = await mediaCoordinator.startAudioInput(true); // User activated

                        if (success) {
                            listenButton.innerHTML = '⏹️ Stop Audio';
                            listenButton.classList.add('active');
                            listenButton.style.backgroundColor = 'rgba(76, 175, 80, 0.8)';
                            statusSpan.textContent = 'Audio Input Active';
                        } else {
                            throw new Error('Failed to start audio input');
                        }
                    }
                } catch (error) {
                    console.error('[TalkingHeadUI] Audio input error:', error);
                    this.showNotification('Error with audio: ' + error.message, true, 3000);
                }
            });
        }

        // Setup video button handler - integrated with MediaCoordinator
        if (videoButton && cameraAvailable && !this.cameraPermissionDenied) {
            videoButton.addEventListener('click', async () => {
                try {
                    // Get MediaCoordinator from avatar with fallback
                    const mediaCoordinator = this.avatar?.mediaCoordinator || 
                        (await this.initializeMediaCoordinator());
                    if (!mediaCoordinator) {
                        throw new Error('MediaCoordinator not available');
                    }

                    const isCurrentlyActive = mediaCoordinator.isVideoStreamingActive();

                    if (isCurrentlyActive) {
                        console.log('[TalkingHeadUI] Stopping video input via MediaCoordinator');
                        await mediaCoordinator.stopVideoStreaming();

                        videoButton.innerHTML = '📹 Video';
                        videoButton.classList.remove('active');
                        videoButton.style.backgroundColor = 'var(--accent-color)';
                        statusSpan.textContent = 'Video Input Off';
                    } else {
                        console.log('[TalkingHeadUI] Starting video input via MediaCoordinator');
                        const success = await mediaCoordinator.startVideoStreaming(true); // User activated

                        if (success) {
                            videoButton.innerHTML = '⏹️ Stop Video';
                            videoButton.classList.add('active');
                            videoButton.style.backgroundColor = 'rgba(33, 150, 243, 0.8)';
                            statusSpan.textContent = 'Video Input Active';

                            // Open camera UI window using existing CameraViewer when available
                            try {
                                const viewerCamera = this.avatar?.viewer?.cameraViewer
                                    || this.avatar?.viewer?.uiSettings?.components?.cameraViewer;
                                if (viewerCamera && typeof viewerCamera.openViewer === 'function') {
                                    await viewerCamera.openViewer();
                                    console.log('[TalkingHeadUI] CameraViewer opened successfully');
                                } else {
                                    console.warn('[TalkingHeadUI] CameraViewer not available to open UI window');
                                }
                            } catch (viewerErr) {
                                console.warn('[TalkingHeadUI] Unable to open CameraViewer:', viewerErr);
                            }
                        } else {
                            throw new Error('Failed to start video input');
                        }
                    }
                } catch (error) {
                    console.error('[TalkingHeadUI] Video input error:', error);
                    this.showNotification('Error with video: ' + error.message, true, 3000);
                }
            });
        } else if (videoButton) {
            // Camera not available - show error message
            videoButton.addEventListener('click', () => {
                this.showNotification('Camera not available. Please check camera permissions and refresh the page.', true, 5000);
            });
        }

        console.log('[TalkingHeadUI] ✅ Simplified input controls connected - no state management');

        // Create SparkTTS voice selector button BEFORE storing references
        const voiceButton = document.createElement('button');
        voiceButton.className = 'talking-head-voice-button';
        voiceButton.innerHTML = '🔊 Voice';
        // CSS class will handle styling automatically

        // Create dropdown menu for SparkTTS voices
        const voiceMenu = document.createElement('div');
        voiceMenu.className = 'voice-menu';

        // Store references for status synchronization and enhanced dropdown management AFTER all elements are created
        this.controls = {
            speechButton,
            listenButton,
            videoButton,
            statusSpan,
            container: controlContainer,
            poseButton,
            poseMenu,
            animationButton,
            animationMenu,
            voiceButton,
            voiceMenu
        };

        // Add voice cloning button at the top
        const voiceCloneButton = document.createElement('button');
        voiceCloneButton.className = 'voice-clone-button';
        voiceCloneButton.innerHTML = '🎤 Clone Voice';

        // Create voice cloning modal (remove the 'x' close button)
        const voiceCloneModal = document.createElement('div');
        voiceCloneModal.className = 'voice-clone-modal';
        voiceCloneModal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000; /* Higher z-index to ensure it's above the overlay */
            overflow-y: auto;
            padding: 20px 0;
        `;

        // Add styles for the modal content to make it more visible
        const modalStyle = document.createElement('style');
        modalStyle.textContent = `
            .voice-clone-content {
                background-color: rgba(40, 44, 52, 0.95);
                border-radius: 12px;
                padding: 20px;
                width: 90%;
                max-width: 550px;
                max-height: 90vh;
                overflow-y: auto;
                color: white;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
                font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .voice-clone-content .modal-header {
                margin-bottom: 15px;
                text-align: center;
                position: sticky;
                top: 0;
                background-color: rgba(40, 44, 52, 0.95);
                padding: 15px 0;
                z-index: 10;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                margin-top: -20px;
                margin-left: -20px;
                margin-right: -20px;
                padding-left: 20px;
                padding-right: 20px;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
            }

            .voice-clone-content h3 {
                margin: 0 0 10px 0;
                color: white;
                font-size: 20px;
                font-weight: 600;
            }

            .voice-clone-content .prompt-section h3 {
                font-size: 16px;
                margin-bottom: 8px;
            }

            .voice-clone-content .prompt-container {
                position: relative;
                margin-bottom: 15px;
            }

            .voice-clone-content .prompt-text {
                background-color: rgba(255, 255, 255, 0.1);
                padding: 12px;
                border-radius: 8px;
                font-size: 14px;
                line-height: 1.5;
                border-left: 4px solid #4CAF50;
                max-height: 80px;
                overflow-y: auto;
                margin: 0;
                cursor: text;
                transition: all 0.2s ease;
            }

            .voice-clone-content .prompt-text:hover {
                background-color: rgba(255, 255, 255, 0.15);
            }

            .voice-clone-content .prompt-text.editable {
                background-color: rgba(255, 255, 255, 0.2);
                border: 1px solid rgba(76, 175, 80, 0.5);
                outline: none;
                min-height: 80px;
            }

            .voice-clone-content .recording-section {
                background-color: rgba(0, 0, 0, 0.2);
                border-radius: 12px;
                padding: 20px;
                margin-bottom: 20px;
                display: flex;
                flex-direction: column;
                gap: 15px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                position: relative;
                scroll-margin-top: 20px;
            }

            /* Add scroll styling */
            .voice-clone-content::-webkit-scrollbar {
                width: 8px;
            }

            .voice-clone-content::-webkit-scrollbar-track {
                background: rgba(0, 0, 0, 0.2);
                border-radius: 4px;
            }

            .voice-clone-content::-webkit-scrollbar-thumb {
                background: rgba(76, 175, 80, 0.5);
                border-radius: 4px;
            }

            .voice-clone-content::-webkit-scrollbar-thumb:hover {
                background: rgba(76, 175, 80, 0.7);
            }

            .voice-clone-content .recording-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                grid-template-rows: auto auto;
                gap: 15px;
                grid-template-areas:
                    "record timer"
                    "upload stt";
                align-items: stretch;
            }

            @media (max-width: 600px) {
                .voice-clone-content .recording-grid {
                    grid-template-columns: 1fr;
                    grid-template-areas:
                        "record"
                        "timer"
                        "upload"
                        "stt";
                }
            }

            .voice-clone-content .recording-controls {
                grid-area: record;
            }

            .voice-clone-content .timer-container {
                grid-area: timer;
                display: flex;
                justify-content: flex-end;
                align-items: center;
            }

            .voice-clone-content .file-upload {
                grid-area: upload;
            }

            .voice-clone-content .stt-option-container {
                grid-area: stt;
                display: flex;
                align-items: center;
            }

            .voice-clone-content .waveform-container {
                background-color: rgba(0, 0, 0, 0.3);
                border-radius: 8px;
                padding: 10px;
                margin-bottom: 15px;
                height: 60px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: height 0.3s ease;
                overflow: hidden;
                width: 100%;
                box-sizing: border-box;
                position: relative;
            }

            .voice-clone-content .waveform-container.recording {
                height: 100px;
            }

            #voiceWaveform {
                width: 100%;
                height: 40px;
                transition: height 0.3s ease;
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
            }

            #voiceWaveform.recording {
                height: 80px;
            }

            .voice-clone-content .preview-section {
                margin-top: 15px;
                display: none; /* Hidden by default, will be shown when .active class is added */
                background-color: rgba(0, 0, 0, 0.2);
                border-radius: 12px;
                padding: 15px;
                margin-bottom: 15px;
                position: relative;
                z-index: 1;
                pointer-events: auto;
                scroll-margin-top: 20px;
            }

            .voice-clone-content .preview-section h3 {
                font-size: 18px;
                margin-bottom: 5px;
                text-align: center;
            }

            .voice-clone-content .preview-section p {
                font-size: 14px;
                margin-bottom: 15px;
                text-align: center;
                opacity: 0.8;
            }

            .voice-clone-content .preview-section.active {
                display: block;
            }

            .voice-clone-content .preview-controls {
                display: flex;
                align-items: center;
                gap: 15px;
                margin-bottom: 15px;
            }

            .voice-clone-content .preview-clone-button {
                display: flex;
                align-items: center;
                gap: 8px;
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px 16px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                transition: all 0.2s ease;
                flex-grow: 1;
            }

            .voice-clone-content .preview-clone-button:hover:not(:disabled) {
                background-color: #45a049;
                transform: translateY(-2px);
            }

            .voice-clone-content .preview-clone-button:disabled {
                background-color: #cccccc;
                cursor: not-allowed;
                opacity: 0.7;
            }

            .voice-clone-content .preview-clone-button.playing {
                background-color: #f44336;
            }

            .voice-clone-content .preview-waveform-container {
                width: 100%;
                height: 60px;
                background-color: rgba(0, 0, 0, 0.3);
                border-radius: 8px;
                overflow: hidden;
                margin-bottom: 10px;
            }

            .voice-clone-content .preview-progress {
                display: flex;
                flex-direction: column;
                gap: 5px;
            }

            .voice-clone-content .preview-progress-bar {
                background-color: rgba(255, 255, 255, 0.2);
                border-radius: 4px;
                height: 6px;
                overflow: hidden;
            }

            .voice-clone-content .preview-progress-fill {
                background-color: #4CAF50;
                height: 100%;
                width: 0%;
                border-radius: 4px;
                transition: width 0.3s;
            }

            .voice-clone-content .preview-time {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.7);
                text-align: right;
            }

            .voice-clone-content .recording-controls {
                display: flex;
                align-items: center;
                width: 100%;
            }

            .voice-clone-content .record-button {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 30px;
                cursor: pointer;
                font-size: 15px;
                font-weight: 500;
                transition: all 0.2s ease;
                width: 100%;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            }

            .voice-clone-content .record-button:hover {
                background-color: #45a049;
                transform: translateY(-2px);
                box-shadow: 0 4px 10px rgba(76, 175, 80, 0.4);
            }

            .voice-clone-content .record-button.recording {
                background-color: #f44336;
                animation: pulse 1.5s infinite;
                box-shadow: 0 4px 10px rgba(244, 67, 54, 0.4);
            }

            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.7; }
                100% { opacity: 1; }
            }

            .voice-clone-content .record-icon {
                color: white;
                font-size: 12px;
            }

            .voice-clone-content .timer {
                font-size: 18px;
                font-weight: 600;
                min-width: 80px;
                background-color: rgba(0, 0, 0, 0.3);
                padding: 12px 20px;
                border-radius: 20px;
                text-align: center;
                box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
                border: 1px solid rgba(255, 255, 255, 0.1);
                color: rgba(255, 255, 255, 0.9);
                display: inline-block;
            }

            .voice-clone-content .file-upload {
                background-color: rgba(0, 0, 0, 0.15);
                border-radius: 12px;
                padding: 15px;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
                border: 1px solid rgba(255, 255, 255, 0.05);
                height: 100%;
                display: flex;
                flex-direction: column;
            }

            .voice-clone-content .file-upload p {
                margin: 0 0 10px 0;
                font-size: 15px;
                opacity: 0.9;
                font-weight: 500;
                color: rgba(255, 255, 255, 0.9);
                text-align: center;
            }

            .voice-clone-content .file-input-container {
                position: relative;
                margin-bottom: 12px;
            }

            .voice-clone-content input[type="file"] {
                background-color: rgba(255, 255, 255, 0.1);
                padding: 12px;
                border-radius: 8px;
                width: 100%;
                color: white;
                border: 1px dashed rgba(255, 255, 255, 0.3);
                cursor: pointer;
                font-size: 14px;
                text-align: center;
                transition: all 0.2s ease;
                box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
            }

            .voice-clone-content input[type="file"]:hover {
                background-color: rgba(255, 255, 255, 0.15);
                border-color: rgba(255, 255, 255, 0.5);
                box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
            }

            .voice-clone-content .stt-option-container {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
            }

            .voice-clone-content .stt-option {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
                font-size: 14px;
                background-color: rgba(0, 0, 0, 0.2);
                padding: 12px 15px;
                border-radius: 12px;
                border: 1px solid rgba(255, 255, 255, 0.05);
                width: 100%;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            }

            .voice-clone-content .stt-checkbox {
                width: 20px;
                height: 20px;
                cursor: pointer;
                accent-color: #4CAF50;
            }

            .voice-clone-content .progress-section {
                margin: 15px 0;
                display: flex;
                flex-direction: column;
                gap: 5px;
            }

            .voice-clone-content .progress-bar {
                background-color: rgba(255, 255, 255, 0.2);
                border-radius: 4px;
                height: 8px;
                overflow: hidden;
            }

            .voice-clone-content .progress-fill {
                background-color: #4CAF50;
                height: 100%;
                width: 0%;
                border-radius: 4px;
                transition: width 0.3s;
            }

            .voice-clone-content .progress-text {
                font-size: 13px;
                opacity: 0.8;
                text-align: center;
            }

            .voice-clone-content .action-buttons {
                display: flex;
                gap: 12px;
                margin-top: 15px;
            }

            .voice-clone-content .clone-button,
            .voice-clone-content .cancel-button {
                flex: 1;
                min-width: 0;
            }

            .voice-clone-content .clone-button {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 15px;
                font-weight: 500;
                transition: all 0.2s ease;
            }

            .voice-clone-content .clone-button:hover:not(:disabled) {
                background-color: #45a049;
                transform: translateY(-2px);
            }

            .voice-clone-content .clone-button:disabled {
                background-color: rgba(76, 175, 80, 0.4);
                cursor: not-allowed;
            }

            .voice-clone-content .cancel-button {
                background-color: transparent;
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                padding: 10px 20px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 15px;
                font-weight: 500;
                transition: all 0.2s ease;
            }

            .voice-clone-content .cancel-button:hover {
                background-color: rgba(255, 255, 255, 0.1);
                border-color: rgba(255, 255, 255, 0.5);
            }

            /* Advanced parameters styles */
            .voice-clone-content .advanced-params-section {
                background-color: rgba(0, 0, 0, 0.2);
                border-radius: 12px;
                padding: 15px;
                margin: 15px 0;
                scroll-margin-top: 20px;
            }

            .voice-clone-content .advanced-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
            }

            .voice-clone-content .advanced-header h3 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
            }

            .voice-clone-content .toggle-advanced {
                background-color: rgba(0, 120, 255, 0.7);
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                transition: all 0.2s ease;
            }

            .voice-clone-content .toggle-advanced:hover {
                background-color: rgba(0, 120, 255, 0.9);
            }

            .voice-clone-content .param-row {
                margin-bottom: 12px;
                position: relative;
            }

            .voice-clone-content .param-row label {
                display: block;
                margin-bottom: 5px;
                font-size: 14px;
                display: flex;
                justify-content: space-between;
            }

            .voice-clone-content .param-slider {
                width: 100%;
                height: 6px;
                -webkit-appearance: none;
                appearance: none;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 3px;
                outline: none;
            }

            .voice-clone-content .param-slider::-webkit-slider-thumb {
                -webkit-appearance: none;
                appearance: none;
                width: 16px;
                height: 16px;
                border-radius: 50%;
                background: #4CAF50;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .voice-clone-content .param-slider::-moz-range-thumb {
                width: 16px;
                height: 16px;
                border-radius: 50%;
                background: #4CAF50;
                cursor: pointer;
                transition: all 0.2s ease;
                border: none;
            }

            .voice-clone-content .param-slider::-webkit-slider-thumb:hover {
                background: #45a049;
                transform: scale(1.2);
            }

            .voice-clone-content .param-slider::-moz-range-thumb:hover {
                background: #45a049;
                transform: scale(1.2);
            }

            .voice-clone-content .param-tooltip {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.7);
                margin-top: 4px;
                font-style: italic;
            }

            .voice-clone-content .param-value {
                font-weight: bold;
                color: #4CAF50;
            }

            /* Preview controls */
            .voice-clone-content .preview-controls {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 15px;
                margin-top: 15px;
                margin-bottom: 15px;
            }

            .voice-clone-content .preview-original-button,
            .voice-clone-content .preview-clone-button {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 30px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
                flex: 1;
                max-width: 180px;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            }

            .voice-clone-content .preview-original-button {
                background-color: #4CAF50;
            }

            .voice-clone-content .preview-original-button:hover:not(:disabled) {
                background-color: #45a049;
                transform: translateY(-2px);
                box-shadow: 0 4px 10px rgba(76, 175, 80, 0.4);
            }

            .voice-clone-content .preview-clone-button:hover:not(:disabled) {
                background-color: #1976D2;
                transform: translateY(-2px);
                box-shadow: 0 4px 10px rgba(33, 150, 243, 0.4);
            }

            .voice-clone-content .preview-original-button:disabled,
            .voice-clone-content .preview-clone-button:disabled {
                background-color: rgba(33, 150, 243, 0.3);
                cursor: not-allowed;
                box-shadow: none;
                opacity: 0.7;
            }

            .voice-clone-content .preview-original-button:disabled {
                background-color: rgba(76, 175, 80, 0.3);
            }

            .voice-clone-content .preview-original-button.playing,
            .voice-clone-content .preview-clone-button.playing {
                background-color: #f44336;
            }

            .voice-clone-content .preview-status {
                font-size: 13px;
                color: rgba(255, 255, 255, 0.8);
            }
        `;
        document.head.appendChild(modalStyle);
        voiceCloneModal.innerHTML = `
            <div class="voice-clone-content">
                <div class="modal-header">
                    <h3>Voice Cloning</h3>
                </div>
                <div class="prompt-section">
                    <h3>Please read the following text:</h3>
                    <div class="prompt-container">
                        <p class="prompt-text" title="Click to edit">中华文明源远流长，智慧结晶闪闪发光。江南水乡柔情似水，北国风光雄浑壮丽。科技创新推动发展，人工智能改变生活。</p>
                    </div>
                </div>
                <div class="recording-section">
                    <div class="waveform-container">
                        <canvas id="voiceWaveform"></canvas>
                    </div>
                    <div class="recording-grid">
                        <div class="recording-controls">
                            <button class="record-button">
                                <span class="record-icon">●</span>
                                <span class="record-text">Start Recording</span>
                            </button>
                        </div>
                        <div class="timer-container">
                            <div class="timer">00:00</div>
                        </div>
                        <div class="file-upload">
                            <p>Or upload an audio file:</p>
                            <div class="file-input-container">
                                <input type="file" class="voice-file-input" accept="audio/*">
                            </div>
                        </div>
                        <div class="stt-option-container">
                            <div class="stt-option">
                                <input type="checkbox" class="stt-checkbox" id="use-stt" checked>
                                <label for="use-stt">Use speech recognition to get text from audio (for both recording and file upload)</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Parameters Section -->
                <div class="advanced-params-section">
                    <div class="advanced-header">
                        <h3>Advanced Parameters</h3>
                        <button class="toggle-advanced">Show</button>
                    </div>
                    <div class="advanced-controls" style="display: none;">
                        <div class="param-row">
                            <label for="temperature">Temperature: <span class="param-value">0.9</span></label>
                            <input type="range" id="temperature" class="param-slider" min="0.1" max="1.5" step="0.05" value="0.9">
                            <div class="param-tooltip">Controls randomness: higher values produce more varied outputs</div>
                        </div>
                        <div class="param-row">
                            <label for="top_p">Top P: <span class="param-value">0.95</span></label>
                            <input type="range" id="top_p" class="param-slider" min="0.1" max="1.0" step="0.05" value="0.95">
                            <div class="param-tooltip">Controls diversity: lower values make output more focused</div>
                        </div>
                        <div class="param-row">
                            <label for="top_k">Top K: <span class="param-value">50</span></label>
                            <input type="range" id="top_k" class="param-slider" min="1" max="100" step="1" value="50">
                            <div class="param-tooltip">Limits vocabulary choices: lower values make output more predictable</div>
                        </div>
                        <div class="param-row">
                            <label for="window_size">Window Size: <span class="param-value">50</span></label>
                            <input type="range" id="window_size" class="param-slider" min="10" max="100" step="5" value="50">
                            <div class="param-tooltip">Context window size: higher values consider more context</div>
                        </div>
                    </div>
                </div>

                <div class="preview-section">
                    <h3>Voice Preview</h3>
                    <p>Listen to your voice before and after cloning.</p>
                    <div class="preview-controls">
                        <button class="preview-original-button" disabled>
                            <span class="preview-icon">🎤</span>
                            <span class="preview-text">Original Audio</span>
                        </button>
                        <button class="preview-clone-button" disabled>
                            <span class="preview-icon">🎧</span>
                            <span class="preview-text">Cloned Voice</span>
                        </button>
                    </div>
                    <div class="preview-status" style="margin-top: 10px; min-height: 20px; text-align: center;"></div>
                </div>

                <div class="progress-section">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="progress-text">Recording Progress: 0%</div>
                </div>
                <div class="action-buttons">
                    <button class="clone-button" disabled>Clone Voice</button>
                    <button class="cancel-button">Cancel</button>
                </div>
            </div>
        `;
        document.body.appendChild(voiceCloneModal);
        this.addVoiceCloningStyles();
        this.setupVoiceCloningHandlers(voiceCloneModal);
        voiceMenu.appendChild(voiceCloneButton);

        // Toggle modal on Clone Voice button click
        voiceCloneButton.addEventListener('click', () => {
            const isVisible = voiceCloneModal.style.display === 'flex';
            if (isVisible) {
                voiceCloneModal.style.display = 'none';
                // Remove the overlay if it exists
                if (voiceCloneModal.dataset.overlayId) {
                    const overlay = document.getElementById(voiceCloneModal.dataset.overlayId);
                    if (overlay) {
                        overlay.remove();
                    }
                    delete voiceCloneModal.dataset.overlayId;
                }
            } else {
                // First hide the voice menu completely
                voiceMenu.style.display = 'none';
                // Then show the modal
                voiceCloneModal.style.display = 'flex';

                // We don't need a transparent overlay anymore since we fixed the modal's z-index
                // and set pointer-events to none on the overlay

                // Set a unique ID for tracking modal state
                voiceCloneModal.dataset.modalId = 'voice-clone-modal-' + Date.now();

                // Reset state when opening the modal
                self.clonedVoiceFile = null;

                // Make sure the preview buttons are in the correct state
                const previewSection = voiceCloneModal.querySelector('.preview-section');
                if (previewSection) {
                    const previewOriginalBtn = previewSection.querySelector('.preview-original-button');
                    const previewCloneBtn = previewSection.querySelector('.preview-clone-button');
                    const previewStatus = previewSection.querySelector('.preview-status');

                    if (previewOriginalBtn) {
                        previewOriginalBtn.disabled = true;
                    }

                    if (previewCloneBtn) {
                        previewCloneBtn.disabled = true;
                    }

                    // Initialize the preview status
                    if (previewStatus) {
                        previewStatus.innerHTML = '';
                        previewStatus.appendChild(document.createTextNode('Record or upload audio to begin'));
                    }
                }

                // Make sure the voice buffer list is updated when opening the modal
                if (self.voiceBuffer && self.voiceBuffer.length > 0) {
                    self.sortVoiceBufferList();
                    console.log('[TalkingHeadUI] Voice buffer list updated on modal open');
                }
            }
        });

        // Cancel button closes the modal
        const cancelButton = voiceCloneModal.querySelector('.cancel-button');
        cancelButton.addEventListener('click', () => {
            voiceCloneModal.style.display = 'none';

            // Remove the overlay if it exists
            if (voiceCloneModal.dataset.overlayId) {
                const overlay = document.getElementById(voiceCloneModal.dataset.overlayId);
                if (overlay) {
                    overlay.remove();
                }
                delete voiceCloneModal.dataset.overlayId;
            }
        });

        // Expand/collapse area for other voices
        const voicesToggle = document.createElement('button');
        voicesToggle.className = 'voices-toggle';
        voicesToggle.innerHTML = 'Other Voices ▼';
        // CSS class will handle styling automatically
        voiceMenu.appendChild(voicesToggle);

        // Container for the voices list (hidden by default)
        const voicesListContainer = document.createElement('div');
        voicesListContainer.className = 'voices-list-container';
        voicesListContainer.style.display = 'none';
        voiceMenu.appendChild(voicesListContainer);

        // Expand/collapse logic
        let voicesExpanded = false;
        voicesToggle.addEventListener('click', async () => {
            voicesExpanded = !voicesExpanded;
            voicesListContainer.style.display = voicesExpanded ? 'block' : 'none';
            voicesToggle.innerHTML = voicesExpanded ? 'Other Voices ▲' : 'Other Voices ▼';
            if (voicesExpanded && voicesListContainer.childElementCount === 0) {
                // Show loading message
                voicesListContainer.innerHTML = '<div style="color:white;padding:8px 12px;text-align:center;">Loading voices...</div>';
                try {
                    const roles = await this.avatar.getSparkTTSRoles();
                    voicesListContainer.innerHTML = '';
                    if (roles && roles.length > 0) {
                        const genderMap = {
                            '余承东': 'male', '刘德华': 'male', '后羿': 'male', '周杰伦': 'male',
                            '哪吒': 'male', '徐志胜': 'male', '李靖': 'male',
                            '殷夫人': 'female', '陈鲁豫': 'female', 'Donald Trump': 'male'
                        };
                        roles.forEach(role => {
                            const voiceItem = document.createElement('button');
                            const gender = genderMap[role] || 'male';
                            const genderIcon = gender === 'male' ? '👨' : '👩';
                            voiceItem.innerHTML = `${genderIcon} ${role}`;
                            voiceItem.dataset.role = role;
                            voiceItem.dataset.gender = gender;
                            voiceItem.className = 'voice-item';
                            voiceItem.style.cssText = `
                                background-color: rgba(30, 30, 30, 0.8);
                                border: none;
                                color: white;
                                padding: 8px 12px;
                                border-radius: 4px;
                                cursor: pointer;
                                text-align: left;
                                width: 100%;
                                margin-bottom: 2px;
                            `;
                            voiceItem.addEventListener('click', () => {
                                // Voice setting now handled by @/agent/tools/tts.js
                                console.log(`[TalkingHeadUI] Voice selection managed by agent system: ${role} (${gender})`);

                                voiceButton.innerHTML = `🔊 ${role.length > 10 ? role.substring(0, 10) + '...' : role}`;
                                voiceMenu.style.display = 'none';
                                statusSpan.textContent = `Voice: ${role}`;
                            });
                            voicesListContainer.appendChild(voiceItem);
                        });
                    } else {
                        voicesListContainer.innerHTML = '<div style="color:white;padding:8px 12px;text-align:center;">No voices available</div>';
                    }
                } catch (error) {
                    voicesListContainer.innerHTML = '<div style="color:red;padding:8px 12px;text-align:center;">Error loading voices</div>';
                }
            }
        });

        // Add voice buffer container
        const voiceBufferContainer = document.createElement('div');
        voiceBufferContainer.className = 'voice-buffer-container';
        // CSS class will handle styling automatically

        const voiceBufferTitle = document.createElement('div');
        voiceBufferTitle.className = 'voice-buffer-title';
        voiceBufferTitle.textContent = 'Voice Buffer';
        // CSS class will handle styling automatically

        const clearBufferButton = document.createElement('button');
        clearBufferButton.textContent = 'Clear All';
        clearBufferButton.style.cssText = `
            background: none;
            border: none;
            color: #ff6b6b;
            font-size: 12px;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
        `;
        clearBufferButton.addEventListener('click', () => this.clearVoiceBuffer());
        voiceBufferTitle.appendChild(clearBufferButton);

        voiceBufferContainer.appendChild(voiceBufferTitle);

        const voiceBufferList = document.createElement('div');
        voiceBufferList.className = 'voice-buffer-list';
        voiceBufferList.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 8px;
            max-height: 200px;
            overflow-y: auto;
        `;
        voiceBufferContainer.appendChild(voiceBufferList);

        voiceMenu.appendChild(voiceBufferContainer);

        // Add all elements to control container (conditionally)
        if (speechButton) controlContainer.appendChild(speechButton);
        if (poseButton) {
            controlContainer.appendChild(poseButton);
            controlContainer.appendChild(poseMenu);
        }
        if (animationButton) {
            controlContainer.appendChild(animationButton);
            controlContainer.appendChild(animationMenu);
        }
        if (voiceButton) {
            controlContainer.appendChild(voiceButton);
            controlContainer.appendChild(voiceMenu);
        }
        if (listenButton) controlContainer.appendChild(listenButton);
        if (videoButton) controlContainer.appendChild(videoButton);
        controlContainer.appendChild(statusSpan);
        document.body.appendChild(controlContainer);

        // Rely on global CSS in app/viewer/styles/viewer.css for layout and scaling

        console.log('[TalkingHeadUI] ✅ CONTROL PANEL CREATED WITH FORCE STYLES!');
        console.log('[TalkingHeadUI] Control container and statusSpan added to DOM:', {
            containerInDOM: document.body.contains(controlContainer),
            statusSpanInContainer: controlContainer.contains(statusSpan),
            statusSpanText: statusSpan.textContent,
            containerChildren: controlContainer.children.length,
            containerStyle: controlContainer.style.cssText,
            containerClass: controlContainer.className,
            containerPosition: controlContainer.getBoundingClientRect()
        });

        // Add periodic visibility check to prevent disappearance
        const visibilityCheck = setInterval(() => {
            if (document.body.contains(controlContainer)) {
                const computedStyle = window.getComputedStyle(controlContainer);
                const isVisible = computedStyle.display !== 'none' &&
                    computedStyle.visibility !== 'hidden' &&
                    computedStyle.opacity !== '0';

                if (!isVisible) {
                    console.log('[TalkingHeadUI] ⚠️ CONTROL PANEL BECAME INVISIBLE - FORCING VISIBILITY!');
                    controlContainer.style.setProperty('display', 'flex', 'important');
                    controlContainer.style.setProperty('opacity', '1', 'important');
                    controlContainer.style.setProperty('visibility', 'visible', 'important');
                    controlContainer.style.setProperty('pointer-events', 'auto', 'important');
                }
            } else {
                console.log('[TalkingHeadUI] ⚠️ CONTROL PANEL REMOVED FROM DOM!');
                clearInterval(visibilityCheck);
            }
        }, 1000); // Check every second

        // Store the interval for cleanup
        this.visibilityCheckInterval = visibilityCheck;

        // Enhanced voice button click handler with comprehensive visibility forcing
        voiceButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            console.log('[TalkingHeadUI] Voice button clicked - starting comprehensive visibility fix');

            // Close other menus first
            this.hideAllDropdowns();

            // Toggle voice menu with comprehensive visibility forcing
            const isShowing = voiceMenu.classList.contains('show') || voiceMenu.style.display === 'flex';
            console.log('[TalkingHeadUI] Voice menu currently showing:', isShowing);

            if (isShowing) {
                this.hideDropdown(voiceMenu);
                console.log('[TalkingHeadUI] Voice menu force hidden');
            } else {
                this.showDropdown(voiceMenu, voiceButton);
                console.log('[TalkingHeadUI] Voice menu force shown');

                // Show success notification
                this.showNotification('Voice menu opened - comprehensive visibility applied!');
            }
        });

        // Enhanced close voice menu when clicking outside with comprehensive approach
        document.addEventListener('click', (e) => {
            if (!voiceButton.contains(e.target) && !voiceMenu.contains(e.target)) {
                this.hideDropdown(voiceMenu);
            }
        });

        // Debug toggle button removed

        return this.controls;
    }

    /**
     * Comprehensive dropdown visibility management methods
     */

    /**
     * Hide all dropdown menus with GLOBAL RESET
     */
    hideAllDropdowns() {
        const allDropdowns = document.querySelectorAll('.talking-head-dropdown, .voice-menu');
        allDropdowns.forEach(dropdown => {
            this.hideDropdown(dropdown);
        });
        console.log('[TalkingHeadUI] GLOBAL: All dropdowns hidden');
    }

    /**
     * Hide a specific dropdown with GLOBAL RESET
     * @param {HTMLElement} dropdown - The dropdown element to hide
     */
    hideDropdown(dropdown) {
        if (!dropdown) return;

        // Remove all visibility classes
        dropdown.classList.remove('show', 'talking-head-visible', 'force-visible');

        // Reset all styles with important overrides
        dropdown.style.setProperty('display', 'none', 'important');
        dropdown.style.setProperty('opacity', '0', 'important');
        dropdown.style.setProperty('visibility', 'hidden', 'important');
        dropdown.style.setProperty('pointer-events', 'none', 'important');
        dropdown.style.setProperty('transform', 'translateY(15px) scale(0.95)', 'important');

        console.log('[TalkingHeadUI] GLOBAL RESET: Hidden dropdown:', dropdown.className);
    }

    /**
     * Show a dropdown with GLOBAL EMERGENCY VISIBILITY
     * @param {HTMLElement} dropdown - The dropdown element to show
     * @param {HTMLElement} button - The button that triggered the dropdown
     */
    showDropdown(dropdown, button) {
        if (!dropdown || !button) return;

        console.log('[TalkingHeadUI] GLOBAL EMERGENCY DROPDOWN SHOW:', dropdown.className);

        // GLOBAL CRITICAL: Force display with maximum priority
        dropdown.style.setProperty('display', 'flex', 'important');
        dropdown.style.setProperty('opacity', '1', 'important');
        dropdown.style.setProperty('visibility', 'visible', 'important');
        dropdown.style.setProperty('pointer-events', 'auto', 'important');
        dropdown.style.setProperty('transform', 'translateY(0) scale(1)', 'important');
        dropdown.style.setProperty('position', 'fixed', 'important');
        dropdown.style.setProperty('z-index', '999999', 'important');

        // Add all visibility classes
        dropdown.classList.add('show');
        dropdown.classList.add('talking-head-visible');
        dropdown.classList.add('force-visible');

        // Simple positioning below the button with emergency override
        const buttonRect = button.getBoundingClientRect();
        dropdown.style.setProperty('left', buttonRect.left + 'px', 'important');
        dropdown.style.setProperty('top', (buttonRect.bottom + 8) + 'px', 'important');

        // Force immediate repaint
        dropdown.offsetHeight;

        console.log('[TalkingHeadUI] GLOBAL EMERGENCY: Dropdown forced visible with z-index 999999');
        console.log('[TalkingHeadUI] Final computed style:', {
            display: window.getComputedStyle(dropdown).display,
            opacity: window.getComputedStyle(dropdown).opacity,
            zIndex: window.getComputedStyle(dropdown).zIndex,
            visibility: window.getComputedStyle(dropdown).visibility
        });
    }

    /**
     * Creates a Ready Player Me iframe modal
     * @returns {Object} Modal elements and event handler
     */
    createReadyPlayerMeModal() {
        // Create modal for the iframe
        const modal = document.createElement('div');
        modal.className = 'rpm-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        `;

        // Add close button
        const closeButton = document.createElement('button');
        closeButton.textContent = '✕';
        closeButton.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            z-index: 2001;
        `;

        // Create iframe
        const iframe = document.createElement('iframe');
        iframe.className = 'rpm-frame';
        iframe.style.cssText = `
            width: 90%;
            height: 90%;
            border: none;
            border-radius: 8px;
        `;

        // Set attributes
        iframe.setAttribute('allow', 'camera *; microphone *; clipboard-write');

        // Use demo subdomain (replace with your own if you have one)
        const subdomain = 'demo';
        iframe.src = `https://${subdomain}.readyplayer.me/avatar?frameApi`;

        // Add elements to modal
        modal.appendChild(closeButton);
        modal.appendChild(iframe);

        return { modal, closeButton, iframe };
    }

    /**
     * Update the status text
     * @param {string} text - Status text to display
     */
    updateStatus(text) {
        console.log('[TalkingHeadUI] updateStatus called:', {
            text,
            hasControls: !!this.controls,
            hasStatusSpan: !!this.controls?.statusSpan,
            currentText: this.controls?.statusSpan?.textContent
        });

        if (this.controls && this.controls.statusSpan) {
            this.controls.statusSpan.textContent = text;
            console.log('[TalkingHeadUI] Status updated successfully to:', text);
        } else {
            console.warn('[TalkingHeadUI] Cannot update status - controls or statusSpan not available');
        }
    }

    /**
     * Update video button status based on current listen/video streaming state
     */
    updateVideoButtonStatus() {
        if (!this.controls || !this.controls.videoButton || !this.controls.statusSpan) {
            return;
        }

        const { statusSpan } = this.controls;

        if (this.avatar.isVideoStreaming) {
            if (this.avatar.isListening) {
                statusSpan.textContent = 'Listening with Video + Audio...';
            } else {
                statusSpan.textContent = 'Camera Open (Start listening to stream data)';
            }
        } else if (this.avatar.isListening) {
            statusSpan.textContent = 'Listening (Audio Only)';
        }
    }

    /**
     * Stop the animation update loop
     */
    stopAnimationUpdateLoop() {
        if (this.animationUpdateId) {
            console.log('[TalkingHeadUI] Stopping animation update loop');
            cancelAnimationFrame(this.animationUpdateId);
            this.animationUpdateId = null;
        }
    }

    /**
 * Start video streaming camera window (integrates with existing listen system)
 */
    async startVideoStreaming() {
        try {
            // Check camera permissions before attempting to start
            if (this.cameraPermissionDenied) {
                throw new Error('Camera permission denied. Please allow camera access and refresh the page.');
            }

            console.log('[TalkingHeadUI] Starting video streaming camera window...');

            // Get the centralized camera manager (same one used by gestures.js)
            if (!this.cameraManager) {
                // Import and get the CameraManager from viewer or create new one
                if (this.avatar.viewer && this.avatar.viewer.cameraManager) {
                    this.cameraManager = this.avatar.viewer.cameraManager;
                    console.log('[TalkingHeadUI] Using existing CameraManager from viewer');
                } else {
                    // Create new CameraManager if not available
                    const { CameraManager } = await import('../../../media/core/CameraManager.js');

                    // Use viewer container for camera manager
                    const container = this.avatar.viewer?.container || document.body;

                    this.cameraManager = new CameraManager(container, {
                        cornerPosition: 'bottom-left', // Position video on lower left side
                        video: {
                            width: 640,
                            height: 480,
                            frameRate: 15, // Lower frame rate for LLM processing
                            facingMode: 'user'
                        },
                        audio: {
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true,
                            sampleRate: 16000
                        }
                    });

                    // Initialize the camera manager
                    await this.cameraManager.initialize();
                    console.log('[TalkingHeadUI] Created and initialized new CameraManager');
                }
            }

            // Start camera using the centralized manager
            const stream = await this.cameraManager.startCamera();
            if (!stream) {
                throw new Error('Failed to start camera stream');
            }

            console.log('[TalkingHeadUI] Camera started successfully, stream active:', stream.active);

            // Show camera in corner mode for video streaming (similar to gestures.js)
            await this.cameraManager.showCamera('corner');
            console.log('[TalkingHeadUI] Camera corner view opened');

            // Set streaming state - the existing listen system will handle LLM integration
            this.avatar.isVideoStreaming = true;

            // ✅ FIX: Ensure TalkingAvatar can access the camera manager
            if (this.avatar.talkingHeadControls) {
                this.avatar.talkingHeadControls.cameraManager = this.cameraManager;
                console.log('[TalkingHeadUI] 🔗 Connected camera manager to TalkingAvatar controls');
            } else {
                console.warn('[TalkingHeadUI] ⚠️ TalkingAvatar controls not available for camera manager connection');
            }

            // Notify the avatar that video is now available for the existing listen system
            if (this.avatar.enableVideoInput && this.avatar.isListening) {
                // The avatar's existing media streaming will pick up the video automatically
                console.log('[TalkingHeadUI] Video stream integrated with existing listen system');
            }

            console.log('[TalkingHeadUI] Video streaming camera window started successfully');

        } catch (error) {
            console.error('[TalkingHeadUI] Error starting video streaming:', error);

            // Check if this is a camera permission error and update state
            if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError' ||
                error.message.includes('permission') || error.message.includes('Permission')) {
                console.log('[TalkingHeadUI] Camera permission error detected - updating UI state');
                this.cameraPermissionDenied = true;

                // Disable video button in the UI
                this._disableVideoButton();
            }

            // Cleanup on error
            if (this.cameraManager && this.cameraManager.isCameraActive()) {
                try {
                    await this.cameraManager.hideCamera();
                } catch (cleanupError) {
                    console.warn('[TalkingHeadUI] Error during cleanup:', cleanupError);
                }
            }

            throw error;
        }
    }

    /**
     * Disable video button when camera permission is denied
     * @private
     */
    _disableVideoButton() {
        if (this.controls && this.controls.videoButton) {
            const videoButton = this.controls.videoButton;
            videoButton.disabled = true;
            videoButton.innerHTML = '📹 Video (N/A)';
            videoButton.style.cssText = `
                padding: 8px 16px;
                border-radius: 8px;
                background-color: rgba(100, 100, 100, 0.5);
                border: none;
                color: rgba(255, 255, 255, 0.5);
                font-size: 14px;
                display: flex;
                align-items: center;
                gap: 6px;
                cursor: not-allowed;
            `;
            videoButton.title = 'Camera permission denied or no camera available';
            console.log('[TalkingHeadUI] Video button disabled due to camera permission issues');
        }
    }

    /**
     * Stop video streaming camera window
     */
    async stopVideoStreaming() {
        try {
            console.log('[TalkingHeadUI] Stopping video streaming camera window...');

            // Hide camera using the centralized manager (similar to gestures.js)
            if (this.cameraManager && this.cameraManager.isCameraActive()) {
                await this.cameraManager.hideCamera();
                console.log('[TalkingHeadUI] Camera hidden successfully');
            }

            // Clear streaming state
            this.avatar.isVideoStreaming = false;

            console.log('[TalkingHeadUI] Video streaming camera window stopped successfully');

        } catch (error) {
            console.error('[TalkingHeadUI] Error stopping video streaming:', error);
            throw error;
        }
    }







    /**
     * Clean up UI elements
     */
    dispose() {
        // Clean up video streaming if active
        if (this.avatar.isVideoStreaming) {
            this.stopVideoStreaming().catch(error => {
                console.error('[TalkingHeadUI] Error stopping video streaming during dispose:', error);
            });
        }

        // Clean up animation resources
        if (this.skeletalAnimationManager) {
            this.skeletalAnimationManager.stopAnimation();
        }

        // Stop animation update loop
        this.stopAnimationUpdateLoop();

        // Remove UI elements
        if (this.controls && this.controls.container) {
            document.body.removeChild(this.controls.container);
        }
        this.controls = null;
    }

    /**
     * Add styles for voice cloning UI
     */
    addVoiceCloningStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .voice-buffer-container {
                margin: 10px 0;
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                padding: 10px;
            }

            .voice-buffer-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
            }

            .voice-buffer-title {
                font-size: 14px;
                color: white;
                margin: 0;
            }

            .voice-buffer-list {
                max-height: 300px;
                overflow-y: auto;
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .voice-buffer-item {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 6px;
                padding: 10px;
                transition: background-color 0.2s;
            }

            .voice-buffer-item:hover {
                background: rgba(255, 255, 255, 0.1);
            }

            .voice-buffer-item.active-voice {
                background: rgba(76, 175, 80, 0.2);
                border-left: 4px solid #4CAF50;
            }

            .voice-buffer-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
            }

            .timestamp {
                color: rgba(255, 255, 255, 0.7);
                font-size: 12px;
            }

            .controls {
                display: flex;
                gap: 4px;
            }

            .controls button {
                background: none;
                border: none;
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                transition: background-color 0.2s;
            }

            .controls button:hover {
                background: rgba(255, 255, 255, 0.1);
            }

            .controls button:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            .voice-preview {
                color: rgba(255, 255, 255, 0.7);
                font-size: 12px;
                margin-top: 4px;
            }

            .debug-info {
                display: none;
                background: rgba(0, 0, 0, 0.2);
                padding: 8px;
                margin-top: 8px;
                border-radius: 4px;
                font-family: monospace;
                font-size: 11px;
                color: rgba(255, 255, 255, 0.8);
                overflow-x: auto;
            }

            .debug-info pre {
                margin: 0;
                white-space: pre-wrap;
            }

            /* Debug toggle styles removed */

            .favorite-btn {
                color: #ccc;
            }

            .favorite-btn.active {
                color: #ffd700;
            }

            .preview-btn {
                color: #4CAF50;
            }

            .use-btn {
                color: #2196F3;
            }

            .delete-btn {
                color: #f44336;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Simplified transcription UI update - AliyunBailianChatModel handles audio transcription natively
     * @param {Blob} audioData - Audio data (passed to agent system for processing)
     * @param {Object} options - UI update options
     * @returns {Promise<string|null>} - Default text for UI or null
     */
    async transcribeAudioAndUpdateUI(audioData, options = {}) {
        // Note: audioData parameter preserved for compatibility - actual transcription handled by AliyunBailianChatModel
        const self = this;
        const {
            progressText,
            progressFill,
            promptText,
            modalContent,
            fileName = null
        } = options;

        try {
            // Update UI to show processing state
            if (progressText) progressText.textContent = 'Audio ready for agent processing...';
            if (progressFill) progressFill.style.width = '30%';

            // AliyunBailianChatModel handles transcription natively via realtime WebSocket API
            console.log('[TalkingHeadUI] Audio transcription for voice cloning handled by AliyunBailianChatModel with enableTranscription=true');

            // Create a simple UI indication that audio is ready for voice cloning
            const transcriptionContainer = document.createElement('div');
            transcriptionContainer.className = 'transcription-container';
            transcriptionContainer.style.cssText = `
                margin-top: 15px;
                padding: 12px;
                background-color: rgba(0, 0, 0, 0.05);
                border-radius: 8px;
                border-left: 4px solid #4CAF50;
                font-size: 14px;
                transition: all 0.3s ease;
            `;

            const transcriptionHeader = document.createElement('div');
            transcriptionHeader.style.cssText = `
                font-weight: bold;
                margin-bottom: 8px;
                color: #4CAF50;
            `;
            transcriptionHeader.textContent = '🎤 Audio Ready for Voice Cloning';

            const transcriptionText = document.createElement('div');
            transcriptionText.style.cssText = `
                white-space: pre-wrap;
                word-break: break-word;
                line-height: 1.5;
                color: #666;
                font-style: italic;
            `;
            transcriptionText.textContent = 'Audio will be transcribed automatically by AliyunBailianChatModel for voice cloning analysis.';

            transcriptionContainer.appendChild(transcriptionHeader);
            transcriptionContainer.appendChild(transcriptionText);

            // Clear previous transcription if exists
            const existingTranscription = document.querySelector('.transcription-container');
            if (existingTranscription) {
                existingTranscription.remove();
            }

            // Add to the modal content
            if (modalContent) {
                modalContent.appendChild(transcriptionContainer);
            } else {
                const defaultModalContent = document.querySelector('.modal-content');
                if (defaultModalContent) {
                    defaultModalContent.appendChild(transcriptionContainer);
                }
            }

            // Update progress
            if (progressText) progressText.textContent = 'Audio ready for voice cloning.';
            if (progressFill) progressFill.style.width = '50%';

            // Return default text for voice cloning UI compatibility
            const defaultText = '这是我声音的样本。'; // Default Chinese text
            if (promptText) {
                promptText.textContent = defaultText;
                self.showNotification('Audio ready. Agent will handle transcription automatically.', false, 3000);
            }

            return defaultText;

        } catch (error) {
            console.error('[TalkingHeadUI] Audio processing UI error:', error);
            self.showNotification('Audio processing ready. Agent system will handle transcription.', false);

            if (progressText) {
                if (fileName) {
                    progressText.textContent = `File selected: ${fileName} (ready for agent processing)`;
                } else {
                    progressText.textContent = 'Recording complete. Ready for agent processing.';
                }
            }

            if (progressFill) progressFill.style.width = '30%';
            return '这是我声音的样本。'; // Fallback default text
        }
    }

    /**
     * Setup event handlers for voice cloning UI
     */
    setupVoiceCloningHandlers(modal) {
        // State variables
        let mediaRecorder = null;
        let audioChunks = [];
        let recordingStartTime = null;
        let timerInterval = null;
        let audioContext = null;
        let analyser = null;

        // No voice cloning parameters needed anymore

        // Get UI elements
        const recordButton = modal.querySelector('.record-button');
        const recordText = recordButton.querySelector('.record-text');
        const timer = modal.querySelector('.timer');
        const progressFill = modal.querySelector('.progress-fill');
        const progressText = modal.querySelector('.progress-text');
        const cloneButton = modal.querySelector('.clone-button');
        const promptText = modal.querySelector('.prompt-text');
        const sttCheckbox = modal.querySelector('.stt-checkbox');
        sttCheckbox.checked = false;
        const fileInput = modal.querySelector('.voice-file-input');
        const cancelButton = modal.querySelector('.cancel-button');

        // Advanced parameters elements - no longer needed but keep the toggle for UI consistency
        const toggleAdvancedBtn = modal.querySelector('.toggle-advanced');
        const advancedControls = modal.querySelector('.advanced-controls');

        // Preview button
        const previewButton = modal.querySelector('.preview-clone-button');
        const previewStatus = modal.querySelector('.preview-status');

        // Store reference to this for use in callbacks
        const self = this;

        // Setup advanced parameters toggle
        if (toggleAdvancedBtn && advancedControls) {
            toggleAdvancedBtn.addEventListener('click', () => {
                const isVisible = advancedControls.style.display !== 'none';
                advancedControls.style.display = isVisible ? 'none' : 'block';
                toggleAdvancedBtn.textContent = isVisible ? 'Show' : 'Hide';
            });
        }

        // Setup prompt text click to edit
        if (promptText) {
            // Make prompt text editable on click
            promptText.addEventListener('click', () => {
                // Only start editing if not already in edit mode
                if (promptText.contentEditable !== 'true') {
                    // Edit mode - start editing
                    promptText.contentEditable = 'true';
                    promptText.classList.add('editable');
                    promptText.focus();

                    // Place cursor at the end of the text
                    const range = document.createRange();
                    const selection = window.getSelection();
                    range.selectNodeContents(promptText);
                    range.collapse(false);
                    selection.removeAllRanges();
                    selection.addRange(range);

                    // Disable preview button while editing
                    if (previewButton) {
                        previewButton.disabled = true;
                    }
                }
            });

            // Handle blur event to save changes
            promptText.addEventListener('blur', () => {
                if (promptText.contentEditable === 'true') {
                    // Save mode - finish editing
                    promptText.contentEditable = 'false';
                    promptText.classList.remove('editable');

                    // Enable preview button only if we have a reference audio file
                    if (self.reference_audio_file && previewButton) {
                        previewButton.disabled = false;
                    } else {
                        previewButton.disabled = true;
                    }
                }
            });

            // Handle Enter key to save and exit edit mode
            promptText.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey && promptText.contentEditable === 'true') {
                    e.preventDefault();
                    promptText.blur(); // Trigger the blur event to save
                }
            });
        }

        // No parameter sliders needed anymore since we're not using hyperparameters

        // Get the original audio preview button
        const previewOriginalButton = modal.querySelector('.preview-original-button');

        // Setup original audio preview button
        if (previewOriginalButton) {
            previewOriginalButton.addEventListener('click', async () => {
                if (!self.voiceFile) {
                    self.showNotification('Please record or upload a voice file first', true);
                    return;
                }

                // Stop any currently playing audio
                if (self.currentPreviewAudio) {
                    self.currentPreviewAudio.pause();
                    self.currentPreviewAudio = null;

                    // Reset both buttons if they were in playing state
                    previewOriginalButton.classList.remove('playing');
                    previewOriginalButton.innerHTML = '<span class="preview-icon">🎤</span><span class="preview-text">Original Audio</span>';

                    if (previewButton) {
                        previewButton.classList.remove('playing');
                        previewButton.innerHTML = '<span class="preview-icon">🎧</span><span class="preview-text">Cloned Voice</span>';
                    }

                    return; // Exit if we're just stopping playback
                }

                try {
                    // Set button to playing state
                    previewOriginalButton.classList.add('playing');
                    previewOriginalButton.innerHTML = '<span class="preview-icon">⏹️</span><span class="preview-text">Stop</span>';

                    // Create audio URL from the voice file
                    const audioUrl = URL.createObjectURL(self.voiceFile);
                    const audioPlayer = document.createElement('audio');
                    audioPlayer.src = audioUrl;

                    // Store reference for stopping
                    self.currentPreviewAudio = audioPlayer;

                    // Set up ended event to reset the button
                    audioPlayer.onended = () => {
                        previewOriginalButton.classList.remove('playing');
                        previewOriginalButton.innerHTML = '<span class="preview-icon">🎤</span><span class="preview-text">Original Audio</span>';
                        self.currentPreviewAudio = null;
                    };

                    // Play the audio
                    await audioPlayer.play();

                    // Update status
                    previewStatus.innerHTML = '';
                    previewStatus.appendChild(document.createTextNode('Playing original audio...'));

                } catch (error) {
                    console.error('[TalkingHeadUI] Error playing original audio:', error);
                    previewStatus.innerHTML = '';
                    previewStatus.appendChild(document.createTextNode(`Error: ${error.message}`));
                    self.showNotification(`Audio playback error: ${error.message}`, true);

                    // Reset button state
                    previewOriginalButton.classList.remove('playing');
                    previewOriginalButton.innerHTML = '<span class="preview-icon">🎤</span><span class="preview-text">Original Audio</span>';
                    self.currentPreviewAudio = null;
                }
            });
        }

        // Setup cloned voice preview button
        if (previewButton) {
            previewButton.addEventListener('click', async () => {
                if (!self.voiceFile) {
                    self.showNotification('Please record or upload a voice file first', true);
                    return;
                }

                // Only play if a reference audio file exists
                if (!self.reference_audio_file) {
                    self.showNotification('Please clone the voice first before previewing', true);
                    return;
                }

                // Stop any currently playing audio
                if (self.currentPreviewAudio) {
                    self.currentPreviewAudio.pause();
                    self.currentPreviewAudio = null;

                    // Reset both buttons if they were in playing state
                    if (previewOriginalButton) {
                        previewOriginalButton.classList.remove('playing');
                        previewOriginalButton.innerHTML = '<span class="preview-icon">🎤</span><span class="preview-text">Original Audio</span>';
                    }

                    previewButton.classList.remove('playing');
                    previewButton.innerHTML = '<span class="preview-icon">🎧</span><span class="preview-text">Cloned Voice</span>';

                    return; // Exit if we're just stopping playback
                }

                try {
                    // Set button to loading state
                    previewButton.disabled = true;
                    previewStatus.textContent = 'Loading cloned voice preview...';

                    // Preview the voice using the TTS service
                    const ttsService = self.avatar.ttsServiceInstance;
                    if (!ttsService) {
                        throw new Error('TTS service not initialized');
                    }

                    // Use a sample text for preview
                    const previewText = "声音克隆技术展现非凡魅力，人工智能创造无限可能。传统文化薪火相传，现代科技日新月异。山川河流壮美如画，诗词歌赋韵味悠长。";

                    // Get the preview result using the previewVoice method
                    const previewResult = await ttsService.previewVoice(previewText, {
                        reference_audio_file: self.reference_audio_file,
                        roleName: self.avatar.name
                    });

                    if (!previewResult.success) {
                        throw new Error(`Failed to preview voice: ${previewResult.error}`);
                    }

                    // Create an audio player with the preview result
                    const audioPlayer = document.createElement('audio');
                    audioPlayer.src = previewResult.audioUrl;

                    // Store reference for stopping
                    self.currentPreviewAudio = audioPlayer;

                    // Set up ended event to reset the button
                    audioPlayer.onended = () => {
                        previewButton.classList.remove('playing');
                        previewButton.innerHTML = '<span class="preview-icon">🎧</span><span class="preview-text">Cloned Voice</span>';
                        self.currentPreviewAudio = null;
                    };

                    // Set button to playing state
                    previewButton.classList.add('playing');
                    previewButton.innerHTML = '<span class="preview-icon">⏹️</span><span class="preview-text">Stop</span>';

                    // Play the audio
                    await audioPlayer.play();

                    // Update status
                    previewStatus.innerHTML = '';
                    previewStatus.appendChild(document.createTextNode('Playing cloned voice preview...'));

                    // Enable the clone button
                    cloneButton.disabled = false;
                } catch (error) {
                    console.error('[TalkingHeadUI] Error in voice preview:', error);
                    previewStatus.innerHTML = '';
                    previewStatus.appendChild(document.createTextNode(`Error: ${error.message}`));
                    self.showNotification(`Voice preview error: ${error.message}`, true);

                    // Reset button state
                    previewButton.classList.remove('playing');
                    previewButton.innerHTML = '<span class="preview-icon">🎧</span><span class="preview-text">Cloned Voice</span>';
                } finally {
                    previewButton.disabled = false;
                }
            });
        }

        // Initialize canvas for waveform
        const canvas = document.getElementById('voiceWaveform');
        if (canvas) {
            // Set canvas dimensions to match container
            const container = modal.querySelector('.waveform-container');
            if (container) {
                canvas.width = container.clientWidth - 20; // Account for padding
                canvas.height = 60;
            } else {
                canvas.width = 500;
                canvas.height = 60;
            }

            const canvasCtx = canvas.getContext('2d');

            // Draw initial empty waveform
            canvasCtx.fillStyle = 'rgb(20, 20, 20)';
            canvasCtx.fillRect(0, 0, canvas.width, canvas.height);
            canvasCtx.lineWidth = 2;
            canvasCtx.strokeStyle = 'rgb(76, 175, 80)';
            canvasCtx.beginPath();
            canvasCtx.moveTo(0, canvas.height / 2);
            canvasCtx.lineTo(canvas.width, canvas.height / 2);
            canvasCtx.stroke();
        }

        // Record button handler
        recordButton.addEventListener('click', () => {
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                stopRecording();
            } else {
                startRecording();
            }
        });

        // File input handler
        fileInput.addEventListener('change', async (e) => {
            const file = e.target.files[0];
            if (file) {
                // Accept any audio file format
                if (!file.type.startsWith('audio/')) {
                    console.warn(`[TalkingHeadUI] File might not be an audio file: ${file.type}`);
                    // Continue anyway, just log a warning
                }

                try {
                    // Verify file size (e.g., max 10MB)
                    const maxSize = 10 * 1024 * 1024; // 10MB
                    if (file.size > maxSize) {
                        throw new Error(`File size exceeds maximum limit of 10MB`);
                    }

                    // Convert file to Blob with normalized audio format
                    // Use a generic audio type to ensure consistent processing with recording
                    const blob = new Blob([file], { type: 'audio/wav' });
                    if (!blob || blob.size === 0) {
                        throw new Error('Failed to create valid audio blob');
                    }

                    // Log file details for debugging
                    console.log(`[TalkingHeadUI] File selected: size=${blob.size} bytes, original type=${file.type}, normalized type=${blob.type}`);

                    // Store the blob for later use
                    self.voiceFile = blob;
                    console.log('[TalkingHeadUI] File selected:', blob);
                    // Enable clone button when file is selected
                    cloneButton.disabled = false;

                    // Enable only the original audio preview button
                    if (previewOriginalButton) {
                        previewOriginalButton.disabled = false;
                    }
                    // Keep the cloned voice preview button disabled until cloning is done
                    if (previewButton) {
                        previewButton.disabled = true;
                    }

                    // Clear any existing reference audio file since we're using a new source file
                    self.reference_audio_file = null;

                    // Update progress text

                    // Show preview section
                    const previewSection = modal.querySelector('.preview-section');
                    if (previewSection) {
                        previewSection.classList.add('active');

                        // Make sure the preview buttons are in the correct state
                        const previewOriginalBtn = previewSection.querySelector('.preview-original-button');
                        const previewCloneBtn = previewSection.querySelector('.preview-clone-button');

                        if (previewOriginalBtn) {
                            previewOriginalBtn.disabled = false;
                        }

                        if (previewCloneBtn) {
                            previewCloneBtn.disabled = true;
                        }
                    }

                    // Check if STT is enabled
                    sttCheckbox.checked = true;
                    if (sttCheckbox.checked) {
                        // Use the shared transcription function
                        await self.transcribeAudioAndUpdateUI(blob, {
                            progressText,
                            progressFill,
                            promptText,
                            fileName: file.name
                        });
                    }
                } catch (error) {
                    console.error('[TalkingHeadUI] Error processing audio file:', error);
                    progressText.textContent = `Error: ${error.message}`;
                    // Reset UI state
                    cloneButton.disabled = true;
                    if (previewOriginalButton) previewOriginalButton.disabled = true;
                    if (previewButton) previewButton.disabled = true;
                    self.voiceFile = null;
                }
            }
        });

        // Clone button handler
        cloneButton.addEventListener('click', async () => {
            if (!self.voiceFile) {
                self.showNotification('Please record or upload a voice file first', true);
                return;
            }

            try {
                cloneButton.disabled = true;
                progressFill.style.width = '0%';
                progressFill.style.display = 'block';
                progressText.textContent = 'Cloning voice...';

                // Get the text from the prompt
                let text = promptText.textContent.trim();

                // If using STT with uploaded file or recording and it hasn't been transcribed yet
                if (self.voiceFile && sttCheckbox.checked && !document.querySelector('.transcription-container')) {
                    try {
                        progressText.textContent = 'Transcribing audio...';
                        const sttService = self.avatar.sttServiceInstance;
                        if (!sttService) {
                            throw new Error('STT service not available');
                        }

                        // Get the current language from the avatar's voice config
                        let language = self.avatar.voiceConfig?.currentLanguage || 'auto';

                        // Ensure we're using a valid language code from the allowed list: 'auto', 'zh', 'en', 'yue', 'ja', 'ko', 'nospeech'
                        if (language === 'chinese') {
                            language = 'zh';
                            console.log('[TalkingHeadUI] Converting language "chinese" to "zh" for transcription');
                        }

                        console.log(`[TalkingHeadUI] Transcribing with language: ${language}`);

                        const transcribedText = await sttService.transcribeAudio(self.voiceFile, {
                            language: language
                        });

                        if (transcribedText) {
                            text = transcribedText;

                            // Update the prompt text with the transcribed text
                            if (promptText) {
                                promptText.textContent = transcribedText;
                            }

                            // Create a beautiful transcription display
                            const transcriptionContainer = document.createElement('div');
                            transcriptionContainer.className = 'transcription-container';
                            transcriptionContainer.style.cssText = `
                                margin-top: 15px;
                                padding: 12px;
                                background-color: rgba(0, 0, 0, 0.05);
                                border-radius: 8px;
                                border-left: 4px solid #4CAF50;
                                font-size: 14px;
                                max-height: 150px;
                                overflow-y: auto;
                                transition: all 0.3s ease;
                            `;

                            const transcriptionHeader = document.createElement('div');
                            transcriptionHeader.style.cssText = `
                                font-weight: bold;
                                margin-bottom: 8px;
                                color: #4CAF50;
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                            `;
                            transcriptionHeader.innerHTML = `
                                <span>Transcribed Text</span>
                                <button class="copy-btn" style="
                                    background: none;
                                    border: none;
                                    cursor: pointer;
                                    font-size: 14px;
                                    color: #666;
                                ">📋 Copy</button>
                            `;

                            const transcriptionText = document.createElement('div');
                            transcriptionText.style.cssText = `
                                white-space: pre-wrap;
                                word-break: break-word;
                                line-height: 1.5;
                            `;
                            transcriptionText.textContent = transcribedText;

                            transcriptionContainer.appendChild(transcriptionHeader);
                            transcriptionContainer.appendChild(transcriptionText);

                            // Add copy functionality
                            transcriptionHeader.querySelector('.copy-btn').addEventListener('click', () => {
                                navigator.clipboard.writeText(transcribedText)
                                    .then(() => {
                                        self.showNotification('Text copied to clipboard!');
                                    })
                                    .catch(err => {
                                        console.error('Failed to copy text:', err);
                                        self.showNotification('Failed to copy text', true);
                                    });
                            });

                            // Clear previous transcription if exists
                            const existingTranscription = document.querySelector('.transcription-container');
                            if (existingTranscription) {
                                existingTranscription.remove();
                            }

                            // Add to the modal content
                            const modalContent = document.querySelector('.modal-content');
                            if (modalContent) {
                                modalContent.appendChild(transcriptionContainer);
                            }

                            // Update the prompt text with the transcribed text
                            if (promptText) {
                                promptText.textContent = transcribedText;

                                // Add a notification to let the user know they can edit the text
                                self.showNotification('Transcription complete. Click on the text to edit it.', false, 5000);
                            }

                            progressText.textContent = 'Audio transcribed successfully. Cloning voice...';
                        } else {
                            throw new Error('No text was transcribed');
                        }
                    } catch (error) {
                        console.error('STT Error:', error);
                        self.showNotification('Failed to transcribe audio. Using default text.', true);
                    }
                }

                // Clone the voice
                try {
                    // Update progress
                    progressFill.style.width = '20%';
                    progressText.textContent = 'Processing audio...';

                    // Get the TTS service instance
                    if (!self.avatar.ttsServiceInstance || self.avatar.ttsService !== 'sparkTTS') {
                        throw new Error('SparkTTS service not initialized');
                    }

                    const ttsService = self.avatar.ttsServiceInstance;
                    const avatarName = self.avatar.name;

                    console.log(`[TalkingHeadUI] Using avatar name for file organization: ${avatarName}`);

                    // Use the avatar name as the role name directly without generating a timestamp
                    // This allows the role name to be consistent with the seed file

                    // Update progress
                    progressFill.style.width = '30%';
                    progressText.textContent = 'Preparing audio for voice cloning...';

                    // Update progress
                    progressFill.style.width = '40%';
                    progressText.textContent = 'Optimizing audio format...';

                    // Optimize the audio for voice cloning (convert to 16kHz mono WAV)
                    console.log(`[TalkingHeadUI] Optimizing audio for voice cloning, original size: ${self.voiceFile.size} bytes, type: ${self.voiceFile.type}`);
                    const optimizedAudioBlob = await optimizeAudioForVoiceCloning(self.voiceFile);
                    console.log(`[TalkingHeadUI] Audio optimized, new size: ${optimizedAudioBlob.size} bytes, type: audio/wav`);

                    // Update progress
                    progressFill.style.width = '50%';
                    progressText.textContent = 'Cloning voice...';
                    const responseFormat = 'wav';

                    // Use the addSpeaker method to register the voice with the TTS service
                    const transcribedText = promptText.textContent.trim();
                    console.log(`[TalkingHeadUI] Adding speaker with optimized audio, size: ${optimizedAudioBlob.size} bytes, format: 16kHz mono WAV`);
                    const addSpeakerResult = await ttsService.addSpeaker(avatarName, optimizedAudioBlob, transcribedText);

                    if (!addSpeakerResult.success) {
                        throw new Error(`Failed to add speaker: ${addSpeakerResult.error}`);
                    }

                    // Preview the voice with a sample text
                    const result = await ttsService.speak(text, {
                        roleName: avatarName,
                        response_format: responseFormat
                    });

                    // Save the original audio blob for future use
                    self.reference_audio_file = self.voiceFile;

                    if (result.success) {
                        // Update progress
                        progressFill.style.width = '70%';
                        progressText.textContent = 'Saving voice files...';

                        // No temporary optimized file to clean up in this implementation

                        // We already have the avatar name from above, but let's ensure consistency
                        // by using the same avatarName variable that we used for the roleName
                        const timestamp = Date.now();

                        // Generate a hash code for the file (using timestamp as a simple hash)
                        const hashCode = timestamp.toString().substring(timestamp.toString().length - 8);

                        // Create directory if it doesn't exist
                        // Use the avatar name instead of 'default' for the directory
                        const dirPath = `assets/audio/${avatarName}`;
                        console.log(`[TalkingHeadUI] Creating directory: ${dirPath}`);

                        // Save the original audio file following seed file naming convention: {meshname}_{hash}.xxx
                        const audioPath = `/assets/audio/${avatarName}/${avatarName}_${hashCode}_original.${responseFormat}`;
                        console.log(`[TalkingHeadUI] Saving original audio to: ${audioPath}`);

                        // Use our imported uploadAudioFile utility to save the file
                        const originalUploadResult = await uploadAudioFile(self.voiceFile, audioPath);

                        if (!originalUploadResult.success) {
                            throw new Error(`Failed to save original audio file: ${originalUploadResult.error}`);
                        }

                        console.log(`[TalkingHeadUI] Original audio file saved successfully:`, originalUploadResult);

                        // Update progress
                        progressFill.style.width = '90%';
                        progressText.textContent = 'Saving cloned voice...';

                        // Handle the audio data directly as binary
                        let audioData;
                        if (result.audioData instanceof Blob) {
                            audioData = result.audioData;
                        } else if (result.audioData instanceof ArrayBuffer) {
                            audioData = new Blob([result.audioData], { type: `audio/${responseFormat}` });
                        } else if (typeof result.audioData === 'string' && result.audioData.startsWith('data:')) {
                            // If it's a data URL, convert it to a blob
                            const response = await fetch(result.audioData);
                            audioData = await response.blob();
                        } else {
                            throw new Error('Invalid audio data format received from voice cloning service');
                        }

                        // Store the reference audio file for future use
                        self.reference_audio_file = audioData;

                        // Save the reference audio file following seed file naming convention: {meshname}_{hash}.xxx
                        const referenceAudioPath = `/assets/audio/${avatarName}/${avatarName}_${hashCode}_reference.${responseFormat}`;
                        console.log(`[TalkingHeadUI] Saving reference audio file to: ${referenceAudioPath}`);

                        // Use our imported uploadAudioFile utility to save the reference audio file
                        const referenceUploadResult = await uploadAudioFile(audioData, referenceAudioPath);

                        if (!referenceUploadResult.success) {
                            throw new Error(`Failed to save reference audio file: ${referenceUploadResult.error}`);
                        }

                        console.log(`[TalkingHeadUI] Reference audio file saved successfully:`, referenceUploadResult);

                        // Make sure the preview buttons are properly enabled after saving the cloned voice file
                        const previewSectionAfterSave = modal.querySelector('.preview-section');
                        if (previewSectionAfterSave) {
                            const previewCloneBtn = previewSectionAfterSave.querySelector('.preview-clone-button');
                            if (previewCloneBtn) {
                                previewCloneBtn.disabled = false;
                                console.log('[TalkingHeadUI] Enabling preview clone button after saving cloned audio file');
                            }

                            // Update the preview status
                            const previewStatusElement = previewSectionAfterSave.querySelector('.preview-status');
                            if (previewStatusElement) {
                                previewStatusElement.innerHTML = '';
                                previewStatusElement.appendChild(document.createTextNode('Cloned voice saved successfully! Click the buttons above to preview.'));
                            }
                        }

                        // Prepare the voice configuration data
                        // This would be used for seed file storage, but we're skipping that step
                        // and directly adding to the voice buffer
                        const voiceConfigData = {
                            reference_audio_file: referenceAudioPath,
                            reference_audio_file_path: referenceAudioPath, // Add this line to save the path
                            roleName: avatarName, // Use the roleName we generated
                            timestamp: timestamp,
                            text: text,
                            // Store the reference text (transcribed text) separately
                            reference_text: transcribedText,
                            // Explicitly set favorite to false - only the favorite button should set this to true
                            favorite: false
                        };

                        // Log the voice configuration data for debugging
                        console.log('[TalkingHeadUI] Voice configuration data:', voiceConfigData);

                        // We don't need to generate a cache key anymore since we're not storing to seed file
                        // But we'll log the information for debugging purposes
                        console.log(`[TalkingHeadUI] Voice cloning complete for avatar: ${avatarName}`);
                        console.log(`[TalkingHeadUI] Role name: ${avatarName}`);
                        console.log(`[TalkingHeadUI] Original audio: ${audioPath}`);
                        console.log(`[TalkingHeadUI] Reference audio file: ${referenceAudioPath}`);

                        // Do NOT store the seed file after voice cloning
                        // The seed file should ONLY be modified when the favorite icon is clicked
                        // Instead, just add the voice to the voice buffer
                        console.log(`[TalkingHeadUI] Skipping seed file storage after voice cloning. The seed file should only be modified via the favorite icon.`);

                        // Add the voice to the voice buffer without modifying the seed file
                        // Use the voice configuration data we prepared earlier
                        // Include the audio blob for direct use
                        voiceConfigData.reference_audio_file = self.reference_audio_file;
                        self.addVoiceBufferItem(voiceConfigData);

                        // Don't automatically set the cloned voice as the current voice
                        // The user needs to click the check button (✓) to temporarily use it
                        // or the star button (⭐) to permanently set it
                        console.log(`[TalkingHeadUI] Voice cloned successfully: ${avatarName}. Use the check button to apply temporarily or star button to set as default.`);

                        // Force avatar to return to idle/listening state after voice cloning
                        // This clears any stuck animation states that prevent proper listening mode
                        if (self.avatar && self.avatar.animator) {
                            console.log('[TalkingHeadUI] Forcing avatar to clear animation state after voice cloning');

                            // Use the force stop method to aggressively clear all animation state
                            if (typeof self.avatar.animator.forceStopAllAnimations === 'function') {
                                self.avatar.animator.forceStopAllAnimations();
                            } else if (typeof self.avatar.animator.stopAnimation === 'function') {
                                // Fallback to regular stop method
                                self.avatar.animator.stopAnimation();
                            }

                            // Force transition to idle state
                            self.avatar.animator.setState('idle');

                            // If the avatar is in listening mode, restore listening state
                            if (self.avatar.isListening) {
                                setTimeout(() => {
                                    if (self.avatar.animator && self.avatar.isListening) {
                                        console.log('[TalkingHeadUI] Restoring listening state after voice cloning');
                                        self.avatar.animator.setState('listening');
                                    }
                                }, 500); // Small delay to let idle state settle
                            }
                        }

                        // Update progress to 100%
                        progressFill.style.width = '100%';
                        progressText.textContent = 'Voice cloned successfully!';

                        // Enable the cloned voice preview button now that we have a cloned voice
                        if (previewButton) {
                            previewButton.disabled = false;
                            console.log('[TalkingHeadUI] Enabling cloned voice preview button');
                        }

                        // Also enable the preview button in the preview section
                        const previewSectionElement = modal.querySelector('.preview-section');
                        if (previewSectionElement) {
                            const previewCloneBtn = previewSectionElement.querySelector('.preview-clone-button');
                            if (previewCloneBtn) {
                                previewCloneBtn.disabled = false;
                                console.log('[TalkingHeadUI] Enabling preview section clone button');
                            }

                            // Update the preview status
                            const previewStatusElement = previewSectionElement.querySelector('.preview-status');
                            if (previewStatusElement) {
                                previewStatusElement.innerHTML = '';
                                previewStatusElement.appendChild(document.createTextNode('Voice cloned successfully! Click the buttons above to preview.'));
                            }
                        }
                    } else {
                        throw new Error(result.error || 'Voice cloning failed');
                    }
                } catch (error) {
                    console.error('[TalkingHeadUI] Error in voice cloning process:', error);
                    self.showNotification(`Voice cloning error: ${error.message}`, true);
                    return;
                }

                self.showNotification('Voice cloned successfully! You can now preview the cloned voice.');

                // Don't close the modal immediately - allow the user to preview the cloned voice
                // Instead, update the UI to show success and enable preview buttons
                progressText.textContent = 'Voice cloned successfully! You can now preview the cloned voice.';

                // Update the preview status as well
                if (previewStatus) {
                    previewStatus.innerHTML = '';
                    previewStatus.appendChild(document.createTextNode('Voice cloned successfully! Click the buttons above to preview.'));
                }

                // Make sure the preview buttons are properly enabled
                const previewSection = modal.querySelector('.preview-section');
                if (previewSection) {
                    const previewOriginalBtn = previewSection.querySelector('.preview-original-button');
                    const previewCloneBtn = previewSection.querySelector('.preview-clone-button');

                    if (previewOriginalBtn) {
                        previewOriginalBtn.disabled = false;
                    }

                    if (previewCloneBtn) {
                        previewCloneBtn.disabled = false;
                        console.log('[TalkingHeadUI] Enabling cloned voice preview button after successful cloning');
                    }
                }

                // Change the cancel button to a close button
                if (cancelButton) {
                    cancelButton.textContent = 'Close';
                    cancelButton.style.backgroundColor = '#4CAF50'; // Green color to indicate success
                }
            } catch (error) {
                console.error('Voice cloning error:', error);

                // Provide specific error guidance
                let errorMessage = 'Failed to clone voice: ' + error.message;

                if (error.message.includes('not initialized')) {
                    errorMessage = 'TTS service not ready. Please wait a moment and try again.';
                } else if (error.message.includes('connection')) {
                    errorMessage = 'Connection error. Please check your network and try again.';
                } else if (error.message.includes('file')) {
                    errorMessage = 'Audio file error. Please try a different audio file (WAV/MP3, max 10MB).';
                }

                self.showNotification(errorMessage, true);
            } finally {
                cloneButton.disabled = false;
            }
        });

        // Cancel button handler
        cancelButton.addEventListener('click', () => {
            modal.style.display = 'none';
            stopRecording();

            // Clean up modal state
            if (modal.dataset.modalId) {
                delete modal.dataset.modalId;
            }

            // Make sure the voice buffer list is updated when closing the modal
            if (self.voiceBuffer && self.voiceBuffer.length > 0) {
                self.sortVoiceBufferList();
                console.log('[TalkingHeadUI] Voice buffer list updated on modal close');
            }
        });

        // Start recording function
        async function startRecording() {
            try {
                // Simple recording options - agent system handles format optimization
                const getUserMediaOptions = { audio: true };

                const stream = await navigator.mediaDevices.getUserMedia(getUserMediaOptions);

                // Setup audio context and analyser
                // @ts-ignore: webkitAudioContext is for Safari compatibility
                const AudioContextClass = window.AudioContext || window.webkitAudioContext;
                audioContext = new AudioContextClass();
                analyser = audioContext.createAnalyser();
                const source = audioContext.createMediaStreamSource(stream);
                source.connect(analyser);

                // Configure analyser
                analyser.fftSize = 2048;
                // The buffer length and data array will be used by the visualizer

                // Start recording with default options - agent system will handle format conversion
                mediaRecorder = new MediaRecorder(stream);
                console.log(`[TalkingHeadUI] MediaRecorder initialized with format: ${mediaRecorder.mimeType}`);
                console.log('[TalkingHeadUI] Audio processing optimized by AliyunBailianChatModel');
                audioChunks = [];

                mediaRecorder.ondataavailable = (event) => {
                    audioChunks.push(event.data);
                };

                mediaRecorder.onstop = async () => {
                    // Create audio blob from chunks with normalized format
                    // Use the same audio type as file upload for consistency
                    const audioBlob = new Blob(audioChunks, {
                        type: 'audio/wav'
                    });
                    console.log(`[TalkingHeadUI] Recording completed, blob size: ${audioBlob.size} bytes, type: ${audioBlob.type} (normalized from ${mediaRecorder.mimeType || 'audio/webm;codecs=opus'})`);
                    self.voiceFile = audioBlob;

                    // Clear any existing reference audio file since we're using a new recording
                    self.reference_audio_file = null;

                    cloneButton.disabled = false;

                    // Enable only the original audio preview button
                    if (previewOriginalButton) {
                        previewOriginalButton.disabled = false;
                    }
                    // Keep the cloned voice preview button disabled until cloning is done
                    if (previewButton) {
                        previewButton.disabled = true;
                    }

                    progressText.textContent = 'Recording complete. Ready to clone voice.';

                    // Check if STT is enabled
                    if (sttCheckbox.checked) {
                        // Use the shared transcription function
                        await self.transcribeAudioAndUpdateUI(audioBlob, {
                            progressText,
                            progressFill,
                            promptText
                        });
                    }

                    // Show preview section
                    const previewSection = modal.querySelector('.preview-section');
                    if (previewSection) {
                        previewSection.classList.add('active');

                        // Make sure the preview buttons are in the correct state
                        const previewOriginalBtn = previewSection.querySelector('.preview-original-button');
                        const previewCloneBtn = previewSection.querySelector('.preview-clone-button');

                        if (previewOriginalBtn) {
                            previewOriginalBtn.disabled = false;
                        }

                        if (previewCloneBtn) {
                            previewCloneBtn.disabled = true;
                        }
                    }
                };

                // Start recording
                mediaRecorder.start();
                recordingStartTime = Date.now();
                recordButton.classList.add('recording');
                recordText.textContent = 'Stop Recording';

                // Expand the waveform container
                const waveformContainer = modal.querySelector('.waveform-container');
                const waveformCanvas = document.getElementById('voiceWaveform');
                if (waveformContainer) waveformContainer.classList.add('recording');
                if (waveformCanvas) waveformCanvas.classList.add('recording');

                // Start timer
                timerInterval = setInterval(updateTimer, 1000);

                // Use our createWaveformVisualizer function to create and start the animation
                // This function will get the canvas context internally
                const animate = createWaveformVisualizer(canvas, analyser);
                animate(); // Start the animation

            } catch (error) {
                console.error('Error starting recording:', error);
                self.showNotification('Error accessing microphone: ' + error.message, true);
            }
        }

        // Stop recording function
        function stopRecording() {
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                mediaRecorder.stop();
                mediaRecorder.stream.getTracks().forEach(track => track.stop());

                if (audioContext) {
                    audioContext.close();
                    audioContext = null;
                    analyser = null;
                }

                recordButton.classList.remove('recording');
                recordText.textContent = 'Start Recording';

                // Collapse the waveform container
                const waveformContainer = modal.querySelector('.waveform-container');
                const waveformCanvas = document.getElementById('voiceWaveform');
                if (waveformContainer) waveformContainer.classList.remove('recording');
                if (waveformCanvas) waveformCanvas.classList.remove('recording');

                if (timerInterval) {
                    clearInterval(timerInterval);
                    timerInterval = null;
                }

                // Reset waveform
                if (canvas) {
                    const ctx = canvas.getContext('2d');
                    if (ctx) {
                        ctx.fillStyle = 'rgb(20, 20, 20)';
                        ctx.fillRect(0, 0, canvas.width, canvas.height);
                        ctx.lineWidth = 2;
                        ctx.strokeStyle = 'rgb(76, 175, 80)';
                        ctx.beginPath();
                        ctx.moveTo(0, canvas.height / 2);
                        ctx.lineTo(canvas.width, canvas.height / 2);
                        ctx.stroke();
                    }
                }
            }
        }

        // Update timer function
        function updateTimer() {
            if (!recordingStartTime) return;

            const elapsed = Math.floor((Date.now() - recordingStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
            const seconds = (elapsed % 60).toString().padStart(2, '0');
            timer.textContent = `${minutes}:${seconds}`;

            // Update progress (assuming 30 seconds is the target duration)
            const progress = Math.min((elapsed / 30) * 100, 100);
            progressFill.style.width = `${progress}%`;
            progressText.textContent = `Recording Progress: ${Math.round(progress)}%`;

            // Stop recording after 30 seconds
            if (elapsed >= 30) {
                stopRecording();
            }
        }
    }

    /**
     * Show a notification message
     * @param {string} message - The message to display
     * @param {boolean} isError - Whether this is an error message
     * @param {number} duration - How long to display the notification in milliseconds (default: 3000)
     */
    showNotification(message, isError = false, duration = 3000) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${isError ? 'error' : 'success'}`;
        notification.textContent = message;

        // Add styles for the notification
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: ${isError ? 'rgba(255, 0, 0, 0.9)' : 'rgba(76, 175, 80, 0.9)'};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            z-index: 10001;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            max-width: 300px;
            word-wrap: break-word;
        `;

        // Add to document
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 10);

        // Remove after specified duration
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);
    }

    /**
     * Add a voice buffer item
     */
    addVoiceBufferItem(voiceData) {
        try {
            this.debugLog('Adding voice buffer item', voiceData);

            // Initialize voice buffer if it doesn't exist
            if (!this.voiceBuffer) {
                this.voiceBuffer = [];
            }

            // Check if voiceData has required fields
            if (!voiceData || !voiceData.timestamp) {
                this.debugLog('Invalid voice data, skipping');
                return;
            }

            // Check if text is available, use a default if not
            if (!voiceData.text) {
                voiceData.text = "Voice sample";
            }

            // Initialize favorite status if not set
            if (voiceData.favorite === undefined) {
                voiceData.favorite = false;
            }

            // Add to voice buffer array
            this.voiceBuffer.push(voiceData);
            this.debugLog('Voice buffer item added to array');

            // Sort and update the UI
            this.sortVoiceBufferList();

            // Make sure the voice buffer list is visible
            const voiceBufferContainer = document.querySelector('.voice-buffer-container');
            if (voiceBufferContainer) {
                voiceBufferContainer.style.display = 'block';
            }

            this.debugLog('Voice buffer item added successfully');
            console.log('[TalkingHeadUI] Voice buffer updated with new item:', voiceData.roleName);
        } catch (error) {
            console.error('[TalkingHeadUI] Error adding voice buffer item:', error);
            this.showNotification('Failed to add voice to buffer', true);
        }
    }

    /**
     * Use a voice buffer item temporarily (without saving to seed file)
     */
    async useVoiceBufferItem(voiceData) {
        try {
            this.debugLog('Using voice buffer item temporarily', voiceData);

            if (!this.avatar) {
                throw new Error('Avatar not available');
            }

            // Show loading state
            const useBtn = document.querySelector(`[data-timestamp="${voiceData.timestamp}"] .use-btn`);
            if (useBtn) {
                useBtn.disabled = true;
                useBtn.textContent = '⌛';
            }

            // Temporarily use this voice without saving to seed file
            if (this.avatar) {
                // Store the reference to the cloned audio file for future speak calls
                this.avatar.currentClonedVoice = voiceData.roleName;

                // Check if we need to fetch the audio file
                if (voiceData.reference_audio_file) {
                    // If reference_audio_file is a string (URL), fetch the audio blob
                    if (typeof voiceData.reference_audio_file === 'string') {
                        try {
                            console.log(`[TalkingHeadUI] Fetching audio file from: ${voiceData.reference_audio_file}`);
                            const response = await fetch(voiceData.reference_audio_file);
                            if (response.ok) {
                                const audioBlob = await response.blob();
                                console.log(`[TalkingHeadUI] Successfully fetched audio blob, size: ${audioBlob.size} bytes, type: ${audioBlob.type}`);

                                // Update the voiceData with the actual blob
                                voiceData.reference_audio_file = audioBlob;
                            } else {
                                console.warn(`[TalkingHeadUI] Failed to fetch audio file: ${response.statusText}`);
                            }
                        } catch (fetchError) {
                            console.error(`[TalkingHeadUI] Error fetching audio file: ${fetchError.message}`);
                        }
                    }
                }

                // Set up the TTS service to use this cloned voice temporarily
                if (voiceData.reference_audio_file instanceof Blob) {
                    // Use the blob directly if available
                    console.log(`[TalkingHeadUI] Using saved audio blob directly, size: ${voiceData.reference_audio_file.size} bytes, type: ${voiceData.reference_audio_file.type}`);

                    // Audio blob handling now managed by @/agent/tools/tts.js
                    console.log('[TalkingHeadUI] Audio blob assignment handled by agent system');

                    // Use the enhanced setClonedVoice method with the voice data and isPermanent=false
                    await this.avatar.setClonedVoice(voiceData, false);
                    console.log(`[TalkingHeadUI] Successfully set cloned voice with blob: ${voiceData.roleName}`);
                } else if (voiceData.reference_audio_file && this.avatar.setClonedVoice) {
                    // Fall back to using the file path if blob is not available
                    console.log(`[TalkingHeadUI] Using reference_audio_file: ${voiceData.reference_audio_file}`);
                    await this.avatar.setClonedVoice(voiceData, false);
                    console.log(`[TalkingHeadUI] Successfully set cloned voice with file: ${voiceData.roleName}`);
                } else {
                    throw new Error('Avatar does not support cloned voices or no audio file available');
                }

                // Update the UI to show this voice is active
                this.updateActiveVoiceUI(voiceData.roleName);

                // Show notification
                this.showNotification(`Temporarily using voice: ${voiceData.roleName}`);
            } else {
                throw new Error('Failed to set cloned voice');
            }

            // Update UI
            if (useBtn) {
                useBtn.disabled = false;
                useBtn.textContent = '✓';
            }

            this.debugLog('Voice applied temporarily');
        } catch (error) {
            console.error('[TalkingHeadUI] Error using voice buffer item:', error);
            this.showNotification(`Failed to apply voice: ${error.message}`, true);

            // Reset button state
            const useBtn = document.querySelector(`[data-timestamp="${voiceData.timestamp}"] .use-btn`);
            if (useBtn) {
                useBtn.disabled = false;
                useBtn.textContent = '✓';
            }
        }
    }

    /**
     * Delete all audio files associated with a voice and remove speaker from remote TTS service
     * @param {Object} voiceData - The voice data containing file paths
     */
    async deleteVoiceFiles(voiceData) {
        const filesToDelete = new Set();

        // Collect all possible file paths
        if (voiceData.audioUrl) {
            filesToDelete.add(voiceData.audioUrl);
        }

        if (voiceData.reference_audio_file_path) {
            filesToDelete.add(voiceData.reference_audio_file_path);
        }

        if (voiceData.reference_audio_file && typeof voiceData.reference_audio_file === 'string') {
            filesToDelete.add(voiceData.reference_audio_file);
        }

        // Speaker deletion now handled by @/agent/tools/tts.js
        console.log(`[TalkingHeadUI] Speaker deletion managed by agent system: ${voiceData.roleName}`);

        // Get the download server URL
        const port = window.downloadServerPort || 2994;
        const host = window.config?.host || 'localhost';
        const downloadServerUrl = `http://${host}:${port}`;

        // Delete each file
        for (const filePath of filesToDelete) {
            if (!filePath) continue;

            try {
                console.log(`[TalkingHeadUI] Deleting voice file: ${filePath}`);

                // Use the delete-file endpoint
                const deleteResponse = await fetch(`${downloadServerUrl}/delete-file?path=${encodeURIComponent(filePath)}`, {
                    method: 'DELETE'
                });

                if (deleteResponse.ok) {
                    console.log(`[TalkingHeadUI] Successfully deleted voice file: ${filePath}`);
                } else {
                    console.warn(`[TalkingHeadUI] Failed to delete voice file ${filePath}: ${deleteResponse.statusText}`);
                }
            } catch (error) {
                console.warn(`[TalkingHeadUI] Error deleting voice file ${filePath}:`, error);
            }
        }
    }

    /**
     * Remove favorite voice configuration from seed file
     * @param {Object} voiceData - The voice data to remove from seed
     */
    async removeFavoriteVoiceFromSeed(voiceData) {
        // Note: voiceData parameter preserved for future use - currently removing favorite status for current avatar
        try {
            // Import the cache utilities
            const { storeSeed, generateCacheKey } = await import('@/utils/cache.js');

            const avatarName = this.avatar.avatar?.name || 'default';
            this.debugLog(`Removing favorite voice from seed for avatar: ${avatarName}`);

            // Generate cache key using the same format as other assets
            let voiceCacheKey;
            if (avatarName === 'default') {
                voiceCacheKey = 'default';
            } else {
                try {
                    const voiceSource = 'voice';
                    const voiceInput = avatarName;
                    voiceCacheKey = await generateCacheKey(voiceSource, voiceInput, null);
                } catch (error) {
                    this.debugLog(`Error generating cache key, using fallback: ${avatarName}`);
                    voiceCacheKey = avatarName;
                }
            }

            // Clear the seed data by storing null values
            const seedData = {
                roleName: null,
                favorite: false,
                reference_audio_file_path: null,
                timestamp: Date.now(),
                meshName: avatarName
            };

            await storeSeed(voiceCacheKey, JSON.stringify(seedData));
            this.debugLog(`Cleared favorite voice from seed file: ${voiceCacheKey}`);

            // Voice reset now handled by @/agent/tools/tts.js
            console.log('[TalkingHeadUI] Voice reset managed by agent system');
        } catch (error) {
            console.error('[TalkingHeadUI] Error removing favorite voice from seed:', error);
        }
    }

    /**
     * Remove a voice buffer item
     */
    async removeVoiceBufferItem(voiceData) {
        try {
            this.debugLog('Removing voice buffer item', voiceData);

            // Remove from DOM
            const item = document.querySelector(`[data-timestamp="${voiceData.timestamp}"]`);
            if (item) {
                item.remove();
            }

            // Remove from array
            this.voiceBuffer = this.voiceBuffer.filter(v => v.timestamp !== voiceData.timestamp);

            // Enhanced file deletion - delete all associated audio files
            await this.deleteVoiceFiles(voiceData);

            // If this was a favorited voice, also remove it from the seed file
            if (voiceData.favorite) {
                await this.removeFavoriteVoiceFromSeed(voiceData);
            }

            this.debugLog('Voice buffer item removed successfully');
            this.showNotification(`Voice "${voiceData.roleName}" deleted successfully`);
        } catch (error) {
            console.error('[TalkingHeadUI] Error removing voice buffer item:', error);
            this.showNotification('Failed to remove voice from buffer', true);
        }
    }

    /**
     * Clear all voice buffer items
     */
    async clearVoiceBuffer() {
        try {
            this.debugLog('Clearing voice buffer');

            // Remove all items from DOM
            const voiceBufferList = document.querySelector('.voice-buffer-list');
            if (voiceBufferList) {
                voiceBufferList.innerHTML = '';
            }

            // Delete all associated files
            for (const voiceData of this.voiceBuffer) {
                if (voiceData.audioUrl) {
                    try {
                        const response = await fetch(voiceData.audioUrl, { method: 'DELETE' });
                        if (!response.ok) {
                            console.warn(`Failed to delete audio file: ${response.statusText}`);
                        }
                    } catch (error) {
                        console.warn('[TalkingHeadUI] Error deleting audio file:', error);
                    }
                }
            }

            // Clear array
            this.voiceBuffer = [];
            this.debugLog('Voice buffer cleared successfully');
        } catch (error) {
            console.error('[TalkingHeadUI] Error clearing voice buffer:', error);
            this.showNotification('Failed to clear voice buffer', true);
        }
    }

    /**
     * Load saved voice configuration
     */
    async loadSavedVoice() {
        try {
            // Initialize voice buffer if it doesn't exist
            if (!this.voiceBuffer) {
                this.voiceBuffer = [];
            }

            // Get the current avatar name
            const avatarName = this.avatar?.name || 'default';

            // Import cache utilities
            const { getStoredSeed, generateCacheKey } = await import('@/utils/cache.js');

            // Generate cache key for voice data
            let voiceCacheKey;
            if (avatarName === 'default') {
                voiceCacheKey = 'default';
            } else {
                voiceCacheKey = await generateCacheKey('voice', avatarName, null);
            }

            // Load saved voice configuration from cache
            const storedSeed = await getStoredSeed(voiceCacheKey);

            if (storedSeed) {
                let voiceConfig;
                if (typeof storedSeed === 'string') {
                    voiceConfig = JSON.parse(storedSeed);
                } else {
                    voiceConfig = storedSeed;
                }

                // Check if this is a favorite voice and should be loaded
                if (voiceConfig.favorite && voiceConfig.roleName) {
                    this.debugLog(`Loading favorite voice for avatar ${avatarName}:`, voiceConfig);

                    // Add to voice buffer
                    this.addVoiceBufferItem(voiceConfig);

                    // Set as active voice through agent system
                    if (this.avatar && this.avatar.setClonedVoice) {
                        await this.avatar.setClonedVoice(voiceConfig, true);
                        this.updateActiveVoiceUI(voiceConfig.roleName);
                    }

                    this.showNotification(`Loaded saved voice: ${voiceConfig.roleName}`);
                    return voiceConfig;
                }
            }

            this.debugLog(`No saved favorite voice found for avatar ${avatarName}`);

            // FIXED: Create a default voice profile if none exists
            const defaultVoiceConfig = {
                roleName: '陈鲁豫', // Default voice
                favorite: false,
                source: 'default',
                timestamp: Date.now()
            };

            this.debugLog(`Creating default voice profile for avatar ${avatarName}:`, defaultVoiceConfig);

            // Set as active voice through agent system if available
            if (this.avatar && this.avatar.setVoice) {
                try {
                    await this.avatar.setVoice(defaultVoiceConfig.roleName);
                    this.updateActiveVoiceUI(defaultVoiceConfig.roleName);
                    this.showNotification(`Using default voice: ${defaultVoiceConfig.roleName}`);
                } catch (error) {
                    this.debugLog('Could not set default voice through avatar, continuing silently:', error);
                }
            }

            return defaultVoiceConfig;

        } catch (error) {
            console.error('[TalkingHeadUI] Error loading saved voice:', error);
            return null;
        }
    }

    /**
     * Preview a voice from the voice buffer
     */
    async previewVoiceItem(voiceData) {
        try {
            this.debugLog('Previewing voice:', voiceData);

            if (!voiceData || !voiceData.roleName) {
                this.showNotification('Invalid voice data', true);
                return;
            }

            // Use a sample text for preview (stored for potential future use)
            // const previewText = voiceData.text || "这是我声音的样本。";

            // TTS service now handled by @/agent/tools/tts.js
            if (!this.avatar) {
                this.showNotification('Avatar not available', true);
                return;
            }

            // Stop any currently playing audio
            if (this.currentPreviewAudio) {
                this.currentPreviewAudio.pause();
                this.currentPreviewAudio = null;

                // Reset the preview button if it was changed to a stop button
                const previewBtn = document.querySelector(`[data-timestamp="${voiceData.timestamp}"] .preview-btn`);
                if (previewBtn && previewBtn.textContent === '⏹️') {
                    previewBtn.textContent = '▶️';
                    return; // Exit if we're just stopping playback
                }
            }

            // Change the preview button to a stop button
            const previewBtn = document.querySelector(`[data-timestamp="${voiceData.timestamp}"] .preview-btn`);
            if (previewBtn) {
                previewBtn.textContent = '⏹️';
            }

            // For cloned voices, play the saved audio file directly instead of using TTS
            try {
                // First check if we have a reference_audio_blob
                if (voiceData.reference_audio_blob instanceof Blob) {
                    this.showNotification(`Previewing voice: ${voiceData.roleName}`);

                    // Create a URL from the blob
                    const audioUrl = URL.createObjectURL(voiceData.reference_audio_blob);
                    console.log(`[TalkingHeadUI] Playing reference audio blob, size: ${voiceData.reference_audio_blob.size} bytes, type: ${voiceData.reference_audio_blob.type}`);

                    // Create and play audio element
                    const audio = new Audio(audioUrl);
                    this.currentPreviewAudio = audio; // Store reference for stopping

                    // Set up ended event to reset the button and revoke the URL
                    audio.onended = () => {
                        if (previewBtn) {
                            previewBtn.textContent = '▶️';
                        }
                        this.currentPreviewAudio = null;
                        URL.revokeObjectURL(audioUrl); // Clean up the URL
                    };

                    await audio.play();
                }
                // Check if we have a reference audio file path
                else if (voiceData.reference_audio_file) {
                    this.showNotification(`Previewing voice: ${voiceData.roleName}`);

                    // Handle if reference_audio_file is already a Blob
                    let audioUrl;
                    if (voiceData.reference_audio_file instanceof Blob) {
                        audioUrl = URL.createObjectURL(voiceData.reference_audio_file);
                        console.log(`[TalkingHeadUI] Playing reference audio file blob, size: ${voiceData.reference_audio_file.size} bytes, type: ${voiceData.reference_audio_file.type}`);
                    } else {
                        // It's a URL string
                        audioUrl = voiceData.reference_audio_file;
                        console.log(`[TalkingHeadUI] Playing reference audio file URL: ${audioUrl}`);
                    }

                    // Create and play audio element
                    const audio = new Audio(audioUrl);
                    this.currentPreviewAudio = audio; // Store reference for stopping

                    // Set up ended event to reset the button
                    audio.onended = () => {
                        if (previewBtn) {
                            previewBtn.textContent = '▶️';
                        }
                        this.currentPreviewAudio = null;
                        // Revoke the URL if we created one
                        if (voiceData.reference_audio_file instanceof Blob) {
                            URL.revokeObjectURL(audioUrl);
                        }
                    };

                    await audio.play();
                } else {
                    // If no saved audio file, try to use the TTS service
                    this.showNotification(`No saved audio found, using TTS service`);

                    // Use a default voice based on the current gender
                    const gender = this.avatar.voiceConfig?.currentGender || 'male';
                    const language = this.avatar.voiceConfig?.currentLanguage || 'chinese';

                    // Set the voice to a default one
                    this.avatar.setVoice(language, gender);

                    // TTS preview now handled by @/agent/tools/tts.js
                    console.log('[TalkingHeadUI] TTS preview managed by agent system');

                    // Reset the button after TTS completes
                    if (previewBtn) {
                        previewBtn.textContent = '▶️';
                    }
                }
            } catch (error) {
                console.error('[TalkingHeadUI] Error previewing voice:', error);

                // Provide a more detailed error message
                let errorMessage = 'Failed to preview voice';
                if (error.name === 'NotSupportedError') {
                    errorMessage = 'Audio format not supported or file not found';
                } else if (error.name === 'NotAllowedError') {
                    errorMessage = 'Audio playback not allowed (user interaction required)';
                } else if (error.message) {
                    errorMessage = `Error: ${error.message}`;
                }

                this.showNotification(errorMessage, true);
                console.log(`[TalkingHeadUI] Voice data that caused error:`, JSON.stringify({
                    roleName: voiceData.roleName,
                    hasBlob: voiceData.reference_audio_blob instanceof Blob,
                    blobType: voiceData.reference_audio_blob instanceof Blob ? voiceData.reference_audio_blob.type : 'N/A',
                    reference_audio_file: typeof voiceData.reference_audio_file === 'string' ? voiceData.reference_audio_file :
                        (voiceData.reference_audio_file instanceof Blob ? 'Blob' : typeof voiceData.reference_audio_file)
                }));

                // Reset the button on error
                if (previewBtn) {
                    previewBtn.textContent = '▶️';
                }
            }
        } catch (error) {
            console.error('[TalkingHeadUI] Error previewing voice:', error);

            // Provide a more detailed error message
            let errorMessage = 'Failed to preview voice';
            if (error.name === 'NotSupportedError') {
                errorMessage = 'Audio format not supported or file not found';
            } else if (error.name === 'NotAllowedError') {
                errorMessage = 'Audio playback not allowed (user interaction required)';
            } else if (error.message) {
                errorMessage = `Error: ${error.message}`;
            }

            this.showNotification(errorMessage, true);

            // Reset any preview button that might be in stop state
            const previewBtn = document.querySelector('.preview-btn');
            if (previewBtn && previewBtn.textContent === '⏹️') {
                previewBtn.textContent = '▶️';
            }
        }
    }

    /**
     * Toggle favorite status for a voice buffer item and save it to the seed file
     * Only one voice can be marked as favorite at a time
     */
    async toggleFavorite(voiceData, event) {
        try {
            const button = event.target;
            voiceData.favorite = !voiceData.favorite;

            if (voiceData.favorite) {
                // First, remove favorite status from any other voice in the buffer
                for (const voice of this.voiceBuffer) {
                    if (voice !== voiceData && voice.favorite) {
                        // Remove favorite status from this voice
                        voice.favorite = false;

                        // Update the UI for this voice item
                        const otherButton = document.querySelector(`[data-timestamp="${voice.timestamp}"] .favorite-btn`);
                        if (otherButton) {
                            otherButton.textContent = '❔';
                            otherButton.style.color = '';
                        }

                        this.debugLog(`Removed favorite status from voice: ${voice.roleName}`);
                    }
                }

                // Update the current button
                button.textContent = '⭐';
                button.style.color = 'gold';

                // When favorited, save the voice permanently to the seed file
                try {
                    // Get the current avatar name
                    const avatarName = this.avatar.avatar?.name || 'default';

                    // Import the cache utilities
                    const { storeSeed, generateCacheKey } = await import('@/utils/cache.js');

                    // Move audio file to the favorite folder
                    let reference_audio_file_path = voiceData.reference_audio_file_path;

                    // Move the reference audio file to the favorite folder if it exists
                    if (reference_audio_file_path && typeof reference_audio_file_path === 'string' && !isInFavoriteFolder(reference_audio_file_path)) {
                        this.debugLog(`Moving reference audio file to favorite folder: ${reference_audio_file_path}`);
                        reference_audio_file_path = await moveToFavoriteFolder(reference_audio_file_path, avatarName);
                        voiceData.reference_audio_file_path = reference_audio_file_path;
                    }

                    // Create seed data with favorite flag and updated paths
                    const seedData = {
                        reference_audio_file_path: reference_audio_file_path, // Add this line to save the path
                        roleName: voiceData.roleName,
                        timestamp: voiceData.timestamp,
                        reference_text: voiceData.reference_text, // Include reference_text
                        favorite: true,
                        meshName: this.avatar?.name || 'default', // Connect to the current avatar mesh
                        // Include voice parameters for better quality when using the voice
                        temperature: voiceData.temperature || 0.9,
                        top_p: voiceData.top_p || 0.95,
                        top_k: voiceData.top_k || 50,
                        window_size: voiceData.window_size || 50
                    };

                    // Generate a cache key for the avatar
                    let voiceCacheKey;

                    // Special case for 'default' avatar to avoid the hash suffix
                    if (avatarName === 'default') {
                        voiceCacheKey = 'default'; // Use plain 'default' for the default avatar
                        this.debugLog(`Using special case for default avatar: ${voiceCacheKey}`);
                    } else {
                        const voiceSource = 'voice';
                        const voiceInput = avatarName;
                        // Use null as the seed to ensure consistent naming with other assets
                        voiceCacheKey = await generateCacheKey(voiceSource, voiceInput, null);
                    }

                    // Store the seed using the cache API
                    const result = await storeSeed(voiceCacheKey, JSON.stringify(seedData));

                    if (!result || !result.success) {
                        console.error('[TalkingHeadUI] Error storing seed data:', result?.error || 'Unknown error');
                        this.showNotification('Warning: Voice settings may not persist between sessions', true);
                    } else {
                        this.debugLog(`Stored favorited voice in seed file for ${voiceCacheKey}:`, seedData);
                    }

                    // Show notification
                    this.showNotification('Voice saved as favorite and connected to avatar');

                    // Set this voice as the permanent voice for the avatar
                    if (this.avatar.setClonedVoice) {
                        // Use the enhanced setClonedVoice method with audio file path and isPermanent=true
                        await this.avatar.setClonedVoice(voiceData, true);
                        this.debugLog(`Set cloned voice ${voiceData.roleName} as permanent default for avatar ${avatarName}`);

                        // Update UI to show this voice is active
                        this.updateActiveVoiceUI(voiceData.roleName);
                    }

                    // Move the favorited item to the top of the list
                    this.sortVoiceBufferList();
                } catch (error) {
                    console.error('[TalkingHeadUI] Error saving favorited voice:', error);
                    this.showNotification('Failed to save favorite voice', true);
                }
            } else {
                button.textContent = '⭐';
                button.style.color = '';

                // When unfavorited, update the seed file
                try {
                    // Get the current avatar name
                    const avatarName = this.avatar.avatar?.name || 'default';

                    // Import the cache utilities
                    const { storeSeed, getStoredSeed, generateCacheKey } = await import('@/utils/cache.js');

                    // Generate a cache key for the avatar
                    let voiceCacheKey;

                    // Special case for 'default' avatar to avoid the hash suffix
                    if (avatarName === 'default') {
                        voiceCacheKey = 'default'; // Use plain 'default' for the default avatar
                        this.debugLog(`Using special case for default avatar: ${voiceCacheKey}`);
                    } else {
                        const voiceSource = 'voice';
                        const voiceInput = avatarName;
                        // Use null as the seed to ensure consistent naming with other assets
                        voiceCacheKey = await generateCacheKey(voiceSource, voiceInput, null);
                    }

                    // Get the current seed data
                    const currentSeedData = await getStoredSeed(voiceCacheKey);
                    if (currentSeedData) {
                        // Parse the seed data
                        let seedData;
                        if (typeof currentSeedData === 'string') {
                            seedData = JSON.parse(currentSeedData);
                        } else {
                            seedData = currentSeedData;
                        }

                        // Move audio file back to temporary folder if it's in the favorite folder
                        let reference_audio_file_path = seedData.reference_audio_file || seedData.reference_audio;

                        // Move the reference audio file back to the temporary folder if it exists
                        if (reference_audio_file_path && typeof reference_audio_file_path === 'string' && isInFavoriteFolder(reference_audio_file_path)) {
                            this.debugLog(`Moving reference audio file from favorite folder: ${reference_audio_file_path}`);
                            reference_audio_file_path = await moveFromFavoriteFolder(reference_audio_file_path, avatarName);
                            voiceData.reference_audio_file_path = reference_audio_file_path;
                        }

                        // Update the favorite flag
                        seedData.favorite = false;
                        seedData.reference_audio_file_path = reference_audio_file_path;

                        // Store the updated seed
                        const result = await storeSeed(voiceCacheKey, JSON.stringify(seedData));

                        if (!result || !result.success) {
                            console.error('[TalkingHeadUI] Error updating seed data:', result?.error || 'Unknown error');
                            this.showNotification('Warning: Voice settings may not persist between sessions', true);
                        } else {
                            this.debugLog(`Updated seed file to remove favorite status for ${voiceCacheKey}`);
                        }

                        // Reset to default voice if this was the active voice
                        if (this.avatar.currentClonedVoice === voiceData.roleName) {
                            // Reset to default voice
                            // Voice reset now handled by @/agent/tools/tts.js
                            console.log('[TalkingHeadUI] Voice reset to default managed by agent system');

                            // Clear the cloned voice references
                            this.avatar.currentClonedVoice = null;

                            // Update UI to clear active voice indication
                            this.updateActiveVoiceUI(null);
                        }
                    }

                    // Sort the voice buffer list
                    this.sortVoiceBufferList();
                } catch (error) {
                    console.error('[TalkingHeadUI] Error updating favorite status:', error);
                }
            }

            this.debugLog('Toggled favorite status:', voiceData);
        } catch (error) {
            console.error('[TalkingHeadUI] Error toggling favorite:', error);
        }
    }

    /**
     * Sort the voice buffer list to show favorited items at the top
     */
    sortVoiceBufferList() {
        try {
            // Sort the voice buffer array
            this.voiceBuffer.sort((a, b) => {
                // Favorited items come first
                if (a.favorite && !b.favorite) return -1;
                if (!a.favorite && b.favorite) return 1;
                // Then sort by timestamp (newest first)
                return b.timestamp - a.timestamp;
            });

            // Get the voice buffer list element
            const voiceBufferList = document.querySelector('.voice-buffer-list');
            if (!voiceBufferList) {
                console.warn('[TalkingHeadUI] Voice buffer list element not found');
                return;
            }

            // Clear the current list
            voiceBufferList.innerHTML = '';

            // If there are no items, show a message
            if (this.voiceBuffer.length === 0) {
                const emptyMessage = document.createElement('div');
                emptyMessage.textContent = 'No voice clones available';
                emptyMessage.style.cssText = `
                    color: rgba(255, 255, 255, 0.7);
                    text-align: center;
                    padding: 10px;
                    font-style: italic;
                `;
                voiceBufferList.appendChild(emptyMessage);
                return;
            }

            // Re-add the items in the sorted order
            for (const voiceData of this.voiceBuffer) {
                // Create a new item
                const item = document.createElement('div');
                item.className = 'voice-buffer-item';
                item.dataset.timestamp = voiceData.timestamp;
                item.style.cssText = `
                    background-color: rgba(30, 30, 30, 0.8);
                    border-radius: 6px;
                    padding: 10px;
                    margin-bottom: 5px;
                `;

                // Create header with timestamp and controls
                const header = document.createElement('div');
                header.className = 'voice-buffer-header';
                header.innerHTML = `
                    <span class="timestamp">${new Date(voiceData.timestamp).toLocaleString()}</span>
                    <div class="controls">
                        <button class="preview-btn" title="Preview Voice">▶️</button>
                        <button class="use-btn" title="Use Voice">✓</button>
                        <button class="favorite-btn" title="Favorite Voice">${voiceData.favorite ? '★' : '⭐'}</button>
                        <button class="delete-btn" title="Delete Voice">🗑️</button>
                    </div>
                `;

                // Set the favorite button color if needed
                if (voiceData.favorite) {
                    header.querySelector('.favorite-btn').style.color = 'gold';
                }

                item.appendChild(header);

                // Add preview text
                const preview = document.createElement('div');
                preview.className = 'voice-preview';
                preview.textContent = voiceData.text.length > 50
                    ? `${voiceData.text.substring(0, 50)}...`
                    : voiceData.text;
                item.appendChild(preview);

                // Add event listeners
                header.querySelector('.preview-btn').addEventListener('click', () => {
                    console.log('[TalkingHeadUI] Preview button clicked for voice:', voiceData);
                    this.previewVoiceItem(voiceData);
                });
                header.querySelector('.use-btn').addEventListener('click', () => this.useVoiceBufferItem(voiceData));
                header.querySelector('.favorite-btn').addEventListener('click', (e) => this.toggleFavorite(voiceData, e));
                header.querySelector('.delete-btn').addEventListener('click', () => this.removeVoiceBufferItem(voiceData));

                // Add to the list
                voiceBufferList.appendChild(item);
            }

            this.debugLog('Voice buffer list sorted');
        } catch (error) {
            console.error('[TalkingHeadUI] Error sorting voice buffer list:', error);
        }
    }

    // Add debug toggle method
    toggleDebugMode() {
        this.debugMode = !this.debugMode;
        console.log(`[TalkingHeadUI] Debug mode ${this.debugMode ? 'enabled' : 'disabled'}`);
        this.updateDebugUI();
    }

    // Update debug UI elements
    updateDebugUI() {
        const debugElements = document.querySelectorAll('.debug-info');
        debugElements.forEach(el => {
            el.style.display = this.debugMode ? 'block' : 'none';
        });
    }

    // Add debug logging
    debugLog(message, data = null) {
        if (this.debugMode) {
            console.log(`[TalkingHeadUI] ${message}`, data || '');
        }
    }

    /**
     * Update the UI to indicate which voice is currently active
     * @param {string} activeRoleName - The role name of the active voice
     */
    updateActiveVoiceUI(activeRoleName) {
        try {
            if (!activeRoleName) {
                // If no active role name, reset the UI
                const voiceItems = document.querySelectorAll('.voice-buffer-item');
                voiceItems.forEach(item => {
                    item.classList.remove('active-voice');
                    item.style.borderLeft = '';
                });

                // Reset the voice button text to default
                if (this.controls && this.controls.voiceButton) {
                    this.controls.voiceButton.innerHTML = '🔊 Voice';
                }

                // Reset the status text
                if (this.controls && this.controls.statusSpan) {
                    this.controls.statusSpan.textContent = 'Ready';
                }

                return;
            }

            this.debugLog(`Updating UI to show active voice: ${activeRoleName}`);

            // Get all voice buffer items
            const voiceItems = document.querySelectorAll('.voice-buffer-item');

            // Remove active class from all items
            voiceItems.forEach(item => {
                item.classList.remove('active-voice');
                item.style.borderLeft = '';
            });

            // Find if this is a cloned voice in the voice buffer
            let isClonedVoice = false;

            for (const voiceData of this.voiceBuffer) {
                if (voiceData.roleName === activeRoleName) {
                    isClonedVoice = true;

                    // Find the DOM element with this timestamp
                    const item = document.querySelector(`[data-timestamp="${voiceData.timestamp}"]`);
                    if (item) {
                        // Add visual indication that this voice is active
                        item.classList.add('active-voice');
                        item.style.borderLeft = '4px solid #4CAF50';
                        this.debugLog(`Marked voice item as active: ${activeRoleName}`);
                    }
                    break;
                }
            }

            // Update the voice button text to show the active voice
            if (this.controls && this.controls.voiceButton) {
                // Add a microphone icon for cloned voices
                const icon = isClonedVoice ? '🎤' : '🔊';
                const displayName = activeRoleName.length > 10 ? activeRoleName.substring(0, 10) + '...' : activeRoleName;
                this.controls.voiceButton.innerHTML = `${icon} ${displayName}`;

                // Add a subtle visual indicator for cloned voices
                if (isClonedVoice) {
                    this.controls.voiceButton.style.backgroundColor = 'rgba(76, 175, 80, 0.7)'; // Green for cloned voices
                } else {
                    this.controls.voiceButton.style.backgroundColor = 'rgba(0, 120, 255, 0.7)'; // Blue for regular voices
                }
            }

            // Update the status text to show the active voice
            if (this.controls && this.controls.statusSpan) {
                const voiceType = isClonedVoice ? 'Cloned Voice' : 'Voice';
                this.controls.statusSpan.textContent = `${voiceType}: ${activeRoleName}`;
            }

        } catch (error) {
            console.error('[TalkingHeadUI] Error updating active voice UI:', error);
        }
    }

    /**
     * Load available animations with enhanced registry integration and cache utilities
     * @param {HTMLElement} animationMenu - The animation menu DOM element
     * @param {Object} talkingHead - TalkingHead instance
     */
    async loadAvailableAnimations(animationMenu, talkingHead) {
        try {
            // Clear existing items
            animationMenu.innerHTML = '';

            // Initialize the skeletal animation manager if necessary
            if (!this.skeletalAnimationManager) {
                this.skeletalAnimationManager = new SkeletalAnimator(talkingHead);
            }

            // Use cache.js utilities for proper port configuration
            const port = getDownloadServerPort();
            const host = window.config?.host || 'localhost';
            const downloadServerUrl = `http://${host}:${port}`;

            console.log(`[TalkingHeadUI] Using port ${port} from cache utilities`);

            // Define animations path
            const animationPath = '/assets/animations/';

            let animationsFound = false;
            let serverAnimations = [];

            // Try to fetch animations from the server
            try {
                const encodedDir = encodeURIComponent(animationPath.replace(/^\//, ''));
                const listFilesUrl = `${downloadServerUrl}/list-files?dir=${encodedDir}`;

                console.log(`[TalkingHeadUI] Fetching animations from server: ${listFilesUrl}`);

                const response = await fetch(listFilesUrl);

                if (response.ok) {
                    const data = await response.json();

                    if (data && data.files && Array.isArray(data.files)) {
                        // Filter for FBX files
                        serverAnimations = data.files.filter(file =>
                            file.toLowerCase().endsWith('.fbx')
                        );

                        console.log(`[TalkingHeadUI] Found ${serverAnimations.length} FBX files on server`);
                        animationsFound = serverAnimations.length > 0;
                    }
                } else {
                    console.warn(`[TalkingHeadUI] Server request failed: ${response.status} ${response.statusText}`);
                }
            } catch (serverError) {
                console.warn(`[TalkingHeadUI] Error fetching from server:`, serverError);
            }

            // Create animation items with registry metadata
            if (animationsFound) {
                // Group animations by category for better organization
                const categorizedAnimations = new Map();

                serverAnimations.forEach(file => {
                    const registryData = ANIMATION_REGISTRY.byFilename[file] || {};
                    const category = registryData.category || 'misc';

                    if (!categorizedAnimations.has(category)) {
                        categorizedAnimations.set(category, []);
                    }

                    categorizedAnimations.get(category).push({
                        file,
                        registryData
                    });
                });

                // Create animations in category order
                const sortedCategories = Array.from(categorizedAnimations.entries());

                for (const [category, animations] of sortedCategories) {
                    // Add category header
                    const categoryHeader = document.createElement('div');
                    categoryHeader.textContent = category.charAt(0).toUpperCase() + category.slice(1);
                    categoryHeader.className = 'animation-category-header';
                    animationMenu.appendChild(categoryHeader);

                    // Create animation items
                    animations.forEach(({ file, registryData }) => {
                        const displayName = registryData.description?.en ||
                            registryData.description ||
                            file.replace(/\.fbx$/i, '').replace(/_/g, ' ');

                        this.createAnimationItem(animationMenu, talkingHead, {
                            name: displayName,
                            file: file,
                            path: animationPath,
                            category: registryData.category,
                            description: registryData.description
                        });
                    });
                }
            }

            // Add a stop animation button
            const stopAnimButton = document.createElement('button');
            stopAnimButton.textContent = '⏹️ Stop Animation';
            stopAnimButton.className = 'animation-item stop-animation';
            stopAnimButton.className += ' stop-animation-button';

            stopAnimButton.addEventListener('click', () => {
                if (this.skeletalAnimationManager) {
                    this.skeletalAnimationManager.stopAnimation();

                    // Stop the animation update loop
                    this.stopAnimationUpdateLoop();

                    // Show notification
                    this.showNotification('Animation stopped');
                    console.log('[TalkingHeadUI] Animation stopped by user');
                }

                // Hide the menu
                animationMenu.style.display = 'none';
            });

            animationMenu.appendChild(stopAnimButton);

        } catch (error) {
            console.error('[TalkingHeadUI] Error loading animations:', error);
            this.showNotification('Failed to load animations', true);
        }
    }

    /**
     * Create an animation item button
     * @param {HTMLElement} animationMenu - The animation menu DOM element
     * @param {Object} talkingHead - TalkingHead instance
     * @param {Object} animation - Animation data (name, file, path)
     */
    createAnimationItem(animationMenu, talkingHead, animation) {
        const animItem = document.createElement('button');
        animItem.textContent = animation.name;
        animItem.className = 'animation-item';
        // CSS class will handle styling automatically

        animItem.addEventListener('click', async () => {
            // Set button state
            const originalText = animItem.textContent;
            animItem.textContent = '⏳ Loading...';
            animItem.disabled = true;

            try {
                // Show notification that we're playing an animation
                this.showNotification('Loading animation...');

                // Show loading notification
                this.showNotification(`Loading animation: ${animation.name}...`);

                // Find the target mesh
                let targetMesh = null;

                // Try to find the mesh in different locations based on the structure
                if (talkingHead.avatar && talkingHead.avatar.mesh) {
                    console.log('[TalkingHeadUI] Found mesh on talkingHead.avatar');
                    targetMesh = talkingHead.avatar.mesh;
                } else if (talkingHead.mesh) {
                    console.log('[TalkingHeadUI] Found mesh directly on talkingHead');
                    targetMesh = talkingHead.mesh;
                } else if (talkingHead.userData && talkingHead.userData.mesh) {
                    console.log('[TalkingHeadUI] Found mesh in talkingHead.userData');
                    targetMesh = talkingHead.userData.mesh;
                } else {
                    // Last resort: try to find a skinned mesh in the scene
                    console.log('[TalkingHeadUI] Trying to find a skinned mesh in the scene');
                    let skinnedMesh = null;
                    talkingHead.scene.traverse((object) => {
                        if (object.isSkinnedMesh) {
                            skinnedMesh = object;
                        }
                    });

                    if (skinnedMesh && skinnedMesh.skeleton) {
                        console.log('[TalkingHeadUI] Found a skinned mesh in the scene');
                        targetMesh = skinnedMesh;
                    } else {
                        // Try to find any mesh as a last resort
                        let anyMesh = null;
                        talkingHead.scene.traverse((object) => {
                            if (object.isMesh && !anyMesh) {
                                anyMesh = object;
                            }
                        });

                        if (anyMesh) {
                            console.log('[TalkingHeadUI] Found a mesh in the scene');
                            targetMesh = anyMesh;
                        }
                    }
                }

                if (!targetMesh) {
                    this.showNotification('No avatar mesh available', true);
                    animItem.textContent = originalText;
                    animItem.disabled = false;
                    return;
                }

                // Now that we have the mesh, find the armature/skeleton
                let armature = null;

                // First check if there's an armature property on the talkingHead
                if (talkingHead.armature) {
                    console.log('[TalkingHeadUI] Found armature directly on talkingHead');
                    armature = talkingHead.armature;
                } else if (talkingHead.avatar && talkingHead.avatar.armature) {
                    console.log('[TalkingHeadUI] Found armature on talkingHead.avatar');
                    armature = talkingHead.avatar.armature;
                } else {
                    // Try to find armature in the mesh or its parents
                    if (targetMesh.parent && (targetMesh.parent.type === 'Bone' || targetMesh.parent.isBone)) {
                        console.log('[TalkingHeadUI] Found armature in mesh parent');
                        // Find the root bone
                        let rootBone = targetMesh.parent;
                        while (rootBone.parent && (rootBone.parent.type === 'Bone' || rootBone.parent.isBone)) {
                            rootBone = rootBone.parent;
                        }
                        armature = rootBone;
                    } else if (targetMesh.skeleton) {
                        console.log('[TalkingHeadUI] Found skeleton in mesh');

                        // For skinned meshes, we need to find or create an appropriate armature
                        if (targetMesh.isSkinnedMesh) {
                            // First try to find an object with bones property
                            let boneContainer = null;
                            talkingHead.scene.traverse((object) => {
                                if (object.bones && Array.isArray(object.bones) && object.bones.length > 0) {
                                    boneContainer = object;
                                }
                            });

                            if (boneContainer) {
                                console.log('[TalkingHeadUI] Found bone container in scene');
                                armature = boneContainer;
                            } else {
                                // As a fallback, create a dummy armature using the skeleton
                                console.log('[TalkingHeadUI] Creating dummy armature from skeleton');
                                armature = targetMesh;
                            }
                        }
                    } else {
                        // Try to find any bone in the scene as a last resort
                        console.log('[TalkingHeadUI] Trying to find bones in the scene');
                        let foundBone = null;
                        talkingHead.scene.traverse((object) => {
                            if ((object.type === 'Bone' || object.isBone) && !foundBone) {
                                foundBone = object;
                            }
                        });

                        if (foundBone) {
                            console.log('[TalkingHeadUI] Found a bone in the scene');
                            // Find the root bone
                            let rootBone = foundBone;
                            while (rootBone.parent && (rootBone.parent.type === 'Bone' || rootBone.parent.isBone)) {
                                rootBone = rootBone.parent;
                            }
                            armature = rootBone;
                        } else {
                            // Last resort: use the mesh itself
                            console.log('[TalkingHeadUI] No armature found, using mesh itself');
                            armature = targetMesh;
                        }
                    }
                }

                // Combine the path and file for the animation URL
                const animPath = (animation.path || '') + animation.file;
                console.log('[TalkingHeadUI] Trying to load animation from:', animPath);

                // 在播放新动画前先停止当前正在播放的动画
                if (this.skeletalAnimationManager) {
                    console.log('[TalkingHeadUI] Stopping any current animation before playing new one');
                    this.skeletalAnimationManager.stopAnimation();
                }

                // Try to play the animation
                try {
                    console.log('[TalkingHeadUI] Using armature:', armature);

                    if (this.debugMode) {
                        console.log('[DEBUG] Target mesh structure:', targetMesh);
                        console.log('[DEBUG] Armature structure:', armature);

                        // Log bone hierarchy if available
                        if (armature.isBone || armature.type === 'Bone') {
                            console.log('[DEBUG] Bone hierarchy:');
                            let traverse = (bone, level = 0) => {
                                const indent = ' '.repeat(level * 2);
                                console.log(`${indent}- ${bone.name || 'unnamed bone'}`);
                                (bone.children || []).forEach(child => traverse(child, level + 1));
                            };
                            traverse(armature);
                        }
                    }

                    // Extract animation category from the filename or use a default
                    const animationName = animation.name || animation.file || '';
                    const animCategory = animationName.toLowerCase().includes('dance') ? 'dancing' :
                        animationName.toLowerCase().includes('martial') ||
                            animationName.toLowerCase().includes('fight') ? 'fighting' : 'general';

                    // Start the animation system if it's not already running
                    if (!this.skeletalAnimationManager.isRunning) {
                        this.skeletalAnimationManager.start();
                    }

                    // Play the animation with the new API
                    const success = await this.skeletalAnimationManager.playAnimation(
                        animCategory, // Animation category
                        animation.file, // Animation file name
                        10000 // Duration in milliseconds (10 seconds)
                    );

                    if (success) {
                        this.showNotification(`Playing animation: ${originalText}`);

                        // Set up animation update with proper time delta
                        if (!this.animationUpdateId) {
                            // Create a clock to track time
                            if (!this.animationClock) {
                                this.animationClock = {
                                    lastTime: performance.now() / 1000,
                                    getDelta: function () {
                                        const currentTime = performance.now() / 1000;
                                        const delta = currentTime - this.lastTime;
                                        this.lastTime = currentTime;
                                        // Clamp delta to reasonable values (between 1/120 and 1/15 seconds)
                                        return Math.min(Math.max(delta, 1 / 120), 1 / 15);
                                    }
                                };
                            }

                            // Reset the clock
                            this.animationClock.lastTime = performance.now() / 1000;

                            // Keep track of consecutive errors
                            let errorCount = 0;
                            const MAX_ERRORS = 5; // Maximum consecutive errors before stopping the animation loop

                            const updateAnimation = () => {
                                try {
                                    if (this.skeletalAnimationManager) {
                                        // We don't need to calculate delta time anymore
                                        // The SkeletalAnimator handles its own timing

                                        // The new SkeletalAnimator handles its own updates internally
                                        // We don't need to call update manually anymore
                                        // Just log that the animation is still playing
                                        // Reset error count on successful update
                                        errorCount = 0;
                                    }
                                } catch (error) {
                                    errorCount++;
                                    console.error('[TalkingHeadUI] Error in animation update:', error);

                                    // If too many consecutive errors, stop the animation loop
                                    if (errorCount >= MAX_ERRORS) {
                                        console.error('[TalkingHeadUI] Too many animation errors, stopping animation loop');
                                        this.showNotification('Animation stopped due to errors', true);

                                        // Clean up animation resources
                                        if (this.skeletalAnimationManager) {
                                            try {
                                                this.skeletalAnimationManager.stopAnimation();
                                            } catch (cleanupError) {
                                                console.error('[TalkingHeadUI] Error cleaning up animation:', cleanupError);
                                            }
                                        }

                                        // Don't request another frame
                                        return;
                                    }
                                }

                                // Continue the animation loop
                                this.animationUpdateId = requestAnimationFrame(updateAnimation);
                            };

                            // Start the animation loop
                            this.animationUpdateId = requestAnimationFrame(updateAnimation);
                            console.log('[TalkingHeadUI] Animation update loop started with time-based delta');
                        }
                    } else {
                        console.error('[TalkingHeadUI] Failed to play animation');
                        this.showNotification('Failed to play animation - Check browser console for details', true);
                    }
                } catch (error) {
                    console.error(`[TalkingHeadUI] Error playing animation:`, error);
                    this.showNotification(`Error: ${error.message || 'Unknown error'}`, true);
                }

                // Reset button
                animItem.textContent = originalText;
                animItem.disabled = false;

                // Hide menu
                animationMenu.style.display = 'none';
            } catch (error) {
                console.error('[TalkingHeadUI] Error playing animation:', error);
                this.showNotification(`Error: ${error.message || 'Unknown error'}`, true);

                // Reset button state
                animItem.textContent = originalText;
                animItem.disabled = false;
            }
        });

        // Add to menu
        animationMenu.appendChild(animItem);
        return animItem;
    }
}

export default TalkingHeadUI;
