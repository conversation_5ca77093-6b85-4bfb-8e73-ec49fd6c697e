/**
 * Cache Manager
 * 
 * Generalized caching system for models, WASM files, and other assets.
 * Supports multiple storage backends: IndexedDB, File System API, and server upload.
 */

import { buildDownloadServerUrl } from '../../utils/portManager.js';
import { createLogger, LogLevel } from '@/utils/logger';

const logger = createLogger('CacheManager', LogLevel.DEBUG);

// Storage backends enum
export const StorageBackend = {
    INDEXED_DB: 'indexeddb',
    FILE_SYSTEM: 'filesystem',
    SERVER_UPLOAD: 'server',
    MEMORY: 'memory'
};

// Cache configuration
const CACHE_CONFIG = {
    dbName: 'HologramCache',
    dbVersion: 1,
    stores: {
        models: 'models',
        wasm: 'wasm',
        assets: 'assets',
        metadata: 'metadata'
    },
    maxMemoryCache: 100 * 1024 * 1024, // 100MB
    defaultTTL: 24 * 60 * 60 * 1000 // 24 hours
};

/**
 * Generic Cache Manager
 * Handles caching across multiple storage backends with fallback strategies
 */
export class CacheManager {
    constructor(options = {}) {
        this.namespace = options.namespace || 'default';
        this.uiManager = options.uiManager;
        this.preferredBackend = options.backend || StorageBackend.INDEXED_DB;
        this.enableFallbacks = options.enableFallbacks !== false;

        // Storage backends
        this.backends = new Map();
        this.memoryCache = new Map();
        this.memoryCacheSize = 0;

        // Initialize storage backends
        this._initializeBackends();

        logger.info('💾 Cache Manager initialized', {
            namespace: this.namespace,
            preferredBackend: this.preferredBackend,
            enableFallbacks: this.enableFallbacks
        });
    }

    /**
     * Initialize available storage backends
     */
    async _initializeBackends() {
        try {
            // IndexedDB backend
            if (typeof window !== 'undefined' && window.indexedDB) {
                this.backends.set(StorageBackend.INDEXED_DB, await this._initIndexedDB());
                logger.debug('✅ IndexedDB backend initialized');
            }

            // File System Access API backend (Chrome/Edge)
            if (typeof window !== 'undefined' && 'showDirectoryPicker' in window) {
                this.backends.set(StorageBackend.FILE_SYSTEM, this._initFileSystemAPI());
                logger.debug('✅ File System API backend available');
            }

            // Server upload backend
            this.backends.set(StorageBackend.SERVER_UPLOAD, this._initServerBackend());
            logger.debug('✅ Server backend initialized');

            // Memory backend (always available)
            this.backends.set(StorageBackend.MEMORY, this._initMemoryBackend());
            logger.debug('✅ Memory backend initialized');

        } catch (error) {
            logger.error('❌ Backend initialization failed:', error);
        }
    }

    /**
     * Initialize IndexedDB backend
     */
    async _initIndexedDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(`${CACHE_CONFIG.dbName}_${this.namespace}`, CACHE_CONFIG.dbVersion);

            request.onerror = () => reject(new Error('IndexedDB initialization failed'));

            request.onupgradeneeded = (event) => {
                const db = event.target.result;

                // Create object stores if they don't exist
                Object.values(CACHE_CONFIG.stores).forEach(storeName => {
                    if (!db.objectStoreNames.contains(storeName)) {
                        const store = db.createObjectStore(storeName, { keyPath: 'key' });
                        store.createIndex('timestamp', 'timestamp', { unique: false });
                        store.createIndex('ttl', 'ttl', { unique: false });
                    }
                });
            };

            request.onsuccess = () => resolve(request.result);
        });
    }

    /**
     * Initialize File System API backend
     */
    _initFileSystemAPI() {
        return {
            type: StorageBackend.FILE_SYSTEM,
            available: true,
            directoryHandle: null
        };
    }

    /**
     * Initialize server backend
     */
    _initServerBackend() {
        return {
            type: StorageBackend.SERVER_UPLOAD,
            available: true,
            baseUrl: buildDownloadServerUrl('/')
        };
    }

    /**
     * Initialize memory backend
     */
    _initMemoryBackend() {
        return {
            type: StorageBackend.MEMORY,
            available: true,
            cache: this.memoryCache
        };
    }

    /**
     * Save file to cache using preferred backend with fallbacks
     */
    async saveFile(filePath, data, options = {}) {
        const {
            backend = this.preferredBackend,
            ttl = CACHE_CONFIG.defaultTTL,
            metadata = {},
            forceOverwrite = false
        } = options;

        logger.info(`💾 Saving file to cache: ${filePath}`, {
            backend,
            size: data.byteLength || data.length,
            ttl
        });

        try {
            // Try preferred backend first
            const result = await this._saveToBackend(backend, filePath, data, { ttl, metadata, forceOverwrite });
            if (result.success) {
                logger.debug(`✅ File saved to ${backend}: ${filePath}`);
                return result;
            }
        } catch (error) {
            logger.warn(`⚠️ ${backend} save failed for ${filePath}:`, error.message);
        }

        // Try fallback backends if enabled
        if (this.enableFallbacks) {
            const fallbacks = Array.from(this.backends.keys()).filter(b => b !== backend);

            for (const fallbackBackend of fallbacks) {
                try {
                    const result = await this._saveToBackend(fallbackBackend, filePath, data, { ttl, metadata, forceOverwrite });
                    if (result.success) {
                        logger.info(`✅ File saved to fallback ${fallbackBackend}: ${filePath}`);
                        return result;
                    }
                } catch (error) {
                    logger.debug(`❌ Fallback ${fallbackBackend} failed:`, error.message);
                }
            }
        }

        throw new Error(`Failed to save file ${filePath} to any backend`);
    }

    /**
     * Load file from cache
     */
    async loadFile(filePath, options = {}) {
        const { backend = this.preferredBackend } = options;

        logger.debug(`📂 Loading file from cache: ${filePath}`);

        try {
            // Try preferred backend first
            const result = await this._loadFromBackend(backend, filePath);
            if (result.success) {
                logger.debug(`✅ File loaded from ${backend}: ${filePath}`);
                return result;
            }
        } catch (error) {
            logger.debug(`❌ ${backend} load failed for ${filePath}:`, error.message);
        }

        // Try all available backends
        if (this.enableFallbacks) {
            const allBackends = Array.from(this.backends.keys()).filter(b => b !== backend);

            for (const fallbackBackend of allBackends) {
                try {
                    const result = await this._loadFromBackend(fallbackBackend, filePath);
                    if (result.success) {
                        logger.debug(`✅ File loaded from fallback ${fallbackBackend}: ${filePath}`);
                        return result;
                    }
                } catch (error) {
                    logger.debug(`❌ Fallback ${fallbackBackend} failed:`, error.message);
                }
            }
        }

        return { success: false, error: 'File not found in any backend' };
    }

    /**
     * Check if file exists in cache
     */
    async hasFile(filePath, options = {}) {
        const { backend = this.preferredBackend } = options;

        try {
            const result = await this._loadFromBackend(backend, filePath);
            return result.success;
        } catch (error) {
            return false;
        }
    }

    /**
     * Delete file from cache
     */
    async deleteFile(filePath, options = {}) {
        const { backend = this.preferredBackend, allBackends = false } = options;

        if (allBackends) {
            // Delete from all backends
            const results = [];
            for (const backendType of this.backends.keys()) {
                try {
                    const result = await this._deleteFromBackend(backendType, filePath);
                    results.push({ backend: backendType, success: result.success });
                } catch (error) {
                    results.push({ backend: backendType, success: false, error: error.message });
                }
            }
            return results;
        } else {
            // Delete from specific backend
            return await this._deleteFromBackend(backend, filePath);
        }
    }

    /**
     * Clear expired entries
     */
    async clearExpired() {
        logger.info('🧹 Clearing expired cache entries...');

        const now = Date.now();
        let totalCleared = 0;

        for (const [backendType, backend] of this.backends) {
            try {
                const cleared = await this._clearExpiredFromBackend(backendType, now);
                totalCleared += cleared;
                logger.debug(`🗑️ Cleared ${cleared} expired entries from ${backendType}`);
            } catch (error) {
                logger.error(`❌ Failed to clear expired entries from ${backendType}:`, error);
            }
        }

        logger.info(`✅ Cleared ${totalCleared} expired cache entries`);
        return totalCleared;
    }

    /**
     * Get cache statistics
     */
    async getStats() {
        const stats = {
            namespace: this.namespace,
            backends: {},
            totalSize: 0,
            totalFiles: 0
        };

        for (const [backendType, backend] of this.backends) {
            try {
                const backendStats = await this._getBackendStats(backendType);
                stats.backends[backendType] = backendStats;
                stats.totalSize += backendStats.size || 0;
                stats.totalFiles += backendStats.count || 0;
            } catch (error) {
                stats.backends[backendType] = { error: error.message };
            }
        }

        return stats;
    }

    // Backend-specific implementation methods

    /**
     * Save to specific backend
     */
    async _saveToBackend(backend, filePath, data, options) {
        switch (backend) {
            case StorageBackend.INDEXED_DB:
                return await this._saveToIndexedDB(filePath, data, options);

            case StorageBackend.FILE_SYSTEM:
                return await this._saveToFileSystem(filePath, data, options);

            case StorageBackend.SERVER_UPLOAD:
                return await this._saveToServer(filePath, data, options);

            case StorageBackend.MEMORY:
                return await this._saveToMemory(filePath, data, options);

            default:
                throw new Error(`Unsupported backend: ${backend}`);
        }
    }

    /**
     * Load from specific backend
     */
    async _loadFromBackend(backend, filePath) {
        switch (backend) {
            case StorageBackend.INDEXED_DB:
                return await this._loadFromIndexedDB(filePath);

            case StorageBackend.FILE_SYSTEM:
                return await this._loadFromFileSystem(filePath);

            case StorageBackend.SERVER_UPLOAD:
                return await this._loadFromServer(filePath);

            case StorageBackend.MEMORY:
                return await this._loadFromMemory(filePath);

            default:
                throw new Error(`Unsupported backend: ${backend}`);
        }
    }

    /**
     * Delete from specific backend
     */
    async _deleteFromBackend(backend, filePath) {
        switch (backend) {
            case StorageBackend.INDEXED_DB:
                return await this._deleteFromIndexedDB(filePath);

            case StorageBackend.FILE_SYSTEM:
                return await this._deleteFromFileSystem(filePath);

            case StorageBackend.SERVER_UPLOAD:
                return await this._deleteFromServer(filePath);

            case StorageBackend.MEMORY:
                return await this._deleteFromMemory(filePath);

            default:
                throw new Error(`Unsupported backend: ${backend}`);
        }
    }

    // IndexedDB implementation
    async _saveToIndexedDB(filePath, data, options) {
        const db = this.backends.get(StorageBackend.INDEXED_DB);
        if (!db) throw new Error('IndexedDB not available');

        return new Promise((resolve, reject) => {
            const transaction = db.transaction([CACHE_CONFIG.stores.models], 'readwrite');
            const store = transaction.objectStore(CACHE_CONFIG.stores.models);

            const record = {
                key: filePath,
                data: data,
                timestamp: Date.now(),
                ttl: options.ttl,
                metadata: options.metadata,
                size: data.byteLength || data.length
            };

            const request = store.put(record);

            request.onsuccess = () => resolve({
                success: true,
                backend: StorageBackend.INDEXED_DB,
                size: record.size
            });

            request.onerror = () => reject(new Error('IndexedDB save failed'));
        });
    }

    async _loadFromIndexedDB(filePath) {
        const db = this.backends.get(StorageBackend.INDEXED_DB);
        if (!db) throw new Error('IndexedDB not available');

        return new Promise((resolve) => {
            const transaction = db.transaction([CACHE_CONFIG.stores.models], 'readonly');
            const store = transaction.objectStore(CACHE_CONFIG.stores.models);
            const request = store.get(filePath);

            request.onsuccess = () => {
                const result = request.result;
                if (!result) {
                    resolve({ success: false, error: 'File not found' });
                    return;
                }

                // Check TTL
                if (result.ttl && (Date.now() - result.timestamp) > result.ttl) {
                    resolve({ success: false, error: 'File expired' });
                    return;
                }

                resolve({
                    success: true,
                    data: result.data,
                    metadata: result.metadata,
                    timestamp: result.timestamp
                });
            };

            request.onerror = () => resolve({ success: false, error: 'IndexedDB load failed' });
        });
    }

    // Memory implementation
    async _saveToMemory(filePath, data, options) {
        const size = data.byteLength || data.length;

        // Check memory limit
        if (this.memoryCacheSize + size > CACHE_CONFIG.maxMemoryCache) {
            await this._evictFromMemory();
        }

        const record = {
            data: data,
            timestamp: Date.now(),
            ttl: options.ttl,
            metadata: options.metadata,
            size: size
        };

        this.memoryCache.set(filePath, record);
        this.memoryCacheSize += size;

        return { success: true, backend: StorageBackend.MEMORY, size };
    }

    async _loadFromMemory(filePath) {
        const record = this.memoryCache.get(filePath);
        if (!record) {
            return { success: false, error: 'File not found' };
        }

        // Check TTL
        if (record.ttl && (Date.now() - record.timestamp) > record.ttl) {
            this.memoryCache.delete(filePath);
            this.memoryCacheSize -= record.size;
            return { success: false, error: 'File expired' };
        }

        return {
            success: true,
            data: record.data,
            metadata: record.metadata,
            timestamp: record.timestamp
        };
    }

    // Server implementation  
    async _saveToServer(filePath, data, options) {
        const backend = this.backends.get(StorageBackend.SERVER_UPLOAD);
        if (!backend) throw new Error('Server backend not available');

        // Use existing ModelDownloader logic
        const formData = new FormData();
        const blob = new Blob([data], { type: 'application/octet-stream' });
        const filename = filePath.split('/').pop();
        const directory = filePath.split('/').slice(0, -1).join('/');

        formData.append('path', directory);
        formData.append('file', blob, filename);

        const response = await fetch(buildDownloadServerUrl('/upload'), {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`Server upload failed: ${response.status}`);
        }

        return {
            success: true,
            backend: StorageBackend.SERVER_UPLOAD,
            size: data.byteLength || data.length
        };
    }

    async _loadFromServer(filePath) {
        try {
            const url = buildDownloadServerUrl(`/${filePath}`);
            const response = await fetch(url);

            if (!response.ok) {
                return { success: false, error: `Server load failed: ${response.status}` };
            }

            const data = await response.arrayBuffer();
            return {
                success: true,
                data: data,
                metadata: {},
                timestamp: Date.now()
            };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // Utility methods
    async _evictFromMemory() {
        // Simple LRU eviction - remove oldest entries
        const entries = Array.from(this.memoryCache.entries());
        entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

        const toEvict = entries.slice(0, Math.floor(entries.length * 0.3));

        for (const [key, record] of toEvict) {
            this.memoryCache.delete(key);
            this.memoryCacheSize -= record.size;
        }

        logger.debug(`🗑️ Evicted ${toEvict.length} entries from memory cache`);
    }

    // Placeholder implementations for File System API
    async _saveToFileSystem(filePath, data, options) {
        throw new Error('File System API not yet implemented');
    }

    async _loadFromFileSystem(filePath) {
        throw new Error('File System API not yet implemented');
    }

    // Additional placeholder methods for completeness
    async _deleteFromIndexedDB(filePath) {
        // Implementation for IndexedDB deletion
        return { success: true };
    }

    async _deleteFromMemory(filePath) {
        const record = this.memoryCache.get(filePath);
        if (record) {
            this.memoryCache.delete(filePath);
            this.memoryCacheSize -= record.size;
        }
        return { success: true };
    }

    async _deleteFromServer(filePath) {
        // Implementation for server deletion
        return { success: true };
    }

    async _deleteFromFileSystem(filePath) {
        // Implementation for file system deletion
        return { success: true };
    }

    async _clearExpiredFromBackend(backendType, now) {
        // Implementation for clearing expired entries
        return 0;
    }

    async _getBackendStats(backendType) {
        // Implementation for getting backend statistics
        return { count: 0, size: 0 };
    }
}

export default CacheManager;
