# MediaPipe Consolidation and Caching Enhancement

## Overview

This consolidation addresses the MediaPipe WASM loading issues by creating a unified module system and generalized caching architecture. The changes prioritize local-first caching to prevent network dependency failures.

## Key Changes Made

### 1. MediaPipe Module (`src/modules/mediapipe/index.js`)

**Purpose**: Consolidated MediaPipe management for all detection modes
**Features**:
- Unified initialization with local-first WASM loading strategy
- Support for multiple detection modes (pose-only, hands-only, pose+hands, holistic)
- Advanced caching with fallback strategies
- Error handling and recovery mechanisms

**Key Methods**:
- `initialize()`: Handles WASM and model loading with local-first strategy
- `detect()`: Unified detection interface for all modes
- `_downloadAndCacheWasm()`: Downloads and caches WASM files locally
- `_initializeWasmWithCaching()`: Tries local WASM files before CDN

### 2. Common Cache Manager (`src/modules/common/cacheManager.js`)

**Purpose**: Generalized caching system for models, WASM files, and assets
**Features**:
- Multiple storage backends (IndexedDB, File System API, Server Upload, Memory)
- Automatic fallback between storage methods
- TTL (Time To Live) support for cache expiration
- Memory management with LRU eviction

**Storage Backends**:
- **IndexedDB**: Primary browser storage for models/WASM
- **Server Upload**: Server-side storage for sharing across sessions
- **Memory**: Fast temporary storage with size limits
- **File System API**: Future support for local file access

### 3. Checkpoint Manager Middleware (`src/server/middleware/checkpointManager.js`)

**Purpose**: Generalized checkpoint downloading and inference management
**Features**:
- Support for multiple model types (MediaPipe, Sherpa-ONNX, HuggingFace, Custom)
- Retry logic with exponential backoff
- Progress tracking for downloads
- RESTful API for checkpoint management

**Endpoints**:
- `POST /api/checkpoints/download`: Download model checkpoints
- `POST /api/checkpoints/upload`: Upload custom checkpoints
- `GET /api/checkpoints/list`: List available checkpoints
- `DELETE /api/checkpoints/:modelType/:checkpointId`: Delete checkpoints
- `GET /api/checkpoints/health`: Health check

### 4. Enhanced PoseDetector (`src/recognition/poseDetector.js`)

**Changes**:
- Replaced direct MediaPipe usage with consolidated MediaPipe module
- Simplified initialization and detection logic
- Improved error handling and logging
- Backward compatibility maintained

**Benefits**:
- Eliminates network dependency issues
- Faster initialization through cached models
- More reliable operation in offline scenarios

### 5. Enhanced ContextualAnalysisService

**Changes**:
- Integrated MediaPipe module for visual analysis
- Added holistic detection for comprehensive multimodal analysis
- Improved visual context tracking

## Network Issue Resolution

### Problem
```
GET https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@latest/wasm/vision_wasm_internal.wasm net::ERR_CONNECTION_CLOSED
```

### Solution Strategy

1. **Local-First WASM Loading**:
   ```javascript
   // Try local paths first
   const localWasmPaths = [
       '/assets/mediapipe/wasm/',
       '/public/assets/mediapipe/wasm/',
       '/models/mediapipe/wasm/'
   ];
   ```

2. **Download and Cache**:
   ```javascript
   // Download WASM files and cache locally
   await this._downloadAndCacheWasm();
   ```

3. **CDN Fallback**:
   ```javascript
   // Final fallback to CDN only if local methods fail
   const vision = await FilesetResolver.forVisionTasks(cdnFallbackUrl);
   ```

### Cache Hierarchy
1. **Memory Cache**: Fastest access for frequently used models
2. **IndexedDB**: Persistent browser storage
3. **Server Storage**: Shared across sessions and users
4. **CDN**: Last resort fallback

## Usage Examples

### MediaPipe Module
```javascript
import MediaPipeModule, { DetectionMode } from '../modules/mediapipe/index.js';

const mediaPipe = new MediaPipeModule({
    detectionMode: DetectionMode.HOLISTIC,
    uiManager: uiManager
});

await mediaPipe.initialize();
const results = await mediaPipe.detect(video, timestamp);
```

### Cache Manager
```javascript
import { CacheManager } from '../modules/common/cacheManager.js';

const cache = new CacheManager({ namespace: 'models' });
await cache.saveFile('model.task', modelBuffer);
const result = await cache.loadFile('model.task');
```

### Checkpoint Manager API
```bash
# Download a checkpoint
curl -X POST http://localhost:3000/api/checkpoints/download \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com/model.task", "modelType": "MEDIAPIPE", "checkpointId": "pose_model_v1"}'

# List checkpoints
curl http://localhost:3000/api/checkpoints/list/MEDIAPIPE
```

## Benefits

### 1. Network Resilience
- Eliminates dependency on external CDNs for critical WASM files
- Graceful degradation when network is unavailable
- Faster loading through local caching

### 2. Performance Improvements
- Reduced initialization time through cached models
- Memory-efficient model management
- Background downloading and caching

### 3. Developer Experience
- Unified API for all MediaPipe operations
- Better error messages and logging
- Simplified integration for new components

### 4. Maintainability
- Centralized model and caching logic
- Consistent patterns across different model types
- Easier testing and debugging

## Migration Path

### For Existing Code
The changes maintain backward compatibility. Existing `PoseDetector` usage continues to work:

```javascript
// This still works unchanged
const poseDetector = new PoseDetector({ 
    detectionMode: 'pose_only',
    uiManager: uiManager 
});
await poseDetector.initialize();
const results = await poseDetector.detectPose(video, timestamp);
```

### For New Implementations
Use the MediaPipe module directly for more control:

```javascript
// New recommended approach
import MediaPipeModule from '../modules/mediapipe/index.js';
const mediaPipe = new MediaPipeModule(options);
await mediaPipe.initialize();
const results = await mediaPipe.detect(video, timestamp);
```

## Configuration

### Environment Variables
```env
# Cache configuration
VITE_CACHE_MAX_SIZE=500000000
VITE_CACHE_TTL=86400000

# Model download configuration  
VITE_MODEL_DOWNLOAD_RETRIES=3
VITE_MODEL_DOWNLOAD_TIMEOUT=30000
```

### Model Configurations
Update `src/config/models.js` to include local paths:
```javascript
export const MODEL_CONFIGS = {
    mediapipe: {
        holistic: {
            modelPath: 'mediapipe/holistic_landmarker.task',
            fallbackPath: 'https://storage.googleapis.com/...',
            localPaths: ['/assets/mediapipe/wasm/']
        }
    }
};
```

## Testing

### Unit Tests
- Cache manager functionality
- MediaPipe module initialization
- Checkpoint manager endpoints

### Integration Tests  
- Full MediaPipe pipeline with caching
- Network failure scenarios
- Cache persistence across sessions

### Performance Tests
- Model loading times with/without cache
- Memory usage patterns
- Concurrent download handling

## Future Enhancements

1. **Model Versioning**: Track and manage model versions
2. **Automatic Updates**: Background model updates
3. **Compression**: Compress cached models for space efficiency
4. **Analytics**: Track cache hit rates and performance metrics
5. **P2P Sharing**: Share models between browser instances

This consolidation provides a robust, efficient, and maintainable foundation for MediaPipe operations while solving the immediate network connectivity issues.
