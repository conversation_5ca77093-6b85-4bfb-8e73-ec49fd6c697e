/**
 * MediaPipe Module
 * 
 * Consolidated MediaPipe management for pose detection, hand tracking, and contextual analysis.
 * Provides unified initialization, model loading, and caching strategies.
 */

import { FilesetResolver, HolisticLandmarker, PoseLandmarker, HandLandmarker } from '@mediapipe/tasks-vision';
import { MODEL_CONFIGS } from '../../config/models.js';
import ModelDownloader from '../../utils/modelDownloader.js';
import { CacheManager, StorageBackend } from '../common/cacheManager.js';
import { createLogger, LogLevel } from '@/utils/logger';
import { getDownloadServerUrl, buildDownloadServerUrl } from '../../utils/portManager.js';

const logger = createLogger('MediaPipeModule', LogLevel.DEBUG);

// Detection modes enum
export const DetectionMode = {
    POSE_ONLY: 'pose_only',
    HANDS_ONLY: 'hands_only',
    POSE_AND_HANDS: 'pose_and_hands',
    HOLISTIC: 'holistic'
};

// WASM configuration - use full download server URLs for MediaPipe
const getWasmConfig = () => {
    const downloadServerUrl = getDownloadServerUrl();
    return {
        // Full URLs for MediaPipe FilesetResolver (it needs absolute URLs)
        localPaths: [
            `${downloadServerUrl}/models/mediapipe/wasm`
        ],
        // Same URLs for availability checking
        downloadServerPaths: [
            `${downloadServerUrl}/models/mediapipe/wasm`
        ],
        cdnFallback: "https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@latest/wasm",
        wasmFiles: [
            'vision_wasm_internal.wasm',
            'vision_wasm_internal.js'
        ]
    };
};

/**
 * MediaPipe Module Class
 * Manages MediaPipe initialization, model loading, and detector lifecycle
 */
export class MediaPipeModule {
    constructor(options = {}) {
        this.uiManager = options.uiManager;
        this.detectionMode = options.detectionMode || DetectionMode.POSE_ONLY;
        this.debug = options.debug || false;

        // Initialize subsystems with server backend for filesystem storage
        this.cacheManager = new CacheManager({
            namespace: 'mediapipe',
            uiManager: this.uiManager,
            backend: StorageBackend.SERVER_UPLOAD,  // Use server backend to save files to filesystem
            enableFallbacks: true  // Allow fallback to other backends if needed
        });
        this.modelDownloader = new ModelDownloader({ uiManager: this.uiManager });

        // Detector instances
        this.detectors = {
            holistic: null,
            pose: null,
            hand: null
        };

        // State management
        this.isInitialized = false;
        this.initializationPromise = null;
        this.wasmInitialized = false;

        // Model configurations
        this.modelConfigs = this._getModelConfigs();

        logger.info('🎭 MediaPipe Module initialized', {
            detectionMode: this.detectionMode,
            modelsAvailable: Object.keys(this.modelConfigs)
        });
    }

    /**
     * Initialize MediaPipe with local-first caching strategy
     */
    async initialize(canvas = null) {
        if (this.initializationPromise) {
            return this.initializationPromise;
        }

        this.initializationPromise = this._performInitialization(canvas);
        return this.initializationPromise;
    }

    /**
     * Initialize just the WASM files (for backward compatibility)
     */
    async initializeWasm() {
        return await this._initializeWasmWithCaching();
    }

    /**
     * Internal initialization logic
     */
    async _performInitialization(canvas) {
        try {
            logger.info('🚀 Starting MediaPipe initialization...');

            // Step 1: Initialize WASM with local-first strategy
            await this._initializeWasmWithCaching();

            // Step 2: Download and cache required models
            await this._ensureModelsAvailable();

            // Step 3: Initialize detectors based on detection mode
            await this._initializeDetectors(canvas);

            this.isInitialized = true;
            logger.info('✅ MediaPipe initialization complete');

        } catch (error) {
            logger.error('❌ MediaPipe initialization failed:', error);
            throw new Error(`MediaPipe initialization failed: ${error.message}`);
        }
    }

    /**
     * Initialize WASM files with local-first caching strategy
     */
    async _initializeWasmWithCaching() {
        if (this.wasmInitialized) {
            return;
        }

        logger.info('🔧 Initializing MediaPipe WASM...');

        const wasmConfig = getWasmConfig();
        logger.debug('WASM configuration:', {
            downloadServerUrl: getDownloadServerUrl(),
            localPaths: wasmConfig.localPaths,
            cdnFallback: wasmConfig.cdnFallback
        });

        // Check if WASM files are available and try to load them
        for (const wasmUrl of wasmConfig.localPaths) {
            try {
                // Check if WASM files are available
                const testUrl = `${wasmUrl}/vision_wasm_internal.wasm`;
                logger.debug(`Checking WASM availability at: ${testUrl}`);

                const testResponse = await fetch(testUrl, { method: 'HEAD' });
                if (testResponse.ok) {
                    logger.debug(`WASM files available at: ${wasmUrl}`);

                    // Try to initialize with the full URL for MediaPipe
                    try {
                        const vision = await FilesetResolver.forVisionTasks(wasmUrl);
                        this.vision = vision;
                        this.wasmInitialized = true;
                        logger.info(`✅ WASM loaded from: ${wasmUrl}`);
                        return;
                    } catch (error) {
                        logger.debug(`❌ WASM initialization failed for: ${wasmUrl} - ${error.message}`);
                    }
                }
            } catch (error) {
                logger.debug(`❌ WASM availability check failed: ${wasmUrl} - ${error.message}`);
            }
        }

        // Try downloading and caching WASM files
        logger.info('📥 Downloading and caching WASM files...');
        try {
            await this._downloadAndCacheWasm();

            // Retry with cached files using download server URLs
            for (const wasmUrl of wasmConfig.localPaths) {
                try {
                    const vision = await FilesetResolver.forVisionTasks(wasmUrl);
                    this.vision = vision;
                    this.wasmInitialized = true;
                    logger.info(`✅ WASM loaded from cached location: ${wasmUrl}`);
                    return;
                } catch (error) {
                    continue;
                }
            }
        } catch (cacheError) {
            logger.warn('⚠️ WASM caching failed:', cacheError.message);
        }

        // Final fallback to CDN
        logger.info('🌐 Falling back to CDN for WASM files...');
        try {
            const vision = await FilesetResolver.forVisionTasks(wasmConfig.cdnFallback);
            this.vision = vision;
            this.wasmInitialized = true;
            logger.info('✅ WASM loaded from CDN');
        } catch (cdnError) {
            throw new Error(`All WASM loading strategies failed. CDN error: ${cdnError.message}`);
        }
    }

    /**
     * Download and cache WASM files locally
     */
    async _downloadAndCacheWasm() {
        this.uiManager?.showLoadingPanel('Downloading MediaPipe WASM files...');

        const wasmConfig = getWasmConfig();

        try {
            for (const wasmFile of wasmConfig.wasmFiles) {
                const url = `${wasmConfig.cdnFallback}/${wasmFile}`;

                logger.info(`📦 Downloading ${wasmFile} from ${url}...`);
                this.uiManager?.updateLoadingProgress(`Downloading ${wasmFile}...`);

                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`Failed to download ${wasmFile}: ${response.status}`);
                }

                const wasmBuffer = await response.arrayBuffer();

                // Cache the file using the cache manager
                const localPath = `models/mediapipe/wasm/${wasmFile}`;
                await this.cacheManager.saveFile(localPath, wasmBuffer);

                logger.info(`✅ WASM file cached: ${localPath}`);
            }

            logger.info('✅ All WASM files cached successfully');
        } finally {
            this.uiManager?.hideLoadingPanel();
        }
    }

    /**
     * Ensure all required models are available locally
     */
    async _ensureModelsAvailable() {
        const requiredModels = this._getRequiredModels();

        for (const [modelType, config] of Object.entries(requiredModels)) {
            logger.info(`🔍 Ensuring ${modelType} model is available...`);
            try {
                await this.modelDownloader.ensureModelExists(config);
                logger.debug(`✅ ${modelType} model ready`);
            } catch (error) {
                logger.error(`❌ Failed to ensure ${modelType} model:`, error);
                throw error;
            }
        }
    }

    /**
     * Initialize detectors based on detection mode
     */
    async _initializeDetectors(canvas) {
        const delegate = await this._getBestDelegate();

        switch (this.detectionMode) {
            case DetectionMode.HOLISTIC:
                await this._initializeHolisticDetector(delegate, canvas);
                break;

            case DetectionMode.POSE_ONLY:
                await this._initializePoseDetector(delegate, canvas);
                break;

            case DetectionMode.HANDS_ONLY:
                await this._initializeHandDetector(delegate, canvas);
                break;

            case DetectionMode.POSE_AND_HANDS:
                await this._initializePoseDetector(delegate, canvas);
                await this._initializeHandDetector(delegate, canvas);
                break;

            default:
                throw new Error(`Unsupported detection mode: ${this.detectionMode}`);
        }
    }

    /**
     * Initialize holistic detector
     */
    async _initializeHolisticDetector(delegate, canvas) {
        const config = this.modelConfigs.HOLISTIC;

        try {
            this.detectors.holistic = await HolisticLandmarker.createFromOptions(
                this.vision,
                {
                    baseOptions: {
                        modelAssetPath: await this.modelDownloader.ensureModelExists(config),
                        delegate: delegate
                    },
                    ...config.options
                }
            );

            logger.info('✅ Holistic detector initialized');
        } catch (error) {
            logger.error('❌ Holistic detector initialization failed:', error);
            throw error;
        }
    }

    /**
     * Initialize pose detector
     */
    async _initializePoseDetector(delegate, canvas) {
        const config = this.modelConfigs.POSE;

        try {
            this.detectors.pose = await PoseLandmarker.createFromOptions(
                this.vision,
                {
                    baseOptions: {
                        modelAssetPath: await this.modelDownloader.ensureModelExists(config),
                        delegate: delegate
                    },
                    ...config.options
                }
            );

            logger.info('✅ Pose detector initialized');
        } catch (error) {
            logger.error('❌ Pose detector initialization failed:', error);
            throw error;
        }
    }

    /**
     * Initialize hand detector
     */
    async _initializeHandDetector(delegate, canvas) {
        const config = this.modelConfigs.HAND;

        try {
            this.detectors.hand = await HandLandmarker.createFromOptions(
                this.vision,
                {
                    baseOptions: {
                        modelAssetPath: await this.modelDownloader.ensureModelExists(config),
                        delegate: delegate
                    },
                    ...config.options
                }
            );

            logger.info('✅ Hand detector initialized');
        } catch (error) {
            logger.error('❌ Hand detector initialization failed:', error);
            throw error;
        }
    }

    /**
     * Detect pose/hands from video frame
     */
    async detect(video, timestamp) {
        if (!this.isInitialized) {
            throw new Error('MediaPipe module not initialized');
        }

        // Input validation
        if (!video || video.readyState !== 4 || video.videoWidth === 0 || video.videoHeight === 0) {
            return null;
        }

        try {
            switch (this.detectionMode) {
                case DetectionMode.HOLISTIC:
                    return await this._detectHolistic(video, timestamp);

                case DetectionMode.POSE_ONLY:
                    return await this._detectPose(video, timestamp);

                case DetectionMode.HANDS_ONLY:
                    return await this._detectHands(video, timestamp);

                case DetectionMode.POSE_AND_HANDS:
                    return await this._detectPoseAndHands(video, timestamp);

                default:
                    throw new Error(`Unsupported detection mode: ${this.detectionMode}`);
            }
        } catch (error) {
            logger.error('❌ Detection failed:', error);
            return null;
        }
    }

    /**
     * Detect using holistic model
     */
    async _detectHolistic(video, timestamp) {
        const results = this.detectors.holistic.detectForVideo(video, timestamp);
        return this._normalizeHolisticResults(results);
    }

    /**
     * Detect using pose model
     */
    async _detectPose(video, timestamp) {
        const results = this.detectors.pose.detectForVideo(video, timestamp);
        return this._normalizePoseResults(results);
    }

    /**
     * Detect using hand model
     */
    async _detectHands(video, timestamp) {
        const results = this.detectors.hand.detectForVideo(video, timestamp);
        return this._normalizeHandResults(results);
    }

    /**
     * Detect using both pose and hand models
     */
    async _detectPoseAndHands(video, timestamp) {
        const [poseResults, handResults] = await Promise.all([
            this._detectPose(video, timestamp),
            this._detectHands(video, timestamp)
        ]);

        return this._combinePoseAndHandResults(poseResults, handResults, timestamp);
    }

    /**
     * Get model configurations
     */
    _getModelConfigs() {
        return {
            HOLISTIC: {
                modelPath: MODEL_CONFIGS.mediapipe?.holistic?.modelPath || 'mediapipe/holistic_landmarker.task',
                fallbackPath: MODEL_CONFIGS.mediapipe?.holistic?.fallbackPath || 'https://storage.googleapis.com/mediapipe-models/holistic_landmarker/holistic_landmarker/float16/latest/holistic_landmarker.task',
                options: {
                    runningMode: "VIDEO",
                    minFaceDetectionConfidence: 0.5,
                    minFacePresenceConfidence: 0.5,
                    minFaceSuppressionThreshold: 0.3,
                    outputFaceBlendshapes: false,
                    minPoseDetectionConfidence: 0.5,
                    minPoseSuppressionThreshold: 0.3,
                    minPosePresenceConfidence: 0.5,
                    outputPoseSegmentationMasks: false,
                    minHandLandmarksConfidence: 0.5,
                }
            },
            POSE: {
                modelPath: MODEL_CONFIGS.mediapipe?.pose?.modelPath || 'mediapipe/pose_landmarker_lite.task',
                fallbackPath: MODEL_CONFIGS.mediapipe?.pose?.fallbackPath || 'https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_lite/float16/1/pose_landmarker_lite.task',
                options: {
                    runningMode: "VIDEO",
                    numPoses: 1,
                    minPoseDetectionConfidence: 0.5,
                    minPosePresenceConfidence: 0.5,
                    minTrackingConfidence: 0.5,
                    outputSegmentationMasks: false,
                }
            },
            HAND: {
                modelPath: MODEL_CONFIGS.mediapipe?.hand?.modelPath || 'mediapipe/hand_landmarker.task',
                fallbackPath: MODEL_CONFIGS.mediapipe?.hand?.fallbackPath || 'https://storage.googleapis.com/mediapipe-models/hand_landmarker/hand_landmarker/float16/1/hand_landmarker.task',
                options: {
                    runningMode: "VIDEO",
                    numHands: 2,
                    minHandDetectionConfidence: 0.5,
                    minHandPresenceConfidence: 0.8,
                    minTrackingConfidence: 0.5
                },
            }
        };
    }

    /**
     * Get required models based on detection mode
     */
    _getRequiredModels() {
        switch (this.detectionMode) {
            case DetectionMode.HOLISTIC:
                return { HOLISTIC: this.modelConfigs.HOLISTIC };

            case DetectionMode.POSE_ONLY:
                return { POSE: this.modelConfigs.POSE };

            case DetectionMode.HANDS_ONLY:
                return { HAND: this.modelConfigs.HAND };

            case DetectionMode.POSE_AND_HANDS:
                return {
                    POSE: this.modelConfigs.POSE,
                    HAND: this.modelConfigs.HAND
                };

            default:
                return {};
        }
    }

    /**
     * Get best available delegate (GPU > CPU)
     */
    async _getBestDelegate() {
        try {
            // Check for WebGL support (GPU acceleration)
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
            if (gl) {
                logger.info('🚀 Using GPU delegate');
                return 'GPU';
            }
        } catch (error) {
            logger.debug('GPU check failed:', error);
        }

        logger.info('🔧 Using CPU delegate');
        return 'CPU';
    }

    // Result normalization methods
    _normalizeHolisticResults(results) {
        return {
            landmarks: results.poseLandmarks || null,
            worldLandmarks: results.poseWorldLandmarks || null,
            handPoses: {
                left: results.leftHandLandmarks ? {
                    landmarks: results.leftHandLandmarks,
                    worldLandmarks: results.leftHandWorldLandmarks
                } : null,
                right: results.rightHandLandmarks ? {
                    landmarks: results.rightHandLandmarks,
                    worldLandmarks: results.rightHandWorldLandmarks
                } : null
            },
            faceLandmarks: results.faceLandmarks || null
        };
    }

    _normalizePoseResults(results) {
        return {
            landmarks: results.landmarks || null,
            worldLandmarks: results.worldLandmarks || null,
            handPoses: {}
        };
    }

    _normalizeHandResults(results) {
        const handPoses = {};
        if (results.handedness && results.landmarks) {
            results.handedness.forEach((hand, index) => {
                const handType = hand[0].categoryName.toLowerCase();
                handPoses[handType] = {
                    landmarks: results.landmarks[index],
                    worldLandmarks: results.worldLandmarks?.[index] || results.landmarks[index]
                };
            });
        }

        return {
            landmarks: null,
            worldLandmarks: null,
            handPoses
        };
    }

    _combinePoseAndHandResults(poseResults, handResults, timestamp) {
        return {
            landmarks: poseResults?.landmarks || null,
            worldLandmarks: poseResults?.worldLandmarks || null,
            handPoses: handResults?.handPoses || {},
            timestamp
        };
    }

    /**
     * Reset and cleanup
     */
    async reset() {
        logger.info('🔄 Resetting MediaPipe module...');

        // Close detectors
        Object.values(this.detectors).forEach(detector => {
            if (detector?.close) {
                detector.close();
            }
        });

        // Reset state
        this.detectors = { holistic: null, pose: null, hand: null };
        this.isInitialized = false;
        this.initializationPromise = null;
        this.wasmInitialized = false;
        this.vision = null;

        logger.info('✅ MediaPipe module reset complete');
    }

    /**
     * Check if module is ready for detection
     */
    isReady() {
        return this.isInitialized && Object.values(this.detectors).some(detector => detector !== null);
    }

    /**
     * Ensure a model exists (for backward compatibility)
     */
    async ensureModelExists(modelConfig) {
        return await this.modelDownloader.ensureModelExists(modelConfig);
    }

    /**
     * Save model to cache (for backward compatibility)
     */
    async saveModelToCache(path, buffer) {
        return await this.cacheManager.saveFile(path, buffer);
    }
}

export default MediaPipeModule;
