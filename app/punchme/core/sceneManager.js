import {
    Engine, Scene, Vector3, FollowCamera, HemisphericLight, StandardRenderingPipeline,
    MeshBuilder, Observable, Color3, DynamicTexture, AssetsManager, BoundingInfoHelper,
    DirectionalLight, ShadowGenerator, Scalar
} from '@babylonjs/core';
import { CharacterManager } from '../characters';
import { PhysicsSystem } from './physicsSystem.js';
import { GameCameraManager } from './gameCameraManager.js';
import { createLogger } from '@/utils/logger';

// Create a global logger for this module
const logger = createLogger('SceneManager');

class SceneManager {
    constructor(canvas, worldBounds) {
        this.canvas = canvas;

        // Define physical world boundaries
        this.worldBounds = worldBounds || {
            x: { min: -7, max: 7 },    // Physical collision bounds
            y: { min: 0, max: 5 },     // Gravity effect range
            z: { min: -2, max: 10 }    // Combat area depth
        };

        // Define separate visualization bounds with wider range
        this.visualizationBounds = {
            x: { min: this.worldBounds.x.min * 1.5, max: this.worldBounds.x.max * 1.5 },
            y: { min: this.worldBounds.y.min - 1, max: this.worldBounds.y.max + 2 },
            z: { min: this.worldBounds.z.min * 1.2, max: this.worldBounds.z.max * 1.2 }
        };

        // Add boundary validation method
        this.validateBounds(this.worldBounds);
        this.validateBounds(this.visualizationBounds);

        // Replace static scale with dynamic calculation
        this.coordinateScale = {
            visualization: {
                x: 1.0,
                y: 1.0,
                z: 1.0
            },
            physics: 1.0
        };

        // Add character reference point
        this.characterCenter = new Vector3(0, 0, 0);
        this.characterBounds = null;

        // Add character metrics
        this.characterMetrics = {
            bounds: null,
            center: new Vector3(0, 0, 0),
            dimensions: {
                width: 0,
                height: 0,
                depth: 0
            },
            joints: new Map()  // Store key joint positions
        };

        // Add coordinate system default metrics
        this.defaultMetrics = {
            center: { x: 0, y: 1, z: 0 },
            dimensions: { width: 1, height: 2, depth: 1 },
            bounds: {
                min: { x: -0.5, y: 0, z: -0.5 },
                max: { x: 0.5, y: 2, z: 0.5 }
            }
        };
        // Add character loading states
        this.characterLoadingStates = {
            isInitializing: false,
            initializationPromise: null,
            loadedCharacters: new Set()
        };

        // Create initialization promise
        this.initializationPromise = this.initialize();

        // this.setupScene();
        // this.setupDefaultCamera(); // Initial camera setup
        // this.setupLighting();

        // Initialize physics first
        this.physicsSystem = new PhysicsSystem(this.scene);
        this.scene.physicsSystem = this.physicsSystem;

        // Add validation tracking
        this.lastValidatedPosition = null;

        // Add shadow properties
        this.shadows = {
            enabled: true,
            generator: null,
            resolution: 1024,
            casters: new Set(),
            receivers: new Set()
        };

    }

    async initialize() {
        try {
            await this.setupScene();
            // await this.setupDefaultCamera();
            await this.setupLighting();

            // Initialize CharacterManager after physics is ready
            this.characterManager = new CharacterManager(
                this.scene,
                this.worldBounds,
            );
            // 1) Create an instance of GameCameraManager
            this.gameCameraManager = new GameCameraManager(this.scene, this.canvas);

            // 2) Load default characters
            await this.loadDefaultCharacters();

            return true;
        } catch (error) {
            logger.error('Initialization failed:', error);
            throw error;
        }
    }

    async loadDefaultCharacters() {
        this.characterLoadingStates.isInitializing = true;

        try {
            // Ensure physics is fully initialized before creating any characters
            if (this.physicsSystem) {
                await this.physicsSystem.waitForInit();
            }

            // 1) Load enemy
            let role = "enemy";
            await this.characterManager.loadCharacter(
                "Maria J J Ong.glb",
                role,
            );
            const enemyCharacter = this.scene.getMeshByName(role);
            // Apply shadows to the character
            this.setupCharacterShadows(enemyCharacter, role);
            this.characterLoadingStates.loadedCharacters.add(role);

            // 2) Wait a frame to ensure physics is initialized
            await new Promise(resolve => requestAnimationFrame(resolve));

            // 3) Load player
            role = "player";
            await this.characterManager.loadCharacter(
                "Remy.glb",
                role
            );
            const playerCharacter = this.scene.getMeshByName(role);
            this.setupCharacterShadows(playerCharacter, role);
            this.characterLoadingStates.loadedCharacters.add(role);

            // 4) Now that both are loaded, set up the camera
            // Start with the "start camera"
            this.gameCameraManager.setupStartCamera();

            // 5) Physics collider setup - now using async methods and storing references
            const playerController = this.physicsSystem.createCharacterController(
                playerCharacter,
                { height: 1.7, mass: 1 }
            );
            const enemyController = this.physicsSystem.createCharacterController(
                enemyCharacter,
                { height: 2.2, mass: 1 }
            );
            // Setup animation-physics sync for both characters
            // this.setupAnimationPhysicsSync(playerCharacter);
            // this.setupAnimationPhysicsSync(enemyCharacter);

            return { enemyCharacter, playerCharacter };
        } catch (error) {
            logger.error('Failed to load default characters:', error);
            throw error;
        } finally {
            this.characterLoadingStates.isInitializing = false;
        }
    }
    // setupAnimationPhysicsSync(character) {
    //     if (!character) {
    //         console.warn("[SceneManager] Cannot setup physics sync: No character provided");
    //         return;
    //     }

    //     // Make sure physics system is available
    //     if (!this.physicsSystem) {
    //         console.warn(`[SceneManager] Cannot setup physics sync: Physics system not available`);
    //         return;
    //     }

    //     // Store observers in the character metadata to prevent duplicates and allow cleanup
    //     if (!character.metadata) character.metadata = {};
    //     if (character.metadata.physicsObservers) {
    //         // Clean up existing observers to prevent duplicates
    //         character.metadata.physicsObservers.forEach(observer => observer.remove());
    //     }

    //     character.metadata.physicsObservers = [];

    //     // Add frame update observer - make it specific to this character
    //     const renderObserver = this.scene.onBeforeRenderObservable.add(() => {
    //         this.physicsSystem.syncColliderWithCharacter(character);
    //     });
    //     character.metadata.physicsObservers.push({
    //         remove: () => this.scene.onBeforeRenderObservable.remove(renderObserver)
    //     });

    //     // Add skeleton animation observer if available
    //     if (character.skeleton) {
    //         const skeletonObserver = character.skeleton.onBeforeComputeObservable.add(() => {
    //             this.physicsSystem.syncColliderWithCharacter(character);
    //         });
    //         character.metadata.physicsObservers.push({
    //             remove: () => character.skeleton.onBeforeComputeObservable.remove(skeletonObserver)
    //         });
    //     }

    //     console.log(`[SceneManager] Setup animation-physics sync for: ${character.name}`);
    // }
    getCharacterManager() {
        return this.characterManager;
    }

    async waitForCharactersReady() {
        if (!this.characterLoadingStates.isInitializing &&
            this.characterLoadingStates.loadedCharacters.size === 2) {
            return true;
        }

        return new Promise((resolve) => {
            const checkReady = () => {
                if (!this.characterLoadingStates.isInitializing &&
                    this.characterLoadingStates.loadedCharacters.size === 2) {
                    resolve(true);
                } else {
                    setTimeout(checkReady, 100);
                }
            };
            checkReady();
        });
    }

    validateBounds(bounds) {
        if (!bounds || ['x', 'y', 'z'].some(axis =>
            !bounds[axis] ||
            typeof bounds[axis].min !== 'number' ||
            typeof bounds[axis].max !== 'number' ||
            bounds[axis].min >= bounds[axis].max
        )) {
            throw new Error('Invalid bounds configuration');
        }
    }

    setupScene() {
        this.engine = new Engine(this.canvas, true);
        this.scene = new Scene(this.engine);

        // Initialize AssetsManager
        this.assetsManager = new AssetsManager(this.scene);
        this.assetsManager.useDefaultLoadingScreen = true;

        // Add reference to SceneManager in scene
        this.scene.sceneManager = this;

        // Configure rendering groups for proper transparency
        this.scene.setRenderingAutoClearDepthStencil(0, true, true, true);
        this.scene.setRenderingAutoClearDepthStencil(1, true, false, true);

        // Set scene background to beautiful dark blue
        this.scene.clearColor = new Color3(0.1, 0.2, 0.3);
        this.scene.ambientColor = new Color3(0.1, 0.2, 0.3);
        this.scene.transparencyMode = Scene.TRANSPARENCYMODE_ACCUMULATE;

        // Enable alpha blending
        this.scene.alphaMode = Engine.ALPHA_ADD;

        this.scene.executeWhenReady(() => {
            logger.info("Scene ready, starting render loop");
            this.engine.runRenderLoop(() => {
                if (this.scene.activeCamera) {
                    this.scene.render();
                }
            });
        });
        const ground = MeshBuilder.CreateGround("ground", { width: 10, height: 10 }, this.scene);
        ground.position.y = this.worldBounds.y.min; // make sure it's at y=0
        this.scene.ground = ground;
        var helper = this.scene.createDefaultEnvironment({ enableGroundShadow: true });
        helper.setMainColor(Color3.Black());
        helper.ground.position.y += 0.01;
        // Add bounding box tracking
        this.boundingBoxHelper = new BoundingInfoHelper(this.engine);
        this.boundingBoxObservers = new Map();

        // // Add real-time bounding box updates
        // this.scene.onBeforeRenderObservable.add(async () => {
        //     await this.boundingBoxHelper.computeAsync(this.scene.meshes);
        //     this.notifyBoundingBoxObservers();
        // });

        // Debug option to show bounding boxes
        this.showBoundingBoxes = false;
    }

    setupLighting() {
        // Ambient light for overall scene illumination
        this.light = new HemisphericLight("hemisphericLight", new Vector3(0, 1, 0), this.scene);
        this.light.intensity = 0.5; // Reduce ambient light to make shadows more visible

        // Add directional light for shadows
        this.directionalLight = new DirectionalLight("directionalLight", new Vector3(-1, -2, -1), this.scene);
        this.directionalLight.intensity = 0.8;
        this.directionalLight.position = new Vector3(10, 20, 10);

        // Set up shadow generator
        if (this.shadows.enabled) {
            this.setupShadowGenerator();
        }
    }

    setupShadowGenerator() {
        if (!this.directionalLight) {
            logger.warn('Cannot set up shadow generator: No directional light found');
            return;
        }

        // Create shadow generator with specified resolution
        this.shadows.generator = new ShadowGenerator(this.shadows.resolution, this.directionalLight);

        // Configure shadow properties
        this.shadows.generator.useBlurExponentialShadowMap = true;
        this.shadows.generator.blurScale = 2;
        this.shadows.generator.depthScale = 50;

        // Use soft contact shadows for better quality
        this.shadows.generator.contactHardeningLightSizeUVRatio = 0.05;

        logger.info('Shadow generator configured:', {
            resolution: this.shadows.resolution,
            lightDirection: this.directionalLight.direction.asArray()
        });

        // Add existing shadow casters
        this.applyShadowsToScene();
    }

    applyShadowsToScene() {
        if (!this.shadows.generator) return;

        // Apply shadows to existing meshes based on previously registered casters and receivers
        this.shadows.casters.forEach(meshName => {
            const mesh = this.scene.getMeshByName(meshName);
            if (mesh) {
                this.addShadowCaster(mesh);
            }
        });

        this.shadows.receivers.forEach(meshName => {
            const mesh = this.scene.getMeshByName(meshName);
            if (mesh) {
                this.addShadowReceiver(mesh);
            }
        });
    }

    addShadowCaster(mesh, includeChildren = true) {
        if (!this.shadows.generator || !mesh) return;

        // Add mesh as shadow caster
        this.shadows.generator.addShadowCaster(mesh, includeChildren);
        this.shadows.casters.add(mesh.name);

        logger.debug(`Added shadow caster: ${mesh.name}`);
    }

    addShadowReceiver(mesh) {
        if (!mesh) return;

        // Set mesh to receive shadows
        mesh.receiveShadows = true;
        this.shadows.receivers.add(mesh.name);

        logger.debug(`Added shadow receiver: ${mesh.name}`);
    }

    getShadowGenerator() {
        return this.shadows.generator;
    }

    toggleShadows(enabled) {
        this.shadows.enabled = enabled;

        if (enabled && !this.shadows.generator) {
            this.setupShadowGenerator();
        } else if (!enabled && this.shadows.generator) {
            this.shadows.generator.dispose();
            this.shadows.generator = null;
        }
    }

    setupPostProcessing() {
        if (!this.camera) {
            logger.warn('Cannot setup post-processing: Camera not initialized');
            return;
        }

        const postProcess = new StandardRenderingPipeline(
            "postProcess",
            this.scene,
            1.0,
            null,
            [this.camera]
        );
        postProcess.fxaaEnabled = true;
    }

    getScene() {
        return this.scene;
    }

    getCamera() {
        return this.camera;
    }

    getEngine() {
        return this.engine;
    }

    // 添加获取坐标转换比例的方法
    getCoordinateScale() {
        return this.coordinateScale;
    }

    getWorldBounds() {
        return { ...this.worldBounds };  // Return copy to prevent modification
    }

    getVisualizationBounds() {
        return { ...this.visualizationBounds };  // Return copy to prevent modification
    }

    // setVisualizationBounds(newBounds) {
    //     this.validateBounds(newBounds);
    //     this.visualizationBounds = { ...newBounds };

    //     // Update camera if it exists
    //     if (this.camera) {
    //         this.adjustCameraToVisualizationBounds();
    //     }
    // }

    // adjustCameraToVisualizationBounds() {
    //     const bounds = this.visualizationBounds;
    //     const maxDimension = Math.max(
    //         bounds.x.max - bounds.x.min,
    //         bounds.y.max - bounds.y.min,
    //         bounds.z.max - bounds.z.min
    //     );

    //     if (this.camera) {
    //         this.camera.radius = maxDimension * 1.5;
    //         this.camera.lowerRadiusLimit = maxDimension * 0.5;
    //         this.camera.upperRadiusLimit = maxDimension * 4;
    //     }
    // }

    getMaxExpectedBoneLength() {
        // Calculate a reasonable max bone length based on visualization bounds
        const bounds = this.visualizationBounds;
        const maxDimension = Math.max(
            Math.abs(bounds.x.max - bounds.x.min),
            Math.abs(bounds.y.max - bounds.y.min),
            Math.abs(bounds.z.max - bounds.z.min)
        );
        // Return 20% of the max dimension as the expected max bone length
        return maxDimension * 0.2;
    }

    // adjustCameraForCharacters(playerHeight, enemyHeight) {
    //     if (!this.camera) {
    //         console.error('[SceneManager] No camera found to adjust');
    //         return;
    //     }

    //     // Calculate optimal camera position based on character heights
    //     const maxHeight = Math.max(playerHeight, enemyHeight);
    //     const distance = maxHeight * 2;    // 更近的距离（从 3 改为 2）
    //     const elevation = maxHeight * 1.2;  // 降低视角高度（从 1 改为 0.8）

    //     this.camera.position = new Vector3(0, elevation, -distance);
    //     this.camera.setTarget(new Vector3(0, maxHeight * 0.8, 0)); // 稍微降低目标点（从 0.5 改为 0.4）

    //     // Update camera limits for closer interaction
    //     this.camera.lowerRadiusLimit = maxHeight * 1.5;    // 更近的最小距离（从 1.5 改为 1.2）
    //     this.camera.upperRadiusLimit = maxHeight * 5;      // 更近的最大距离（从 5 改为 3）

    //     console.log('[SceneManager] Adjusted camera for characters:', {
    //         playerHeight,
    //         enemyHeight,
    //         cameraPosition: this.camera.position.asArray(),
    //         cameraTarget: this.camera.getTarget().asArray()
    //     });
    // }

    getCharacterMetrics() {
        return this.characterMetrics;
    }

    clampValue(value, min, max) {
        if (typeof value !== 'number' || typeof min !== 'number' || typeof max !== 'number') {
            logger.warn('Invalid clamp parameters:', { value, min, max });
            return 0;
        }
        return Math.min(Math.max(value, min), max);
    }

    validateMetrics(metrics) {
        if (!metrics) return false;

        // Check for required structure
        const hasValidBounds = metrics.bounds &&
            metrics.bounds.min &&
            metrics.bounds.max &&
            typeof metrics.bounds.min.x === 'number' &&
            typeof metrics.bounds.min.y === 'number' &&
            typeof metrics.bounds.min.z === 'number' &&
            typeof metrics.bounds.max.x === 'number' &&
            typeof metrics.bounds.max.y === 'number' &&
            typeof metrics.bounds.max.z === 'number';

        const hasValidDimensions = metrics.dimensions &&
            typeof metrics.dimensions.width === 'number' &&
            typeof metrics.dimensions.height === 'number' &&
            typeof metrics.dimensions.depth === 'number';

        const hasValidCenter = metrics.center &&
            typeof metrics.center.x === 'number' &&
            typeof metrics.center.y === 'number' &&
            typeof metrics.center.z === 'number';

        return hasValidBounds && hasValidDimensions && hasValidCenter;
    }

    // Add new method to get AssetsManager
    getAssetsManager() {
        return this.assetsManager;
    }

    // Add methods for bounding box tracking
    toggleBoundingBoxes(show) {
        this.showBoundingBoxes = show;
        this.scene.meshes.forEach(mesh => {
            mesh.showBoundingBox = show;
        });
    }

    // notifyBoundingBoxObservers() {
    //     // Get just the character meshes (player and enemy)
    //     const characterMeshes = this.scene.meshes.filter(mesh =>
    //         this.isCharacterMesh(mesh)
    //     );

    //     // Process only the character meshes
    //     characterMeshes.forEach(mesh => {
    //         if (!mesh.name) return;

    //         const observers = this.boundingBoxObservers.get(mesh.name);
    //         if (observers) {
    //             // Ensure bounding info is up to date
    //             mesh.computeWorldMatrix(true);
    //             const boundingInfo = mesh.getBoundingInfo();
    //             this.notifyObserversForMesh(mesh.name, mesh, boundingInfo);
    //         }
    //     });
    // }

    // notifyObserversForMesh(meshName, mesh, boundingInfo) {
    //     const observers = this.boundingBoxObservers.get(meshName);
    //     if (!observers || observers.size === 0) return;

    //     // Prepare bounding data once for all observers
    //     const boundingData = {
    //         mesh: mesh,
    //         boundingInfo: boundingInfo,
    //         dimensions: {
    //             width: boundingInfo.boundingBox.maximumWorld.x - boundingInfo.boundingBox.minimumWorld.x,
    //             height: boundingInfo.boundingBox.maximumWorld.y - boundingInfo.boundingBox.minimumWorld.y,
    //             depth: boundingInfo.boundingBox.maximumWorld.z - boundingInfo.boundingBox.minimumWorld.z
    //         },
    //         center: boundingInfo.boundingBox.centerWorld.clone()
    //     };

    //     // Notify all observers with the prepared data
    //     observers.forEach(callback => {
    //         try {
    //             callback(boundingData);
    //         } catch (error) {
    //             console.error(`[SceneManager] Error in bounding box observer for ${meshName}:`, error);
    //         }
    //     });
    // }

    // Utility method to check if a mesh is a character mesh
    isCharacterMesh(mesh) {
        // Check for mesh name matching player or enemy
        if (mesh.name === "player" || mesh.name === "enemy") {
            return true;
        }

        // Check metadata if available
        if (mesh.metadata && mesh.metadata.role) {
            const role = mesh.metadata.role.toLowerCase();
            return role === "player" || role === "enemy";
        }

        return false;
    }

    // Add new method for setting up character shadows
    setupCharacterShadows(character, role) {
        if (!this.scene.sceneManager) {
            logger.warn(`Cannot setup shadows: Scene manager not available`);
            return;
        }

        // Set up different shadow configurations based on character role
        const shadowConfig = {
            player: {
                castShadows: true,
                receiveShadows: false, // Player character doesn't need to receive shadows
                includeChildren: true
            },
            enemy: {
                castShadows: true,
                receiveShadows: true,
                includeChildren: true
            }
        };

        const config = shadowConfig[role];
        if (!config) {
            logger.warn(`No shadow configuration for role: ${role}`);
            return;
        }

        try {
            // Get the shadow generator from scene manager
            const shadowGenerator = this.scene.sceneManager.getShadowGenerator();
            if (!shadowGenerator) {
                logger.warn(`No shadow generator available`);
                return;
            }

            // Apply shadow casting for this character
            if (config.castShadows) {
                this.scene.sceneManager.addShadowCaster(character, config.includeChildren);

                // Additional handling for multi-part characters
                if (character.metadata?.isMultiPart && character.metadata.parts) {
                    character.metadata.parts.forEach(part => {
                        this.scene.sceneManager.addShadowCaster(part, config.includeChildren);
                    });
                }
            }

            // Set up shadow receiving if needed
            if (config.receiveShadows) {
                this.scene.sceneManager.addShadowReceiver(character);

                // Additional handling for multi-part characters
                if (character.metadata?.isMultiPart && character.metadata.parts) {
                    character.metadata.parts.forEach(part => {
                        this.scene.sceneManager.addShadowReceiver(part);
                    });
                }
            }

            // Add ground as shadow receiver if it exists
            const ground = this.scene.getMeshByName("ground");
            if (ground) {
                this.scene.sceneManager.addShadowReceiver(ground);
            }

            logger.info(`Shadow setup for ${role}:`, {
                castShadows: config.castShadows,
                receiveShadows: config.receiveShadows,
                shadowGenerator: !!shadowGenerator
            });

        } catch (error) {
            logger.error(`Error setting up shadows for ${role}:`, error);
        }
    }
}

export default SceneManager;
