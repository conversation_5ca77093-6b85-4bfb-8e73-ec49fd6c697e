/**
 * Character Search Tool
 * 
 * Provides intelligent character search and matching capabilities for
 * voice input processing and manual character discovery.
 */

import { createLogger } from '../../../src/utils/logger';

export class CharacterSearchTool {
    constructor(options = {}) {
        this.options = {
            enableFuzzySearch: true,
            enableSemanticSearch: false,
            searchThreshold: 0.6,
            maxResults: 10,
            enableCaching: true,
            ...options
        };

        this.logger = createLogger('CharacterSearchTool');

        // Character database
        this.characters = new Map();
        this.characterIndex = new Map(); // Search index
        this.searchCache = new Map();

        // Search configuration
        this.searchWeights = {
            name: 1.0,
            description: 0.8,
            tags: 0.9,
            aliases: 1.0,
            personality: 0.6
        };

        // Popular character database with aliases and search terms
        this.popularCharacters = new Map([
            // Anime Characters
            ['luffy', {
                id: 'luffy',
                name: 'Monkey D. Luffy',
                series: 'One Piece',
                description: 'Energetic pirate captain with rubber powers',
                aliases: ['luffy', 'straw hat', 'monkey d luffy', 'pirate king'],
                tags: ['anime', 'pirate', 'energetic', 'shounen'],
                personality: { enthusiasm: 0.95, creativity: 0.8, directness: 0.7 }
            }],
            ['naruto', {
                id: 'naruto',
                name: 'Naruto Uzumaki',
                series: 'Naruto',
                description: 'Determined ninja with fox spirit',
                aliases: ['naruto', 'naruto uzumaki', 'hokage', 'ninja'],
                tags: ['anime', 'ninja', 'determined', 'shounen'],
                personality: { enthusiasm: 0.9, empathy: 0.8, creativity: 0.7 }
            }],
            ['goku', {
                id: 'goku',
                name: 'Son Goku',
                series: 'Dragon Ball',
                description: 'Saiyan warrior who loves fighting and food',
                aliases: ['goku', 'son goku', 'kakarot', 'saiyan'],
                tags: ['anime', 'warrior', 'pure-hearted', 'shounen'],
                personality: { enthusiasm: 0.85, empathy: 0.9, directness: 0.8 }
            }],
            ['pikachu', {
                id: 'pikachu',
                name: 'Pikachu',
                series: 'Pokemon',
                description: 'Electric mouse Pokemon, loyal companion',
                aliases: ['pikachu', 'pokemon', 'electric mouse', 'pika'],
                tags: ['pokemon', 'electric', 'cute', 'loyal'],
                personality: { enthusiasm: 0.9, empathy: 0.8, creativity: 0.6 }
            }],

            // Game Characters
            ['zelda', {
                id: 'zelda',
                name: 'Princess Zelda',
                series: 'Legend of Zelda',
                description: 'Wise princess with magical powers',
                aliases: ['zelda', 'princess zelda', 'hyrule princess'],
                tags: ['game', 'princess', 'wise', 'magical'],
                personality: { formality: 0.8, empathy: 0.9, creativity: 0.7 }
            }],
            ['mario', {
                id: 'mario',
                name: 'Mario',
                series: 'Super Mario',
                description: 'Cheerful plumber who saves princesses',
                aliases: ['mario', 'super mario', 'plumber', 'mario bros'],
                tags: ['game', 'plumber', 'cheerful', 'hero'],
                personality: { enthusiasm: 0.9, empathy: 0.8, directness: 0.7 }
            }],

            // AI Personalities
            ['assistant', {
                id: 'assistant',
                name: 'AI Assistant',
                series: 'AI',
                description: 'Helpful and professional AI assistant',
                aliases: ['assistant', 'ai assistant', 'helper', 'support'],
                tags: ['ai', 'helpful', 'professional', 'knowledgeable'],
                personality: { formality: 0.7, empathy: 0.8, directness: 0.8 }
            }],
            ['friend', {
                id: 'friend',
                name: 'Friendly Companion',
                series: 'AI',
                description: 'Casual and warm conversational partner',
                aliases: ['friend', 'buddy', 'companion', 'pal'],
                tags: ['ai', 'friendly', 'casual', 'warm'],
                personality: { formality: 0.3, enthusiasm: 0.8, empathy: 0.9 }
            }],
            ['expert', {
                id: 'expert',
                name: 'Subject Expert',
                series: 'AI',
                description: 'Knowledgeable specialist in various fields',
                aliases: ['expert', 'specialist', 'professor', 'scholar'],
                tags: ['ai', 'knowledgeable', 'analytical', 'precise'],
                personality: { formality: 0.8, directness: 0.9, creativity: 0.4 }
            }],
            ['creative', {
                id: 'creative',
                name: 'Creative Mentor',
                series: 'AI',
                description: 'Imaginative and inspiring creative guide',
                aliases: ['creative', 'artist', 'mentor', 'inspiration'],
                tags: ['ai', 'creative', 'inspiring', 'imaginative'],
                personality: { formality: 0.4, enthusiasm: 0.9, creativity: 0.95 }
            }],
            ['coach', {
                id: 'coach',
                name: 'Motivational Coach',
                series: 'AI',
                description: 'Energetic and encouraging personal coach',
                aliases: ['coach', 'trainer', 'motivator', 'mentor'],
                tags: ['ai', 'motivational', 'energetic', 'supportive'],
                personality: { formality: 0.5, enthusiasm: 0.95, empathy: 0.8 }
            }]
        ]);

        this.initialize();
    }
    /**
     * Execute a character search with optional params
     * Aligns with services version used by RolePlayingPanel
     */
    async execute(params = {}) {
        const name = params.characterName || params.query || '';
        const maxResults = params.maxResults || this.options.maxResults;
        const results = this.search(name, { maxResults });
        return {
            characters: results.map(r => ({
                id: r.character.id,
                name: r.character.name,
                description: r.character.description,
                avatar: r.character.avatar || '👤',
                anime: r.character.series || 'AI',
                matchScore: r.confidence || r.score || 0.5,
                source: 'local'
            })),
            metadata: { query: name, count: results.length }
        };
    }

    /**
     * Initialize the character search tool
     */
    async initialize() {
        this.logger.info('🔍 Initializing Character Search Tool...');

        // Load popular characters into main database
        for (const [id, character] of this.popularCharacters) {
            this.addCharacter(character);
        }

        // Build search index
        this.buildSearchIndex();

        this.logger.info(`✅ Character Search Tool initialized with ${this.characters.size} characters`);
    }

    /**
     * Add a character to the search database
     */
    addCharacter(character) {
        if (!character.id || !character.name) {
            this.logger.warn('⚠️ Invalid character data:', character);
            return false;
        }

        // Ensure required fields
        const normalizedCharacter = {
            ...character,
            aliases: character.aliases || [],
            tags: character.tags || [],
            searchScore: 0,
            lastUsed: null
        };

        this.characters.set(character.id, normalizedCharacter);

        // Clear cache when adding new characters
        if (this.options.enableCaching) {
            this.searchCache.clear();
        }

        this.logger.debug('📝 Added character:', character.name);
        return true;
    }

    /**
     * Build search index for faster searches
     */
    buildSearchIndex() {
        this.characterIndex.clear();

        for (const [id, character] of this.characters) {
            const searchTerms = [
                character.name.toLowerCase(),
                ...character.aliases.map(alias => alias.toLowerCase()),
                ...character.tags.map(tag => tag.toLowerCase()),
                character.description.toLowerCase()
            ];

            // Create n-gram index for partial matching
            for (const term of searchTerms) {
                // Add full term
                this.addToIndex(term, id, 1.0);

                // Add partial terms (3+ characters)
                for (let i = 0; i <= term.length - 3; i++) {
                    for (let j = i + 3; j <= term.length; j++) {
                        const substring = term.substring(i, j);
                        this.addToIndex(substring, id, 0.7);
                    }
                }
            }
        }

        this.logger.info(`📚 Built search index with ${this.characterIndex.size} entries`);
    }

    /**
     * Add term to search index
     */
    addToIndex(term, characterId, weight) {
        if (!this.characterIndex.has(term)) {
            this.characterIndex.set(term, new Map());
        }

        const termIndex = this.characterIndex.get(term);
        const currentWeight = termIndex.get(characterId) || 0;
        termIndex.set(characterId, Math.max(currentWeight, weight));
    }

    /**
     * Search for characters by query
     */
    search(query, options = {}) {
        const searchOptions = {
            maxResults: this.options.maxResults,
            threshold: this.options.searchThreshold,
            fuzzy: this.options.enableFuzzySearch,
            ...options
        };

        if (!query || query.trim().length < 2) {
            return [];
        }

        const normalizedQuery = query.toLowerCase().trim();
        const cacheKey = `${normalizedQuery}_${JSON.stringify(searchOptions)}`;

        // Check cache
        if (this.options.enableCaching && this.searchCache.has(cacheKey)) {
            return this.searchCache.get(cacheKey);
        }

        this.logger.debug('🔍 Searching for:', normalizedQuery);

        // Perform different types of searches
        const results = new Map();

        // 1. Exact matches
        this.exactSearch(normalizedQuery, results);

        // 2. Partial matches
        this.partialSearch(normalizedQuery, results);

        // 3. Fuzzy matching (if enabled)
        if (searchOptions.fuzzy) {
            this.fuzzySearch(normalizedQuery, results);
        }

        // 4. Semantic search (if enabled)
        if (this.options.enableSemanticSearch) {
            this.semanticSearch(normalizedQuery, results);
        }

        // Sort and filter results
        const sortedResults = this.sortAndFilterResults(results, searchOptions);

        // Cache results
        if (this.options.enableCaching) {
            this.searchCache.set(cacheKey, sortedResults);
        }

        this.logger.debug(`🎯 Found ${sortedResults.length} results for: ${normalizedQuery}`);
        return sortedResults;
    }

    /**
     * Exact search for precise matches
     */
    exactSearch(query, results) {
        for (const [id, character] of this.characters) {
            let score = 0;

            // Check name
            if (character.name.toLowerCase() === query) {
                score = this.searchWeights.name * 2.0;
            }

            // Check aliases
            for (const alias of character.aliases) {
                if (alias.toLowerCase() === query) {
                    score = Math.max(score, this.searchWeights.aliases * 2.0);
                }
            }

            if (score > 0) {
                results.set(id, Math.max(results.get(id) || 0, score));
            }
        }
    }

    /**
     * Partial search for substring matches
     */
    partialSearch(query, results) {
        if (this.characterIndex.has(query)) {
            const matches = this.characterIndex.get(query);
            for (const [id, weight] of matches) {
                const currentScore = results.get(id) || 0;
                results.set(id, Math.max(currentScore, weight * 1.5));
            }
        }

        // Search for query as substring in character data
        for (const [id, character] of this.characters) {
            let score = 0;

            if (character.name.toLowerCase().includes(query)) {
                score = this.searchWeights.name;
            }

            if (character.description.toLowerCase().includes(query)) {
                score = Math.max(score, this.searchWeights.description);
            }

            for (const alias of character.aliases) {
                if (alias.toLowerCase().includes(query)) {
                    score = Math.max(score, this.searchWeights.aliases);
                }
            }

            for (const tag of character.tags) {
                if (tag.toLowerCase().includes(query)) {
                    score = Math.max(score, this.searchWeights.tags);
                }
            }

            if (score > 0) {
                const currentScore = results.get(id) || 0;
                results.set(id, Math.max(currentScore, score));
            }
        }
    }

    /**
     * Fuzzy search for approximate matches
     */
    fuzzySearch(query, results) {
        for (const [id, character] of this.characters) {
            const searchTerms = [
                character.name,
                ...character.aliases,
                character.description
            ];

            let maxScore = 0;
            for (const term of searchTerms) {
                const similarity = this.calculateStringSimilarity(query, term.toLowerCase());
                if (similarity > this.options.searchThreshold) {
                    maxScore = Math.max(maxScore, similarity * 0.8);
                }
            }

            if (maxScore > 0) {
                const currentScore = results.get(id) || 0;
                results.set(id, Math.max(currentScore, maxScore));
            }
        }
    }

    /**
     * Semantic search based on context and meaning
     */
    semanticSearch(query, results) {
        // Simple semantic matching based on tags and descriptions
        const queryWords = query.split(' ').filter(word => word.length > 2);

        for (const [id, character] of this.characters) {
            let semanticScore = 0;

            for (const word of queryWords) {
                // Check if word relates to character's domain
                if (character.tags.some(tag => tag.includes(word))) {
                    semanticScore += 0.3;
                }

                // Check description for semantic relevance
                if (character.description.toLowerCase().includes(word)) {
                    semanticScore += 0.2;
                }
            }

            if (semanticScore > 0) {
                const currentScore = results.get(id) || 0;
                results.set(id, Math.max(currentScore, semanticScore));
            }
        }
    }

    /**
     * Sort and filter search results
     */
    sortAndFilterResults(results, options) {
        // Convert to array and add character data
        const resultsArray = Array.from(results.entries())
            .map(([id, score]) => ({
                character: this.characters.get(id),
                score,
                confidence: Math.min(score, 1.0)
            }))
            .filter(result => result.score >= options.threshold)
            .sort((a, b) => b.score - a.score);

        // Apply usage-based boosting
        const boostedResults = resultsArray.map(result => {
            if (result.character.lastUsed) {
                const daysSinceUse = (Date.now() - result.character.lastUsed) / (24 * 60 * 60 * 1000);
                const usageBoost = Math.max(0, 1 - daysSinceUse / 30); // Boost recently used characters
                result.score += usageBoost * 0.1;
                result.confidence = Math.min(result.score, 1.0);
            }
            return result;
        });

        // Re-sort after boosting and limit results
        return boostedResults
            .sort((a, b) => b.score - a.score)
            .slice(0, options.maxResults);
    }

    /**
     * Calculate string similarity using Jaro-Winkler algorithm
     */
    calculateStringSimilarity(str1, str2) {
        if (str1.length === 0 && str2.length === 0) return 1.0;
        if (str1.length === 0 || str2.length === 0) return 0.0;

        const matchWindow = Math.max(str1.length, str2.length) / 2 - 1;
        if (matchWindow < 0) return str1 === str2 ? 1.0 : 0.0;

        const str1Matches = new Array(str1.length).fill(false);
        const str2Matches = new Array(str2.length).fill(false);

        let matches = 0;
        let transpositions = 0;

        // Find matches
        for (let i = 0; i < str1.length; i++) {
            const start = Math.max(0, i - matchWindow);
            const end = Math.min(i + matchWindow + 1, str2.length);

            for (let j = start; j < end; j++) {
                if (str2Matches[j] || str1[i] !== str2[j]) continue;
                str1Matches[i] = str2Matches[j] = true;
                matches++;
                break;
            }
        }

        if (matches === 0) return 0.0;

        // Find transpositions
        let k = 0;
        for (let i = 0; i < str1.length; i++) {
            if (!str1Matches[i]) continue;
            while (!str2Matches[k]) k++;
            if (str1[i] !== str2[k]) transpositions++;
            k++;
        }

        const jaro = (matches / str1.length + matches / str2.length + (matches - transpositions / 2) / matches) / 3;

        // Jaro-Winkler modification
        let prefix = 0;
        for (let i = 0; i < Math.min(str1.length, str2.length, 4); i++) {
            if (str1[i] === str2[i]) prefix++;
            else break;
        }

        return jaro + 0.1 * prefix * (1 - jaro);
    }

    /**
     * Get character by ID
     */
    getCharacter(id) {
        const character = this.characters.get(id);
        if (character) {
            // Update last used timestamp
            character.lastUsed = Date.now();
        }
        return character;
    }

    /**
     * Get all characters
     */
    getAllCharacters() {
        return Array.from(this.characters.values());
    }

    /**
     * Get popular characters
     */
    getPopularCharacters(limit = 10) {
        return Array.from(this.characters.values())
            .sort((a, b) => (b.lastUsed || 0) - (a.lastUsed || 0))
            .slice(0, limit);
    }

    /**
     * Get characters by category/tag
     */
    getCharactersByTag(tag, limit = 10) {
        return Array.from(this.characters.values())
            .filter(character => character.tags.includes(tag.toLowerCase()))
            .slice(0, limit);
    }

    /**
     * Get search suggestions based on partial input
     */
    getSuggestions(query, limit = 5) {
        if (!query || query.length < 2) {
            return this.getPopularCharacters(limit);
        }

        return this.search(query, { maxResults: limit, threshold: 0.3 })
            .map(result => result.character);
    }

    /**
     * Update search configuration
     */
    updateConfig(config) {
        this.options = { ...this.options, ...config };

        if (config.searchWeights) {
            this.searchWeights = { ...this.searchWeights, ...config.searchWeights };
        }

        this.logger.info('🔧 Search configuration updated', this.options);
    }

    /**
     * Clear search cache
     */
    clearCache() {
        this.searchCache.clear();
        this.logger.info('🧹 Search cache cleared');
    }

    /**
     * Get search statistics
     */
    getStats() {
        return {
            totalCharacters: this.characters.size,
            indexEntries: this.characterIndex.size,
            cacheEntries: this.searchCache.size,
            popularCharacters: this.getPopularCharacters(5).map(c => c.name)
        };
    }

    /**
     * Dispose of resources
     */
    dispose() {
        this.characters.clear();
        this.characterIndex.clear();
        this.searchCache.clear();
        this.logger.info('🧹 Character Search Tool disposed');
    }
}

export default CharacterSearchTool;