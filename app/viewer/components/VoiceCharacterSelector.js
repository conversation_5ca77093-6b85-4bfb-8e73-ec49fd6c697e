/**
 * Voice Character Selector Component
 * 
 * Enables voice input for character selection and integrates with the existing
 * role-playing system, AliyunWebSocketChatModel for speech-to-text, and
 * CharacterService for seamless character selection.
 */

import { createLogger } from '../../../src/utils/logger';
import { RealtimeAudioManager } from '../../../src/media/modality/audio.js';

export class VoiceCharacterSelector {
    constructor(options = {}) {
        this.options = {
            container: null,
            characterService: null,
            agentCoordinator: null,
            rolePlayingPanel: null,
            audioProcessor: null,
            onCharacterSelected: null,
            onVoiceError: null,
            enableAudioFeedback: true,
            voiceActivationKey: 'Space', // Default key for push-to-talk
            autoDetection: true, // Enable automatic voice activity detection
            language: 'en-US',
            ...options
        };

        this.logger = createLogger('VoiceCharacterSelector');
        
        // Voice processing state
        this.isVoiceActive = false;
        this.isListening = false;
        this.mediaStream = null;
        this.audioContext = null;
        this.realtimeAudioManager = null;
        
        // Character search state
        this.availableCharacters = [];
        this.lastRecognizedText = '';
        this.recognitionConfidence = 0;
        
        // Voice activation elements
        this.voiceButton = null;
        this.voiceStatus = null;
        this.voiceIndicator = null;
        
        // Character name patterns for voice recognition
        this.characterPatterns = new Map([
            // Anime/Game Characters
            ['luffy', ['luffy', 'monkey d luffy', 'straw hat']],
            ['naruto', ['naruto', 'naruto uzumaki', 'hokage']],
            ['goku', ['goku', 'son goku', 'kakarot']],
            ['pikachu', ['pikachu', 'pokemon', 'electric mouse']],
            ['zelda', ['zelda', 'princess zelda', 'hyrule']],
            ['mario', ['mario', 'super mario', 'plumber']],
            
            // AI Assistants
            ['assistant', ['assistant', 'ai assistant', 'helper']],
            ['friend', ['friend', 'friendly companion', 'buddy']],
            ['expert', ['expert', 'subject expert', 'professor']],
            ['creative', ['creative', 'creative mentor', 'artist']],
            ['coach', ['coach', 'motivational coach', 'trainer']],
            
            // Generic terms
            ['custom', ['custom', 'custom character', 'personalized']]
        ]);

        // Audio feedback sounds (URLs or base64)
        this.audioFeedback = {
            listening: null,
            recognized: null,
            selected: null,
            error: null
        };

        this.initialize();
    }

    /**
     * Initialize the voice character selector
     */
    async initialize() {
        try {
            this.logger.info('🎙️ Initializing Voice Character Selector...');
            
            // Load available characters from role playing panel
            await this.loadAvailableCharacters();
            
            // Initialize audio feedback system
            if (this.options.enableAudioFeedback) {
                await this.initializeAudioFeedback();
            }
            
            // Initialize realtime audio manager
            await this.initializeRealtimeAudio();
            
            this.logger.info('✅ Voice Character Selector initialized successfully');
            
        } catch (error) {
            this.logger.error('❌ Failed to initialize Voice Character Selector:', error);
            if (this.options.onVoiceError) {
                this.options.onVoiceError(error);
            }
        }
    }

    /**
     * Load available characters from the role playing panel
     */
    async loadAvailableCharacters() {
        try {
            if (this.options.rolePlayingPanel) {
                this.availableCharacters = this.options.rolePlayingPanel.getCharacterPresets();
                this.logger.info('📋 Loaded characters for voice selection:', this.availableCharacters.length);
            } else if (this.options.characterService) {
                // Fallback to character service if available
                this.availableCharacters = await this.options.characterService.getAvailableCharacters?.() || [];
            }

            // Update character patterns with loaded characters
            this.updateCharacterPatterns();
            
        } catch (error) {
            this.logger.warn('⚠️ Could not load available characters:', error);
            this.availableCharacters = [];
        }
    }

    /**
     * Update character patterns with loaded characters
     */
    updateCharacterPatterns() {
        this.availableCharacters.forEach(character => {
            if (!this.characterPatterns.has(character.id)) {
                const patterns = [
                    character.name.toLowerCase(),
                    character.id.toLowerCase(),
                    ...character.description.toLowerCase().split(' ').slice(0, 3)
                ];
                this.characterPatterns.set(character.id, patterns);
            }
        });

        this.logger.debug('🔍 Updated character patterns:', Array.from(this.characterPatterns.keys()));
    }

    /**
     * Initialize audio feedback system
     */
    async initializeAudioFeedback() {
        try {
            // Create audio context for feedback sounds
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // Generate simple beep tones for feedback
            this.audioFeedback = {
                listening: this.createBeepTone(440, 0.1, 0.2), // Short beep
                recognized: this.createBeepTone(660, 0.1, 0.15), // Higher beep
                selected: this.createChordTone([440, 550, 660], 0.3, 0.25), // Chord
                error: this.createBeepTone(220, 0.2, 0.3) // Lower, longer beep
            };
            
            this.logger.info('🔊 Audio feedback system initialized');
            
        } catch (error) {
            this.logger.warn('⚠️ Could not initialize audio feedback:', error);
            this.options.enableAudioFeedback = false;
        }
    }

    /**
     * Initialize realtime audio manager for voice input
     */
    async initializeRealtimeAudio() {
        try {
            this.realtimeAudioManager = new RealtimeAudioManager({
                sampleRate: 16000,
                numChannels: 1,
                bitDepth: 16,
                minIntervalMs: 100,
                enableDebugLogging: false,
                logger: this.logger,
                onError: (error) => this.handleAudioError(error)
            });

            this.logger.info('🎵 Realtime audio manager initialized');

        } catch (error) {
            this.logger.error('❌ Failed to initialize realtime audio:', error);
            throw error;
        }
    }

    /**
     * Create voice activation UI components
     */
    createVoiceUI(container) {
        if (!container) {
            this.logger.warn('⚠️ No container provided for voice UI');
            return;
        }

        const voiceSection = document.createElement('div');
        voiceSection.className = 'voice-character-section';
        voiceSection.innerHTML = `
            <div class="voice-header">
                <h3 class="voice-title">
                    <span class="voice-icon">🎙️</span>
                    Voice Character Selection
                </h3>
                <div class="voice-status-indicator">
                    <span class="status-dot"></span>
                    <span class="status-text">Ready</span>
                </div>
            </div>
            
            <div class="voice-controls">
                <button class="voice-activation-btn" id="voice-activation-btn">
                    <span class="btn-icon">🎤</span>
                    <span class="btn-text">Hold to Speak</span>
                </button>
                
                <div class="voice-instructions">
                    <p>Say "I want to be [character name]" or "Select [character]"</p>
                    <p>Example: "I want to be Luffy" or "Select the creative mentor"</p>
                </div>
            </div>
            
            <div class="voice-feedback">
                <div class="recognized-text">
                    <span class="label">Recognized:</span>
                    <span class="text" id="recognized-text">...</span>
                </div>
                <div class="confidence-meter">
                    <span class="label">Confidence:</span>
                    <div class="meter">
                        <div class="fill" id="confidence-fill"></div>
                    </div>
                    <span class="percentage" id="confidence-percentage">0%</span>
                </div>
            </div>
            
            <div class="character-suggestions">
                <div class="suggestions-header">Available Characters:</div>
                <div class="suggestions-list" id="character-suggestions"></div>
            </div>
        `;

        container.appendChild(voiceSection);

        // Store references to key elements
        this.voiceButton = container.querySelector('#voice-activation-btn');
        this.voiceStatus = container.querySelector('.status-text');
        this.voiceIndicator = container.querySelector('.status-dot');
        
        // Setup event listeners
        this.attachVoiceEventListeners();
        
        // Update character suggestions
        this.updateCharacterSuggestions(container);

        this.logger.info('🎨 Voice UI created successfully');
    }

    /**
     * Attach event listeners for voice controls
     */
    attachVoiceEventListeners() {
        if (!this.voiceButton) return;

        // Mouse events for voice activation
        this.voiceButton.addEventListener('mousedown', () => this.startVoiceRecognition());
        this.voiceButton.addEventListener('mouseup', () => this.stopVoiceRecognition());
        this.voiceButton.addEventListener('mouseleave', () => this.stopVoiceRecognition());

        // Keyboard events for voice activation
        document.addEventListener('keydown', (e) => {
            if (e.code === this.options.voiceActivationKey && !e.repeat) {
                e.preventDefault();
                this.startVoiceRecognition();
            }
        });

        document.addEventListener('keyup', (e) => {
            if (e.code === this.options.voiceActivationKey) {
                e.preventDefault();
                this.stopVoiceRecognition();
            }
        });

        this.logger.debug('🔗 Voice event listeners attached');
    }

    /**
     * Update character suggestions in the UI
     */
    updateCharacterSuggestions(container) {
        const suggestionsContainer = container.querySelector('#character-suggestions');
        if (!suggestionsContainer) return;

        suggestionsContainer.innerHTML = '';

        this.availableCharacters.slice(0, 6).forEach(character => {
            const suggestion = document.createElement('div');
            suggestion.className = 'character-suggestion';
            suggestion.innerHTML = `
                <span class="char-avatar">${character.avatar || '🎭'}</span>
                <span class="char-name">${character.name}</span>
            `;
            
            suggestion.addEventListener('click', () => {
                this.selectCharacterById(character.id);
            });
            
            suggestionsContainer.appendChild(suggestion);
        });
    }

    /**
     * Start voice recognition process
     */
    async startVoiceRecognition() {
        if (this.isListening) return;

        try {
            this.logger.info('🎙️ Starting voice recognition...');
            this.isListening = true;
            
            // Update UI state
            this.updateVoiceStatus('Listening...', 'listening');
            this.voiceButton.classList.add('listening');
            
            // Play listening feedback
            if (this.options.enableAudioFeedback) {
                this.playAudioFeedback('listening');
            }

            // Request microphone access
            this.mediaStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 16000,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            });

            // Start audio processing with AliyunWebSocketChatModel
            await this.startAudioProcessing();

        } catch (error) {
            this.logger.error('❌ Error starting voice recognition:', error);
            this.handleVoiceError(error);
        }
    }

    /**
     * Stop voice recognition process
     */
    async stopVoiceRecognition() {
        if (!this.isListening) return;

        try {
            this.logger.info('🛑 Stopping voice recognition...');
            this.isListening = false;
            
            // Update UI state
            this.updateVoiceStatus('Processing...', 'processing');
            this.voiceButton.classList.remove('listening');
            
            // Stop audio processing
            await this.stopAudioProcessing();
            
            // Close media stream
            if (this.mediaStream) {
                this.mediaStream.getTracks().forEach(track => track.stop());
                this.mediaStream = null;
            }

            // Process any final recognition results
            setTimeout(() => {
                this.updateVoiceStatus('Ready', 'ready');
            }, 1000);

        } catch (error) {
            this.logger.error('❌ Error stopping voice recognition:', error);
            this.handleVoiceError(error);
        }
    }

    /**
     * Start audio processing with System1 integration
     */
    async startAudioProcessing() {
        try {
            // Get agent service from coordinator
            const agentService = this.options.agentCoordinator?.getAgentService?.();
            if (!agentService || !agentService.model) {
                throw new Error('Agent service or model not available');
            }

            // Initialize realtime mode for voice input
            await agentService.initializeRealtimeMode({
                reason: 'voice_character_selection',
                onVoiceActivityDetected: () => {
                    this.logger.debug('🎙️ Voice activity detected');
                    this.updateVoiceStatus('Speaking...', 'speaking');
                },
                onVoiceActivityStopped: () => {
                    this.logger.debug('🔇 Voice activity stopped');
                    this.updateVoiceStatus('Processing...', 'processing');
                },
                onTranscriptReceived: (transcript) => {
                    this.handleTranscriptReceived(transcript);
                },
                onAudioReceived: (audioData) => {
                    // Handle audio response if needed
                    this.logger.debug('🔊 Audio response received');
                }
            });

            // Start processing audio from microphone
            if (this.options.audioProcessor) {
                this.options.audioProcessor.startProcessing();
                
                // Process audio chunks through AudioProcessor
                const audioContext = new AudioContext({ sampleRate: 16000 });
                const source = audioContext.createMediaStreamSource(this.mediaStream);
                const processor = audioContext.createScriptProcessor(4096, 1, 1);
                
                processor.onaudioprocess = async (event) => {
                    if (!this.isListening) return;
                    
                    const inputBuffer = event.inputBuffer;
                    const inputData = inputBuffer.getChannelData(0);
                    
                    // Send audio data through AudioProcessor
                    await this.options.audioProcessor.handleAudioData(
                        inputData, 
                        'float32', 
                        agentService
                    );
                };
                
                source.connect(processor);
                processor.connect(audioContext.destination);
                
                this.audioContext = audioContext;
            }

        } catch (error) {
            this.logger.error('❌ Error starting audio processing:', error);
            throw error;
        }
    }

    /**
     * Stop audio processing
     */
    async stopAudioProcessing() {
        try {
            // Stop audio processor
            if (this.options.audioProcessor) {
                this.options.audioProcessor.stopProcessing();
            }

            // Close audio context
            if (this.audioContext && this.audioContext.state !== 'closed') {
                await this.audioContext.close();
                this.audioContext = null;
            }

        } catch (error) {
            this.logger.error('❌ Error stopping audio processing:', error);
        }
    }

    /**
     * Handle transcript received from speech recognition
     */
    handleTranscriptReceived(transcript) {
        this.logger.info('📝 Transcript received:', transcript);
        
        // Update UI with recognized text
        this.updateRecognizedText(transcript.text || transcript, transcript.confidence || 0.8);
        
        // Play recognition feedback
        if (this.options.enableAudioFeedback) {
            this.playAudioFeedback('recognized');
        }

        // Extract character name from transcript
        const characterId = this.extractCharacterFromText(transcript.text || transcript);
        
        if (characterId) {
            this.logger.info('✅ Character identified from voice:', characterId);
            this.selectCharacterById(characterId);
        } else {
            this.logger.warn('⚠️ Could not identify character from transcript');
            this.updateVoiceStatus('Character not recognized', 'error');
            
            if (this.options.enableAudioFeedback) {
                this.playAudioFeedback('error');
            }
        }
    }

    /**
     * Extract character name from recognized text
     */
    extractCharacterFromText(text) {
        const normalizedText = text.toLowerCase().trim();
        this.logger.debug('🔍 Analyzing text for character:', normalizedText);

        // Common voice patterns for character selection
        const patterns = [
            /(?:i want to be|select|choose|pick) (.+)/,
            /(?:become|transform into|be) (.+)/,
            /(?:play as|role play as) (.+)/,
            /(.+)(?:character|role)/,
            /^(.+)$/ // Fallback: entire text
        ];

        let candidateText = '';
        for (const pattern of patterns) {
            const match = normalizedText.match(pattern);
            if (match && match[1]) {
                candidateText = match[1].trim();
                break;
            }
        }

        if (!candidateText) {
            candidateText = normalizedText;
        }

        // Find matching character
        for (const [characterId, searchTerms] of this.characterPatterns.entries()) {
            for (const term of searchTerms) {
                if (candidateText.includes(term.toLowerCase()) || 
                    term.toLowerCase().includes(candidateText)) {
                    return characterId;
                }
            }
        }

        // Fuzzy matching for partial matches
        return this.findBestCharacterMatch(candidateText);
    }

    /**
     * Find best character match using fuzzy matching
     */
    findBestCharacterMatch(text) {
        let bestMatch = null;
        let bestScore = 0;

        for (const [characterId, searchTerms] of this.characterPatterns.entries()) {
            for (const term of searchTerms) {
                const score = this.calculateSimilarity(text, term);
                if (score > bestScore && score > 0.6) { // 60% similarity threshold
                    bestScore = score;
                    bestMatch = characterId;
                }
            }
        }

        if (bestMatch) {
            this.logger.debug(`🎯 Best character match: ${bestMatch} (score: ${bestScore})`);
        }

        return bestMatch;
    }

    /**
     * Calculate similarity between two strings (simple implementation)
     */
    calculateSimilarity(str1, str2) {
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;
        
        if (longer.length === 0) return 1.0;
        
        const distance = this.levenshteinDistance(longer, shorter);
        return (longer.length - distance) / longer.length;
    }

    /**
     * Calculate Levenshtein distance between two strings
     */
    levenshteinDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }

    /**
     * Select character by ID and apply to role playing system
     */
    async selectCharacterById(characterId) {
        try {
            this.logger.info('🎭 Selecting character:', characterId);
            
            // Find the character
            const character = this.availableCharacters.find(c => c.id === characterId);
            if (!character) {
                throw new Error(`Character not found: ${characterId}`);
            }

            // Update UI status
            this.updateVoiceStatus(`Selected: ${character.name}`, 'selected');
            
            // Play selection feedback
            if (this.options.enableAudioFeedback) {
                this.playAudioFeedback('selected');
            }

            // Apply character through role playing panel
            if (this.options.rolePlayingPanel) {
                this.options.rolePlayingPanel.selectCharacter(characterId);
                await this.options.rolePlayingPanel.applyCharacterConfiguration();
            }

            // Apply through character service
            if (this.options.characterService) {
                await this.options.characterService.setCharacterContext(character);
            }

            // Trigger callback
            if (this.options.onCharacterSelected) {
                this.options.onCharacterSelected(character);
            }

            this.logger.info('✅ Character selected successfully:', character.name);

        } catch (error) {
            this.logger.error('❌ Error selecting character:', error);
            this.handleVoiceError(error);
        }
    }

    /**
     * Update voice status in the UI
     */
    updateVoiceStatus(text, state = 'ready') {
        if (this.voiceStatus) {
            this.voiceStatus.textContent = text;
        }
        
        if (this.voiceIndicator) {
            this.voiceIndicator.className = `status-dot ${state}`;
        }

        this.logger.debug('📱 Voice status updated:', text, state);
    }

    /**
     * Update recognized text display
     */
    updateRecognizedText(text, confidence = 0) {
        const recognizedTextElement = document.querySelector('#recognized-text');
        const confidenceFill = document.querySelector('#confidence-fill');
        const confidencePercentage = document.querySelector('#confidence-percentage');

        if (recognizedTextElement) {
            recognizedTextElement.textContent = text;
        }

        if (confidenceFill && confidencePercentage) {
            const percentage = Math.round(confidence * 100);
            confidenceFill.style.width = `${percentage}%`;
            confidencePercentage.textContent = `${percentage}%`;
        }

        this.lastRecognizedText = text;
        this.recognitionConfidence = confidence;
    }

    /**
     * Handle voice processing errors
     */
    handleVoiceError(error) {
        this.logger.error('❌ Voice error:', error);
        
        this.updateVoiceStatus('Error occurred', 'error');
        
        if (this.options.enableAudioFeedback) {
            this.playAudioFeedback('error');
        }

        if (this.options.onVoiceError) {
            this.options.onVoiceError(error);
        }

        // Reset state
        this.isListening = false;
        this.isVoiceActive = false;
        
        if (this.voiceButton) {
            this.voiceButton.classList.remove('listening');
        }
    }

    /**
     * Handle audio processing errors
     */
    handleAudioError(error) {
        this.logger.error('❌ Audio processing error:', error);
        this.handleVoiceError(error);
    }

    /**
     * Create beep tone for audio feedback
     */
    createBeepTone(frequency, duration, volume = 0.1) {
        return () => {
            if (!this.audioContext) return;
            
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);
            
            oscillator.frequency.value = frequency;
            oscillator.type = 'sine';
            
            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(volume, this.audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);
            
            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + duration);
        };
    }

    /**
     * Create chord tone for complex audio feedback
     */
    createChordTone(frequencies, duration, volume = 0.1) {
        return () => {
            if (!this.audioContext) return;
            
            frequencies.forEach((frequency, index) => {
                setTimeout(() => {
                    const oscillator = this.audioContext.createOscillator();
                    const gainNode = this.audioContext.createGain();
                    
                    oscillator.connect(gainNode);
                    gainNode.connect(this.audioContext.destination);
                    
                    oscillator.frequency.value = frequency;
                    oscillator.type = 'sine';
                    
                    gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
                    gainNode.gain.linearRampToValueAtTime(volume / frequencies.length, this.audioContext.currentTime + 0.01);
                    gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);
                    
                    oscillator.start(this.audioContext.currentTime);
                    oscillator.stop(this.audioContext.currentTime + duration);
                }, index * 50);
            });
        };
    }

    /**
     * Play audio feedback
     */
    playAudioFeedback(type) {
        if (!this.options.enableAudioFeedback || !this.audioFeedback[type]) {
            return;
        }

        try {
            this.audioFeedback[type]();
        } catch (error) {
            this.logger.warn('⚠️ Error playing audio feedback:', error);
        }
    }

    /**
     * Get voice processing status
     */
    getVoiceStatus() {
        return {
            isVoiceActive: this.isVoiceActive,
            isListening: this.isListening,
            lastRecognizedText: this.lastRecognizedText,
            recognitionConfidence: this.recognitionConfidence,
            availableCharacters: this.availableCharacters.length
        };
    }

    /**
     * Dispose of resources
     */
    async dispose() {
        try {
            this.logger.info('🧹 Disposing Voice Character Selector...');

            // Stop any active recognition
            if (this.isListening) {
                await this.stopVoiceRecognition();
            }

            // Clean up audio context
            if (this.audioContext && this.audioContext.state !== 'closed') {
                await this.audioContext.close();
            }

            // Clean up realtime audio manager
            if (this.realtimeAudioManager) {
                await this.realtimeAudioManager.dispose();
            }

            // Reset state
            this.isVoiceActive = false;
            this.isListening = false;
            this.mediaStream = null;
            this.audioContext = null;
            this.availableCharacters = [];

            this.logger.info('✅ Voice Character Selector disposed');

        } catch (error) {
            this.logger.error('❌ Error disposing Voice Character Selector:', error);
        }
    }
}

export default VoiceCharacterSelector;