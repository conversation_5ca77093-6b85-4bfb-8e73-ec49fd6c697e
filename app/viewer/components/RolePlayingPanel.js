/**
 * Role-Playing Panel Component
 * 
 * Provides character selection, personality configuration, and role-playing
 * interface with consistency integration for the dual brain system.
 * Enhanced with voice integration for seamless character selection.
 */

// Import from the canonical component location
import CharacterSearchTool from './CharacterSearchTool.js';
import CharacterAnalysisService from '../services/CharacterAnalysisService.js';

export class RolePlayingPanel {
    constructor(options = {}) {
        this.options = {
            container: null,
            characterService: null,
            agentCoordinator: null,
            onCharacterChange: null,
            onPersonalityUpdate: null,
            position: 'right',
            minimized: false,
            ...options
        };

        this.isVisible = false;
        this.currentCharacter = null;
        this.personalities = {};
        this.panel = null;
        this.characterGrid = null;
        this.personalityControls = null;
        this.voiceStyleSelector = null;

        // Initialize character search tool and analysis service
        this.characterSearchTool = new CharacterSearchTool({
            enableCache: true,
            maxResults: 10,
            maxConcurrentSearches: 3
        });

        this.characterAnalysisService = new CharacterAnalysisService({
            dualBrainCoordinator: options.dualBrainCoordinator,
            agentService: options.agentCoordinator
        });

        // Voice integration components (System 1 coordination)
        this.voiceSession = null;
        this.isVoiceListening = false;
        this.voiceTranscriptionBuffer = '';

        // Character search components
        this.characterSearchInput = null;
        this.characterSearchResults = null;
        this.voiceSearchButton = null;
        this.characterRecommendations = null;
        this.searchState = {
            isSearching: false,
            isVoiceRecording: false,
            searchQuery: '',
            filteredCharacters: [...this.characterPresets],
            recommendations: []
        };

        // Default character presets (simplified - personalities come from CharacterAnalysisService)
        this.characterPresets = [
            {
                id: 'assistant',
                name: 'AI Assistant',
                description: 'Helpful, professional, and knowledgeable',
                avatar: '🤖',
                voiceStyle: 'professional'
            },
            {
                id: 'friend',
                name: 'Friendly Companion',
                description: 'Casual, warm, and conversational',
                avatar: '😊',
                voiceStyle: 'casual'
            },
            {
                id: 'expert',
                name: 'Subject Expert',
                description: 'Knowledgeable, precise, and analytical',
                avatar: '🎓',
                voiceStyle: 'authoritative'
            },
            {
                id: 'creative',
                name: 'Creative Mentor',
                description: 'Imaginative, inspiring, and innovative',
                avatar: '🎨',
                voiceStyle: 'expressive'
            },
            {
                id: 'coach',
                name: 'Motivational Coach',
                description: 'Encouraging, energetic, and goal-focused',
                avatar: '💪',
                voiceStyle: 'energetic'
            },
            {
                id: 'custom',
                name: 'Custom Character',
                description: 'Design your own unique personality',
                avatar: '⚙️',
                voiceStyle: 'balanced'
            }
        ];

        this.voiceStyles = [
            { id: 'professional', name: 'Professional', description: 'Formal and business-like' },
            { id: 'casual', name: 'Casual', description: 'Relaxed and conversational' },
            { id: 'authoritative', name: 'Authoritative', description: 'Confident and commanding' },
            { id: 'expressive', name: 'Expressive', description: 'Animated and creative' },
            { id: 'energetic', name: 'Energetic', description: 'High-energy and motivational' },
            { id: 'balanced', name: 'Balanced', description: 'Neutral and adaptable' }
        ];

        // Enhanced character presets for search
        this.enhanceCharacterPresets();

        // Voice search integration
        this.voiceSearchAPI = null;
        this.speechRecognition = null;
        this.initializeVoiceSearch();

        this.initialize();
    }

    /**
     * Initialize the role-playing panel component
     */
    initialize() {
        this.createPanel();
        this.createCharacterSearchInterface();
        this.createCharacterGrid();
        this.createCharacterRecommendations();
        this.createPersonalityControls();
        this.createVoiceStyleSelector();
        this.attachEventListeners();

        // Set default character
        this.selectCharacter('assistant');

        // Initialize character recommendations
        this.updateCharacterRecommendations();

        console.log('[RolePlayingPanel] Initialized with character presets:', this.characterPresets.length);
    }

    /**
     * Create the main panel container
     */
    createPanel() {
        this.panel = document.createElement('div');
        this.panel.className = 'role-playing-panel';
        this.panel.setAttribute('data-position', this.options.position);

        if (this.options.minimized) {
            this.panel.classList.add('minimized');
        }

        // Create header
        const header = document.createElement('div');
        header.className = 'rp-header';
        header.innerHTML = `
            <div class="rp-title">
                <span class="rp-icon">🎭</span>
                <span class="rp-text">Role Playing</span>
            </div>
            <div class="rp-controls">
                <button class="rp-minimize-btn" title="Minimize panel">
                    <span class="minimize-icon">−</span>
                </button>
                <button class="rp-close-btn" title="Close panel">
                    <span class="close-icon">×</span>
                </button>
            </div>
        `;

        // Create content area
        const content = document.createElement('div');
        content.className = 'rp-content';

        this.panel.appendChild(header);
        this.panel.appendChild(content);

        // Add to container or body
        if (this.options.container) {
            this.options.container.appendChild(this.panel);
        } else {
            document.body.appendChild(this.panel);
        }
    }

    /**
     * Create character search interface
     */
    createCharacterSearchInterface() {
        const content = this.panel.querySelector('.rp-content');

        const section = document.createElement('div');
        section.className = 'rp-section character-search-section';
        section.innerHTML = `
            <h3 class="rp-section-title">🔍 Find Character</h3>
        `;

        // Create search container
        const searchContainer = document.createElement('div');
        searchContainer.className = 'character-search-container';

        // Enhanced search input with voice button and smart features
        const searchInputGroup = document.createElement('div');
        searchInputGroup.className = 'search-input-group';
        searchInputGroup.innerHTML = `
            <div class="search-input-wrapper">
                <div class="search-input-icon">🔍</div>
                <input 
                    type="text" 
                    class="character-search-input" 
                    placeholder="Search anime characters, personalities, or series..."
                    autocomplete="off"
                    spellcheck="false"
                />
                <div class="search-input-icons">
                    <button class="voice-search-btn enhanced-voice-btn" title="Voice search" aria-label="Voice search for characters">
                        <span class="voice-icon">🎤</span>
                        <span class="voice-recording-indicator"></span>
                        <div class="voice-status-text">Tap to speak</div>
                    </button>
                    <button class="clear-search-btn" title="Clear search" aria-label="Clear search" style="display: none;">
                        <span class="clear-icon">×</span>
                    </button>
                </div>
            </div>
            <div class="search-suggestions" style="display: none;">
                <div class="search-suggestions-list"></div>
            </div>
        `;

        // Enhanced search results container with preview
        const searchResults = document.createElement('div');
        searchResults.className = 'character-search-results';
        searchResults.innerHTML = `
            <div class="search-status-bar">
                <div class="search-status"></div>
                <div class="search-filters">
                    <button class="filter-btn active" data-filter="all">All</button>
                    <button class="filter-btn" data-filter="anime">Anime</button>
                    <button class="filter-btn" data-filter="popular">Popular</button>
                </div>
            </div>
            <div class="search-results-preview">
                <div class="results-loading" style="display: none;">
                    <div class="loading-spinner"></div>
                    <span>Searching characters...</span>
                </div>
                <div class="search-results-grid"></div>
            </div>
        `;

        searchContainer.appendChild(searchInputGroup);
        searchContainer.appendChild(searchResults);
        section.appendChild(searchContainer);
        content.appendChild(section);

        // Store references
        this.characterSearchInput = section.querySelector('.character-search-input');
        this.characterSearchResults = section.querySelector('.character-search-results');
        this.voiceSearchButton = section.querySelector('.voice-search-btn');
        this.searchSuggestions = section.querySelector('.search-suggestions');
        this.searchFilters = section.querySelectorAll('.filter-btn');
    }

    /**
     * Create character selection grid
     */
    createCharacterGrid() {
        const content = this.panel.querySelector('.rp-content');

        const section = document.createElement('div');
        section.className = 'rp-section character-grid-section';
        section.innerHTML = `
            <h3 class="rp-section-title">✨ Character Gallery</h3>
        `;

        this.characterGrid = document.createElement('div');
        this.characterGrid.className = 'character-grid';

        this.refreshCharacterCards();

        section.appendChild(this.characterGrid);
        content.appendChild(section);
    }

    /**
     * Create character recommendations section
     */
    createCharacterRecommendations() {
        const content = this.panel.querySelector('.rp-content');

        const section = document.createElement('div');
        section.className = 'rp-section character-recommendations-section';
        section.innerHTML = `
            <h3 class="rp-section-title">💡 Recommended</h3>
        `;

        this.characterRecommendations = document.createElement('div');
        this.characterRecommendations.className = 'character-recommendations';

        section.appendChild(this.characterRecommendations);
        content.appendChild(section);
    }

    /**
     * Create personality configuration controls
     */
    createPersonalityControls() {
        const content = this.panel.querySelector('.rp-content');

        const section = document.createElement('div');
        section.className = 'rp-section personality-section';
        section.innerHTML = `
            <h3 class="rp-section-title">Personality Settings</h3>
        `;

        this.personalityControls = document.createElement('div');
        this.personalityControls.className = 'personality-controls';

        // Create sliders for personality traits
        const traits = [
            { key: 'formality', label: 'Formality', min: 0, max: 1, step: 0.1, description: 'How formal vs casual' },
            { key: 'enthusiasm', label: 'Enthusiasm', min: 0, max: 1, step: 0.1, description: 'Energy and excitement level' },
            { key: 'empathy', label: 'Empathy', min: 0, max: 1, step: 0.1, description: 'Understanding and compassion' },
            { key: 'creativity', label: 'Creativity', min: 0, max: 1, step: 0.1, description: 'Imaginative and innovative thinking' },
            { key: 'directness', label: 'Directness', min: 0, max: 1, step: 0.1, description: 'Straightforward vs diplomatic' }
        ];

        traits.forEach(trait => {
            const sliderGroup = document.createElement('div');
            sliderGroup.className = 'personality-slider-group';

            sliderGroup.innerHTML = `
                <div class="slider-header">
                    <label class="slider-label">${trait.label}</label>
                    <span class="slider-value" data-trait="${trait.key}">0.5</span>
                </div>
                <div class="slider-container">
                    <input 
                        type="range" 
                        class="personality-slider"
                        data-trait="${trait.key}"
                        min="${trait.min}" 
                        max="${trait.max}" 
                        step="${trait.step}"
                        value="0.5"
                    />
                    <div class="slider-track-fill"></div>
                </div>
                <div class="slider-description">${trait.description}</div>
            `;

            this.personalityControls.appendChild(sliderGroup);
        });

        section.appendChild(this.personalityControls);
        content.appendChild(section);
    }

    /**
     * Create voice style selector
     */
    createVoiceStyleSelector() {
        const content = this.panel.querySelector('.rp-content');

        const section = document.createElement('div');
        section.className = 'rp-section voice-section';
        section.innerHTML = `
            <h3 class="rp-section-title">Voice Style</h3>
        `;

        this.voiceStyleSelector = document.createElement('div');
        this.voiceStyleSelector.className = 'voice-style-selector';

        // Create dropdown
        const dropdown = document.createElement('div');
        dropdown.className = 'voice-dropdown';
        dropdown.innerHTML = `
            <select class="voice-select" id="voice-style-select">
                ${this.voiceStyles.map(style =>
            `<option value="${style.id}">${style.name}</option>`
        ).join('')}
            </select>
            <div class="voice-description" id="voice-description">
                ${this.voiceStyles[0].description}
            </div>
        `;

        this.voiceStyleSelector.appendChild(dropdown);
        section.appendChild(this.voiceStyleSelector);
        content.appendChild(section);

        // Create apply button
        const applySection = document.createElement('div');
        applySection.className = 'rp-section apply-section';
        applySection.innerHTML = `
            <button class="apply-character-btn">
                <span class="btn-icon">✨</span>
                <span class="btn-text">Apply Character</span>
            </button>
            <div class="character-status-display">
                <span class="status-text">No character selected</span>
            </div>
        `;

        content.appendChild(applySection);
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        // Header controls
        const minimizeBtn = this.panel.querySelector('.rp-minimize-btn');
        const closeBtn = this.panel.querySelector('.rp-close-btn');

        minimizeBtn.addEventListener('click', () => this.toggleMinimize());
        closeBtn.addEventListener('click', () => this.hide());

        // Character search
        if (this.characterSearchInput) {
            this.characterSearchInput.addEventListener('input', (e) => {
                this.handleSearchInput(e.target.value);
            });

            this.characterSearchInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    this.performCharacterSearch(e.target.value);
                }
            });
        }

        // Voice search button
        if (this.voiceSearchButton) {
            this.voiceSearchButton.addEventListener('click', () => {
                this.toggleVoiceSearch();
            });
        }

        // Clear search button
        const clearSearchBtn = this.panel.querySelector('.clear-search-btn');
        if (clearSearchBtn) {
            clearSearchBtn.addEventListener('click', () => {
                this.clearSearch();
            });
        }

        // Character selection (including search results)
        this.panel.addEventListener('click', (e) => {
            const card = e.target.closest('.character-card, .search-result-card, .recommendation-card');
            if (card) {
                const characterId = card.getAttribute('data-character-id');
                this.selectCharacter(characterId);
            }
        });

        // Personality sliders
        if (this.personalityControls) {
            const sliders = this.personalityControls.querySelectorAll('.personality-slider');
            sliders.forEach(slider => {
                slider.addEventListener('input', (e) => {
                    this.updatePersonalityTrait(e.target.getAttribute('data-trait'), parseFloat(e.target.value));
                });
            });
        }

        // Voice style selector
        const voiceSelect = this.panel.querySelector('.voice-select');
        if (voiceSelect) {
            voiceSelect.addEventListener('change', (e) => {
                this.updateVoiceStyle(e.target.value);
            });
        }

        // Apply button
        const applyBtn = this.panel.querySelector('.apply-character-btn');
        if (applyBtn) {
            applyBtn.addEventListener('click', () => {
                this.applyCharacterConfiguration();
            });
        }
    }

    /**
     * Select a character preset
     */
    selectCharacter(characterId) {
        const character = this.characterPresets.find(c => c.id === characterId);
        if (!character) return;

        // Update selection visual
        this.characterGrid.querySelectorAll('.character-card').forEach(card => {
            card.classList.remove('selected');
        });

        const selectedCard = this.characterGrid.querySelector(`[data-character-id="${characterId}"]`);
        if (selectedCard) {
            selectedCard.classList.add('selected');
        }

        // Update current character
        this.currentCharacter = { ...character };

        // Update personality sliders
        this.updatePersonalitySliders(character.personality);

        // Update voice style
        const voiceSelect = this.panel.querySelector('.voice-select');
        voiceSelect.value = character.voiceStyle;
        this.updateVoiceDescription(character.voiceStyle);

        // Update status
        this.updateCharacterStatus(`Selected: ${character.name}`);

        console.log('[RolePlayingPanel] Character selected:', characterId);
    }

    /**
     * Update personality trait value
     */
    updatePersonalityTrait(trait, value) {
        if (!this.currentCharacter) return;

        this.currentCharacter.personality[trait] = value;

        // Update slider value display
        const valueDisplay = this.personalityControls.querySelector(`[data-trait="${trait}"]`);
        if (valueDisplay && valueDisplay.classList.contains('slider-value')) {
            valueDisplay.textContent = value.toFixed(1);
        }

        // Update slider fill
        const slider = this.personalityControls.querySelector(`input[data-trait="${trait}"]`);
        if (slider) {
            const percentage = ((value - slider.min) / (slider.max - slider.min)) * 100;
            const fillElement = slider.parentElement.querySelector('.slider-track-fill');
            if (fillElement) {
                fillElement.style.width = `${percentage}%`;
            }
        }

        // Mark as custom if not already
        if (this.currentCharacter.id !== 'custom') {
            this.currentCharacter.id = 'custom';
            this.currentCharacter.name = 'Custom Character';
            this.selectCharacter('custom');
        }

        console.log('[RolePlayingPanel] Personality trait updated:', trait, value);
    }

    /**
     * Update personality sliders to match character
     */
    updatePersonalitySliders(personality) {
        Object.entries(personality).forEach(([trait, value]) => {
            const slider = this.personalityControls.querySelector(`input[data-trait="${trait}"]`);
            const valueDisplay = this.personalityControls.querySelector(`.slider-value[data-trait="${trait}"]`);

            if (slider) {
                slider.value = value;

                // Update visual fill
                const percentage = ((value - slider.min) / (slider.max - slider.min)) * 100;
                const fillElement = slider.parentElement.querySelector('.slider-track-fill');
                if (fillElement) {
                    fillElement.style.width = `${percentage}%`;
                }
            }

            if (valueDisplay) {
                valueDisplay.textContent = value.toFixed(1);
            }
        });
    }

    /**
     * Update voice style
     */
    updateVoiceStyle(styleId) {
        if (!this.currentCharacter) return;

        this.currentCharacter.voiceStyle = styleId;
        this.updateVoiceDescription(styleId);

        console.log('[RolePlayingPanel] Voice style updated:', styleId);
    }

    /**
     * Update voice style description
     */
    updateVoiceDescription(styleId) {
        const style = this.voiceStyles.find(s => s.id === styleId);
        const descriptionElement = this.panel.querySelector('#voice-description');

        if (style && descriptionElement) {
            descriptionElement.textContent = style.description;
        }
    }

    /**
     * Update character status display
     */
    updateCharacterStatus(status) {
        const statusElement = this.panel.querySelector('.status-text');
        if (statusElement) {
            statusElement.textContent = status;
        }
    }

    /**
     * Apply character configuration to the system
     */
    async applyCharacterConfiguration() {
        if (!this.currentCharacter) {
            this.updateCharacterStatus('No character selected');
            return;
        }

        try {
            this.updateCharacterStatus('Applying character...');

            // Apply to character service
            if (this.options.characterService) {
                await this.options.characterService.setCharacterContext(this.currentCharacter);
            }

            // Apply to agent coordinator
            if (this.options.agentCoordinator) {
                await this.options.agentCoordinator.updateCharacterPersonality(this.currentCharacter);
            }

            // Trigger callbacks
            if (this.options.onCharacterChange) {
                this.options.onCharacterChange(this.currentCharacter);
            }

            if (this.options.onPersonalityUpdate) {
                this.options.onPersonalityUpdate(this.currentCharacter.personality);
            }

            this.updateCharacterStatus(`Active: ${this.currentCharacter.name}`);

            // Visual feedback
            const applyBtn = this.panel.querySelector('.apply-character-btn');
            applyBtn.classList.add('applied');
            setTimeout(() => applyBtn.classList.remove('applied'), 2000);

            console.log('[RolePlayingPanel] Character configuration applied:', this.currentCharacter);

        } catch (error) {
            console.error('[RolePlayingPanel] Failed to apply character configuration:', error);
            this.updateCharacterStatus('Failed to apply character');
        }
    }

    /**
     * Toggle panel minimize state
     */
    toggleMinimize() {
        this.panel.classList.toggle('minimized');
        this.options.minimized = this.panel.classList.contains('minimized');

        const minimizeIcon = this.panel.querySelector('.minimize-icon');
        minimizeIcon.textContent = this.options.minimized ? '+' : '−';
    }

    /**
     * Show the panel
     */
    show() {
        this.panel.classList.add('visible');
        this.isVisible = true;
        console.log('[RolePlayingPanel] Panel shown');
    }

    /**
     * Hide the panel
     */
    hide() {
        this.panel.classList.remove('visible');
        this.isVisible = false;
        console.log('[RolePlayingPanel] Panel hidden');
    }

    /**
     * Toggle panel visibility
     */
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    /**
     * Get current character configuration
     */
    getCurrentCharacter() {
        return this.currentCharacter ? { ...this.currentCharacter } : null;
    }

    /**
     * Update character from external source
     */
    updateCharacter(characterData) {
        if (characterData) {
            this.currentCharacter = { ...characterData };
            this.selectCharacter(characterData.id);
        }
    }

    /**
     * Get all available character presets
     */
    getCharacterPresets() {
        return [...this.characterPresets];
    }

    /**
     * Add custom character preset
     */
    addCharacterPreset(character) {
        if (character.id && !this.characterPresets.find(c => c.id === character.id)) {
            this.characterPresets.push(character);
            this.refreshCharacterGrid();
            console.log('[RolePlayingPanel] Character preset added:', character.id);
        }
    }

    /**
     * Refresh character grid display
     */
    refreshCharacterGrid() {
        this.characterGrid.innerHTML = '';
        this.createCharacterGrid();
    }

    /**
     * Initialize voice search capabilities
     */
    initializeVoiceSearch() {
        // Check for Web Speech API support
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.speechRecognition = new SpeechRecognition();

            this.speechRecognition.continuous = false;
            this.speechRecognition.interimResults = false;
            this.speechRecognition.lang = 'en-US';

            this.speechRecognition.onstart = () => {
                this.searchState.isVoiceRecording = true;
                this.updateVoiceSearchUI(true);
                console.log('[RolePlayingPanel] Voice recognition started');
            };

            this.speechRecognition.onresult = (event) => {
                const result = event.results[0][0].transcript;
                this.characterSearchInput.value = result;
                this.handleSearchInput(result);
                console.log('[RolePlayingPanel] Voice search result:', result);
            };

            this.speechRecognition.onerror = (event) => {
                console.error('[RolePlayingPanel] Voice recognition error:', event.error);
                this.searchState.isVoiceRecording = false;
                this.updateVoiceSearchUI(false);
            };

            this.speechRecognition.onend = () => {
                this.searchState.isVoiceRecording = false;
                this.updateVoiceSearchUI(false);
                console.log('[RolePlayingPanel] Voice recognition ended');
            };
        } else {
            console.warn('[RolePlayingPanel] Web Speech API not supported');
        }
    }

    /**
     * Enhance character presets with search metadata
     */
    enhanceCharacterPresets() {
        this.characterPresets = this.characterPresets.map(character => ({
            ...character,
            searchTags: this.generateSearchTags(character),
            personalityScore: this.calculatePersonalityScore(character),
            popularityScore: Math.random() * 0.3 + 0.7, // Simulate popularity
            matchScore: 0 // Will be updated during search
        }));
    }

    /**
     * Generate search tags for character
     */
    generateSearchTags(character) {
        const tags = [character.name.toLowerCase(), character.description.toLowerCase()];
        const p = character.personality;

        // Add personality-based tags
        if (p.formality > 0.7) tags.push('formal', 'professional', 'business');
        if (p.formality < 0.3) tags.push('casual', 'relaxed', 'informal');
        if (p.enthusiasm > 0.7) tags.push('enthusiastic', 'energetic', 'excited');
        if (p.empathy > 0.7) tags.push('empathetic', 'caring', 'understanding');
        if (p.creativity > 0.7) tags.push('creative', 'innovative', 'artistic');
        if (p.directness > 0.7) tags.push('direct', 'straightforward', 'honest');

        // Add voice style tags
        tags.push(character.voiceStyle);

        // Add role-specific tags
        switch (character.id) {
            case 'assistant': tags.push('helper', 'ai', 'support'); break;
            case 'friend': tags.push('companion', 'buddy', 'friendly'); break;
            case 'expert': tags.push('knowledgeable', 'teacher', 'mentor'); break;
            case 'creative': tags.push('artist', 'designer', 'inspiration'); break;
            case 'coach': tags.push('motivational', 'trainer', 'guide'); break;
        }

        return [...new Set(tags)];
    }

    /**
     * Calculate personality uniqueness score
     */
    calculatePersonalityScore(character) {
        const p = character.personality;
        const traits = Object.values(p);

        // Calculate variance (higher variance = more unique personality)
        const mean = traits.reduce((sum, val) => sum + val, 0) / traits.length;
        const variance = traits.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / traits.length;

        return Math.min(1, variance * 2);
    }

    /**
     * Handle search input changes
     */
    handleSearchInput(query) {
        this.searchState.searchQuery = query.trim();

        // Show/hide clear button
        const clearBtn = this.panel.querySelector('.clear-search-btn');
        if (clearBtn) {
            clearBtn.style.display = this.searchState.searchQuery ? 'flex' : 'none';
        }

        if (this.searchState.searchQuery.length === 0) {
            this.clearSearchResults();
            return;
        }

        if (this.searchState.searchQuery.length >= 2) {
            this.performCharacterSearch(this.searchState.searchQuery);
        }
    }

    /**
     * Perform character search using CharacterSearchTool
     */
    async performCharacterSearch(query) {
        this.searchState.isSearching = true;
        this.updateSearchStatus('Searching anime characters...');

        try {
            // Use the actual CharacterSearchTool for anime character search
            const searchParams = {
                characterName: query,
                enableCache: true,
                maxResults: 8,
                searchType: 'anime'
            };

            const searchResults = await this.characterSearchTool.execute(searchParams);

            if (searchResults.characters && searchResults.characters.length > 0) {
                // Enhance results with analysis if available
                const enhancedResults = await this.enhanceSearchResults(searchResults.characters);
                this.displaySearchResults(enhancedResults);
            } else {
                // Fall back to local character search
                const localResults = this.searchLocalCharacters(query);
                this.displaySearchResults(localResults);
            }

        } catch (error) {
            console.error('[RolePlayingPanel] Character search failed:', error);
            // Fall back to local search
            const localResults = this.searchLocalCharacters(query);
            this.displaySearchResults(localResults);
        } finally {
            this.searchState.isSearching = false;
        }
    }

    /**
     * Enhance search results with AI analysis
     */
    async enhanceSearchResults(characters) {
        const enhancedCharacters = [];

        for (const character of characters) {
            try {
                // Generate personality profile using System 2
                const personalityProfile = await this.characterAnalysisService.generatePersonalityProfile(
                    character.name,
                    character.description || character.summary || `${character.name} from ${character.anime}`
                );

                // Analyze character for better understanding
                const analysis = await this.characterAnalysisService.analyzeCharacter(character);

                enhancedCharacters.push({
                    ...character,
                    id: `anime_${character.name.toLowerCase().replace(/\s+/g, '_')}`,
                    personality: personalityProfile,
                    analysis: analysis,
                    matchScore: 0.9, // High score for anime characters
                    matchReasons: ['anime character'],
                    source: 'anime'
                });

            } catch (error) {
                console.warn(`Failed to enhance character ${character.name}:`, error);
                // Add with minimal enhancement
                enhancedCharacters.push({
                    ...character,
                    id: `anime_${character.name.toLowerCase().replace(/\s+/g, '_')}`,
                    personality: this.getDefaultPersonality(),
                    matchScore: 0.7,
                    matchReasons: ['anime character'],
                    source: 'anime'
                });
            }
        }

        return enhancedCharacters;
    }

    /**
     * Search local characters (fallback)
     */
    searchLocalCharacters(query) {
        const normalizedQuery = query.toLowerCase();
        const queryWords = normalizedQuery.split(' ').filter(word => word.length > 0);

        return this.characterPresets.map(character => {
            let matchScore = 0;
            let matchReasons = [];

            // Name matching (highest weight)
            const nameLower = character.name.toLowerCase();
            if (nameLower.includes(normalizedQuery)) {
                matchScore += 0.4;
                matchReasons.push('name match');
            }

            // Description matching
            const descLower = character.description.toLowerCase();
            if (descLower.includes(normalizedQuery)) {
                matchScore += 0.3;
                matchReasons.push('description match');
            }

            // Tag matching
            const tagMatches = character.searchTags.filter(tag =>
                queryWords.some(word => tag.includes(word))
            );
            if (tagMatches.length > 0) {
                matchScore += (tagMatches.length / character.searchTags.length) * 0.3;
                matchReasons.push('trait match');
            }

            // Fuzzy matching for typos
            const fuzzyScore = this.calculateFuzzyMatch(normalizedQuery, character.name.toLowerCase());
            if (fuzzyScore > 0.6) {
                matchScore += fuzzyScore * 0.2;
                matchReasons.push('similar name');
            }

            return {
                ...character,
                matchScore,
                matchReasons
            };
        })
            .filter(character => character.matchScore > 0.1)
            .sort((a, b) => b.matchScore - a.matchScore)
            .slice(0, 6); // Limit to top 6 results
    }

    /**
     * Calculate fuzzy string matching score
     */
    calculateFuzzyMatch(str1, str2) {
        const len1 = str1.length;
        const len2 = str2.length;
        const maxLen = Math.max(len1, len2);

        if (maxLen === 0) return 1;

        const distance = this.levenshteinDistance(str1, str2);
        return (maxLen - distance) / maxLen;
    }

    /**
     * Calculate Levenshtein distance
     */
    levenshteinDistance(str1, str2) {
        const matrix = [];

        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }

        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }

        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }

        return matrix[str2.length][str1.length];
    }

    /**
     * Display enhanced search results with character preview
     */
    displaySearchResults(results) {
        const searchResults = this.panel.querySelector('.search-results-grid');
        const statusElement = this.panel.querySelector('.search-status');
        const loadingElement = this.panel.querySelector('.results-loading');

        // Hide loading indicator
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        if (results.length === 0) {
            statusElement.innerHTML = `
                <span class="status-icon">❌</span>
                <span class="status-text">No characters found</span>
            `;
            searchResults.innerHTML = `
                <div class="no-results">
                    <div class="no-results-icon">🔍</div>
                    <div class="no-results-title">No characters found</div>
                    <div class="no-results-subtitle">Try different keywords or explore the gallery below</div>
                    <div class="search-suggestions-hint">Popular searches: Naruto, Goku, Luffy, Eren</div>
                </div>
            `;
            return;
        }

        statusElement.innerHTML = `
            <span class="status-icon">✅</span>
            <span class="status-text">Found ${results.length} character${results.length > 1 ? 's' : ''}</span>
        `;

        searchResults.innerHTML = results.map(character => {
            const matchBadge = character.matchScore > 0.8 ? 'perfect-match' :
                character.matchScore > 0.5 ? 'good-match' : 'partial-match';

            const personalityTraits = character.personality ?
                Object.entries(character.personality)
                    .filter(([key, value]) => value > 0.6)
                    .sort(([, a], [, b]) => b - a)
                    .slice(0, 3)
                    .map(([key, value]) => `
                        <span class="trait-badge trait-${key}" title="${key}: ${Math.round(value * 100)}%">
                            ${key}
                        </span>
                    `).join('') : '';

            const isImageUrl = character.avatar && (character.avatar.startsWith('http') || character.avatar.startsWith('data:'));
            const avatarDisplay = isImageUrl ?
                `<img src="${character.avatar}" alt="${character.name}" class="character-image" onerror="this.style.display='none';this.nextElementSibling.style.display='flex'" loading="lazy" />` :
                '';
            const fallbackAvatar = isImageUrl ?
                `<div class="character-emoji" style="display:none">${character.avatar || '👤'}</div>` :
                `<div class="character-emoji">${character.avatar || '👤'}</div>`;

            const seriesInfo = character.animeAppearances && character.animeAppearances.length > 0 ?
                `<div class="character-series">From: ${character.animeAppearances[0].title}</div>` : '';

            const popularityIndicator = character.favorites > 1000 ?
                `<div class="popularity-indicator" title="${character.favorites} favorites">
                    <span class="popularity-icon">❤️</span>
                    <span class="popularity-count">${this.formatNumber(character.favorites)}</span>
                </div>` : '';

            return `
                <div class="search-result-card character-card enhanced-card ${this.currentCharacter?.id === character.id ? 'selected' : ''}" 
                     data-character-id="${character.id}"
                     data-character-source="${character.source || 'local'}">
                    <div class="character-avatar-container">
                        ${avatarDisplay}
                        ${fallbackAvatar}
                        <div class="character-overlay">
                            <button class="preview-btn" title="Quick preview">
                                <span class="preview-icon">👁️</span>
                            </button>
                        </div>
                        ${popularityIndicator}
                    </div>
                    <div class="character-info">
                        <div class="character-header">
                            <div class="character-name" title="${character.fullName || character.name}">${character.name}</div>
                            <div class="match-badge ${matchBadge}">${Math.round(character.matchScore * 100)}%</div>
                        </div>
                        <div class="character-description">${this.truncateText(character.description, 80)}</div>
                        ${seriesInfo}
                        <div class="character-traits">
                            ${personalityTraits}
                        </div>
                        <div class="match-info">
                            <span class="match-reasons">${character.matchReasons ? character.matchReasons.join(', ') : 'name match'}</span>
                        </div>
                    </div>
                    <div class="character-actions">
                        <button class="select-character-btn" title="Select this character">
                            <span class="select-icon">✨</span>
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        // Show search results section with animation
        this.characterSearchResults.style.display = 'block';

        // Add click handlers for preview buttons
        this.attachSearchResultHandlers();
    }

    /**
     * Clear search results
     */
    clearSearchResults() {
        const searchResults = this.panel.querySelector('.search-results-grid');
        const statusElement = this.panel.querySelector('.search-status');

        if (searchResults) searchResults.innerHTML = '';
        if (statusElement) statusElement.textContent = '';

        if (this.characterSearchResults) {
            this.characterSearchResults.style.display = 'none';
        }
    }

    /**
     * Clear search input and results
     */
    clearSearch() {
        if (this.characterSearchInput) {
            this.characterSearchInput.value = '';
        }

        this.searchState.searchQuery = '';
        this.clearSearchResults();

        const clearBtn = this.panel.querySelector('.clear-search-btn');
        if (clearBtn) {
            clearBtn.style.display = 'none';
        }
    }

    /**
     * Update search status
     */
    updateSearchStatus(status) {
        const statusElement = this.panel.querySelector('.search-status');
        if (statusElement) {
            statusElement.textContent = status;
        }
    }

    /**
     * Toggle voice search using System 1 (AliyunWebSocketChatModel)
     */
    async toggleVoiceSearch() {
        if (!this.options.agentCoordinator) {
            this.showNotification('Voice search requires agent coordinator', 'warning');
            return;
        }

        if (this.isVoiceListening) {
            await this.stopVoiceSearch();
        } else {
            await this.startVoiceSearch();
        }
    }

    /**
     * Start voice search using System 1
     */
    async startVoiceSearch() {
        try {
            this.isVoiceListening = true;
            this.voiceTranscriptionBuffer = '';
            this.updateVoiceSearchUI(true);

            // Use System 1 (AliyunWebSocketChatModel) for real-time transcription
            const voiceCallbacks = {
                onTranscription: (text) => {
                    this.voiceTranscriptionBuffer += text + ' ';
                    this.updateVoiceTranscription(this.voiceTranscriptionBuffer.trim());
                },

                onVoiceActivityStopped: () => {
                    if (this.voiceTranscriptionBuffer.trim()) {
                        this.processVoiceCharacterSearch(this.voiceTranscriptionBuffer.trim());
                    }
                },

                onError: (error) => {
                    console.error('[RolePlayingPanel] Voice search error:', error);
                    this.stopVoiceSearch();
                    this.showNotification('Voice search failed', 'error');
                }
            };

            // Start voice session through agent coordinator
            this.voiceSession = await this.options.agentCoordinator.startVoiceSession({
                ...voiceCallbacks,
                realtime: true,
                transcriptionOnly: true,
                timeout: 8000 // 8 seconds timeout
            });

            this.showNotification('Listening for character names...', 'info');

        } catch (error) {
            console.error('[RolePlayingPanel] Failed to start voice search:', error);
            this.stopVoiceSearch();
            this.showNotification('Voice search unavailable', 'error');
        }
    }

    /**
     * Stop voice search
     */
    async stopVoiceSearch() {
        if (this.voiceSession && this.options.agentCoordinator) {
            try {
                await this.options.agentCoordinator.stopVoiceSession(this.voiceSession);
                this.voiceSession = null;
            } catch (error) {
                console.warn('[RolePlayingPanel] Error stopping voice session:', error);
            }
        }

        this.isVoiceListening = false;
        this.updateVoiceSearchUI(false);
    }

    /**
     * Process voice input for character search
     */
    async processVoiceCharacterSearch(transcription) {
        try {
            // Extract character name from voice input
            const characterRequest = this.extractCharacterFromVoice(transcription);

            if (characterRequest) {
                // Update search input with extracted character name
                if (this.characterSearchInput) {
                    this.characterSearchInput.value = characterRequest.name;
                }

                // Perform search
                await this.performCharacterSearch(characterRequest.name);

                // Provide audio feedback if character found
                this.provideVoiceFeedback(characterRequest);

            } else {
                this.showNotification('Could not understand character request', 'warning');
            }

        } catch (error) {
            console.error('[RolePlayingPanel] Voice character processing failed:', error);
            this.showNotification('Voice processing failed', 'error');
        } finally {
            this.stopVoiceSearch();
        }
    }

    /**
     * Extract character name from voice transcription
     */
    extractCharacterFromVoice(text) {
        const normalizedText = text.toLowerCase().trim();

        // Patterns to extract character names from voice input
        const patterns = [
            /(?:i want to be|i am|i'm|play as|select|choose)\s+(.+)/i,
            /(?:make me|turn me into)\s+(.+)/i,
            /(?:search for|find|show me)\s+(.+)/i,
            /(?:character|char):\s*(.+)/i,
            /^(.+?)(?:\s+from\s+(.+))?$/i  // fallback: assume whole text is character name
        ];

        for (const pattern of patterns) {
            const match = normalizedText.match(pattern);
            if (match) {
                const characterName = match[1]?.trim();
                const animeName = match[2]?.trim();

                if (characterName && characterName.length > 1) {
                    return {
                        name: this.capitalizeCharacterName(characterName),
                        anime: animeName ? this.capitalizeAnimeName(animeName) : null,
                        originalInput: text,
                        confidence: this.calculateVoiceConfidence(text, characterName)
                    };
                }
            }
        }

        return null;
    }

    /**
     * Provide voice feedback for character selection
     */
    async provideVoiceFeedback(characterRequest) {
        try {
            if (this.options.agentCoordinator?.speak) {
                const feedbackText = characterRequest.anime ?
                    `Searching for ${characterRequest.name} from ${characterRequest.anime}` :
                    `Searching for ${characterRequest.name}`;

                await this.options.agentCoordinator.speak(feedbackText);
            }
        } catch (error) {
            console.warn('[RolePlayingPanel] Voice feedback failed:', error);
        }
    }

    /**
     * Update voice transcription display
     */
    updateVoiceTranscription(text) {
        const statusText = this.voiceSearchButton?.querySelector('.voice-status-text');
        if (statusText) {
            statusText.textContent = text || 'Listening...';
        }
    }

    /**
     * Calculate confidence score for voice extraction
     */
    calculateVoiceConfidence(originalText, extractedName) {
        const textLength = originalText.length;
        const nameLength = extractedName.length;

        // Higher confidence for shorter, clearer inputs
        if (textLength < 25 && nameLength > 2) {
            return 0.9;
        }

        // Medium confidence for structured inputs
        if (originalText.toLowerCase().includes('want to be') ||
            originalText.toLowerCase().includes('search for')) {
            return 0.8;
        }

        // Lower confidence for longer, ambiguous inputs
        if (textLength > 60) {
            return 0.4;
        }

        return 0.6;
    }

    /**
     * Capitalize character name properly
     */
    capitalizeCharacterName(name) {
        return name.split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');
    }

    /**
     * Capitalize anime name properly  
     */
    capitalizeAnimeName(name) {
        // Handle special cases like "One Piece", "One-Punch Man"
        const specialCases = {
            'one piece': 'One Piece',
            'one-punch man': 'One-Punch Man',
            'one punch man': 'One-Punch Man',
            'dragon ball': 'Dragon Ball',
            'dragon ball z': 'Dragon Ball Z',
            'my hero academia': 'My Hero Academia',
            'attack on titan': 'Attack on Titan',
            'demon slayer': 'Demon Slayer'
        };

        const normalized = name.toLowerCase();
        if (specialCases[normalized]) {
            return specialCases[normalized];
        }

        return this.capitalizeCharacterName(name);
    }

    /**
     * Get default personality trait scores
     */
    getDefaultPersonality() {
        return {
            formality: 0.5,
            enthusiasm: 0.6,
            empathy: 0.6,
            creativity: 0.5,
            directness: 0.6,
            humor: 0.4,
            leadership: 0.5,
            analytical: 0.5
        };
    }

    /**
     * Toggle voice search (legacy Web Speech API fallback)
     */
    toggleVoiceSearchLegacy() {
        if (!this.speechRecognition) {
            this.showNotification('Voice search not supported in this browser', 'warning');
            return;
        }

        if (this.searchState.isVoiceRecording) {
            this.speechRecognition.stop();
        } else {
            try {
                this.speechRecognition.start();
            } catch (error) {
                console.error('[RolePlayingPanel] Voice search error:', error);
                this.showNotification('Voice search failed to start', 'error');
            }
        }
    }

    /**
     * Update enhanced voice search UI with better feedback
     */
    updateVoiceSearchUI(isRecording) {
        const voiceIcon = this.voiceSearchButton.querySelector('.voice-icon');
        const recordingIndicator = this.voiceSearchButton.querySelector('.voice-recording-indicator');
        const statusText = this.voiceSearchButton.querySelector('.voice-status-text');

        if (isRecording) {
            this.voiceSearchButton.classList.add('recording');
            voiceIcon.textContent = '🔴';
            recordingIndicator.style.display = 'block';
            statusText.textContent = 'Listening...';
            this.voiceSearchButton.title = 'Stop recording';

            // Add pulsing animation
            this.voiceSearchButton.style.animation = 'voice-pulse 1.5s infinite';

            // Show voice feedback
            this.showVoiceFeedback('Listening for character names...');
        } else {
            this.voiceSearchButton.classList.remove('recording');
            voiceIcon.textContent = '🎤';
            recordingIndicator.style.display = 'none';
            statusText.textContent = 'Tap to speak';
            this.voiceSearchButton.title = 'Voice search for characters';

            // Remove pulsing animation
            this.voiceSearchButton.style.animation = '';

            // Hide voice feedback
            this.hideVoiceFeedback();
        }
    }

    /**
     * Update character recommendations
     */
    updateCharacterRecommendations() {
        const recommendations = this.generateRecommendations();
        this.displayRecommendations(recommendations);
    }

    /**
     * Generate character recommendations
     */
    generateRecommendations() {
        // For now, return popular and diverse characters
        // In future versions, this could use user interaction history
        const recommendations = [...this.characterPresets]
            .sort((a, b) => {
                // Sort by popularity and personality uniqueness
                const scoreA = (a.popularityScore || 0.5) + (a.personalityScore || 0) * 0.3;
                const scoreB = (b.popularityScore || 0.5) + (b.personalityScore || 0) * 0.3;
                return scoreB - scoreA;
            })
            .slice(0, 3) // Top 3 recommendations
            .map(character => ({
                ...character,
                recommendationReason: this.getRecommendationReason(character)
            }));

        return recommendations;
    }

    /**
     * Get recommendation reason for character
     */
    getRecommendationReason(character) {
        const p = character.personality;

        if (character.popularityScore > 0.8) return 'Popular choice';
        if (character.personalityScore > 0.7) return 'Unique personality';
        if (p.empathy > 0.8) return 'Great for conversations';
        if (p.creativity > 0.8) return 'Creative and inspiring';
        if (p.enthusiasm > 0.8) return 'High energy';
        if (p.formality > 0.8) return 'Professional';

        return 'Recommended';
    }

    /**
     * Display character recommendations
     */
    displayRecommendations(recommendations) {
        if (!this.characterRecommendations) return;

        this.characterRecommendations.innerHTML = recommendations.map(character => `
            <div class="recommendation-card character-card ${this.currentCharacter?.id === character.id ? 'selected' : ''}" 
                 data-character-id="${character.id}">
                <div class="character-avatar">${character.avatar}</div>
                <div class="character-info">
                    <div class="character-name">${character.name}</div>
                    <div class="character-description">${character.description}</div>
                    <div class="recommendation-reason">${character.recommendationReason}</div>
                </div>
                <div class="recommendation-badge">⭐</div>
            </div>
        `).join('');
    }

    /**
     * Refresh character cards in grid
     */
    refreshCharacterCards() {
        if (!this.characterGrid) return;

        this.characterGrid.innerHTML = '';

        // Create character cards
        this.characterPresets.forEach(character => {
            const card = document.createElement('div');
            card.className = 'character-card';
            card.setAttribute('data-character-id', character.id);

            card.innerHTML = `
                <div class="character-avatar">${character.avatar}</div>
                <div class="character-info">
                    <div class="character-name">${character.name}</div>
                    <div class="character-description">${character.description}</div>
                    <div class="character-traits">
                        ${Object.entries(character.personality)
                    .filter(([key, value]) => value > 0.7)
                    .map(([key, value]) => `<span class="trait-badge">${key}</span>`)
                    .slice(0, 3)
                    .join('')}
                    </div>
                </div>
                <div class="character-status">
                    <div class="status-indicator"></div>
                </div>
            `;

            this.characterGrid.appendChild(card);
        });
    }

    /**
     * Show notification (simple implementation)
     */
    showNotification(message, type = 'info') {
        console.log(`[RolePlayingPanel] ${type.toUpperCase()}: ${message}`);

        // Could be enhanced with a proper notification system
        // For now, we'll use console and potentially update status
        this.updateCharacterStatus(message);

        // Clear notification after 3 seconds
        setTimeout(() => {
            if (this.currentCharacter) {
                this.updateCharacterStatus(`Active: ${this.currentCharacter.name}`);
            }
        }, 3000);
    }

    /**
     * Dispose of the component
     */
    dispose() {
        // Clean up speech recognition
        if (this.speechRecognition) {
            this.speechRecognition.stop();
            this.speechRecognition = null;
        }

        if (this.panel && this.panel.parentNode) {
            this.panel.parentNode.removeChild(this.panel);
        }

        this.currentCharacter = null;
        this.searchState = null;

        console.log('[RolePlayingPanel] Component disposed');
    }
}

export default RolePlayingPanel;