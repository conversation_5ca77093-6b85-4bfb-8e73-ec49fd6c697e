import { MeshSelector } from '../../src/ui/components/MeshSelector.js';
import { CameraViewer } from '../../src/ui/components/cameraViewer.js';
import { VRButton } from "three/examples/jsm/webxr/VRButton.js";
import {
    transcribeAudioWithSTT,
    createOptimizedRecordingOptions,
    handleTranscriptionWorkflow
} from '../../src/utils/audioTranscription.js';

/**
 * UI Settings and Controls for the Viewer
 *
 * Manages UI components, layouts, and control settings
 * for the viewer application.
 */
export class UISettings {
    constructor(viewer) {
        this.viewer = viewer;
        this.container = viewer?.container;

        // Store references to UI components
        this.components = {
            progressContainer: null,
            meshSelector: null,
            cameraViewer: null,
            actionsMenu: null
        };

        // UI state
        this.state = {
            isGenerating: false,
            debugMode: false
        };

        // Initialize notification system
        this.setupNotifications();

        // Add UI components for gesture controls
        this.gestureUI = {
            scalingIndicatorCreated: false,
            zoomSliderCreated: false
        };
    }

    /**
     * Initialize UI components and controls
     */
    initialize() {
        if (!this.container) {
            console.error('[UISettings] Container not found');
            return false;
        }

        // Enable automatic UI scaling based on browser zoom/viewport scale
        this.setupAutoUIScaling();

        // Create progress container
        this.setupProgressContainer();

        // // Add conversion controls
        // this.setupConversionUI();

        // // Add Ready Player Me button
        // this.setupReadyPlayerMeButton();

        // Add actions menu (replaces individual buttons)
        this.setupActionsMenu();

        return true;
    }

    /**
     * Setup automatic UI scaling that follows browser zoom using visualViewport.scale
     */
    setupAutoUIScaling() {
        const docEl = document.documentElement;

        const applyScale = () => {
            try {
                const vv = window.visualViewport;
                // Prefer visualViewport.scale; clamp to >= 1 to keep UI readable when zoomed out
                let scale = vv && typeof vv.scale === 'number' ? vv.scale : 1;
                if (!isFinite(scale) || scale <= 0) scale = 1;
                const multiplier = Math.max(1, Math.min(scale, 3));
                docEl.style.setProperty('--ui-scale-multiplier', String(Number(multiplier.toFixed(2))));
            } catch (e) {
                // Fallback: keep default multiplier
            }
        };

        // Initial apply
        applyScale();

        // Re-apply on viewport changes
        if (window.visualViewport) {
            window.visualViewport.addEventListener('resize', applyScale);
            window.visualViewport.addEventListener('scroll', applyScale);
        }
        window.addEventListener('resize', applyScale);
        window.addEventListener('orientationchange', applyScale);
    }

    /**
     * Initialize UI components from config
     */
    async initializeComponents(config) {
        console.log('[UISettings] Initializing components from config');

        // Initialize MeshSelector component
        if (config?.props?.MeshSelector) {
            await this.initializeMeshSelector(config.props.MeshSelector);
        }

        // Initialize CameraViewer component with embedded preference
        if (config?.props?.CameraViewer) {
            // Set preferPopup to false to use embedded corner mode by default
            const cameraViewerConfig = {
                ...config.props.CameraViewer,
                preferPopup: false // Use embedded corner mode instead of popup
            };
            await this.initializeCameraViewer(cameraViewerConfig);
        }

        // Initialize Role Playing Panel if enabled
        if (config?.props?.RolePlayingPanel) {
            await this.initializeRolePlayingPanel(config.props.RolePlayingPanel);
        }

        // API Cost Tracker will be initialized via actions menu instead

        return true;
    }
    /**
     * Setup actions menu that combines multiple action buttons
     */
    setupActionsMenu() {
        if (!this.container) return;

        // Create menu container
        const menuContainer = document.createElement('div');
        menuContainer.className = 'actions-menu-container';

        // Create main menu button
        const menuButton = document.createElement('button');
        menuButton.className = 'actions-menu-button';
        menuButton.innerHTML = '<span>⚙️</span>';
        menuButton.title = 'Actions Menu';

        // Create dropdown menu
        const dropdownMenu = document.createElement('div');
        dropdownMenu.className = 'actions-dropdown';

        // Create menu items
        const convertMenuItem = document.createElement('div');
        convertMenuItem.className = 'menu-item';
        convertMenuItem.innerHTML = '<span>🔄</span> Any to 3D';

        const avatarMenuItem = document.createElement('div');
        avatarMenuItem.className = 'menu-item';
        avatarMenuItem.innerHTML = '<span>👤</span> ReadyPlayerMe';

        // Create gesture control menu item
        const gestureMenuItem = document.createElement('div');
        gestureMenuItem.className = 'menu-item';
        gestureMenuItem.innerHTML = '<span>👋</span> Gesture Controls';
        gestureMenuItem.title = 'Toggle Gesture Controls';

        // Create QR code menu item for mobile control
        const qrCodeMenuItem = document.createElement('div');
        qrCodeMenuItem.className = 'menu-item';
        qrCodeMenuItem.innerHTML = '<span>📱</span> Mobile Control';
        qrCodeMenuItem.title = 'Connect Mobile Device';

        // Create API Cost Tracker menu item
        const apiCostMenuItem = document.createElement('div');
        apiCostMenuItem.className = 'menu-item';
        apiCostMenuItem.innerHTML = '<span>💰</span> API Cost Tracker';
        apiCostMenuItem.title = 'Toggle API Cost Tracker';

        // Create Role Playing menu item
        const rolePlayingMenuItem = document.createElement('div');
        rolePlayingMenuItem.className = 'menu-item';
        rolePlayingMenuItem.innerHTML = '<span>🎭</span> Role Playing';
        rolePlayingMenuItem.title = 'Configure Character & Personality';

        // We'll check if the WebSocket server is available when the viewer is initialized
        // For now, we'll create the menu item but we'll update its state later

        // Add VR button from base or viewer if available
        let vrMenuItemAdded = false;

        // Try to find VR button from different sources
        let vrButton = null;

        if (this.viewer && this.viewer.vrButton) {
            console.log('[UISettings] Found VR button in viewer');
            vrButton = this.viewer.vrButton;
        } else if (document.getElementById('VRButton')) {
            console.log('[UISettings] Found VR button in DOM');
            vrButton = document.getElementById('VRButton');
        }

        if (vrButton) {
            this.addVRMenuOption(vrButton, dropdownMenu);
            vrMenuItemAdded = true;
        }

        if (!vrMenuItemAdded) {
            console.warn('[UISettings] No VR button found to add to menu');
        }

        // Add menu items to dropdown
        dropdownMenu.appendChild(convertMenuItem);
        dropdownMenu.appendChild(avatarMenuItem);
        dropdownMenu.appendChild(gestureMenuItem);
        dropdownMenu.appendChild(qrCodeMenuItem);
        dropdownMenu.appendChild(apiCostMenuItem);
        dropdownMenu.appendChild(rolePlayingMenuItem);

        // Add elements to container
        menuContainer.appendChild(menuButton);
        menuContainer.appendChild(dropdownMenu);

        // Add to document
        this.container.appendChild(menuContainer);

        // Add styles for menu
        this.addActionsMenuStyles();

        // Setup button handlers and prep modals

        // Create conversion modal but don't add the button
        this.setupConversionUI(false);

        // Setup click events
        menuButton.addEventListener('click', () => {
            // Toggle show class for CSS transitions
            dropdownMenu.classList.toggle('show');

            // Send UI state to mobile devices
            if (this.viewer && typeof this.viewer._sendUIState === 'function') {
                this.viewer._sendUIState();
            }
        });

        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!menuContainer.contains(e.target)) {
                dropdownMenu.classList.remove('show');
            }
        });

        // Add hover effects are now handled by CSS
        // Remove redundant hover effect code since it's now in CSS

        // Convert to 3D functionality
        convertMenuItem.addEventListener('click', () => {
            dropdownMenu.classList.remove('show');

            // Find and show the conversion modal
            const modal = document.querySelector('.conversion-modal');
            if (modal) {
                modal.classList.remove('hidden');

                // Reset form fields (copied from the original handler)
                const textInput = modal.querySelector('.text-input');
                const fileInput = modal.querySelector('.file-input');
                const preview = modal.querySelector('.preview');
                const sourceSelect = modal.querySelector('.source-select');
                const cameraButton = modal.querySelector('.camera-button');
                const genderWrapper = modal.querySelector('.gender-wrapper');

                if (textInput) textInput.value = '';
                if (fileInput) fileInput.value = '';
                if (preview) preview.innerHTML = '';

                // Always hide camera button and gender selector initially
                if (cameraButton) cameraButton.classList.add('hidden');
                if (genderWrapper) genderWrapper.classList.add('hidden');

                if (sourceSelect) {
                    sourceSelect.value = 'text';
                    if (textInput) textInput.classList.remove('hidden');
                    if (fileInput) fileInput.classList.add('hidden');

                    // Double-check that camera and gender are hidden for Text to 3D
                    if (cameraButton && sourceSelect.value === 'text') {
                        cameraButton.classList.add('hidden');
                    }
                    if (genderWrapper && sourceSelect.value === 'text') {
                        genderWrapper.classList.add('hidden');
                    }

                    // Trigger the change event to ensure proper UI state
                    const event = new Event('change');
                    sourceSelect.dispatchEvent(event);
                }
            }
        });

        // Ready Player Me functionality
        avatarMenuItem.addEventListener('click', () => {
            dropdownMenu.classList.remove('show');
            this.viewer.openReadyPlayerMe();
        });

        // Gesture control functionality
        gestureMenuItem.addEventListener('click', async () => {
            dropdownMenu.classList.remove('show');

            // Check camera availability before enabling gesture controls
            const cameraAvailable = await this.checkCameraAvailability();
            if (!cameraAvailable) {
                this.showNotification('Camera not available. Gesture controls require camera access.', true, 3000);
                this.muteGestureControlUI(gestureMenuItem);
                return;
            }

            // Toggle gesture controls
            const isActive = this.viewer.toggleGestureControls();

            // Update menu item appearance based on state
            this.updateGestureMenuItem(gestureMenuItem, isActive);
        });

        // QR code functionality
        qrCodeMenuItem.addEventListener('click', () => {
            dropdownMenu.classList.remove('show');

            // First check if mobile mode is enabled
            if (!window.isMobileEnabled) {
                this.showNotification('Mobile control is not available. Use "npm run launch:mobile viewer dev" to enable mobile support.', true, 5000);
                return;
            }

            // QR code display functionality removed - using realtime audio mode
            this.showNotification('Mobile QR connection disabled. Using realtime audio mode for interaction.', false, 3000);
        });

        // API Cost Tracker functionality
        apiCostMenuItem.addEventListener('click', async () => {
            dropdownMenu.classList.remove('show');
            await this.toggleApiCostTracker();
        });

        // Role Playing functionality
        rolePlayingMenuItem.addEventListener('click', async () => {
            dropdownMenu.classList.remove('show');
            await this.toggleRolePlayingPanel();
        });

        // Store references
        this.components.actionsMenu = {
            container: menuContainer,
            button: menuButton,
            dropdown: dropdownMenu,
            gestureMenuItem: gestureMenuItem,
            qrCodeMenuItem: qrCodeMenuItem,
            apiCostMenuItem: apiCostMenuItem,
            rolePlayingMenuItem: rolePlayingMenuItem
        };

        // Update the gesture control menu item based on state
        this.updateGestureMenuItem = (menuItem, isActive) => {
            if (!menuItem) return;

            if (isActive) {
                menuItem.classList.add('active');
                menuItem.innerHTML = '<span>👋</span> Gesture Controls (On)';
                menuItem.style.color = '#4CAF50'; // Green color for active state
            } else {
                menuItem.classList.remove('active');
                menuItem.innerHTML = '<span>👋</span> Gesture Controls';
                menuItem.style.color = 'white'; // Default color
            }
        };

        // Update the mobile control menu item when the QR code display is initialized
        // This will be called from the viewer when the QR code display is ready
        this.updateMobileControlMenuItem = (isAvailable) => {
            if (!qrCodeMenuItem) return;

            if (!isAvailable) {
                // Disable the menu item
                qrCodeMenuItem.classList.add('disabled');
                qrCodeMenuItem.style.opacity = '0.5';
                qrCodeMenuItem.style.cursor = 'not-allowed';

                // Check if we're not in mobile mode or if the WebSocket server is not available
                if (!window.isMobileEnabled) {
                    qrCodeMenuItem.title = 'Mobile control not available. Use "npm run launch:mobile viewer dev" to enable.';
                    qrCodeMenuItem.innerHTML = '<span>📱</span> Mobile Control (Use launch:mobile)';
                } else {
                    qrCodeMenuItem.title = 'WebSocket server not available. Check if the server is running on port 3000.';
                    qrCodeMenuItem.innerHTML = '<span>📱</span> Mobile Control (Server Error)';
                }
            } else {
                // Enable the menu item
                qrCodeMenuItem.classList.remove('disabled');
                qrCodeMenuItem.style.opacity = '1';
                qrCodeMenuItem.style.cursor = 'pointer';
                qrCodeMenuItem.title = 'Connect Mobile Device';
                qrCodeMenuItem.innerHTML = '<span>📱</span> Mobile Control';
            }
        };
    }

    /**
     * Adds a VR menu option that triggers the VR button
     * @param {HTMLElement} vrButton - The VR button element from base
     * @param {HTMLElement} dropdownMenu - The dropdown menu to add the option to
     */
    addVRMenuOption(vrButton, dropdownMenu) {
        if (!vrButton) return;

        // Create VR menu item with enhanced WebXR compatibility
        const vrMenuItem = document.createElement('div');
        vrMenuItem.className = 'menu-item vr-menu-item';
        vrMenuItem.setAttribute('data-vr', 'true');
        vrMenuItem.setAttribute('data-webxr-menu-item', 'true');

        // Create icon span
        const iconSpan = document.createElement('span');
        iconSpan.textContent = '🥽';
        iconSpan.style.marginRight = '8px';

        // Create text span - ensure styling matches other menu items
        const textSpan = document.createElement('span');
        textSpan.className = 'vr-button-text';
        textSpan.style.fontSize = 'inherit'; // Inherit size from .menu-item
        textSpan.style.fontFamily = 'inherit'; // Use same font as parent
        textSpan.style.fontWeight = 'normal'; // Use normal font weight

        // Check the current text content and modify based on session state
        const buttonText = vrButton.textContent || '';
        if (buttonText.includes('EXIT')) {
            textSpan.textContent = 'Exit Glass';
        } else {
            textSpan.textContent = 'Enter Glass';
        }

        // Text span now has proper class for WebXR polyfill detection

        // Create a MutationObserver to watch for text changes on the original button
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    const newText = vrButton.textContent || '';
                    if (newText.includes('EXIT')) {
                        textSpan.textContent = 'Exit Glass';
                    } else {
                        textSpan.textContent = 'Enter Glass';
                    }
                }
            });
        });

        // Start observing the button
        observer.observe(vrButton, {
            childList: true,
            characterData: true,
            subtree: true
        });

        vrMenuItem.appendChild(iconSpan);
        vrMenuItem.appendChild(textSpan);

        // Add click handler that triggers the original VR button
        vrMenuItem.addEventListener('click', () => {
            console.log('[UISettings] VR menu item clicked, triggering original button');

            // Ensure WebXR polyfill can detect this action
            const vrClickEvent = new CustomEvent('vrButtonClicked', {
                detail: {
                    vrButton: vrButton,
                    menuItem: vrMenuItem,
                    source: 'ui-menu'
                }
            });
            document.dispatchEvent(vrClickEvent);

            vrButton.click();

            // Update text immediately after clicking
            setTimeout(() => {
                const currentText = vrButton.textContent || '';
                if (currentText.includes('EXIT')) {
                    textSpan.textContent = 'Exit Glass';
                } else {
                    textSpan.textContent = 'Enter Glass';
                }
            }, 100);

            dropdownMenu.classList.remove('visible');
        });

        // Add to dropdown menu
        dropdownMenu.appendChild(vrMenuItem);

        // Notify WebXR polyfill that VRButton is available in menu
        setTimeout(() => {
            const menuVRButtonEvent = new CustomEvent('webxrVRButtonAvailable', {
                detail: {
                    vrButton: vrMenuItem,
                    originalButton: vrButton,
                    detectionMethod: 'ui-menu-integration'
                }
            });
            document.dispatchEvent(menuVRButtonEvent);
        }, 0);

        console.log('[UISettings] VR menu item added to actions menu with WebXR integration');
    }

    /**
     * Add styles for actions menu - now minimal since styles moved to CSS
     */
    addActionsMenuStyles() {
        // Styles are now consolidated in viewer.css
        // This method kept for backward compatibility
        console.log('[UISettings] Actions menu styles loaded from CSS');
    }

    setupReadyPlayerMeButton() {
        // Create avatar button
        const avatarButton = document.createElement('button');
        avatarButton.className = 'rpm-avatar-button';
        avatarButton.innerHTML = '<span>👤</span> Create Avatar';
        avatarButton.title = 'Create Custom Avatar';

        // Add click handler to open Ready Player Me
        avatarButton.addEventListener('click', () => {
            this.viewer.openReadyPlayerMe();
        });

        // Add to container
        this.container.appendChild(avatarButton);

        // Store reference
        this.components.readyPlayerMeButton = avatarButton;
    }
    /**
     * Initialize MeshSelector component
     */
    async initializeMeshSelector(config) {
        try {
            console.log('[UISettings] Initializing MeshSelector with config:', config);

            this.components.meshSelector = new MeshSelector(
                this.viewer,
                config.meshPath,
                config.meshFormat,
                config.enableDelete
            );

            console.log('[UISettings] MeshSelector initialized successfully');
            return this.components.meshSelector;
        } catch (error) {
            console.error('[UISettings] Failed to initialize MeshSelector:', error);
            return null;
        }
    }

    /**
     * Initialize CameraViewer component
     */
    async initializeCameraViewer(config) {
        try {
            console.log('[UISettings] Initializing CameraViewer');

            // Clean up existing instance if present
            if (this.components.cameraViewer) {
                this.components.cameraViewer.dispose();
                this.components.cameraViewer = null;
            }

            // Get or create video element - ensure it exists FIRST
            let videoElement = this.viewer?.inputManager?.videoElement;
            if (!videoElement) {
                console.log('[UISettings] Creating new video element for CameraViewer');
                videoElement = document.createElement('video');
                videoElement.id = 'input-video';
                videoElement.autoplay = true;
                videoElement.playsInline = true;
                videoElement.muted = true;
                videoElement.style.display = 'none';
                document.body.appendChild(videoElement);

                // Store in viewer's inputManager
                if (this.viewer) {
                    if (!this.viewer.inputManager) {
                        this.viewer.inputManager = {};
                    }
                    this.viewer.inputManager.videoElement = videoElement;
                }
            }

            // Create new camera viewer instance with simplified settings
            this.components.cameraViewer = new CameraViewer(this.viewer, {
                width: 640,
                height: 480,
                preferPopup: false, // Use corner mode by default
                modalConfig: {
                    maxWidth: '90vw',
                    maxHeight: '90vh',
                    minWidth: '320px',
                    minHeight: '180px'
                }
            });

            // Initialize with video element
            const canvas = await this.components.cameraViewer.initialize(videoElement);
            console.log('[UISettings] CameraViewer initialized with canvas:', canvas ? 'Success' : 'Failed');

            // Set up camera callbacks
            this.setupCameraCallbacks();

            return this.components.cameraViewer;
        } catch (error) {
            console.error('[UISettings] Failed to initialize CameraViewer:', error);
            return null;
        }
    }

    /**
     * Setup callbacks for camera viewer events
     */
    setupCameraCallbacks() {
        if (!this.components.cameraViewer) return;

        this.components.cameraViewer.setViewerCallbacks(
            // On camera open
            (canvas) => {
                console.log('[UISettings] Camera opened');

                // Notify any listeners about camera state
                if (this.viewer.gestureController) {
                    // Give a moment for the camera to fully initialize
                    setTimeout(() => {
                        this.viewer.gestureController.validateComponents();
                    }, 500);
                }
            },
            // On camera close
            () => {
                console.log('[UISettings] Camera closed');
            }
        );
    }


    /**
     * Get reference to the camera viewer component
     */
    getCameraViewer() {
        return this.components.cameraViewer;
    }

    /**
     * Initialize Role Playing Panel component
     */
    async initializeRolePlayingPanel(config = {}) {
        try {
            console.log('[UISettings] Initializing Role Playing Panel');

            // Import components
            const { default: RolePlayingPanel } = await import('./components/RolePlayingPanel.js');
            const { default: CharacterService } = await import('./services/CharacterService.js');

            // Get agent coordinator from viewer
            const agentCoordinator = this.viewer?.talkingAvatar?.agentCoordinator;

            // Initialize character service
            const characterService = new CharacterService({
                agentCoordinator: agentCoordinator,
                enablePersistence: true
            });

            // Create role playing panel
            this.components.rolePlayingPanel = new RolePlayingPanel({
                container: this.container,
                characterService: characterService,
                agentCoordinator: agentCoordinator,
                position: config.position || 'right',
                minimized: config.minimized || false,
                onCharacterChange: (character) => {
                    console.log('[UISettings] Character changed:', character.name);
                    this.showNotification(`Character changed to: ${character.name}`, false);
                },
                onPersonalityUpdate: (personality) => {
                    console.log('[UISettings] Personality updated:', personality);
                    this.showNotification('Personality settings updated', false);
                }
            });

            // Store character service reference
            this.components.characterService = characterService;

            console.log('[UISettings] Role Playing Panel initialized successfully');
            return this.components.rolePlayingPanel;

        } catch (error) {
            console.error('[UISettings] Failed to initialize Role Playing Panel:', error);
            return null;
        }
    }

    /**
     * Toggle Role Playing Panel visibility
     */
    async toggleRolePlayingPanel() {
        try {
            if (!this.components.rolePlayingPanel) {
                // Initialize if not already initialized
                await this.initializeRolePlayingPanel();
            }

            if (this.components.rolePlayingPanel) {
                this.components.rolePlayingPanel.toggle();

                // Update menu item appearance
                const isVisible = this.components.rolePlayingPanel.isVisible;
                this.updateRolePlayingMenuItem(isVisible);

                this.showNotification(`Role Playing Panel ${isVisible ? 'opened' : 'closed'}`, false);
            } else {
                this.showNotification('Failed to initialize Role Playing Panel', true);
            }
        } catch (error) {
            console.error('[UISettings] Failed to toggle Role Playing Panel:', error);
            this.showNotification('Failed to open Role Playing Panel', true);
        }
    }

    /**
     * Update Role Playing menu item appearance
     */
    updateRolePlayingMenuItem(isActive) {
        const menuItem = this.components.actionsMenu?.rolePlayingMenuItem;
        if (!menuItem) return;

        if (isActive) {
            menuItem.classList.add('active');
            menuItem.innerHTML = '<span>🎭</span> Role Playing (Active)';
            menuItem.style.color = '#5856D6'; // Purple color for role playing
        } else {
            menuItem.classList.remove('active');
            menuItem.innerHTML = '<span>🎭</span> Role Playing';
            menuItem.style.color = 'white'; // Default color
        }
    }

    /**
     * Get reference to the role playing panel component
     */
    getRolePlayingPanel() {
        return this.components.rolePlayingPanel;
    }

    /**
     * Get reference to the character service
     */
    getCharacterService() {
        return this.components.characterService;
    }

    /**
     * Initialize API Cost Tracker UI component
     */
    async initializeApiCostTracker(config = {}) {
        try {
            console.log('[UISettings] Initializing API Cost Tracker');

            // Import the ApiCostTracker UI component
            const { ApiCostTrackerUI } = await import('../../src/ui/components/ApiCostTracker.js');

            // Get the cost tracker instance from the agent service
            const costTracker = await this._getCostTrackerInstance();

            if (!costTracker) {
                console.warn('[UISettings] No cost tracker instance found - API cost tracking disabled');
                return null;
            }

            // Create and initialize the UI component
            this.components.apiCostTracker = new ApiCostTrackerUI({
                position: config.position || 'top-right',
                minimized: config.minimized || false,
                showDetailed: config.showDetailed || false,
                updateInterval: config.updateInterval || 1000
            });

            await this.components.apiCostTracker.initialize(costTracker);

            console.log('[UISettings] API Cost Tracker initialized successfully');
            return this.components.apiCostTracker;

        } catch (error) {
            console.error('[UISettings] Failed to initialize API Cost Tracker:', error);
            return null;
        }
    }

    /**
     * Toggle API Cost Tracker visibility
     */
    async toggleApiCostTracker() {
        try {
            if (!this.components.apiCostTracker) {
                // Initialize if not already initialized
                const costTracker = await this._getCostTrackerInstance();

                if (!costTracker) {
                    this.showNotification('No cost tracker instance found - API cost tracking disabled', true);
                    return;
                }

                // Import the ApiCostTracker UI component
                const { ApiCostTrackerUI } = await import('../../src/ui/components/ApiCostTracker.js');

                // Create and initialize the UI component
                this.components.apiCostTracker = new ApiCostTrackerUI({
                    position: 'top-right',
                    minimized: false,
                    showDetailed: false,
                    updateInterval: 1000
                });

                await this.components.apiCostTracker.initialize(costTracker);
                this.showNotification('API Cost Tracker enabled', false);

                // Update menu item appearance
                this.updateApiCostMenuItem(true);
            } else {
                // Toggle visibility
                this.components.apiCostTracker.toggle();

                // Check if it's visible and update menu item
                const isVisible = this.components.apiCostTracker.container &&
                    this.components.apiCostTracker.container.style.display !== 'none';
                this.updateApiCostMenuItem(isVisible);

                this.showNotification(`API Cost Tracker ${isVisible ? 'shown' : 'hidden'}`, false);
            }
        } catch (error) {
            console.error('[UISettings] Failed to toggle API Cost Tracker:', error);
            this.showNotification('Failed to toggle API Cost Tracker', true);
        }
    }

    /**
     * Update API Cost Tracker menu item appearance
     */
    updateApiCostMenuItem(isActive) {
        const menuItem = this.components.actionsMenu?.apiCostMenuItem;
        if (!menuItem) return;

        if (isActive) {
            menuItem.classList.add('active');
            menuItem.innerHTML = '<span>💰</span> API Cost Tracker (On)';
            menuItem.style.color = '#4CAF50'; // Green color for active state
        } else {
            menuItem.classList.remove('active');
            menuItem.innerHTML = '<span>💰</span> API Cost Tracker';
            menuItem.style.color = 'white'; // Default color
        }
    }

    /**
     * Get cost tracker instance from the agent service
     * @private
     */
    async _getCostTrackerInstance() {
        try {
            // Try to get it from the viewer's talking avatar agent service
            const agentService = this.viewer?.talkingAvatar?.agentCoordinator?.getAgentService();
            if (agentService && typeof agentService.getCostTracker === 'function') {
                return agentService.getCostTracker();
            }

            // Fallback: import and use the default cost calculator from consolidated pricing config
            const { defaultCostCalculator } = await import('../../src/agent/models/aliyun/AliyunPricingConfig.js');
            return defaultCostCalculator;

        } catch (error) {
            console.error('[UISettings] Failed to get cost tracker instance:', error);
            return null;
        }
    }

    /**
     * Setup progress container
     */
    setupProgressContainer() {
        const template = `
            <div class="progress-fill"></div>
            <div class="progress-text">Generating: 0%</div>
            <div class="status-text">Starting...</div>
            <button class="cancel-button">Cancel</button>
            <style>
                .progress-container { padding: calc(12px * var(--ui-scale)) calc(20px * var(--ui-scale)); }
                .progress-text, .status-text { font-size: calc(14px * var(--ui-scale)); }
                .progress-container .cancel-button {
                    padding: calc(6px * var(--ui-scale)) calc(16px * var(--ui-scale));
                    border-radius: calc(6px * var(--ui-scale));
                    font-size: calc(14px * var(--ui-scale));
                }
                .progress-bar { height: calc(4px * var(--ui-scale)); }
            </style>
        `;

        // Create progress container
        const progressContainer = document.createElement('div');
        progressContainer.className = 'progress-container';
        progressContainer.innerHTML = template;

        // Add cancel handler
        const cancelButton = progressContainer.querySelector('.cancel-button');
        cancelButton.addEventListener('click', () => {
            if (this.state.isGenerating) {
                this.viewer.cancelGeneration();
            }
        });

        document.body.appendChild(progressContainer);

        // Store reference
        this.components.progressContainer = {
            container: progressContainer,
            bar: progressContainer.querySelector('.progress-fill'),
            text: progressContainer.querySelector('.progress-text'),
            status: progressContainer.querySelector('.status-text'),
            cancelButton: cancelButton
        };
    }

    /**
     * Setup conversion UI for the viewer
     * @param {boolean} addButton - Whether to add the standalone button (defaults to true)
     */
    setupConversionUI(addButton = true) {
        if (!this.container) return;

        // Create conversion modal
        const modal = document.createElement('div');
        modal.className = 'conversion-modal hidden';

        // Create modal content
        const content = document.createElement('div');
        content.className = 'conversion-content';

        // Create source type wrapper for horizontal layout
        const sourceWrapper = document.createElement('div');
        sourceWrapper.className = 'source-wrapper';

        // Source type selector
        const sourceSelect = document.createElement('select');
        sourceSelect.className = 'source-select';
        sourceSelect.innerHTML = `
            <option value="text">Text to 3D</option>
            <option value="textto3d">Text to 3D (Dedicated)</option>
            <option value="image">Image to 3D</option>
            <option value="doll">Doll to 3D</option>
            <option value="tripo-doll">Tripo Doll</option>
        `;

        // Create microphone button for ASR (initially hidden)
        const microphoneButton = document.createElement('button');
        microphoneButton.className = 'microphone-button hidden';
        microphoneButton.type = 'button';
        microphoneButton.innerHTML = `
            <svg class="mic-icon" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2a3 3 0 0 0-3 3v6a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3z"/>
                <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                <line x1="12" y1="19" x2="12" y2="23"/>
                <line x1="8" y1="23" x2="16" y2="23"/>
            </svg>
            <span class="mic-text">Voice Input</span>
        `;
        microphoneButton.title = 'Record audio for text generation';

        // Add recording state management
        microphoneButton.isRecording = false;
        microphoneButton.mediaRecorder = null;
        microphoneButton.audioChunks = [];

        // Create regenerate checkbox wrapper with icon
        const checkboxWrapper = document.createElement('div');
        checkboxWrapper.className = 'regenerate-wrapper';

        const regenerateCheckbox = document.createElement('input');
        regenerateCheckbox.type = 'checkbox';
        regenerateCheckbox.id = 'regenerate-checkbox';
        regenerateCheckbox.className = 'regenerate-checkbox';

        // Set reference to checkbox for viewer access
        this.viewer.regenerateCheckbox = regenerateCheckbox;

        // Add change listener to regenerate checkbox
        regenerateCheckbox.addEventListener('change', (e) => {
            if (e.target.checked) {
                console.log('[UI] Regenerate enabled - will use random seed on next generation');
            } else {
                console.log('[UI] Regenerate disabled - will use default seed on next generation');
            }
        });

        const checkboxLabel = document.createElement('label');
        checkboxLabel.htmlFor = 'regenerate-checkbox';
        checkboxLabel.className = 'regenerate-label';
        // Using a refresh/sync icon
        checkboxLabel.innerHTML = `
            <svg class="regenerate-icon" viewBox="0 0 24 24" width="18" height="18">
                <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
            </svg>
            <span class="tooltip">Regenerate with new seed</span>
        `;

        // Add elements to their containers
        checkboxWrapper.appendChild(regenerateCheckbox);
        checkboxWrapper.appendChild(checkboxLabel);

        sourceWrapper.appendChild(sourceSelect);
        sourceWrapper.appendChild(checkboxWrapper);

        // Input area
        const inputArea = document.createElement('div');
        inputArea.className = 'input-area';

        const textInput = document.createElement('textarea');
        textInput.placeholder = 'Enter description for 3D model...';
        textInput.className = 'text-input';

        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'image/*,.glb';
        fileInput.className = 'file-input hidden';

        const preview = document.createElement('div');
        preview.className = 'preview';

        // Buttons
        const generateButton = document.createElement('button');
        generateButton.textContent = 'Generate';
        generateButton.className = 'generate-button';

        const cancelButton = document.createElement('button');
        cancelButton.textContent = 'Cancel';
        cancelButton.className = 'cancel-button';

        // Add elements to modal
        inputArea.appendChild(textInput);
        inputArea.appendChild(fileInput);
        inputArea.appendChild(preview);
        inputArea.appendChild(microphoneButton);

        content.appendChild(sourceWrapper);
        content.appendChild(inputArea);
        content.appendChild(generateButton);
        content.appendChild(cancelButton);

        modal.appendChild(content);
        this.container.appendChild(modal);

        // Only add the standalone button if specified
        if (addButton) {
            // Add conversion button to main UI
            const convertButton = document.createElement('button');
            convertButton.textContent = 'Convert to 3D';
            convertButton.className = 'convert-button';

            // Event handlers
            this.setupConversionEventHandlers(
                modal, sourceSelect, textInput, fileInput,
                preview, generateButton, cancelButton, convertButton, inputArea, microphoneButton
            );

            this.container.appendChild(convertButton);
        } else {
            // Setup event handlers without the standalone button
            this.setupConversionEventHandlers(
                modal, sourceSelect, textInput, fileInput,
                preview, generateButton, cancelButton, null, inputArea, microphoneButton
            );
        }
    }

    /**
     * Setup event handlers for conversion UI
     */
    setupConversionEventHandlers(
        modal, sourceSelect, textInput, fileInput,
        preview, generateButton, cancelButton, convertButton, inputArea, microphoneButton
    ) {
        // Create camera button for tripo-doll with improved styling
        const cameraButton = document.createElement('button');
        cameraButton.className = 'camera-button hidden'; // Start hidden by default
        cameraButton.innerHTML = '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path><circle cx="12" cy="13" r="4"></circle></svg> Use Camera';
        cameraButton.title = 'Take a photo with your camera';
        // Styles are now in CSS

        // Create gender selector for tripo-doll with improved styling
        const genderWrapper = document.createElement('div');
        genderWrapper.className = 'gender-wrapper hidden'; // Start hidden by default
        // Styles are now in CSS

        const genderLabel = document.createElement('label');
        genderLabel.textContent = 'Gender:';
        genderLabel.htmlFor = 'gender-select';
        // Styles are now in CSS

        const genderSelect = document.createElement('select');
        genderSelect.className = 'gender-select';
        genderSelect.id = 'gender-select';
        genderSelect.innerHTML = `
            <option value="boy">Boy</option>
            <option value="girl">Girl</option>
        `;
        // Styles are now in CSS

        genderWrapper.appendChild(genderLabel);
        genderWrapper.appendChild(genderSelect);

        // Don't add camera button and gender selector to input area initially
        // They will be added only when Tripo Doll is selected

        // Source select change handler
        sourceSelect.addEventListener('change', () => {
            const source = sourceSelect.value;
            console.log('[UISettings] Source changed to:', source);

            // First, remove all UI elements from the DOM to ensure they don't appear
            if (cameraButton.parentNode === inputArea) {
                inputArea.removeChild(cameraButton);
            }

            if (genderWrapper.parentNode === inputArea) {
                inputArea.removeChild(genderWrapper);
            }

            // Hide all input elements first
            textInput.classList.add('hidden');
            fileInput.classList.add('hidden');

            // Show appropriate elements based on source
            if (source === 'text' || source === 'textto3d') {
                textInput.classList.remove('hidden');

                // Show microphone button only for textto3d (dedicated service)
                if (source === 'textto3d') {
                    microphoneButton.classList.remove('hidden');
                } else {
                    microphoneButton.classList.add('hidden');
                }
            } else {
                fileInput.classList.remove('hidden');
                microphoneButton.classList.add('hidden');
            }

            // ONLY add camera button and gender selector for Tripo Doll
            if (source === 'tripo-doll') {
                // Add camera button back to the DOM
                inputArea.appendChild(cameraButton);
                cameraButton.classList.remove('hidden');
                cameraButton.innerHTML = '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path><circle cx="12" cy="13" r="4"></circle></svg> Take Tripo Doll Photo';

                // Add gender selector back to the DOM
                inputArea.appendChild(genderWrapper);
                genderWrapper.classList.remove('hidden');

                console.log('[UISettings] Camera button and gender selector added for Tripo Doll');
            } else {
                console.log('[UISettings] Camera button and gender selector not added for source:', source);
            }

            // Set appropriate file accept types based on source
            switch (source) {
                case 'image':
                    fileInput.accept = 'image/*';
                    break;
                case 'doll':
                    fileInput.accept = 'image/*';  // Doll to 3D also accepts images
                    break;
                case 'tripo-doll':
                    fileInput.accept = 'image/*';  // Tripo Doll accepts images
                    break;
                default:
                    fileInput.accept = '';
            }

            // Clear preview and file input
            preview.innerHTML = '';
            fileInput.value = '';

            // Find the modal content element
            const modalContent = modal.querySelector('.conversion-content');
            if (modalContent) {
                // Add a title to the modal based on the selected source
                const modalTitle = modalContent.querySelector('.modal-title') || document.createElement('h2');
                modalTitle.className = 'modal-title';

                // Set title based on source
                switch (source) {
                    case 'text':
                        modalTitle.textContent = 'Text to 3D';
                        break;
                    case 'textto3d':
                        modalTitle.textContent = 'Text to 3D (Dedicated)';
                        break;
                    case 'image':
                        modalTitle.textContent = 'Image to 3D';
                        break;
                    case 'doll':
                        modalTitle.textContent = 'Doll to 3D';
                        break;
                    case 'tripo-doll':
                        modalTitle.textContent = 'Tripo Doll';
                        break;
                    default:
                        modalTitle.textContent = 'Any to 3D';
                }

                // Add title to content if it doesn't exist
                if (!modalContent.querySelector('.modal-title')) {
                    modalContent.insertBefore(modalTitle, modalContent.firstChild);
                }
            }
        });

        // File input change handler
        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        preview.innerHTML = `<img src="${e.target.result}" alt="Preview">`;
                    };
                    reader.readAsDataURL(file);
                } else {
                    preview.innerHTML = `<div class="file-name">${file.name}</div>`;
                }
            }
        });

        // Camera button click handler
        cameraButton.addEventListener('click', async () => {
            // Check if camera is available
            if (!this.components.cameraViewer) {
                console.error('[UISettings] Camera viewer not available');
                this.showNotification('Camera not available', true);
                return;
            }

            // Get the current source type
            const source = sourceSelect.value;

            // Only allow camera for Tripo Doll
            if (source !== 'tripo-doll') {
                console.error('[UISettings] Camera not available for this source type');
                this.showNotification('Camera is only available for Tripo Doll', true);
                return;
            }

            const isTripoDoll = source === 'tripo-doll';

            try {
                // Create a promise to get the captured photo
                const capturePromise = new Promise((resolve, reject) => {
                    // Start camera
                    this.components.cameraViewer.startCamera()
                        .then(stream => {
                            console.log('[UISettings] Camera started successfully, stream active:', stream && stream.active);

                            // Verify stream has video tracks
                            if (!stream || !stream.getVideoTracks || stream.getVideoTracks().length === 0) {
                                reject(new Error('No video tracks available in stream'));
                                return;
                            }

                            console.log('[UISettings] Stream has', stream.getVideoTracks().length, 'video tracks');

                            // Create centered embedded camera modal instead of popup
                            const cameraModal = this.createCenteredCameraModal(stream, isTripoDoll, genderSelect, resolve, reject);

                            if (!cameraModal) {
                                reject(new Error('Failed to create camera modal'));
                                return;
                            }


                        })
                        .catch(error => {
                            console.error('[UISettings] Failed to start camera:', error);
                            reject(error);
                        });
                });

                // Wait for capture
                const captureResult = await capturePromise;

                // Update preview with captured photo
                preview.innerHTML = `<img src="${captureResult.dataUrl}" alt="Captured Photo">`;

                // Update gender select if it's Tripo Doll
                if (isTripoDoll && captureResult.gender) {
                    genderSelect.value = captureResult.gender;
                }

                // Store the data URL for later use
                preview.dataset.capturedPhoto = captureResult.dataUrl;
                preview.dataset.source = source; // Store the source type

                console.log(`[UISettings] ${isTripoDoll ? 'Tripo Doll' : 'Doll'} photo captured successfully`);
            } catch (error) {
                console.error('[UISettings] Error in camera capture process:', error);
                this.showNotification('Failed to capture photo: ' + (error.message || 'Unknown error'), true);
            }
        });

        // Microphone button click handler
        microphoneButton.addEventListener('click', async () => {
            try {
                if (!microphoneButton.isRecording) {
                    // Start recording
                    console.log('[UI] Starting audio recording for ASR');

                    // Get optimized recording options
                    const recordingOptions = createOptimizedRecordingOptions();

                    // Request microphone access with optimized settings
                    const stream = await navigator.mediaDevices.getUserMedia(recordingOptions.getUserMediaOptions);

                    // Create MediaRecorder with optimized options
                    microphoneButton.mediaRecorder = new MediaRecorder(stream, recordingOptions.mediaRecorderOptions);

                    // Clear previous chunks
                    microphoneButton.audioChunks = [];

                    // Set up data collection
                    microphoneButton.mediaRecorder.ondataavailable = (event) => {
                        if (event.data.size > 0) {
                            microphoneButton.audioChunks.push(event.data);
                        }
                    };

                    // Set up recording completion with unified transcription workflow
                    microphoneButton.mediaRecorder.onstop = async () => {
                        console.log('[UI] Audio recording stopped, processing...');

                        // Stop all tracks to release microphone
                        stream.getTracks().forEach(track => track.stop());

                        // Create audio blob
                        const audioBlob = new Blob(microphoneButton.audioChunks, {
                            type: recordingOptions.mediaRecorderOptions.mimeType
                        });

                        // Use unified transcription workflow
                        await handleTranscriptionWorkflow({
                            audioBlob,
                            talkingAvatar: this.viewer.talkingAvatar,
                            uiComponent: this,
                            onSuccess: async (result) => {
                                // Fill the text input with transcribed text
                                textInput.value = result.text;

                                // Show success message
                                this.showNotification('Audio transcribed successfully! Generating 3D model...');

                                // Auto-click the generate button after a short delay
                                setTimeout(() => {
                                    generateButton.click();
                                }, 500);
                            },
                            onError: (error) => {
                                // Focus on text input for manual entry
                                if (textInput) {
                                    textInput.focus();
                                    textInput.placeholder = 'Please type your 3D model description here...';
                                }
                            }
                        });
                    };

                    // Start recording
                    microphoneButton.mediaRecorder.start();
                    microphoneButton.isRecording = true;

                    // Update UI
                    microphoneButton.classList.add('recording');
                    microphoneButton.innerHTML = `
                        <svg class="mic-icon" viewBox="0 0 24 24" width="20" height="20" fill="currentColor" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"/>
                            <path d="M12 2a3 3 0 0 0-3 3v6a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3z"/>
                            <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                            <line x1="12" y1="19" x2="12" y2="23"/>
                            <line x1="8" y1="23" x2="16" y2="23"/>
                        </svg>
                        <span class="mic-text">Recording...</span>
                    `;

                    this.showNotification('Recording audio... Click again to stop');

                } else {
                    // Stop recording
                    console.log('[UI] Stopping audio recording');

                    if (microphoneButton.mediaRecorder && microphoneButton.mediaRecorder.state === 'recording') {
                        microphoneButton.mediaRecorder.stop();
                    }

                    microphoneButton.isRecording = false;

                    // Update UI
                    microphoneButton.classList.remove('recording');
                    microphoneButton.innerHTML = `
                        <svg class="mic-icon" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12 2a3 3 0 0 0-3 3v6a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3z"/>
                            <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                            <line x1="12" y1="19" x2="12" y2="23"/>
                            <line x1="8" y1="23" x2="16" y2="23"/>
                        </svg>
                        <span class="mic-text">Voice Input</span>
                    `;

                    this.showNotification('Processing audio...');
                }
            } catch (error) {
                console.error('[UI] Error with microphone recording:', error);
                this.showNotification('Error accessing microphone: ' + error.message, true);

                // Reset recording state
                microphoneButton.isRecording = false;
                microphoneButton.classList.remove('recording');
                microphoneButton.innerHTML = `
                    <svg class="mic-icon" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M12 2a3 3 0 0 0-3 3v6a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3z"/>
                        <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                        <line x1="12" y1="19" x2="12" y2="23"/>
                        <line x1="8" y1="23" x2="16" y2="23"/>
                    </svg>
                    <span class="mic-text">Voice Input</span>
                `;
            }
        });

        // Generate button click handler
        generateButton.addEventListener('click', async () => {
            const source = sourceSelect.value;
            let input;
            let options = {};

            // Find the modal content element
            const modalContent = modal.querySelector('.conversion-content');
            if (modalContent) {
                // Create a button container for the buttons if it doesn't exist
                if (!modalContent.querySelector('.button-container')) {
                    const buttonContainer = document.createElement('div');
                    buttonContainer.className = 'button-container';

                    // Move the buttons to the container
                    buttonContainer.appendChild(generateButton);
                    buttonContainer.appendChild(cancelButton);

                    // Add the container to the content
                    modalContent.appendChild(buttonContainer);
                }
            }

            if (source === 'text' || source === 'textto3d') {
                input = textInput.value.trim();
                if (!input) {
                    this.showNotification('Please enter a description', true);
                    return;
                }
            } else if (source === 'tripo-doll' || source === 'doll') {
                // For tripo-doll and doll, check if we have a captured photo or a file
                if (preview.dataset.capturedPhoto) {
                    // Use captured photo
                    const dataUrl = preview.dataset.capturedPhoto;

                    // Convert data URL to blob
                    const response = await fetch(dataUrl);
                    const blob = await response.blob();

                    // Upload the blob with appropriate filename
                    const filename = source === 'tripo-doll' ? 'tripo_doll_photo.jpg' : 'doll_photo.jpg';
                    input = await this.uploadFile(blob);

                    // Add gender option for tripo-doll
                    if (source === 'tripo-doll') {
                        options.gender = genderSelect.value;
                    }

                    // Add source type to options
                    options.sourceType = preview.dataset.source || source;
                } else {
                    // Check for file input
                    const file = fileInput.files[0];
                    if (!file) {
                        // Only mention camera for Tripo Doll
                        if (source === 'tripo-doll') {
                            this.showNotification('Please select a file or capture a Tripo Doll photo', true);
                        } else {
                            this.showNotification('Please select a file', true);
                        }
                        return;
                    }
                    input = await this.uploadFile(file);

                    // Add gender option for tripo-doll
                    if (source === 'tripo-doll') {
                        options.gender = genderSelect.value;
                    }
                }
            } else {
                // For other sources, use file input
                const file = fileInput.files[0];
                if (!file) {
                    this.showNotification('Please select a file', true);
                    return;
                }
                input = await this.uploadFile(file);
            }

            // Start generation and hide modal
            modal.classList.add('hidden');
            await this.viewer.generate3DModel(source, input, options);
        });

        // Cancel button click handler
        cancelButton.addEventListener('click', () => {
            console.log('[UI] Cancel button clicked in modal');
            if (this.state.isGenerating) {
                this.viewer.cancelGeneration();
            }

            // Reset microphone button state when canceling
            if (microphoneButton && microphoneButton.isRecording) {
                if (microphoneButton.mediaRecorder && microphoneButton.mediaRecorder.state === 'recording') {
                    microphoneButton.mediaRecorder.stop();
                }
                microphoneButton.isRecording = false;
                microphoneButton.classList.remove('recording');
                microphoneButton.innerHTML = `
                    <svg class="mic-icon" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M12 2a3 3 0 0 0-3 3v6a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3z"/>
                        <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                        <line x1="12" y1="19" x2="12" y2="23"/>
                        <line x1="8" y1="23" x2="16" y2="23"/>
                    </svg>
                    <span class="mic-text">Voice Input</span>
                `;
            }

            modal.classList.add('hidden');
        });

        // Convert button click handler
        if (convertButton) {
            convertButton.addEventListener('click', () => {
                modal.classList.remove('hidden');
                textInput.value = '';
                fileInput.value = '';
                preview.innerHTML = '';
                sourceSelect.value = 'text';

                // Show text input and hide file input
                textInput.classList.remove('hidden');
                fileInput.classList.add('hidden');

                // Reset microphone button state
                microphoneButton.classList.add('hidden');
                if (microphoneButton.isRecording) {
                    if (microphoneButton.mediaRecorder && microphoneButton.mediaRecorder.state === 'recording') {
                        microphoneButton.mediaRecorder.stop();
                    }
                    microphoneButton.isRecording = false;
                    microphoneButton.classList.remove('recording');
                    microphoneButton.innerHTML = `
                        <svg class="mic-icon" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12 2a3 3 0 0 0-3 3v6a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3z"/>
                            <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                            <line x1="12" y1="19" x2="12" y2="23"/>
                            <line x1="8" y1="23" x2="16" y2="23"/>
                        </svg>
                        <span class="mic-text">Voice Input</span>
                    `;
                }

                // Remove camera button and gender selector from DOM if they exist
                if (cameraButton.parentNode === inputArea) {
                    inputArea.removeChild(cameraButton);
                }

                if (genderWrapper.parentNode === inputArea) {
                    inputArea.removeChild(genderWrapper);
                }

                // Trigger the change event to ensure proper UI state
                const event = new Event('change');
                sourceSelect.dispatchEvent(event);
            });
        }
    }

    /**
     * Create a centered camera modal for tripo-doll photo capture
     */
    createCenteredCameraModal(stream, isTripoDoll, genderSelect, resolve, reject) {
        // Create modal overlay
        const modalOverlay = document.createElement('div');
        modalOverlay.className = 'tripo-camera-modal-overlay';

        // Create modal content
        const modalContent = document.createElement('div');
        modalContent.className = 'tripo-camera-modal-content';

        // Create video element
        const videoElement = document.createElement('video');
        videoElement.autoplay = true;
        videoElement.playsInline = true;
        videoElement.muted = true;
        videoElement.srcObject = stream;

        // Ensure video plays properly
        videoElement.addEventListener('loadedmetadata', () => {
            console.log('[UISettings] Video metadata loaded, dimensions:', videoElement.videoWidth, 'x', videoElement.videoHeight);
            videoElement.play().catch(err => {
                console.error('[UISettings] Error starting video playback:', err);
            });
        });

        // Handle video errors
        videoElement.addEventListener('error', (e) => {
            console.error('[UISettings] Video error:', e);
        });

        // Add additional debugging
        videoElement.addEventListener('canplay', () => {
            console.log('[UISettings] Video can start playing');
        });

        videoElement.addEventListener('playing', () => {
            console.log('[UISettings] Video is now playing');
        });

        // Force video to start playing if stream is already ready
        if (stream && stream.active && stream.getVideoTracks().length > 0) {
            console.log('[UISettings] Stream is ready, attempting to play video immediately');
            setTimeout(() => {
                videoElement.play().catch(err => {
                    console.error('[UISettings] Error in delayed video play:', err);
                });
            }, 100);
        }

        // Create title
        const title = document.createElement('div');
        title.className = 'tripo-camera-modal-title';
        title.textContent = isTripoDoll ? 'Tripo Doll Photo Capture' : 'Photo Capture';

        // No gender selector in the photo capture modal - it's handled in the any-to-3d panel

        // Create capture button
        const captureButton = document.createElement('button');
        captureButton.className = 'tripo-camera-capture-button';

        // Add camera icon to capture button
        captureButton.innerHTML = `
            <svg viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                <circle cx="12" cy="13" r="4"></circle>
            </svg>
        `;

        // Create close button
        const closeButton = document.createElement('button');
        closeButton.className = 'tripo-camera-close-button';
        closeButton.innerHTML = '×';

        // Add capture functionality
        captureButton.addEventListener('click', () => {
            try {
                // Create canvas for capture
                const canvas = document.createElement('canvas');
                canvas.width = videoElement.videoWidth;
                canvas.height = videoElement.videoHeight;

                // Draw video frame to canvas
                const ctx = canvas.getContext('2d');
                ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

                // Get data URL
                const dataUrl = canvas.toDataURL('image/jpeg', 0.9);

                // Close modal
                this.closeCameraModal(modalOverlay);

                // Resolve with data URL and gender from the any-to-3d panel
                resolve({
                    dataUrl,
                    gender: isTripoDoll && genderSelect ? genderSelect.value : null
                });
            } catch (error) {
                console.error('[UISettings] Error capturing photo:', error);
                reject(error);
            }
        });

        // Add close functionality
        const closeModal = () => {
            this.closeCameraModal(modalOverlay);
            reject(new Error('Camera modal closed'));
        };

        closeButton.addEventListener('click', closeModal);
        modalOverlay.addEventListener('click', (e) => {
            if (e.target === modalOverlay) {
                closeModal();
            }
        });

        // Escape key to close
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                document.removeEventListener('keydown', handleEscape);
                closeModal();
            }
        };
        document.addEventListener('keydown', handleEscape);

        // Assemble modal
        modalContent.appendChild(videoElement);
        modalContent.appendChild(title);
        modalContent.appendChild(captureButton);
        modalContent.appendChild(closeButton);
        modalOverlay.appendChild(modalContent);

        // Add to DOM
        document.body.appendChild(modalOverlay);

        // Animate in
        requestAnimationFrame(() => {
            modalOverlay.style.opacity = '1';
            modalContent.style.transform = 'scale(1)';
        });

        return modalOverlay;
    }

    /**
     * Close the camera modal
     */
    closeCameraModal(modalOverlay) {
        // DON'T stop the camera stream here - let the cameraViewer manage it
        // The stream should remain active for potential reuse
        console.log('[UISettings] Closing camera modal, keeping stream active for reuse');

        // Animate out and remove
        modalOverlay.style.opacity = '0';
        modalOverlay.querySelector('.tripo-camera-modal-content').style.transform = 'scale(0.9)';

        setTimeout(() => {
            if (modalOverlay.parentNode) {
                modalOverlay.parentNode.removeChild(modalOverlay);
            }
        }, 300);
    }



    /**
     * Helper method to upload a file
     * @param {File|Blob} file - The file or blob to upload
     * @returns {Promise<Object>} The uploaded file data
     */
    async uploadFile(file) {
        // Convert file to proper format for image/doll processing
        return new Promise((resolve) => {
            if (file instanceof Blob) {
                // If it's already a blob, create a data URL
                const reader = new FileReader();
                reader.onload = (e) => {
                    // Format the image data as required by the API
                    const imageData = {
                        url: e.target.result,
                        name: file.name || 'captured_photo.jpg',
                        type: file.type || 'image/jpeg',
                        size: file.size,
                    };
                    resolve(imageData);
                };
                reader.readAsDataURL(file);
            } else {
                // Regular file object
                const reader = new FileReader();
                reader.onload = (e) => {
                    // Format the image data as required by the API
                    const imageData = {
                        url: e.target.result,
                        name: file.name,
                        type: file.type,
                        size: file.size,
                    };
                    resolve(imageData);
                };
                reader.readAsDataURL(file);
            }
        });
    }

    /**
     * Update the isGenerating state
     */
    setGeneratingState(isGenerating) {
        this.state.isGenerating = isGenerating;
    }

    /**
     * Show progress indicator
     */
    showProgress() {
        if (!this.components.progressContainer) return;

        this.components.progressContainer.container.classList.add('visible');
        this.updateProgress(10);
        this.updateStatus('Starting generation...');
    }

    /**
     * Hide progress indicator
     */
    hideProgress() {
        if (!this.components.progressContainer) return;

        this.components.progressContainer.container.classList.remove('visible');
    }

    /**
     * Update progress bar
     */
    updateProgress(progress) {
        const { bar, text } = this.components.progressContainer || {};
        if (!bar || !text) return;

        // Ensure progress is a number between 0-100
        const safeProgress = Math.min(Math.max(0, progress), 100);

        // Update the progress bar width
        bar.style.width = `${safeProgress}%`;

        // Update the progress text
        text.textContent = `Generating: ${Math.round(safeProgress)}%`;

        // Log for debugging
        console.log(`[UI] Progress updated: ${safeProgress}%`);
    }

    /**
     * Update status text
     */
    updateStatus(message, isError = false) {
        const { status } = this.components.progressContainer || {};
        if (!status) return;

        status.textContent = message;
        status.style.color = isError ? '#ff4444' : '';

        // Log for debugging
        console.log(`[UI] Status updated: ${message}`);
    }

    /**
     * Show a loading panel with a message
     */
    showLoadingPanel(message = 'Loading...') {
        // Use the progress container for loading panel
        if (this.components.progressContainer) {
            this.components.progressContainer.container.classList.add('visible');
            this.updateStatus(message);
            this.updateProgress(10); // Start with a small progress indication
            console.log(`[UISettings] Showing loading panel: ${message}`);
        } else {
            console.warn('[UISettings] Progress container not available for loading panel');
        }
    }

    /**
     * Update loading progress
     */
    updateLoadingProgress(message, progress = null) {
        if (!this.components.progressContainer) return;

        // Update status message
        this.updateStatus(message);

        // Update progress if provided
        if (progress !== null && !isNaN(progress)) {
            this.updateProgress(progress);
        }

        console.log(`[UISettings] Loading progress: ${message} (${progress !== null ? progress + '%' : 'indeterminate'})`);
    }

    /**
     * Hide the loading panel
     */
    hideLoadingPanel() {
        if (this.components.progressContainer) {
            this.components.progressContainer.container.classList.remove('visible');
            console.log('[UISettings] Hiding loading panel');
        }
    }

    /**
     * Set up notification system
     */
    setupNotifications() {
        if (document.getElementById('ui-notifications')) return;

        // Add styles for notifications
        const style = document.createElement('style');
        style.id = 'ui-notification-styles';
        style.textContent = `
            .ui-notification {
                position: fixed;
                top: calc(20px * var(--ui-scale));
                left: 50%;
                transform: translateX(-50%);
                background-color: rgba(0, 0, 0, 0.8);
                color: white;
                padding: calc(10px * var(--ui-scale)) calc(20px * var(--ui-scale));
                border-radius: calc(5px * var(--ui-scale));
                z-index: 1000;
                font-size: calc(14px * var(--ui-scale));
                box-shadow: 0 calc(2px * var(--ui-scale)) calc(10px * var(--ui-scale)) rgba(0, 0, 0, 0.3);
                opacity: 1;
                transition: opacity 0.3s ease;
                pointer-events: none;
            }

            .ui-notification.error {
                background-color: rgba(200, 0, 0, 0.8);
            }

            .ui-notification.fade-out {
                opacity: 0;
            }
        `;
        document.head.appendChild(style);

        // Create container for notifications
        const container = document.createElement('div');
        container.id = 'ui-notifications';
        document.body.appendChild(container);
    }

    /**
     * Show a notification message
     */
    showNotification(message, isError = false) {
        const container = document.getElementById('ui-notifications');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `ui-notification ${isError ? 'error' : ''}`;
        notification.textContent = message;
        container.appendChild(notification);

        // Remove after delay with fade
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => notification.remove(), 500);
        }, 2000);
    }

    /**
     * Update UI elements for speaking state
     * @param {boolean} isSpeaking - Whether the avatar is speaking
     * @param {Object} options - Additional options
     * @param {Object} options.talkingHeadControls - TalkingHead controls object
     * @param {Object} options.animator - Animator instance
     * @param {boolean} options.isListening - Whether currently listening
     */
    updateUIForSpeakingState(isSpeaking, options = {}) {
        const { talkingHeadControls, animator, isListening } = options;

        // Update UI
        if (talkingHeadControls) {
            talkingHeadControls.statusSpan.textContent = isSpeaking ? 'Speaking...' : 'Ready';
        }

        // Update animation state if we have an animator
        if (animator) {
            if (isSpeaking) {
                // Set speaking state with speak_with_hands animation
                animator.setState('speaking');
                console.log('[TalkingAvatar] Set animator to speaking state');
            } else if (isListening) {
                // If we're listening, set listening state
                animator.setState('listening');
                console.log('[TalkingAvatar] Set animator to listening state');
            } else {
                // Otherwise set idle state
                animator.setState('idle');
                console.log('[TalkingAvatar] Set animator to idle state');
            }
        }

        // VAD handled by Aliyun omni server - no local VAD needed
    }

    /**
     * Update UI elements for listening state
     * @param {boolean} isListening - Whether the avatar is listening
     * @param {Object} options - Additional options
     * @param {Object} options.talkingHeadControls - TalkingHead controls object
     * @param {Object} options.animator - Animator instance
     * @param {boolean} options.isSpeaking - Whether currently speaking
     */
    updateUIForListeningState(isListening, options = {}) {
        const { talkingHeadControls, animator, isSpeaking } = options;

        // Update UI
        if (talkingHeadControls) {
            talkingHeadControls.statusSpan.textContent = isListening ? 'Listening...' : 'Ready';
        }

        // Update animation state if we have an animator
        if (animator) {
            if (isListening) {
                animator.setState('listening');
                console.log('[TalkingAvatar] Set animator to listening state');
            } else if (isSpeaking) {
                animator.setState('speaking');
                console.log('[TalkingAvatar] Set animator to speaking state');
            } else {
                animator.setState('idle');
                console.log('[TalkingAvatar] Set animator to idle state');
            }
        }
    }

    /**
     * Update listening UI elements
     * @param {boolean} isListening - Whether currently listening
     */
    updateListeningUI(isListening) {
        // Update UI elements to reflect listening status
        // Check if the method exists before calling it
        if (typeof this.updateListeningStatus === 'function') {
            this.updateListeningStatus(isListening);
        } else {
            // Fallback: Update status message if the specific method doesn't exist
            if (this.updateStatus) {
                this.updateStatus(
                    isListening ? 'Listening...' : 'Ready',
                    false
                );
            }
        }
    }

    /**
     * Update listening status specifically
     * @param {boolean} isListening - Whether currently listening
     */
    updateListeningStatus(isListening) {
        // Update status message for listening state
        this.updateStatus(
            isListening ? 'Listening...' : 'Ready',
            false
        );
    }

    /**
     * Update avatar status with unified handling
     * @param {string} status - Status message to display
     * @param {Object} options - Additional options
     * @param {Object} options.talkingHeadControls - TalkingHead controls object
     * @param {Object} options.uiManager - UI manager instance
     * @param {Object} options.statusDisplay - Status display element
     */
    updateAvatarStatus(status, options = {}) {
        const { talkingHeadControls, uiManager, statusDisplay } = options;

        if (talkingHeadControls) {
            talkingHeadControls.statusSpan.textContent = status;
        }

        if (uiManager) {
            uiManager.updateStatus(status);
        } else if (statusDisplay) {
            statusDisplay.textContent = status;
        } else {
            console.log(`[TalkingAvatar] Status: ${status}`);
        }
    }

    /**
     * Clean up resources
     */
    dispose() {
        // Remove gesture control container
        if (this.components.gestureControl?.container) {
            this.components.gestureControl.container.remove();
        }

        // Remove progress container
        if (this.components.progressContainer?.container) {
            this.components.progressContainer.container.remove();
        }

        // Remove notification container
        const notificationContainer = document.getElementById('ui-notifications');
        if (notificationContainer) {
            notificationContainer.remove();
        }

        // Clean up role playing components
        if (this.components.rolePlayingPanel) {
            this.components.rolePlayingPanel.dispose();
            this.components.rolePlayingPanel = null;
        }

        if (this.components.characterService) {
            this.components.characterService.dispose();
            this.components.characterService = null;
        }

        // Clean up gesture UI elements
        this.cleanupGestureUI();
    }
    /**
     * Show rotation gesture indicator in the UI
     */
    showGestureRotationIndicator() {
        // Create indicator if it doesn't exist
        if (!this.rotationIndicator) {
            this.rotationIndicator = document.createElement('div');
            this.rotationIndicator.className = 'gesture-rotation-indicator';
            this.rotationIndicator.innerHTML = `
                <div class="rotation-icon">
                    <svg viewBox="0 0 24 24" width="24" height="24">
                        <path d="M12,4V1L8,5L12,9V6C15.31,6 18,8.69 18,12C18,15.31 15.31,18 12,18C8.69,18 6,15.31 6,12H4C4,16.42 7.58,20 12,20C16.42,20 20,16.42 20,12C20,7.58 16.42,4 12,4Z" />
                    </svg>
                </div>
                <div class="rotation-status">Rotating</div>
            `;
            document.body.appendChild(this.rotationIndicator);

            // Add styles if not already added
            if (!document.getElementById('gesture-rotation-styles')) {
                const styles = document.createElement('style');
                styles.id = 'gesture-rotation-styles';
                styles.innerHTML = `
                    .gesture-rotation-indicator {
                        position: fixed;
                        top: 100px;
                        right: 20px;
                        background-color: rgba(0, 0, 0, 0.7);
                        color: white;
                        padding: 8px 12px;
                        border-radius: 5px;
                        z-index: 1000;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        opacity: 0;
                        transition: opacity 0.3s ease;
                    }
                    .gesture-rotation-indicator.visible {
                        opacity: 1;
                    }
                    .rotation-icon {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    .rotation-icon svg {
                        fill: white;
                    }
                    .rotation-status {
                        font-size: 12px;
                    }
                `;
                document.head.appendChild(styles);
            }
        }

        // Make indicator visible
        this.rotationIndicator.classList.add('visible');

        // Clear hide timeout if exists
        if (this.hideRotationIndicatorTimeout) {
            clearTimeout(this.hideRotationIndicatorTimeout);
        }
    }

    /**
     * Hide rotation gesture indicator
     */
    hideGestureRotationIndicator() {
        if (!this.rotationIndicator) return;

        // Set timeout to hide with animation
        this.hideRotationIndicatorTimeout = setTimeout(() => {
            this.rotationIndicator.classList.remove('visible');
        }, 1000);
    }

    /**
     * Update rotation indicator - REMOVED locked parameter
     * This function is kept for compatibility but simplified
     */
    updateGestureRotationIndicator() {
        // Function kept for API compatibility but functionality removed
        // as we no longer show lock status for rotation
    }
    /**
     * Create and show scaling status indicator for gesture controls
     * @param {boolean} locked - Whether the scaling is locked
     */
    updateGestureScalingIndicator(locked) {
        // Create indicator if it doesn't exist
        if (!document.getElementById('scaling-status-indicator')) {
            const indicator = document.createElement('div');
            indicator.id = 'scaling-status-indicator';
            indicator.className = 'scaling-status-indicator';

            // Add loading spinner
            const spinner = document.createElement('div');
            spinner.className = 'scaling-spinner';
            indicator.appendChild(spinner);

            // Add status text
            const statusText = document.createElement('div');
            statusText.className = 'scaling-status-text';
            indicator.appendChild(statusText);

            // Add styles
            this.addGestureScalingIndicatorStyles();

            // Append to document
            document.body.appendChild(indicator);

            this.gestureUI.scalingIndicatorCreated = true;
        }

        // Get indicator and update text/class
        const indicator = document.getElementById('scaling-status-indicator');
        const statusText = indicator.querySelector('.scaling-status-text');

        // Update text and class based on lock state
        statusText.textContent = locked ? 'Locked' : 'Unlocked';
        indicator.className = `scaling-status-indicator ${locked ? 'locked' : 'unlocked'}`;

        // Show the indicator
        indicator.style.display = 'flex';
    }

    /**
     * Hide scaling status indicator
     */
    hideGestureScalingIndicator() {
        const indicator = document.getElementById('scaling-status-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }
    /**
     * Display gesture tutorial overlay
     */
    showGestureTutorial() {
        if (document.getElementById('gesture-tutorial')) return;

        const tutorial = document.createElement('div');
        tutorial.id = 'gesture-tutorial';
        tutorial.className = 'gesture-tutorial';

        // Add tutorial content with illustrations
        tutorial.innerHTML = `
            <div class="tutorial-header">
                <h3>Gesture Controls</h3>
                <button class="close-tutorial">×</button>
            </div>
            <div class="tutorial-content">
                <div class="gesture-item">
                    <div class="gesture-icon rotation-icon">
                        <svg viewBox="0 0 100 100" width="80" height="80">
                            <path d="M30,50 L50,30 L50,70 Z" fill="#4CAF50"/>
                            <circle cx="65" cy="50" r="15" stroke="#4CAF50" stroke-width="2" fill="none"/>
                        </svg>
                    </div>
                    <div class="gesture-description">
                        <h4>Rotation</h4>
                        <p>Extend index and middle fingers, move hand to rotate view</p>
                    </div>
                </div>
                <div class="gesture-item">
                    <div class="gesture-icon pinch-icon">
                        <svg viewBox="0 0 100 100" width="80" height="80">
                            <path d="M30,50 L70,50" stroke="#2196F3" stroke-width="2"/>
                            <circle cx="30" cy="50" r="10" fill="#2196F3"/>
                            <circle cx="70" cy="50" r="10" fill="#2196F3"/>
                            <path d="M45,35 L55,65" stroke="#2196F3" stroke-width="2" stroke-dasharray="4"/>
                        </svg>
                    </div>
                    <div class="gesture-description">
                        <h4>Zoom</h4>
                        <p>Pinch thumb and index finger to zoom in/out</p>
                    </div>
                </div>
            </div>
        `;

        // Add styles
        const style = document.createElement('style');
        style.id = 'gesture-tutorial-styles';
        style.textContent = `
            .gesture-tutorial {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background-color: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 20px;
                border-radius: 10px;
                z-index: 1000;
                max-width: 400px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
                transition: opacity 0.5s ease;
            }
            .tutorial-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 1px solid rgba(255, 255, 255, 0.3);
                padding-bottom: 10px;
                margin-bottom: 15px;
            }
            .close-tutorial {
                background: none;
                border: none;
                color: white;
                font-size: 20px;
                cursor: pointer;
            }
            .tutorial-content {
                display: flex;
                flex-direction: column;
                gap: 20px;
            }
            .gesture-item {
                display: flex;
                align-items: center;
                gap: 15px;
            }
            .gesture-icon {
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 50%;
                padding: 10px;
            }
            .gesture-description h4 {
                margin: 0 0 5px 0;
            }
            .gesture-description p {
                margin: 0;
                font-size: 14px;
                opacity: 0.8;
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(tutorial);

        // Add event listener for close button
        tutorial.querySelector('.close-tutorial').addEventListener('click', () => {
            this.hideGestureTutorial();
        });

        // Auto-hide after 10 seconds
        setTimeout(() => this.hideGestureTutorial(), 10000);
    }
    /**
     * Hide gesture tutorial overlay
     */
    hideGestureTutorial() {
        const tutorial = document.getElementById('gesture-tutorial');
        if (tutorial) {
            tutorial.style.opacity = '0';
            setTimeout(() => tutorial.remove(), 500);
        }
    }
    /**
     * Add styles for scaling status indicator
     */
    addGestureScalingIndicatorStyles() {
        if (!document.getElementById('scaling-indicator-styles')) {
            const style = document.createElement('style');
            style.id = 'scaling-indicator-styles';
            style.textContent = `
                .scaling-status-indicator {
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background-color: rgba(0, 0, 0, 0.7);
                    color: white;
                    border-radius: 5px;
                    padding: 15px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    z-index: 1000;
                    pointer-events: none;
                }

                .scaling-status-indicator.locked {
                    background-color: rgba(0, 120, 255, 0.7);
                }

                .scaling-status-indicator.unlocked {
                    background-color: rgba(100, 100, 100, 0.7);
                }

                .scaling-spinner {
                    width: 30px;
                    height: 30px;
                    border: 3px solid rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    border-top-color: #fff;
                    animation: spin 1s ease-in-out infinite;
                    margin-bottom: 10px;
                }

                .scaling-status-text {
                    font-size: 16px;
                    font-weight: bold;
                }

                @keyframes spin {
                    to { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * Show zoom slider for gesture controls
     */
    showGestureZoomSlider() {
        // Create slider if it doesn't exist
        if (!document.getElementById('zoom-slider-container')) {
            const container = document.createElement('div');
            container.id = 'zoom-slider-container';
            container.className = 'zoom-slider-container';

            const track = document.createElement('div');
            track.className = 'zoom-slider-track';

            const fill = document.createElement('div');
            fill.className = 'zoom-slider-fill';

            const thumb = document.createElement('div');
            thumb.className = 'zoom-slider-thumb';

            const label = document.createElement('div');
            label.className = 'zoom-label';

            track.appendChild(fill);
            track.appendChild(thumb);
            container.appendChild(track);
            container.appendChild(label);

            // Add styles for zoom slider
            this.addGestureZoomSliderStyles();

            document.body.appendChild(container);
            this.gestureUI.zoomSliderCreated = true;
        }

        // Show the slider
        const slider = document.getElementById('zoom-slider-container');
        slider.classList.add('visible');
    }

    /**
     * Hide zoom slider
     */
    hideGestureZoomSlider() {
        const slider = document.getElementById('zoom-slider-container');
        if (slider) {
            slider.classList.remove('visible');
        }
    }

    /**
     * Update zoom slider position based on current zoom level
     * @param {Object} camera - Camera object with zoom property
     * @param {number} minZoom - Minimum zoom level
     * @param {number} maxZoom - Maximum zoom level
     */
    updateGestureZoomSlider(camera, minZoom, maxZoom) {
        const slider = document.getElementById('zoom-slider-container');
        if (!slider) return;

        let currentZoom = 1;

        // Get current zoom level
        if (camera && camera.zoom !== undefined) {
            currentZoom = camera.zoom;
        }

        // Calculate position percentage based on zoom constraints
        let percentage = ((currentZoom - minZoom) / (maxZoom - minZoom)) * 100;

        // Clamp between 0-100
        percentage = Math.min(Math.max(percentage, 0), 100);

        // Update UI elements
        const fill = slider.querySelector('.zoom-slider-fill');
        const thumb = slider.querySelector('.zoom-slider-thumb');
        const label = slider.querySelector('.zoom-label');

        if (fill) fill.style.width = `${percentage}%`;
        if (thumb) thumb.style.left = `${percentage}%`;
        if (label) label.textContent = `Zoom: ${currentZoom.toFixed(1)}x`;
    }

    /**
     * Add styles for zoom slider
     */
    addGestureZoomSliderStyles() {
        if (!document.getElementById('zoom-slider-styles')) {
            const style = document.createElement('style');
            style.id = 'zoom-slider-styles';
            style.textContent = `
                .zoom-slider-container {
                    position: fixed;
                    bottom: 30px;
                    left: 50%;
                    transform: translateX(-50%);
                    background-color: rgba(0, 0, 0, 0.5);
                    border-radius: 20px;
                    padding: 5px 20px;
                    width: 300px;
                    height: 40px;
                    display: flex;
                    align-items: center;
                    pointer-events: none;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                    z-index: 1000;
                }

                .zoom-slider-container.visible {
                    opacity: 1;
                }

                .zoom-slider-track {
                    width: 100%;
                    height: 6px;
                    background-color: rgba(255, 255, 255, 0.3);
                    border-radius: 3px;
                    position: relative;
                }

                .zoom-slider-fill {
                    height: 100%;
                    background-color: rgba(0, 120, 255, 0.8);
                    border-radius: 3px;
                    position: absolute;
                    left: 0;
                    top: 0;
                }

                .zoom-slider-thumb {
                    width: 16px;
                    height: 16px;
                    background-color: white;
                    border-radius: 50%;
                    position: absolute;
                    top: 50%;
                    transform: translate(-50%, -50%);
                    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
                }

                .zoom-label {
                    position: absolute;
                    top: -20px;
                    left: 50%;
                    transform: translateX(-50%);
                    color: white;
                    font-size: 12px;
                    white-space: nowrap;
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * Clean up gesture UI elements
     */
    cleanupGestureUI() {
        // Remove scaling indicator
        const indicator = document.getElementById('scaling-status-indicator');
        if (indicator) {
            indicator.remove();
        }

        // Remove zoom slider
        const slider = document.getElementById('zoom-slider-container');
        if (slider) {
            slider.remove();
        }

        // Remove styles
        const indicatorStyles = document.getElementById('scaling-indicator-styles');
        if (indicatorStyles) {
            indicatorStyles.remove();
        }

        const sliderStyles = document.getElementById('zoom-slider-styles');
        if (sliderStyles) {
            sliderStyles.remove();
        }

        this.gestureUI = {
            scalingIndicatorCreated: false,
            zoomSliderCreated: false
        };
    }

    /**
     * Update the gesture control UI based on state
     * @param {boolean} isActive - Whether gesture controls are active
     */
    updateGestureControlUI(isActive) {
        // Update the menu item in the actions menu
        if (this.components.actionsMenu && this.components.actionsMenu.gestureMenuItem) {
            this.updateGestureMenuItem(this.components.actionsMenu.gestureMenuItem, isActive);
        }
    }

    /**
     * Check camera availability
     * @returns {Promise<boolean>} True if camera is available
     */
    async checkCameraAvailability() {
        try {
            // First check if mediaDevices is available
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                console.warn('[UISettings] Media devices not available - camera features will be disabled');
                return false;
            }

            // Try to request camera access to check permissions
            const testStream = await navigator.mediaDevices.getUserMedia({
                video: { width: 1, height: 1 },
                audio: false
            });

            // If successful, stop the test stream immediately
            testStream.getTracks().forEach(track => track.stop());
            return true;

        } catch (error) {
            console.warn('[UISettings] Camera not available:', error.message);
            return false;
        }
    }

    /**
     * Mute/disable gesture control UI when camera is unavailable
     * @param {HTMLElement} gestureMenuItem - The gesture control menu item
     */
    muteGestureControlUI(gestureMenuItem) {
        if (gestureMenuItem) {
            // Add visual indication that gesture controls are disabled
            gestureMenuItem.style.opacity = '0.5';
            gestureMenuItem.style.cursor = 'not-allowed';
            gestureMenuItem.title = 'Camera not available - gesture controls disabled';

            // Add disabled class for styling
            gestureMenuItem.classList.add('disabled');

            console.log('[UISettings] Gesture control UI muted due to camera unavailability');
        }
    }

}
