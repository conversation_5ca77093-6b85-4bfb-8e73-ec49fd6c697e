/**
 * Configuration Manager Service for Talking Avatar
 * 
 * Extracts configuration management functionality from TalkingAvatar to provide:
 * - Voice configuration and cloned voice management
 * - Contextual configuration for automatic responses
 * - Agent system configuration
 * - Configuration persistence and validation
 * 
 * This service follows the service-oriented architecture principles and provides
 * clean separation of configuration concerns from UI logic.
 */

import { createLogger } from '../../../src/utils/logger';

export interface VoiceConfig {
  currentLanguage: string;
  currentGender: string;
  useVoice: {
    lang: string;
    id: string;
  };
}

export interface ContextualConfig {
  enableAutomaticResponses: boolean;
  responseContext: string;
  timingProfile: string;
  analysisInterval: number;
}

export interface AgentSystemConfig {
  agentSystemAlwaysEnabled: boolean;
  llmService: string;
  configuredViaViewerConfig: boolean;
  aliyunAdvancedFeatures?: AliyunAdvancedFeatures;
}

export interface AliyunAdvancedFeatures {
  enableStructuredOutput: boolean;  // 结构化输出
  enableDeepThinking: boolean;      // 深度思考
  enableStreamingOutput: boolean;   // 流式输出  
  enableMultiTurnDialogue: boolean; // 多轮对话
  thinkingDepth?: 'shallow' | 'medium' | 'deep';
  outputFormat?: 'json' | 'text' | 'mixed';
  maxTurns?: number;
}

export interface ClonedVoiceData {
  roleName: string;
  referenceAudio?: string;
  voiceId?: string;
  settings?: Record<string, any>;
}

export interface ConfigManagerOptions {
  logger?: any;
  enableConfigPersistence?: boolean;
  configValidation?: boolean;
}

/**
 * Configuration Manager Service for Talking Avatar
 * Handles all configuration management for the avatar system
 */
export class ConfigManager {
  private logger: any;
  private options: ConfigManagerOptions;

  // Voice configuration
  private voiceConfig: VoiceConfig = {
    currentLanguage: 'auto',
    currentGender: 'male',
    useVoice: { lang: 'en-US', id: 'default' }
  };

  // Contextual configuration
  private contextualConfig: ContextualConfig = {
    enableAutomaticResponses: true,
    responseContext: 'friendly_assistant',
    timingProfile: 'balanced',
    analysisInterval: 5000
  };

  // Agent system configuration
  private agentSystemConfig: AgentSystemConfig = {
    agentSystemAlwaysEnabled: true,
    llmService: 'aliyun',
    configuredViaViewerConfig: true,
    aliyunAdvancedFeatures: {
      enableStructuredOutput: true,    // 结构化输出 - Default enabled for better data handling
      enableDeepThinking: true,        // 深度思考 - Default enabled for better reasoning  
      enableStreamingOutput: true,     // 流式输出 - Default enabled for responsive UI
      enableMultiTurnDialogue: true,   // 多轮对话 - Default enabled for conversation continuity
      thinkingDepth: 'medium',         // Balanced thinking depth
      outputFormat: 'json',            // Structured JSON for parsing
      maxTurns: 10                     // Reasonable conversation memory
    }
  };

  // Cloned voice management
  private currentVoiceConfig: ClonedVoiceData | null = null;
  private isUsingClonedVoice: boolean = false;
  private currentClonedVoice: string | null = null;
  private currentRole: string | null = null;
  private agentId: string | null = null;

  // Configuration change tracking
  private configChangeHistory: Array<{
    timestamp: number;
    configType: string;
    changes: Record<string, any>;
  }> = [];

  constructor(options: ConfigManagerOptions = {}) {
    this.logger = options.logger || createLogger('ConfigManager');
    this.options = options;

    // Initialize configuration objects
    this.voiceConfig = this.createDefaultVoiceConfig();
    this.contextualConfig = this.createDefaultContextualConfig();
    this.agentSystemConfig = this.createDefaultAgentSystemConfig();

    // Load persisted configuration
    this.loadPersistedConfiguration();

    this.logger.info('⚙️ ConfigManager initialized', {
      enableConfigPersistence: options.enableConfigPersistence ?? false,
      configValidation: options.configValidation ?? true
    });
  }

  /**
   * Create default voice configuration
   */
  private createDefaultVoiceConfig(): VoiceConfig {
    return {
      currentLanguage: 'auto',
      currentGender: 'male',
      useVoice: { lang: 'en-US', id: 'default' }
    };
  }

  /**
   * Create default contextual configuration
   */
  private createDefaultContextualConfig(): ContextualConfig {
    return {
      enableAutomaticResponses: true,
      responseContext: 'friendly_assistant',
      timingProfile: 'balanced',
      analysisInterval: 5000
    };
  }

  /**
   * Create default agent system configuration
   */
  private createDefaultAgentSystemConfig(): AgentSystemConfig {
    return {
      agentSystemAlwaysEnabled: true,
      llmService: 'aliyun',
      configuredViaViewerConfig: true
    };
  }

  /**
   * Load persisted configuration from localStorage
   */
  private loadPersistedConfiguration(): void {
    try {
      const persistenceKey = 'hologram-voice-config';
      const storedData = localStorage.getItem(persistenceKey);

      if (storedData) {
        const configData = JSON.parse(storedData);

        // Validate configuration version and structure
        if (configData.version === '1.0' && configData.voiceConfig) {
          this.currentVoiceConfig = configData.voiceConfig;
          this.logger.info('✅ Voice configuration loaded from localStorage', {
            timestamp: new Date(configData.timestamp).toISOString(),
            roleName: configData.voiceConfig.roleName
          });
        } else {
          this.logger.warn('⚠️ Stored configuration version mismatch, using defaults');
        }
      }
    } catch (error) {
      this.logger.warn('⚠️ Error loading persisted configuration:', error);
      // Continue with defaults
    }
  }

  /**
   * Initialize configuration from options
   */
  initializeConfiguration(options: {
    enableAutomaticResponses?: boolean;
    responseContext?: string;
    timingProfile?: string;
    analysisInterval?: number;
    llmService?: string;
  } = {}): void {
    try {
      this.logger.info('🔧 Initializing avatar configuration...');

      // Update contextual configuration
      this.contextualConfig = {
        enableAutomaticResponses: options.enableAutomaticResponses !== false,
        responseContext: options.responseContext || 'friendly_assistant',
        timingProfile: options.timingProfile || 'balanced',
        analysisInterval: options.analysisInterval || 5000
      };

      // Update agent system configuration
      this.agentSystemConfig = {
        agentSystemAlwaysEnabled: true,
        llmService: options.llmService || 'aliyun',
        configuredViaViewerConfig: true
      };

      // Track configuration change
      this.recordConfigChange('initialization', {
        contextualConfig: this.contextualConfig,
        agentSystemConfig: this.agentSystemConfig
      });

      this.logger.info('✅ Avatar configuration initialized', {
        contextualConfig: this.contextualConfig,
        agentSystemConfig: this.agentSystemConfig
      });

    } catch (error) {
      this.logger.error('❌ Error initializing configuration:', error);
    }
  }

  /**
   * Set cloned voice configuration
   */
  async setClonedVoice(voiceData: ClonedVoiceData, isPermanent: boolean = false): Promise<boolean> {
    try {
      this.logger.info('🎤 Setting cloned voice configuration:', {
        roleName: voiceData.roleName,
        isPermanent,
        hasReferenceAudio: !!voiceData.referenceAudio
      });

      // Validate voice data
      if (this.options.configValidation) {
        const isValid = this.validateVoiceData(voiceData);
        if (!isValid) {
          throw new Error('Invalid voice data provided');
        }
      }

      // Store voice configuration
      this.currentVoiceConfig = { ...voiceData };
      this.currentClonedVoice = voiceData.roleName;
      this.isUsingClonedVoice = true;
      this.currentRole = voiceData.roleName;

      // Update voice config
      this.voiceConfig = {
        ...this.voiceConfig,
        currentGender: voiceData.settings?.gender || this.voiceConfig.currentGender,
        currentLanguage: voiceData.settings?.language || this.voiceConfig.currentLanguage
      };

      // Track configuration change
      this.recordConfigChange('cloned_voice', {
        voiceData: { ...voiceData, referenceAudio: '[HIDDEN]' }, // Hide audio data in logs
        isPermanent,
        previousVoice: this.currentClonedVoice
      });

      // Persist configuration if enabled and permanent
      if (this.options.enableConfigPersistence && isPermanent) {
        await this.persistVoiceConfiguration();
      }

      this.logger.info('✅ Cloned voice configuration set successfully');
      return true;

    } catch (error) {
      this.logger.error('❌ Error setting cloned voice:', error);
      return false;
    }
  }

  /**
   * Update voice configuration
   */
  updateVoiceConfig(updates: Partial<VoiceConfig>): void {
    try {
      const previousConfig = { ...this.voiceConfig };

      this.voiceConfig = {
        ...this.voiceConfig,
        ...updates
      };

      // Track configuration change
      this.recordConfigChange('voice_config', {
        updates,
        previousConfig
      });

      this.logger.info('🎵 Voice configuration updated', {
        updates,
        newConfig: this.voiceConfig
      });

    } catch (error) {
      this.logger.error('❌ Error updating voice configuration:', error);
    }
  }

  /**
   * Update contextual configuration
   */
  updateContextualConfig(updates: Partial<ContextualConfig>): void {
    try {
      const previousConfig = { ...this.contextualConfig };

      this.contextualConfig = {
        ...this.contextualConfig,
        ...updates
      };

      // Track configuration change
      this.recordConfigChange('contextual_config', {
        updates,
        previousConfig
      });

      this.logger.info('🎯 Contextual configuration updated', {
        updates,
        newConfig: this.contextualConfig
      });

    } catch (error) {
      this.logger.error('❌ Error updating contextual configuration:', error);
    }
  }

  /**
   * Update agent system configuration
   */
  updateAgentSystemConfig(updates: Partial<AgentSystemConfig>): void {
    try {
      const previousConfig = { ...this.agentSystemConfig };

      this.agentSystemConfig = {
        ...this.agentSystemConfig,
        ...updates
      };

      // Track configuration change
      this.recordConfigChange('agent_system_config', {
        updates,
        previousConfig
      });

      this.logger.info('🤖 Agent system configuration updated', {
        updates,
        newConfig: this.agentSystemConfig
      });

    } catch (error) {
      this.logger.error('❌ Error updating agent system configuration:', error);
    }
  }

  /**
   * Set agent ID for voice configuration association
   */
  setAgentId(agentId: string): void {
    this.agentId = agentId;
    this.logger.debug('🆔 Agent ID set for configuration association:', agentId);
  }

  /**
   * Validate voice data structure
   */
  private validateVoiceData(voiceData: ClonedVoiceData): boolean {
    try {
      // Check required fields
      if (!voiceData.roleName || typeof voiceData.roleName !== 'string') {
        this.logger.warn('⚠️ Invalid voice data: missing or invalid roleName');
        return false;
      }

      // Validate optional fields if present
      if (voiceData.referenceAudio && typeof voiceData.referenceAudio !== 'string') {
        this.logger.warn('⚠️ Invalid voice data: referenceAudio must be string');
        return false;
      }

      if (voiceData.voiceId && typeof voiceData.voiceId !== 'string') {
        this.logger.warn('⚠️ Invalid voice data: voiceId must be string');
        return false;
      }

      return true;

    } catch (error) {
      this.logger.warn('⚠️ Error validating voice data:', error);
      return false;
    }
  }

  /**
   * Record configuration change for audit trail
   */
  private recordConfigChange(configType: string, changes: Record<string, any>): void {
    const changeRecord = {
      timestamp: Date.now(),
      configType,
      changes
    };

    this.configChangeHistory.push(changeRecord);

    // Keep only last 100 changes to prevent memory issues
    if (this.configChangeHistory.length > 100) {
      this.configChangeHistory = this.configChangeHistory.slice(-100);
    }

    this.logger.debug('📝 Configuration change recorded', {
      configType,
      timestamp: changeRecord.timestamp
    });
  }

  /**
   * Persist voice configuration (placeholder for actual persistence)
   */
  private async persistVoiceConfiguration(): Promise<void> {
    try {
      // This would typically save to localStorage, database, or API
      this.logger.info('💾 Voice configuration persistence requested', {
        voiceConfigExists: !!this.currentVoiceConfig,
        roleName: this.currentVoiceConfig?.roleName
      });

      // Implement actual persistence mechanism using localStorage
      const persistenceKey = 'hologram-voice-config';
      const configData = {
        voiceConfig: this.currentVoiceConfig,
        timestamp: Date.now(),
        version: '1.0'
      };

      try {
        localStorage.setItem(persistenceKey, JSON.stringify(configData));
        this.logger.info('✅ Voice configuration persisted to localStorage');
      } catch (storageError) {
        this.logger.warn('⚠️ localStorage not available, using memory persistence only');
        // Fallback: keep in memory only
      }

    } catch (error) {
      this.logger.warn('⚠️ Error persisting voice configuration:', error);
    }
  }

  // Configuration getters
  getVoiceConfig(): VoiceConfig {
    return { ...this.voiceConfig };
  }

  getContextualConfig(): ContextualConfig {
    return { ...this.contextualConfig };
  }

  getAgentSystemConfig(): AgentSystemConfig {
    return { ...this.agentSystemConfig };
  }

  /**
   * Get Aliyun advanced features configuration
   */
  getAliyunAdvancedFeatures(): AliyunAdvancedFeatures | undefined {
    return this.agentSystemConfig.aliyunAdvancedFeatures ?
      { ...this.agentSystemConfig.aliyunAdvancedFeatures } : undefined;
  }

  /**
   * Update Aliyun advanced features configuration
   */
  updateAliyunAdvancedFeatures(updates: Partial<AliyunAdvancedFeatures>): void {
    try {
      const previousConfig = { ...this.agentSystemConfig.aliyunAdvancedFeatures };

      this.agentSystemConfig.aliyunAdvancedFeatures = {
        ...this.agentSystemConfig.aliyunAdvancedFeatures!,
        ...updates
      };

      // Track configuration change
      this.recordConfigChange('aliyun_advanced_features', {
        updates,
        previousConfig,
        newConfig: this.agentSystemConfig.aliyunAdvancedFeatures
      });

      this.logger.info('✅ Aliyun advanced features updated', {
        enabledFeatures: {
          structuredOutput: this.agentSystemConfig.aliyunAdvancedFeatures.enableStructuredOutput,
          deepThinking: this.agentSystemConfig.aliyunAdvancedFeatures.enableDeepThinking,
          streaming: this.agentSystemConfig.aliyunAdvancedFeatures.enableStreamingOutput,
          multiTurn: this.agentSystemConfig.aliyunAdvancedFeatures.enableMultiTurnDialogue
        }
      });

    } catch (error) {
      this.logger.error('❌ Error updating Aliyun advanced features:', error);
      throw error;
    }
  }

  /**
   * Check if specific Aliyun advanced feature is enabled
   */
  isAliyunFeatureEnabled(feature: keyof AliyunAdvancedFeatures): boolean {
    const config = this.agentSystemConfig.aliyunAdvancedFeatures;
    if (!config) return false;

    return config[feature] as boolean || false;
  }

  getCurrentVoiceConfig(): ClonedVoiceData | null {
    return this.currentVoiceConfig ? { ...this.currentVoiceConfig } : null;
  }

  isUsingClonedVoiceConfig(): boolean {
    return this.isUsingClonedVoice;
  }

  getCurrentClonedVoice(): string | null {
    return this.currentClonedVoice;
  }

  getCurrentRole(): string | null {
    return this.currentRole;
  }

  getAgentId(): string | null {
    return this.agentId;
  }

  getAllConfiguration(): {
    voice: VoiceConfig;
    contextual: ContextualConfig;
    agentSystem: AgentSystemConfig;
    clonedVoice: ClonedVoiceData | null;
    metadata: {
      isUsingClonedVoice: boolean;
      currentClonedVoice: string | null;
      currentRole: string | null;
      agentId: string | null;
    };
  } {
    return {
      voice: this.getVoiceConfig(),
      contextual: this.getContextualConfig(),
      agentSystem: this.getAgentSystemConfig(),
      clonedVoice: this.getCurrentVoiceConfig(),
      metadata: {
        isUsingClonedVoice: this.isUsingClonedVoice,
        currentClonedVoice: this.currentClonedVoice,
        currentRole: this.currentRole,
        agentId: this.agentId
      }
    };
  }

  getConfigChangeHistory(): Array<{
    timestamp: number;
    configType: string;
    changes: Record<string, any>;
  }> {
    return [...this.configChangeHistory];
  }

  /**
   * Clear cloned voice configuration
   */
  clearClonedVoice(): void {
    try {
      this.logger.info('🧹 Clearing cloned voice configuration');

      const previousConfig = this.getCurrentVoiceConfig();

      this.currentVoiceConfig = null;
      this.isUsingClonedVoice = false;
      this.currentClonedVoice = null;
      this.currentRole = null;

      // Reset voice config to defaults
      this.voiceConfig = {
        currentLanguage: 'auto',
        currentGender: 'male',
        useVoice: { lang: 'en-US', id: 'default' }
      };

      // Track configuration change
      this.recordConfigChange('clear_cloned_voice', {
        previousConfig
      });

      this.logger.info('✅ Cloned voice configuration cleared');

    } catch (error) {
      this.logger.error('❌ Error clearing cloned voice configuration:', error);
    }
  }

  /**
   * Reset all configuration to defaults
   */
  resetToDefaults(): void {
    try {
      this.logger.info('🔄 Resetting all configuration to defaults');

      // Clear cloned voice
      this.clearClonedVoice();

      // Reset contextual config
      this.contextualConfig = {
        enableAutomaticResponses: true,
        responseContext: 'friendly_assistant',
        timingProfile: 'balanced',
        analysisInterval: 5000
      };

      // Reset agent system config
      this.agentSystemConfig = {
        agentSystemAlwaysEnabled: true,
        llmService: 'aliyun',
        configuredViaViewerConfig: true
      };

      // Track configuration change
      this.recordConfigChange('reset_to_defaults', {
        timestamp: Date.now()
      });

      this.logger.info('✅ All configuration reset to defaults');

    } catch (error) {
      this.logger.error('❌ Error resetting configuration to defaults:', error);
    }
  }

  /**
   * Dispose of resources and cleanup
   */
  dispose(): void {
    try {
      this.logger.info('🧹 Disposing ConfigManager resources...');

      // Clear sensitive data
      this.currentVoiceConfig = null;
      this.currentClonedVoice = null;
      this.currentRole = null;
      this.agentId = null;

      // Clear change history
      this.configChangeHistory = [];

      // Reset flags
      this.isUsingClonedVoice = false;

      this.logger.info('✅ ConfigManager disposed successfully');

    } catch (error) {
      this.logger.error('❌ Error disposing ConfigManager:', error);
    }
  }
}

export default ConfigManager;