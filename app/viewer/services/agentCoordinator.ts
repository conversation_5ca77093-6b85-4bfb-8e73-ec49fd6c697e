/**
 * Agent Coordinator Service for Talking Avatar
 * 
 * Provides viewer-specific functionality that complements the universal core agent service:
 * - VAD (Voice Activity Detection) handler setup for UI integration
 * - Character personality management for avatar personas
 * - UI-specific agent coordination and readiness monitoring
 * 
 * This service follows the service-oriented architecture principles and provides
 * clean separation of UI concerns from the universal agent core.
 */

import { createLogger } from '../../../src/utils/logger';
import type { LangGraphAgentService } from '../../../src/agent/core.js';

export interface AgentServiceConfig {
  toolRegistrationConfig: any;
  toolOptions: any;
  modelProvider: string;
  aliyunApiKey: string;
  modelOptions: {
    defaultModel: string;
    enableRealtime: boolean;
    audioConfig: any;
  };
  agentConfig: {
    enableAutonomousTools: boolean;
    enableDualBrain: boolean;
    maxIterations: number;
    periodicAnalysisInterval?: number;
    decisionCooldown?: number;
  };
}

export interface AgentReadinessOptions {
  maxAttempts?: number;
  intervalMs?: number;
  timeoutMs?: number;
}

export interface CharacterContext {
  identity: {
    name: string;
    role: string;
    description: string;
    avatar: string;
  };
  personality: {
    traits: {
      formality: number;
      enthusiasm: number;
      empathy: number;
      creativity: number;
      directness: number;
    };
    description: string;
    guidelines: string[];
  };
  communication: {
    voiceStyle: string;
    formality: number;
    enthusiasm: number;
    empathy: number;
  };
  systemPrompt: string;
  enhancedPrompt: string;
  timestamp: number;
  version: string;
}

export interface AgentCoordinatorOptions {
  logger?: any;
  enableVADHandlers?: boolean;
  agentReadinessOptions?: AgentReadinessOptions;
}

/**
 * Agent Coordinator Service for Talking Avatar
 * Provides UI-specific coordination layer for the universal core agent service
 * - Eliminates redundant wrapper methods by using core.js directly
 * - Preserves VAD handling for UI integration
 * - Preserves character personality management for avatar personas
 */
export class AgentCoordinator {
  private logger: any;
  private options: AgentCoordinatorOptions;

  // Direct reference to core agent service (eliminates wrapper methods)
  private agentService: LangGraphAgentService | null = null;

  // UI-specific state management
  private vadHandlersSetup: boolean = false;
  private vadUpdateInterval: NodeJS.Timeout | null = null;
  private vadCleanupFunctions: Array<() => void> = [];

  // Character context management (UI-specific functionality)
  private currentCharacterContext: CharacterContext | null = null;

  // Readiness monitoring (UI-specific concerns)
  private readinessCheckInterval: NodeJS.Timeout | null = null;
  private lastReadinessCheck: number = 0;

  constructor(options: AgentCoordinatorOptions = {}) {
    this.logger = options.logger || createLogger('AgentCoordinator');
    this.options = options;

    this.logger.info('🤖 AgentCoordinator initialized for UI coordination', {
      enableVADHandlers: options.enableVADHandlers ?? true,
      readinessOptions: options.agentReadinessOptions
    });
  }

  /**
   * Initialize the core agent service directly (removes wrapper complexity)
   */
  async initializeAgentService(config: AgentServiceConfig): Promise<boolean> {
    try {
      this.logger.info('🚀 Initializing core LangGraph Agent Service directly...');

      // Import and create the core agent service directly
      const { LangGraphAgentService } = await import('../../../src/agent/core.js');

      this.agentService = new LangGraphAgentService({
        toolRegistrationConfig: config.toolRegistrationConfig,
        toolOptions: config.toolOptions,
        modelProvider: config.modelProvider,
        aliyunApiKey: config.aliyunApiKey,
        modelOptions: config.modelOptions,
        agentConfig: config.agentConfig
      });

      // Initialize the core service (it handles all the complex initialization)
      await this.agentService.initialize();

      this.logger.info('✅ Core LangGraph Agent Service initialized successfully');

      // Setup UI-specific VAD handlers if enabled
      if (this.options.enableVADHandlers) {
        this.setupVADHandlers();
      }

      return true;

    } catch (error) {
      this.logger.error('❌ Failed to initialize core agent service:', error);
      this.agentService = null;
      throw error;
    }
  }

  /**
   * Setup VAD (Voice Activity Detection) handlers for UI integration
   * This is UI-specific functionality that remains in the coordinator
   */
  setupVADHandlers(): void {
    try {
      if (!this.agentService) {
        this.logger.debug('❌ Core agent service not available for VAD setup');
        return;
      }

      if (this.vadHandlersSetup) {
        this.logger.debug('🎙️ VAD handlers already setup');
        return;
      }

      // VAD handling is now completely managed by the UI layer
      // Core service remains general-purpose and provider-agnostic
      
      // Setup UI-specific audio activity tracking for dual brain context
      this._setupAudioActivityTracking();

      this.vadHandlersSetup = true;
      this.logger.info('✅ UI-specific VAD handlers setup successfully');

    } catch (error) {
      this.logger.error('❌ Error setting up VAD handlers:', error);
    }
  }

  /**
   * Setup UI-specific audio activity tracking for dual brain context using System 1 VAD signals
   * This functionality remains in the coordinator as it's UI-specific integration
   * @private
   */
  private _setupAudioActivityTracking(): void {
    try {
      // Track audio activity state from System 1 VAD signals
      // Note: These variables are used within event handlers and may not be directly referenced in main function scope
      let currentAudioActivity = false;
      let currentVADContext: any = null;
      let speechStartTime: number | null = null;
      let vadEventHistory: Array<{ event: string, timestamp: number, data: any }> = [];

      // Get the dual brain coordinator if available from core service
      const dualBrainCoordinator = this.agentService?.getDualBrainCoordinator?.();

      if (!dualBrainCoordinator) {
        this.logger.debug('No dual brain coordinator available for audio activity tracking');
        return;
      }

      // Get System 1 model for VAD event subscription from core service
      const system1Model = this.agentService?.getModel('system1') || this.agentService?.getModel();

      if (!system1Model) {
        this.logger.warn('⚠️ No System 1 model available for VAD signal integration');
        return;
      }

      this.logger.info('🎙️ Setting up System 1 VAD signal integration for UI audio activity tracking');

      // Subscribe to VAD events from System 1 WebSocket model with enhanced signal mapping
      if (system1Model.on && typeof system1Model.on === 'function') {
        // Handle speech started events (multiple event types for broader compatibility)
        const speechStartedHandler = (vadData: any) => {
          this.logger.debug('🎙️ [VAD] Speech started signal from System 1', vadData);

          currentAudioActivity = true;
          speechStartTime = Date.now();
          currentVADContext = vadData;

          // Add to history
          vadEventHistory.push({
            event: 'speech_started',
            timestamp: speechStartTime,
            data: vadData
          });

          // Maintain history size
          if (vadEventHistory.length > 10) {
            vadEventHistory = vadEventHistory.slice(-10);
          }

          // Create enhanced environmental context for dual brain
          const environmentalContext = {
            audioActivity: true,
            vadSignal: 'speaking',
            speechQuality: vadData.audioQuality || 'good',
            speakerProximity: vadData.speakerProximity || 'close',
            interruptionRisk: 'high',
            speechStartTime,
            vadContext: vadData,
            environmentalIntelligence: {
              engagementLevel: vadData.engagementLevel || 'active',
              acousticEnvironment: vadData.acousticEnvironment || 'indoor',
              contextualComplexity: vadData.contextualComplexity || 'moderate'
            },
            timestamp: speechStartTime,
            source: 'ui_vad_integration'
          };

          // Update dual brain coordinator with rich VAD context
          dualBrainCoordinator.updateContext?.('environmental', environmentalContext);
          
          // Log activity state for debugging
          this.logger.debug(`VAD state: audioActivity=${currentAudioActivity}, hasContext=${!!currentVADContext}`);

          this.logger.debug('✅ [VAD] Environmental context updated for speech started');
        };

        // Register multiple event types for speech started (broader compatibility)
        system1Model.on('voiceActivityDetected', speechStartedHandler);
        system1Model.on('speech_started', speechStartedHandler);
        system1Model.on('speechStarted', speechStartedHandler);

        // Handle speech stopped events with enhanced signal mapping
        const speechStoppedHandler = (vadData: any) => {
          const speechDuration = speechStartTime ? Date.now() - speechStartTime : 0;

          this.logger.debug('🎙️ [VAD] Speech stopped signal from System 1', {
            duration: speechDuration,
            vadData
          });

          currentAudioActivity = false;
          currentVADContext = vadData;

          // Add to history
          vadEventHistory.push({
            event: 'speech_stopped',
            timestamp: Date.now(),
            data: { ...vadData, speechDuration }
          });

          // Maintain history size
          if (vadEventHistory.length > 10) {
            vadEventHistory = vadEventHistory.slice(-10);
          }

          // Create post-speech environmental context
          const postSpeechContext = {
            audioActivity: false,
            vadSignal: 'silent',
            speechDuration,
            speechQuality: vadData.speechQuality || 'good',
            readinessForResponse: vadData.readinessForResponse || { ready: true, level: 'high' },
            interruptionRisk: 'low',
            vadContext: vadData,
            conversationTransition: {
              fromSpeech: true,
              toProcessing: true,
              flowQuality: vadEventHistory.length > 2 ? 'established' : 'developing'
            },
            environmentalTransition: {
              transitionType: 'speech_to_silence',
              transitionSpeed: speechDuration < 1000 ? 'quick' : 'normal',
              readyForProcessing: true
            },
            dualBrainHandoff: {
              contextualComplexity: vadData.contextualComplexity || 'moderate',
              processingRecommendation: vadData.dualBrainContext?.systemTwoRecommendation || 'continue_system_one'
            },
            timestamp: Date.now(),
            source: 'ui_vad_integration'
          };

          // Update dual brain coordinator with post-speech context
          dualBrainCoordinator.updateContext?.('environmental', postSpeechContext);

          // Reset speech tracking
          speechStartTime = null;

          this.logger.debug('✅ [VAD] Environmental context updated for speech stopped');
        };

        // Register multiple event types for speech stopped (broader compatibility)
        system1Model.on('voiceActivityStopped', speechStoppedHandler);
        system1Model.on('speech_stopped', speechStoppedHandler);
        system1Model.on('speechStopped', speechStoppedHandler);

        // Handle VAD errors with enhanced error recovery
        const vadErrorHandler = (error: any) => {
          this.logger.warn('⚠️ [VAD] VAD error from System 1', error);

          // Provide fallback context on VAD errors
          const fallbackContext = {
            audioActivity: false,
            vadSignal: 'unknown',
            vadError: true,
            errorContext: error,
            interruptionRisk: 'low',
            timestamp: Date.now(),
            source: 'ui_vad_error_fallback'
          };

          dualBrainCoordinator.updateContext?.('environmental', fallbackContext);
        };

        // Register VAD error handlers
        system1Model.on('vadError', vadErrorHandler);
        system1Model.on('error', vadErrorHandler);

        this.logger.info('✅ [VAD] UI VAD event handlers registered with System 1 model', {
          eventTypes: ['voiceActivityDetected', 'speech_started', 'voiceActivityStopped', 'speech_stopped', 'vadError'],
          modelType: system1Model.constructor.name
        });

        // Store cleanup functions for proper disposal
        this.vadCleanupFunctions = [
          () => {
            // Unregister event listeners
            if (system1Model.off && typeof system1Model.off === 'function') {
              system1Model.off('voiceActivityDetected');
              system1Model.off('speech_started');
              system1Model.off('speechStarted');
              system1Model.off('voiceActivityStopped');
              system1Model.off('speech_stopped');
              system1Model.off('speechStopped');
              system1Model.off('vadError');
              system1Model.off('error');
            }
          }
        ];

      } else {
        this.logger.warn('⚠️ System 1 model does not support event subscription - VAD integration limited');
      }

    } catch (error) {
      this.logger.error('❌ Failed to setup UI-specific VAD integration:', error);
    }
  }

  /**
   * Update character personality context for the dual brain system
   * This is UI-specific functionality that remains in the coordinator
   */
  async updateCharacterPersonality(character: any): Promise<boolean> {
    try {
      this.logger.info('🎭 Updating character personality for dual brain system', {
        characterName: character.name,
        characterId: character.id
      });

      if (!this.agentService) {
        this.logger.warn('⚠️ Core agent service not available for character update');
        return false;
      }

      // Build character context structure
      const characterContext: CharacterContext = {
        identity: {
          name: character.name,
          role: character.id,
          description: character.description,
          avatar: character.avatar
        },
        personality: {
          traits: character.personality,
          description: character.description || 'Character personality profile',
          guidelines: this.generateBehaviorGuidelines(character)
        },
        communication: {
          voiceStyle: character.voiceStyle,
          formality: character.personality.formality,
          enthusiasm: character.personality.enthusiasm,
          empathy: character.personality.empathy
        },
        systemPrompt: character.systemPrompt,
        enhancedPrompt: this.enhanceSystemPrompt(character),
        timestamp: Date.now(),
        version: '1.0'
      };

      // Store current character context
      this.currentCharacterContext = characterContext;

      // Core service doesn't have updateCharacterContext - character context is handled via dual brain coordinator and models

      // Apply to dual brain coordinator if available from core service
      const dualBrainCoordinator = this.agentService.getDualBrainCoordinator?.();
      if (dualBrainCoordinator) {
        await this.applyCharacterToDualBrain(dualBrainCoordinator, characterContext);
      }

      // Update model system prompts
      await this.updateModelSystemPrompts(characterContext);

      this.logger.info('✅ Character personality applied successfully to dual brain system');
      return true;

    } catch (error) {
      this.logger.error('❌ Failed to update character personality:', error);
      return false;
    }
  }

  /**
   * Apply character context to dual brain coordinator
   * UI-specific character integration logic
   */
  private async applyCharacterToDualBrain(coordinator: any, context: CharacterContext): Promise<void> {
    try {
      this.logger.debug('🧠 Applying character context to dual brain coordinator');

      // System1 context (reactive responses)
      const system1Context = {
        personality: context.personality,
        communication: context.communication,
        responseStyle: {
          speed: context.personality.traits.enthusiasm > 0.7 ? 'quick' : 'thoughtful',
          tone: context.personality.traits.formality > 0.7 ? 'professional' : 'conversational',
          energy: context.personality.traits.enthusiasm,
          warmth: context.personality.traits.empathy,
          creativity: context.personality.traits.creativity
        }
      };

      // System2 context (analytical oversight)
      const system2Context = {
        guidelines: context.personality.guidelines,
        consistencyRules: [
          'Maintain personality trait alignment in all responses',
          'Ensure voice style consistency throughout conversation',
          'Verify emotional tone matches character empathy level',
          'Check formality level against character settings',
          'Validate creative expression aligns with character creativity',
          'Ensure directness level matches character preferences'
        ],
        characterMonitoring: {
          traits: context.personality.traits,
          expectedBehavior: context.personality.description,
          voiceStyle: context.communication.voiceStyle
        }
      };

      // Apply contexts if coordinator supports these methods
      if (coordinator.updateSystem1Context) {
        await coordinator.updateSystem1Context(system1Context);
        this.logger.debug('✅ System1 context updated with character personality');
      }

      if (coordinator.updateSystem2Context) {
        await coordinator.updateSystem2Context(system2Context);
        this.logger.debug('✅ System2 context updated with character consistency rules');
      }

      // Update coordination parameters based on character
      if (coordinator.updateCoordinationParameters) {
        const coordinationParams = {
          responseSpeed: context.personality.traits.enthusiasm > 0.7 ? 'fast' : 'measured',
          analysisDepth: context.personality.traits.creativity > 0.7 ? 'creative' : 'focused',
          consistencyWeight: context.personality.traits.empathy > 0.7 ? 0.8 : 0.7,
          adaptabilityFactor: this.calculateAdaptabilityScore(context.personality.traits)
        };

        await coordinator.updateCoordinationParameters(coordinationParams);
        this.logger.debug('✅ Coordination parameters updated for character consistency');
      }

    } catch (error) {
      this.logger.error('❌ Failed to apply character context to dual brain:', error);
    }
  }

  /**
   * Update model system prompts with character context
   */
  private async updateModelSystemPrompts(context: CharacterContext): Promise<void> {
    try {
      this.logger.debug('📝 Updating model system prompts with character context');

      // Update primary model through core service
      const primaryModel = this.agentService?.getModel();
      if (primaryModel && primaryModel.updateSystemPrompt) {
        await primaryModel.updateSystemPrompt(context.enhancedPrompt);
        this.logger.debug('✅ Primary model system prompt updated');
      }

      // Update System1 model if available through core service
      const system1Model = this.agentService?.getModel('system1');
      if (system1Model && system1Model.updateSystemPrompt) {
        const system1Prompt = this.createSystem1Prompt(context);
        await system1Model.updateSystemPrompt(system1Prompt);
        this.logger.debug('✅ System1 model prompt updated');
      }

      // Update System2 model if available through core service
      const system2Model = this.agentService?.getModel('system2');
      if (system2Model && system2Model.updateSystemPrompt) {
        const system2Prompt = this.createSystem2Prompt(context);
        await system2Model.updateSystemPrompt(system2Prompt);
        this.logger.debug('✅ System2 model prompt updated');
      }

    } catch (error) {
      this.logger.error('❌ Failed to update model system prompts:', error);
    }
  }

  /**
   * Generate behavior guidelines for character consistency
   */
  private generateBehaviorGuidelines(character: any): string[] {
    const guidelines: string[] = [];
    const p = character.personality;

    // Formality guidelines
    if (p.formality > 0.7) {
      guidelines.push('Use formal language and professional tone');
      guidelines.push('Address users with respect and courtesy');
    } else if (p.formality < 0.3) {
      guidelines.push('Use casual, friendly language');
      guidelines.push('Be conversational and approachable');
    }

    // Enthusiasm guidelines
    if (p.enthusiasm > 0.7) {
      guidelines.push('Show excitement and energy in responses');
      guidelines.push('Use positive, uplifting language');
    } else if (p.enthusiasm < 0.3) {
      guidelines.push('Maintain a calm, steady tone');
      guidelines.push('Focus on clear, measured responses');
    }

    // Empathy guidelines
    if (p.empathy > 0.7) {
      guidelines.push('Show understanding and compassion');
      guidelines.push('Acknowledge emotions and feelings');
    } else if (p.empathy < 0.3) {
      guidelines.push('Focus on facts and logical analysis');
      guidelines.push('Maintain objective perspective');
    }

    // Creativity guidelines
    if (p.creativity > 0.7) {
      guidelines.push('Offer creative solutions and ideas');
      guidelines.push('Think outside conventional approaches');
    } else if (p.creativity < 0.3) {
      guidelines.push('Provide practical, proven solutions');
      guidelines.push('Focus on established methods');
    }

    // Directness guidelines
    if (p.directness > 0.7) {
      guidelines.push('Give clear, straightforward answers');
      guidelines.push('Be honest and direct in communication');
    } else if (p.directness < 0.3) {
      guidelines.push('Use diplomatic language');
      guidelines.push('Consider feelings when delivering information');
    }

    return guidelines;
  }

  /**
   * Enhance system prompt with character context
   */
  private enhanceSystemPrompt(character: any): string {
    const basePrompt = character.systemPrompt;
    const personalityDesc = character.description || 'A unique character with distinctive personality traits';
    const guidelines = this.generateBehaviorGuidelines(character);

    return `${basePrompt}

CHARACTER PERSONALITY:
You embody the personality of "${character.name}" - ${character.description}. Your personality is ${personalityDesc}.

BEHAVIOR GUIDELINES:
${guidelines.map((guideline: string) => `- ${guideline}`).join('\n')}

CONSISTENCY REQUIREMENTS:
- Maintain character consistency throughout the conversation
- Adapt responses to match your personality traits
- Consider the voice style: ${character.voiceStyle}
- Balance authenticity with helpfulness

Remember to stay true to this character while being helpful and accurate.`;
  }

  /**
   * Create System1 (reactive) prompt with character context
   */
  private createSystem1Prompt(context: CharacterContext): string {
    return `You are the reactive system (System 1) for ${context.identity.name}.

IMMEDIATE RESPONSE CHARACTERISTICS:
- Personality: ${context.personality.description}
- Voice Style: ${context.communication.voiceStyle}
- Formality Level: ${context.personality.traits.formality}
- Enthusiasm Level: ${context.personality.traits.enthusiasm}
- Empathy Level: ${context.personality.traits.empathy}

REACTIVE GUIDELINES:
- Provide immediate, personality-consistent responses
- Match the emotional tone and energy level expected for this character
- Maintain character voice and style in all interactions
- React naturally to user input while staying true to personality traits

Your responses should feel authentic and immediate while staying true to the character.`;
  }

  /**
   * Create System2 (analytical) prompt with character context
   */
  private createSystem2Prompt(context: CharacterContext): string {
    return `You are the analytical system (System 2) for ${context.identity.name}.

ANALYTICAL RESPONSIBILITIES:
- Ensure response consistency with character personality
- Verify factual accuracy and helpfulness
- Monitor for character coherence and trait alignment
- Provide contextual analysis for personality consistency

CONSISTENCY RULES:
${context.personality.guidelines.map((rule: string) => `- ${rule}`).join('\n')}

CHARACTER MONITORING:
- Expected Traits: ${JSON.stringify(context.personality.traits, null, 2)}
- Voice Style: ${context.communication.voiceStyle}
- Personality Description: ${context.personality.description}

ANALYSIS FOCUS:
- Character consistency: Does the response match the configured personality traits?
- Contextual appropriateness: Is the response suitable for the character and situation?
- Quality assurance: Is the information accurate and helpful?
- Trait alignment: Do the tone and approach match the personality settings?

Balance analytical oversight with maintaining natural character expression.`;
  }

  /**
   * Calculate adaptability score based on personality balance
   */
  private calculateAdaptabilityScore(traits: any): number {
    // Higher adaptability for balanced personalities
    const values = Object.values(traits) as number[];
    const balance = 1 - Math.abs(0.5 - values.reduce((sum, val) => sum + val, 0) / values.length);
    const creativity = traits.creativity;
    const empathy = traits.empathy;

    return Math.min(1, (balance + creativity + empathy) / 3);
  }

  // Direct delegation methods to core service (eliminates wrapper complexity)
  
  getAgentService(): LangGraphAgentService | null {
    return this.agentService;
  }

  isAgentInitialized(): boolean {
    return !!this.agentService && this.agentService['_initialized'];
  }

  areVADHandlersSetup(): boolean {
    return this.vadHandlersSetup;
  }

  getCurrentCharacterContext(): CharacterContext | null {
    return this.currentCharacterContext ? { ...this.currentCharacterContext } : null;
  }

  getAgentServiceStatus(): {
    initialized: boolean;
    hasService: boolean;
    vadHandlersSetup: boolean;
    hasDualBrainCoordinator: boolean;
    lastReadinessCheck: number;
    hasCharacterContext: boolean;
  } {
    return {
      initialized: this.isAgentInitialized(),
      hasService: !!this.agentService,
      vadHandlersSetup: this.vadHandlersSetup,
      hasDualBrainCoordinator: !!this.agentService?.getDualBrainCoordinator?.(),
      lastReadinessCheck: this.lastReadinessCheck,
      hasCharacterContext: !!this.currentCharacterContext
    };
  }

  /**
   * Start readiness monitoring for agent service
   * Provides periodic health checks and readiness status
   */
  startReadinessMonitoring(options?: AgentReadinessOptions): void {
    try {
      const config = {
        maxAttempts: options?.maxAttempts ?? this.options.agentReadinessOptions?.maxAttempts ?? 10,
        intervalMs: options?.intervalMs ?? this.options.agentReadinessOptions?.intervalMs ?? 2000,
        timeoutMs: options?.timeoutMs ?? this.options.agentReadinessOptions?.timeoutMs ?? 30000
      };

      this.logger.info('🏃 Starting agent readiness monitoring', config);

      // Clear any existing monitoring
      if (this.readinessCheckInterval) {
        clearInterval(this.readinessCheckInterval);
      }

      let attempts = 0;
      const startTime = Date.now();

      this.readinessCheckInterval = setInterval(() => {
        try {
          const now = Date.now();
          
          // Check timeout
          if (now - startTime > config.timeoutMs) {
            this.logger.warn('⚠️ Readiness monitoring timeout reached');
            this.stopReadinessMonitoring();
            return;
          }

          // Check max attempts
          if (attempts >= config.maxAttempts) {
            this.logger.warn('⚠️ Max readiness check attempts reached');
            this.stopReadinessMonitoring();
            return;
          }

          attempts++;
          this.lastReadinessCheck = now;

          // Perform readiness check
          const status = this.getAgentServiceStatus();
          
          if (status.initialized && status.hasService) {
            this.logger.debug(`✅ Agent service ready (attempt ${attempts}/${config.maxAttempts})`);
            
            // Optionally stop monitoring after successful readiness
            if (attempts >= 3) { // Give it a few successful checks
              this.logger.info('✅ Agent service consistently ready, reducing monitoring frequency');
              
              // Clear current interval and set up less frequent monitoring
              if (this.readinessCheckInterval) {
                clearInterval(this.readinessCheckInterval);
              }
              this.readinessCheckInterval = setInterval(() => {
                this.lastReadinessCheck = Date.now();
                const currentStatus = this.getAgentServiceStatus();
                
                if (!currentStatus.initialized || !currentStatus.hasService) {
                  this.logger.warn('⚠️ Agent service lost readiness, restarting frequent monitoring');
                  this.startReadinessMonitoring(options); // Restart frequent monitoring
                }
              }, config.intervalMs * 5); // Check 5x less frequently
            }
          } else {
            this.logger.debug(`🔄 Agent service not ready (attempt ${attempts}/${config.maxAttempts})`, status);
          }

        } catch (error) {
          this.logger.error('❌ Error in readiness check:', error);
        }
      }, config.intervalMs);

      this.logger.info('✅ Agent readiness monitoring started');

    } catch (error) {
      this.logger.error('❌ Failed to start readiness monitoring:', error);
    }
  }

  /**
   * Stop readiness monitoring
   */
  stopReadinessMonitoring(): void {
    if (this.readinessCheckInterval) {
      clearInterval(this.readinessCheckInterval);
      this.readinessCheckInterval = null;
      this.logger.info('🛑 Agent readiness monitoring stopped');
    }
  }

  /**
   * Dispose of UI-specific resources and cleanup
   */
  async dispose(): Promise<void> {
    try {
      this.logger.info('🧹 Disposing AgentCoordinator UI resources...');

      // Stop readiness monitoring
      this.stopReadinessMonitoring();

      // Stop VAD interval updates
      if (this.vadUpdateInterval) {
        clearInterval(this.vadUpdateInterval);
        this.vadUpdateInterval = null;
      }

      // Run VAD cleanup functions
      this.vadCleanupFunctions.forEach(cleanup => {
        try {
          cleanup();
        } catch (error) {
          this.logger.warn('⚠️ Error during VAD cleanup:', error);
        }
      });
      this.vadCleanupFunctions = [];

      // Dispose core agent service
      if (this.agentService && typeof this.agentService.dispose === 'function') {
        await this.agentService.dispose();
      }

      // Reset state
      this.agentService = null;
      this.vadHandlersSetup = false;
      this.currentCharacterContext = null;
      this.lastReadinessCheck = 0;

      this.logger.info('✅ AgentCoordinator UI resources disposed successfully');

    } catch (error) {
      this.logger.error('❌ Error disposing AgentCoordinator:', error);
    }
  }
}

export default AgentCoordinator;