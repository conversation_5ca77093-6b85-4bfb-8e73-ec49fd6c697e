/**
 * Talking Avatar Services Index
 * 
 * Centralized exports for all avatar-specific services moved from 
 * src/agent/services to app/viewer/services with cleaned up naming.
 * 
 * These services are specific to the Talking Avatar application and provide:
 * - Audio processing and streaming
 * - Media coordination and video capture
 * - State management and transitions
 * - Agent coordination and initialization
 * - Configuration management
 * - Resource cleanup and disposal
 */

// Audio Processing
export { AudioProcessor } from './audioProcessor.js';
export type {
  AudioProcessingConfig,
  AudioConnectionDiagnostics,
  AudioProcessorOptions
} from './audioProcessor.js';

// Media Coordination
export { MediaCoordinator } from './mediaCoordinator.js';
export type {
  MediaConfig,
  MediaCoordinatorOptions,
  VideoFrameCaptureState
} from './mediaCoordinator.js';

// State Coordination
export { StateCoordinator } from './stateCoordinator.js';
export type {
  UIState,
  BargeInOptions,
  AudioCaptureConfig,
  StateCoordinatorOptions
} from './stateCoordinator.js';

// Agent Coordination
export { AgentCoordinator } from './agentCoordinator.js';
export type {
  AgentServiceConfig,
  AgentReadinessOptions,
  ProactiveSpeakingDecision,
  AgentCoordinatorOptions
} from './agentCoordinator.js';

// DualBrain Coordination Types
export type {
  DualBrainCoordinatorInstance,
  DualBrainSystems,
  DualBrainCoordinatorOptions
} from './types.js';
export { isDualBrainCoordinator } from './types.js';

// Configuration Management
export { ConfigManager } from './configManager.js';
export type {
  VoiceConfig,
  ContextualConfig,
  AgentSystemConfig,
  ClonedVoiceData,
  ConfigManagerOptions
} from './configManager.js';

// Resource Management
export { ResourceManager } from './resourceManager.js';
export type {
  ResourceState,
  CleanupOptions,
  ResourceManagerOptions
} from './resourceManager.js';

// Camera Permission Management
export { CameraPermissionManager, cameraPermissionManager } from './cameraPermissionManager.js';
export type {
  CameraPermissionState,
  CameraPermissionOptions,
  PermissionRequestResult
} from './cameraPermissionManager.js';

// Default exports for convenience
export { AudioProcessor as default } from './audioProcessor.js';