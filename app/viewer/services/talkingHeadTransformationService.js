/**
 * TalkingHead Transformation Service
 * 
 * Handles the transformation of avatar meshes to talking head mode using the 
 * established animation system from git history 7035c983d677fdf9f6b65097f80fd001cda90915
 * 
 * This service follows the original transformation pattern while maintaining 
 * the service-oriented architecture.
 */

import { createLogger } from '@/utils/logger';
import { TalkingHeadUI } from '@/modules/talkinghead/src/ui.js';
import { AnimatorFactory } from '@/animation/animatorFactory.js';
import { ASSETS } from '../viewerConfig.js';
import { ALIYUN_AUDIO_CONFIG } from '@/agent/models/aliyun/AliyunConfig.js';

export class TalkingHeadTransformationService {
    constructor(options = {}) {
        this.logger = createLogger('TalkingHeadTransformationService');
        this.options = options;

        // Track transformation state
        this.isTransformed = false;
        this.currentMesh = null;
        this.originalMeshId = null;
        this.animator = null;
        this.talkingHead = null;
        this.ui = null;
        this.talkingHeadControls = null;

        this.logger.info('🎭 TalkingHead Transformation Service initialized');
    }

    /**
     * Transform avatar mesh to talking head mode
     * Uses the original transformation logic from git history
     * 
     * @param {Object} inputMesh - The 3D mesh to transform
     * @param {Object} viewer - Viewer instance for UI feedback
     * @param {Object} avatar - Avatar context object
     * @returns {Promise<boolean>} Success status
     */
    async transformToTalkingHead(inputMesh = null, viewer = null, avatar = null) {
        try {
            this.logger.info('🎭 Starting TalkingHead transformation using original method...');

            // Check if already in TalkingHead mode
            if (this.isTransformed) {
                this.logger.debug('Already in TalkingHead mode');
                return true;
            }

            // Determine mesh to use
            const meshToUse = inputMesh || this.currentMesh;

            if (!meshToUse) {
                const errorMsg = "No avatar mesh loaded. Please create an avatar first.";
                this.logger.error('❌ ' + errorMsg);
                if (viewer && viewer.showError) {
                    viewer.showError(errorMsg);
                }
                return false;
            }

            // Show loading indicator
            if (viewer && viewer.uiSettings) {
                viewer.uiSettings.showProgress();
                viewer.uiSettings.updateStatus('Transforming to TalkingHead...');
            }

            // Store the original mesh ID/reference for later comparison
            this.originalMeshId = meshToUse.uuid;
            this.currentMesh = meshToUse;
            this.isTransformed = true;

            // Check orbital controls availability
            if (viewer && !viewer.controls) {
                this.logger.warn('⚠️ Orbital controls not available, cannot transform to TalkingHead with zooming or rotation');
            }

            // Configure TalkingHead options with adaptive camera settings
            const transformOptions = {
                lipsyncModules: ["en", "fi", "zh"], // Add Chinese lip-sync
                avatarMood: "neutral",
                cameraView: 'full',
                cameraRotateX: 0,
                useExistRenderer: true
            };

            this.logger.debug('🔧 Using transform options:', transformOptions);

            // Create the appropriate animator based on mesh capabilities
            this.logger.info('🎨 Creating animator for mesh');

            // Configure animator options
            const animatorOptions = {
                debug: true,
                headMovementIntensity: 0.7,
                handGestureIntensity: 0.8,
                idleAnimationIntensity: 0.4,
                speakingAnimationIntensity: 0.7
            };

            // Create animator instance using factory
            this.animator = AnimatorFactory.createAnimator(meshToUse, animatorOptions);

            // Initialize the animator (this also starts the animation loop)
            this.animator.initialize();

            // Store the animator reference in the mesh's userData for animation coordination
            if (meshToUse && meshToUse.userData) {
                this.logger.debug('📝 Storing baseAnimator reference in mesh userData');
                meshToUse.userData.baseAnimator = this.animator;

                // Also store it in the armature if available
                if (this.animator.armature && this.animator.armature.userData) {
                    this.logger.debug('📝 Storing baseAnimator reference in armature userData');
                    this.animator.armature.userData.baseAnimator = this.animator;
                    this.animator.armature.userData.isDoll = true; // Mark as doll mesh for position preservation
                }
            }

            // Make sure the animation loop is running
            if (!this.animator.isRunning) {
                this.logger.info('🔄 Animation loop not started by initialize, starting manually');
                this.animator.startAnimationLoop();
            }

            // Use the animator instance directly (no deprecated getAnimatorInterface)
            this.talkingHead = this.animator;

            // Log the type of animator created
            this.logger.info(`✅ Created ${this.animator.constructor.name} for mesh`);

            // Handle avatar configuration
            await this._configureAvatar(meshToUse, avatar);

            // Debug the bone structure
            await this._logSkeletonStructure(this.talkingHead.armature);

            // Initialize UI and connect controls
            this.logger.info('🎨 Initializing UI components...');
            this.ui = new TalkingHeadUI(this);
            this.talkingHeadControls = await this.ui.createControls(this.talkingHead);

            // Update status and hide progress
            if (viewer && viewer.uiSettings) {
                viewer.uiSettings.updateStatus('TalkingHead transformation complete!');
                viewer.uiSettings.hideProgress();
            }

            // Update UI status
            if (this.ui && typeof this.ui.updateStatus === 'function') {
                this.ui.updateStatus('Ready');
            }

            // Update mesh selector in viewer if available
            if (viewer && viewer.meshSelector &&
                typeof viewer.meshSelector.updateAfterTransformation === 'function') {
                viewer.meshSelector.updateAfterTransformation();
            }

            // Add for debugging in console (browser environment only)
            if (typeof window !== 'undefined' && window && this.talkingHead) {
                window.th = this.talkingHead;
            }

            this.logger.info('✅ TalkingHead transformation completed successfully');

            // Start dual brain systems now that TalkingHead is ready
            await this._startDualBrainSystems(viewer);

            // Start media capture now that TalkingHead is ready
            await this._startMediaCaptureForTalkingHead(viewer);

            return true;

        } catch (error) {
            this.logger.error('❌ Failed to transform to TalkingHead:', error);

            // Clean up on failure
            this.isTransformed = false;
            this.originalMeshId = null;
            this.currentMesh = null;

            // Hide progress and show error
            if (viewer) {
                if (viewer.uiSettings) {
                    viewer.uiSettings.hideProgress();
                }
                if (viewer.showError) {
                    viewer.showError(`Failed to transform: ${error.message}`);
                }
            }

            return false;
        }
    }

    /**
     * Configure avatar settings
     * @param {Object} meshToUse - The mesh being transformed
     * @param {Object} avatar - Avatar context object
     * @private
     */
    async _configureAvatar(meshToUse, avatar) {
        try {
            // Generate a stable filename based on unique identifiers
            const filename = meshToUse.userData.fileName || '';
            const baseFilename = filename.replace(/\.[^/.]+$/, ''); // Remove file extension
            const meshFilePath = `${ASSETS.SAVE_OPTIONS.meshPath}/${baseFilename}.glb`;

            this.logger.debug('📁 Avatar file paths:', { filename, baseFilename, meshFilePath });

            // Set the avatar name property for use in voice cloning
            const avatarContext = avatar || {};
            avatarContext.name = baseFilename;
            this.logger.info(`🎭 Set avatar name for voice cloning: ${avatarContext.name}`);

            // Set agentId for memory (avatar name + hash)
            try {
                const { generateCacheKey } = await import('@/utils/cache.js');
                this.agentId = await generateCacheKey('voice', avatarContext.name, null);
                this.logger.info('🔑 Set agentId for memory:', this.agentId);
            } catch (e) {
                this.logger.warn('⚠️ Failed to set agentId for memory:', e);
            }

            // Voice configuration using Aliyun defaults
            const gender = meshToUse.userData.gender;
            const currentVoice = {
                lang: 'en-US',
                id: ALIYUN_AUDIO_CONFIG.defaultVoice || 'default'
            };

            // Avatar configuration
            const showAvatarConfig = {
                body: gender === 'female' ? 'F' : 'M',
                avatarMood: "neutral"
            };

            this.logger.info('📋 Avatar configuration completed', {
                filename: baseFilename,
                gender,
                voice: currentVoice,
                config: showAvatarConfig
            });

        } catch (error) {
            this.logger.error('❌ Error configuring avatar:', error);
            throw error;
        }
    }

    /**
     * Log the skeleton structure of a 3D model to help with debugging
     * @param {Object3D} object - The 3D object to analyze
     * @private
     */
    async _logSkeletonStructure(object) {
        if (!object) return;

        try {
            this.logger.debug('🦴 Logging skeleton structure');
            // Use the debugBones utility function from AnimationUtils with proper import
            const { debugBones } = await import('@/animation/AnimationUtils.js');
            const bones = debugBones(object);
            if (bones.length > 0) {
                this.logger.info(`🦴 Found ${bones.length} bones in skeleton`);
            }
        } catch (error) {
            this.logger.warn('⚠️ Could not debug skeleton structure:', error);
        }
    }

    /**
     * Check if currently transformed
     * @returns {boolean} Transformation status
     */
    isInTalkingHeadMode() {
        return this.isTransformed;
    }

    /**
     * Get the current talking head instance
     * @returns {Object|null} TalkingHead instance
     */
    getTalkingHead() {
        return this.talkingHead;
    }

    /**
     * Get the current animator instance
     * @returns {Object|null} Animator instance
     */
    getAnimator() {
        return this.animator;
    }

    /**
     * Get the UI controls
     * @returns {Object|null} UI controls
     */
    getUIControls() {
        return this.talkingHeadControls;
    }

    /**
     * Update status through UI
     * @param {string} status - Status message
     */
    updateStatus(status) {
        if (this.ui && typeof this.ui.updateStatus === 'function') {
            this.ui.updateStatus(status);
        }
    }

    /**
     * Dispose TalkingHead transformation resources
     */
    async dispose() {
        try {
            this.logger.info('🧹 Disposing TalkingHead transformation resources...');

            // Stop the animation loop if we have direct access to the animator
            if (this.animator) {
                try {
                    this.animator.stopAnimationLoop();
                    this.logger.info('🔄 Animation loop stopped');

                    // Call dispose method if available
                    if (typeof this.animator.dispose === 'function') {
                        this.animator.dispose();
                    }
                } catch (error) {
                    this.logger.error('❌ Error stopping animation loop:', error);
                }
                this.animator = null;
            }

            // Clean up talkingHead reference
            this.talkingHead = null;

            // Remove UI elements using the UI class
            if (this.ui) {
                if (typeof this.ui.dispose === 'function') {
                    this.ui.dispose();
                }
                this.ui = null;
            }
            this.talkingHeadControls = null;

            // Remove container if it exists
            if (typeof document !== 'undefined') {
                const container = document.getElementById('talking-head-container');
                if (container && container.parentNode) {
                    container.parentNode.removeChild(container);
                }
            }

            // Reset state
            this.isTransformed = false;
            this.currentMesh = null;
            this.originalMeshId = null;

            this.logger.info('✅ TalkingHead transformation resources disposed successfully');

        } catch (error) {
            this.logger.error('❌ Error disposing TalkingHead transformation resources:', error);
        }
    }

    /**
     * Start dual brain systems after TalkingHead transformation is complete
     * @private
     */
    async _startDualBrainSystems(viewer) {
        try {
            this.logger.info('🧠 Starting dual brain systems after TalkingHead transformation...');

            // Check system status directly
            const talkingAvatar = viewer?.talkingAvatar;
            const agentCoordinator = talkingAvatar?.agentCoordinator;
            const agentService = agentCoordinator?.getAgentService();

            // Check if we have the necessary services
            if (!agentCoordinator) {
                this.logger.error('❌ No agent coordinator available - cannot start dual brain systems');
                return;
            }

            if (!agentService) {
                this.logger.warn('⚠️ Agent service not available, waiting 2 seconds and retrying...');
                
                // Wait for agent service to be ready
                await this._waitForAgentService(viewer, 5000);
                
                const retryAgentService = viewer?.talkingAvatar?.agentCoordinator?.getAgentService();
                if (!retryAgentService) {
                    this.logger.error('❌ Agent service still not available after waiting');
                    return;
                }
            }

            // Use core agentService interface to start dual brain systems
            const result = await agentService.startDualBrainSystems();

            if (result) {
                this.logger.info('✅ Dual brain systems started successfully after TalkingHead transformation');
                this.logger.info('🎯 Dual brain should now make proactive API calls every 3 seconds');
            } else {
                this.logger.warn('⚠️ Dual brain systems failed to start');
            }

        } catch (error) {
            this.logger.error('❌ Error starting dual brain systems:', error);
        }
    }

    /**
     * Start media capture for TalkingHead mode
     * @private
     */
    async _startMediaCaptureForTalkingHead(viewer) {
        try {
            this.logger.info('📹 Starting media capture for TalkingHead mode...');

            const mediaCoordinator = viewer?.talkingAvatar?.mediaCoordinator;
            const inputCoordinator = mediaCoordinator?.getInputCoordinator?.();

            if (!inputCoordinator) {
                this.logger.warn('⚠️ No input coordinator available for media capture');
                return;
            }

            // Check if there's a startMediaCaptureForTalkingHead method
            if (typeof inputCoordinator.startMediaCaptureForTalkingHead === 'function') {
                const started = await inputCoordinator.startMediaCaptureForTalkingHead();
                
                if (started) {
                    this.logger.info('✅ Media capture started successfully for TalkingHead mode');
                } else {
                    this.logger.warn('⚠️ Failed to start media capture for TalkingHead mode');
                }
            } else {
                this.logger.warn('⚠️ startMediaCaptureForTalkingHead method not available');
            }

        } catch (error) {
            this.logger.error('❌ Error starting media capture for TalkingHead mode:', error);
        }
    }

    /**
     * Wait for agent service to be available
     * @private
     */
    async _waitForAgentService(viewer, timeoutMs = 5000) {
        const startTime = Date.now();
        const checkInterval = 500; // Check every 500ms

        while (Date.now() - startTime < timeoutMs) {
            const agentService = viewer?.talkingAvatar?.agentCoordinator?.getAgentService();
            if (agentService && agentService.getDualBrainCoordinator) {
                this.logger.info('✅ Agent service became available');
                return true;
            }

            // Wait before next check
            await new Promise(resolve => setTimeout(resolve, checkInterval));
        }

        this.logger.warn(`⚠️ Agent service did not become available within ${timeoutMs}ms`);
        return false;
    }


    /**
     * Open Ready Player Me avatar creation interface
     * This method is kept for compatibility but delegates to ResourceManager
     */
    openReadyPlayerMe() {
        this.logger.info('🎭 Opening Ready Player Me (delegated to ResourceManager)');
        // This would be handled by the ResourceManager in the main architecture
        // Kept here for interface compatibility
    }
}

export default TalkingHeadTransformationService;