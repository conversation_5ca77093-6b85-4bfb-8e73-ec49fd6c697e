/**
 * TypeScript interfaces for DualBrainCoordinator integration
 * Provides type safety for the external coordinator pattern
 */

export interface DualBrainCoordinatorOptions {
  enableRealTimeProcessing?: boolean;
  enableProactiveDecisions?: boolean;
  enablePeriodicAnalysis?: boolean;
  system1ResponseTimeout?: number;
  system2AnalysisTimeout?: number;
  communicationBatchSize?: number;
  periodicAnalysisInterval?: number;
  decisionCooldown?: number;
  proactiveAnalysisInterval?: number;
}

export interface DualBrainSystems {
  system1: any; // LLM instance for real-time responses
  system2: any; // LLM instance for deep reasoning
}

export interface DualBrainCoordinatorInstance {
  // Core lifecycle methods
  initialize(systems: DualBrainSystems): Promise<void>;
  shutdown(): Promise<void>;

  // State management
  isActive: boolean;
  getMetrics(): any;

  // Context management
  contextBridge?: {
    updateContext(type: string, contextData: any): void;
  };

  // Proactive speaking handler
  handleProactiveSpeaking(decision: any): Promise<void>;
}

export interface DualBrainFactory {
  createDualBrainCoordinator(
    systems: DualBrainSystems,
    options?: DualBrainCoordinatorOptions
  ): Promise<DualBrainCoordinatorInstance>;
}

// Type guard for DualBrainCoordinator validation
export function isDualBrainCoordinator(obj: any): obj is DualBrainCoordinatorInstance {
  return obj &&
    typeof obj.initialize === 'function' &&
    typeof obj.shutdown === 'function' &&
    typeof obj.handleProactiveSpeaking === 'function';
}