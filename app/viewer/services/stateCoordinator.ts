/**
 * State Coordinator Service for Talking Avatar
 * 
 * Extracts state management functionality from TalkingAvatar to provide:
 * - State transition management (idle, listening, processing, speaking)
 * - Barge-in interruption handling
 * - Audio capture lifecycle management
 * - Automatic response controls
 * 
 * This service follows the service-oriented architecture principles and provides
 * clean separation of state management concerns from UI logic.
 */

import { createLogger } from '../../../src/utils/logger';

export interface UIState {
  currentState: 'idle' | 'listening' | 'processing' | 'speaking' | 'error';
  isListeningActive: boolean;
  isVideoActive: boolean;
  activationSource: string | null;
}

export interface BargeInOptions {
  reason?: 'user_interrupt' | 'voice_activity' | 'manual';
  clearBuffer?: boolean;
  timestamp?: number;
  source?: string;
}

export type AudioOutputFormat = 'pcm16' | 'float32' | 'base64' | 'wav';

export interface AudioCaptureConfig {
  sampleRate: number;
  channelCount: number;
  echoCancellation: boolean;
  noiseSuppression: boolean;
  audioOutputFormat: AudioOutputFormat;
  minIntervalMs: number;
}

export interface StateCoordinatorOptions {
  logger?: any;
  enableAutoResponse?: boolean;
  realtimeSessionTimeoutMs?: number;
}

/**
 * State Coordinator Service for Talking Avatar
 * Handles all state management and transitions for the avatar system
 */
export class StateCoordinator {
  private logger: any;
  private options: StateCoordinatorOptions;

  // UI state management
  private uiState: UIState = {
    currentState: 'idle',
    isListeningActive: false,
    isVideoActive: false,
    activationSource: null
  };

  // Media capture manager
  private mediaCaptureManager: any = null;

  // State transition tracking
  private lastStateChange: number = 0;
  private lastBargeInTime: number = 0;

  // Audio capture configuration
  private audioCaptureConfig: AudioCaptureConfig = {
    sampleRate: 16000,
    channelCount: 1,
    echoCancellation: true,
    noiseSuppression: true,
    audioOutputFormat: 'pcm16' as AudioOutputFormat, // Aliyun expects PCM16
    minIntervalMs: 200 // Send audio every 200ms
  };

  constructor(options: StateCoordinatorOptions = {}) {
    this.logger = options.logger || createLogger('StateCoordinator');
    this.options = options;

    this.logger.info('🎯 StateCoordinator initialized', {
      enableAutoResponse: options.enableAutoResponse ?? true,
      realtimeSessionTimeoutMs: options.realtimeSessionTimeoutMs ?? 10000
    });
  }

  /**
   * Return to idle state using coordination services
   */
  async returnToIdle(sessionId: string = 'default'): Promise<void> {
    try {
      this.logger.debug('[StateChange] Transitioning to idle state', { sessionId });

      // Update UI state
      this.uiState.currentState = 'idle';
      this.uiState.isListeningActive = false;
      this.uiState.activationSource = null;

      // Update state change timestamp
      this.lastStateChange = Date.now();

      this.logger.debug('[StateChange] State: → idle', {
        sessionId,
        timestamp: this.lastStateChange
      });

    } catch (error) {
      this.logger.error('❌ Error returning to idle state:', error);
      this.uiState.currentState = 'error';
    }
  }

  /**
   * Start listening - controls audio input capture
   */
  async startListening(sessionId: string = 'default', agentService: any): Promise<void> {
    try {
      this.logger.info('🎙️ Starting audio input capture...');

      // Wait for realtime session to be ready
      if (agentService?.model?.constructor?.name === 'AliyunBailianChatModel') {
        this.logger.info('⏳ Waiting for Aliyun realtime session to stabilize...');

        const sessionReady = await this.waitForRealtimeSessionReady(
          agentService,
          this.options.realtimeSessionTimeoutMs || 10000
        );

        if (!sessionReady) {
          throw new Error('Realtime session failed to stabilize within timeout');
        }

        this.logger.info('✅ Realtime session ready - proceeding with audio capture');
      }

      // Initialize MediaCaptureManager if not already available
      await this.initializeMediaCaptureManager(agentService);

      // Connect MediaCaptureManager to WebSocket audio streaming
      if (agentService && this.mediaCaptureManager) {
        const connectionSuccess = await agentService.connectMediaCapture(this.mediaCaptureManager);
        if (!connectionSuccess) {
          this.logger.error('❌ Failed to connect MediaCaptureManager to WebSocket audio streaming');
          // Still proceed with audio capture but without WebSocket streaming
          this.logger.warn('⚠️ Proceeding with local audio capture only - no real-time streaming');
        } else {
          this.logger.info('✅ MediaCaptureManager connected to WebSocket audio streaming');
        }
      }

      // Start audio capture with proper configuration
      await this.startAudioCapture();

      // Update UI state
      this.uiState.currentState = 'listening';
      this.uiState.isListeningActive = true;
      this.uiState.activationSource = 'manual';
      this.lastStateChange = Date.now();

      this.logger.info('✅ Audio capture started successfully', {
        sessionId,
        timestamp: this.lastStateChange
      });

    } catch (error) {
      this.logger.error('❌ Error starting listening:', error);
      this.uiState.currentState = 'error';
      throw error;
    }
  }

  /**
   * Stop listening and audio capture
   */
  async stopListening(sessionId: string = 'default'): Promise<void> {
    try {
      this.logger.info('🔇 Stopping audio input capture...');

      // Stop audio capture
      if (this.mediaCaptureManager) {
        await this.mediaCaptureManager.stopAudioCapture();
      }

      // Update UI state
      this.uiState.currentState = 'idle';
      this.uiState.isListeningActive = false;
      this.uiState.activationSource = null;
      this.lastStateChange = Date.now();

      this.logger.info('✅ Audio capture stopped successfully', {
        sessionId,
        timestamp: this.lastStateChange
      });

    } catch (error) {
      this.logger.error('❌ Error stopping listening:', error);
      this.uiState.currentState = 'error';
    }
  }

  /**
   * Handle barge-in interruption
   */
  async handleBargeIn(input: any = null, options: BargeInOptions = {}, stateManager: any): Promise<void> {
    try {
      const { reason = 'user_interrupt', timestamp = Date.now(), source = 'barge_in' } = options;

      this.logger.info('🛑 Triggering barge-in workflow execution', {
        hasInput: !!input,
        reason,
        currentState: this.uiState.currentState,
        timeSinceLastBargeIn: timestamp - this.lastBargeInTime
      });

      // Prevent rapid successive barge-ins
      if (timestamp - this.lastBargeInTime < 500) {
        this.logger.debug('⚠️ Ignoring rapid barge-in request');
        return;
      }

      // Trigger unified workflow execution with VAD metadata
      if (stateManager && stateManager.setState && stateManager.states) {
        await stateManager.setState(stateManager.states.LISTENING, {
          sessionId: this.getSessionId(),
          reason,
          vadTriggered: reason === 'voice_activity',
          timestamp,
          input,
          source
        });
      }

      // Track last barge-in time
      this.lastBargeInTime = timestamp;
      this.lastStateChange = timestamp;

      this.logger.debug('✅ Barge-in workflow execution completed', {
        reason,
        timestamp
      });

    } catch (error) {
      this.logger.error('❌ Failed to handle barge-in:', error);

      // Fallback: ensure we stop speaking at minimum
      try {
        this.uiState.currentState = 'idle';
        this.uiState.isListeningActive = false;
      } catch (fallbackError) {
        this.logger.error('❌ Fallback barge-in handling also failed:', fallbackError);
      }
    }
  }

  /**
   * Initialize MediaCaptureManager with audio processing configuration
   */
  private async initializeMediaCaptureManager(agentService: any): Promise<void> {
    if (this.mediaCaptureManager) {
      this.logger.debug('📱 MediaCaptureManager already initialized');
      return;
    }

    try {
      const { MediaCaptureManager } = await import('../../../src/media/capture/MediaCaptureManager.js');
      const { createAudioProcessingConfig } = await import('../../../src/media/modality/audio.js');

      this.mediaCaptureManager = new MediaCaptureManager({
        audio: {
          sampleRate: this.audioCaptureConfig.sampleRate,
          channelCount: this.audioCaptureConfig.channelCount,
          echoCancellation: this.audioCaptureConfig.echoCancellation,
          noiseSuppression: this.audioCaptureConfig.noiseSuppression
        },
        audioOutputFormat: this.audioCaptureConfig.audioOutputFormat,
        minIntervalMs: this.audioCaptureConfig.minIntervalMs,
        onAudioData: createAudioProcessingConfig({
          webSocketSender: agentService?.model?.sendRealtimeAudio?.bind(agentService.model),
          contextUpdater: agentService?.updateDualBrainContext?.bind(agentService),
          logger: this.logger
        }).onAudioData,
        onCaptureStart: () => {
          this.logger.debug('🎙️ Audio capture started');
          this.uiState.isListeningActive = true;
        },
        onCaptureStop: () => {
          this.logger.debug('🔇 Audio capture stopped');
          this.uiState.isListeningActive = false;
        },
        onCaptureError: (error: Error) => {
          this.logger.error('❌ Audio capture error:', error);
          this.uiState.currentState = 'error';
        }
      });

      this.logger.info('✅ MediaCaptureManager initialized successfully');

    } catch (error) {
      this.logger.error('❌ Failed to initialize MediaCaptureManager:', error);
      throw error;
    }
  }

  /**
   * Start audio capture with the initialized manager
   */
  private async startAudioCapture(): Promise<void> {
    if (!this.mediaCaptureManager) {
      throw new Error('MediaCaptureManager not initialized');
    }

    try {
      // ✅ FIX: Explicitly pass userActivated=true since this is only called through user-activated listening
      await this.mediaCaptureManager.startCapture('audio', true);
      this.logger.debug('🎙️ Audio capture started successfully with user activation');
    } catch (error) {
      this.logger.error('❌ Failed to start audio capture:', error);
      throw error;
    }
  }

  /**
   * Wait for realtime session to be ready
   */
  private async waitForRealtimeSessionReady(agentService: any, timeoutMs: number): Promise<boolean> {
    const startTime = Date.now();
    const checkInterval = 100;

    while (Date.now() - startTime < timeoutMs) {
      try {
        if (agentService?.model?.realtimeSessionStabilized) {
          return true;
        }

        if (agentService?.model?.realtimeSocket?.readyState === 1) {
          await new Promise(resolve => setTimeout(resolve, 200));
          return true;
        }

        await new Promise(resolve => setTimeout(resolve, checkInterval));

      } catch (error) {
        this.logger.warn('⚠️ Error checking realtime session readiness:', error);
        await new Promise(resolve => setTimeout(resolve, checkInterval));
      }
    }

    this.logger.warn('⚠️ Realtime session failed to stabilize within timeout', {
      timeoutMs,
      elapsedMs: Date.now() - startTime
    });

    return false;
  }

  // State getters and utilities
  getUIState(): UIState {
    return { ...this.uiState };
  }

  updateUIState(updates: Partial<UIState>): void {
    this.uiState = { ...this.uiState, ...updates };
    this.lastStateChange = Date.now();

    this.logger.debug('[StateChange] UI state updated', {
      updates,
      newState: this.uiState,
      timestamp: this.lastStateChange
    });
  }

  isListening(): boolean {
    return this.uiState.isListeningActive && this.uiState.currentState === 'listening';
  }

  isSpeaking(): boolean {
    return this.uiState.currentState === 'speaking';
  }

  isProcessing(): boolean {
    return this.uiState.currentState === 'processing';
  }

  isInError(): boolean {
    return this.uiState.currentState === 'error';
  }

  getCurrentState(): string {
    return this.uiState.currentState;
  }

  updateAudioCaptureConfig(config: Partial<AudioCaptureConfig>): void {
    this.audioCaptureConfig = { ...this.audioCaptureConfig, ...config };
    this.logger.info('🔧 Audio capture configuration updated', this.audioCaptureConfig);
  }

  getAudioCaptureConfig(): AudioCaptureConfig {
    return { ...this.audioCaptureConfig };
  }

  getStateStatistics(): {
    currentState: string;
    lastStateChange: number;
    lastBargeInTime: number;
    isListeningActive: boolean;
    timeSinceLastStateChange: number;
    timeSinceLastBargeIn: number;
  } {
    const now = Date.now();
    return {
      currentState: this.uiState.currentState,
      lastStateChange: this.lastStateChange,
      lastBargeInTime: this.lastBargeInTime,
      isListeningActive: this.uiState.isListeningActive,
      timeSinceLastStateChange: now - this.lastStateChange,
      timeSinceLastBargeIn: now - this.lastBargeInTime
    };
  }

  private getSessionId(): string {
    return `session_${Date.now()}`;
  }

  getMediaCaptureManager(): any {
    return this.mediaCaptureManager;
  }

  /**
   * Dispose of resources and cleanup
   */
  async dispose(): Promise<void> {
    try {
      this.logger.info('🧹 Disposing StateCoordinator resources...');

      // Stop audio capture
      if (this.mediaCaptureManager) {
        await this.mediaCaptureManager.stopAudioCapture();
        this.mediaCaptureManager = null;
      }

      // Reset UI state
      this.uiState = {
        currentState: 'idle',
        isListeningActive: false,
        isVideoActive: false,
        activationSource: null
      };

      // Reset timestamps
      this.lastStateChange = 0;
      this.lastBargeInTime = 0;

      this.logger.info('✅ StateCoordinator disposed successfully');

    } catch (error) {
      this.logger.error('❌ Error disposing StateCoordinator:', error);
    }
  }
}

export default StateCoordinator;