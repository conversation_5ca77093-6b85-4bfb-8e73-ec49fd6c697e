/**
 * Media Coordinator Service for Talking Avatar
 * 
 * PHASE 2 CONSOLIDATION: Streamlined media coordination that delegates
 * streaming functionality to core StreamingManager while maintaining
 * UI-specific media state and permissions management.
 * 
 * Focuses on:
 * - UI-specific media state management
 * - Media permissions and user consent
 * - Video frame capture for multimodal input
 * - Integration bridge to core StreamingManager
 * 
 * Delegates to StreamingManager:
 * - All streaming functionality
 * - Performance optimization
 * - LangGraph integration
 */

import { createLogger } from '../../../src/utils/logger';
import type { InputCoordination } from '../../../src/media/modality/inputCoordination.ts';
import { mediaPermissionManager } from '../../../src/media/MediaPermissionManager.js';
import { MediaStateManager, MediaTypeKey, type MediaState } from '../../../src/media/state/MediaStateManager.js';
import { MediaCaptureManager } from '../../../src/media/capture/MediaCaptureManager.js';
// NEW: Import core StreamingManager for delegation
import type { StreamingManager } from '../../../src/agent/streaming/StreamingManager.js';

export interface MediaConfig {
  video: {
    frameRate: number;
    maxFrameSize: number;
    compressionQuality: number;
  };
  audio: {
    sampleRate: number;
    bufferSize: number;
    enableAutoFlush: boolean;
  };
}

export interface MediaCoordinatorOptions {
  logger?: any;
  enableVideoCapture?: boolean;
  enableInputCoordination?: boolean;
  mediaConfig?: Partial<MediaConfig>;
  requireUserConsent?: boolean; // Require explicit user consent before audio/video initialization
  autoStartAudioCapture?: boolean; // Whether to automatically start audio capture on initialization
}

export interface VideoFrameCaptureState {
  isCapturing: boolean;
  frameCount: number;
  lastCaptureTime: number;
  captureInterval: NodeJS.Timeout | null;
}

/**
 * Media Coordinator Service for Talking Avatar
 * Handles all media-related coordination for the avatar system
 */
export class MediaCoordinator {
  private logger: any;
  private options: MediaCoordinatorOptions;
  private mediaConfig: MediaConfig;
  private initialized: boolean = false;

  // Unified state management via MediaStateManager
  private mediaStateManager: MediaStateManager;
  private mediaCaptureManager: MediaCaptureManager;

  // PHASE 2: Delegate streaming to core StreamingManager
  private streamingManager: StreamingManager | null = null;

  // 🔥 CRITICAL FIX: Add DualBrainCoordinator reference for integration
  private dualBrainCoordinator: any | null = null;

  // Video frame capture state (UI-specific)
  private videoFrameCaptureState: VideoFrameCaptureState = {
    isCapturing: false,
    frameCount: 0,
    lastCaptureTime: 0,
    captureInterval: null
  };

  // Current video frames for multimodal input
  private currentVideoFrames: string[] = [];

  // Input coordination manager
  private inputCoordinator: InputCoordination | null = null;

  constructor(options: MediaCoordinatorOptions = {}) {
    this.logger = options.logger || createLogger('MediaCoordinator');
    this.options = {
      requireUserConsent: true, // Default to requiring consent
      autoStartAudioCapture: false, // Default to NOT auto-starting audio
      ...options
    };

    // Initialize media configuration with defaults
    this.mediaConfig = {
      video: {
        frameRate: 2, // Conservative for real-time processing
        maxFrameSize: 500 * 1024, // 500KB limit
        compressionQuality: 0.8
      },
      audio: {
        sampleRate: 16000, // Aliyun compatible
        bufferSize: 20,
        enableAutoFlush: true
      },
      ...options.mediaConfig
    };

    // Initialize MediaStateManager for unified state management
    this.mediaStateManager = new MediaStateManager({
      logger: this.logger,
      sessionTimeoutMs: 30000
    });

    // Initialize MediaCaptureManager for device operations
    this.mediaCaptureManager = new MediaCaptureManager({
      audio: this.mediaConfig.audio,
      video: this.mediaConfig.video,
      onCaptureStart: () => this.handleCaptureStart(),
      onCaptureStop: () => this.handleCaptureStop(),
      onCaptureError: (error) => this.handleCaptureError(error)
    });

    // Set up state change callbacks
    this.mediaStateManager.onStateChanged((mediaType, state) => {
      this.logger.info(`📊 Media state changed: ${mediaType}`, state);
    });

    this.mediaStateManager.onActivationErrorOccurred((mediaType, error) => {
      this.logger.error(`❌ Media activation error: ${mediaType}`, error);
    });

    this.logger.info('🎬 MediaCoordinator initialized with unified state management', {
      enableVideoCapture: options.enableVideoCapture ?? true,
      enableInputCoordination: options.enableInputCoordination ?? true,
      requireUserConsent: this.options.requireUserConsent,
      autoStartAudioCapture: this.options.autoStartAudioCapture,
      mediaConfig: this.mediaConfig
    });
  }

  /**
   * PHASE 2: Set StreamingManager for delegation
   * Called by agentService to connect core streaming functionality
   */
  setStreamingManager(streamingManager: StreamingManager): void {
    this.streamingManager = streamingManager;
    this.logger.info('✅ StreamingManager connected - delegating streaming operations to core service');
  }

  /**
   * 🔥 CRITICAL FIX: Set DualBrainCoordinator for audio/video integration
   * Called by agentService to enable proper audio input routing
   */
  setDualBrainCoordinator(dualBrainCoordinator: any): void {
    this.dualBrainCoordinator = dualBrainCoordinator;
    this.logger.info('✅ DualBrainCoordinator connected - enabling audio input integration');
  }

  /**
   * Initialize MediaCoordinator and MediaStateManager
   */
  async initialize(): Promise<boolean> {
    try {
      this.logger.info('🚀 Initializing MediaCoordinator with streamlined design...');

      // Initialize MediaStateManager with MediaCaptureManager
      const initialized = await this.mediaStateManager.initialize(this.mediaCaptureManager);

      if (initialized) {
        this.logger.info('✅ MediaCoordinator initialized successfully - streaming delegation ready');
        this.initialized = true;
        return true;
      } else {
        this.logger.error('❌ Failed to initialize MediaStateManager');
        this.initialized = false;
        return false;
      }
    } catch (error) {
      this.logger.error('❌ Failed to initialize MediaCoordinator:', error);
      this.initialized = false;
      return false;
    }
  }

  /**
   * Start audio input with unified state management
   */
  async startAudioInput(userActivated: boolean = true): Promise<boolean> {
    try {
      this.logger.info('🎤 Starting audio input via unified state management...');
      // Ensure coordinator is initialized to prevent capture manager errors
      if (!this.initialized) {
        this.logger.info('ℹ️ MediaCoordinator not initialized; initializing now before starting audio');
        const ok = await this.initialize();
        if (!ok) {
          this.logger.error('❌ Unable to initialize MediaCoordinator for audio start');
          return false;
        }
      }

      // Use MediaStateManager for state-aware audio activation
      const success = await this.mediaStateManager.setMediaState('audio', true, userActivated);

      if (success) {
        this.logger.info('✅ Audio input started successfully');
        
        // 🔥 CRITICAL FIX: Properly integrate with DualBrainCoordinator for audio activation
        if (this.dualBrainCoordinator && typeof this.dualBrainCoordinator.recordUserInteraction === 'function') {
          try {
            this.dualBrainCoordinator.recordUserInteraction('audio');
            this.logger.info('✅ [INTEGRATION-FIX] DualBrainCoordinator notified of audio activation');
          } catch (error) {
            this.logger.error('❌ [INTEGRATION-ERROR] Failed to notify DualBrainCoordinator:', error);
          }
        } else {
          this.logger.debug('ℹ️ [INTEGRATION-INFO] DualBrainCoordinator not available - proceeding without coordination');
        }
      } else {
        this.logger.warn('❌ Failed to start audio input');
      }

      return success;
    } catch (error) {
      this.logger.error('❌ Error starting audio input:', error);
      return false;
    }
  }

  /**
   * Stop audio input with unified state management
   */
  async stopAudioInput(): Promise<boolean> {
    try {
      this.logger.info('🛑 Stopping audio input via unified state management...');

      // Use MediaStateManager for state-aware audio deactivation
      const success = await this.mediaStateManager.setMediaState('audio', false, false);

      if (success) {
        this.logger.info('✅ Audio input stopped successfully');
      } else {
        this.logger.warn('❌ Failed to stop audio input');
      }

      return success;
    } catch (error) {
      this.logger.error('❌ Error stopping audio input:', error);
      return false;
    }
  }

  /**
   * Handle capture start event
   */
  private handleCaptureStart(): void {
    this.logger.info('📹 Media capture started');
    
    // 🔥 CRITICAL FIX: Properly integrate with DualBrainCoordinator for media capture
    if (this.dualBrainCoordinator && typeof this.dualBrainCoordinator.recordUserInteraction === 'function') {
      try {
        this.dualBrainCoordinator.recordUserInteraction('media_capture');
        this.logger.info('✅ [INTEGRATION-FIX] DualBrainCoordinator notified of media capture start');
      } catch (error) {
        this.logger.error('❌ [INTEGRATION-ERROR] Failed to notify DualBrainCoordinator:', error);
      }
    } else {
      this.logger.debug('ℹ️ [INTEGRATION-INFO] DualBrainCoordinator not available - proceeding without coordination');
    }
  }

  /**
   * Handle capture stop event
   */
  private handleCaptureStop(): void {
    this.logger.info('🛑 Media capture stopped');
  }

  /**
   * Handle capture error event
   */
  private handleCaptureError(error: Error): void {
    this.logger.error('❌ Media capture error:', error);
  }
  async initializeInputCoordination(agentService: any, requireUserConsent: boolean = true): Promise<boolean> {
    try {
      if (!this.options.enableInputCoordination) {
        this.logger.info('📋 Input coordination disabled in options');
        return false;
      }

      // 🔥 CRITICAL FIX: Check user consent before initializing audio subsystem
      if (requireUserConsent && !await this.hasUserConsentForAudio()) {
        this.logger.info('📋 Audio input coordination requires user consent - deferring initialization');
        this.logger.info('ℹ️ Call initializeInputCoordination() with requireUserConsent=false after obtaining consent');
        return false;
      }

      // 🔥 NEW: Automatically connect to StreamingManager from agent service
      if (agentService && typeof agentService.connectToStreamingManager === 'function') {
        this.logger.info('🔗 Attempting to connect to StreamingManager from agent service...');
        const connected = await agentService.connectToStreamingManager(this, 'setStreamingManager');
        if (connected) {
          this.logger.info('✅ Successfully connected to StreamingManager from agent service');
        } else {
          this.logger.warn('⚠️ Failed to connect to StreamingManager from agent service');
        }
      } else if (agentService && typeof agentService.getStreamingManager === 'function') {
        // Fallback: try direct getter method
        const streamingManager = agentService.getStreamingManager();
        if (streamingManager) {
          this.setStreamingManager(streamingManager);
          this.logger.info('✅ Connected to StreamingManager via direct getter');
        } else {
          this.logger.warn('⚠️ StreamingManager not available from agent service getter');
        }
      }

      // PHASE 2: Check StreamingManager connection status
      if (this.streamingManager) {
        this.logger.info('✅ Using StreamingManager for optimized connection handling');
      } else {
        this.logger.warn('⚠️ StreamingManager not available - proceeding with basic coordination');
      }

      this.logger.info('🎯 Initializing InputCoordination with user consent and streaming connection...');

      // Import InputCoordination
      const { InputCoordination } = await import('../../../src/media/modality/index.js');

      // Create InputCoordination instance
      this.inputCoordinator = new InputCoordination({
        bufferSize: this.mediaConfig.audio.bufferSize,
        qualitySettings: {
          audioSampleRate: this.mediaConfig.audio.sampleRate,
          videoFrameRate: this.mediaConfig.video.frameRate,
          maxVideoFrameSize: this.mediaConfig.video.maxFrameSize,
          enableAutoFlush: this.mediaConfig.audio.enableAutoFlush
        }
      });

      // 🔥 CRITICAL FIX: Only start input coordination AFTER user consent validation
      // Check if user has granted audio permission before starting
      const hasPermission = await mediaPermissionManager.checkPermissionStatus('microphone');
      if (!hasPermission.granted) {
        this.logger.warn('⚠️ Audio permission not granted - InputCoordination created but NOT started');
        this.logger.info('📋 Call startAudioInputWithConsent() to request permission and start coordination');
        // Don't start - wait for explicit user action
        return false;
      }

      // Start input coordination only with permission
      this.inputCoordinator.start();

      // Connect to agent service via direct MediaCaptureManager integration
      const connected = await agentService.connectInputCoordinator(this.inputCoordinator);

      if (connected) {
        this.logger.info('✅ InputCoordination connected to agent service');
        return true;
      } else {
        this.logger.warn('⚠️ Failed to connect InputCoordination to agent service');
        return false;
      }

    } catch (error) {
      this.logger.error('❌ Failed to initialize InputCoordination:', error);
      return false;
    }
  }

  /**
   * Set up video frame capture for multimodal input
   * Uses centralized camera permission manager to ensure proper access
   */
  async setupVideoFrameCapture(cameraManager: any): Promise<void> {
    try {
      if (!this.options.enableVideoCapture) {
        this.logger.info('📹 Video capture disabled in options');
        return;
      }

      // Check camera permission status first
      const permissionStatus = await mediaPermissionManager.checkPermissionStatus('camera');
      if (!permissionStatus.granted) {
        this.logger.warn('📷 Camera permission not granted, requesting access...');

        // Request camera access through centralized manager
        const permissionResult = await mediaPermissionManager.requestMediaAccess(
          'MediaCoordinator-VideoFrameCapture',
          {
            video: this.mediaConfig.video,
            mediaType: 'camera',
            requestReason: 'This application needs camera access for video frame capture and multimodal input processing.'
          }
        );

        if (!permissionResult.success) {
          this.logger.warn('❌ Camera permission denied for video frame capture:', permissionResult.error);
          return;
        }

        this.logger.info('✅ Camera permission granted for video frame capture');
      }

      // Import Aliyun configuration for video settings
      const { createMediaConfig } = await import('../../../src/agent/models/aliyun/AliyunConfig.js');
      const aliyunMediaConfig = createMediaConfig();

      // Clear any existing frame capture interval
      if (this.videoFrameCaptureState.captureInterval) {
        clearInterval(this.videoFrameCaptureState.captureInterval);
        this.videoFrameCaptureState.captureInterval = null;
      }

      // Validate camera manager
      if (!cameraManager) {
        this.logger.warn('❌ No camera manager provided for video frame capture');
        return;
      }

      // Check if camera is active (with centralized permission manager)
      if (!cameraManager.isCameraActive() && !mediaPermissionManager.isMediaActive()) {
        this.logger.warn('📷 Camera is not active, attempting to activate...');

        // Try to get the active stream from permission manager
        const activeStream = mediaPermissionManager.getCurrentStream();
        if (activeStream) {
          this.logger.info('🔄 Using active stream from permission manager');
        } else {
          this.logger.warn('⚠️ No active camera stream available, frame capture may not work properly');
          return;
        }
      }

      this.logger.info('🎬 Setting up video frame capture...');

      // Use Aliyun recommended frame rate if available, otherwise use our default
      const frameRateMs = aliyunMediaConfig?.video?.frameRate
        ? 1000 / aliyunMediaConfig.video.frameRate
        : 1000 / this.mediaConfig.video.frameRate;

      // Capture frames periodically for multimodal input
      this.videoFrameCaptureState.captureInterval = setInterval(async () => {
        await this.captureVideoFrames(cameraManager);
      }, frameRateMs);

      this.videoFrameCaptureState.isCapturing = true;
      this.logger.info('✅ Video frame capture setup complete', {
        frameRateMs,
        expectedFps: 1000 / frameRateMs
      });

    } catch (error) {
      this.logger.error('❌ Error setting up video frame capture:', error);
    }
  }

  /**
   * Capture video frames from camera manager
   */
  private async captureVideoFrames(cameraManager: any): Promise<void> {
    try {
      this.logger.debug('🎬 Frame capture interval triggered', {
        isVideoStreaming: this.mediaStateManager.isActive('video'),
        cameraActive: cameraManager.isCameraActive(),
        cameraManagerExists: !!cameraManager
      });

      if (this.mediaStateManager.isActive('video') && cameraManager.isCameraActive()) {
        this.logger.debug('📸 Attempting to capture frames...');
        const frames = await cameraManager.captureCurrentFrames(3); // Capture 3 frames

        if (frames && frames.length > 0) {
          this.currentVideoFrames = frames;
          this.videoFrameCaptureState.frameCount += frames.length;
          this.videoFrameCaptureState.lastCaptureTime = Date.now();

          this.logger.debug('✅ Captured video frames for multimodal input', {
            frameCount: frames.length,
            frameSizes: frames.map((frame: string) => frame.length),
            totalCapturedFrames: this.videoFrameCaptureState.frameCount
          });
        } else {
          this.logger.warn('⚠️ Frame capture returned empty or invalid frames:', frames);
        }
      } else {
        this.logger.debug('⏸️ Skipping frame capture - conditions not met');
      }
    } catch (error) {
      this.logger.warn('❌ Error capturing video frames:', error);
    }
  }

  /**
   * Start video streaming with unified state management
   * @param userActivated Whether video streaming was explicitly activated by user
   */
  async startVideoStreaming(userActivated: boolean = true): Promise<boolean> {
    try {
      this.logger.info('🎬 Starting video streaming via unified state management...');
      // Ensure coordinator is initialized to prevent capture manager errors
      if (!this.initialized) {
        this.logger.info('ℹ️ MediaCoordinator not initialized; initializing now before starting video');
        const ok = await this.initialize();
        if (!ok) {
          this.logger.error('❌ Unable to initialize MediaCoordinator for video start');
          return false;
        }
      }

      // Use MediaStateManager for state-aware video activation
      const success = await this.mediaStateManager.setMediaState('video', true, userActivated);

      if (success) {
        this.logger.info('✅ Video streaming started successfully');
        
        // 🔥 CRITICAL FIX: Properly integrate with DualBrainCoordinator for video activation
        if (this.dualBrainCoordinator && typeof this.dualBrainCoordinator.recordUserInteraction === 'function') {
          try {
            this.dualBrainCoordinator.recordUserInteraction('video');
            this.logger.info('✅ [INTEGRATION-FIX] DualBrainCoordinator notified of video activation');
          } catch (error) {
            this.logger.error('❌ [INTEGRATION-ERROR] Failed to notify DualBrainCoordinator:', error);
          }
        } else {
          this.logger.debug('ℹ️ [INTEGRATION-INFO] DualBrainCoordinator not available - proceeding without coordination');
        }
      } else {
        this.logger.warn('❌ Failed to start video streaming');
      }

      return success;
    } catch (error) {
      this.logger.error('❌ Error starting video streaming:', error);
      return false;
    }
  }

  /**
   * Stop video streaming with unified state management
   */
  async stopVideoStreaming(): Promise<void> {
    try {
      this.logger.info('🛑 Stopping video streaming via unified state management...');

      // Use MediaStateManager for state-aware video deactivation
      await this.mediaStateManager.setMediaState('video', false, false);

      // Stop video frame capture
      if (this.videoFrameCaptureState.captureInterval) {
        clearInterval(this.videoFrameCaptureState.captureInterval);
        this.videoFrameCaptureState.captureInterval = null;
        this.videoFrameCaptureState.isCapturing = false;
      }

      // Clear current video frames
      this.currentVideoFrames = [];

      this.logger.info('✅ Video streaming stopped successfully');

    } catch (error) {
      this.logger.error('❌ Error stopping video streaming:', error);
    }
  }

  /**
   * Get current video frames for multimodal input
   */
  getCurrentVideoFrames(): string[] {
    return [...this.currentVideoFrames];
  }

  /**
   * Get video frame capture state
   */
  getVideoFrameCaptureState(): VideoFrameCaptureState {
    return { ...this.videoFrameCaptureState };
  }

  /**
   * Check if video streaming is active via MediaStateManager
   */
  isVideoStreamingActive(): boolean {
    return this.mediaStateManager.isActive('video');
  }

  /**
   * Update media configuration
   */
  updateMediaConfig(newConfig: Partial<MediaConfig>): void {
    this.mediaConfig = {
      ...this.mediaConfig,
      ...newConfig,
      video: { ...this.mediaConfig.video, ...newConfig.video },
      audio: { ...this.mediaConfig.audio, ...newConfig.audio }
    };

    this.logger.info('🔧 Media configuration updated', this.mediaConfig);
  }

  /**
   * Get current media configuration
   */
  getMediaConfig(): MediaConfig {
    return { ...this.mediaConfig };
  }

  /**
   * 🔥 NEW: Check if user has granted consent for audio capture
   */
  private async hasUserConsentForAudio(): Promise<boolean> {
    try {
      // Check if user has previously granted audio permission
      const permissionStatus = await mediaPermissionManager.checkPermissionStatus('microphone');

      if (permissionStatus.granted) {
        this.logger.info('✅ User has previously granted audio permission');
        return true;
      }

      // Check if there's a stored consent preference
      const storedConsent = localStorage.getItem('hologram-audio-consent');
      if (storedConsent === 'granted') {
        this.logger.info('✅ User consent found in storage');
        return true;
      }

      this.logger.info('❌ No user consent found for audio capture');
      return false;
    } catch (error) {
      this.logger.warn('⚠️ Error checking audio consent:', error);
      return false;
    }
  }

  /**
   * 🔥 NEW: Request user consent for audio capture
   */
  async requestAudioPermissionAndConsent(): Promise<boolean> {
    try {
      this.logger.info('🎤 Requesting audio permission and user consent...');

      // Request microphone access through centralized manager
      const permissionResult = await mediaPermissionManager.requestMediaAccess(
        'MediaCoordinator-AudioConsent',
        {
          audio: this.mediaConfig.audio,
          mediaType: 'microphone',
          requestReason: 'This application needs microphone access for voice interaction and audio processing.'
        }
      );

      if (!permissionResult.success) {
        this.logger.warn('❌ Audio permission denied:', permissionResult.error);
        return false;
      }

      // Store consent preference
      localStorage.setItem('hologram-audio-consent', 'granted');
      localStorage.setItem('hologram-audio-consent-timestamp', Date.now().toString());

      this.logger.info('✅ Audio permission and consent granted');
      return true;

    } catch (error) {
      this.logger.error('❌ Error requesting audio permission:', error);
      return false;
    }
  }

  /**
   * 🔥 NEW: Start audio input with user consent flow
   */
  async startAudioInputWithConsent(agentService: any): Promise<boolean> {
    try {
      this.logger.info('🎤 Starting audio input with consent validation...');

      // Check if already initialized
      if (this.inputCoordinator) {
        this.logger.info('✅ Input coordination already active');
        return true;
      }

      // Request permission and consent if not already granted
      const hasConsent = await this.hasUserConsentForAudio() || await this.requestAudioPermissionAndConsent();

      if (!hasConsent) {
        this.logger.warn('❌ Cannot start audio input without user consent');
        return false;
      }

      // Initialize with consent obtained
      return await this.initializeInputCoordination(agentService, false);

    } catch (error) {
      this.logger.error('❌ Error starting audio input with consent:', error);
      return false;
    }
  }

  /**
   * Get current media state from MediaStateManager
   */
  getMediaState(): MediaState {
    return this.mediaStateManager.getMediaState();
  }

  /**
   * Check if audio is active
   */
  isAudioActive(): boolean {
    return this.mediaStateManager.isActive('audio');
  }

  /**
   * Check if user has activated audio
   */
  isAudioUserActivated(): boolean {
    return this.mediaStateManager.isUserActivated('audio');
  }

  /**
   * Get MediaStateManager instance for advanced usage
   */
  getMediaStateManager(): MediaStateManager {
    return this.mediaStateManager;
  }

  /**
   * Get MediaCaptureManager instance for device operations
   */
  getMediaCaptureManager(): MediaCaptureManager {
    return this.mediaCaptureManager;
  }
  getInputCoordinator(): InputCoordination | null {
    return this.inputCoordinator;
  }

  /**
   * PHASE 2: Get StreamingManager for advanced streaming operations
   */
  getStreamingManager(): StreamingManager | null {
    return this.streamingManager;
  }

  // PHASE 2: WebSocket connection management removed
  // This functionality is now delegated to StreamingManager which provides
  // optimized connection handling with better performance and reliability

  /**
   * Dispose of resources and cleanup
   */
  async dispose(): Promise<void> {
    try {
      this.logger.info('🧹 Disposing MediaCoordinator resources...');

      // Stop all media via MediaStateManager
      await this.mediaStateManager.resetMediaState();

      // Stop video streaming via MediaStateManager
      await this.stopVideoStreaming();

      // Stop input coordination
      if (this.inputCoordinator) {
        this.inputCoordinator.stop();
        this.inputCoordinator = null;
      }

      // Dispose MediaStateManager
      await this.mediaStateManager.dispose();

      // Dispose MediaCaptureManager
      this.mediaCaptureManager.dispose();

      // Clear video frames
      this.currentVideoFrames = [];

      // Reset capture state
      this.videoFrameCaptureState = {
        isCapturing: false,
        frameCount: 0,
        lastCaptureTime: 0,
        captureInterval: null
      };

      this.logger.info('✅ MediaCoordinator disposed successfully');

    } catch (error) {
      this.logger.error('❌ Error disposing MediaCoordinator:', error);
    }
  }
}

export default MediaCoordinator;