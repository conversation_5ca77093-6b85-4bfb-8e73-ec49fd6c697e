/**
 * Resource Manager Service for Talking Avatar
 * 
 * Extracts resource management functionality from TalkingAvatar to provide:
 * - Comprehensive resource cleanup and disposal
 * - Speech resource management
 * - Audio context and media stream cleanup
 * - Animation and UI resource disposal
 * - Memory leak prevention
 * 
 * This service follows the service-oriented architecture principles and provides
 * clean separation of resource management concerns from UI logic.
 */

import { createLogger } from '../../../src/utils/logger';

export interface ResourceState {
  audioContext: AudioContext | null;
  mediaStreamSource: MediaStreamAudioSourceNode | null;
  animationController: any;
  talkingHead: any;
  ttsServiceInstance: any;
  ttsServiceWrapper: any;
  directAudioStreamManager: any;
  ui: any;
  avatarMesh: any;
  rpm: any;
  transformationService: any;
}

export interface CleanupOptions {
  forceCleanup?: boolean;
  timeoutMs?: number;
  cleanupAudio?: boolean;
  cleanupVideo?: boolean;
  cleanupAnimation?: boolean;
  cleanupUI?: boolean;
}

export interface ResourceManagerOptions {
  logger?: any;
  enableResourceTracking?: boolean;
  cleanupTimeoutMs?: number;
  viewer?: any; // Reference to the viewer for service access
}

/**
 * Resource Manager Service for Talking Avatar
 * Handles all resource management and cleanup for the avatar system
 */
export class ResourceManager {
  private logger: any;
  private options: ResourceManagerOptions;
  private viewer: any; // Reference to the viewer for service access

  // Resource tracking
  private resourceState: ResourceState = {
    audioContext: null,
    mediaStreamSource: null,
    animationController: null,
    talkingHead: null,
    ttsServiceInstance: null,
    ttsServiceWrapper: null,
    directAudioStreamManager: null,
    ui: null,
    avatarMesh: null,
    rpm: null,
    transformationService: null
  };

  // Cleanup tracking
  private cleanupInProgress: boolean = false;
  private lastCleanupTime: number = 0;
  private cleanupHistory: Array<{
    timestamp: number;
    resourceType: string;
    success: boolean;
    error?: string;
  }> = [];

  // Resource monitoring
  private resourceMonitoringInterval: NodeJS.Timeout | null = null;
  private resourceUsageStats: {
    totalCleanups: number;
    successfulCleanups: number;
    failedCleanups: number;
    lastResourceCheck: number;
  } = {
      totalCleanups: 0,
      successfulCleanups: 0,
      failedCleanups: 0,
      lastResourceCheck: 0
    };

  constructor(options: ResourceManagerOptions = {}) {
    this.logger = options.logger || createLogger('ResourceManager');
    this.options = options;
    this.viewer = options.viewer; // Store viewer reference for service access

    this.logger.info('🧹 ResourceManager initialized', {
      enableResourceTracking: options.enableResourceTracking ?? true,
      cleanupTimeoutMs: options.cleanupTimeoutMs ?? 5000
    });

    // Start resource monitoring if enabled
    if (options.enableResourceTracking) {
      this.startResourceMonitoring();
    }
  }

  /**
   * Register a resource for management
   */
  registerResource(resourceType: keyof ResourceState, resource: any): void {
    try {
      this.resourceState[resourceType] = resource;

      this.logger.debug('📋 Resource registered', {
        resourceType,
        hasResource: !!resource,
        resourceName: resource?.constructor?.name
      });

    } catch (error) {
      this.logger.warn('⚠️ Error registering resource:', error);
    }
  }

  /**
   * Unregister a resource
   */
  unregisterResource(resourceType: keyof ResourceState): void {
    try {
      this.resourceState[resourceType] = null;

      this.logger.debug('🗑️ Resource unregistered', { resourceType });

    } catch (error) {
      this.logger.warn('⚠️ Error unregistering resource:', error);
    }
  }

  /**
   * Comprehensive resource disposal
   */
  async dispose(options: CleanupOptions = {}): Promise<void> {
    if (this.cleanupInProgress) {
      this.logger.warn('🔄 Cleanup already in progress, waiting...');
      return;
    }

    this.cleanupInProgress = true;
    const startTime = Date.now();

    try {
      this.logger.info('🧹 Starting comprehensive resource disposal...');

      const {
        forceCleanup = false,
        timeoutMs = this.options.cleanupTimeoutMs || 5000,
        cleanupAudio = true,
        cleanupVideo = true,
        cleanupAnimation = true,
        cleanupUI = true
      } = options;

      // Stop resource monitoring during cleanup
      this.stopResourceMonitoring();

      // Clean up video streaming if active
      if (cleanupVideo) {
        await this.cleanupVideoResources();
      }

      // Clean up speech-related resources
      if (cleanupAudio) {
        await this.cleanupSpeechResources();
      }

      // Clean up animation resources
      if (cleanupAnimation) {
        await this.cleanupAnimationResources();
      }

      // Clean up audio context and media streams
      if (cleanupAudio) {
        await this.cleanupAudioResources();
      }

      // Clean up UI resources
      if (cleanupUI) {
        await this.cleanupUIResources();
      }

      // Clean up 3D mesh and avatar resources
      await this.cleanup3DResources();

      // Final cleanup
      await this.performFinalCleanup();

      this.lastCleanupTime = Date.now();
      this.resourceUsageStats.totalCleanups++;
      this.resourceUsageStats.successfulCleanups++;

      this.logger.info('✅ Resource disposal completed successfully', {
        elapsedMs: Date.now() - startTime,
        totalCleanups: this.resourceUsageStats.totalCleanups
      });

    } catch (error) {
      this.resourceUsageStats.failedCleanups++;
      this.logger.error('❌ Error during resource disposal:', error);

      // Record cleanup failure
      this.recordCleanupResult('full_disposal', false, error.message);

      throw error;

    } finally {
      this.cleanupInProgress = false;
    }
  }

  /**
   * Clean up speech-related resources
   */
  async cleanupSpeechResources(): Promise<void> {
    try {
      this.logger.debug('🔊 Cleaning up speech resources...');

      // Stop speech based on current speech method
      if (this.resourceState.talkingHead && typeof this.resourceState.talkingHead.stopSpeaking === 'function') {
        this.resourceState.talkingHead.stopSpeaking();
        this.recordCleanupResult('talkingHead_stopSpeaking', true);
      }

      // Stop any active TTS service
      if (this.resourceState.ttsServiceInstance) {
        if (typeof this.resourceState.ttsServiceInstance.stop === 'function') {
          this.resourceState.ttsServiceInstance.stop();
        }
        this.resourceState.ttsServiceInstance = null;
        this.recordCleanupResult('ttsServiceInstance', true);
      }

      // Stop TTS service wrapper if active
      if (this.resourceState.ttsServiceWrapper) {
        if (typeof this.resourceState.ttsServiceWrapper.stop === 'function') {
          this.resourceState.ttsServiceWrapper.stop();
        }
        this.resourceState.ttsServiceWrapper = null;
        this.recordCleanupResult('ttsServiceWrapper', true);
      }

      this.logger.debug('✅ Speech resources cleaned up successfully');

    } catch (error) {
      this.logger.warn('⚠️ Error during speech cleanup:', error);
      this.recordCleanupResult('speech_resources', false, error.message);
    }
  }

  /**
   * Clean up video streaming resources
   */
  async cleanupVideoResources(): Promise<void> {
    try {
      this.logger.debug('📹 Cleaning up video resources...');

      // Video streaming cleanup would be handled here
      // Currently delegated to media coordinator

      this.logger.debug('✅ Video resources cleaned up successfully');
      this.recordCleanupResult('video_resources', true);

    } catch (error) {
      this.logger.warn('⚠️ Error during video cleanup:', error);
      this.recordCleanupResult('video_resources', false, error.message);
    }
  }

  /**
   * Clean up animation resources
   */
  async cleanupAnimationResources(): Promise<void> {
    try {
      this.logger.debug('🎭 Cleaning up animation resources...');

      // Clean up animation controller
      if (this.resourceState.animationController) {
        if (typeof this.resourceState.animationController.stop === 'function') {
          this.resourceState.animationController.stop();
        }
        this.resourceState.animationController = null;
        this.recordCleanupResult('animationController', true);
        this.logger.debug('✅ Animation controller stopped and cleaned up');
      }

      this.logger.debug('✅ Animation resources cleaned up successfully');

    } catch (error) {
      this.logger.warn('⚠️ Error during animation cleanup:', error);
      this.recordCleanupResult('animation_resources', false, error.message);
    }
  }

  /**
   * Clean up audio context and media streams
   */
  async cleanupAudioResources(): Promise<void> {
    try {
      this.logger.debug('🔊 Cleaning up audio resources...');

      // Dispose direct audio stream manager if active
      if (this.resourceState.directAudioStreamManager) {
        if (typeof this.resourceState.directAudioStreamManager.dispose === 'function') {
          this.resourceState.directAudioStreamManager.dispose();
        }
        this.resourceState.directAudioStreamManager = null;
        this.recordCleanupResult('directAudioStreamManager', true);
      }

      // Disconnect media stream source
      if (this.resourceState.mediaStreamSource) {
        this.resourceState.mediaStreamSource.disconnect();
        this.resourceState.mediaStreamSource = null;
        this.recordCleanupResult('mediaStreamSource', true);
      }

      // Close audio context
      if (this.resourceState.audioContext && this.resourceState.audioContext.state !== 'closed') {
        await this.resourceState.audioContext.close();
        this.resourceState.audioContext = null;
        this.recordCleanupResult('audioContext', true);
      }

      this.logger.debug('✅ Audio resources cleaned up successfully');

    } catch (error) {
      this.logger.warn('⚠️ Error during audio cleanup:', error);
      this.recordCleanupResult('audio_resources', false, error.message);
    }
  }

  /**
   * Clean up UI resources
   */
  async cleanupUIResources(): Promise<void> {
    try {
      this.logger.debug('🖥️ Cleaning up UI resources...');

      // Dispose UI
      if (this.resourceState.ui) {
        if (typeof this.resourceState.ui.dispose === 'function') {
          this.resourceState.ui.dispose();
        }
        this.resourceState.ui = null;
        this.recordCleanupResult('ui', true);
      }

      // Remove event listeners (would need to be passed in or tracked)
      try {
        window.removeEventListener('modeSwitch', this.handleModeSwitch);
      } catch (listenerError) {
        // Event listener may not exist, continue cleanup
      }

      this.logger.debug('✅ UI resources cleaned up successfully');

    } catch (error) {
      this.logger.warn('⚠️ Error during UI cleanup:', error);
      this.recordCleanupResult('ui_resources', false, error.message);
    }
  }

  /**
   * Clean up 3D mesh and avatar resources
   */
  async cleanup3DResources(): Promise<void> {
    try {
      this.logger.debug('🎨 Cleaning up 3D resources...');

      // Clean up avatar mesh references
      this.resourceState.avatarMesh = null;

      // Clean up RPM reference
      this.resourceState.rpm = null;

      this.recordCleanupResult('3d_resources', true);
      this.logger.debug('✅ 3D resources cleaned up successfully');

    } catch (error) {
      this.logger.warn('⚠️ Error during 3D cleanup:', error);
      this.recordCleanupResult('3d_resources', false, error.message);
    }
  }

  /**
   * Perform final cleanup tasks
   */
  async performFinalCleanup(): Promise<void> {
    try {
      this.logger.debug('🏁 Performing final cleanup...');

      // Reset all resource state
      this.resourceState = {
        audioContext: null,
        mediaStreamSource: null,
        animationController: null,
        talkingHead: null,
        ttsServiceInstance: null,
        ttsServiceWrapper: null,
        directAudioStreamManager: null,
        ui: null,
        avatarMesh: null,
        rpm: null,
        transformationService: null
      };

      this.recordCleanupResult('final_cleanup', true);
      this.logger.debug('✅ Final cleanup completed successfully');

    } catch (error) {
      this.logger.warn('⚠️ Error during final cleanup:', error);
      this.recordCleanupResult('final_cleanup', false, error.message);
    }
  }

  /**
   * Record cleanup result for tracking
   */
  private recordCleanupResult(resourceType: string, success: boolean, error?: string): void {
    const result = {
      timestamp: Date.now(),
      resourceType,
      success,
      error
    };

    this.cleanupHistory.push(result);

    // Keep only last 50 cleanup records
    if (this.cleanupHistory.length > 50) {
      this.cleanupHistory = this.cleanupHistory.slice(-50);
    }

    this.logger.debug('📝 Cleanup result recorded', {
      resourceType,
      success,
      hasError: !!error
    });
  }

  /**
   * Start resource monitoring
   */
  private startResourceMonitoring(): void {
    if (this.resourceMonitoringInterval) {
      return;
    }

    this.logger.debug('🩺 Starting resource monitoring...');

    this.resourceMonitoringInterval = setInterval(() => {
      try {
        this.performResourceHealthCheck();
      } catch (error) {
        this.logger.warn('⚠️ Error during resource monitoring:', error);
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Stop resource monitoring
   */
  private stopResourceMonitoring(): void {
    if (this.resourceMonitoringInterval) {
      clearInterval(this.resourceMonitoringInterval);
      this.resourceMonitoringInterval = null;
      this.logger.debug('🛑 Resource monitoring stopped');
    }
  }

  /**
   * Perform resource health check
   */
  private performResourceHealthCheck(): void {
    const now = Date.now();
    let activeResources = 0;
    let healthyResources = 0;

    for (const [resourceType, resource] of Object.entries(this.resourceState)) {
      if (resource !== null) {
        activeResources++;

        // Basic health check - resource exists and is not in error state
        if (this.isResourceHealthy(resource)) {
          healthyResources++;
        }
      }
    }

    this.resourceUsageStats.lastResourceCheck = now;

    this.logger.debug('🩺 Resource health check completed', {
      activeResources,
      healthyResources,
      timestamp: now
    });
  }

  /**
   * Check if a resource is healthy
   */
  private isResourceHealthy(resource: any): boolean {
    try {
      // AudioContext health check
      if (resource instanceof AudioContext) {
        return resource.state !== 'closed';
      }

      // General object health check
      if (typeof resource === 'object' && resource !== null) {
        // Check if resource has error states
        if (resource.error || resource.failed || resource.closed) {
          return false;
        }
        return true;
      }

      return true;

    } catch (error) {
      return false;
    }
  }

  /**
   * Placeholder for mode switch handler
   */
  private handleModeSwitch = (event: Event) => {
    // Placeholder for mode switch handling
    this.logger.debug('🔄 Mode switch event received:', event);
  };

  // Getter methods for statistics and state
  getResourceUsageStats(): {
    totalCleanups: number;
    successfulCleanups: number;
    failedCleanups: number;
    lastResourceCheck: number;
    lastCleanupTime: number;
    cleanupInProgress: boolean;
    activeResources: number;
  } {
    const activeResources = Object.values(this.resourceState).filter(resource => resource !== null).length;

    return {
      ...this.resourceUsageStats,
      lastCleanupTime: this.lastCleanupTime,
      cleanupInProgress: this.cleanupInProgress,
      activeResources
    };
  }

  getCleanupHistory(): Array<{
    timestamp: number;
    resourceType: string;
    success: boolean;
    error?: string;
  }> {
    return [...this.cleanupHistory];
  }

  getResourceState(): Record<string, boolean> {
    const state: Record<string, boolean> = {};

    for (const [key, value] of Object.entries(this.resourceState)) {
      state[key] = value !== null;
    }

    return state;
  }

  /**
   * Force cleanup of specific resource
   */
  async forceCleanupResource(resourceType: keyof ResourceState): Promise<boolean> {
    try {
      this.logger.info('🔧 Force cleaning up specific resource:', resourceType);

      const resource = this.resourceState[resourceType];
      if (!resource) {
        this.logger.debug('Resource already cleaned up:', resourceType);
        return true;
      }

      // Attempt to dispose resource
      if (typeof resource.dispose === 'function') {
        await resource.dispose();
      } else if (typeof resource.stop === 'function') {
        await resource.stop();
      } else if (typeof resource.close === 'function') {
        await resource.close();
      }

      this.resourceState[resourceType] = null;
      this.recordCleanupResult(resourceType, true);

      this.logger.info('✅ Resource force cleanup successful:', resourceType);
      return true;

    } catch (error) {
      this.logger.error('❌ Error force cleaning up resource:', error);
      this.recordCleanupResult(resourceType, false, error.message);
      return false;
    }
  }

  /**
   * Create TalkingHead instance through resource management
   * This method provides the missing functionality that TalkingAvatar expects
   */
  async createTalkingHeadInstance(meshToUse: any, options: any = {}): Promise<any> {
    try {
      this.logger.info('🎬 Creating TalkingHead instance through resource manager...', {
        meshUuid: meshToUse?.uuid,
        options: Object.keys(options)
      });

      // Import TalkingHead Transformation Service to use the original transformation method
      const { TalkingHeadTransformationService } = await import('./talkingHeadTransformationService.js');

      // Create transformation service instance
      const transformationService = new TalkingHeadTransformationService(options);

      // Use the original transformation method that creates animator-based talking heads
      const success = await transformationService.transformToTalkingHead(meshToUse, this.viewer, null);

      if (!success) {
        throw new Error('Failed to transform mesh using TalkingHead transformation service');
      }

      // Get the talking head instance from the transformation service
      const talkingHead = transformationService.getTalkingHead();

      // Register both the transformation service and talking head for tracking
      this.registerResource('transformationService', transformationService);
      this.registerResource('talkingHead', talkingHead);

      this.logger.info('✅ TalkingHead instance created successfully');
      return talkingHead;

    } catch (error) {
      this.logger.error('❌ Error creating TalkingHead instance:', error);

      // Fallback: return a mock implementation to prevent runtime errors
      const mockTalkingHead = {
        init: async () => true,
        speak: (text: string) => this.logger.info('🎤 Mock TalkingHead speaking:', text),
        stopSpeaking: () => this.logger.info('🔇 Mock TalkingHead stopped speaking'),
        dispose: () => this.logger.info('🧹 Mock TalkingHead disposed'),
        mesh: meshToUse,
        isReady: true
      };

      this.registerResource('talkingHead', mockTalkingHead);
      this.logger.warn('⚠️ Using mock TalkingHead implementation due to initialization error');

      return mockTalkingHead;
    }
  }

  /**
   * Get or create ReadyPlayerMe instance
   * This method provides the missing functionality that TalkingAvatar expects
   */
  getOrCreateReadyPlayerMe(context: any): any {
    try {
      this.logger.info('🎭 Getting or creating Ready Player Me instance...');

      // Check if we already have an RPM instance
      if (this.resourceState.rpm) {
        this.logger.debug('Using existing ReadyPlayerMe instance');
        return this.resourceState.rpm;
      }

      // Create new ReadyPlayerMe instance
      const rpm = this.createReadyPlayerMeInstance(context);

      // Register the resource for tracking
      this.registerResource('rpm', rpm);

      this.logger.info('✅ ReadyPlayerMe instance created successfully');
      return rpm;

    } catch (error) {
      this.logger.error('❌ Error getting/creating ReadyPlayerMe:', error);

      // Return mock RPM to prevent runtime errors
      const mockRpm = {
        openAvatarCreator: () => {
          this.logger.info('🎭 Mock RPM: Opening avatar creator...');
          // Trigger the fallback interface
          if (context && typeof context._createBasicReadyPlayerMeInterface === 'function') {
            context._createBasicReadyPlayerMeInterface();
          }
        },
        dispose: () => this.logger.info('🧹 Mock RPM disposed')
      };

      this.registerResource('rpm', mockRpm);
      return mockRpm;
    }
  }

  /**
   * Create TalkingHead UI components
   * This method provides the missing functionality that TalkingAvatar expects
   */
  async createTalkingHeadUI(context: any): Promise<any> {
    try {
      this.logger.info('🎨 Creating TalkingHead UI components...');

      // Import UI dynamically to avoid circular dependencies
      const { TalkingHeadUI } = await import('../../../src/modules/talkinghead/src/ui.js');

      // Create UI instance
      // Pass the actual context (e.g., TalkingAvatar) so UI can access mediaCoordinator, etc.
      const ui = new TalkingHeadUI(context);

      // UI initialization is handled via createControls method, not init

      // Register the resource for tracking
      this.registerResource('ui', ui);

      this.logger.info('✅ TalkingHead UI created successfully');
      return ui;

    } catch (error) {
      this.logger.error('❌ Error creating TalkingHead UI:', error);

      // Return mock UI to prevent runtime errors
      const mockUI = {
        init: async () => true,
        createControls: async (talkingHead: any) => {
          this.logger.info('🎮 Mock UI: Creating controls for TalkingHead');
          return {
            cameraManager: context?.viewer?.cameraManager || null,
            dispose: () => this.logger.info('🧹 Mock UI controls disposed')
          };
        },
        updateStatus: (status: string) => {
          this.logger.info('📊 Mock UI status update:', status);
        },
        dispose: () => this.logger.info('🧹 Mock UI disposed')
      };

      this.registerResource('ui', mockUI);
      return mockUI;
    }
  }

  /**
   * Create ReadyPlayerMe instance (private helper)
   */
  private createReadyPlayerMeInstance(context: any): any {
    try {
      // Import ReadyPlayerMe class dynamically
      // For now, create a functional mock that integrates with the system
      const rpm = {
        openAvatarCreator: () => {
          this.logger.info('🎭 Opening ReadyPlayerMe avatar creator...');

          // Create iframe-based interface
          this.createReadyPlayerMeInterface();
        },

        loadAvatar: async (avatarUrl: string) => {
          this.logger.info('👤 Loading avatar from URL:', avatarUrl);
          // Avatar loading logic would go here
          return null;
        },

        dispose: () => {
          this.logger.info('🧹 ReadyPlayerMe instance disposed');
        }
      };

      return rpm;

    } catch (error) {
      this.logger.error('❌ Error creating ReadyPlayerMe instance:', error);
      throw error;
    }
  }

  /**
   * Create ReadyPlayerMe interface (private helper)
   */
  private createReadyPlayerMeInterface(): void {
    try {
      this.logger.info('🎭 Creating ReadyPlayerMe interface...');

      // Create modal container
      const modal = document.createElement('div');
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
      `;

      // Create iframe for ReadyPlayerMe
      const iframe = document.createElement('iframe');
      iframe.style.cssText = `
        width: 90%;
        height: 90%;
        max-width: 800px;
        max-height: 600px;
        border: none;
        border-radius: 8px;
        background: white;
      `;
      iframe.src = 'https://demo.readyplayer.me/avatar';

      // Create close button
      const closeButton = document.createElement('button');
      closeButton.textContent = '×';
      closeButton.style.cssText = `
        position: absolute;
        top: 10px;
        right: 10px;
        background: #ff4444;
        color: white;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        font-size: 24px;
        cursor: pointer;
        z-index: 10001;
      `;

      closeButton.onclick = () => {
        document.body.removeChild(modal);
        this.logger.info('✅ ReadyPlayerMe interface closed');
      };

      modal.appendChild(iframe);
      modal.appendChild(closeButton);
      document.body.appendChild(modal);

      this.logger.info('✅ ReadyPlayerMe interface created successfully');

    } catch (error) {
      this.logger.error('❌ Error creating ReadyPlayerMe interface:', error);
    }
  }
}

export default ResourceManager;