/**
 * Audio Processor Service for Talking Avatar
 * 
 * Extracts audio processing functionality from TalkingAvatar to provide:
 * - Audio data handling and processing
 * - AudioContext initialization and management
 * - Real-time audio streaming to agent service
 * - Audio format conversion and validation
 * 
 * This service follows the service-oriented architecture principles and provides
 * clean separation of audio processing concerns from UI logic.
 */

import { createLogger } from '../../../src/utils/logger';
import { 
  createAudioContext, 
  createMediaStreamSource,
  processRealtimeAudio,
  handleAudioData,
  type AudioProcessingResult,
  calculateAudioVolume,
  estimateAudioQuality
} from '../../../src/media/modality/audio.js';

// Define local analysis result interface
interface AudioAnalysisResult {
  volume: number;
  quality: string | number;
  duration: number;
  sampleRate: number;
  channels: number;
}

export interface AudioProcessingConfig {
  sampleRate: number;
  bufferSize: number;
  enableAnalysis: boolean;
  enableRealtimeProcessing: boolean;
  volumeThreshold: number;
  qualityThreshold: number;
}

export interface AudioConnectionDiagnostics {
  hasAgentService: boolean;
  hasModel: boolean;
  hasRealtimeSocket: boolean;
  socketReadyState: number | undefined;
  sessionStabilized: boolean;
  sessionId: string | undefined;
}

export interface AudioProcessorOptions {
  logger?: any;
  processingConfig?: Partial<AudioProcessingConfig>;
  enableConnectionHealthMonitoring?: boolean;
}

/**
 * Audio Processor Service for Talking Avatar
 * Handles all audio processing for the avatar system with enterprise-grade reliability
 */
export class AudioProcessor {
  private logger: any;
  private options: AudioProcessorOptions;
  private processingConfig: AudioProcessingConfig;
  
  // Audio context and processing
  private audioContext: AudioContext | null = null;
  private mediaStreamSource: MediaStreamAudioSourceNode | null = null;
  
  // Connection health monitoring
  private connectionHealthInterval: NodeJS.Timeout | null = null;
  private lastConnectionCheck: number = 0;
  
  // Audio processing state
  private isProcessingActive: boolean = false;
  private processedChunks: number = 0;
  private lastProcessingError: Error | null = null;

  constructor(options: AudioProcessorOptions = {}) {
    this.logger = options.logger || createLogger('AudioProcessor');
    this.options = options;
    
    // Initialize audio processing configuration with defaults
    this.processingConfig = {
      sampleRate: 16000, // Aliyun compatible
      bufferSize: 4096,
      enableAnalysis: true,
      enableRealtimeProcessing: true,
      volumeThreshold: 0.01,
      qualityThreshold: 0.7,
      ...options.processingConfig
    };

    this.logger.info('🎙️ AudioProcessor initialized', {
      processingConfig: this.processingConfig,
      enableConnectionHealthMonitoring: options.enableConnectionHealthMonitoring ?? true
    });
  }

  /**
   * Initialize AudioContext with optimized settings for avatar processing
   */
  async initializeAudioContext(): Promise<boolean> {
    try {
      this.logger.info('🔊 Initializing AudioContext for avatar audio processing...');

      // Create audio context with avatar-optimized settings
      this.audioContext = await createAudioContext({
        sampleRate: this.processingConfig.sampleRate,
        numChannels: 1,
        bitDepth: 16
      });

      if (!this.audioContext) {
        throw new Error('Failed to create AudioContext');
      }

      // Resume context if suspended (common on mobile)
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }

      this.logger.info('✅ AudioContext initialized successfully', {
        sampleRate: this.audioContext.sampleRate,
        state: this.audioContext.state,
        baseLatency: this.audioContext.baseLatency
      });

      return true;

    } catch (error) {
      this.logger.error('❌ Failed to initialize AudioContext:', error);
      this.lastProcessingError = error instanceof Error ? error : new Error(String(error));
      return false;
    }
  }

  /**
   * Handle audio data from MediaCaptureManager and send to persistent realtime websocket
   * This is the core audio processing method extracted from TalkingAvatar
   */
  async handleAudioData(audioData: any, format: string, agentService: any): Promise<void> {
    try {
      // Enhanced realtime connection checking with detailed diagnostics
      const connectionDiagnostics = this.getConnectionDiagnostics(agentService);
      const realtimeActive = agentService?.model?.isRealtimeModeActive?.();
      
      if (!realtimeActive) {
        this.logger.warn('⚠️ Realtime connection not active - audio input will be ignored', connectionDiagnostics);

        // Auto-retry connection establishment if socket is disconnected
        if (connectionDiagnostics.hasModel && 
            (!connectionDiagnostics.hasRealtimeSocket || connectionDiagnostics.socketReadyState !== 1)) {
          
          await this.attemptRealtimeReconnection(agentService);
        }
        return;
      }

      // Process audio data with enhanced error handling
      await this.processAndSendAudioData(audioData, format, agentService);
      
      // Update processing statistics
      this.processedChunks++;
      this.lastProcessingError = null;

    } catch (error) {
      this.logger.error('❌ Error handling audio data:', error);
      this.lastProcessingError = error instanceof Error ? error : new Error(String(error));
    }
  }

  /**
   * Process and send audio data to the agent service
   */
  private async processAndSendAudioData(audioData: any, format: string, agentService: any): Promise<void> {
    try {
      // Perform audio analysis if enabled
      if (this.processingConfig.enableAnalysis) {
        const analysisResult = await this.analyzeAudioData(audioData, format);
        
        // Check quality thresholds
        if (typeof analysisResult.quality === 'number' && analysisResult.quality < this.processingConfig.qualityThreshold) {
          this.logger.warn('⚠️ Audio quality below threshold', {
            quality: analysisResult.quality,
            threshold: this.processingConfig.qualityThreshold
          });
        }
        
        // Check volume thresholds
        if (analysisResult.volume < this.processingConfig.volumeThreshold) {
          this.logger.debug('🔇 Audio volume below threshold, skipping', {
            volume: analysisResult.volume,
            threshold: this.processingConfig.volumeThreshold
          });
          return;
        }
      }

      // Send audio data to persistent Aliyun realtime websocket  
      if (agentService.model.sendRealtimeAudio) {
        // Convert pcm16 format to uint8array for AliyunWebSocketChatModel compatibility
        const compatibleFormat = format === 'pcm16' ? 'uint8array' : format;
        await agentService.model.sendRealtimeAudio(audioData, compatibleFormat);
        
        this.logger.debug('🎙️ Audio chunk sent to persistent realtime websocket', { 
          originalFormat: format, 
          sentFormat: compatibleFormat,
          dataSize: audioData?.length || audioData?.byteLength || 0
        });
      } else {
        this.logger.warn('⚠️ Model does not support sendRealtimeAudio');
      }

    } catch (error) {
      this.logger.error('❌ Error processing and sending audio data:', error);
      throw error;
    }
  }

  /**
   * Analyze audio data for quality and volume metrics
   */
  private async analyzeAudioData(audioData: any, format: string): Promise<AudioAnalysisResult> {
    try {
      // Use centralized audio processing utilities
      if (audioData instanceof Float32Array) {
        // Process with basic analysis
        const volume = calculateAudioVolume(audioData);
        const quality = estimateAudioQuality(audioData);
        
        return {
          volume,
          quality,
          duration: audioData.length / this.processingConfig.sampleRate,
          sampleRate: this.processingConfig.sampleRate,
          channels: 1
        };
      } else {
        // Handle other audio formats with basic analysis
        const volume = 0.1; // Default volume for non-Float32Array
        const quality = 'good';
        
        return {
          volume,
          quality,
          duration: 0,
          sampleRate: this.processingConfig.sampleRate,
          channels: 1
        };
      }

    } catch (error) {
      this.logger.warn('⚠️ Error analyzing audio data:', error);
      
      // Return default analysis result
      return this.createDefaultAnalysisResult();
    }
  }

  /**
   * Attempt to re-establish realtime connection
   */
  private async attemptRealtimeReconnection(agentService: any): Promise<void> {
    try {
      this.logger.info('🔄 Attempting to re-establish realtime connection...');

      await agentService.initializeRealtimeMode({
        reason: 'reconnection_attempt',
        // Provide essential VAD callbacks to prevent errors
        onVoiceActivityDetected: () => {
          this.logger.debug('🎙️ Voice activity detected');
        },
        onVoiceActivityStopped: () => {
          this.logger.debug('🔇 Voice activity stopped');
        },
        onAudioReceived: (audioData: any) => {
          this.logger.debug('🔊 Audio received from model');
          // Handle audio response if needed
        }
      });

      this.logger.info('✅ Realtime connection re-established successfully');

    } catch (reconnectError) {
      this.logger.error('❌ Failed to re-establish realtime connection:', reconnectError);
      this.lastProcessingError = reconnectError instanceof Error ? reconnectError : new Error(String(reconnectError));
    }
  }

  /**
   * Get detailed connection diagnostics
   */
  private getConnectionDiagnostics(agentService: any): AudioConnectionDiagnostics {
    return {
      hasAgentService: !!agentService,
      hasModel: !!agentService?.model,
      hasRealtimeSocket: !!agentService?.model?.realtimeSocket,
      socketReadyState: agentService?.model?.realtimeSocket?.readyState,
      sessionStabilized: agentService?.model?.realtimeSessionStabilized,
      sessionId: agentService?.model?.realtimeSessionId
    };
  }

  /**
   * Start monitoring realtime connection health
   */
  startConnectionHealthMonitoring(agentService: any): void {
    if (!this.options.enableConnectionHealthMonitoring) {
      this.logger.info('🩺 Connection health monitoring disabled in options');
      return;
    }

    // Clear existing monitoring
    this.stopConnectionHealthMonitoring();

    this.logger.info('🩺 Starting connection health monitoring...');

    this.connectionHealthInterval = setInterval(() => {
      const diagnostics = this.getConnectionDiagnostics(agentService);
      const now = Date.now();
      
      this.logger.debug('🩺 Connection health check', {
        ...diagnostics,
        timeSinceLastCheck: now - this.lastConnectionCheck,
        processedChunks: this.processedChunks,
        hasRecentError: !!this.lastProcessingError
      });

      // Check for connection issues
      if (diagnostics.hasAgentService && diagnostics.hasModel) {
        if (!diagnostics.hasRealtimeSocket || diagnostics.socketReadyState !== 1) {
          this.logger.warn('⚠️ Realtime connection health issue detected', diagnostics);
        }
      }

      this.lastConnectionCheck = now;
    }, 30000); // Check every 30 seconds
  }

  /**
   * Stop connection health monitoring
   */
  stopConnectionHealthMonitoring(): void {
    if (this.connectionHealthInterval) {
      clearInterval(this.connectionHealthInterval);
      this.connectionHealthInterval = null;
      this.logger.info('🛑 Connection health monitoring stopped');
    }
  }

  /**
   * Update audio processing configuration
   */
  updateProcessingConfig(newConfig: Partial<AudioProcessingConfig>): void {
    this.processingConfig = {
      ...this.processingConfig,
      ...newConfig
    };

    this.logger.info('🔧 Audio processing configuration updated', this.processingConfig);
  }

  /**
   * Get current processing configuration
   */
  getProcessingConfig(): AudioProcessingConfig {
    return { ...this.processingConfig };
  }

  /**
   * Get processing statistics
   */
  getProcessingStats(): {
    processedChunks: number;
    isProcessingActive: boolean;
    lastProcessingError: Error | null;
    lastConnectionCheck: number;
  } {
    return {
      processedChunks: this.processedChunks,
      isProcessingActive: this.isProcessingActive,
      lastProcessingError: this.lastProcessingError,
      lastConnectionCheck: this.lastConnectionCheck
    };
  }

  /**
   * Check if audio processing is healthy
   */
  isProcessingHealthy(): boolean {
    return !this.lastProcessingError && this.isProcessingActive;
  }

  /**
   * Create default analysis result for error cases
   */
  private createDefaultAnalysisResult(): AudioAnalysisResult {
    return {
      volume: 0,
      quality: 'silent',
      duration: 0,
      sampleRate: this.processingConfig.sampleRate,
      channels: 1
    };
  }

  /**
   * Start audio processing
   */
  startProcessing(): void {
    this.isProcessingActive = true;
    this.logger.info('▶️ Audio processing started');
  }

  /**
   * Stop audio processing
   */
  stopProcessing(): void {
    this.isProcessingActive = false;
    this.logger.info('⏹️ Audio processing stopped');
  }

  /**
   * Dispose of resources and cleanup
   */
  async dispose(): Promise<void> {
    try {
      this.logger.info('🧹 Disposing AudioProcessor resources...');

      // Stop processing
      this.stopProcessing();

      // Stop connection health monitoring
      this.stopConnectionHealthMonitoring();

      // Clean up audio context
      if (this.mediaStreamSource) {
        this.mediaStreamSource.disconnect();
        this.mediaStreamSource = null;
      }

      if (this.audioContext) {
        await this.audioContext.close();
        this.audioContext = null;
      }

      // Reset state
      this.processedChunks = 0;
      this.lastProcessingError = null;
      this.lastConnectionCheck = 0;

      this.logger.info('✅ AudioProcessor disposed successfully');

    } catch (error) {
      this.logger.error('❌ Error disposing AudioProcessor:', error);
    }
  }
}

export default AudioProcessor;