/**
 * Unified Character Service
 * 
 * Consolidated service that combines character management, personality configurations,
 * and AI-powered character analysis for role-playing functionality with the dual brain system.
 * 
 * Combines functionality from:
 * - CharacterService: Character presets, personality configurations, context integration
 * - CharacterAnalysisService: AI-powered character analysis and summarization
 */

import { createLogger } from '../../../src/utils/logger.js';
import { CHARACTER_CONSOLIDATION_PROMPTS } from '../../../src/agent/prompts/noactivity.js';

const logger = createLogger('CharacterService');

export class CharacterService {
    constructor(options = {}) {
        this.options = {
            agentCoordinator: null,
            agentService: null,
            dualBrainCoordinator: null,
            memoryManager: null,
            userId: 'default_user',
            storageKey: 'hologram_character_data',
            enablePersistence: true,
            cacheExpiry: 3600000, // 1 hour
            maxSummaryLength: 500,
            ...options
        };

        this.currentCharacter = null;
        this.characterHistory = [];
        this.personalityProfiles = new Map();
        this.contextMemory = new Map();
        
        // Character analysis caches (from CharacterAnalysisService)
        this.analysisCache = new Map();
        this.personalityCache = new Map();
        
        // Character consistency tracking
        this.consistencyMetrics = {
            responseAlignment: 0,
            personalityStability: 0,
            contextualRelevance: 0
        };

        // LangGraph Memory Integration
        this.memoryManager = options.memoryManager || options.agentService?.getMemoryManager?.();
        this.userId = options.userId || 'default_user';

        this.logger = logger;
        this.initialize();
    }

    /**
     * Initialize the unified character service
     */
    async initialize() {
        this.loadStoredData();
        this.logger.info('🧠 Unified Character Service initialized', {
            persistence: this.options.enablePersistence,
            hasAgentService: !!this.options.agentService,
            hasDualBrain: !!this.options.dualBrainCoordinator
        });
    }

    // ============================================================================
    // CHARACTER MANAGEMENT (Original CharacterService functionality)
    // ============================================================================

    /**
     * Set active character context for the dual brain system
     */
    async setCharacterContext(character) {
        try {
            this.logger.info(`[CharacterService] Setting character context: ${character.name}`);

            // Validate character structure
            if (!this.validateCharacterStructure(character)) {
                throw new Error('Invalid character structure');
            }

            // Update current character
            this.currentCharacter = { ...character };
            
            // Build comprehensive context for dual brain coordination
            const context = this.buildCharacterContext(character);
            
            // Apply to agent coordinator if available
            if (this.options.agentCoordinator) {
                await this.applyCharacterToAgentSystem(context);
            }

            // Update personality profile
            this.updatePersonalityProfile(character);
            
            // Track character change
            this.recordCharacterChange(character);
            
            // Persist data
            if (this.options.enablePersistence) {
                this.saveData();
            }

            this.logger.info('[CharacterService] Character context applied successfully');
            return true;

        } catch (error) {
            this.logger.error('[CharacterService] Failed to set character context:', error);
            return false;
        }
    }

    /**
     * Build comprehensive character context for dual brain system
     */
    buildCharacterContext(character) {
        const personalityTraits = this.generatePersonalityDescription(character.personality);
        const behaviorGuidelines = this.generateBehaviorGuidelines(character);
        const contextualCues = this.generateContextualCues(character);

        return {
            // Core character identity
            identity: {
                name: character.name,
                role: character.id,
                description: character.description,
                avatar: character.avatar
            },

            // System prompts and instructions
            systemPrompt: character.systemPrompt,
            enhancedPrompt: this.enhanceSystemPrompt(character),
            
            // Personality configuration
            personality: {
                traits: character.personality,
                description: personalityTraits,
                guidelines: behaviorGuidelines
            },

            // Communication style
            communication: {
                voiceStyle: character.voiceStyle,
                formality: character.personality.formality,
                enthusiasm: character.personality.enthusiasm,
                empathy: character.personality.empathy
            },

            // Contextual behavior cues
            context: {
                cues: contextualCues,
                consistency: this.consistencyMetrics,
                adaptability: this.calculateAdaptabilityScore(character)
            },

            // Timestamp and versioning
            timestamp: Date.now(),
            version: '1.0'
        };
    }

    /**
     * Apply character context to agent system
     */
    async applyCharacterToAgentSystem(context) {
        try {
            const agentService = this.options.agentCoordinator.getAgentService();
            
            if (!agentService) {
                this.logger.warn('[CharacterService] Agent service not available');
                return false;
            }

            // Apply to dual brain coordinator if available
            const dualBrainCoordinator = agentService.getDualBrainCoordinator?.();
            if (dualBrainCoordinator) {
                await this.applyToDualBrainSystem(dualBrainCoordinator, context);
            }

            // Update agent service configuration
            if (agentService.updateCharacterContext) {
                await agentService.updateCharacterContext(context);
            }

            // Update model system prompts
            await this.updateModelSystemPrompts(agentService, context);

            return true;

        } catch (error) {
            this.logger.error('[CharacterService] Failed to apply character to agent system:', error);
            return false;
        }
    }

    /**
     * Apply character context to dual brain system
     */
    async applyToDualBrainSystem(dualBrainCoordinator, context) {
        try {
            this.logger.info('[CharacterService] Applying character to dual brain system');

            // Update System1 (reactive) with personality traits
            const system1Context = {
                personality: context.personality,
                communication: context.communication,
                responseStyle: this.generateSystem1ResponseStyle(context)
            };

            // Update System2 (analytical) with behavioral guidelines
            const system2Context = {
                guidelines: context.personality.guidelines,
                consistencyRules: this.generateConsistencyRules(),
                contextualAwareness: context.context
            };

            // Apply contexts to dual brain systems
            if (dualBrainCoordinator.updateSystem1Context) {
                await dualBrainCoordinator.updateSystem1Context(system1Context);
            }

            if (dualBrainCoordinator.updateSystem2Context) {
                await dualBrainCoordinator.updateSystem2Context(system2Context);
            }

            // Update coordination parameters
            if (dualBrainCoordinator.updateCoordinationParameters) {
                const coordinationParams = this.generateCoordinationParameters(context);
                await dualBrainCoordinator.updateCoordinationParameters(coordinationParams);
            }

            this.logger.info('[CharacterService] Dual brain system updated with character context');

        } catch (error) {
            this.logger.error('[CharacterService] Failed to apply character to dual brain system:', error);
        }
    }

    // ============================================================================
    // CHARACTER ANALYSIS (CharacterAnalysisService functionality)
    // ============================================================================

    /**
     * Prepare character data for analysis (context only - no System 2 invocation)
     * Returns analysis request context that can be used by System 2 externally
     */
    async prepareCharacterAnalysisContext(characterData) {
        try {
            const characterId = `${characterData.name}_${characterData.anime}`.toLowerCase().replace(/\s+/g, '_');

            // Check LangGraph memory first
            if (this.memoryManager) {
                try {
                    const memoryResults = await this.memoryManager.searchMemories(
                        this.userId,
                        {
                            context: 'character',
                            filter: { characterId },
                            limit: 1
                        }
                    );

                    if (memoryResults.length > 0) {
                        const cached = memoryResults[0];
                        if (Date.now() - cached.timestamp < this.options.cacheExpiry) {
                            this.logger.info(`📋 Using memory cached analysis for ${characterData.name}`);
                            return {
                                analysisReady: true,
                                cached: true,
                                result: cached.content,
                                characterId
                            };
                        }
                    }
                } catch (error) {
                    this.logger.warn('Failed to retrieve character analysis from memory:', error);
                }
            }

            // Fallback to local cache
            const cacheKey = `analysis_${characterData.name}_${characterData.anime}`;
            if (this.analysisCache.has(cacheKey)) {
                const cached = this.analysisCache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.options.cacheExpiry) {
                    this.logger.info(`📋 Using local cached analysis for ${characterData.name}`);
                    return {
                        analysisReady: true,
                        cached: true,
                        result: cached.data,
                        characterId
                    };
                }
            }

            this.logger.info(`📋 Preparing character analysis context for: ${characterData.name} from ${characterData.anime}`);

            // Prepare analysis context for external System 2 invocation
            const analysisContext = {
                analysisReady: false,
                characterData,
                characterId,
                analysisPrompt: CHARACTER_CONSOLIDATION_PROMPTS.analysis
                    .replace('{characterData}', JSON.stringify(characterData, null, 2)),
                cacheKey,
                memoryKey: {
                    userId: this.userId,
                    characterId,
                    type: 'character_analysis',
                    name: characterData.name,
                    anime: characterData.anime
                }
            };

            this.logger.info(`✅ Character analysis context prepared for ${characterData.name}`);
            return analysisContext;

        } catch (error) {
            this.logger.error(`❌ Character analysis context preparation failed for ${characterData.name}:`, error);
            return {
                analysisReady: true,
                cached: false,
                error: true,
                result: this.generateFallbackAnalysis(characterData)
            };
        }
    }

    /**
     * Prepare personality profile generation context (context only - no System 2 invocation)
     * Returns context that can be used by System 2 externally
     */
    async preparePersonalityProfileContext(characterName, description) {
        try {
            const cacheKey = `personality_${characterName}`;

            // Check cache
            if (this.personalityCache.has(cacheKey)) {
                const cached = this.personalityCache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.options.cacheExpiry) {
                    return {
                        profileReady: true,
                        cached: true,
                        result: cached.data
                    };
                }
            }

            this.logger.info(`📋 Preparing personality profile context for ${characterName}`);

            const personalityPrompt = CHARACTER_CONSOLIDATION_PROMPTS.rolePlay
                .replace('{characterName}', characterName)
                .replace('{personalityTraits}', description)
                .replace('{contextualInfo}', 'Personality trait mapping');

            // Return context for external System 2 invocation
            return {
                profileReady: false,
                characterName,
                description,
                personalityPrompt,
                cacheKey,
                fallbackPersonality: this.getDefaultPersonality()
            };

        } catch (error) {
            this.logger.error(`❌ Personality profile context preparation failed for ${characterName}:`, error);
            return {
                profileReady: true,
                cached: false,
                error: true,
                result: this.getDefaultPersonality()
            };
        }
    }

    /**
     * Store analysis result from external System 2 invocation
     * This method is called after System 2 has processed the analysis externally
     */
    async storeAnalysisResult(analysisContext, analysisResult) {
        try {
            const { characterId, cacheKey, memoryKey } = analysisContext;
            
            // Parse and structure the analysis
            const structuredAnalysis = this.parseCharacterAnalysis(analysisResult, analysisContext.characterData);

            // Store in LangGraph memory
            if (this.memoryManager && memoryKey) {
                try {
                    await this.memoryManager.addMemories(
                        memoryKey.userId,
                        {
                            content: structuredAnalysis,
                            characterId: memoryKey.characterId,
                            type: memoryKey.type,
                            name: memoryKey.name,
                            anime: memoryKey.anime,
                            metadata: {
                                type: 'character_analysis',
                                characterId,
                                analysisVersion: '1.0'
                            }
                        },
                        'character'
                    );

                    this.logger.debug(`💾 Character analysis stored in memory for ${memoryKey.name}`);
                } catch (error) {
                    this.logger.warn('Failed to store character analysis in memory:', error);
                }
            }

            // Also cache locally as fallback
            if (cacheKey) {
                this.analysisCache.set(cacheKey, {
                    data: structuredAnalysis,
                    timestamp: Date.now()
                });
            }

            this.logger.info(`✅ Character analysis stored for ${analysisContext.characterData?.name || 'unknown'}`);
            return structuredAnalysis;

        } catch (error) {
            this.logger.error('❌ Failed to store analysis result:', error);
            return this.generateFallbackAnalysis(analysisContext.characterData);
        }
    }

    /**
     * Store personality profile result from external System 2 invocation
     */
    async storePersonalityResult(profileContext, personalityResult) {
        try {
            const { characterName, cacheKey } = profileContext;
            const personalityTraits = this.parsePersonalityScores(personalityResult);

            // Cache the result
            if (cacheKey) {
                this.personalityCache.set(cacheKey, {
                    data: personalityTraits,
                    timestamp: Date.now()
                });
            }

            this.logger.info(`✅ Personality profile stored for ${characterName}`);
            return personalityTraits;

        } catch (error) {
            this.logger.error(`❌ Failed to store personality result for ${profileContext.characterName}:`, error);
            return this.getDefaultPersonality();
        }
    }

    // ============================================================================
    // UTILITY METHODS (Shared between both services)
    // ============================================================================

    /**
     * Generate personality description from traits
     */
    generatePersonalityDescription(personality) {
        const traits = [];
        
        if (personality.formality > 0.7) traits.push('formal and professional');
        else if (personality.formality < 0.3) traits.push('casual and relaxed');
        
        if (personality.enthusiasm > 0.7) traits.push('enthusiastic and energetic');
        else if (personality.enthusiasm < 0.3) traits.push('calm and measured');
        
        if (personality.empathy > 0.7) traits.push('empathetic and understanding');
        else if (personality.empathy < 0.3) traits.push('objective and analytical');
        
        if (personality.creativity > 0.7) traits.push('creative and imaginative');
        else if (personality.creativity < 0.3) traits.push('practical and straightforward');
        
        if (personality.directness > 0.7) traits.push('direct and clear');
        else if (personality.directness < 0.3) traits.push('diplomatic and tactful');

        return traits.length > 0 ? traits.join(', ') : 'balanced and adaptable';
    }

    /**
     * Generate behavior guidelines for character consistency
     */
    generateBehaviorGuidelines(character) {
        const guidelines = [];
        const p = character.personality;

        // Formality guidelines
        if (p.formality > 0.7) {
            guidelines.push('Use formal language and professional tone');
            guidelines.push('Address users with respect and courtesy');
        } else if (p.formality < 0.3) {
            guidelines.push('Use casual, friendly language');
            guidelines.push('Be conversational and approachable');
        }

        // Enthusiasm guidelines
        if (p.enthusiasm > 0.7) {
            guidelines.push('Show excitement and energy in responses');
            guidelines.push('Use positive, uplifting language');
        } else if (p.enthusiasm < 0.3) {
            guidelines.push('Maintain a calm, steady tone');
            guidelines.push('Focus on clear, measured responses');
        }

        // Empathy guidelines
        if (p.empathy > 0.7) {
            guidelines.push('Show understanding and compassion');
            guidelines.push('Acknowledge emotions and feelings');
        } else if (p.empathy < 0.3) {
            guidelines.push('Focus on facts and logical analysis');
            guidelines.push('Maintain objective perspective');
        }

        // Creativity guidelines
        if (p.creativity > 0.7) {
            guidelines.push('Offer creative solutions and ideas');
            guidelines.push('Think outside conventional approaches');
        } else if (p.creativity < 0.3) {
            guidelines.push('Provide practical, proven solutions');
            guidelines.push('Focus on established methods');
        }

        // Directness guidelines
        if (p.directness > 0.7) {
            guidelines.push('Give clear, straightforward answers');
            guidelines.push('Be honest and direct in communication');
        } else if (p.directness < 0.3) {
            guidelines.push('Use diplomatic language');
            guidelines.push('Consider feelings when delivering information');
        }

        return guidelines;
    }

    /**
     * Generate contextual cues for character behavior
     */
    generateContextualCues(character) {
        return {
            greetingStyle: this.generateGreetingStyle(character),
            questionHandling: this.generateQuestionHandling(character),
            problemSolving: this.generateProblemSolving(character),
            emotionalResponse: this.generateEmotionalResponse(character),
            conversationStyle: this.generateConversationStyle(character)
        };
    }

    /**
     * Validate character structure
     */
    validateCharacterStructure(character) {
        const required = ['id', 'name', 'description', 'personality', 'voiceStyle', 'systemPrompt'];
        const personalityTraits = ['formality', 'enthusiasm', 'empathy', 'creativity', 'directness'];

        // Check required fields
        for (const field of required) {
            if (!character[field]) {
                this.logger.error(`[CharacterService] Missing required field: ${field}`);
                return false;
            }
        }

        // Check personality traits
        for (const trait of personalityTraits) {
            if (typeof character.personality[trait] !== 'number' || 
                character.personality[trait] < 0 || 
                character.personality[trait] > 1) {
                this.logger.error(`[CharacterService] Invalid personality trait: ${trait}`);
                return false;
            }
        }

        return true;
    }

    /**
     * Parse character analysis from System 2 response
     */
    parseCharacterAnalysis(response, originalData) {
        try {
            // Try to parse as JSON first
            if (response.startsWith('{') || response.startsWith('[')) {
                const parsed = JSON.parse(response);
                return {
                    ...originalData,
                    analysis: parsed,
                    summary: parsed.overview || parsed.summary || '',
                    personality: parsed.personality || {},
                    speakingStyle: parsed.speakingStyle || '',
                    relationships: parsed.relationships || [],
                    rolePlayGuidelines: parsed.rolePlayGuidelines || ''
                };
            }

            // Parse structured text response
            const sections = this.extractSections(response);
            return {
                ...originalData,
                analysis: sections,
                summary: sections.overview || sections['Character Overview'] || '',
                personality: sections['Personality Analysis'] || {},
                speakingStyle: sections['Speaking Style'] || '',
                relationships: sections['Key Relationships'] || [],
                rolePlayGuidelines: sections['Role-Playing Guidelines'] || ''
            };

        } catch (error) {
            this.logger.warn('⚠️ Failed to parse structured analysis, using raw response');
            return {
                ...originalData,
                summary: response.slice(0, this.options.maxSummaryLength),
                analysis: { raw: response }
            };
        }
    }

    /**
     * Parse personality scores from System 2 response
     */
    parsePersonalityScores(response) {
        try {
            // Try direct JSON parse
            if (response.startsWith('{')) {
                return JSON.parse(response);
            }

            // Extract JSON from text response
            const jsonMatch = response.match(/\{[^}]+\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }

            // Parse structured text
            const traits = {};
            const lines = response.split('\n');
            for (const line of lines) {
                const match = line.match(/(\w+):\s*([\d.]+)/);
                if (match) {
                    traits[match[1]] = parseFloat(match[2]);
                }
            }

            return traits;

        } catch (error) {
            this.logger.warn('⚠️ Failed to parse personality scores, using defaults');
            return this.getDefaultPersonality();
        }
    }

    /**
     * Get default personality trait scores
     */
    getDefaultPersonality() {
        return {
            formality: 0.5,
            enthusiasm: 0.6,
            empathy: 0.6,
            creativity: 0.5,
            directness: 0.6,
            humor: 0.4,
            leadership: 0.5,
            analytical: 0.5
        };
    }

    // ============================================================================
    // ADDITIONAL UTILITY METHODS
    // ============================================================================

    generateGreetingStyle(character) {
        const p = character.personality;
        
        if (p.formality > 0.7 && p.enthusiasm < 0.5) {
            return 'formal_professional';
        } else if (p.formality < 0.3 && p.enthusiasm > 0.7) {
            return 'casual_enthusiastic';
        } else if (p.empathy > 0.7) {
            return 'warm_welcoming';
        } else {
            return 'balanced_friendly';
        }
    }

    generateQuestionHandling(character) {
        const p = character.personality;
        
        if (p.directness > 0.7 && p.creativity < 0.5) {
            return 'direct_factual';
        } else if (p.creativity > 0.7 && p.enthusiasm > 0.5) {
            return 'creative_exploratory';
        } else if (p.empathy > 0.7) {
            return 'understanding_supportive';
        } else {
            return 'comprehensive_balanced';
        }
    }

    generateProblemSolving(character) {
        const p = character.personality;
        
        if (p.creativity > 0.7) {
            return 'innovative_creative';
        } else if (p.directness > 0.7) {
            return 'systematic_direct';
        } else if (p.empathy > 0.7) {
            return 'collaborative_empathetic';
        } else {
            return 'analytical_methodical';
        }
    }

    generateEmotionalResponse(character) {
        const p = character.personality;
        
        if (p.empathy > 0.7 && p.enthusiasm > 0.5) {
            return 'highly_empathetic';
        } else if (p.empathy < 0.3) {
            return 'objective_analytical';
        } else if (p.formality > 0.7) {
            return 'professional_supportive';
        } else {
            return 'balanced_understanding';
        }
    }

    generateConversationStyle(character) {
        const p = character.personality;
        
        if (p.formality > 0.7) {
            return 'structured_professional';
        } else if (p.enthusiasm > 0.7 && p.creativity > 0.5) {
            return 'dynamic_engaging';
        } else if (p.empathy > 0.7) {
            return 'supportive_collaborative';
        } else {
            return 'natural_adaptive';
        }
    }

    enhanceSystemPrompt(character) {
        const basePrompt = character.systemPrompt;
        const personalityDesc = this.generatePersonalityDescription(character.personality);
        const guidelines = this.generateBehaviorGuidelines(character);
        
        return `${basePrompt}

CHARACTER PERSONALITY:
You embody the personality of "${character.name}" - ${character.description}. Your personality is ${personalityDesc}.

BEHAVIOR GUIDELINES:
${guidelines.map(guideline => `- ${guideline}`).join('\n')}

CONSISTENCY REQUIREMENTS:
- Maintain character consistency throughout the conversation
- Adapt responses to match your personality traits
- Consider the voice style: ${character.voiceStyle}
- Balance authenticity with helpfulness

Remember to stay true to this character while being helpful and accurate.`;
    }

    createSystem1Prompt(context) {
        return `You are the reactive system (System 1) for ${context.identity.name}.

IMMEDIATE RESPONSE CHARACTERISTICS:
- Personality: ${context.personality.description}
- Voice Style: ${context.communication.voiceStyle}
- Response Pattern: ${context.context.cues.conversationStyle}

REACTIVE GUIDELINES:
- Provide immediate, personality-consistent responses
- Match the emotional tone and energy level
- Maintain character voice and style
- React naturally to user input

Your responses should feel authentic and immediate while staying true to the character.`;
    }

    createSystem2Prompt(context) {
        return `You are the analytical system (System 2) for ${context.identity.name}.

ANALYTICAL RESPONSIBILITIES:
- Ensure response consistency with character personality
- Verify factual accuracy and helpfulness
- Monitor for character coherence
- Provide contextual analysis

CONSISTENCY RULES:
${context.personality.guidelines.map(rule => `- ${rule}`).join('\n')}

ANALYSIS FOCUS:
- Character consistency: Does the response match the personality?
- Contextual appropriateness: Is the response suitable for the situation?
- Quality assurance: Is the information accurate and helpful?

Balance analytical oversight with maintaining natural character expression.`;
    }

    generateCoordinationParameters(context) {
        const p = context.personality.traits;
        
        return {
            responseSpeed: p.enthusiasm > 0.7 ? 'fast' : p.formality > 0.7 ? 'measured' : 'balanced',
            analysisDepth: p.creativity > 0.7 ? 'creative' : p.directness > 0.7 ? 'focused' : 'comprehensive',
            consistencyWeight: p.empathy > 0.7 ? 0.8 : 0.7,
            adaptabilityFactor: this.calculateAdaptabilityScore(context.identity)
        };
    }

    calculateAdaptabilityScore(character) {
        const p = character.personality;
        
        // Higher adaptability for balanced personalities
        const balance = 1 - Math.abs(0.5 - p.formality) - Math.abs(0.5 - p.directness);
        const creativity = p.creativity;
        const empathy = p.empathy;
        
        return Math.min(1, (balance + creativity + empathy) / 3);
    }

    generateSystem1ResponseStyle(context) {
        const p = context.personality.traits;
        
        return {
            speed: p.enthusiasm > 0.7 ? 'quick' : 'thoughtful',
            tone: p.formality > 0.7 ? 'professional' : 'conversational',
            energy: p.enthusiasm,
            warmth: p.empathy,
            creativity: p.creativity
        };
    }

    generateConsistencyRules() {
        return [
            'Maintain personality trait alignment in all responses',
            'Ensure voice style consistency throughout conversation',
            'Verify emotional tone matches character empathy level',
            'Check formality level against character settings',
            'Validate creative expression aligns with character creativity',
            'Ensure directness level matches character preferences'
        ];
    }

    updatePersonalityProfile(character) {
        const profileKey = character.id;
        const profile = {
            character: { ...character },
            lastUsed: Date.now(),
            usageCount: (this.personalityProfiles.get(profileKey)?.usageCount || 0) + 1,
            consistencyScore: this.calculateConsistencyScore(character)
        };

        this.personalityProfiles.set(profileKey, profile);
    }

    calculateConsistencyScore(character) {
        // Base score on personality trait balance and coherence
        const p = character.personality;
        const traits = Object.values(p);
        
        // Check for extreme combinations that might be inconsistent
        const variance = this.calculateVariance(traits);
        const coherence = this.checkPersonalityCoherence(p);
        
        return Math.min(1, coherence - (variance * 0.3));
    }

    calculateVariance(values) {
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
        return squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;
    }

    checkPersonalityCoherence(personality) {
        let coherenceScore = 1.0;
        
        // Check for potentially conflicting traits
        if (personality.formality > 0.8 && personality.enthusiasm > 0.8) {
            coherenceScore -= 0.2; // High formality + high enthusiasm can be challenging
        }
        
        if (personality.directness > 0.8 && personality.empathy > 0.8) {
            coherenceScore -= 0.1; // Very direct + very empathetic needs balance
        }
        
        if (personality.creativity > 0.8 && personality.formality > 0.8) {
            coherenceScore -= 0.15; // High creativity + high formality can conflict
        }
        
        return Math.max(0, coherenceScore);
    }

    recordCharacterChange(character) {
        const record = {
            characterId: character.id,
            characterName: character.name,
            timestamp: Date.now(),
            personality: { ...character.personality },
            voiceStyle: character.voiceStyle
        };

        this.characterHistory.unshift(record);
        
        // Keep only last 50 changes
        if (this.characterHistory.length > 50) {
            this.characterHistory = this.characterHistory.slice(0, 50);
        }
    }

    extractSections(text) {
        const sections = {};
        const lines = text.split('\n');
        let currentSection = null;
        let currentContent = [];

        for (const line of lines) {
            const trimmed = line.trim();

            // Check if this is a section header
            if (trimmed.match(/^\d+\.\s+(.+):?$/) || trimmed.match(/^[A-Z][^:]+:$/)) {
                // Save previous section
                if (currentSection && currentContent.length > 0) {
                    sections[currentSection] = currentContent.join('\n').trim();
                }

                // Start new section
                currentSection = trimmed.replace(/^\d+\.\s+/, '').replace(/:$/, '');
                currentContent = [];
            } else if (currentSection && trimmed) {
                currentContent.push(trimmed);
            }
        }

        // Save final section
        if (currentSection && currentContent.length > 0) {
            sections[currentSection] = currentContent.join('\n').trim();
        }

        return sections;
    }

    generateFallbackAnalysis(characterData) {
        return {
            ...characterData,
            summary: `${characterData.name} from ${characterData.anime}. A character with unique traits and an interesting personality.`,
            analysis: {
                fallback: true,
                note: 'Generated using fallback analysis due to System 2 unavailability'
            },
            personality: this.getDefaultPersonality(),
            speakingStyle: 'Natural conversational style',
            relationships: [],
            rolePlayGuidelines: `Embody ${characterData.name} with authenticity and respect for the character's core traits.`
        };
    }

    // ============================================================================
    // ACCESSORS AND UTILITIES
    // ============================================================================

    getCurrentCharacter() {
        return this.currentCharacter ? { ...this.currentCharacter } : null;
    }

    getCharacterHistory() {
        return [...this.characterHistory];
    }

    getPersonalityProfiles() {
        return new Map(this.personalityProfiles);
    }

    getConsistencyMetrics() {
        return { ...this.consistencyMetrics };
    }

    updateConsistencyMetrics(metrics) {
        this.consistencyMetrics = {
            ...this.consistencyMetrics,
            ...metrics,
            lastUpdate: Date.now()
        };

        if (this.options.enablePersistence) {
            this.saveData();
        }
    }

    clearCache() {
        this.analysisCache.clear();
        this.personalityCache.clear();
        this.logger.info('🧹 Analysis caches cleared');
    }

    getCacheStats() {
        return {
            analysisCache: this.analysisCache.size,
            personalityCache: this.personalityCache.size,
            totalCached: this.analysisCache.size + this.personalityCache.size
        };
    }

    // ============================================================================
    // PERSISTENCE
    // ============================================================================

    loadStoredData() {
        if (!this.options.enablePersistence) return;

        try {
            const stored = localStorage.getItem(this.options.storageKey);
            if (stored) {
                const data = JSON.parse(stored);
                
                if (data.currentCharacter) {
                    this.currentCharacter = data.currentCharacter;
                }
                
                if (data.characterHistory) {
                    this.characterHistory = data.characterHistory;
                }
                
                if (data.personalityProfiles) {
                    this.personalityProfiles = new Map(data.personalityProfiles);
                }
                
                if (data.consistencyMetrics) {
                    this.consistencyMetrics = data.consistencyMetrics;
                }

                this.logger.info('[CharacterService] Loaded stored character data');
            }
        } catch (error) {
            this.logger.warn('[CharacterService] Failed to load stored data:', error);
        }
    }

    saveData() {
        if (!this.options.enablePersistence) return;

        try {
            const data = {
                currentCharacter: this.currentCharacter,
                characterHistory: this.characterHistory,
                personalityProfiles: Array.from(this.personalityProfiles.entries()),
                consistencyMetrics: this.consistencyMetrics,
                lastSaved: Date.now()
            };

            localStorage.setItem(this.options.storageKey, JSON.stringify(data));
        } catch (error) {
            this.logger.warn('[CharacterService] Failed to save data:', error);
        }
    }

    clearStoredData() {
        if (this.options.enablePersistence) {
            localStorage.removeItem(this.options.storageKey);
        }
        
        this.currentCharacter = null;
        this.characterHistory = [];
        this.personalityProfiles.clear();
        this.consistencyMetrics = {
            responseAlignment: 0,
            personalityStability: 0,
            contextualRelevance: 0
        };

        this.logger.info('[CharacterService] Cleared all stored data');
    }

    dispose() {
        if (this.options.enablePersistence) {
            this.saveData();
        }
        
        this.currentCharacter = null;
        this.characterHistory = [];
        this.personalityProfiles.clear();
        this.contextMemory.clear();
        this.analysisCache.clear();
        this.personalityCache.clear();
        
        this.logger.info('[CharacterService] Unified service disposed');
    }
}

export default CharacterService;