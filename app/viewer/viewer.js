import { Base } from '../base.js';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { SCENE_CONFIG, ANIMATION_CONFIG, ASSETS, UI_CONFIG, CONTROLS_CONFIG, ENVIRONMENT_CONFIG } from './viewerConfig.js';
import { FloatingEffect, TrackballEffect } from '../../src/effects/modelEffects.js';
// Server imports removed - using agent-based architecture
// import { api } from '@/server/api.ts';
// import { anyTo3DService as algorithmService } from '@/server/algorithms/anyto3d.ts';
// import { textTo3DService } from '@/server/algorithms/textto3d.ts';
// Remove UIModules import and directly import MeshSelector and CameraViewer
// Fix the import to use default import for PoseDetector
import PoseDetector, { DetectionMode } from '../../src/recognition/poseDetector.js';
import { Canvas2DVisualizer } from '../../src/ui/components/visualizers/canvas2DVisualizer.js';
import { GestureController } from './gestures.js';
import { UISettings } from './ui.js';
import { TalkingAvatar } from './talkingavatar.js';
import { detectAvatarMesh } from '../../src/viewer/detection/AvatarMeshDetector.ts';
import { PhotoCapture } from './photoCapture.js';
// QRCodeDisplay removed - mobile connection functionality no longer needed
// import { QRCodeDisplay } from '../../src/ui/components/QRCodeDisplay.js';
// connectionManager removed - using realtime mode directly
import { config } from '../../src/config/client.ts';
import { getDownloadServerPort } from '../../src/utils/portManager.js';
import { uiScalingManager } from './scaling.js';

// Check if we're running in mobile mode
// This is set when the app is launched with 'npm run launch:mobile viewer dev'
// We'll use this to determine if mobile control features should be enabled
window.isMobileEnabled = checkIfMobileEnabled();

/**
 * Check if the application is running in mobile mode
 * @returns {boolean} True if mobile mode is enabled
 */
function checkIfMobileEnabled() {
    // Check if we have a URL parameter indicating mobile mode
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('mobileEnabled')) {
        return urlParams.get('mobileEnabled') === 'true';
    }

    // Check if we're running on the mobile-specific port
    // The mobile version typically runs on port 3002
    const isMobilePort = window.location.port === '3002';

    // Check if we have any other indicators in the environment
    // For example, check if the WebSocket server is running on port 3000
    // But we'll do this check passively to avoid console errors

    // For now, we'll assume mobile mode is disabled unless explicitly enabled
    console.log(`[Viewer] Mobile mode is ${isMobilePort ? 'enabled' : 'disabled'} based on port check`);
    return isMobilePort;
}

export class Viewer extends Base {
    constructor(container, params = {}) {
        // Get environment configuration from viewerConfig.js
        const environmentConfig = {
            environment: {
                ...ENVIRONMENT_CONFIG,
                ...params.environment
            }
        };

        // Merge scene config with params and environment config
        const mergedParams = {
            ...SCENE_CONFIG,
            ...environmentConfig,
            ...params,
            rendererOptions: {
                antialias: true,
                alpha: true,
                ...params.rendererOptions
            }
        };
        super(mergedParams);
        this.container = container;
        this.objects = new Map();
        this.effects = new Map();
        this.animations = new Map();
        this.currentProgress = 0;
        this.isGenerating = false;
        this.config = ASSETS.SAVE_OPTIONS;
        this.skeletalAnimationManager = null;

        // Replace gesture control state with GestureController
        this.gestureController = null;

        // Add UI settings
        this.uiSettings = new UISettings(this);

        // Initialize talking avatar
        this.talkingAvatar = new TalkingAvatar(this);

        // Initialize tripo-doll capture
        this.photoCapture = null;

        // QR code display for mobile control removed - using realtime mode
        // this.qrCodeDisplay = null;
    }

    /**
     * Set up connection event handlers for mobile synchronization
     * @private
     */
    _setupConnectionHandlers() {
        // Connection manager removed - mobile functionality no longer used
        // All mobile device handling has been removed in favor of realtime mode
        console.log('[Viewer] Connection handlers setup skipped - using realtime mode');
    }

    /**
     * Send current UI state to connected mobile devices
     * @private
     */
    _sendUIState() {
        // First check if connection is ready before preparing data
        if (!this._isConnectionReady()) {
            // Skip silently without logging to avoid console spam
            return;
        }

        console.log('[Viewer] Preparing UI state to send');

        // Get the current mesh selector state if available
        let meshSelectorState = null;
        if (this.uiSettings?.components?.meshSelector) {
            const meshSelector = this.uiSettings.components.meshSelector;
            console.log('[Viewer] MeshSelector found:', meshSelector);
            console.log('[Viewer] MeshSelector meshes:', meshSelector.meshes);
            console.log('[Viewer] MeshSelector selectedMesh:', meshSelector.selectedMesh);

            // Create a deep copy of the meshes array to ensure it's properly serialized
            const meshesArray = Array.isArray(meshSelector.meshes) ?
                meshSelector.meshes.map(mesh => ({
                    id: mesh.id,
                    name: mesh.name
                })) : [];

            console.log('[Viewer] Processed meshes for sync:', meshesArray);

            meshSelectorState = {
                meshes: meshesArray,
                selectedMesh: meshSelector.selectedMesh || null,
                visible: meshSelector.isVisible || false
            };
        } else {
            console.log('[Viewer] No MeshSelector found in uiSettings.components');
        }

        // Get the current actions menu state if available
        let actionsMenuState = null;
        if (this.uiSettings?.components?.actionsMenu) {
            const actionsMenu = this.uiSettings.components.actionsMenu;
            console.log('[Viewer] ActionsMenu found:', actionsMenu);

            actionsMenuState = {
                visible: actionsMenu.dropdown.classList.contains('visible')
            };
        } else {
            console.log('[Viewer] No ActionsMenu found in uiSettings.components');
        }

        // Check if we have any objects in the scene
        console.log('[Viewer] Objects in scene:', this.objects.size);
        if (this.objects.size > 0) {
            console.log('[Viewer] Objects:', Array.from(this.objects.entries()));
        }

        const uiState = {
            isGenerating: this.isGenerating || false,
            statusMessage: this.uiSettings?.statusText?.textContent || 'Ready',
            hasModel: this.objects.size > 0,
            meshSelector: meshSelectorState,
            actionsMenu: actionsMenuState,
            // Add any other UI elements that should be synchronized
            vrAvailable: !!this.vrButton || !!document.getElementById('VRButton'),
            // Add additional UI state information
            currentScene: {
                name: this.scene?.name || 'Main Scene',
                objectCount: this.scene?.children?.length || 0
            },
            // Add camera information
            camera: {
                position: this.camera ? {
                    x: this.camera.position.x,
                    y: this.camera.position.y,
                    z: this.camera.position.z
                } : null,
                rotation: this.camera ? {
                    x: this.camera.rotation.x,
                    y: this.camera.rotation.y,
                    z: this.camera.rotation.z
                } : null
            }
        };

        console.log('[Viewer] UI state prepared but mobile connections disabled - using realtime mode');
        // Mobile connections removed - using realtime mode
    }

    /**
     * Send current mesh information to connected mobile devices
     * @private
     */
    _sendMeshInfo() {
        // First check if connection is ready before preparing data
        if (!this._isConnectionReady()) {
            // Skip silently without logging to avoid console spam
            return;
        }

        console.log('[Viewer] Preparing mesh info to send');
        console.log('[Viewer] Objects in scene:', this.objects.size);

        // Get the current mesh information
        let meshInfo = {
            hasMesh: false
        };

        // Find the first mesh in the scene
        if (this.objects.size > 0) {
            console.log('[Viewer] Objects:', Array.from(this.objects.entries()));

            for (const [id, object] of this.objects) {
                console.log(`[Viewer] Found mesh: ${id}, name: ${object.name}, type: ${object.type}`);

                // Get the full URL for the mesh using the window.location
                const protocol = window.location.protocol;
                const hostname = window.location.hostname || 'localhost';
                const port = getDownloadServerPort();

                // Construct the full URL for the mesh
                // Make sure to use the correct path format
                let meshPath = '';
                if (id.startsWith('assets/')) {
                    // If the ID already includes the assets prefix, don't add it again
                    meshPath = id;
                } else {
                    // Otherwise, add the assets/meshes prefix
                    meshPath = `assets/meshes/${id}`;
                }

                const fullMeshUrl = `${protocol}//${hostname}:${port}/${meshPath}`;
                console.log(`[Viewer] Constructed mesh URL: ${fullMeshUrl}`);

                meshInfo = {
                    hasMesh: true,
                    meshId: id,
                    meshName: object.name || id,
                    meshType: object.type,
                    meshUrl: fullMeshUrl // Use the full URL for the mesh
                };
                break;
            }
        } else {
            console.log('[Viewer] No meshes found in the scene');
        }

        console.log('[Viewer] Mesh info prepared but mobile connections disabled - using realtime mode');
        // Mobile connections removed - using realtime mode

        // Also send camera state when mesh info is sent
        this._sendCameraState();
    }

    /**
     * Check if connection manager is connected and ready to send messages
     * @private
     * @returns {boolean} Whether the connection manager is ready
     */
    _isConnectionReady() {
        // Mobile connection functionality removed - using realtime mode
        return false; // Always return false since mobile connections are disabled
    }

    /**
     * Send current camera state to connected mobile devices
     * @private
     */
    _sendCameraState() {
        // First check if connection is ready before preparing data
        if (!this._isConnectionReady()) {
            // Skip silently without logging to avoid console spam
            return;
        }

        console.log('[Viewer] Preparing camera state to send');

        if (!this.camera) {
            console.log('[Viewer] No camera found, cannot send camera state');
            return;
        }

        if (!this.controls) {
            console.log('[Viewer] No controls found, cannot send camera state');
            return;
        }

        const cameraState = {
            position: {
                x: this.camera.position.x,
                y: this.camera.position.y,
                z: this.camera.position.z
            },
            rotation: {
                x: this.camera.rotation.x,
                y: this.camera.rotation.y,
                z: this.camera.rotation.z
            },
            target: {
                x: this.controls.target.x,
                y: this.controls.target.y,
                z: this.controls.target.z
            }
        };

        console.log('[Viewer] Camera state prepared but mobile connections disabled - using realtime mode');
        // Mobile connections removed - using realtime mode
    }

    async initViewer() {
        try {
            // Initialize base class
            await this.init_base();

            // Environment setup is now handled in base.js
            // The thin box environment is created in base.js setupEnvironment method

            // Gradio client initialization disabled - using agent-based architecture
            console.log('3D generation services now handled through agent system');
            // Legacy Gradio/server-based 3D generation has been replaced with agent-based tools

            // Create video element first - this is critical
            console.log('[Viewer] Setting up video element');
            const video = document.createElement('video');
            video.id = 'input-video';
            video.autoplay = true;
            video.playsInLine = true;
            video.muted = true;
            video.style.display = 'none';
            document.body.appendChild(video);
            const rpmStyles = document.createElement('style');
            rpmStyles.textContent = `
                .rpm-modal {
                    transition: opacity 0.3s ease;
                }
                .rpm-frame {
                    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
                    transition: transform 0.3s ease;
                }
                .rpm-modal button:hover {
                    opacity: 0.8;
                }
            `;
            document.head.appendChild(rpmStyles);
            // Store in inputManager
            if (!this.inputManager) {
                this.inputManager = {};
            }
            this.inputManager.videoElement = video;

            // Load default assets
            await this.loadDefaultAssets();

            // Setup UI components
            this.setupUI();

            // Initialize UI settings after video element is created
            this.uiSettings.initialize();

            // Initialize talking avatar with timeout to prevent hanging
            try {
                const avatarInitPromise = this.talkingAvatar.initialize();
                // Set a timeout for avatar initialization
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('TalkingAvatar initialization timeout')), 5000);
                });

                await Promise.race([avatarInitPromise, timeoutPromise]);
                console.log('[Viewer] TalkingAvatar initialized successfully');
            } catch (error) {
                console.warn('[Viewer] TalkingAvatar initialization failed or timed out:', error.message);
                // Continue with initialization even if TalkingAvatar fails
            }

            // Initialize tripo-doll capture
            try {
                this.photoCapture = new PhotoCapture(this);
                await this.photoCapture.initialize();
                console.log('[Viewer] photoCapture initialized successfully');
            } catch (error) {
                console.warn('[Viewer] photoCapture initialization failed:', error.message);
                // Continue with initialization even if photoCapture fails
            }

            // Connection manager removed - mobile functionality replaced with realtime mode
            this._setupConnectionHandlers();

            // Disable mobile control menu item since we no longer use QR mobile connections
            if (this.uiSettings && typeof this.uiSettings.updateMobileControlMenuItem === 'function') {
                this.uiSettings.updateMobileControlMenuItem(false);
            }

            // Setup orbital controls
            this.setupControls();

            // Initialize gesture control if available
            await this.initializeGestureControls();

            // Start rendering
            this.startRenderLoop();

            console.log('Viewer initialized successfully');
        } catch (error) {
            console.error('Failed to initialize viewer:', error);
            throw error;
        }
    }

    setupUI() {
        // Basic UI setup if needed
        console.log('Setting up basic UI...');

        // Setup progress container
        this.progressContainer = document.createElement('div');
        this.progressContainer.className = 'progress-container';
        this.progressContainer.id = UI_CONFIG.progress.containerId;
        this.progressContainer.innerHTML = UI_CONFIG.progress.template;
        document.body.appendChild(this.progressContainer);

        // Get progress elements
        this.progressBar = this.progressContainer.querySelector('.progress-fill');
        this.progressText = this.progressContainer.querySelector('.progress-text');
        this.statusText = this.progressContainer.querySelector('.status-text');
        this.cancelButton = this.progressContainer.querySelector('.cancel-button');

        // Add cancel handler
        this.cancelButton.addEventListener('click', () => this.cancelGeneration());

        // Initialize UI with config
        if (!this.container) {
            console.error('Viewer container not found');
            return;
        }

        // Let UISettings handle component initialization
        if (this.uiSettings) {
            this.uiSettings.initializeComponents(UI_CONFIG);

            // Store references to components for direct access
            this.meshSelector = this.uiSettings.components.meshSelector;
            this.cameraViewer = this.uiSettings.components.cameraViewer;

            // Add event listener to send UI updates when mesh selector changes
            if (this.meshSelector) {
                // Add a MutationObserver to detect changes to the mesh selector
                const observer = new MutationObserver(() => {
                    // Send UI state to mobile devices
                    this._sendUIState();
                });

                // Observe changes to the mesh selector
                observer.observe(this.meshSelector.container, {
                    childList: true,
                    subtree: true,
                    attributes: true
                });

                // Also observe the select element for value changes
                if (this.meshSelector.select) {
                    this.meshSelector.select.addEventListener('change', (event) => {
                        // Get the selected mesh ID
                        const selectedMeshId = event.target.value;

                        // Process mesh selection
                        if (selectedMeshId) {
                            this._processMeshSelection(selectedMeshId);
                        }

                        // Send UI state to mobile devices
                        this._sendUIState();

                        // Also send mesh info
                        this._sendMeshInfo();
                    });
                }
            }

            // Store reference to gesture control elements for direct access
            if (this.uiSettings.components.gestureControl) {
                this.gestureControlContainer = this.uiSettings.components.gestureControl.container;
                this.gestureToggleButton = this.uiSettings.components.gestureControl.button;
                this.gestureLabel = this.uiSettings.components.gestureControl.label;
            }
        } else {
            console.error('UI Settings not initialized');
        }
    }
    _processMeshSelection(selectedMeshId) {
        console.log('[Viewer] Processing mesh selection:', selectedMeshId);
    }
    setupUIEvents() {
        if (!this.container) {
            console.error('Container not found');
            return;
        }

        // Create conversion modal
        const modal = document.createElement('div');
        modal.className = 'conversion-modal hidden';

        const content = document.createElement('div');
        content.className = 'conversion-content';

        // Create source type wrapper for horizontal layout
        const sourceWrapper = document.createElement('div');
        sourceWrapper.className = 'source-wrapper';

        // Source type selector
        const sourceSelect = document.createElement('select');
        sourceSelect.className = 'source-select';
        sourceSelect.innerHTML = `
            <option value="text">Text to 3D</option>
            <option value="image">Image to 3D</option>
            <option value="doll">Doll to 3D</option>
        `;

        // Create regenerate checkbox wrapper with icon
        const checkboxWrapper = document.createElement('div');
        checkboxWrapper.className = 'regenerate-wrapper';

        this.regenerateCheckbox = document.createElement('input');
        this.regenerateCheckbox.type = 'checkbox';
        this.regenerateCheckbox.id = 'regenerate-checkbox';
        this.regenerateCheckbox.className = 'regenerate-checkbox';

        // Add change listener to regenerate checkbox
        this.regenerateCheckbox.addEventListener('change', (e) => {
            if (e.target.checked) {
                console.log('[Viewer] Regenerate enabled - will use random seed on next generation');
            } else {
                console.log('[Viewer] Regenerate disabled - will use default seed on next generation');
            }
        });

        const checkboxLabel = document.createElement('label');
        checkboxLabel.htmlFor = 'regenerate-checkbox';
        checkboxLabel.className = 'regenerate-label';
        // Using a refresh/sync icon
        checkboxLabel.innerHTML = `
            <svg class="regenerate-icon" viewBox="0 0 24 24" width="18" height="18">
                <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
            </svg>
            <span class="tooltip">Regenerate with new seed</span>
        `;

        // Add elements to their containers
        checkboxWrapper.appendChild(this.regenerateCheckbox);
        checkboxWrapper.appendChild(checkboxLabel);

        sourceWrapper.appendChild(sourceSelect);
        sourceWrapper.appendChild(checkboxWrapper);

        // Input area
        const inputArea = document.createElement('div');
        inputArea.className = 'input-area';

        const textInput = document.createElement('textarea');
        textInput.placeholder = 'Enter description for 3D model...';
        textInput.className = 'text-input';

        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'image/*,.glb';
        fileInput.className = 'file-input hidden';

        const preview = document.createElement('div');
        preview.className = 'preview';

        // Buttons
        const generateButton = document.createElement('button');
        generateButton.textContent = 'Generate';
        generateButton.className = 'generate-button';

        const cancelButton = document.createElement('button');
        cancelButton.textContent = 'Cancel';
        cancelButton.className = 'cancel-button';

        // Add elements to modal
        inputArea.appendChild(textInput);
        inputArea.appendChild(fileInput);
        inputArea.appendChild(preview);

        content.appendChild(sourceWrapper);
        content.appendChild(inputArea);
        content.appendChild(generateButton);
        content.appendChild(cancelButton);

        modal.appendChild(content);
        this.container.appendChild(modal);

        // Event handlers
        sourceSelect.addEventListener('change', () => {
            const source = sourceSelect.value;
            console.log('[Viewer] Source changed to:', source);

            // Hide all input elements first
            textInput.classList.add('hidden');
            fileInput.classList.add('hidden');

            // Find camera button and gender wrapper
            const cameraButton = modal.querySelector('.camera-button');
            const genderWrapper = modal.querySelector('.gender-wrapper');

            // Always hide camera button and gender selector first
            if (cameraButton) cameraButton.classList.add('hidden');
            if (genderWrapper) genderWrapper.classList.add('hidden');

            // Show appropriate elements based on source
            if (source === 'text') {
                textInput.classList.remove('hidden');
                // Explicitly ensure camera button and gender selector are hidden for Text to 3D
                if (cameraButton) cameraButton.classList.add('hidden');
                if (genderWrapper) genderWrapper.classList.add('hidden');
            } else {
                fileInput.classList.remove('hidden');
            }

            // Show camera button and gender selector ONLY for Tripo Doll
            if (source === 'tripo-doll') {
                if (cameraButton) {
                    cameraButton.classList.remove('hidden');
                    cameraButton.innerHTML = '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path><circle cx="12" cy="13" r="4"></circle></svg> Take Tripo Doll Photo';
                }
                if (genderWrapper) genderWrapper.classList.remove('hidden');
            }

            // Set appropriate file accept types based on source
            switch (source) {
                case 'image':
                    fileInput.accept = 'image/*';
                    break;
                case 'doll':
                    fileInput.accept = 'image/*';  // Doll to 3D also accepts images
                    break;
                case 'tripo-doll':
                    fileInput.accept = 'image/*';  // Tripo Doll accepts images
                    break;
                default:
                    fileInput.accept = '';
            }

            // Clear preview and file input
            preview.innerHTML = '';
            fileInput.value = '';
        });

        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        preview.innerHTML = `<img src="${e.target.result}" alt="Preview">`;
                    };
                    reader.readAsDataURL(file);
                } else {
                    preview.innerHTML = `<div class="file-name">${file.name}</div>`;
                }
            }
        });

        generateButton.addEventListener('click', async () => {
            const source = sourceSelect.value;
            let input;

            if (source === 'text') {
                input = textInput.value.trim();
                if (!input) {
                    alert('Please enter a description');
                    return;
                }
            } else {
                const file = fileInput.files[0];
                if (!file) {
                    alert('Please select a file');
                    return;
                }
                input = await this.uploadFile(file);
            }
            await this.generate3DModel(source, input);
        });

        cancelButton.addEventListener('click', () => {
            console.log('[Viewer] Cancel button clicked in modal, isGenerating:', this.isGenerating);
            if (this.isGenerating) {
                this.cancelGeneration();
            }
            modal.classList.add('hidden');
        });

        // Add convert button to main UI
        const convertButton = document.createElement('button');
        convertButton.textContent = 'Convert to 3D';
        convertButton.className = 'convert-button';
        convertButton.addEventListener('click', () => {
            modal.classList.remove('hidden');
            textInput.value = '';
            fileInput.value = '';
            preview.innerHTML = '';
            sourceSelect.value = 'text';
            textInput.classList.remove('hidden');
            fileInput.classList.add('hidden');

            // Ensure camera button and gender selector are hidden for Text to 3D
            const cameraButton = modal.querySelector('.camera-button');
            const genderWrapper = modal.querySelector('.gender-wrapper');
            if (cameraButton) cameraButton.classList.add('hidden');
            if (genderWrapper) genderWrapper.classList.add('hidden');

            // Double-check that camera and gender are hidden for Text to 3D
            if (cameraButton && sourceSelect.value === 'text') {
                cameraButton.classList.add('hidden');
            }
            if (genderWrapper && sourceSelect.value === 'text') {
                genderWrapper.classList.add('hidden');
            }

            // Trigger the change event to ensure proper UI state
            const event = new Event('change');
            sourceSelect.dispatchEvent(event);
        });

        this.container.appendChild(convertButton);


    }

    async uploadFile(file) {
        // Convert file to proper format for image/doll processing
        return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                // Format the image data as required by the API
                const imageData = {
                    url: e.target.result,
                    name: file.name,
                    type: file.type,
                    size: file.size,
                };
                resolve(imageData);
            };
            reader.readAsDataURL(file);
        });
    }

    async generate3DModel(source, input, options = {}) {
        try {
            this.isGenerating = true;
            // Update UI generating state
            this.uiSettings.setGeneratingState(true);
            this.uiSettings.showProgress();

            // Update seed in AssetLoader
            this.assetLoader.setSeed(this.getCurrentSeed(), this.regenerateCheckbox?.checked || false);

            // Try to get from cache first (for all sources)
            const cachedResult = await this.assetLoader.getCachedModel(source, input);

            this.fetchMeshes();
            var meshUrl = null;
            if (cachedResult) {
                this.uiSettings.updateStatus('Loading cached model...');

                try {
                    // For cached results, use the URL directly
                    meshUrl = typeof cachedResult.meshResult === 'object' ?
                        cachedResult.meshResult.url : cachedResult.meshResult;

                    if (!meshUrl) {
                        throw new Error('No valid mesh URL in cached result');
                    }

                    await this.loadAssets({
                        meshes: {
                            generated: meshUrl,
                        }
                    }, ASSETS.RELOAD_OPTIONS);

                    this.uiSettings.updateStatus('Loaded from cache!');
                    setTimeout(() => this.uiSettings.hideProgress(), 1000);

                    // For tripo-doll, add additional metadata to the mesh
                    if (source === 'tripo-doll') {
                        const meshObject = this.objects.get('avatar');
                        if (meshObject) {
                            meshObject.userData = {
                                ...meshObject.userData,
                                type: 'tripo-doll',
                                gender: options.gender || 'boy',
                            };
                        }
                    }

                    return cachedResult;
                } catch (error) {
                    console.warn('Failed to load cached model, falling back to generation:', error);
                }
            }
            else {

                // Set appropriate status message based on source
                const statusMessage = 'Starting generation...';
                this.uiSettings.updateStatus(statusMessage);

                // Prepare request parameters based on source
                const requestParams = {
                    source,
                    input: input,
                    saveOptions: ASSETS.SAVE_OPTIONS
                };

                // Add source-specific parameters
                if (source === 'tripo-doll') {
                    requestParams.gender = options.gender || 'boy';
                } else {
                    requestParams.seed = this.getCurrentSeed();
                    requestParams.predict = false;
                }

                // 3D generation services now integrated with agent system
                console.log(`[Viewer] 3D generation (${source}) now handled through agent tools`);
                this.uiSettings.updateStatus('3D generation moved to agent system - use TalkingAvatar tools', true);
                setTimeout(() => this.uiSettings.hideProgress(), 3000);

                // Fallback: return error to indicate service migration
                const result = {
                    success: false,
                    error: '3D generation services have been migrated to the agent system. Use TalkingAvatar with agent mode for 3D content generation.',
                    data: null
                };

                this.isGenerating = false;
                this.uiSettings.setGeneratingState(false);

                if (!result.success) {
                    this.uiSettings.updateStatus(result.error || 'Generation failed', true);
                    setTimeout(() => this.uiSettings.hideProgress(), 2000);
                    return null;
                }

                // Process successful result
                console.log(`[Viewer] ${source} Result:`, result.data);

                // Extract mesh result and handle both string and object formats
                let meshResult;
                if (source === 'textto3d') {
                    // For TextTo3D service, use the model3d result or downloadGlb1
                    meshResult = result.data.model3d || result.data.downloadGlb1;
                } else {
                    // For other services, use rigged result by default
                    meshResult = result.data.riggingResult;
                }

                if (!meshResult) {
                    this.uiSettings.updateStatus('No mesh result in response data', true);
                    setTimeout(() => this.uiSettings.hideProgress(), 2000);
                    return null;
                }

                // Check if meshResult is an object with url property or a string
                meshUrl = typeof meshResult === 'object' ? meshResult.url : meshResult;
                if (!meshUrl) {
                    this.uiSettings.updateStatus('No valid mesh URL was generated', true);
                    setTimeout(() => this.uiSettings.hideProgress(), 2000);
                    return null;
                }

                // Cache the successful result for all sources
                await this.assetLoader.cacheModel(source, input, result.data);
            }

            this.fetchMeshes();

            try {
                this.uiSettings.updateStatus('Loading generated model...');

                // Load the assets with the local path
                await this.loadAssets({
                    meshes: {
                        generated: meshUrl
                    }
                }, ASSETS.RELOAD_OPTIONS);

                this.uiSettings.updateStatus('Generation complete!');
                setTimeout(() => this.uiSettings.hideProgress(), 1000);
                return;
            } catch (error) {
                this.uiSettings.updateStatus('Failed to save or load the model', true);
                console.error('Error processing mesh:', error);
                setTimeout(() => this.uiSettings.hideProgress(), 2000);
                return null;
            }
        } catch (error) {
            this.isGenerating = false;
            this.uiSettings.setGeneratingState(false);
            console.error('Error in generate3DModel:', error);
            this.uiSettings.updateStatus(error.message || 'Failed to generate 3D model', true);
            setTimeout(() => this.uiSettings.hideProgress(), 2000);
            return null;
        }
    }

    async fetchMeshes() {
        if (this.meshSelector) {
            this.meshSelector.fetchMeshes();
        }
    }

    async loadDefaultAssets() {
        try {
            const assets = {
                // Environment is handled in base.js setupEnvironment method
                // Just load meshes and set background color
                // backgroundColor: ASSETS.BACKGROUND_COLOR || '#000000', // Default to black if not defined
                meshes: {}
            };

            this.loadAssets(assets, ASSETS.RELOAD_OPTIONS);

            console.log('[Viewer] Default assets loaded');

        } catch (error) {
            console.error('Failed to load assets:', error);
            // Emit error event for UI handling
            this.dispatchEvent(new CustomEvent('error', { detail: error }));
        }
    }

    // Override base class method to add animation effects
    async setupObject(id, object) {
        // Call base class setupObject first
        await super.setupObject(id, object);

        // Check if the loaded object could be an avatar with skeleton (and optionally morph targets)
        if (this.talkingAvatar) {
            // Use a timeout to ensure the object is fully loaded and attached to the scene
            setTimeout(() => {
                const detectionResult = detectAvatarMesh(object, {
                    logDetection: true,
                    onMeshFound: (mesh, analysis) => {
                        // Update the talkingAvatar references
                        this.talkingAvatar.avatarMesh = mesh;
                        if (mesh.morphTargetDictionary && mesh.morphTargetInfluences) {
                            this.talkingAvatar.morphTargetDictionary = mesh.morphTargetDictionary;
                            this.talkingAvatar.morphTargetInfluences = mesh.morphTargetInfluences;
                        }
                    }
                });
                if (detectionResult.found) {
                    if (detectionResult.hasMorphTargets) {
                        console.log(`[Viewer] Detected ${detectionResult.meshType} with morph targets in object ${id}`);

                        // Update UI if needed
                        if (this.uiSettings) {
                            this.uiSettings.updateStatus(`Detected ${detectionResult.meshType} with morph targets`, false);
                        }
                    } else {
                        console.log(`[Viewer] Detected ${detectionResult.meshType} with skeleton only in object ${id}`);

                        // Update UI if needed
                        if (this.uiSettings) {
                            this.uiSettings.updateStatus(`Detected ${detectionResult.meshType} with skeleton only`, false);
                        }
                    }
                }
            }, 100);
        }

        // Add viewer-specific setup (like effects) here
        const config = ANIMATION_CONFIG[id.toUpperCase()];
        if (config?.EFFECTS) {
            const effects = [];
            for (const [_, effectConfig] of Object.entries(config.EFFECTS)) {
                switch (effectConfig.type) {
                    case 'FLOATING':
                        effects.push(new FloatingEffect(effectConfig.params));
                        break;
                    case 'TRACKBALL':
                        effects.push(new TrackballEffect(effectConfig.params));
                        break;
                }
            }
            this.effects.set(id, effects);
        }

        // Notify mobile devices about the new mesh
        setTimeout(() => {
            this._sendMeshInfo();
        }, 500);

        return object;
    }

    // Override base class method
    startRenderLoop() {
        // Use SceneManager if available
        if (this.sceneManager) {
            // Add custom update to SceneManager's animation loop
            const originalUpdate = this.sceneManager.update.bind(this.sceneManager);
            this.sceneManager.update = () => {
                if (this.controls) {
                    this.controls.update();
                }
                this.updateEffects();
                originalUpdate();
            };

            // Start the render loop
            this.sceneManager.start();
        } else {
            // Fallback to old method for backward compatibility
            this.renderer.setAnimationLoop(() => {
                if (this.controls) {
                    this.controls.update();
                }
                this.updateEffects();

                // Environment rendering is now handled in base.js render method
                super.render(); // Call base class render
            });
        }
    }

    updateEffects() {
        // const time = this.clock.getElapsedTime();
        // for (const [id, object] of this.objects) {
        //     const effects = this.effects.get(id);
        //     if (effects) {
        //         effects.forEach(effect => effect.update(object, time));
        //     }
        // }
    }
    // Cleanup method
    dispose() {
        // Stop gesture detection
        if (this.gestureController) {
            this.gestureController.dispose();
            this.gestureController = null;
        }

        // Clean up UI settings
        if (this.uiSettings) {
            this.uiSettings.dispose();
            this.uiSettings = null;
        }

        // Clean up UI scaling manager
        if (uiScalingManager) {
            uiScalingManager.dispose();
        }

        // QR code display cleanup removed - mobile functionality no longer used
        // if (this.qrCodeDisplay) {
        //     this.qrCodeDisplay.dispose();
        //     this.qrCodeDisplay = null;
        // }

        // Scene environment cleanup is now handled in base.js dispose method

        // Disconnect from WebSocket
        // connectionManager.disconnect(); // connectionManager removed

        if (this.controls) {
            this.controls.dispose();
        }
        super.dispose(); // Call Base's dispose method
        // Add any Viewer-specific cleanup here if necessary
        // First cleanup viewer-specific resources
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
        }
        // Clear viewer-specific collections
        this.objects?.clear();
        this.effects?.clear();

        // Stop all animations
        for (const animation of this.animations.values()) {
            animation.stop();
        }
        this.animations.clear();
    }

    cancelGeneration() {
        console.log('[Viewer] Cancelling generation, isGenerating:', this.isGenerating);
        if (this.isGenerating) {
            this.isGenerating = false;
            this.uiSettings.setGeneratingState(false);

            // Legacy server-based generation services removed
            // Generation cancellation now handled through agent system
            console.log('[Viewer] Legacy 3D generation services no longer available');

            this.uiSettings.hideProgress();
            this.uiSettings.updateStatus('Generation cancelled - use agent system for 3D generation', true);
            console.log('[Viewer] Generation cancelled');
        }
    }

    getCurrentSeed() {
        return this.assetLoader.getCurrentSeed();
    }

    showError(message) {
        this.uiSettings.showNotification(message, true);
    }

    setupControls() {
        // Ensure camera and renderer are available before setting up controls
        if (!this.camera) {
            console.warn('[Viewer] Camera not initialized, cannot setup controls');
            return false;
        }

        if (!this.renderer || !this.renderer.domElement) {
            console.warn('[Viewer] Renderer not initialized, cannot setup controls');
            return false;
        }

        // Skip if controls are already initialized
        if (this.controls) {
            console.log('[Viewer] Controls already initialized');
            return true;
        }

        // Create and configure orbital controls
        this.controls = new OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableRotate = true;
        
        // Apply configuration from CONTROLS_CONFIG
        Object.entries(CONTROLS_CONFIG).forEach(([key, value]) => {
            if (key === 'target') {
                this.controls.target.set(value.x, value.y, value.z);
            } else {
                this.controls[key] = value;
            }
        });

        // Add change event listener to send camera updates to mobile devices
        this.controls.addEventListener('change', () => {
            // Throttle camera updates to avoid flooding the WebSocket connection
            if (!this._cameraUpdateTimeout) {
                this._cameraUpdateTimeout = setTimeout(() => {
                    this._sendCameraState();
                    this._cameraUpdateTimeout = null;
                }, 100); // Send updates at most every 100ms
            }
        });

        this.controls.update();
        console.log('[Viewer] Controls setup completed successfully');
        return true;
    }



    /**
     * Transform the current avatar to a TalkingHead implementation
     * Uses consolidated TalkingHeadTransformationService for consistent implementation
     * @returns {Promise<boolean>} Success status
     */
    async transformToTalkingHead() {
        if (!this.talkingAvatar) {
            console.error('[Viewer] TalkingAvatar not initialized');
            this.showError('Avatar system not initialized');
            return false;
        }

        try {
            // Import the consolidated transformation service
            const { TalkingHeadTransformationService } = await import('./services/talkingHeadTransformationService.js');

            // Create service instance for direct use
            if (!this.transformationService) {
                this.transformationService = new TalkingHeadTransformationService();
            }

            // Check if we're already in TalkingHead mode
            if (this.transformationService.isInTalkingHeadMode()) {
                console.log('[Viewer] Already in TalkingHead mode, exiting');
                await this.transformationService.dispose();
                return true;
            }

            // Get reference to currently selected mesh for proper cleanup
            const currentMeshId = this.meshSelector?.select?.value || null;
            const currentMesh = currentMeshId ?
                this.objects.get('selected') :
                null;

            console.log('[Viewer] Transforming mesh to TalkingHead using consolidated service, current mesh:',
                currentMesh ? currentMesh.name || 'unnamed' : 'none');

            // Store the original controls configuration
            this._originalControlsConfig = {};
            if (this.controls) {
                this._originalControlsConfig = {
                    enableRotate: this.controls.enableRotate,
                    enableZoom: this.controls.enableZoom,
                    enablePan: this.controls.enablePan,
                    minDistance: this.controls.minDistance,
                    maxDistance: this.controls.maxDistance
                };
            } else {
                console.warn('[Viewer] Controls not initialized, using default configuration');
                this._originalControlsConfig = {
                    enableRotate: true,
                    enableZoom: true,
                    enablePan: true,
                    minDistance: 1,
                    maxDistance: 100
                };
            }

            // Configure raycaster for TalkingHead mode
            const raycasterConfigured = this.setupRaycasterForTalkingHead();
            
            if (!raycasterConfigured) {
                console.warn('[Viewer] TalkingHead raycaster configuration failed, but continuing with transformation');
            }

            // Use the consolidated transformation service directly
            const success = await this.transformationService.transformToTalkingHead(currentMesh, this);

            if (success) {
                // Update local references from the transformation service
                this.talkingAvatar.talkingHead = this.transformationService.getTalkingHead();
                this.talkingAvatar.ui = this.transformationService.ui;
                this.talkingAvatar.inTalkingHeadMode = true;
            }

            return success;
        } catch (error) {
            console.error('[Viewer] Error transforming to TalkingHead:', error);
            this.showError('Failed to transform avatar: ' + (error.message || 'Unknown error'));
            return false;
        }
    }

    /**
     * Set up raycaster for TalkingHead mode
     * This allows for better interaction with the avatar
     */
    setupRaycasterForTalkingHead() {
        // Create raycaster if it doesn't exist
        if (!this.raycaster) {
            this.raycaster = new THREE.Raycaster();
        }

        // Configure raycaster for better precision with avatar meshes
        this.raycaster.params.Line.threshold = 0.1;
        this.raycaster.params.Points.threshold = 0.1;
        this.raycaster.params.Mesh.threshold = 0.01;

        // Ensure controls are initialized before configuring them
        if (!this.controls) {
            console.warn('[Viewer] Controls not initialized yet, attempting to set up controls');
            const controlsReady = this.setupControls();
            
            if (!controlsReady) {
                console.error('[Viewer] Cannot setup controls - camera or renderer not ready. TalkingHead configuration will be skipped.');
                return false;
            }
        }

        // Enable orbital controls with specific settings for TalkingHead mode
        if (this.controls) {
            this.controls.enableRotate = true;
            this.controls.enableZoom = true;
            this.controls.enablePan = false;
            this.controls.minDistance = 0.5;
            this.controls.maxDistance = 5;

            // Update controls
            this.controls.update();
            console.log('[Viewer] TalkingHead raycaster and controls configured successfully');
            return true;
        } else {
            console.error('[Viewer] Controls still not available after setup attempt');
            return false;
        }
    }

    /**
     * Restore original raycaster and controls configuration
     * Called when exiting TalkingHead mode
     */
    restoreRaycasterConfig() {
        // Restore original controls configuration if available
        if (this._originalControlsConfig) {
            Object.entries(this._originalControlsConfig).forEach(([key, value]) => {
                this.controls[key] = value;
            });

            // Clear stored configuration
            this._originalControlsConfig = null;
        }

        // Reset raycaster parameters to defaults
        if (this.raycaster) {
            this.raycaster.params.Line.threshold = 0.1;
            this.raycaster.params.Points.threshold = 0.1;
            this.raycaster.params.Mesh.threshold = 0;
        }

        // Update controls
        this.controls.update();
    }

    async initializeGestureControls() {
        try {
            console.log('[Viewer] Initializing gesture controls...');

            // Initialize camera first if needed
            if (!this.cameraViewer) {
                console.warn('[Viewer] Camera viewer not available, waiting for UISettings to initialize it');

                // Wait a bit in case it's being initialized asynchronously
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Check again
                if (!this.cameraViewer && this.uiSettings?.components?.cameraViewer) {
                    this.cameraViewer = this.uiSettings.components.cameraViewer;
                }

                if (!this.cameraViewer) {
                    console.error('[Viewer] Camera viewer still not available, gesture controls will not work');
                    return false;
                }
            }

            // Initialize pose detector with hand detection only and gesture detection enabled
            this.poseDetector = new PoseDetector({
                detectionMode: DetectionMode.HANDS_ONLY,
                useHands: true,
                usePose: false,
                useGestureDetection: true, // Explicitly enable gesture detection
                uiManager: this.uiSettings // Pass the UI manager for loading panels
            });

            // Initialize camera and detection
            await this.poseDetector.initialize();

            // Setup debug visualization if needed
            if (this.cameraViewer?.canvas) {
                // Set up canvas visualizer for debugging
                this.debugVisualizer = new Canvas2DVisualizer(false);
                await this.debugVisualizer.setCanvas(this.cameraViewer.canvas);

                // Connect visualizer to pose detector
                this.poseDetector.setVisualizer(this.debugVisualizer);
            }

            // Create and initialize gesture controller
            this.gestureController = new GestureController(this);
            await this.gestureController.initialize(
                this.poseDetector,
                this.cameraViewer,
                this.debugVisualizer
            );

            // Register for pose detection and gesture events
            this.setupGestureHandling();

            // Add handler for debug visualization in popup window
            this.debugVisualizationCallbacks = new Set();

            console.log('[Viewer] Gesture controls initialized successfully');

            // Setup keyboard shortcut for gesture controls (G key)
            window.addEventListener('keydown', (event) => {
                // if (event.key === 'g' || event.key === 'G') {
                //     this.toggleGestureControls();
                // }

                // Debug mode toggle
                if (event.ctrlKey && event.shiftKey && (event.key === 'i' || event.key === 'I')) {
                    this.toggleDebugMode();
                }
            });

            return true;
        } catch (error) {
            console.error('[Viewer] Failed to initialize gesture controls:', error);
            return false;
        }
    }

    /**
     * Set up direct gesture handling from pose results
     * This moves gesture processing from detector to viewer
     */
    setupGestureHandling() {
        // Register detector callback for direct access to pose results
        this.cameraViewer.onVideoFrame(async (videoElement, timestamp) => {
            if (!this.gestureController?.isActive()) return;

            try {
                // Use poseDetector to get pose data only
                const poseResults = await this.poseDetector.detectPose(videoElement, timestamp);

                // Skip if no valid pose results
                if (!poseResults) return;

                // Process gestures in the gesture controller
                if (poseResults.handPoses?.left?.landmarks ||
                    poseResults.handPoses?.right?.landmarks) {
                    // Process gesture logic moved to GestureController
                    this.gestureController.processPoseResults(poseResults);
                }

                // Handle visualization if debug mode is on
                if (this.gestureController.isDebugMode() && this.debugVisualizer) {
                    this.debugVisualizer.drawResults(poseResults);

                    // Notify debug visualization callbacks (for popup window)
                    this.debugVisualizationCallbacks.forEach(callback => {
                        try {
                            callback(poseResults);
                        } catch (error) {
                            console.error('[Viewer] Error in debug visualization callback:', error);
                        }
                    });
                }
            } catch (error) {
                console.error('[Viewer] Error processing video frame:', error);
            }
        });
    }

    /**
     * Register a callback for debug visualization frames
     */
    onDebugVisualizationFrame(callback) {
        if (!this.debugVisualizationCallbacks) {
            this.debugVisualizationCallbacks = new Set();
        }

        if (typeof callback === 'function') {
            this.debugVisualizationCallbacks.add(callback);
            return true;
        }
        return false;
    }

    /**
     * Unregister a callback for debug visualization frames
     */
    offDebugVisualizationFrame(callback) {
        if (this.debugVisualizationCallbacks && typeof callback === 'function') {
            return this.debugVisualizationCallbacks.delete(callback);
        }
        return false;
    }

    toggleGestureControls() {
        if (!this.gestureController) {
            console.log('[Viewer] Initializing gesture controller on demand');

            // Initialize gesture controller if possible
            if (this.cameraViewer && this.poseDetector) {
                this.initializeGestureControls().then(success => {
                    if (success) {
                        console.log('[Viewer] Gesture controller initialized on demand');
                        this.toggleGestureControls(); // Call again after initialization
                    } else {
                        console.error('[Viewer] Failed to initialize gesture controller on demand');
                    }
                });
            } else {
                console.error('[Viewer] Cannot initialize gesture controller - camera or pose detector not available');
            }
            return false;
        }


        const isEnabled = this.gestureController.toggle();

        // Show tutorial if enabling gesture controls
        if (isEnabled && this.uiSettings) {
            this.uiSettings.showGestureTutorial();
        }

        // Update UI elements via UISettings
        if (this.uiSettings && typeof this.uiSettings.updateGestureControlUI === 'function') {
            this.uiSettings.updateGestureControlUI(isEnabled);
        }

        return isEnabled;
    }

    /**
     * Opens the Ready Player Me avatar creation interface
     * Delegates to talkingAvatar instance
     */
    openReadyPlayerMe() {
        if (this.talkingAvatar) {
            this.talkingAvatar.openReadyPlayerMe();
        } else {
            console.error('[Viewer] TalkingAvatar not initialized');
        }
    }

    /**
     * Opens the Any to 3D conversion UI with tripo-doll selected
     */
    openTripoDollCamera() {
        // Find and show the conversion modal
        const modal = document.querySelector('.conversion-modal');
        if (modal) {
            // Show the modal with a fade-in effect
            modal.style.opacity = '0';
            modal.classList.remove('hidden');

            // Trigger reflow to ensure transition works
            void modal.offsetWidth;

            // Fade in
            modal.style.opacity = '1';

            // Find and set the source select to tripo-doll
            const sourceSelect = modal.querySelector('.source-select');
            if (sourceSelect) {
                sourceSelect.value = 'tripo-doll';

                // Trigger change event to update UI
                const event = new Event('change');
                sourceSelect.dispatchEvent(event);

                // Make sure Tripo Doll specific UI elements are visible and others are hidden
                const cameraButton = modal.querySelector('.camera-button');
                const genderWrapper = modal.querySelector('.gender-wrapper');

                // First hide all UI elements that shouldn't be visible
                // Then show only the ones needed for Tripo Doll
                if (cameraButton) {
                    // Show camera button for Tripo Doll
                    cameraButton.classList.remove('hidden');
                    // Update camera button text
                    cameraButton.innerHTML = '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path><circle cx="12" cy="13" r="4"></circle></svg> Take Tripo Doll Photo';
                    console.log('[Viewer] Camera button shown for Tripo Doll');
                }

                // Show gender wrapper for Tripo Doll
                if (genderWrapper) {
                    genderWrapper.classList.remove('hidden');
                    console.log('[Viewer] Gender selector shown for Tripo Doll');
                }

                // Hide text input and show file input
                const textInput = modal.querySelector('.text-input');
                const fileInput = modal.querySelector('.file-input');

                if (textInput) textInput.classList.add('hidden');
                if (fileInput) fileInput.classList.remove('hidden');

                // Set the modal title
                const content = modal.querySelector('.conversion-content');
                if (content) {
                    const modalTitle = content.querySelector('.modal-title') || document.createElement('h2');
                    modalTitle.className = 'modal-title';
                    modalTitle.textContent = 'Tripo Doll';

                    // Add title to content if it doesn't exist
                    if (!content.querySelector('.modal-title')) {
                        content.insertBefore(modalTitle, content.firstChild);
                    }

                    // Create a button container for the buttons if it doesn't exist
                    if (!content.querySelector('.button-container')) {
                        const buttonContainer = document.createElement('div');
                        buttonContainer.className = 'button-container';

                        // Move the buttons to the container
                        const generateButton = content.querySelector('.generate-button');
                        const cancelButton = content.querySelector('.cancel-button');

                        if (generateButton && cancelButton) {
                            buttonContainer.appendChild(generateButton);
                            buttonContainer.appendChild(cancelButton);

                            // Add the container to the content
                            content.appendChild(buttonContainer);
                        }
                    }
                }

                // Focus on the camera button to draw attention to it
                setTimeout(() => {
                    if (cameraButton) cameraButton.focus();
                }, 300);
            }
        } else {
            console.error('[Viewer] Conversion modal not found');
        }
    }

    toggleDebugMode() {
        if (!this.gestureController) {
            console.log('[Viewer] Gesture controller not available');
            return false;
        }

        return this.gestureController.toggleDebugMode();
    }

    // NOTE: The setupQRCodeButton method has been removed as the QR code button
    // is now integrated into the actions menu in the UI class

    // NOTE: The _setupConnectionHandlers method is defined earlier in the file


    /**
     * Handler for when mesh is deleted
     * @param {string} meshId - ID of deleted mesh
     */
    onMeshDeleted(meshId) {
        // Check if we need to exit TalkingHead mode
        if (this.talkingAvatar && this.talkingAvatar.currentMeshId === meshId) {
            this.talkingAvatar.exitTalkingHeadMode();
        }

        // Continue with regular mesh deletion logic
        // ...existing code...
    }

    /**
     * Remove all objects from the scene and reset
     */
    clearScene() {
        // If in TalkingHead mode, exit first
        if (this.talkingAvatar && this.talkingAvatar.talkingHead) {
            this.talkingAvatar.exitTalkingHeadMode();
        }

        // Continue with regular scene clearing
        // ...existing code...
    }
}