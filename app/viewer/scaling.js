/**
 * UI Scaling Configuration Constants
 */
const SCALING_CONFIG = {
  BASE_VIEWPORT: { width: 1400, height: 900 },
  DEBOUNCE_DELAY: 150,
  DEVICE_MULTIPLIERS: {
    mobile: { ui: 1.1, button: 1.05, panel: 0.95 },
    tablet: { ui: 1.05, button: 1.0, panel: 0.92 },
    desktop: { ui: 1.0, button: 0.95, panel: 0.9 }
  },
  SCALE_BOUNDS: {
    mobile: { min: 0.9, max: 1.3 },
    tablet: { min: 0.85, max: 1.15 },
    desktop: { min: 0.8, max: 1.05 }
  }
};

/**
 * Automatic UI Scaling Manager
 * Handles dynamic scaling of UI elements based on viewport size and device type
 */
export class UIScalingManager {
  constructor() {
    this.isInitialized = false;
    this.resizeObserver = null;
    this.lastViewportWidth = window.innerWidth;
    this.lastViewportHeight = window.innerHeight;
    this.lastError = null;

    // Bind methods
    this.handleResize = this.handleResize.bind(this);
    this.updateScaling = this.updateScaling.bind(this);

    // Initialize with error handling
    try {
      // Delayed initialization to allow for proper DOM setup
      setTimeout(() => this.initialize(), 0);
    } catch (error) {
      console.error('[UIScaling] Failed to initialize UIScalingManager:', error);
      this.lastError = error;
      this.useFailsafeScaling();
    }
  }

  /**
   * Initialize the scaling manager
   */
  initialize() {
    if (this.isInitialized) return;

    // Set initial scaling
    this.updateScaling();

    // Listen for viewport changes
    window.addEventListener('resize', this.handleResize);
    window.addEventListener('orientationchange', this.handleResize);

    // Use ResizeObserver if available for more precise detection
    if (window.ResizeObserver) {
      this.resizeObserver = new ResizeObserver(this.handleResize);
      this.resizeObserver.observe(document.documentElement);
    }

    this.isInitialized = true;
    console.log('[UIScaling] Automatic scaling manager initialized');
  }

  /**
   * Handle resize events with debouncing
   */
  handleResize() {
    // Debounce resize events
    clearTimeout(this.resizeTimeout);
    this.resizeTimeout = setTimeout(() => {
      try {
        const currentWidth = window.innerWidth;
        const currentHeight = window.innerHeight;

        // Only update if there's a significant change
        if (Math.abs(currentWidth - this.lastViewportWidth) > 50 ||
          Math.abs(currentHeight - this.lastViewportHeight) > 50) {

          this.updateScaling();
          this.lastViewportWidth = currentWidth;
          this.lastViewportHeight = currentHeight;
        }
      } catch (error) {
        console.error('[UIScaling] Error in handleResize:', error);
        this.lastError = error;
        this.useFailsafeScaling();
      }
    }, SCALING_CONFIG.DEBOUNCE_DELAY);
  }

  /**
   * Update CSS custom properties for responsive scaling
   */
  updateScaling() {
    try {
      const viewport = this.getViewportInfo();
      const scalingFactors = this.calculateScalingFactors(viewport);

      // Update CSS custom properties
      const root = document.documentElement;

      Object.entries(scalingFactors).forEach(([property, value]) => {
        root.style.setProperty(property, value);
      });

      console.log('[UIScaling] Updated scaling factors:', scalingFactors);
    } catch (error) {
      console.error('[UIScaling] Error updating scaling factors:', error);
      this.lastError = error;
      this.useFailsafeScaling();
    }
  }

  /**
   * Get viewport and device information
   */
  getViewportInfo() {
    return {
      width: window.innerWidth,
      height: window.innerHeight,
      pixelRatio: window.devicePixelRatio || 1,
      isMobile: window.innerWidth <= 768,
      isTablet: window.innerWidth > 768 && window.innerWidth <= 1024,
      isDesktop: window.innerWidth > 1024,
      isLandscape: window.innerWidth > window.innerHeight,
      aspectRatio: window.innerWidth / window.innerHeight
    };
  }

  /**
   * Calculate optimal scaling factors based on viewport
   */
  calculateScalingFactors(viewport) {
    const { width, height, isMobile, isTablet, pixelRatio } = viewport;

    // Base scaling calculation using configuration constants
    const baseScale = Math.min(width / SCALING_CONFIG.BASE_VIEWPORT.width, height / SCALING_CONFIG.BASE_VIEWPORT.height);

    // Device-specific adjustments using configuration constants
    let deviceType, multipliers, bounds;
    
    if (isMobile) {
      deviceType = 'mobile';
    } else if (isTablet) {
      deviceType = 'tablet';
    } else {
      deviceType = 'desktop';
    }

    multipliers = SCALING_CONFIG.DEVICE_MULTIPLIERS[deviceType];
    bounds = SCALING_CONFIG.SCALE_BOUNDS[deviceType];

    // Calculate scales using configuration
    let uiScale = Math.max(bounds.min, Math.min(baseScale * multipliers.ui, bounds.max));
    let buttonScale = uiScale * multipliers.button;
    let panelScale = uiScale * multipliers.panel;

    // High DPI adjustments - less aggressive
    if (pixelRatio > 1.5) {
      uiScale *= 0.95;
      buttonScale *= 0.95;
      panelScale *= 0.95;
    }

    // Ultra-wide screen adjustments - less aggressive
    if (width > 1920) {
      uiScale *= 0.9;
      buttonScale *= 0.9;
      panelScale *= 0.9;
    }

    return {
      '--ui-scale': uiScale.toFixed(3),
      '--button-scale': buttonScale.toFixed(3),
      '--panel-scale': panelScale.toFixed(3),
      '--nav-button-size': `${Math.round(48 * buttonScale)}px`,
      '--nav-height': `${Math.round(60 * uiScale)}px`,
      '--dropdown-width': `${Math.round(180 * panelScale)}px`,
      '--dropdown-height': `${Math.round(32 * panelScale)}px`
    };
  }

  /**
   * Force update scaling (useful for testing or manual triggers)
   */
  forceUpdate() {
    this.updateScaling();
  }

  /**
   * Get current scaling factors
   */
  getCurrentScaling() {
    const root = document.documentElement;
    return {
      uiScale: parseFloat(getComputedStyle(root).getPropertyValue('--ui-scale')),
      buttonScale: parseFloat(getComputedStyle(root).getPropertyValue('--button-scale')),
      panelScale: parseFloat(getComputedStyle(root).getPropertyValue('--panel-scale'))
    };
  }

  /**
   * Use failsafe scaling when errors occur
   */
  useFailsafeScaling() {
    try {
      console.warn('[UIScaling] Using failsafe scaling due to error:', this.lastError?.message);
      
      // Apply conservative failsafe values
      const root = document.documentElement;
      const failsafeScales = {
        '--ui-scale': '1.0',
        '--button-scale': '1.0', 
        '--panel-scale': '1.0',
        '--nav-button-size': '48px',
        '--nav-height': '56px',
        '--dropdown-width': '240px',
        '--dropdown-height': '32px'
      };

      Object.entries(failsafeScales).forEach(([property, value]) => {
        root.style.setProperty(property, value);
      });

      console.log('[UIScaling] Failsafe scaling applied successfully');
    } catch (error) {
      console.error('[UIScaling] Critical error in failsafe scaling:', error);
    }
  }

  /**
   * Get error status
   */
  getErrorStatus() {
    return {
      hasError: !!this.lastError,
      lastError: this.lastError?.message,
      timestamp: this.lastError ? Date.now() : null
    };
  }

  /**
   * Dispose of the scaling manager
   */
  dispose() {
    if (!this.isInitialized) return;

    window.removeEventListener('resize', this.handleResize);
    window.removeEventListener('orientationchange', this.handleResize);

    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = null;
    }

    if (this.resizeTimeout) {
      clearTimeout(this.resizeTimeout);
      this.resizeTimeout = null;
    }

    this.isInitialized = false;
    console.log('[UIScaling] Scaling manager disposed');
  }
}

// Export singleton instance
export const uiScalingManager = new UIScalingManager();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    uiScalingManager.initialize();
  });
} else {
  uiScalingManager.initialize();
}
