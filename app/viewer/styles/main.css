/* ===== HOLOGRAM SOFTWARE - MAIN UI CONSOLIDATION ===== */
/* Single source of truth for all UI styling */
/* This file replaces fragmented CSS architecture with organized control */

/* ===== FOUNDATION VARIABLES ===== */
/* These variables establish the base design system */
:root {
  /* Core Z-Index Hierarchy - Single source of truth */
  --z-index-canvas: 1;
  --z-index-ui-base: 1000;
  --z-index-controls: 1100;
  --z-index-dropdowns: 1200;
  --z-index-voice-menu: 1300;
  --z-index-mobile-dropdowns: 1400;
  --z-index-modals: 2000;
  --z-index-debug: 9999;

  /* Unified Scaling System - Responsive and consistent */
  --ui-scale-base: clamp(0.8, 2.8vmin, 1.2);
  --ui-scale: var(--ui-scale-base);
  --button-scale: calc(var(--ui-scale) * 0.95);
  --panel-scale: calc(var(--ui-scale) * 0.9);

  /* Color System - Single source for all components */
  --primary-bg: rgba(20, 24, 32, 0.95);
  --secondary-bg: rgba(40, 44, 52, 0.95);
  --accent-color: rgba(0, 120, 255, 0.8);
  --accent-hover: rgba(0, 120, 255, 0.9);
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.7);
  
  /* Component-specific colors */
  --dropdown-bg: var(--primary-bg);
  --dropdown-border: rgba(255, 255, 255, 0.15);
  --dropdown-shadow: rgba(0, 0, 0, 0.5);
  --button-bg: var(--accent-color);
  --button-hover-bg: var(--accent-hover);
  
  /* Dimension system */
  --border-radius-sm: calc(6px * var(--ui-scale));
  --border-radius-md: calc(8px * var(--ui-scale));
  --border-radius-lg: calc(12px * var(--ui-scale));
  --spacing-xs: calc(4px * var(--ui-scale));
  --spacing-sm: calc(6px * var(--ui-scale));
  --spacing-md: calc(8px * var(--ui-scale));
  --spacing-lg: calc(12px * var(--ui-scale));
}

/* ===== CORE TALKING HEAD CONTROLS ===== */
/* Container for all TalkingHead UI controls */
.talking-head-controls {
  position: fixed;
  bottom: var(--spacing-lg);
  left: 50%;
  transform: translateX(-50%);
  
  /* Layout - force single row, allow dropdown overflow */
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  gap: var(--spacing-sm);
  
  /* Styling */
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--primary-bg);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--dropdown-border);
  box-shadow: 0 8px 25px var(--dropdown-shadow);
  
  /* Sizing */
  min-height: calc(48px * var(--ui-scale));
  max-width: 90vw;
  
  /* Overflow handling - critical for dropdowns */
  overflow-x: auto;
  overflow-y: visible;
  scrollbar-width: none;
  -ms-overflow-style: none;
  
  /* Z-index */
  z-index: var(--z-index-controls);
  
  /* Animation */
  transition: all 0.3s ease;
}

.talking-head-controls::-webkit-scrollbar {
  display: none;
}

.talking-head-controls:hover {
  background: var(--secondary-bg);
  transform: translateX(-50%) translateY(-2px);
  box-shadow: 0 12px 35px var(--dropdown-shadow);
}

/* ===== TALKING HEAD BUTTONS ===== */
/* All TalkingHead control buttons - unified styling */
.talking-head-speech-button,
.talking-head-listen-button,
.talking-head-video-button,
.talking-head-pose-button,
.talking-head-animation-button,
.talking-head-voice-button {
  /* Dimensions */
  height: calc(32px * var(--ui-scale));
  min-width: calc(60px * var(--ui-scale));
  padding: var(--spacing-sm) calc(10px * var(--ui-scale));
  
  /* Layout */
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  flex: 1 1 auto;
  
  /* Styling */
  background: var(--button-bg);
  border: none;
  border-radius: var(--border-radius-sm);
  color: var(--text-primary);
  font-size: calc(11px * var(--ui-scale));
  font-weight: 500;
  
  /* Content handling */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  
  /* Interaction */
  cursor: pointer;
  transition: all 0.25s ease;
}

.talking-head-speech-button:hover,
.talking-head-listen-button:hover,
.talking-head-video-button:hover,
.talking-head-pose-button:hover,
.talking-head-animation-button:hover,
.talking-head-voice-button:hover {
  background: var(--button-hover-bg);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 120, 255, 0.3);
}

/* Active states for buttons */
.talking-head-speech-button.active,
.talking-head-pose-button.active,
.talking-head-animation-button.active,
.talking-head-voice-button.active {
  background: rgba(255, 60, 60, 0.8);
  animation: pulse 1.5s ease-in-out infinite alternate;
}

.talking-head-listen-button.active {
  background: rgba(76, 175, 80, 0.8);
  animation: listening-pulse 2s ease-in-out infinite alternate;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
}

.talking-head-video-button.active {
  background: rgba(33, 150, 243, 0.8);
  animation: video-pulse 2s ease-in-out infinite alternate;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
}

/* Disabled states */
.talking-head-speech-button.disabled,
.talking-head-listen-button.disabled,
.talking-head-video-button.disabled,
.talking-head-pose-button.disabled,
.talking-head-animation-button.disabled,
.talking-head-voice-button.disabled {
  background: rgba(100, 100, 100, 0.5);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  opacity: 0.6;
}

.talking-head-speech-button.disabled:hover,
.talking-head-listen-button.disabled:hover,
.talking-head-video-button.disabled:hover,
.talking-head-pose-button.disabled:hover,
.talking-head-animation-button.disabled:hover,
.talking-head-voice-button.disabled:hover {
  transform: none;
  box-shadow: none;
  background: rgba(100, 100, 100, 0.5);
}

/* ===== DROPDOWN SYSTEM ===== */
/* Base dropdown styles - all dropdowns inherit from this */
.talking-head-dropdown,
.voice-menu {
  /* Positioning */
  position: fixed;
  display: none;
  flex-direction: column;
  
  /* Styling */
  background: var(--dropdown-bg);
  backdrop-filter: blur(15px);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  border: 1px solid var(--dropdown-border);
  box-shadow: 0 8px 25px var(--dropdown-shadow);
  
  /* Sizing */
  min-width: calc(200px * var(--ui-scale));
  max-height: calc(300px * var(--ui-scale));
  max-width: 90vw;
  overflow-y: auto;
  gap: var(--spacing-sm);
  
  /* Animation */
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.2s ease;
  pointer-events: none;
  visibility: hidden;
  
  /* Z-index */
  z-index: var(--z-index-dropdowns);
}

/* Voice menu specific sizing */
.voice-menu {
  width: calc(280px * var(--ui-scale));
  z-index: var(--z-index-voice-menu);
}

/* Show state - clean and simple */
.talking-head-dropdown.show,
.voice-menu.show,
.talking-head-visible {
  display: flex;
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
  visibility: visible;
}

/* Dropdown items */
.talking-head-dropdown-item {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: var(--text-primary);
  padding: calc(10px * var(--ui-scale)) calc(12px * var(--ui-scale));
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  text-align: left;
  font-size: calc(13px * var(--ui-scale));
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.talking-head-dropdown-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(2px);
}

/* ===== STATUS DISPLAY ===== */
.talking-head-status {
  color: var(--text-primary);
  font-size: calc(12px * var(--ui-scale));
  opacity: 0.9;
  margin-left: calc(10px * var(--ui-scale));
  white-space: nowrap;
  font-weight: 400;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  :root {
    --ui-scale-base: clamp(0.7, 3.2vmin, 1.0);
  }
  
  .talking-head-dropdown,
  .voice-menu {
    z-index: var(--z-index-mobile-dropdowns);
    max-height: calc(250px * var(--ui-scale));
    width: calc(200px * var(--ui-scale));
  }
  
  .voice-menu {
    width: calc(250px * var(--ui-scale));
  }
  
  .talking-head-controls {
    max-width: 95vw;
    gap: var(--spacing-xs);
  }
}

@media (max-width: 480px) {
  :root {
    --ui-scale-base: clamp(0.6, 3.8vmin, 0.9);
  }
  
  .talking-head-dropdown,
  .voice-menu {
    width: calc(180px * var(--ui-scale));
  }
  
  .talking-head-controls {
    gap: calc(3px * var(--ui-scale));
  }
}

/* Large screens - keep UI compact */
@media (min-width: 1200px) {
  :root {
    --ui-scale-base: clamp(0.8, 2.2vmin, 1.0);
  }
}

/* ===== ANIMATIONS ===== */
@keyframes pulse {
  0% { 
    opacity: 1; 
    box-shadow: 0 3px 10px rgba(255, 60, 60, 0.3); 
  }
  50% { 
    opacity: 0.8; 
    box-shadow: 0 5px 20px rgba(255, 60, 60, 0.5); 
  }
  100% { 
    opacity: 1; 
    box-shadow: 0 3px 10px rgba(255, 60, 60, 0.3); 
  }
}

@keyframes listening-pulse {
  0% { box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4); }
  50% { box-shadow: 0 6px 20px rgba(76, 175, 80, 0.6); }
  100% { box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4); }
}

@keyframes video-pulse {
  0% { box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4); }
  50% { box-shadow: 0 6px 20px rgba(33, 150, 243, 0.6); }
  100% { box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4); }
}

/* ===== SCROLLBAR STYLING ===== */
.talking-head-dropdown::-webkit-scrollbar,
.voice-menu::-webkit-scrollbar {
  width: calc(6px * var(--ui-scale));
}

.talking-head-dropdown::-webkit-scrollbar-track,
.voice-menu::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: calc(3px * var(--ui-scale));
}

.talking-head-dropdown::-webkit-scrollbar-thumb,
.voice-menu::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: calc(3px * var(--ui-scale));
}

.talking-head-dropdown::-webkit-scrollbar-thumb:hover,
.voice-menu::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* ===== UTILITY CLASSES ===== */
.talking-head-hidden {
  display: none;
}

.talking-head-visible {
  display: flex;
}

.talking-head-loading {
  opacity: 0.6;
  pointer-events: none;
  cursor: wait;
}

/* Success/Error states */
.talking-head-success {
  background: rgba(76, 175, 80, 0.8);
}

.talking-head-error {
  background: rgba(244, 67, 54, 0.8);
}

.talking-head-warning {
  background: rgba(255, 152, 0, 0.8);
}

/* ===== DEBUG UTILITIES ===== */
.ui-debug-borders * {
  outline: 1px solid rgba(255, 0, 0, 0.3);
}

.ui-debug-z-index::before {
  content: attr(data-z-index);
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 10px;
  padding: 2px;
  z-index: 99999;
}