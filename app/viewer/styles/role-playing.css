/**
 * Role-Playing Panel Styles
 * 
 * Modern, responsive design for character selection and personality configuration
 * Integrates with existing design system and responsive scaling
 */

/* Main Panel Container */
.role-playing-panel {
    position: fixed;
    top: 80px;
    right: 20px;
    width: 320px;
    max-height: calc(100vh - 100px);
    background: var(--primary-bg, rgba(0, 0, 0, 0.85));
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--panel-border-radius, 12px);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    z-index: var(--z-index-controls, 1001);
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
}

.role-playing-panel.visible {
    opacity: 1;
    transform: translateX(0);
}

.role-playing-panel.minimized {
    height: 60px;
    overflow: hidden;
}

.role-playing-panel.minimized .rp-content {
    display: none;
}

/* Panel positioning variants */
.role-playing-panel[data-position="left"] {
    left: 20px;
    right: auto;
    transform: translateX(-100%);
}

.role-playing-panel[data-position="left"].visible {
    transform: translateX(0);
}

/* Header */
.rp-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--panel-padding, 12px);
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    user-select: none;
}

.rp-title {
    display: flex;
    align-items: center;
    gap: calc(var(--panel-gap, 8px) * 1.5);
    color: white;
    font-weight: 600;
    font-size: calc(var(--button-font-size, 14px) * 1.1);
}

.rp-icon {
    font-size: calc(var(--nav-icon-size, 20px) * 1.2);
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.3));
}

.rp-controls {
    display: flex;
    gap: calc(var(--panel-gap, 8px) * 0.5);
}

.rp-minimize-btn,
.rp-close-btn {
    width: calc(var(--nav-button-size, 48px) * 0.6);
    height: calc(var(--nav-button-size, 48px) * 0.6);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: calc(var(--button-border-radius, 8px) * 0.5);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: calc(var(--button-font-size, 14px) * 1.2);
    transition: all 0.2s ease;
}

.rp-minimize-btn:hover,
.rp-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.rp-close-btn:hover {
    background: rgba(255, 59, 48, 0.3);
    border-color: rgba(255, 59, 48, 0.5);
}

/* Content Area */
.rp-content {
    padding: var(--panel-padding, 12px);
    max-height: calc(100vh - 160px);
    overflow-y: auto;
    overflow-x: hidden;
}

.rp-content::-webkit-scrollbar {
    width: 6px;
}

.rp-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
}

.rp-content::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.rp-content::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Sections */
.rp-section {
    margin-bottom: calc(var(--panel-padding, 12px) * 1.5);
}

.rp-section:last-child {
    margin-bottom: 0;
}

.rp-section-title {
    color: white;
    font-size: calc(var(--button-font-size, 14px) * 1.1);
    font-weight: 600;
    margin: 0 0 var(--panel-gap, 8px) 0;
    display: flex;
    align-items: center;
    gap: var(--panel-gap, 8px);
}

.rp-section-title::before {
    content: '';
    width: 3px;
    height: 16px;
    background: linear-gradient(45deg, #007AFF, #5856D6);
    border-radius: 2px;
}

/* Character Search Interface */
.character-search-section {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: calc(var(--panel-padding, 12px) * 1.5);
    margin-bottom: calc(var(--panel-padding, 12px) * 1.5);
}

.character-search-container {
    display: flex;
    flex-direction: column;
    gap: var(--panel-gap, 8px);
}

.search-input-group {
    position: relative;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.character-search-input {
    width: 100%;
    height: calc(var(--dropdown-height, 32px) * 1.2);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--button-border-radius, 8px);
    color: white;
    font-size: var(--dropdown-font-size, 13px);
    padding: 0 calc(var(--panel-gap, 8px) * 6) 0 calc(var(--panel-gap, 8px) * 1.5);
    outline: none;
    transition: all 0.2s ease;
}

.character-search-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
    font-style: italic;
}

.character-search-input:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(0, 122, 255, 0.5);
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
}

.search-input-icons {
    position: absolute;
    right: calc(var(--panel-gap, 8px) * 0.5);
    display: flex;
    align-items: center;
    gap: calc(var(--panel-gap, 8px) * 0.25);
}

.voice-search-btn,
.clear-search-btn {
    width: calc(var(--nav-button-size, 48px) * 0.6);
    height: calc(var(--nav-button-size, 48px) * 0.6);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: calc(var(--button-border-radius, 8px) * 0.5);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: calc(var(--button-font-size, 14px) * 0.9);
    transition: all 0.2s ease;
    position: relative;
}

.voice-search-btn:hover,
.clear-search-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.voice-search-btn.recording {
    background: rgba(255, 59, 48, 0.3);
    border-color: rgba(255, 59, 48, 0.5);
    animation: voice-pulse 1.5s ease-in-out infinite;
}

@keyframes voice-pulse {
    0%, 100% { 
        box-shadow: 0 0 0 0 rgba(255, 59, 48, 0.4);
        transform: scale(1);
    }
    50% { 
        box-shadow: 0 0 0 8px rgba(255, 59, 48, 0.1);
        transform: scale(1.05);
    }
}

.voice-recording-indicator {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #ff3b30;
    border-radius: 50%;
    display: none;
    animation: blink 1s ease-in-out infinite;
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

.character-search-results {
    display: none;
    margin-top: var(--panel-gap, 8px);
}

.search-status {
    color: rgba(255, 255, 255, 0.7);
    font-size: calc(var(--button-font-size, 14px) * 0.85);
    margin-bottom: calc(var(--panel-gap, 8px) * 0.5);
    font-style: italic;
}

.search-results-grid {
    display: flex;
    flex-direction: column;
    gap: calc(var(--panel-gap, 8px) * 0.75);
}

.search-result-card {
    background: rgba(0, 122, 255, 0.1);
    border-color: rgba(0, 122, 255, 0.3);
}

.search-result-card:hover {
    background: rgba(0, 122, 255, 0.15);
    border-color: rgba(0, 122, 255, 0.4);
}

.match-info {
    display: flex;
    align-items: center;
    gap: calc(var(--panel-gap, 8px) * 0.5);
    margin-top: 2px;
}

.match-badge {
    font-size: calc(var(--button-font-size, 14px) * 0.75);
    padding: 1px 4px;
    border-radius: 3px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.match-badge.perfect-match {
    background: rgba(0, 200, 81, 0.2);
    color: #00C851;
    border: 1px solid rgba(0, 200, 81, 0.3);
}

.match-badge.good-match {
    background: rgba(255, 193, 7, 0.2);
    color: #FFC107;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.match-badge.partial-match {
    background: rgba(255, 87, 51, 0.2);
    color: #FF5733;
    border: 1px solid rgba(255, 87, 51, 0.3);
}

.match-reasons {
    color: rgba(255, 255, 255, 0.6);
    font-size: calc(var(--button-font-size, 14px) * 0.7);
    font-style: italic;
}

.no-results {
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    padding: calc(var(--panel-padding, 12px) * 1.5);
    font-style: italic;
    border: 1px dashed rgba(255, 255, 255, 0.2);
    border-radius: var(--button-border-radius, 8px);
}

/* Character Recommendations */
.character-recommendations-section {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: calc(var(--panel-padding, 12px) * 1.5);
    margin-bottom: calc(var(--panel-padding, 12px) * 1.5);
}

.character-recommendations {
    display: flex;
    flex-direction: column;
    gap: calc(var(--panel-gap, 8px) * 0.75);
}

.recommendation-card {
    background: rgba(255, 214, 0, 0.1);
    border-color: rgba(255, 214, 0, 0.3);
    position: relative;
}

.recommendation-card:hover {
    background: rgba(255, 214, 0, 0.15);
    border-color: rgba(255, 214, 0, 0.4);
}

.recommendation-badge {
    position: absolute;
    top: calc(var(--panel-gap, 8px) * 0.5);
    right: calc(var(--panel-gap, 8px) * 0.5);
    font-size: calc(var(--button-font-size, 14px) * 0.9);
    opacity: 0.8;
}

.recommendation-reason {
    color: rgba(255, 214, 0, 0.9);
    font-size: calc(var(--button-font-size, 14px) * 0.75);
    font-weight: 500;
    margin-top: 2px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Enhanced Character Grid */
.character-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--panel-gap, 8px);
}

.character-traits {
    display: flex;
    flex-wrap: wrap;
    gap: calc(var(--panel-gap, 8px) * 0.25);
    margin-top: 4px;
}

.trait-badge {
    font-size: calc(var(--button-font-size, 14px) * 0.7);
    padding: 1px 4px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    color: rgba(255, 255, 255, 0.8);
    text-transform: capitalize;
    font-weight: 500;
    letter-spacing: 0.3px;
}

.character-card {
    display: flex;
    align-items: center;
    gap: var(--panel-gap, 8px);
    padding: var(--panel-gap, 8px);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--button-border-radius, 8px);
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.character-card:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.character-card.selected {
    background: rgba(0, 122, 255, 0.2);
    border-color: rgba(0, 122, 255, 0.5);
    box-shadow: 0 0 16px rgba(0, 122, 255, 0.3);
}

.character-card.selected::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(180deg, #007AFF, #5856D6);
}

.character-avatar {
    font-size: calc(var(--nav-icon-size, 20px) * 1.8);
    width: calc(var(--nav-button-size, 48px) * 0.8);
    height: calc(var(--nav-button-size, 48px) * 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    flex-shrink: 0;
}

.character-info {
    flex: 1;
    min-width: 0;
}

.character-name {
    color: white;
    font-size: calc(var(--button-font-size, 14px) * 1.0);
    font-weight: 600;
    margin-bottom: 2px;
}

.character-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: calc(var(--button-font-size, 14px) * 0.85);
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
}

.character-status {
    display: flex;
    align-items: center;
    gap: 4px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transition: all 0.2s ease;
}

.character-card.selected .status-indicator {
    background: #00C851;
    box-shadow: 0 0 8px rgba(0, 200, 81, 0.5);
}

/* Personality Controls */
.personality-controls {
    display: flex;
    flex-direction: column;
    gap: calc(var(--panel-gap, 8px) * 1.5);
}

.personality-slider-group {
    display: flex;
    flex-direction: column;
    gap: calc(var(--panel-gap, 8px) * 0.5);
}

.slider-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.slider-label {
    color: white;
    font-size: calc(var(--button-font-size, 14px) * 0.9);
    font-weight: 500;
}

.slider-value {
    color: rgba(255, 255, 255, 0.8);
    font-size: calc(var(--button-font-size, 14px) * 0.85);
    font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    min-width: 32px;
    text-align: center;
}

.slider-container {
    position: relative;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.personality-slider {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    outline: none;
    margin: 0;
    padding: 0;
}

.personality-slider::-webkit-slider-thumb {
    appearance: none;
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: linear-gradient(45deg, #007AFF, #5856D6);
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.2s ease;
    position: relative;
    z-index: 2;
}

.personality-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
}

.personality-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: linear-gradient(45deg, #007AFF, #5856D6);
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.slider-track-fill {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, #007AFF, #5856D6);
    border-radius: 3px;
    transition: width 0.2s ease;
    width: 50%;
}

.slider-description {
    color: rgba(255, 255, 255, 0.6);
    font-size: calc(var(--button-font-size, 14px) * 0.8);
    font-style: italic;
    margin-top: 2px;
}

/* Voice Style Selector */
.voice-style-selector {
    display: flex;
    flex-direction: column;
    gap: var(--panel-gap, 8px);
}

.voice-dropdown {
    display: flex;
    flex-direction: column;
    gap: calc(var(--panel-gap, 8px) * 0.5);
}

.voice-select {
    width: 100%;
    height: var(--dropdown-height, 32px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--button-border-radius, 8px);
    color: white;
    font-size: var(--dropdown-font-size, 13px);
    padding: 0 var(--panel-gap, 8px);
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=US-ASCII,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'><path fill='white' d='M2 0L0 2h4zm0 5L0 3h4z'/></svg>");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 12px;
    padding-right: 32px;
}

.voice-select:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.voice-select:focus {
    outline: none;
    border-color: rgba(0, 122, 255, 0.5);
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
}

.voice-select option {
    background: #2c2c2e;
    color: white;
    padding: 8px;
}

.voice-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: calc(var(--button-font-size, 14px) * 0.85);
    font-style: italic;
    padding-left: var(--panel-gap, 8px);
}

/* Apply Section */
.apply-section {
    display: flex;
    flex-direction: column;
    gap: var(--panel-gap, 8px);
    padding-top: var(--panel-padding, 12px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.apply-character-btn {
    width: 100%;
    height: calc(var(--button-height, 36px) * 1.2);
    background: linear-gradient(45deg, #007AFF, #5856D6);
    border: none;
    border-radius: var(--button-border-radius, 8px);
    color: white;
    font-size: var(--button-font-size, 14px);
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--panel-gap, 8px);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.apply-character-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 24px rgba(0, 122, 255, 0.3);
}

.apply-character-btn:active {
    transform: translateY(0);
}

.apply-character-btn.applied {
    background: linear-gradient(45deg, #00C851, #00A043);
}

.apply-character-btn.applied::after {
    content: '✓';
    position: absolute;
    right: 12px;
    font-size: 16px;
    animation: checkmark 0.5s ease;
}

@keyframes checkmark {
    0% { opacity: 0; transform: scale(0.5); }
    100% { opacity: 1; transform: scale(1); }
}

.btn-icon {
    font-size: calc(var(--nav-icon-size, 20px) * 0.9);
}

.character-status-display {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: calc(var(--panel-gap, 8px) * 0.5);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--button-border-radius, 8px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: calc(var(--button-font-size, 14px) * 0.85);
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .role-playing-panel {
        width: calc(100vw - 40px);
        max-width: 360px;
        right: 20px;
        left: 20px;
        margin: 0 auto;
    }
    
    .role-playing-panel[data-position="left"] {
        left: 20px;
        right: 20px;
    }
    
    .character-search-input {
        font-size: calc(var(--dropdown-font-size, 13px) * 1.1);
        height: calc(var(--dropdown-height, 32px) * 1.4);
    }
    
    .search-input-icons {
        right: calc(var(--panel-gap, 8px) * 0.75);
    }
    
    .voice-search-btn,
    .clear-search-btn {
        width: calc(var(--nav-button-size, 48px) * 0.7);
        height: calc(var(--nav-button-size, 48px) * 0.7);
    }
    
    .character-traits {
        display: none; /* Hide on small screens to save space */
    }
    
    .match-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;
    }
}

@media (max-height: 600px) {
    .role-playing-panel {
        top: 60px;
        max-height: calc(100vh - 80px);
    }
    
    .rp-content {
        max-height: calc(100vh - 140px);
    }
}

/* Animation Enhancements */
@media (prefers-reduced-motion: no-preference) {
    .character-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .personality-slider::-webkit-slider-thumb {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .slider-track-fill {
        transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .role-playing-panel {
        border-width: 2px;
        border-color: white;
    }
    
    .character-card {
        border-width: 2px;
    }
    
    .character-card.selected {
        border-color: #007AFF;
        background: rgba(0, 122, 255, 0.3);
    }
}

/* Accessibility improvements */
.role-playing-panel *:focus {
    outline: 2px solid rgba(0, 122, 255, 0.6);
    outline-offset: 2px;
}

.character-card:focus {
    outline: 2px solid rgba(0, 122, 255, 0.6);
    outline-offset: -2px;
}

/* Loading state */
.role-playing-panel.loading {
    pointer-events: none;
}

.role-playing-panel.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* Dark mode variations */
@media (prefers-color-scheme: light) {
    :root {
        --primary-bg: rgba(255, 255, 255, 0.95);
    }
    
    .role-playing-panel {
        background: var(--primary-bg);
        color: #1d1d1f;
        border-color: rgba(0, 0, 0, 0.1);
    }
    
    .rp-title,
    .character-name,
    .slider-label {
        color: #1d1d1f;
    }
    
    .character-description,
    .slider-value,
    .voice-description,
    .status-text {
        color: rgba(0, 0, 0, 0.7);
    }
}