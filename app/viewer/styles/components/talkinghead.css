/* TalkingHead UI Component Styles */
/* Integrated with automatic scaling system from viewer.css */

/* ===== TALKING HEAD CONTROLS - DELEGATION TO VIEWER.CSS ===== */
/* Main control container styles delegated to viewer.css to avoid conflicts */

/* ===== CONSISTENT BUTTON STYLING - DELEGATION TO VIEWER.CSS ===== */
/* All button styles (.talking-head-*-button) are now handled in viewer.css */
/* This prevents conflicting CSS systems and ensures single-row layout */

/* ===== STATUS SPAN ===== */
.talking-head-status {
  color: var(--text-primary);
  font-size: calc(12px * var(--ui-scale));
  opacity: 0.9;
  margin-left: calc(10px * var(--ui-scale));
  white-space: nowrap;
  font-weight: 400;
}

/* ===== ENHANCED DROPDOWN MENUS WITH COMPREHENSIVE VISIBILITY FORCING ===== */
.talking-head-dropdown {
  position: fixed !important; /* Changed from absolute to fixed for viewport positioning */
  bottom: calc(var(--nav-height) + 70px) !important; /* Position above controls container */
  left: auto !important;
  right: auto !important;
  background: var(--primary-bg) !important;
  backdrop-filter: blur(15px) !important;
  border-radius: calc(12px * var(--ui-scale)) !important;
  padding: calc(12px * var(--ui-scale)) !important;
  display: none !important;
  flex-direction: column !important;
  gap: calc(6px * var(--ui-scale)) !important;
  max-height: calc(300px * var(--ui-scale)) !important;
  overflow-y: auto !important;
  min-width: calc(200px * var(--ui-scale)) !important;
  z-index: var(--z-index-dropdowns) !important; /* Use centralized z-index */
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  opacity: 0 !important;
  transform: translateY(10px) !important;
  transition: all 0.2s ease !important;
  pointer-events: none !important;
  visibility: hidden !important;
}

.talking-head-dropdown.show {
  display: flex !important; /* Fixed: Force display override */
  opacity: 1 !important;
  transform: translateY(0) !important;
  pointer-events: auto !important;
  visibility: visible !important;
}

/* Additional utility class for emergency visibility forcing */
.talking-head-visible {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  pointer-events: auto !important;
}

/* Enhanced voice menu styles with comprehensive visibility */
.voice-menu {
  position: fixed !important; /* Changed from absolute to fixed for viewport positioning */
  bottom: calc(var(--nav-height) + 70px) !important; /* Position above controls container */
  left: auto !important;
  right: auto !important;
  background: var(--primary-bg) !important;
  backdrop-filter: blur(10px) !important;
  border-radius: calc(12px * var(--ui-scale)) !important;
  padding: calc(10px * var(--ui-scale)) !important;
  display: none !important;
  flex-direction: column !important;
  gap: calc(5px * var(--ui-scale)) !important;
  max-height: calc(300px * var(--ui-scale)) !important;
  overflow-y: auto !important;
  width: calc(280px * var(--ui-scale)) !important;
  max-width: 90vw !important;
  z-index: var(--z-index-voice-menu) !important; /* Use centralized z-index */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  opacity: 0 !important;
  transform: translateY(10px) !important;
  transition: all 0.2s ease !important;
  pointer-events: none !important;
  visibility: hidden !important;
}

.voice-menu.show,
.voice-menu[style*="display: flex"] {
  display: flex !important; /* Fixed: Force display override for both class and inline styles */
  opacity: 1 !important;
  transform: translateY(0) !important;
  pointer-events: auto !important;
  visibility: visible !important;
}

/* Emergency fallback for webkit browsers */
@supports (-webkit-appearance: none) {
  .talking-head-dropdown.show,
  .voice-menu.show,
  .talking-head-visible {
    -webkit-transform: translateY(0) !important;
    -webkit-transition: all 0.2s ease !important;
  }
}

/* Mobile responsive enhancements */
@media (max-width: 768px) {
  .talking-head-dropdown,
  .voice-menu {
    z-index: var(--z-index-mobile-dropdowns) !important; /* Use mobile z-index */
    bottom: calc(var(--nav-height) + 15px) !important;
    width: calc(200px * var(--ui-scale)) !important;
    max-height: calc(250px * var(--ui-scale)) !important;
  }
  
  .voice-menu {
    width: calc(250px * var(--ui-scale)) !important;
  }
}

.talking-head-dropdown-item {
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  color: var(--text-primary);
  padding: calc(10px * var(--ui-scale)) calc(12px * var(--ui-scale));
  border-radius: calc(6px * var(--ui-scale));
  cursor: pointer;
  text-align: left;
  font-size: calc(13px * var(--ui-scale));
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.talking-head-dropdown-item:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateX(2px);
}

/* ===== VOICE CLONE MODAL ===== */
.voice-clone-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  overflow-y: auto;
  padding: calc(20px * var(--ui-scale)) 0;
  backdrop-filter: blur(5px);
}

.voice-clone-modal.show {
  display: flex;
}

.voice-clone-content {
  background-color: rgba(40, 44, 52, 0.95);
  border-radius: calc(15px * var(--ui-scale));
  padding: calc(25px * var(--ui-scale));
  width: 90%;
  max-width: calc(600px * var(--ui-scale));
  max-height: 90vh;
  overflow-y: auto;
  color: white;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.6);
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: calc(14px * var(--ui-scale));
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.voice-clone-header {
  margin-bottom: calc(20px * var(--ui-scale));
  text-align: center;
  position: sticky;
  top: 0;
  background-color: rgba(40, 44, 52, 0.95);
  padding: calc(15px * var(--ui-scale)) 0;
  z-index: 10;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin: calc(-25px * var(--ui-scale)) calc(-25px * var(--ui-scale)) calc(20px * var(--ui-scale)) calc(-25px * var(--ui-scale));
  padding-left: calc(25px * var(--ui-scale));
  padding-right: calc(25px * var(--ui-scale));
  border-top-left-radius: calc(15px * var(--ui-scale));
  border-top-right-radius: calc(15px * var(--ui-scale));
}

.voice-clone-header h3 {
  margin: 0;
  color: white;
  font-size: calc(22px * var(--ui-scale));
  font-weight: 600;
}

/* ===== RECORDING SECTION ===== */
.voice-recording-section {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: calc(12px * var(--ui-scale));
  padding: calc(20px * var(--ui-scale));
  margin-bottom: calc(20px * var(--ui-scale));
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.voice-recording-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  gap: calc(15px * var(--ui-scale));
  grid-template-areas:
    "record timer"
    "upload stt";
  align-items: stretch;
}

.voice-record-controls {
  grid-area: record;
}

.voice-timer-container {
  grid-area: timer;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.voice-file-upload {
  grid-area: upload;
}

.voice-stt-container {
  grid-area: stt;
  display: flex;
  align-items: center;
}

.voice-record-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: calc(8px * var(--ui-scale));
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: calc(12px * var(--ui-scale)) calc(20px * var(--ui-scale));
  border-radius: calc(25px * var(--ui-scale));
  cursor: pointer;
  font-size: calc(15px * var(--ui-scale));
  font-weight: 500;
  transition: all 0.3s ease;
  width: 100%;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
}

.voice-record-button:hover {
  background-color: #45a049;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.voice-record-button.recording {
  background-color: #f44336;
  animation: pulse 1.5s infinite;
}

.voice-timer {
  font-size: calc(18px * var(--ui-scale));
  font-weight: 600;
  min-width: calc(80px * var(--ui-scale));
  background-color: rgba(0, 0, 0, 0.4);
  padding: calc(12px * var(--ui-scale)) calc(18px * var(--ui-scale));
  border-radius: calc(20px * var(--ui-scale));
  text-align: center;
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

/* ===== WAVEFORM CONTAINER ===== */
.voice-waveform-container {
  background-color: rgba(0, 0, 0, 0.4);
  border-radius: calc(8px * var(--ui-scale));
  padding: calc(10px * var(--ui-scale));
  margin-bottom: calc(15px * var(--ui-scale));
  height: calc(60px * var(--ui-scale));
  display: flex;
  align-items: center;
  justify-content: center;
  transition: height 0.3s ease;
  overflow: hidden;
  width: 100%;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.voice-waveform-container.recording {
  height: calc(100px * var(--ui-scale));
}

#voiceWaveform {
  width: 100%;
  height: calc(40px * var(--ui-scale));
  transition: height 0.3s ease;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

#voiceWaveform.recording {
  height: calc(80px * var(--ui-scale));
}

/* ===== ACTION BUTTONS ===== */
.voice-clone-actions {
  display: flex;
  gap: calc(12px * var(--ui-scale));
  margin-top: calc(20px * var(--ui-scale));
}

.voice-clone-button,
.voice-cancel-button {
  flex: 1;
  padding: calc(12px * var(--ui-scale)) calc(20px * var(--ui-scale));
  border: none;
  border-radius: calc(8px * var(--ui-scale));
  cursor: pointer;
  font-size: calc(15px * var(--ui-scale));
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.voice-clone-button {
  background-color: #4CAF50;
  color: white;
}

.voice-clone-button:hover:not(:disabled) {
  background-color: #45a049;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.voice-clone-button:disabled {
  background-color: rgba(76, 175, 80, 0.4);
  cursor: not-allowed;
  opacity: 0.6;
}

.voice-cancel-button {
  background-color: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.voice-cancel-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

/* ===== VOICE BUFFER ===== */
.voice-buffer-container {
  margin-top: calc(15px * var(--ui-scale));
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: calc(15px * var(--ui-scale));
}

.voice-buffer-title {
  color: white;
  font-size: calc(14px * var(--ui-scale));
  margin-bottom: calc(10px * var(--ui-scale));
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

.voice-buffer-clear {
  background: none;
  border: none;
  color: #ff6b6b;
  font-size: calc(12px * var(--ui-scale));
  cursor: pointer;
  padding: calc(4px * var(--ui-scale)) calc(8px * var(--ui-scale));
  border-radius: calc(4px * var(--ui-scale));
  transition: all 0.2s ease;
}

.voice-buffer-clear:hover {
  background-color: rgba(255, 107, 107, 0.1);
}

.voice-buffer-list {
  display: flex;
  flex-direction: column;
  gap: calc(8px * var(--ui-scale));
  max-height: calc(200px * var(--ui-scale));
  overflow-y: auto;
}

/* ===== RESPONSIVE DESIGN DELEGATED TO VIEWER.CSS ===== */
/* All responsive button and control styling is handled in viewer.css */
/* This prevents conflicts and ensures consistent scaling */

/* ===== ANIMATIONS ===== */
@keyframes pulse {
  0% { opacity: 1; box-shadow: 0 3px 10px rgba(244, 67, 54, 0.3); }
  50% { opacity: 0.8; box-shadow: 0 5px 20px rgba(244, 67, 54, 0.5); }
  100% { opacity: 1; box-shadow: 0 3px 10px rgba(244, 67, 54, 0.3); }
}

/* ===== SCROLLBAR STYLING ===== */
.voice-clone-content::-webkit-scrollbar,
.talking-head-dropdown::-webkit-scrollbar,
.voice-buffer-list::-webkit-scrollbar {
  width: calc(6px * var(--ui-scale));
}

.voice-clone-content::-webkit-scrollbar-track,
.talking-head-dropdown::-webkit-scrollbar-track,
.voice-buffer-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: calc(3px * var(--ui-scale));
}

.voice-clone-content::-webkit-scrollbar-thumb,
.talking-head-dropdown::-webkit-scrollbar-thumb,
.voice-buffer-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: calc(3px * var(--ui-scale));
}

.voice-clone-content::-webkit-scrollbar-thumb:hover,
.talking-head-dropdown::-webkit-scrollbar-thumb:hover,
.voice-buffer-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* ===== UTILITY CLASSES ===== */
.talking-head-hidden {
  display: none !important;
}

.talking-head-visible {
  display: flex !important;
}

.talking-head-loading {
  opacity: 0.6;
  pointer-events: none;
  cursor: wait;
}

.talking-head-success {
  background-color: #4CAF50 !important;
}

.talking-head-error {
  background-color: #f44336 !important;
}

.talking-head-warning {
  background-color: #ff9800 !important;
}