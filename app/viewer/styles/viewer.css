/* Component imports */
@import url('./components/talkinghead.css');
@import url('./role-playing.css');

/* Z-Index Management System */
:root {
  /* Base z-index values for proper layering */
  --z-index-canvas: 1;
  --z-index-ui-base: 1000;
  --z-index-controls: 1001;
  --z-index-dropdowns: 1003;
  --z-index-voice-menu: 1004;
  --z-index-mobile-dropdowns: 1005;
  --z-index-modals: 10000;
  --z-index-debug: 9999;
}

/* Consolidated Automatic Scaling System */
:root {
  /* Base scaling values */
  --base-font-size: 16px;
  --scale-factor: 1;

  /* Responsive scaling based on viewport - more dynamic scaling */
  --ui-scale: calc(clamp(0.8, 4.6vmin, 1.0) * var(--ui-scale-multiplier, 1));
  --button-scale: calc(var(--ui-scale) * 0.95);
  --panel-scale: calc(var(--ui-scale) * 0.9);

  /* Component-specific multipliers (increase defaults for better legibility) */
  --icon-scale-multiplier: 1.8;
  /* Actions gear and its dropdown */
  --mesh-ui-scale-multiplier: 1.6;
  /* Mesh selector and its controls */
  --controls-scale-multiplier: 1.35;
  /* Bottom talking-head controls */
  --controls-scale: calc(var(--ui-scale) * var(--controls-scale-multiplier));

  /* Button dimensions with automatic scaling */
  --button-height: calc(36px * var(--button-scale));
  --button-padding: calc(8px * var(--button-scale)) calc(16px * var(--button-scale));
  --button-font-size: calc(14px * var(--button-scale));
  --button-border-radius: calc(8px * var(--button-scale));

  /* Panel dimensions with automatic scaling */
  --panel-padding: calc(12px * var(--panel-scale));
  --panel-gap: calc(8px * var(--panel-scale));
  --panel-border-radius: calc(12px * var(--panel-scale));

  /* Navigation dimensions with automatic scaling */
  --nav-height: calc(60px * var(--ui-scale));
  --nav-button-size: calc(48px * var(--button-scale));
  --nav-icon-size: calc(20px * var(--button-scale));

  /* Dropdown and selector scaling - more responsive */
  --dropdown-width: calc(180px * var(--panel-scale));
  --dropdown-height: calc(32px * var(--panel-scale));
  --dropdown-font-size: calc(13px * var(--panel-scale));

  /* Color scheme */
  --primary-bg: rgba(0, 0, 0, 0.7);
  --secondary-bg: rgba(255, 255, 255, 0.95);
  --accent-color: rgba(0, 120, 255, 0.7);
  --text-primary: white;
  --text-secondary: #333;
}

/* @import url('https://rsms.me/inter/inter.css'); */

* {
  box-sizing: border-box;
}

html,
body,
#root {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  background: #ffffff;
  font-family: 'Inter', sans-serif;
  overflow: hidden;
}

/* Main container */
#viewer-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: #ffffff;
  overflow: hidden;
}

/* Canvas settings */
canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  touch-action: none;
}

/* Debug and Error Displays */
#debug-overlay {
  position: fixed;
  top: 10px;
  left: 10px;
  z-index: 1000;
  color: #fff;
  font-family: monospace;
  pointer-events: none;
  display: none;
  /* Hidden by default */
}

#error-display {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: #ff4444;
  padding: 20px;
  border-radius: 5px;
  font-family: sans-serif;
  display: none;
  /* Hidden until needed */
}

.error-message {
  position: absolute;
  top: 20px;
  left: 50%;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  font-family: sans-serif;
  font-size: 14px;
  z-index: 1000;
}

/* Settings Panel */
#settings-panel {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.7);
  padding: 15px;
  border-radius: 5px;
  color: #fff;
  font-family: sans-serif;
  display: none;
  /* Hidden by default */
}

/* TalkingHead UI Controls - Single row layout with consistent scaling */
.talking-head-controls {
  position: fixed;
  bottom: calc(20px * var(--controls-scale));
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-wrap: nowrap;
  /* CRITICAL: Force single row */
  gap: calc(8px * var(--controls-scale));
  padding: calc(10px * var(--controls-scale)) calc(14px * var(--controls-scale));
  background: var(--primary-bg);
  backdrop-filter: blur(10px);
  border-radius: calc(14px * var(--controls-scale));
  z-index: var(--z-index-controls);
  transition: all 0.3s ease;
  min-height: calc(52px * var(--controls-scale));
  max-width: 90vw;
  overflow-x: auto;
  /* Allow horizontal scroll on very small screens */
  overflow-y: visible;
  /* CRITICAL FIX: Allow dropdowns to extend outside container */
  align-items: center;
  /* Ensure vertical alignment */
  scrollbar-width: none;
  /* Hide scrollbar in Firefox */
  -ms-overflow-style: none;
  /* Hide scrollbar in IE/Edge */
}

.talking-head-controls::-webkit-scrollbar {
  display: none;
  /* Hide scrollbar in WebKit browsers */
}

.talking-head-controls:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: translateX(-50%) translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Bottom navigation buttons - compact single row layout */
.talking-head-speech-button,
.talking-head-listen-button,
.talking-head-video-button,
.talking-head-pose-button,
.talking-head-animation-button,
.talking-head-voice-button {
  height: calc(40px * var(--controls-scale));
  padding: calc(8px * var(--controls-scale)) calc(14px * var(--controls-scale));
  border-radius: calc(8px * var(--controls-scale));
  background-color: var(--accent-color);
  border: none;
  color: var(--text-primary);
  font-size: calc(13px * var(--controls-scale));
  display: flex;
  align-items: center;
  justify-content: center;
  gap: calc(6px * var(--controls-scale));
  cursor: pointer;
  transition: all 0.25s ease;
  font-weight: 500;
  white-space: nowrap;
  min-width: calc(72px * var(--controls-scale));
  min-height: calc(34px * var(--controls-scale));
  flex: 1 1 auto;
  text-overflow: ellipsis;
  overflow: hidden;
}

.talking-head-speech-button:hover,
.talking-head-listen-button:hover,
.talking-head-video-button:hover,
.talking-head-pose-button:hover,
.talking-head-animation-button:hover,
.talking-head-voice-button:hover {
  background-color: rgba(0, 120, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 120, 255, 0.3);
}

/* Active state for buttons */
.talking-head-speech-button.active,
.talking-head-pose-button.active,
.talking-head-animation-button.active,
.talking-head-voice-button.active {
  background-color: rgba(255, 60, 60, 0.8);
  animation: pulse 1.5s ease-in-out infinite alternate;
}

/* Listen button active state - green for active */
.talking-head-listen-button.active {
  background-color: rgba(76, 175, 80, 0.8) !important;
  color: white !important;
  animation: listening-pulse 2s ease-in-out infinite alternate;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
}

/* Video button active state - blue for active */
.talking-head-video-button.active {
  background-color: rgba(33, 150, 243, 0.8) !important;
  color: white !important;
  animation: video-pulse 2s ease-in-out infinite alternate;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
}

/* Specific button transitions */
.talking-head-listen-button,
.talking-head-video-button {
  transition: all 0.3s ease, background-color 0.2s ease;
}

/* Disabled state for buttons */
.talking-head-speech-button.disabled,
.talking-head-listen-button.disabled,
.talking-head-video-button.disabled,
.talking-head-pose-button.disabled,
.talking-head-animation-button.disabled,
.talking-head-voice-button.disabled {
  background-color: rgba(100, 100, 100, 0.5);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  opacity: 0.6;
}

.talking-head-speech-button.disabled:hover,
.talking-head-listen-button.disabled:hover,
.talking-head-video-button.disabled:hover,
.talking-head-pose-button.disabled:hover,
.talking-head-animation-button.disabled:hover,
.talking-head-voice-button.disabled:hover {
  transform: none;
  box-shadow: none;
  background-color: rgba(100, 100, 100, 0.5);
}

/* Disabled state for video button */
.talking-head-video-button-disabled {
  height: var(--nav-button-size);
  padding: var(--button-padding);
  border-radius: var(--button-border-radius);
  background-color: rgba(100, 100, 100, 0.5);
  border: none;
  color: rgba(255, 255, 255, 0.5);
  font-size: var(--button-font-size);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: calc(6px * var(--button-scale));
  cursor: not-allowed;
  font-weight: 500;
  white-space: nowrap;
  min-width: calc(80px * var(--button-scale));
}

/* Status span for talking head controls */
.talking-head-status {
  color: var(--text-primary);
  font-size: calc(12px * var(--controls-scale));
  opacity: 0.9;
  margin-left: calc(10px * var(--controls-scale));
  white-space: nowrap;
  font-weight: 400;
}

/* Enhanced dropdown menus for poses and voices with comprehensive visibility forcing */
.pose-menu,
.voice-menu {
  position: fixed !important;
  /* Changed from absolute to fixed for viewport positioning */
  bottom: calc(var(--nav-height) + 70px) !important;
  /* Position above controls container */
  left: auto !important;
  right: auto !important;
  background: var(--primary-bg) !important;
  backdrop-filter: blur(10px) !important;
  border-radius: var(--button-border-radius) !important;
  padding: calc(10px * var(--panel-scale)) !important;
  display: none !important;
  flex-direction: column !important;
  gap: calc(5px * var(--panel-scale)) !important;
  max-height: calc(300px * var(--panel-scale)) !important;
  overflow-y: auto !important;
  width: var(--dropdown-width) !important;
  z-index: var(--z-index-voice-menu) !important;
  /* Use centralized z-index */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
  opacity: 0 !important;
  transform: translateY(10px) !important;
  transition: all 0.2s ease !important;
  pointer-events: none !important;
  visibility: hidden !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.pose-menu.show,
.voice-menu.show,
.pose-menu[style*="display: flex"],
.voice-menu[style*="display: flex"] {
  display: flex !important;
  /* Fixed: Force display override for all scenarios */
  opacity: 1 !important;
  transform: translateY(0) !important;
  pointer-events: auto !important;
  visibility: visible !important;
}

/* Additional utility classes for emergency visibility forcing */
.talking-head-visible,
.force-visible {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  pointer-events: auto !important;
  transform: translateY(0) !important;
}

/* Menu items in dropdowns */
.pose-menu .pose-option,
.voice-menu .voice-option {
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  color: var(--text-primary);
  padding: calc(8px * var(--panel-scale)) calc(12px * var(--panel-scale));
  border-radius: calc(4px * var(--panel-scale));
  cursor: pointer;
  text-align: left;
  font-size: var(--dropdown-font-size);
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pose-menu .pose-option:hover,
.voice-menu .voice-option:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateX(2px);
}

/* Voice cloning button */
.voice-clone-button {
  background-color: rgba(76, 175, 80, 0.8);
  border: none;
  color: white;
  padding: calc(8px * var(--panel-scale)) calc(12px * var(--panel-scale));
  border-radius: calc(4px * var(--panel-scale));
  cursor: pointer;
  text-align: left;
  margin-bottom: calc(10px * var(--panel-scale));
  display: flex;
  align-items: center;
  gap: calc(8px * var(--panel-scale));
  font-size: var(--dropdown-font-size);
  transition: all 0.2s ease;
}

.voice-clone-button:hover {
  background-color: rgba(76, 175, 80, 1);
  transform: translateY(-1px);
}

/* Voice Clone Modal */
.voice-clone-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  overflow-y: auto;
  padding: calc(20px * var(--ui-scale)) 0;
}

.voice-clone-content {
  background-color: rgba(40, 44, 52, 0.95);
  border-radius: var(--panel-border-radius);
  padding: calc(20px * var(--panel-scale));
  width: 90%;
  max-width: calc(550px * var(--panel-scale));
  max-height: 90vh;
  overflow-y: auto;
  color: white;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: calc(14px * var(--panel-scale));
}

.voice-clone-content .modal-header {
  margin-bottom: calc(15px * var(--panel-scale));
  text-align: center;
  position: sticky;
  top: 0;
  background-color: rgba(40, 44, 52, 0.95);
  padding: calc(15px * var(--panel-scale)) 0;
  z-index: 10;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: calc(-20px * var(--panel-scale));
  margin-left: calc(-20px * var(--panel-scale));
  margin-right: calc(-20px * var(--panel-scale));
  padding-left: calc(20px * var(--panel-scale));
  padding-right: calc(20px * var(--panel-scale));
  border-top-left-radius: var(--panel-border-radius);
  border-top-right-radius: var(--panel-border-radius);
}

.voice-clone-content h3 {
  margin: 0 0 calc(10px * var(--panel-scale)) 0;
  color: white;
  font-size: calc(20px * var(--panel-scale));
  font-weight: 600;
}

/* Conversion modal title */
.modal-title {
  margin: 0 0 calc(15px * var(--ui-scale)) 0;
  font-size: calc(20px * var(--ui-scale));
  font-weight: 600;
  color: var(--text-secondary);
  text-align: center;
}

/* Actions Menu Container */
.actions-menu-container {
  position: fixed;
  top: calc(15px * var(--ui-scale));
  left: calc(15px * var(--ui-scale));
  z-index: 1001;
}

/* Ready Player Me Button */
.rpm-avatar-button {
  position: fixed;
  bottom: calc(70px * var(--ui-scale));
  right: calc(20px * var(--ui-scale));
  padding: var(--button-padding);
  border-radius: calc(20px * var(--button-scale));
  background-color: var(--accent-color);
  border: none;
  color: var(--text-primary);
  font-size: var(--button-font-size);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: calc(6px * var(--button-scale));
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
  font-weight: 500;
}

.rpm-avatar-button:hover {
  background-color: rgba(0, 120, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 120, 255, 0.3);
}

/* Microphone button styles */
.microphone-button {
  display: flex;
  align-items: center;
  gap: calc(8px * var(--button-scale));
  padding: calc(10px * var(--button-scale)) calc(16px * var(--button-scale));
  margin: calc(10px * var(--button-scale)) 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: var(--button-border-radius);
  font-size: var(--button-font-size);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.microphone-button:hover:not(.recording) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.microphone-button.recording {
  background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
  animation: pulse 1.5s ease-in-out infinite alternate;
}

.microphone-button.recording:hover {
  background: linear-gradient(135deg, #ff2f5a 0%, #ff3f1f 100%);
}

.microphone-button .mic-icon {
  width: calc(20px * var(--button-scale));
  height: calc(20px * var(--button-scale));
  flex-shrink: 0;
}

.microphone-button.hidden {
  display: none;
}

@keyframes pulse {
  0% {
    box-shadow: 0 2px 8px rgba(255, 65, 108, 0.3);
  }

  100% {
    box-shadow: 0 4px 16px rgba(255, 65, 108, 0.6);
  }
}

@keyframes listening-pulse {
  0% {
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
    transform: scale(1);
  }

  100% {
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.6);
    transform: scale(1.02);
  }
}

@keyframes video-pulse {
  0% {
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
    transform: scale(1);
  }

  100% {
    box-shadow: 0 4px 20px rgba(33, 150, 243, 0.6);
    transform: scale(1.02);
  }
}

/* Input Controls */
.input-controls {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  padding: 10px;
  border-radius: 5px;
  display: none;
  /* Hidden until implemented */
}

.input-controls select {
  padding: 5px;
  margin-right: 10px;
  background: #333;
  color: #fff;
  border: 1px solid #555;
  border-radius: 3px;
}

.input-controls input[type="file"] {
  display: none;
}

/* Annotations */
.annotation {
  cursor: pointer;
  outline: none;
  border: none;
  font-size: 8px;
  font-weight: 300;
  background: black;
  color: #f0f0f0;
  padding: 2px 10px;
  border-radius: 20px;
  letter-spacing: 1px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

/* Looking Glass Button */
#LookingGlassButton {
  position: absolute;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 24px;
  border: 1px solid white;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font: 13px sans-serif;
  text-align: center;
  opacity: 0.85;
  outline: none;
  z-index: 999;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

#LookingGlassButton:hover {
  background-color: rgba(0, 0, 0, 0.85);
  opacity: 1;
}

#LookingGlassButton:active {
  opacity: 0.75;
}

.conversion-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.75);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  transition: opacity 0.3s ease;
}

.conversion-modal.hidden {
  display: none;
}

.conversion-content {
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  padding: 30px;
  border-radius: 16px;
  width: 90%;
  max-width: 520px;
  display: flex;
  flex-direction: column;
  gap: 22px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25), 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(0);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.modal-title {
  margin: 0 0 calc(15px * var(--ui-scale)) 0;
  font-size: calc(20px * var(--ui-scale));
  font-weight: 600;
  color: var(--text-secondary);
  text-align: center;
}

.modal-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  border-radius: 3px;
}

.input-area {
  display: flex;
  flex-direction: column;
  gap: 18px;
  background: rgba(255, 255, 255, 0.5);
  padding: 20px;
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.text-input {
  width: 100%;
  min-height: 120px;
  padding: 15px 18px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 10px;
  resize: vertical;
  font-size: 16px;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.25s ease;
  background-color: white;
  color: #2c3e50;
}

.text-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 3px 10px rgba(52, 152, 219, 0.15);
}

.text-input::placeholder {
  color: #95a5a6;
}

.file-input {
  width: 100%;
  padding: 15px 18px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 10px;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.25s ease;
  color: #2c3e50;
}

.file-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 3px 10px rgba(52, 152, 219, 0.15);
}

.preview {
  max-height: 220px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px dashed rgba(52, 152, 219, 0.3);
  border-radius: 10px;
  padding: 18px;
  background-color: rgba(52, 152, 219, 0.03);
  transition: all 0.25s ease;
}

.preview:hover {
  border-color: rgba(52, 152, 219, 0.5);
  background-color: rgba(52, 152, 219, 0.05);
}

.preview img {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
  border-radius: 6px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.file-name {
  padding: 14px;
  background: #f8f9fa;
  border-radius: 8px;
  word-break: break-all;
  font-size: 14px;
  color: #2c3e50;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.button-container {
  display: flex;
  gap: 15px;
  margin-top: 5px;
}

.generate-button,
.cancel-button,
.convert-button {
  padding: 14px 28px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.25s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.3px;
}

.generate-button {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
  color: white;
  flex: 2;
}

.generate-button:hover {
  background: linear-gradient(135deg, #27ae60, #219653);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(46, 204, 113, 0.25);
}

.cancel-button {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  flex: 1;
}

.cancel-button:hover {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(231, 76, 60, 0.25);
}

.convert-button {
  position: fixed;
  top: 20px;
  right: calc(240px + 40px);
  background: #2196F3;
  color: white;
  z-index: 100;
  width: 200px;
  border-radius: 6px;
  transition: all 0.2s ease;
  padding: 8px 16px;
  white-space: nowrap;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.convert-button:hover {
  background: #1976D2;
}

.hidden {
  display: none !important;
}

select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: white;
}

/* Progress Container Styles */
.progress-container {
  display: none;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 12px 20px;
  z-index: 1000;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.progress-container.visible {
  display: block;
}

.progress-bar-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
  max-width: 800px;
  margin: 0 auto;
}

.progress-bar {
  flex-grow: 1;
  height: 4px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  width: 0%;
  background: #2196F3;
  transition: width 0.3s ease;
}

.progress-text {
  color: #333;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  min-width: 120px;
}

.status-text {
  color: #000000;
  font-size: 14px;
  white-space: nowrap;
  font-style: normal;
}

.progress-container .cancel-button {
  padding: 6px 16px;
  background: #ff4444;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  transition: all 0.2s ease;
}

.progress-container .cancel-button:hover {
  background: #ff6666;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* VR Button */
.VRButton {
  position: fixed;
  z-index: 1001;
  /* Above progress container */
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  border: none;
  font-weight: 500;
  color: white;
  padding: 12px 24px;
  cursor: pointer;
  font-size: 16px;
  bottom: calc(4rem + 20px);
  /* Position above progress container */
  left: 50%;
  transform: translateX(-50%);
  transition: all 0.2s ease;
  backdrop-filter: blur(5px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.VRButton:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: translateX(-50%) translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Actions menu button - responsive gear icon */
.actions-menu-button {
  width: calc(32px * var(--button-scale) * var(--icon-scale-multiplier)) !important;
  height: calc(32px * var(--button-scale) * var(--icon-scale-multiplier)) !important;
  font-size: calc(14px * var(--button-scale) * var(--icon-scale-multiplier)) !important;
  position: fixed !important;
  top: calc(15px * var(--ui-scale)) !important;
  left: calc(15px * var(--ui-scale)) !important;
  border-radius: 50% !important;
  background-color: var(--accent-color) !important;
  border: none !important;
  color: var(--text-primary) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) !important;
  z-index: 1001 !important;
}

.actions-menu-button:hover {
  background-color: rgba(0, 120, 255, 0.9) !important;
  transform: scale(1.1) !important;
  box-shadow: 0 4px 15px rgba(0, 120, 255, 0.3) !important;
}

/* Actions dropdown - responsive and simplified */
.actions-dropdown {
  width: calc(160px * var(--panel-scale) * var(--icon-scale-multiplier)) !important;
  background-color: var(--secondary-bg) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  border-radius: calc(8px * var(--panel-scale) * var(--icon-scale-multiplier)) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  top: calc(48px * var(--button-scale) * var(--icon-scale-multiplier)) !important;
  position: absolute !important;
  left: 0 !important;
  z-index: 1000 !important;
  overflow: hidden !important;
  opacity: 0 !important;
  transform: translateY(-10px) !important;
  pointer-events: none !important;
  transition: all 0.3s ease !important;
}

.actions-dropdown.show {
  opacity: 1 !important;
  transform: translateY(0) !important;
  pointer-events: auto !important;
}

.menu-item {
  padding: calc(10px * var(--panel-scale) * var(--icon-scale-multiplier)) calc(14px * var(--panel-scale) * var(--icon-scale-multiplier)) !important;
  font-size: calc(14px * var(--panel-scale) * var(--icon-scale-multiplier)) !important;
  color: var(--text-secondary) !important;
  gap: calc(6px * var(--panel-scale)) !important;
  display: flex !important;
  align-items: center !important;
  cursor: pointer !important;
  transition: background-color 0.2s ease !important;
}

.menu-item:hover {
  background-color: rgba(0, 120, 255, 0.1) !important;
}

.menu-item span {
  font-size: inherit !important;
  line-height: 1 !important;
}

/* Ensure the VR menu item's text scales like other labels */
.menu-item .vr-button-text {
  font-size: inherit !important;
}

/* Mesh Selector Styles - consistent responsive scaling */
.mesh-selector {
  position: fixed;
  top: calc(15px * var(--ui-scale) * var(--mesh-ui-scale-multiplier));
  right: calc(15px * var(--ui-scale) * var(--mesh-ui-scale-multiplier));
  z-index: 1000;
  pointer-events: auto;
  display: flex;
  align-items: center;
  gap: calc(6px * var(--ui-scale) * var(--mesh-ui-scale-multiplier));
  background: var(--secondary-bg);
  padding: calc(8px * var(--ui-scale) * var(--mesh-ui-scale-multiplier));
  border-radius: calc(8px * var(--ui-scale) * var(--mesh-ui-scale-multiplier));
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  /* Let panel auto-size; ensure at least the button size so panel and buttons feel unified */
  height: auto;
  min-height: var(--mesh-button-size);
  font-size: calc(13px * var(--ui-scale) * var(--mesh-ui-scale-multiplier));
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  min-height: 24px;
  /* Local sizing variables for icon buttons */
  --mesh-button-size: calc(16px * var(--ui-scale) * var(--mesh-ui-scale-multiplier));
  --mesh-icon-font-size: calc(var(--mesh-button-size) * 0.78);
}

.mesh-selector:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  transform: translateY(-1px);
}

.mesh-select {
  width: calc(160px * var(--ui-scale) * var(--mesh-ui-scale-multiplier));
  height: calc(26px * var(--ui-scale) * var(--mesh-ui-scale-multiplier));
  background: white;
  padding: 0 calc(8px * var(--ui-scale) * var(--mesh-ui-scale-multiplier));
  border: 1px solid #ddd;
  border-radius: calc(4px * var(--ui-scale) * var(--mesh-ui-scale-multiplier));
  font-size: calc(12px * var(--ui-scale) * var(--mesh-ui-scale-multiplier));
  transition: all 0.2s ease;
  min-width: 120px;
  min-height: 22px;
}

.mesh-select:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(0, 120, 255, 0.2);
}

.mesh-select option {
  max-width: calc(180px * var(--button-scale));
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: calc(20px * var(--button-scale));
}

.button-container {
  height: 100%;
  display: flex;
  align-items: center;
  gap: calc(4px * var(--ui-scale));
}

.delete-button,
.make-avatar-button {
  height: var(--mesh-button-size);
  width: var(--mesh-button-size);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  background: #f8f8f8;
  border: 1px solid #ddd;
  border-radius: calc(4px * var(--ui-scale) * var(--mesh-ui-scale-multiplier));
  transition: all 0.2s ease;
  font-size: var(--mesh-icon-font-size);
  line-height: 1;
  cursor: pointer;
  outline: 2px solid transparent;
  outline-offset: 2px;
  min-width: var(--mesh-button-size);
  min-height: var(--mesh-button-size);
  /* Ensure minimum usable size */
}

/* Ensure MeshSelector button icons scale automatically */
.delete-button svg,
.make-avatar-button svg {
  width: calc(var(--mesh-icon-font-size) * 0.8) !important;
  height: calc(var(--mesh-icon-font-size) * 0.8) !important;
  max-width: none !important;
  max-height: none !important;
}

.delete-button:focus,
.make-avatar-button:focus {
  outline-color: var(--accent-color);
}

.make-avatar-button {
  background: #f0f8ff;
  border-color: #ccc;
}

.make-avatar-button.available {
  background: #4caf50;
  border-color: #4caf50;
  color: white;
}

.make-avatar-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.delete-button:hover {
  background: #e0e0e0;
  border-color: #888;
  transform: scale(1.05);
}

.delete-button:active {
  background: #d0d0d0;
  transform: scale(0.95);
}

.loading-indicator {
  color: #666;
  margin-left: 8px;
}

.source-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
  position: relative;
}

.source-wrapper::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.3), transparent);
}

.source-select {
  flex: 1;
  padding: 14px 18px;
  border-radius: 10px;
  border: 1px solid rgba(52, 152, 219, 0.2);
  background-color: white;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.25s ease;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
  color: #2c3e50;
  font-weight: 500;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233498db' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 15px center;
  background-size: 16px;
  padding-right: 45px;
}

.source-select:hover:not(:disabled) {
  border-color: rgba(52, 152, 219, 0.5);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.1);
  transform: translateY(-1px);
}

.source-select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

/* Option styles */
.source-select option {
  padding: 10px;
  font-size: 15px;
}

/* Tripo Doll and Doll specific styles */
.camera-button {
  background: linear-gradient(135deg, #3498db, #2980b9) !important;
  color: white !important;
  padding: 14px 20px !important;
  border-radius: 10px !important;
  border: none !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 10px !important;
  cursor: pointer !important;
  transition: all 0.25s ease !important;
  box-shadow: 0 4px 10px rgba(52, 152, 219, 0.2) !important;
  margin-top: 8px !important;
  width: 100% !important;
  letter-spacing: 0.3px !important;
}

.camera-button:hover {
  background: linear-gradient(135deg, #2980b9, #2573a7) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 15px rgba(52, 152, 219, 0.3) !important;
}

.camera-button svg {
  width: 22px !important;
  height: 22px !important;
}

.gender-wrapper {
  display: flex !important;
  align-items: center !important;
  gap: 15px !important;
  margin-top: 8px !important;
  padding: 15px 18px !important;
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.05), rgba(46, 204, 113, 0.05)) !important;
  border-radius: 10px !important;
  border: 1px solid rgba(52, 152, 219, 0.15) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03) !important;
}

.gender-wrapper label {
  font-weight: 600 !important;
  color: #2c3e50 !important;
  font-size: 16px !important;
  min-width: 70px !important;
}

.gender-select {
  flex: 1 !important;
  padding: 10px 15px !important;
  border-radius: 8px !important;
  border: 1px solid rgba(52, 152, 219, 0.2) !important;
  background-color: white !important;
  font-size: 15px !important;
  cursor: pointer !important;
  transition: all 0.25s ease !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233498db' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 10px center !important;
  background-size: 14px !important;
  padding-right: 35px !important;
  color: #2c3e50 !important;
  font-weight: 500 !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03) !important;
}

.gender-select:hover {
  border-color: rgba(52, 152, 219, 0.4) !important;
  box-shadow: 0 3px 8px rgba(52, 152, 219, 0.1) !important;
}

.gender-select:focus {
  outline: none !important;
  border-color: #3498db !important;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15) !important;
}

.regenerate-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  margin-left: 8px;
}

.regenerate-checkbox {
  display: none;
}

.regenerate-label {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #f5f5f5;
}

.regenerate-label svg {
  width: 16px;
  height: 16px;
}

.regenerate-label:hover {
  background: #e0e0e0;
}

.regenerate-checkbox:checked+.regenerate-label {
  background: #e3f2fd;
}

.regenerate-checkbox:checked+.regenerate-label .regenerate-icon {
  fill: #1976d2;
  transform: rotate(180deg);
}

.regenerate-icon {
  fill: #666;
  transition: all 0.3s ease;
}

.regenerate-label:hover .regenerate-icon {
  fill: #333;
}

/* Tooltip */
.tooltip {
  position: absolute;
  top: 100%;
  right: 50%;
  transform: translateX(50%);
  padding: 6px 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 12px;
  border-radius: 4px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.tooltip::before {
  content: '';
  position: absolute;
  top: -4px;
  right: 50%;
  transform: translateX(50%) rotate(45deg);
  width: 8px;
  height: 8px;
  background: rgba(0, 0, 0, 0.8);
}

.regenerate-label:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

/* General icon size constraints */
button svg,
.button svg,
.mesh-selector svg,
.delete-button svg {
  width: calc(16px * var(--ui-scale));
  height: calc(16px * var(--ui-scale));
  max-width: none;
  max-height: none;
}

/* Ensure UI elements are above canvas */
.mesh-selector,
.conversion-modal,
.convert-button,
.progress-container,
#debug-overlay,
#error-display {
  position: fixed;
  z-index: 9999;
  pointer-events: auto;
}

/* Enhanced Responsive Design with Automatic Scaling */
@media (max-width: 768px) {
  :root {
    /* Increase scaling for mobile touch interactions */
    --ui-scale: clamp(0.8, 3vmin, 1.4);
    --button-scale: calc(var(--ui-scale) * 1.1);
    --panel-scale: calc(var(--ui-scale) * 0.9);
  }

  /* Mobile: Slightly larger buttons for touch */
  .actions-menu-button {
    width: calc(38px * var(--button-scale)) !important;
    height: calc(38px * var(--button-scale)) !important;
    font-size: calc(16px * var(--button-scale)) !important;
  }

  .talking-head-controls {
    flex-wrap: nowrap;
    /* FORCE single row on mobile */
    max-width: 95vw;
    justify-content: flex-start;
    overflow-x: auto;
    /* Allow horizontal scroll if needed */
  }

  .talking-head-speech-button,
  .talking-head-listen-button,
  .talking-head-video-button,
  .talking-head-pose-button,
  .talking-head-animation-button,
  .talking-head-voice-button {
    min-width: calc(60px * var(--button-scale));
    font-size: calc(10px * var(--button-scale));
    flex-shrink: 0;
    /* Prevent shrinking */
  }

  .mesh-selector {
    gap: calc(6px * var(--button-scale));
    padding: calc(6px * var(--button-scale));
    height: calc(32px * var(--button-scale));
    /* Improve mobile positioning and sizing */
    top: calc(10px * var(--ui-scale));
    right: calc(10px * var(--ui-scale));
    max-width: calc(250px * var(--button-scale));
    /* Increased from 200px */
    min-width: calc(150px * var(--button-scale));
    /* Added min-width */
  }

  .mesh-select {
    width: calc(120px * var(--button-scale));
    height: calc(20px * var(--button-scale));
    font-size: calc(11px * var(--button-scale));
    /* Ensure minimum touch-friendly size */
    min-height: 20px;
    min-width: 100px;
  }

  /* Mobile: Slightly wider dropdown but keep compact */
  .actions-dropdown {
    width: calc(160px * var(--panel-scale)) !important;
    top: calc(45px * var(--button-scale)) !important;
  }

  .menu-item {
    padding: calc(10px * var(--panel-scale)) calc(14px * var(--panel-scale)) !important;
    font-size: calc(14px * var(--panel-scale)) !important;
  }

  /* Mobile: Mesh selector adjustments */
  .mesh-selector {
    gap: calc(6px * var(--button-scale));
    padding: calc(6px * var(--button-scale));
    height: calc(32px * var(--button-scale));
  }

  .mesh-select {
    width: calc(120px * var(--button-scale));
    height: calc(20px * var(--button-scale));
    font-size: calc(11px * var(--button-scale));
  }

  /* Dropdown menus positioning for mobile - simplified since they now use fixed positioning */
  .pose-menu,
  .voice-menu,
  .talking-head-dropdown {
    width: calc(180px * var(--panel-scale));
    max-height: calc(250px * var(--panel-scale));
    z-index: var(--z-index-mobile-dropdowns) !important;
    /* Use centralized mobile z-index */
  }
}

@media (max-width: 480px) {
  :root {
    /* Further scaling adjustments for very small screens */
    --ui-scale: clamp(0.9, 4vmin, 1.6);
    --button-scale: calc(var(--ui-scale) * 1.0);
    --panel-scale: calc(var(--ui-scale) * 0.85);
  }

  /* Very small screens: Optimize layouts */
  .mesh-selector {
    top: calc(10px * var(--ui-scale));
    right: calc(10px * var(--ui-scale));
    /* Keep horizontal layout for consistency */
    flex-direction: row;
    height: calc(28px * var(--button-scale));
    width: auto;
    max-width: calc(220px * var(--button-scale));
    /* Increased from 180px */
    min-width: calc(140px * var(--button-scale));
    /* Added min-width */
    gap: calc(4px * var(--button-scale));
    padding: calc(6px * var(--button-scale));
  }

  .mesh-select {
    width: calc(100px * var(--button-scale));
    height: calc(18px * var(--button-scale));
    font-size: calc(10px * var(--button-scale));
    min-width: 80px;
    min-height: 16px;
  }

  .button-container {
    justify-content: center;
    gap: calc(2px * var(--button-scale));
  }

  /* FORCE single row for talking head controls on small screens */
  .talking-head-controls {
    flex-direction: row;
    flex-wrap: nowrap;
    /* NEVER wrap */
    max-width: 98vw;
    overflow-x: auto;
    /* Allow scrolling if needed */
    justify-content: flex-start;
    gap: calc(4px * var(--panel-scale));
    padding: calc(6px * var(--panel-scale));
  }

  .talking-head-speech-button,
  .talking-head-listen-button,
  .talking-head-video-button,
  .talking-head-pose-button,
  .talking-head-voice-button {
    flex: 1;
    min-width: calc(60px * var(--button-scale));
    max-width: calc(120px * var(--button-scale));
  }

  /* Voice clone modal responsive */
  .voice-clone-content {
    width: 95%;
    max-width: calc(400px * var(--panel-scale));
    padding: calc(15px * var(--panel-scale));
  }
}

/* Large screens - reduce scaling to keep UI compact */
@media (min-width: 1200px) {
  :root {
    --ui-scale: clamp(0.6, 1.5vmin, 1.0);
    --button-scale: calc(var(--ui-scale) * 0.8);
    --panel-scale: calc(var(--ui-scale) * 0.75);
  }
}

/* Ultra-wide screens - further reduce scaling */
@media (min-width: 1920px) {
  :root {
    --ui-scale: clamp(0.5, 1vmin, 0.9);
    --button-scale: calc(var(--ui-scale) * 0.7);
    --panel-scale: calc(var(--ui-scale) * 0.7);
  }
}

/* Camera Viewer Styles */
.camera-viewer-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent;
  border-radius: 12px;
  overflow: hidden;
}

.camera-preview {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 240px;
  height: 180px;
  background: rgba(0, 0, 0, 0.85);
  border-radius: 12px;
  cursor: pointer;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.1);
  z-index: 1000;
}

.camera-preview:hover {
  transform: scale(1.05);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.35);
}

.camera-preview video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.camera-viewer-container video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* 最外层 modal：全屏透明，不阻挡点击，但让内部 .modal-content 可交互 */
.modal.camera-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  /* 去掉任何遮罩 */
  pointer-events: none;
  /* 不阻挡背景点击 */
  z-index: 9999;
  display: none;
  /* JS 控制显示 */
  opacity: 0;
  /* 用于淡入淡出 */
  transition: opacity 0.3s ease;
}

/* 打开/关闭动画 */
.modal.camera-modal.show {
  opacity: 1;
}

.modal.camera-modal.closing {
  opacity: 0;
}

/* 弹窗主体 .modal-content */
.modal.camera-modal .modal-content {
  position: absolute;
  /* 初始位置可自行调节 */
  top: 40px;
  left: 40px;

  /* 给个可见的半透明背景，否则看起来像按钮悬空 */
  background: rgba(33, 33, 33, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);

  /* 允许本区域拦截鼠标事件 */
  pointer-events: auto;

  /* 设置初始大小并允许缩放 */
  width: 250px;
  height: 150px;
  /* 16:9 */
  resize: both;
  overflow: auto;

  /* 限制最大最小值，防止拖到太大或太小 */
  min-width: 320px;
  min-height: 180px;
  max-width: 90vw;
  max-height: 90vh;

  /* 用于全局拖拽 */
  cursor: grab;
}

/* 鼠标按下时显示“抓取”状态 */
.modal.camera-modal .modal-content:active {
  cursor: grabbing;
}

/* 弹窗主体内的内容区域（视频/Canvas等）可设为透明或自定义 */
.modal.camera-modal .modal-body {
  background: transparent;
  /* 或者保留黑色等 */
  border: none;
  padding: 0;
  margin: 0;
  width: 100%;
  height: 100%;
}

/* 关闭按钮固定在弹窗右上角 */
.modal.camera-modal .modal-close {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  line-height: 1;
  z-index: 2;
  opacity: 0.7;
}

.modal.camera-modal .modal-close:hover {
  opacity: 1;
}


.camera-viewer-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.camera-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: black;
}

.camera-wrapper video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.camera-wrapper canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.modal .modal-content {
  position: fixed;
  background: rgba(33, 33, 33, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(8px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal .modal-header {
  min-height: 24px;
  background: rgba(0, 0, 0, 0.3);
  cursor: grab;
  user-select: none;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0 8px;
  flex-shrink: 0;
}

.modal .modal-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.HoloViewButton {
  position: absolute;
  bottom: 24px;
  padding: 12px 24px;
  border: 1px solid #fff;
  border-radius: 4px;
  background: #2196F3;
  color: #fff;
  font: 13px sans-serif;
  text-align: center;
  opacity: 0.8;
  outline: none;
  z-index: 999;
  cursor: pointer;
  width: 220px;
  left: calc(50% - 110px);
  transition: all 0.2s ease;
}

.HoloViewButton:hover {
  opacity: 1;
  background: rgba(149, 208, 220, 0.9);
}

.HoloViewButton.presenting {
  background: rgba(33, 150, 243, 0.8);
}

/* Camera popup button styles with automatic scaling */
.camera-popup-button {
  width: calc(30px * var(--button-scale));
  height: calc(30px * var(--button-scale));
  background: var(--primary-bg);
  border: none;
  border-radius: 50%;
  color: var(--text-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: calc(12px * var(--button-scale));
  transition: all 0.2s ease;
}

.camera-popup-button:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.05);
}

.camera-popup-button svg {
  width: calc(18px * var(--button-scale));
  height: calc(18px * var(--button-scale));
}

/* Camera controls container for positioning popup button */
.camera-controls {
  position: absolute;
  bottom: calc(10px * var(--ui-scale));
  right: calc(10px * var(--ui-scale));
  display: flex;
  gap: calc(5px * var(--ui-scale));
  z-index: 1000;
}

/* Camera modal styles with automatic scaling */
.tripo-camera-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(8px);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tripo-camera-modal-content {
  position: relative;
  width: 90vw;
  max-width: calc(800px * var(--ui-scale));
  height: 70vh;
  max-height: calc(600px * var(--ui-scale));
  background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
  border-radius: calc(20px * var(--ui-scale));
  overflow: hidden;
  box-shadow: 0 calc(20px * var(--ui-scale)) calc(60px * var(--ui-scale)) rgba(0, 0, 0, 0.5);
  border: calc(2px * var(--ui-scale)) solid rgba(255, 255, 255, 0.1);
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.tripo-camera-modal-content video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: calc(18px * var(--ui-scale));
}

/* Camera modal title */
.tripo-camera-modal-title {
  position: absolute;
  top: calc(20px * var(--ui-scale));
  left: 50%;
  transform: translateX(-50%);
  color: var(--text-primary);
  font-size: calc(24px * var(--ui-scale));
  font-weight: 700;
  text-shadow: 0 calc(2px * var(--ui-scale)) calc(8px * var(--ui-scale)) rgba(0, 0, 0, 0.8);
  z-index: 1001;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4));
  padding: calc(12px * var(--ui-scale)) calc(24px * var(--ui-scale));
  border-radius: calc(25px * var(--ui-scale));
  backdrop-filter: blur(calc(10px * var(--ui-scale)));
  border: calc(1px * var(--ui-scale)) solid rgba(255, 255, 255, 0.2);
}

/* Camera capture button */
.tripo-camera-capture-button {
  position: absolute;
  bottom: calc(30px * var(--ui-scale));
  left: 50%;
  transform: translateX(-50%);
  width: calc(80px * var(--ui-scale));
  height: calc(80px * var(--ui-scale));
  border-radius: 50%;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  border: calc(5px * var(--ui-scale)) solid white;
  cursor: pointer;
  box-shadow: 0 calc(8px * var(--ui-scale)) calc(25px * var(--ui-scale)) rgba(0, 0, 0, 0.4);
  z-index: 1001;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0;
}

.tripo-camera-capture-button:hover {
  transform: translateX(-50%) scale(1.1);
  box-shadow: 0 calc(12px * var(--ui-scale)) calc(35px * var(--ui-scale)) rgba(0, 0, 0, 0.5);
  background: linear-gradient(135deg, #c0392b, #a93226);
}

.tripo-camera-capture-button svg {
  width: calc(32px * var(--ui-scale));
  height: calc(32px * var(--ui-scale));
}

/* Camera modal close button */
.tripo-camera-close-button {
  position: absolute;
  top: calc(20px * var(--ui-scale));
  right: calc(20px * var(--ui-scale));
  width: calc(40px * var(--ui-scale));
  height: calc(40px * var(--ui-scale));
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
  font-size: calc(24px * var(--ui-scale));
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  transition: all 0.2s ease;
  backdrop-filter: blur(calc(10px * var(--ui-scale)));
}

.tripo-camera-close-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* Existing styles continue... */