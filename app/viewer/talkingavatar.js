/**
 * Refactored TalkingAvatar - Service-Oriented Architecture
 * 
 * This refactored version demonstrates the service-oriented architecture approach
 * by using dependency injection for all extracted services, reducing complexity
 * from 2542 lines to approximately 800 lines while maintaining all functionality.
 * 
 * Key improvements:
 * - Clean separation of concerns through service injection
 * - Simplified UI layer focused only on UI/UX concerns
 * - Enterprise-grade error handling and resource management
 * - Comprehensive logging and monitoring
 * - Production-ready reliability patterns
 */

import { createLogger, LogLevel } from '@/utils/logger.ts';
import { TOOL_CONFIG } from './viewerConfig.js';
import { ALIYUN_MODELS, ALIYUN_AUDIO_CONFIG } from '@/agent/models/aliyun/AliyunConfig.js';
import { getEnvVar } from '@/config/env.ts';

// Import unified media state management
import { MediaStateManager } from '@/media/state/MediaStateManager.ts';

// Import extracted services (moved to app/viewer/services with cleaned naming)
import { MediaCoordinator } from './services/mediaCoordinator.js';
import { AudioProcessor } from './services/audioProcessor.js';
import { AgentCoordinator } from './services/agentCoordinator.js';
import { StateCoordinator } from './services/stateCoordinator.js';
import { ConfigManager } from './services/configManager.js';
import { ResourceManager } from './services/resourceManager.js';

/**
 * Refactored TalkingAvatar Class - Service-Oriented Architecture
 * 
 * Demonstrates clean separation of concerns through dependency injection.
 * All complex logic is delegated to specialized services.
 */
export class TalkingAvatar {
    constructor(viewer, options = {}) {
        this.logger = createLogger('TalkingAvatar', LogLevel.DEBUG);
        this.viewer = viewer;
        this.options = options;

        // Core UI state - simplified to bare minimum
        this.initialized = false;
        this.inTalkingHeadMode = false;
        this.isSpeaking = false;

        // Avatar 3D references - UI concerns only
        this.avatarMesh = null;
        this.morphTargetDictionary = null;
        this.morphTargetInfluences = null;
        this.talkingHead = null;
        this.talkingHeadControls = null;
        this.rpm = null;

        // UI components
        this.ui = null;
        this.animationController = null;
        this.currentTalkingAnimation = null;

        // Initialize services with dependency injection
        this.initializeServices();

        this.logger.info('🎭 TalkingAvatar initialized with service-oriented architecture', {
            servicesInitialized: true,
            viewerAttached: !!viewer,
            optionsProvided: Object.keys(options).length
        });
    }

    /**
     * Initialize all services with dependency injection pattern
     */
    initializeServices() {
        try {
            this.logger.info('🏭 Initializing services with dependency injection...');

            // 1. Configuration Management Service
            this.configManager = new ConfigManager({
                logger: this.logger,
                enableConfigPersistence: this.options.enableConfigPersistence,
                configValidation: this.options.configValidation
            });

            // 2. Resource Management Service  
            this.resourceManager = new ResourceManager({
                logger: this.logger,
                enableResourceTracking: this.options.enableResourceTracking,
                cleanupTimeoutMs: this.options.cleanupTimeoutMs,
                viewer: this.viewer // Pass viewer reference to ResourceManager
            });

            // 3. Media Coordination Service
            this.mediaCoordinator = new MediaCoordinator({
                logger: this.logger,
                enableVideoCapture: this.options.enableVideoCapture ?? true,
                enableInputCoordination: this.options.enableInputCoordination ?? true, // Enable by default
                mediaConfig: this.options.mediaConfig
            });

            // 4. Audio Processing Service
            this.audioProcessor = new AudioProcessor({
                logger: this.logger,
                processingConfig: this.options.audioProcessingConfig,
                enableConnectionHealthMonitoring: this.options.enableConnectionHealthMonitoring
            });

            // 5. Agent Coordination Service
            this.agentCoordinator = new AgentCoordinator({
                logger: this.logger,
                enableVADHandlers: this.options.enableVADHandlers,
                agentReadinessOptions: this.options.agentReadinessOptions
            });

            // 6. State Coordination Service
            this.stateCoordinator = new StateCoordinator({
                logger: this.logger,
                enableAutoResponse: this.options.enableAutoResponse,
                realtimeSessionTimeoutMs: this.options.realtimeSessionTimeoutMs
            });

            this.logger.info('✅ All services initialized successfully with dependency injection');

        } catch (error) {
            this.logger.error('❌ Error initializing services:', error);
            throw new Error(`Service initialization failed: ${error.message}`);
        }
    }

    /**
     * Initialize the TalkingAvatar - simplified to core UI concerns
     */
    async initialize() {
        if (this.initialized) {
            this.logger.debug('TalkingAvatar already initialized');
            return true;
        }

        try {
            this.logger.info('🚀 Initializing TalkingAvatar with service delegation...');

            // Initialize configuration from options
            this.configManager.initializeConfiguration({
                enableAutomaticResponses: this.options.enableAutomaticResponses,
                responseContext: this.options.responseContext,
                timingProfile: this.options.timingProfile,
                analysisInterval: this.options.analysisInterval,
                llmService: this.options.llmService
            });

            // Initialize agent service through coordinator
            const agentConfig = {
                toolRegistrationConfig: TOOL_CONFIG.registration,
                toolOptions: TOOL_CONFIG.toolOptions,
                modelProvider: 'aliyun',
                aliyunApiKey: getEnvVar('VITE_DASHSCOPE_API_KEY', ''),
                modelOptions: {
                    defaultModel: ALIYUN_MODELS.QWEN_OMNI,
                    enableRealtime: true,
                    audioConfig: ALIYUN_AUDIO_CONFIG
                },
                agentConfig: {
                    enableAutonomousTools: true,
                    enableDualBrain: true,
                    maxIterations: 10
                }
            };

            const agentInitialized = await this.agentCoordinator.initializeAgentService(agentConfig);
            if (!agentInitialized) {
                throw new Error('Failed to initialize agent service');
            }

            // Initialize MediaCoordinator state (MediaStateManager + MediaCaptureManager bridge)
            const mediaInitialized = await this.mediaCoordinator.initialize();
            if (!mediaInitialized) {
                this.logger.warn('⚠️ MediaCoordinator initialization failed - media state control may be limited');
            }

            // Initialize input coordination through media coordinator
            const agentService = this.agentCoordinator.getAgentService();
            const inputInitialized = await this.mediaCoordinator.initializeInputCoordination(agentService);
            if (!inputInitialized) {
                this.logger.warn('⚠️ Input coordination initialization failed - continuing without it');
            }

            // Initialize audio context through audio processor
            const audioInitialized = await this.audioProcessor.initializeAudioContext();
            if (!audioInitialized) {
                this.logger.warn('⚠️ Audio context initialization failed - continuing with limited audio');
            }

            // Start health monitoring services
            this.audioProcessor.startConnectionHealthMonitoring(agentService);
            this.agentCoordinator.startReadinessMonitoring();

            this.initialized = true;
            this.logger.info('✅ TalkingAvatar initialized successfully with service architecture');

            return true;

        } catch (error) {
            this.logger.error('❌ Failed to initialize TalkingAvatar:', error);
            this.initialized = false;
            throw error;
        }
    }

    /**
     * Start listening - delegates to state coordinator
     */
    async startListening() {
        try {
            this.logger.info('🎙️ Starting listening through state coordinator...');

            const agentService = this.agentCoordinator.getAgentService();
            await this.stateCoordinator.startListening('default', agentService);

            this.logger.info('✅ Listening started successfully');
            return true;

        } catch (error) {
            this.logger.error('❌ Error starting listening:', error);
            return false;
        }
    }

    /**
     * Stop listening - delegates to state coordinator
     */
    async stopListening() {
        try {
            this.logger.info('🔇 Stopping listening through state coordinator...');

            await this.stateCoordinator.stopListening('default');

            this.logger.info('✅ Listening stopped successfully');
            return true;

        } catch (error) {
            this.logger.error('❌ Error stopping listening:', error);
            return false;
        }
    }

    /**
     * Start video streaming - delegates to media coordinator
     */
    async startVideoStreaming() {
        try {
            this.logger.info('🎬 Starting video streaming through media coordinator...');

            // Setup video frame capture with camera manager
            let cameraManager = null;
            if (this.talkingHeadControls && this.talkingHeadControls.cameraManager) {
                cameraManager = this.talkingHeadControls.cameraManager;
            } else if (this.viewer && this.viewer.cameraManager) {
                cameraManager = this.viewer.cameraManager;
            }

            if (cameraManager) {
                await this.mediaCoordinator.setupVideoFrameCapture(cameraManager);
            }

            // ✅ FIX: Explicitly pass userActivated=true since this is only called through user-activated video button
            const started = await this.mediaCoordinator.startVideoStreaming(true);

            if (started) {
                this.logger.info('✅ Video streaming started successfully');
            }

            return started;

        } catch (error) {
            this.logger.error('❌ Error starting video streaming:', error);
            return false;
        }
    }

    /**
     * Stop video streaming - delegates to media coordinator
     */
    async stopVideoStreaming() {
        try {
            this.logger.info('🛑 Stopping video streaming through media coordinator...');

            await this.mediaCoordinator.stopVideoStreaming();

            this.logger.info('✅ Video streaming stopped successfully');

        } catch (error) {
            this.logger.error('❌ Error stopping video streaming:', error);
        }
    }

    /**
     * Set cloned voice - delegates to config manager
     */
    async setClonedVoice(voiceData, isPermanent = false) {
        try {
            this.logger.info('🎤 Setting cloned voice through config manager...');

            const success = await this.configManager.setClonedVoice(voiceData, isPermanent);

            if (success) {
                this.logger.info('✅ Cloned voice set successfully');
            }

            return success;

        } catch (error) {
            this.logger.error('❌ Error setting cloned voice:', error);
            return false;
        }
    }

    /**
     * Handle barge-in - delegates to state coordinator
     */
    async handleBargeIn(input = null, options = {}) {
        try {
            this.logger.info('🛑 Handling barge-in through state coordinator...');

            // Get a simple state manager interface for compatibility
            const stateManagerInterface = {
                states: { LISTENING: 'listening' },
                setState: async (state, metadata) => {
                    // Simple state transition - could be enhanced
                    this.stateCoordinator.updateUIState({ currentState: state });
                    return true;
                }
            };

            await this.stateCoordinator.handleBargeIn(input, options, stateManagerInterface);

            this.logger.info('✅ Barge-in handled successfully');

        } catch (error) {
            this.logger.error('❌ Error handling barge-in:', error);
        }
    }

    /**
     * Process input - delegates to agent coordinator
     */
    async processInput(input, options = {}) {
        try {
            this.logger.info('🧠 Processing input through agent coordinator...');

            const agentService = this.agentCoordinator.getAgentService();
            if (!agentService) {
                throw new Error('Agent service not available');
            }

            // Ensure agent service is ready
            const isReady = await this.agentCoordinator.ensureAgentServiceReady();
            if (!isReady) {
                throw new Error('Agent service not ready for input processing');
            }

            // Process through agent service
            const result = await agentService.processInput(input, options);

            this.logger.info('✅ Input processed successfully');
            return result;

        } catch (error) {
            this.logger.error('❌ Error processing input:', error);
            throw error;
        }
    }

    /**
     * Handle audio data - delegates to audio processor
     */
    async handleAudioData(audioData, format) {
        try {
            const agentService = this.agentCoordinator.getAgentService();
            await this.audioProcessor.handleAudioData(audioData, format, agentService);

        } catch (error) {
            this.logger.error('❌ Error handling audio data:', error);
        }
    }

    /**
     * Enter talking head mode - UI-focused implementation
     */
    async enterTalkingHeadMode() {
        try {
            this.logger.info('🎭 Entering talking head mode...');

            if (this.inTalkingHeadMode) {
                this.logger.debug('Already in talking head mode');
                return true;
            }

            // Initialize talking head controls if not available
            if (!this.talkingHeadControls && this.viewer && this.viewer.ui) {
                this.talkingHeadControls = this.viewer.ui;
            }

            // Set talking head mode flag
            this.inTalkingHeadMode = true;

            // Initialize agent service if not already done
            if (!this.agentCoordinator.isAgentInitialized()) {
                await this.initialize();
            }

            this.logger.info('✅ Talking head mode activated successfully');
            return true;

        } catch (error) {
            this.logger.error('❌ Error entering talking head mode:', error);
            this.inTalkingHeadMode = false;
            return false;
        }
    }

    /**
     * Exit talking head mode - UI-focused implementation
     */
    async exitTalkingHeadMode() {
        try {
            this.logger.info('🚪 Exiting talking head mode...');

            if (!this.inTalkingHeadMode) {
                this.logger.debug('Not in talking head mode');
                return;
            }

            // Stop all active processes
            await this.stopListening();
            await this.stopVideoStreaming();

            // Reset talking head mode flag
            this.inTalkingHeadMode = false;

            this.logger.info('✅ Talking head mode deactivated successfully');

        } catch (error) {
            this.logger.error('❌ Error exiting talking head mode:', error);
        }
    }

    /**
     * Stop speaking - simplified implementation
     */
    stopSpeaking() {
        try {
            this.logger.info('🔇 Stopping speaking...');

            // Stop speech through talkingHead if available
            if (this.talkingHead && typeof this.talkingHead.stopSpeaking === 'function') {
                this.talkingHead.stopSpeaking();
            }

            // Update speaking state
            this.isSpeaking = false;

            // Update UI state through state coordinator
            this.stateCoordinator.updateUIState({ currentState: 'idle' });

            this.logger.info('✅ Speaking stopped successfully');

        } catch (error) {
            this.logger.error('❌ Error stopping speaking:', error);
        }
    }

    /**
     * Get current state - delegates to state coordinator
     */
    getCurrentState() {
        return this.stateCoordinator.getCurrentState();
    }

    /**
     * Check if listening - delegates to state coordinator
     */
    isListening() {
        return this.stateCoordinator.isListening();
    }

    /**
     * Check if processing - delegates to state coordinator
     */
    isProcessing() {
        return this.stateCoordinator.isProcessing();
    }

    /**
     * Get configuration - delegates to config manager
     */
    getConfiguration() {
        return this.configManager.getAllConfiguration();
    }

    /**
     * Get agent service status - delegates to agent coordinator
     */
    getAgentServiceStatus() {
        return this.agentCoordinator.getAgentServiceStatus();
    }

    /**
     * Get resource usage statistics - delegates to resource manager
     */
    getResourceUsageStats() {
        return this.resourceManager.getResourceUsageStats();
    }

    /**
     * Get media coordination status - delegates to media coordinator
     */
    getMediaStatus() {
        return {
            isVideoStreaming: this.mediaCoordinator.isVideoStreamingActive(),
            videoFrameState: this.mediaCoordinator.getVideoFrameCaptureState(),
            currentVideoFrames: this.mediaCoordinator.getCurrentVideoFrames().length,
            mediaConfig: this.mediaCoordinator.getMediaConfig()
        };
    }

    /**
     * Get audio processing status - delegates to audio processor
     */
    getAudioProcessingStatus() {
        return {
            processingStats: this.audioProcessor.getProcessingStats(),
            processingConfig: this.audioProcessor.getProcessingConfig(),
            isHealthy: this.audioProcessor.isProcessingHealthy()
        };
    }

    /**
     * Update configuration - delegates to appropriate managers
     */
    updateConfiguration(configType, updates) {
        try {
            switch (configType) {
                case 'voice':
                    this.configManager.updateVoiceConfig(updates);
                    break;
                case 'contextual':
                    this.configManager.updateContextualConfig(updates);
                    break;
                case 'agentSystem':
                    this.configManager.updateAgentSystemConfig(updates);
                    break;
                case 'media':
                    this.mediaCoordinator.updateMediaConfig(updates);
                    break;
                case 'audioProcessing':
                    this.audioProcessor.updateProcessingConfig(updates);
                    break;
                case 'audioCapture':
                    this.stateCoordinator.updateAudioCaptureConfig(updates);
                    break;
                default:
                    this.logger.warn('⚠️ Unknown configuration type:', configType);
                    return false;
            }

            this.logger.info('✅ Configuration updated successfully', { configType, updates });
            return true;

        } catch (error) {
            this.logger.error('❌ Error updating configuration:', error);
            return false;
        }
    }

    /**
     * Comprehensive dispose method - delegates to resource manager
     */
    async dispose() {
        try {
            this.logger.info('🧹 Disposing TalkingAvatar through resource manager...');

            // Register all resources with resource manager for proper cleanup
            this.resourceManager.registerResource('talkingHead', this.talkingHead);
            this.resourceManager.registerResource('animationController', this.animationController);
            this.resourceManager.registerResource('ui', this.ui);
            this.resourceManager.registerResource('avatarMesh', this.avatarMesh);
            this.resourceManager.registerResource('rpm', this.rpm);

            // Dispose through resource manager
            await this.resourceManager.dispose({
                cleanupAudio: true,
                cleanupVideo: true,
                cleanupAnimation: true,
                cleanupUI: true
            });

            // Dispose all services
            await this.disposeServices();

            // Reset core state
            this.initialized = false;
            this.inTalkingHeadMode = false;
            this.isSpeaking = false;

            // Clear 3D references
            this.avatarMesh = null;
            this.morphTargetDictionary = null;
            this.morphTargetInfluences = null;
            this.talkingHead = null;
            this.talkingHeadControls = null;
            this.rpm = null;
            this.ui = null;
            this.animationController = null;
            this.currentTalkingAnimation = null;

            this.logger.info('✅ TalkingAvatar disposed successfully');

        } catch (error) {
            this.logger.error('❌ Error disposing TalkingAvatar:', error);
        }
    }

    /**
     * Dispose all services
     */
    async disposeServices() {
        try {
            this.logger.info('🧹 Disposing all services...');

            // Dispose services in reverse dependency order
            if (this.stateCoordinator) {
                await this.stateCoordinator.dispose();
            }

            if (this.agentCoordinator) {
                await this.agentCoordinator.dispose();
            }

            if (this.audioProcessor) {
                await this.audioProcessor.dispose();
            }

            if (this.mediaCoordinator) {
                await this.mediaCoordinator.dispose();
            }

            if (this.configManager) {
                this.configManager.dispose();
            }

            // Resource manager is disposed last as it manages other resources
            // (Already handled in main dispose method)

            this.logger.info('✅ All services disposed successfully');

        } catch (error) {
            this.logger.error('❌ Error disposing services:', error);
        }
    }

    /**
     * Open Ready Player Me avatar creation interface
     * Delegates to resource manager for ReadyPlayerMe instance management
     */
    openReadyPlayerMe() {
        try {
            this.logger.info('🎭 Opening Ready Player Me through resource manager...');

            // Ensure ReadyPlayerMe is initialized through resource manager
            if (!this.rpm) {
                this.logger.info('🏗️ Initializing ReadyPlayerMe instance...');
                this.rpm = this.resourceManager.getOrCreateReadyPlayerMe(this);
            }

            if (this.rpm && typeof this.rpm.openAvatarCreator === 'function') {
                this.rpm.openAvatarCreator();
                this.logger.info('✅ Ready Player Me interface opened successfully');
            } else {
                this.logger.warn('⚠️ ReadyPlayerMe instance not available or openAvatarCreator method missing');
                // Fallback: Create a basic modal interface
                this._createBasicReadyPlayerMeInterface();
            }

        } catch (error) {
            this.logger.error('❌ Error opening Ready Player Me:', error);
            // Fallback to basic interface
            this._createBasicReadyPlayerMeInterface();
        }
    }

    /**
     * Transform avatar to talking head mode
     * Delegates to TalkingHeadTransformationService for consolidated implementation
     * @param {Object} inputMesh - The 3D mesh to transform (optional)
     * @param {Object} viewer - Viewer instance for context (optional) 
     * @returns {Promise<boolean>} Success status
     */
    async transformToTalkingHead(inputMesh = null, viewer = null) {
        try {
            this.logger.info('🎭 Transforming to talking head mode through TalkingHeadTransformationService...');

            // Import the dedicated service
            const { TalkingHeadTransformationService } = await import('./services/talkingHeadTransformationService.js');

            // Create service instance if not available
            if (!this.transformationService) {
                this.transformationService = new TalkingHeadTransformationService({
                    logger: this.logger
                });
            }

            // Determine mesh to use - provided mesh or stored avatar mesh
            const meshToUse = inputMesh || this.avatarMesh;
            const viewerContext = viewer || this.viewer;

            // Delegate to the dedicated transformation service
            const success = await this.transformationService.transformToTalkingHead(meshToUse, viewerContext, null);

            if (success) {
                // Update local state to reflect transformation
                this.inTalkingHeadMode = true;
                this.originalMeshId = meshToUse?.uuid;
                
                // Get references from transformation service
                this.talkingHead = this.transformationService.getTalkingHead();
                this.ui = this.transformationService.ui;
                
                this.logger.info('✅ Successfully delegated transformation to TalkingHeadTransformationService');
            }

            return success;

        } catch (error) {
            this.logger.error('❌ Failed to transform via TalkingHeadTransformationService:', error);
            this.inTalkingHeadMode = false;
            return false;
        }
    }

    /**
     * Create basic Ready Player Me interface as fallback
     * @private
     */
    _createBasicReadyPlayerMeInterface() {
        try {
            this.logger.info('🔧 Creating basic Ready Player Me interface fallback...');

            // Create simple modal with iframe to Ready Player Me
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;

            const iframe = document.createElement('iframe');
            iframe.style.cssText = `
                width: 90%;
                height: 90%;
                max-width: 800px;
                max-height: 600px;
                border: none;
                border-radius: 8px;
                background: white;
            `;
            iframe.src = 'https://demo.readyplayer.me/avatar';

            // Close button
            const closeButton = document.createElement('button');
            closeButton.textContent = '×';
            closeButton.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                background: #ff4444;
                color: white;
                border: none;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                font-size: 24px;
                cursor: pointer;
                z-index: 10001;
            `;

            closeButton.onclick = () => {
                document.body.removeChild(modal);
                this.logger.info('✅ Ready Player Me interface closed');
            };

            modal.appendChild(iframe);
            modal.appendChild(closeButton);
            document.body.appendChild(modal);

            this.logger.info('✅ Basic Ready Player Me interface created');

        } catch (error) {
            this.logger.error('❌ Error creating basic Ready Player Me interface:', error);
        }
    }

    /**
     * Health check - validates all services
     */
    async performHealthCheck() {
        try {
            const healthStatus = {
                overall: 'healthy',
                services: {},
                timestamp: Date.now()
            };

            // Check each service health
            healthStatus.services.configManager = !!this.configManager;
            healthStatus.services.resourceManager = !!this.resourceManager;
            healthStatus.services.mediaCoordinator = !!this.mediaCoordinator;
            healthStatus.services.audioProcessor = this.audioProcessor?.isProcessingHealthy() || false;
            healthStatus.services.agentCoordinator = this.agentCoordinator?.isAgentInitialized() || false;
            healthStatus.services.stateCoordinator = !!this.stateCoordinator;

            // Check overall health
            const healthyServices = Object.values(healthStatus.services).filter(Boolean).length;
            const totalServices = Object.keys(healthStatus.services).length;

            if (healthyServices < totalServices * 0.8) {
                healthStatus.overall = 'unhealthy';
            } else if (healthyServices < totalServices) {
                healthStatus.overall = 'degraded';
            }

            this.logger.debug('🩺 Health check completed', {
                overall: healthStatus.overall,
                healthyServices,
                totalServices
            });

            return healthStatus;

        } catch (error) {
            this.logger.error('❌ Error performing health check:', error);
            return {
                overall: 'error',
                services: {},
                timestamp: Date.now(),
                error: error.message
            };
        }
    }
}

export default TalkingAvatar;