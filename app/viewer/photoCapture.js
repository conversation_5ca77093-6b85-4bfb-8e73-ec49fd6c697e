/**
 * PhotoCapture.js
 *
 * General component for capturing photos with various processing backends
 * Refactored to use the new media capture system (CameraManager and MediaCaptureManager)
 */

import { CameraManager } from '../../src/media/core/CameraManager.js';
import { ASSETS } from './viewerConfig.js';
import { generateCacheKey, storeSeed } from '../../src/utils/cache.js';

export class PhotoCapture {
    constructor(viewer, options = {}) {
        this.viewer = viewer;

        // Options with defaults - embedded camera by default
        this.options = {
            gender: options.gender || 'boy',
            photoQuality: options.photoQuality || 0.9,
            photoFormat: options.photoFormat || 'image/jpeg',
            photoWidth: options.photoWidth || 640,
            photoHeight: options.photoHeight || 480,
            savePath: options.savePath || 'assets/images',
            cameraMode: options.cameraMode || 'embedded', // 'embedded' or 'popup'
            apiEndpoint: options.apiEndpoint || 'tripo-doll', // 'tripo-doll', 'custom', etc.
            ...options
        };

        // Initialize CameraManager for photo capture
        this.cameraManager = new CameraManager(document.body, {
            defaultMode: this.options.cameraMode === 'embedded' ? 'corner' : 'popup',
            cornerPosition: 'bottom-right',
            cornerSize: {
                width: this.options.photoWidth,
                height: this.options.photoHeight
            },
            popupSize: {
                width: this.options.photoWidth * 1.2,
                height: this.options.photoHeight * 1.2
            },
            streamQuality: {
                video: {
                    width: { ideal: this.options.photoWidth },
                    height: { ideal: this.options.photoHeight },
                    frameRate: { ideal: 30 },
                    facingMode: 'user'
                },
                audio: false
            },
            onError: (error) => {
                console.error('[PhotoCapture] CameraManager error:', error);
                this.updateStatus(`Camera error: ${error.message}`, true);
            }
        });

        // UI elements (for custom controls)
        this.genderSelector = null;
        this.captureButton = null;
        this.controlsContainer = null;

        // Status tracking
        this.isProcessing = false;
        this.isCameraActive = false;
        this.lastCapturedPhoto = null;
        this.lastProcessedResult = null;
        this.selectedGender = this.options.gender;
    }

    /**
     * Initialize the component
     * @returns {Promise<boolean>} Success status
     */
    async initialize() {
        try {
            console.log('[PhotoCapture] Initializing...');

            // Initialize CameraManager
            const cameraInitialized = await this.cameraManager.initialize();
            if (!cameraInitialized) {
                console.warn('[PhotoCapture] Failed to initialize CameraManager - continuing without photo capture functionality');
                // Don't throw an error - the main system can still work without photo capture
                // The Aliyun realtime system doesn't depend on photo capture
            } else {
                console.log('[PhotoCapture] Initialized successfully with CameraManager');
            }
            return true;
        } catch (error) {
            console.error('[PhotoCapture] Initialization error:', error);
            return false;
        }
    }

    /**
     * Open the camera capture interface
     */
    async openCamera() {
        if (!this.cameraManager) {
            console.error('[PhotoCapture] CameraManager not initialized');
            return;
        }

        try {
            console.log('[PhotoCapture] Opening camera interface...');

            // Start camera with the configured mode
            await this.cameraManager.startCamera();

            // Show camera in the appropriate mode
            const mode = this.options.cameraMode === 'embedded' ? 'corner' : 'popup';
            await this.cameraManager.showCamera(mode);

            // Create additional UI controls
            this.createCameraControls();
            this.isCameraActive = true;

            console.log('[PhotoCapture] Camera opened successfully');
        } catch (error) {
            console.error('[PhotoCapture] Error opening camera:', error);
            alert('Failed to open camera. Please check camera permissions.');
        }
    }

    /**
     * Create additional camera controls for photo capture
     * CameraManager handles the main camera UI, we just add our custom controls
     */
    createCameraControls() {
        // Wait for CameraManager to create its UI, then add our controls
        setTimeout(() => {
            this._addControlsToCamera();
        }, 100);
    }

    /**
     * Add photo capture controls to the existing camera UI
     */
    _addControlsToCamera() {
        // Find the camera container created by CameraManager
        const cameraElement = document.querySelector('.camera-corner-view, .camera-popup-view');
        if (!cameraElement) {
            console.warn('[PhotoCapture] No camera element found from CameraManager');
            return;
        }

        // Create photo capture controls container
        const controlsContainer = document.createElement('div');
        controlsContainer.className = 'photo-capture-controls';
        controlsContainer.style.cssText = `
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 8px;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            gap: 6px;
            z-index: 1001;
        `;

        // Gender selector
        const genderSelector = document.createElement('select');
        genderSelector.className = 'gender-selector';
        genderSelector.style.cssText = `
            padding: 4px 8px;
            border: 1px solid #667eea;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 12px;
        `;

        // Add gender options
        const genderOptions = [
            { value: 'boy', text: 'Boy' },
            { value: 'girl', text: 'Girl' }
        ];

        genderOptions.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            optionElement.selected = option.value === this.selectedGender;
            genderSelector.appendChild(optionElement);
        });

        genderSelector.addEventListener('change', (e) => {
            this.selectedGender = e.target.value;
            console.log(`[PhotoCapture] Gender changed to: ${this.selectedGender}`);
        });

        // Capture button
        const captureButton = document.createElement('button');
        captureButton.className = 'capture-button';
        captureButton.textContent = 'Capture Photo';
        captureButton.style.cssText = `
            padding: 8px 16px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        `;

        captureButton.addEventListener('click', () => this.capturePhoto());

        // Add hover effects
        captureButton.addEventListener('mouseenter', () => {
            captureButton.style.transform = 'translateY(-2px)';
            captureButton.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.4)';
        });

        captureButton.addEventListener('mouseleave', () => {
            captureButton.style.transform = 'translateY(0)';
            captureButton.style.boxShadow = 'none';
        });

        // Add controls to container
        controlsContainer.appendChild(genderSelector);
        controlsContainer.appendChild(captureButton);

        // Add to camera element
        cameraElement.appendChild(controlsContainer);

        // Store references for cleanup
        this.genderSelector = genderSelector;
        this.captureButton = captureButton;
        this.controlsContainer = controlsContainer;

        console.log('[PhotoCapture] Custom controls added to camera view');
    }

    /**
     * Add photo capture controls to the camera view
     */
    /**
     * Capture photo using CameraManager
     */
    async capturePhoto() {
        if (!this.cameraManager) {
            console.error('[PhotoCapture] CameraManager not available');
            return;
        }

        if (this.isProcessing) {
            this.updateStatus('Already processing a photo, please wait...', true);
            return;
        }

        try {
            console.log('[PhotoCapture] Capturing photo...');

            // Disable capture button temporarily
            if (this.captureButton) {
                this.captureButton.disabled = true;
                this.captureButton.textContent = 'Capturing...';
                this.captureButton.style.opacity = '0.6';
            }

            // Capture photo using CameraManager
            const photoDataUrl = await this.cameraManager.capturePhoto({
                quality: this.options.photoQuality,
                format: this.options.photoFormat
            });

            if (photoDataUrl) {
                // Handle the captured photo
                await this.handlePhotoCaptured(photoDataUrl);
            } else {
                throw new Error('Failed to capture photo');
            }

        } catch (error) {
            console.error('[PhotoCapture] Photo capture error:', error);
            this.updateStatus('Failed to capture photo: ' + error.message, true);
        } finally {
            // Re-enable capture button
            if (this.captureButton) {
                this.captureButton.disabled = false;
                this.captureButton.textContent = 'Capture Photo';
                this.captureButton.style.opacity = '1';
            }
        }
    }

    /**
     * Clean up custom controls when camera is closed
     */
    cleanupControls() {
        if (this.controlsContainer && this.controlsContainer.parentNode) {
            this.controlsContainer.parentNode.removeChild(this.controlsContainer);
        }
        this.genderSelector = null;
        this.captureButton = null;
        this.controlsContainer = null;
        console.log('[PhotoCapture] Custom controls cleaned up');
    }

    /**
     * Update status message - uses console logging for embedded mode
     * @param {string} message - Status message
     * @param {boolean} isError - Whether this is an error message
     */
    updateStatus(message, isError = false) {
        // Log status updates for embedded mode
        if (isError) {
            console.error('[PhotoCapture] Status Error:', message);
        } else {
            console.log('[PhotoCapture] Status:', message);
        }

        // Future: Could integrate with CameraManager or custom UI
        // for better user feedback if needed
    }

    /**
     * Handle captured photo
     * @param {string} photoUrl - Data URL of the captured photo
     */
    async handlePhotoCaptured(photoUrl) {
        if (this.isProcessing) {
            this.updateStatus('Already processing a photo, please wait...', true);
            return;
        }

        try {
            this.isProcessing = true;
            this.lastCapturedPhoto = photoUrl;

            // Update status
            this.updateStatus('Processing photo...');

            // Convert data URL to blob
            const response = await fetch(photoUrl);
            const blob = await response.blob();

            // Get gender from embedded selector or fall back to options
            const gender = this.genderSelector ? this.genderSelector.value : this.selectedGender;

            // Process the photo with the configured API endpoint
            const result = await this.processPhoto(blob, gender);

            // Store processing result
            this.lastProcessedResult = result;

            if (result.success) {
                this.updateStatus('Photo processed successfully!');
                this.lastProcessedResult = result.data;

                // Save metadata for the mesh
                await this.saveMeshMetadata(result.data, gender);

                // Display the mesh using standard mesh loading
                try {
                    // Extract mesh path from the result
                    const meshPath = result.data.meshResult;
                    if (!meshPath) {
                        throw new Error('No mesh path in result data');
                    }

                    // Load the mesh using the standard loadAssets method
                    await this.viewer.loadAssets({
                        meshes: {
                            avatar: meshPath
                        }
                    }, {
                        reloadMeshes: true,
                        reloadEnvironment: false
                    });

                    // Get the loaded mesh object
                    const meshObject = this.viewer.objects.get('avatar');

                    if (!meshObject) {
                        throw new Error('Failed to load tripo-doll mesh');
                    }

                    // Add user data to the mesh
                    const meshFileName = meshPath.split('/').pop();
                    meshObject.userData = {
                        ...meshObject.userData,
                        type: 'tripo-doll',
                        gender: gender,
                        sourceUrl: meshPath,
                        sourceFilename: meshFileName,
                        baseFilename: meshFileName.replace(/\.[^/.]+$/, ''),
                        riggingFile: result.data.riggingResult,
                        imageResult: result.data.imageResult
                    };

                    console.log('[photoCapture] Tripo-doll mesh loaded successfully:', meshObject);

                    this.updateStatus('Mesh loaded successfully!');
                } catch (error) {
                    console.error('[photoCapture] Error loading tripo-doll mesh:', error);
                    this.updateStatus(`Error loading mesh: ${error.message || 'Unknown error'}`, true);
                }
            } else {
                this.updateStatus(`Error: ${result.error || 'Failed to process photo'}`, true);
            }
        } catch (error) {
            console.error('[photoCapture] Error processing photo:', error);
            this.updateStatus(`Error: ${error.message || 'Failed to process photo'}`, true);
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * Save metadata for the mesh
     * @param {Object} meshData - Mesh data from the API
     * @param {string} gender - Gender of the avatar
     */
    async saveMeshMetadata(meshData, gender) {
        try {
            if (!meshData || !meshData.meshResult) {
                console.warn('[photoCapture] No mesh data to save metadata for');
                return;
            }

            // Extract the mesh filename
            const meshPath = meshData.meshResult;
            const meshFileName = meshPath.split('/').pop();
            const baseFilename = meshFileName.replace(/\.[^/.]+$/, '');

            console.log(`[photoCapture] Saving metadata for mesh: ${baseFilename}`);

            // Generate a cache key for the metadata file
            const cacheKey = await generateCacheKey('tripo-doll', baseFilename, null);

            // Create metadata with initial values
            const metadata = {
                roleName: gender === 'girl' ? "陈鲁豫" : "周杰伦", // Use 陈鲁豫 for girls, 周杰伦 for boys
                clonedAudio: null,
                favorite: false,
                timestamp: Date.now(),
                meshName: baseFilename,
                gender: gender,
                type: 'tripo-doll',
                imageResult: meshData.imageResult,
                riggingFile: meshData.riggingResult
            };

            // Store the metadata
            const result = await storeSeed(cacheKey, metadata);

            if (result && result.success) {
                console.log(`[photoCapture] Saved metadata for mesh: ${baseFilename}`, metadata);
                return true;
            } else {
                console.error(`[photoCapture] Failed to save metadata for mesh: ${baseFilename}`);
                return false;
            }
        } catch (error) {
            console.error('[photoCapture] Error saving mesh metadata:', error);
            return false;
        }
    }

    /**
     * Process photo with tripo-doll API
     * @param {Blob} photoBlob - Photo blob
     * @param {string} gender - Gender ('boy' or 'girl')
     * @returns {Promise<Object>} API response
     */
    async processWithTripoDoll(photoBlob, gender) {
        try {
            console.log('[photoCapture] Processing photo with tripo-doll API:', { gender });

            // Create request for anyTo3D service
            const request = {
                source: 'tripo-doll',
                input: photoBlob,
                gender: gender,
                saveOptions: {
                    basePath: 'public',
                    paths: {
                        meshes: ASSETS.SAVE_OPTIONS.meshPath,
                        images: ASSETS.SAVE_OPTIONS.imagePath,
                        videos: ASSETS.SAVE_OPTIONS.videoPath
                    }
                }
            };

            // 3D generation service moved to agent system
            console.log('[photoCapture] 3D generation from photos is now handled through the agent system');
            this.updateStatus('Photo-to-3D generation moved to agent system', true);

            // Return error to indicate service migration
            const result = {
                success: false,
                error: 'Photo-to-3D generation has been migrated to the agent system. Use TalkingAvatar with agent mode for 3D content generation from photos.',
                data: null
            };

            console.log('[photoCapture] Tripo-doll API result:', result);
            return result;
        } catch (error) {
            console.error('[photoCapture] Error calling tripo-doll API:', error);
            throw error;
        }
    }

    /**
     * Process photo with the configured API endpoint
     * @param {Blob} photoBlob - Photo blob
     * @param {string} gender - Gender ('boy' or 'girl')
     * @returns {Promise<Object>} API response
     */
    async processPhoto(photoBlob, gender) {
        const endpoint = this.options.apiEndpoint;
        console.log(`[PhotoCapture] Processing photo with ${endpoint} API:`, { gender });

        switch (endpoint) {
            case 'tripo-doll':
                return await this.processWithTripoDoll(photoBlob, gender);
            case 'custom':
                return await this.processWithCustomAPI(photoBlob, gender);
            default:
                console.warn(`[PhotoCapture] Unknown API endpoint: ${endpoint}, falling back to tripo-doll`);
                return await this.processWithTripoDoll(photoBlob, gender);
        }
    }

    /**
     * Process photo with custom API (placeholder for future extensions)
     * @param {Blob} photoBlob - Photo blob
     * @param {string} gender - Gender
     * @returns {Promise<Object>} API response
     */
    async processWithCustomAPI(photoBlob, gender) {
        // Placeholder for future custom API implementations
        throw new Error('Custom API not implemented yet');
    }

    /**
     * Close the camera interface and cleanup resources
     */
    async closeCamera() {
        try {
            console.log('[PhotoCapture] Closing camera...');

            // Hide and stop camera if active
            if (this.cameraManager && this.isCameraActive) {
                await this.cameraManager.hideCamera();
                this.cameraManager.stopCamera();
            }

            // Cleanup custom controls
            this.cleanupControls();
            this.isCameraActive = false;

            console.log('[PhotoCapture] Camera closed successfully');
        } catch (error) {
            console.error('[PhotoCapture] Error closing camera:', error);
        }
    }

    /**
     * Cleanup all resources when component is destroyed
     */
    async dispose() {
        console.log('[PhotoCapture] Disposing...');

        // Close camera if active
        if (this.isCameraActive) {
            await this.closeCamera();
        }

        // Dispose CameraManager
        if (this.cameraManager) {
            this.cameraManager.dispose();
            this.cameraManager = null;
        }

        // Clear references
        this.viewer = null;
        this.lastCapturedPhoto = null;
        this.lastProcessedResult = null;

        console.log('[PhotoCapture] Disposed successfully');
    }
}
