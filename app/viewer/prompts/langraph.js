/**
 * Viewer-Specific LangGraph Prompting System
 * Specialized prompts for Hologram Avatar viewer application
 * Contains Qwen-Omni specific prompts and viewer tool integration
 */

import {
    buildSystemPrompt,
    createASRContext,
    getMediaInstruction,
    BASE_PROMPT_COMPONENTS
} from '@/agent/prompts/base.js';
import { createLogger, LogLevel } from '@/utils/logger';
import { 
    validateLangGraphOptions, 
    getLangGraphPromptLogger 
} from '@/agent/prompts/base.js';

// Initialize viewer-specific logger
const logger = createLogger('ViewerLangGraphPrompts');
const promptLogger = getLangGraphPromptLogger();

/**
 * Dynamic tool information provider for viewer-specific tools
 * Automatically imports and formats available tools information
 * @param {Object} options - Configuration options
 * @returns {Promise<Object>} Tools information
 */
export async function getAvailableToolsInfo(options = {}) {
    const { includeDescriptions = true, includeExamples = true } = options;

    try {
        // Dynamically import tool manager to get current tools
        const { toolManager } = await import('@/agent/tools/index.js');

        const availableTools = toolManager.getAllTools();
        const toolsByCategory = {};

        // Group tools by category
        for (const tool of availableTools) {
            const category = toolManager.toolCategories.get(tool.name) || 'general';
            if (!toolsByCategory[category]) {
                toolsByCategory[category] = [];
            }
            toolsByCategory[category].push({
                name: tool.name,
                description: tool.description || 'No description available'
            });
        }

        // Format tool information for prompt
        let toolsInfo = '';

        for (const [category, tools] of Object.entries(toolsByCategory)) {
            const categoryName = category.charAt(0).toUpperCase() + category.slice(1);
            toolsInfo += `\n### ${categoryName} Tools:\n`;

            for (const tool of tools) {
                toolsInfo += `- **${tool.name}**: ${includeDescriptions ? tool.description : 'Available'}\n`;
            }
        }

        // Add usage examples for key tools
        if (includeExamples) {
            toolsInfo += `\n### Key Tool Usage Examples:\n`;
            toolsInfo += `- speaking_control: Use for all conversational responses\n`;
            toolsInfo += `- Animation tools: Use when user requests specific actions or emotions\n`;
        }

        return {
            toolsInfo,
            toolCount: availableTools.length,
            categories: Object.keys(toolsByCategory)
        };

    } catch (error) {
        logger.warn('Failed to load dynamic tools info:', error);
        // Fallback to static tool information
        return {
            toolsInfo: `\n### Available Tools:\n- speaking_control: Convert text to speech\n- Animation tools: Trigger avatar animations\n`,
            toolCount: 2,
            categories: ['tts', 'animation']
        };
    }
}

/**
 * Create optimized LangGraph system prompt for Hologram Avatar viewer
 * Uses dynamic tool integration and Qwen-Omni specific prompts
 * @param {Object} options - Configuration options
 * @returns {Promise<string>} System prompt
 */
export async function createLangGraphSystemPrompt(options = {}) {
    const {
        language = 'english',
        gender = 'neutral',
        mood = 'neutral',
        sessionId = 'default',
        asrMetadata = null,
        mediaType = 'text',
        userInput = '',
        includeHistory = true,
        includeToolInfo = true,
        // Internal flag to disable separate logging when called from unified logging
        _suppressLogging = false
    } = options;

    // Only log if not being called from unified logging
    if (!_suppressLogging) {
        logger.info('Creating optimized LangGraph prompt for Hologram Avatar viewer:', {
            language, gender, mood, hasASRMetadata: !!asrMetadata, mediaType, includeToolInfo
        });
    }

    // Start with official Qwen-Omni system prompt for audio output compatibility
    let systemPrompt = `You are Qwen, a virtual human developed by the Qwen Team, Alibaba Group, capable of perceiving auditory and visual inputs, as well as generating text and speech.`;

    // Add language context if ASR detected different language
    if (asrMetadata?.detectedLanguage && asrMetadata.detectedLanguage !== 'en') {
        const asrContext = createASRContext(asrMetadata);
        systemPrompt += `\n\nLanguage Context: User is speaking in ${asrMetadata.detectedLanguage}. Respond in the same language for natural conversation flow.`;

        if (asrContext.languageRule) {
            systemPrompt += `\n${asrContext.languageRule}`;
        }
    }

    // Add personality context only if different from default
    if (gender !== 'neutral' || mood !== 'neutral') {
        systemPrompt += `\n\nPersonality: `;
        if (gender !== 'neutral') {
            systemPrompt += `${gender} voice, `;
        }
        if (mood !== 'neutral') {
            systemPrompt += `${mood} mood.`;
        }
    }

    // Dynamically add tool information using hyperparameters
    if (includeToolInfo) {
        const toolsInfo = await getAvailableToolsInfo({
            includeDescriptions: true,
            includeExamples: false
        });

        systemPrompt += `\n\n## Available Tools (${toolsInfo.toolCount} tools):${toolsInfo.toolsInfo}`;

        // CRITICAL FIX: Add explicit autonomous tool usage instructions for viewer
        systemPrompt += `\n\n## Tool Usage Rules:`;
        systemPrompt += `\n- **ALWAYS use 'speaking_control' tool for all conversational responses**`;
        systemPrompt += `\n- Use action='speak' to make the avatar speak your response text`;
        systemPrompt += `\n- Choose appropriate voice based on conversation context`;
        systemPrompt += `\n- Use animation tools when expressing emotions or specific actions`;
        systemPrompt += `\n- You must decide autonomously when to speak vs think silently`;
        systemPrompt += `\n- For normal conversations, always use speaking tools to respond audibly`;
        
        // Add autonomous decision-making instructions
        systemPrompt += `\n\n## Autonomous Decision-Making:`;
        systemPrompt += `\n- Use 'analyze_conversation_context' to understand user engagement and conversation flow`;
        systemPrompt += `\n- Use 'decide_communication_mode' to choose between speaking, thinking silently, or researching`;
        systemPrompt += `\n- Use 'plan_proactive_engagement' when conversation is inactive to initiate new topics`;
        systemPrompt += `\n- Use 'web_search' or 'knowledge_research' for autonomous learning and fact-checking`;
        systemPrompt += `\n- Use 'track_conversation_topics' to maintain conversation memory and context`;
        systemPrompt += `\n- Use 'detect_conversation_opportunities' to identify moments for proactive engagement`;
        systemPrompt += `\n- **Default behavior: Analyze context first, then decide communication mode, then execute accordingly**`;

        // Tools info will be logged by the SystemPromptManager in utils.js
        // No need to log here - keep langraph.js focused on prompt creation
    }

    // Add media processing instructions only if needed
    if (mediaType && mediaType !== 'text') {
        const mediaInstruction = getMediaInstruction(mediaType);
        if (mediaInstruction) {
            systemPrompt += `\n\n## Media Processing:\n${mediaInstruction}`;
        }
    }

    // Enhanced response guidelines for autonomous tool usage in viewer
    systemPrompt += `\n\n## Response Guidelines:\n`;
    systemPrompt += `- Be conversational and engaging\n`;
    systemPrompt += `- **Default behavior: Analyze context → Decide mode → Execute (usually speaking)**\n`;
    systemPrompt += `- Use autonomous decision-making tools to determine optimal response strategy\n`;
    systemPrompt += `- Only respond silently for complex reasoning tasks or when explicitly requested\n`;
    systemPrompt += `- Maintain language consistency with user input\n`;
    systemPrompt += `- Combine speaking with appropriate animations for rich interaction\n`;
    systemPrompt += `- Proactively engage when conversation becomes inactive\n`;
    systemPrompt += `- Use research tools to stay informed and provide accurate information\n`;

    // Only log separately if not being called from unified logging
    if (!_suppressLogging) {
        // Log optimized prompt creation
        const request = {
            operation: 'Viewer LangGraph Optimized Prompt',
            language,
            toolsIncluded: includeToolInfo,
            promptLength: systemPrompt.length,
            mediaType,
            hasASRMetadata: !!asrMetadata
        };

        promptLogger.logSystemPrompt('createViewerLangGraphSystemPrompt', request, systemPrompt, {
            optimized: true,
            toolsAutoLoaded: includeToolInfo,
            duplicationsRemoved: true,
            webOptimized: true,
            viewerSpecific: true
        });

        logger.info('Generated optimized viewer prompt:', {
            length: systemPrompt.length,
            toolsIncluded: includeToolInfo,
            categories: includeToolInfo ? (await getAvailableToolsInfo()).categories : []
        });
    }

    return systemPrompt;
}

/**
 * Validate LangGraph options with viewer-specific defaults
 * @param {Object} options - Raw options to validate
 * @returns {Object} Validated options with viewer-specific defaults
 */
export function validateViewerLangGraphOptions(options = {}) {
    // Apply viewer-specific defaults before validation
    const viewerDefaults = {
        enableAnimations: true,
        enableTTS: true,
        includeToolInfo: true,
        ...options
    };

    return validateLangGraphOptions(viewerDefaults);
}