import { <PERSON><PERSON>Filter3D, KalmanFilterRotation } from '../../src/utils/KalmanFilter.js';
import { GestureUtils } from '../../src/recognition/gestures/GestureUtils.js';
import { GestureR<PERSON>ognizer } from '../../src/recognition/gestures/GestureRecognizer.js';
import { GestureOperations } from '../../src/recognition/gestures/GestureOperations.js';
import { CameraManager } from '../../src/media/core/CameraManager.js';

/**
 * GestureController - Manages gesture recognition and application in the 3D viewer
 *
 * Handles tracking gesture states, applying transformations to 3D objects,
 * and providing a unified API for the viewer to interact with gestures.
 */
export class GestureController {
    constructor(viewer) {
        this.viewer = viewer;
        this.controls = viewer?.controls;
        this.lookingGlassConfig = viewer.glassConfig;
        // Add controls type detection for debugging
        if (this.controls) {
            this.controlsType = this.controls.constructor.name;
            console.log(`[GestureController] Initialized with controls type: ${this.controlsType}`);

            // Log available methods for debugging
            console.log('[GestureController] Controls methods:',
                Object.getOwnPropertyNames(Object.getPrototypeOf(this.controls))
                    .filter(method => typeof this.controls[method] === 'function'));
        } else {
            console.warn('[GestureController] No controls object available');
        }

        // Gesture control state
        this.state = {
            enabled: false,
            active: false,
            debugMode: false,
            lastGestureTime: 0,
            gestureTimeout: 100, // ms to consider gesture inactive
            rotationSpeed: 2.0,
            scalingSpeed: 1.0,
            minZoom: 0.9,  // Minimum zoom level
            maxZoom: 2.0  // Maximum zoom level
        };

        // Tracking for multi-frame gestures
        this.activeGestures = {
            scaling: false,
            rotation: false,
            punch: false
        };

        // Add scaling lock state tracking - simplified to use hand presence
        this.scalingState = {
            locked: false,
            lastScale: 1.0,
            lastHandPresenceTime: 0,  // When hand was last present
            handPresenceTimeout: 500, // ms to wait after hand disappears before locking
            // Pinch-specific properties for relative scaling
            pinchActive: false,       // Whether pinch gesture is currently active
            referenceDistance: 0,     // Reference distance for relative scaling
            sensitivity: 1,           // Sensitivity multiplier for scaling
            lastPinchDetectionTime: 0 // Time of last pinch detection
        };

        // Add rotation state tracking - simplified to use hand presence
        this.rotationState = {
            locked: false,
            lastRotation: { x: 0, y: 0 },
            lastHandPresenceTime: 0,    // When hand was last present
            rotationActive: false,      // Whether rotation gesture is currently active
            sensitivity: 1.2,           // Sensitivity multiplier for rotation
            lastRotationDetectionTime: 0 // Time of last rotation detection
        };

        // References to external components
        this.poseDetector = null;
        this.cameraViewer = null; // Legacy support
        this.debugVisualizer = null;
        this.gestureDetectionFrameId = null;

        // Initialize centralized camera manager
        this.cameraManager = new CameraManager(document.body, {
            defaultMode: 'corner', // Use embedded corner view for better UX
            cornerPosition: 'bottom-right',
            cornerSize: { width: 320, height: 240 },
            popupSize: { width: 640, height: 480 },
            animationDuration: 300
        });

        // Filter configurations
        this.filterConfig = {
            enabled: true,
            landmarkSmoothing: 0.01,  // Process covariance for landmarks
            gestureSmoothing: 0.05,   // Measurement covariance for landmarks
            scaleSmoothing: 0.1,      // Scale smoothing factor
            rotationSmoothing: 0.05,  // Rotation smoothing factor
        };

        // Filter instances
        this.filters = {
            landmarks: new Map(),       // Landmark position filters (one per landmark point)
            scale: null,                // Scale filter
            rotation: null,             // Rotation filter
            gestureState: new Map(),    // Gesture state filters (for stable gesture detection)
            lastPinchDistance: 0.1      // Store last pinch distance for relative scaling
        };

        // Initialize filters
        this.initializeFilters();
        // Set up notification system
        this.setupNotifications();

        // Add gesture detection state
        this.gestureDetectionState = {
            lastDetectedGestures: [],
            detectionThreshold: 0.8, // Confidence threshold for gesture detection
            gestureHistory: []
        };

        // Set up global zoom constraints
        GestureUtils.setZoomConstraints(this.state.minZoom, this.state.maxZoom);
    }

    /**
     * Initialize the gesture controller with necessary components
     */
    async initialize(poseDetector, cameraViewer, debugVisualizer) {
        this.poseDetector = poseDetector;
        this.cameraViewer = cameraViewer; // Keep for legacy compatibility
        this.debugVisualizer = debugVisualizer;

        if (!this.poseDetector) {
            console.warn('[GestureController] Missing pose detector');
            return false;
        }

        // Initialize centralized camera manager
        const cameraInitialized = await this.cameraManager.initialize();
        if (!cameraInitialized) {
            console.warn('[GestureController] Failed to initialize camera manager - continuing without gesture controls');
            // Don't return false - the system can still work without gesture controls
            // The main Aliyun realtime functionality doesn't depend on gestures
        } else {
            console.log('[GestureController] Camera manager initialized successfully');
            // Register gesture callbacks with camera manager
            this.cameraManager.registerGestureCallbacks({
                onStreamStart: () => console.log('[GestureController] Camera stream started'),
                onStreamStop: () => console.log('[GestureController] Camera stream stopped'),
                onFrame: (frameInfo) => this._handleVideoFrame(frameInfo)
            });
        }

        // Set initial state
        this.state.enabled = true;

        console.log('[GestureController] Initialized successfully with centralized camera');
        return true;
    }

    /**
     * Toggle gesture controls on/off
     */
    toggle() {
        if (!this.state.enabled) {
            console.log('[GestureController] Gesture controls not available');
            return;
        }

        this.state.active = !this.state.active;
        console.log(`[GestureController] Gesture controls ${this.state.active ? 'activated' : 'deactivated'}`);

        // Reset filters when activating
        if (this.state.active) {
            this.resetFilters();
        }

        // Show notification
        this.showNotification(`Gesture controls ${this.state.active ? 'activated' : 'deactivated'}`);

        // Toggle gesture detection
        if (this.state.active) {
            this.startDetection();
        } else {
            this.stopDetection();
        }

        // Update UI
        document.querySelector('.gesture-control-toggle')?.classList.toggle('active', this.state.active);

        return this.state.active;
    }
    /**
     * Initialize Kalman filters for various gesture data
     */
    initializeFilters() {
        // Create scale filter (1D)
        this.filters.scale = new KalmanFilter3D(
            1.0, 0, 0,
            this.filterConfig.scaleSmoothing,
            this.filterConfig.gestureSmoothing
        );

        // Create rotation filter (3D)
        this.filters.rotation = new KalmanFilterRotation(
            0, 0, 0,
            this.filterConfig.rotationSmoothing,
            this.filterConfig.gestureSmoothing,
            2  // Use 2D filter for rotation (x,y axes only)
        );

        // Create gesture state filters
        ['scale', 'rotate', 'punch'].forEach(gestureType => {
            this.filters.gestureState.set(gestureType, new KalmanFilter3D(
                0, 0, 0,
                0.01, // Low process covariance for stable state
                0.5   // High measurement covariance to avoid flickering
            ));
        });

        console.log('[GestureController] Kalman filters initialized');
    }

    /**
     * Update filter configuration
     */
    updateFilterConfig(config) {
        Object.assign(this.filterConfig, config);

        // Update existing filters with new parameters
        if (this.filters.rotation) {
            this.filters.rotation.updateParameters(
                this.filterConfig.rotationSmoothing,
                this.filterConfig.gestureSmoothing
            );
        }

        if (this.filters.scale) {
            this.filters.scale.updateParameters(
                this.filterConfig.scaleSmoothing,
                this.filterConfig.gestureSmoothing
            );
        }

        console.log('[GestureController] Filter configuration updated:', this.filterConfig);
    }

    /**
     * Toggle filtering on/off
     */
    toggleFiltering() {
        this.filterConfig.enabled = !this.filterConfig.enabled;
        this.showNotification(`Gesture filtering ${this.filterConfig.enabled ? 'enabled' : 'disabled'}`);
        return this.filterConfig.enabled;
    }
    /**
     * Process pose detection results to extract and apply gestures
     * This logic was moved from the PoseDetector class
     */
    processPoseResults(poseResults) {
        if (!poseResults || !this.state.active) return;

        try {
            // Check for hand presence first
            const handPresent = this.isAnyHandPresent(poseResults);

            // Update the lock state based on hand presence
            this.updateGestureLocks(handPresent);

            // First detect gestures from hand poses
            const gestures = this.detectGestures(poseResults);

            if (gestures && gestures.length > 0) {
                // Process detected gestures
                this.handleGestures(gestures);

                // Update timestamp to prevent timeout
                this.state.lastGestureTime = performance.now();
            } else if (performance.now() - this.state.lastGestureTime > this.state.gestureTimeout) {
                // If no gestures for a while, reset all active states
                Object.keys(this.activeGestures).forEach(key => {
                    if (this.activeGestures[key]) {
                        this.activeGestures[key] = false;
                    }
                });

                // Reset gesture states
                this.resetPinchState();
                this.resetRotationState();

                // Hide UI elements when no gestures are detected
                if (this.viewer && this.viewer.uiSettings) {
                    this.viewer.uiSettings.hideGestureZoomSlider();
                    this.viewer.uiSettings.hideGestureScalingIndicator();
                    this.viewer.uiSettings.hideGestureRotationIndicator();
                }
            }
        } catch (error) {
            console.error('[GestureController] Error processing pose results:', error);
        }
    }

    /**
     * Get or create a Kalman filter for a specific landmark point
     * @param {string} handId - Hand identifier (left/right)
     * @param {number} pointIndex - Landmark point index (0-20)
     * @returns {KalmanFilter3D} - The filter instance
     */
    getLandmarkFilter(handId, pointIndex) {
        const key = `${handId}_${pointIndex}`;

        if (!this.filters.landmarks.has(key)) {
            // Create a 3D position filter for this landmark
            const filter = new KalmanFilter3D(
                0, 0, 0,
                this.filterConfig.landmarkSmoothing,
                this.filterConfig.gestureSmoothing
            );

            this.filters.landmarks.set(key, filter);
        }

        return this.filters.landmarks.get(key);
    }

    /**
     * Apply Kalman filtering to hand landmarks
     * @param {string} handId - Hand identifier (left/right)
     * @param {Array} landmarks - Array of hand landmarks
     * @returns {Array} - Filtered landmarks
     */
    filterLandmarks(handId, landmarks) {
        if (!this.filterConfig.enabled || !landmarks || landmarks.length === 0) {
            return landmarks;
        }

        return landmarks.map((point, index) => {
            const filter = this.getLandmarkFilter(handId, index);

            // Apply filter to landmark position
            // Use update and predict pattern instead of directly calling filter
            filter.update(point.x, point.y, point.z);
            const filtered = filter.predict();

            // Return filtered position
            return {
                x: filtered[0],
                y: filtered[1],
                z: filtered[2]
            };
        });
    }
    /**
     * Filter pinch scale value
     * @param {number} scale - Raw scale value
     * @returns {number} - Filtered scale value
     */
    filterPinchScale(scale) {
        if (!this.filterConfig.enabled) return scale;

        // Apply filter using update-predict pattern instead of direct filter call
        this.filters.scale.update(scale, 0, 0);
        const filtered = this.filters.scale.predict();

        // Return filtered scale
        return filtered[0];
    }

    /**
     * Filter rotation values
     * @param {Object} rotation - {x, y} rotation values
     * @returns {Object} - Filtered rotation values
     */
    filterRotation(rotation) {
        if (!this.filterConfig.enabled) return rotation;

        // Apply filter using update-predict pattern
        this.filters.rotation.update(rotation.x, rotation.y, 0);
        const filtered = this.filters.rotation.predict();

        // Additional exponential smoothing for extra stability
        if (!this._lastFilteredRotation) {
            this._lastFilteredRotation = { x: filtered[0], y: filtered[1] };
        } else {
            const smoothingFactor = 0.3; // Adjust between 0-1 (lower = smoother)
            this._lastFilteredRotation = {
                x: filtered[0] * smoothingFactor + this._lastFilteredRotation.x * (1 - smoothingFactor),
                y: filtered[1] * smoothingFactor + this._lastFilteredRotation.y * (1 - smoothingFactor)
            };
        }

        // Return double-filtered rotation
        return {
            x: this._lastFilteredRotation.x,
            y: this._lastFilteredRotation.y
        };
    }
    /**
     * Filter a gesture state to avoid flickering
     * @param {string} gestureType - Type of gesture (scale, rotate, punch)
     * @param {boolean} active - Current gesture state
     * @returns {boolean} - Filtered state
     */
    filterGestureState(gestureType, active) {
        if (!this.filterConfig.enabled) return active;

        const filter = this.filters.gestureState.get(gestureType);
        if (!filter) return active;

        // Convert boolean to number
        const activeValue = active ? 1.0 : 0.0;

        // Filter the state value using update-predict pattern
        filter.update(activeValue, 0, 0);
        const filtered = filter.predict();

        // Apply hysteresis - use thresholds to avoid rapid toggling
        // State changes to active only when value exceeds 0.7
        // State changes to inactive only when value falls below 0.3
        if (filtered[0] > 0.7) return true;
        if (filtered[0] < 0.3) return false;

        // In between, maintain previous state
        return active;
    }
    /**
     * Detect gestures from pose data (moved from poseDetector.js)
     */
    detectGestures(poseResults) {
        const detectedGestures = [];

        // Skip if no hand poses or poseResults is null/undefined
        if (!poseResults || !poseResults.handPoses) {
            // Debug: Log what we're actually receiving
            console.log(`[GestureController] No pose results or hand poses:`, {
                hasPoseResults: !!poseResults,
                hasHandPoses: !!poseResults?.handPoses,
                poseResultsKeys: poseResults ? Object.keys(poseResults) : 'null',
                handPosesKeys: poseResults?.handPoses ? Object.keys(poseResults.handPoses) : 'null'
            });
            return detectedGestures;
        }

        // Configure detection confidence thresholds
        const confidenceThreshold = 0.7; // Minimum confidence to consider a hand present
        const minLandmarksVisible = 15; // Minimum number of visible landmarks (out of 21)

        // Process each hand independently
        const hands = ['left', 'right'];

        hands.forEach(hand => {
            const handPose = poseResults.handPoses[hand];

            if (!handPose || !handPose.landmarks || handPose.landmarks.length < 21) {
                // Debug: Log the structure we're receiving
                if (poseResults.handPoses[hand]) {
                    console.log(`[GestureController] ${hand} hand pose structure:`, {
                        hasLandmarks: !!handPose?.landmarks,
                        landmarksLength: handPose?.landmarks?.length,
                        firstLandmark: handPose?.landmarks?.[0]
                    });
                }
                return;
            }
            // Check if hands are actually present with sufficient confidence
            let handConfidence = 0;
            let visibleLandmarks = 0;

            // Debug: Log first few landmarks to understand structure
            if (handPose.landmarks.length > 0) {
                console.log(`[GestureController] Debug ${hand} landmarks sample:`, {
                    totalLandmarks: handPose.landmarks.length,
                    first3Landmarks: handPose.landmarks.slice(0, 3),
                    hasVisibility: handPose.landmarks[0]?.visibility !== undefined,
                    hasCoordinates: handPose.landmarks[0]?.x !== undefined
                });
            }

            // Analyze landmarks to determine hand presence confidence
            handPose.landmarks.forEach((landmark, index) => {
                // If landmark has a confidence/visibility score
                if (landmark.visibility !== undefined) {
                    if (landmark.visibility > 0.5) {
                        visibleLandmarks++;
                        handConfidence += landmark.visibility;
                    }
                } else if (landmark.x !== undefined && landmark.y !== undefined) {
                    // For models that don't provide visibility, check if landmarks are within reasonable bounds
                    // and seem to have valid positions (not all 0,0 or extreme values)
                    const validPosition =
                        landmark.x > 0.05 && landmark.x < 0.95 &&
                        landmark.y > 0.05 && landmark.y < 0.95;

                    if (validPosition) {
                        visibleLandmarks++;
                        handConfidence += 0.8; // Assume decent confidence if position is valid
                    }
                } else {
                    // Log unexpected landmark structure
                    if (index === 0) {
                        console.log(`[GestureController] Unexpected landmark structure at index ${index}:`, landmark);
                    }
                }
            });
            // Calculate average confidence for the hand
            const avgHandConfidence = visibleLandmarks > 0 ? handConfidence / visibleLandmarks : 0;

            // Skip if not enough landmarks are visible or confidence is too low
            if (visibleLandmarks < minLandmarksVisible || avgHandConfidence < confidenceThreshold) {
                console.log(`[GestureController] Skipping low confidence hand detection: visible=${visibleLandmarks}, confidence=${avgHandConfidence.toFixed(2)}`);
                return; // Skip low-confidence detections
            }

            // Apply Kalman filtering to landmarks
            const filteredLandmarks = this.filterLandmarks(hand, handPose.landmarks);

            // Calculate finger states using filtered landmarks
            const fingerStates = this.calculateFingerStates(filteredLandmarks);

            // Check for gestures with priority - only one gesture will be added
            // Priority order: rotation > pinch > fist

            // Rotation gesture (waving palm with all five fingers extended)
            const isRotation = this.isRotationGesture(fingerStates);
            const isRotationFiltered = this.filterGestureState('rotate', isRotation);

            if (isRotationFiltered) {
                // Calculate and filter rotation
                const rawRotation = this.calculateRotation(filteredLandmarks, poseResults.timestamp);
                const filteredRotation = this.filterRotation(rawRotation);

                detectedGestures.push({
                    type: 'rotate',
                    rotation: filteredRotation,
                    hand: hand
                });
            }
            // Only check for pinch if rotation is not detected
            else {
                // Pinch/scale gesture (thumb and index finger)
                const isPinch = this.isPinchGesture(fingerStates);
                const isPinchFiltered = this.filterGestureState('scale', isPinch);

                if (isPinchFiltered) {
                    // Calculate and filter scale value
                    const rawScale = this.calculatePinchScale(filteredLandmarks);
                    const filteredScale = this.filterPinchScale(rawScale);

                    detectedGestures.push({
                        type: 'scale',
                        scale: filteredScale,
                        hand: hand,
                        confidence: avgHandConfidence
                    });
                }
                // Could add more gesture checks here with else if
                // for further priority ordering if needed
            }

            // Fist/punch gesture code is already commented out
        });

        if (detectedGestures.length > 0) {
            // console.log('[GestureController] Filtered gestures:', detectedGestures);
        }

        return detectedGestures;
    }

    /**
     * Calculate finger positions and states for gesture detection
     */
    calculateFingerStates(landmarks) {
        return GestureRecognizer.calculateFingerStates(landmarks);
    }

    /**
     * Calculate distance between two landmark points
     */
    calculateDistance(p1, p2) {
        return GestureUtils.calculateDistance(p1, p2);
    }

    /**
     * Check if hand is making a pinch gesture
     */
    isPinchGesture(fingerStates) {
        return GestureRecognizer.isPinchGesture(fingerStates);
    }

    /**
     * Check if hand is making a rotation gesture (open palm)
     */
    isRotationGesture(fingerStates) {
        return GestureRecognizer.isRotationGesture(fingerStates);
    }

    /**
     * Calculate pinch scale factor with better relative distance handling
     */
    calculatePinchScale(landmarks) {
        // Get thumb and index finger tips
        const thumbTip = landmarks[4];
        const indexTip = landmarks[8];

        // Calculate current distance between fingers
        const currentDistance = this.calculateDistance(thumbTip, indexTip);
        const now = performance.now();

        // Check if this is a new pinch gesture or continuing one
        if (!this.scalingState.pinchActive ||
            now - this.scalingState.lastPinchDetectionTime > this.state.gestureTimeout) {

            // Initialize new gesture
            console.log('[GestureController] New pinch gesture detected, initializing reference');
            this.scalingState.pinchActive = true;
            this.scalingState.referenceDistance = currentDistance;
            this.scalingState.lastPinchDetectionTime = now;

            // First detection should return neutral value
            return 1.0;
        }

        // Update timestamp for gesture tracking
        this.scalingState.lastPinchDetectionTime = now;

        // Calculate scale using relative method
        const result = GestureUtils.calculateRelativePinchScale(
            currentDistance,
            this.scalingState.referenceDistance,
            this.scalingState.sensitivity
        );

        // Update reference distance for next frame
        this.scalingState.referenceDistance = result.updatedReference;

        return result.scale;
    }
    /**
     * Reset the pinch gesture state when gesture ends
     */
    resetPinchState() {
        if (this.scalingState.pinchActive) {
            console.log('[GestureController] Resetting pinch gesture state');
            this.scalingState.pinchActive = false;
            this.scalingState.initialDistance = 0;
            this.scalingState.previousDistance = 0;
            this.scalingState.currentDistance = 0;
        }
    }
    /**
     * Reset the rotation gesture state when gesture ends
     */
    resetRotationState() {
        if (this.rotationState.rotationActive) {
            console.log('[GestureController] Resetting rotation gesture state');
            this.rotationState.rotationActive = false;
            this.rotationState.lastRotation = { x: 0, y: 0 };
        }
    }
    /**
     * Calculate rotation from hand orientation
     */
    calculateRotation(landmarks, timestamp) {
        // Store the current landmarks for the next frame
        if (!this._previousLandmarks) {
            // First frame, no previous landmarks yet
            this._previousLandmarks = [...landmarks];
            return { x: 0, y: 0 };
        }

        // Calculate rotation using current and previous landmarks
        const rotation = GestureUtils.calculateRotation(landmarks, this._previousLandmarks);

        // Update previous landmarks for next frame
        this._previousLandmarks = [...landmarks];

        return rotation;
    }

    /**
     * Check if gesture controls are currently active
     */
    isActive() {
        return this.state.active;
    }

    /**
     * Check if debug mode is enabled
     */
    isDebugMode() {
        return this.state.debugMode;
    }

    /**
     * Toggle debug visualization mode
     */
    toggleDebugMode() {
        this.state.debugMode = !this.state.debugMode;
        console.log(`[GestureController] Debug mode ${this.state.debugMode ? 'enabled' : 'disabled'}`);

        // Update pose detector debug state
        if (this.poseDetector) {
            this.poseDetector.setDebugMode(this.state.debugMode);
        }

        // Show notification
        this.showNotification(`Debug mode ${this.state.debugMode ? 'enabled' : 'disabled'}`);

        return this.state.debugMode;
    }

    /**
     * Start gesture detection loop
     */
    startDetection() {
        console.log('[GestureController] Starting detection with centralized camera...');
        console.log('[GestureController] CameraManager available:', !!this.cameraManager);
        console.log('[GestureController] CameraManager initialized:', this.cameraManager?.isInitialized);

        // Use centralized camera manager
        this.cameraManager.startCamera()
            .then(stream => {
                console.log('[GestureController] Centralized camera started successfully');
                console.log('[GestureController] Stream active:', stream?.active);
                console.log('[GestureController] CameraManager active:', this.cameraManager.isCameraActive());

                // Show camera in corner mode for gesture control
                this.cameraManager.showCamera('corner')
                    .then(() => {
                        console.log('[GestureController] Camera corner view opened');
                        // Start the detection loop
                        this.runDetectionLoop();
                    })
                    .catch(err => {
                        console.error('[GestureController] Failed to show camera corner view:', err);
                        // Try to start detection anyway if camera is running
                        this.runDetectionLoop();
                    });
            })
            .catch(err => {
                console.error('[GestureController] Failed to start centralized camera:', err);

                // Fallback to legacy camera system if available
                if (this.cameraViewer) {
                    console.log('[GestureController] Falling back to legacy camera system');
                    this._startLegacyDetection();
                } else {
                    console.error('[GestureController] No camera system available');
                }
            });
    }

    /**
     * Legacy camera detection start (fallback)
     */
    _startLegacyDetection() {
        console.log('[GestureController] Starting legacy camera detection...');

        if (this.cameraViewer && this.cameraViewer.openPopupWindow && typeof this.cameraViewer.openPopupWindow === 'function') {
            console.log('[GestureController] Opening legacy camera popup window');

            // Open popup window
            this.cameraViewer.openPopupWindow();

            // Wait briefly before starting the stream and checking components
            setTimeout(() => {
                // Ensure camera is started and video is playing
                if (this.cameraViewer.streamManager && !this.cameraViewer.streamManager.active) {
                    console.log('[GestureController] Starting legacy camera stream...');
                    if (typeof this.cameraViewer.startCamera === 'function') {
                        this.cameraViewer.startCamera()
                            .then(() => {
                                console.log('[GestureController] Legacy camera started successfully');
                                // After camera starts, connect stream to popup video
                                this.connectStreamToPopup();
                                // Now start the detection loop
                                this.runDetectionLoop();
                            })
                            .catch(err => {
                                console.error('[GestureController] Failed to start legacy camera:', err);
                            });
                    } else {
                        // If no startCamera method, try to use existing stream
                        this.connectStreamToPopup();
                        this.runDetectionLoop();
                    }
                } else {
                    // Stream already active, just connect and run
                    this.connectStreamToPopup();
                    this.runDetectionLoop();
                }
            }, 500);
        } else {
            console.warn('[GestureController] Cannot open legacy camera popup: method not available');
        }
    }

    /**
     * Handle video frame from centralized camera manager
     */
    _handleVideoFrame(frameInfo) {
        // Forward frame info to any registered frame callbacks
        this.frameCallbacks.forEach(callback => {
            try {
                callback(frameInfo);
            } catch (error) {
                console.error('[GestureController] Error in frame callback:', error);
            }
        });
    }

    /**
     * Connect the video stream to the popup window's video element (legacy support)
     */
    connectStreamToPopup() {
        if (!this.cameraViewer || !this.cameraViewer.popupWindow) {
            console.warn('[GestureController] Cannot connect stream: No popup window available');
            return;
        }

        try {
            // Get the popup video element
            const popupVideo = this.cameraViewer.popupWindow.document.querySelector('.camera-popup-video');
            if (!popupVideo) {
                console.warn('[GestureController] Cannot find popup video element');
                return;
            }

            // Get the source stream
            let sourceStream = null;

            // Try to get stream from different possible sources
            if (this.cameraViewer.streamManager && this.cameraViewer.streamManager.stream) {
                sourceStream = this.cameraViewer.streamManager.stream;
                console.log('[GestureController] Using stream from streamManager');
            } else if (this.cameraViewer.videoElement && this.cameraViewer.videoElement.srcObject) {
                sourceStream = this.cameraViewer.videoElement.srcObject;
                console.log('[GestureController] Using stream from videoElement');
            }

            if (sourceStream && popupVideo.srcObject !== sourceStream) {
                // Clone the stream before assigning to avoid conflicts
                try {
                    const clonedStream = sourceStream.clone();
                    popupVideo.srcObject = clonedStream;
                    console.log('[GestureController] Stream connected to popup video');

                    // Ensure video is playing
                    if (popupVideo.paused) {
                        popupVideo.play()
                            .then(() => console.log('[GestureController] Popup video playback started'))
                            .catch(err => console.error('[GestureController] Failed to play popup video:', err));
                    }
                } catch (err) {
                    console.error('[GestureController] Error cloning stream:', err);
                    // Fallback: try using the stream directly
                    popupVideo.srcObject = sourceStream;
                }
            } else if (!sourceStream) {
                console.warn('[GestureController] No video stream available to connect');
            }
        } catch (error) {
            console.error('[GestureController] Error connecting stream to popup:', error);
        }
    }



    /**
     * Validate that all required components are available
     * @returns {boolean} True if all required components are available
     */
    validateComponents() {
        // Check pose detector first
        if (!this.poseDetector) {
            console.error('[GestureController] No pose detector available');
            return false;
        }

        // Try centralized camera manager first
        if (this.cameraManager && this.cameraManager.isCameraActive() && this.cameraManager.getVideoElement()) {
            const videoSource = this.cameraManager.getVideoElement();
            if (videoSource.readyState < 2) {
                console.warn('[GestureController] Centralized camera video not ready, readyState:', videoSource.readyState);
                return false;
            }
            return true;
        }

        // Fallback to legacy camera viewer validation
        if (!this.cameraViewer) {
            console.error('[GestureController] No camera system available');
            return false;
        }

        // Check for popup window
        if (this.cameraViewer.popupWindow && this.cameraViewer.popupWindow.closed) {
            console.warn('[GestureController] Popup window is closed');
            return false;
        }

        // Get video source (try both main video and popup video)
        let videoSource = this.cameraViewer.videoElement;
        let popupVideo = null;

        if (this.cameraViewer.popupWindow) {
            popupVideo = this.cameraViewer.popupWindow.document.querySelector('.camera-popup-video');
            if (popupVideo && popupVideo.srcObject) {
                videoSource = popupVideo; // Prefer popup video if it has a stream
            }
        }

        if (!videoSource) {
            console.warn('[GestureController] No video source found');
            return false;
        }

        // Check video state
        if (videoSource instanceof HTMLVideoElement) {
            if (!videoSource.srcObject) {
                console.warn('[GestureController] No stream connected to video');
                // Try to connect stream if available
                if (this.cameraViewer.streamManager && this.cameraViewer.streamManager.stream) {
                    videoSource.srcObject = this.cameraViewer.streamManager.stream;
                    console.log('[GestureController] Connected stream from streamManager to video');
                } else {
                    return false;
                }
            }

            if (videoSource.readyState < 2) {
                console.warn('[GestureController] Video not ready, readyState:', videoSource.readyState);
                return false;
            }
        }

        return true;
    }

    /**
     * Stop gesture detection loop
     */
    stopDetection() {
        console.log('[GestureController] Stopping gesture detection...');

        if (this.gestureDetectionFrameId) {
            cancelAnimationFrame(this.gestureDetectionFrameId);
            this.gestureDetectionFrameId = null;
        }

        // Hide camera with smooth animation
        this._hideCameraWithAnimation();

        // Reset all active gestures
        Object.keys(this.activeGestures).forEach(key => {
            this.activeGestures[key] = false;
        });

        // Reset filters when stopping detection
        this.resetFilters();
    }

    /**
     * Hide camera with smooth animation when gesture controls are deactivated
     * @private
     */
    async _hideCameraWithAnimation() {
        try {
            // Hide camera using centralized camera manager with built-in animations
            if (this.cameraManager && this.cameraManager.isCameraActive()) {
                console.log('[GestureController] Hiding camera via centralized manager...');
                await this.cameraManager.hideCamera();
                console.log('[GestureController] Camera hidden successfully');
            }

            // Fallback: Hide legacy camera popup if it exists
            if (this.cameraViewer && this.cameraViewer.popupWindow && !this.cameraViewer.popupWindow.closed) {
                console.log('[GestureController] Closing legacy camera popup...');
                this._animatePopupOut().then(() => {
                    this.cameraViewer.popupWindow.close();
                    this.cameraViewer.popupWindow = null;
                });
            }
        } catch (error) {
            console.error('[GestureController] Error hiding camera:', error);
        }
    }

    /**
     * Animate legacy popup window out with smooth transition
     * @private
     */
    _animatePopupOut() {
        return new Promise(resolve => {
            if (!this.cameraViewer || !this.cameraViewer.popupWindow || this.cameraViewer.popupWindow.closed) {
                resolve();
                return;
            }

            try {
                // Add closing animation to popup
                const popupDoc = this.cameraViewer.popupWindow.document;
                const style = popupDoc.createElement('style');
                style.textContent = `
                    @keyframes fadeOutScale {
                        0% {
                            opacity: 1;
                            transform: scale(1);
                        }
                        100% {
                            opacity: 0;
                            transform: scale(0.8);
                        }
                    }
                    .closing-animation {
                        animation: fadeOutScale 0.3s ease-out forwards;
                    }
                `;
                popupDoc.head.appendChild(style);
                popupDoc.body.classList.add('closing-animation');

                // Wait for animation to complete
                setTimeout(resolve, 300);
            } catch (error) {
                console.warn('[GestureController] Could not animate popup close:', error);
                resolve();
            }
        });
    }

    /**
     * Run the detection loop
     */
    runDetectionLoop() {
        if (this.gestureDetectionFrameId) {
            cancelAnimationFrame(this.gestureDetectionFrameId);
            this.gestureDetectionFrameId = null;
        }

        // Start the detection loop
        this.gestureDetectionFrameId = requestAnimationFrame(() => this.detectionLoop());
        console.log('[GestureController] Detection loop started');
    }

    /**
     * Detection loop - processes each frame for gestures
     */
    detectionLoop() {
        if (!this.state.active) {
            console.log('[GestureController] Detection loop stopped (inactive)');
            return;
        }

        try {
            // Validate components on each frame
            if (!this.validateComponents()) {
                console.warn('[GestureController] Components validation failed, retrying...');
                this.gestureDetectionFrameId = requestAnimationFrame(() => this.detectionLoop());
                return;
            }

            // Get the appropriate video source - try centralized camera first
            let videoSource = null;

            if (this.cameraManager && this.cameraManager.isCameraActive() && this.cameraManager.getVideoElement()) {
                videoSource = this.cameraManager.getVideoElement();
            } else if (this.cameraViewer) {
                // Fallback to legacy camera viewer
                videoSource = this.cameraViewer.videoElement;
                if (this.cameraViewer.popupWindow) {
                    const popupVideo = this.cameraViewer.popupWindow.document.querySelector('.camera-popup-video');
                    if (popupVideo && popupVideo.srcObject && popupVideo.readyState >= 2) {
                        videoSource = popupVideo;
                    }
                }
            }

            // Process the current frame
            if (videoSource && videoSource.readyState >= 2) {
                // Ensure timestamp is strictly increasing to avoid MediaPipe errors
                const timestamp = Math.max(
                    (this.lastProcessedTimestamp || 0) + 1,
                    performance.now()
                );
                this.lastProcessedTimestamp = timestamp;
                this.poseDetector.detectPose(videoSource, timestamp)
                    .then(results => {
                        if (results) {
                            // Process results
                            this.processPoseResults(results);

                            // Draw visualization if needed
                            if (this.state.debugMode && this.debugVisualizer) {
                                // Try centralized camera debug canvas first
                                if (this.cameraManager && this.cameraManager.canvasElement) {
                                    const canvas = this.cameraManager.canvasElement;
                                    if (canvas.width !== videoSource.videoWidth || canvas.height !== videoSource.videoHeight) {
                                        canvas.width = videoSource.videoWidth || 640;
                                        canvas.height = videoSource.videoHeight || 480;
                                    }
                                    this.debugVisualizer.drawResults(results);
                                }
                                // Fallback to legacy popup canvas
                                else if (this.cameraViewer && this.cameraViewer.popupWindow) {
                                    const popupCanvas = this.cameraViewer.popupWindow.document.querySelector('.camera-popup-canvas');
                                    if (popupCanvas) {
                                        if (popupCanvas.width !== videoSource.videoWidth || popupCanvas.height !== videoSource.videoHeight) {
                                            popupCanvas.width = videoSource.videoWidth || 640;
                                            popupCanvas.height = videoSource.videoHeight || 480;
                                        }
                                        this.debugVisualizer.drawResults(results);
                                    }
                                }
                            }
                        }

                        // Continue loop
                        this.gestureDetectionFrameId = requestAnimationFrame(() => this.detectionLoop());
                    })
                    .catch(error => {
                        console.error('[GestureController] Error detecting pose:', error);
                        this.gestureDetectionFrameId = requestAnimationFrame(() => this.detectionLoop());
                    });
            } else {
                console.warn('[GestureController] Video not ready yet, readyState:', videoSource ? videoSource.readyState : 'No video');
                this.gestureDetectionFrameId = requestAnimationFrame(() => this.detectionLoop());
            }
        } catch (error) {
            console.error('[GestureController] Error in detection loop:', error);
            this.gestureDetectionFrameId = requestAnimationFrame(() => this.detectionLoop());
        }
    }

    /**
     * Process detected gestures
     */
    handleGestures(gestures) {
        // Update timestamp to prevent timeout
        this.state.lastGestureTime = performance.now();

        // Handle multiple gestures if detected
        if (Array.isArray(gestures)) {
            gestures.forEach(gesture => {
                this.processGesture(gesture);
            });
        } else if (gestures) {
            this.processGesture(gestures);
        }
    }
    /**
     * Reset all filters (useful when toggling or restarting detection)
     */
    resetFilters() {
        // Clear landmark filters
        this.filters.landmarks.clear();

        // Reset scale and rotation filters
        this.initializeFilters();

        // Reset last pinch distance
        this.filters.lastPinchDistance = 0.1;

        // Reset pinch state
        this.resetPinchState();

        // Reset rotation state
        this.resetRotationState();

        console.log('[GestureController] Filters reset');
    }
    /**
     * Process a single gesture and apply appropriate action
     */
    processGesture(gesture) {
        if (!gesture || !this.controls) return;
        switch (gesture.type) {
            case 'rotate':
                this.handleRotationGesture(gesture);
                this.activeGestures.rotation = true;
                break;

            case 'scale':
                this.handleScalingGesture(gesture);
                this.activeGestures.scaling = true;
                // Show zoom slider UI when scaling gesture is detected
                if (this.viewer && this.viewer.uiSettings) {
                    this.viewer.uiSettings.showGestureZoomSlider();
                }
                break;
        }
    }

    /**
     * Handle rotation gesture and apply to controls
     */
    // handleRotationGesture(gesture) {
    //     const now = performance.now();

    //     // Always update reference rotation for accurate delta calculation
    //     this.rotationState.lastRotation = { ...gesture.rotation };

    //     // Apply increased sensitivity but with dead zone for tiny movements
    //     const rotationDeltaX = Math.abs(gesture.rotation.x);
    //     const rotationDeltaY = Math.abs(gesture.rotation.y);

    //     if (rotationDeltaX > 0.001 || rotationDeltaY > 0.001) {
    //         const adjustedRotation = {
    //             x: gesture.rotation.x * this.rotationState.sensitivity,
    //             y: gesture.rotation.y * this.rotationState.sensitivity
    //         };

    //         GestureOperations.handleRotation(
    //             this.controls,
    //             adjustedRotation,
    //             this.state.rotationSpeed,
    //             this.lookingGlassConfig,
    //         );
    //     }

    //     // Update state
    //     this.rotationState.lastGestureTimestamp = now;
    //     this.rotationState.rotationActive = true;

    //     // Show rotation indicator UI - but don't show lock status
    //     if (this.viewer && this.viewer.uiSettings) {
    //         this.viewer.uiSettings.showGestureRotationIndicator();
    //     }
    // }

    /**
     * Handle scaling gesture and apply to controls
     */
    handleScalingGesture(gesture) {
        // Always update the timestamp
        const now = performance.now();
        this.activeGestures.scaling = true;

        // Only apply scaling if not locked
        if (!this.scalingState.locked) {
            // Update reference scale for delta calculations
            this.scalingState.lastScale = gesture.scale;

            // Apply scaling with gesture operations
            const success = GestureOperations.handleScaling(
                this.controls,
                gesture.scale,
                this.state.scalingSpeed,
                this.lookingGlassConfig,
            );

            // Fallback to direct camera access if needed
            if (!success && this.viewer && this.viewer.camera) {
                GestureUtils.applyZoomToCamera(
                    this.viewer.camera,
                    gesture.scale,
                    this.state.scalingSpeed * this.scalingState.sensitivity,  // Apply sensitivity
                    {
                        minZoom: this.state.minZoom,
                        maxZoom: this.state.maxZoom
                    }
                );
            }

            // Update zoom slider UI
            if (this.viewer && this.viewer.uiSettings) {
                this.viewer.uiSettings.updateGestureZoomSlider(
                    this.viewer.camera,
                    this.state.minZoom,
                    this.state.maxZoom
                );
            }
        }
    }

    /**
     * Check if any hand is present in the frame with sufficient confidence
     */
    isAnyHandPresent(poseResults) {
        if (!poseResults || !poseResults.handPoses) return false;

        const hands = ['left', 'right'];
        for (const hand of hands) {
            const handPose = poseResults.handPoses[hand];
            if (handPose && handPose.landmarks &&
                GestureRecognizer.isHandPresent(handPose.landmarks)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Update lock states for all gestures based on hand presence
     */
    updateGestureLocks(handPresent) {
        const now = performance.now();

        // Update scaling lock state
        if (handPresent) {
            this.scalingState.lastHandPresenceTime = now;
            this.scalingState.locked = false;

            // Update UI to show unlocked status if needed
            if (this.viewer && this.viewer.uiSettings) {
                this.viewer.uiSettings.updateGestureScalingIndicator(false);
            }
        } else {
            const timeSinceHandPresent = now - this.scalingState.lastHandPresenceTime;

            // Lock after timeout period with no hands
            if (timeSinceHandPresent > this.scalingState.handPresenceTimeout && !this.scalingState.locked) {
                this.scalingState.locked = true;

                // Update UI to show locked status
                if (this.viewer && this.viewer.uiSettings) {
                    this.viewer.uiSettings.updateGestureScalingIndicator(true);
                }

                console.log('[GestureController] Scaling locked due to hand absence');
            }
        }

        // Similarly update rotation lock state
        if (handPresent) {
            this.rotationState.lastHandPresenceTime = now;
            this.rotationState.locked = false;
        } else {
            const timeSinceHandPresent = now - this.rotationState.lastHandPresenceTime;

            // Lock after timeout period with no hands
            if (timeSinceHandPresent > this.scalingState.handPresenceTimeout && !this.rotationState.locked) {
                this.rotationState.locked = true;
                console.log('[GestureController] Rotation locked due to hand absence');
            }
        }
    }

    /**
     * Handle rotation gesture and apply to controls
     */
    handleRotationGesture(gesture) {
        const now = performance.now();

        // Always update reference rotation for accurate delta calculation
        this.rotationState.lastRotation = { ...gesture.rotation };

        // Apply increased sensitivity but with dead zone for tiny movements
        const rotationDeltaX = Math.abs(gesture.rotation.x);
        const rotationDeltaY = Math.abs(gesture.rotation.y);

        if (rotationDeltaX > 0.001 || rotationDeltaY > 0.001) {
            const adjustedRotation = {
                x: gesture.rotation.x * this.rotationState.sensitivity,
                y: gesture.rotation.y * this.rotationState.sensitivity
            };

            GestureOperations.handleRotation(
                this.controls,
                adjustedRotation,
                this.state.rotationSpeed,
                this.lookingGlassConfig,
            );
        }

        // Update state
        this.rotationState.lastGestureTimestamp = now;
        this.rotationState.rotationActive = true;

        // Show rotation indicator UI - but don't show lock status
        if (this.viewer && this.viewer.uiSettings) {
            this.viewer.uiSettings.showGestureRotationIndicator();
        }
    }

    /**
     * Reset view to default position
     */
    resetView() {
        if (!this.controls) return;

        // Reset to default position
        this.controls.reset();

        // Show notification
        this.showNotification('View reset');
    }

    /**
     * Set up the notification system
     */
    setupNotifications() {
        if (document.getElementById('gesture-notifications')) return;

        // Add styles for notifications if needed
        const style = document.createElement('style');
        style.id = 'gesture-notification-styles';
        style.textContent = `
            .gesture-notification {
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background-color: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                z-index: 1000;
                font-size: 14px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
                opacity: 1;
                transition: opacity 0.3s ease;
                pointer-events: none;
            }

            .gesture-notification.fade-out {
                opacity: 0;
            }
        `;
        document.head.appendChild(style);

        // Create container for notifications
        const container = document.createElement('div');
        container.id = 'gesture-notifications';
        document.body.appendChild(container);
    }

    /**
     * Show a notification message
     */
    showNotification(message, isError = false) {
        const container = document.getElementById('gesture-notifications');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `gesture-notification ${isError ? 'error' : ''}`;
        notification.textContent = message;
        container.appendChild(notification);

        // Remove after 2.5 seconds with fade
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => notification.remove(), 500);
        }, 2000);
    }

    /**
     * Clean up resources
     */
    dispose() {
        this.stopDetection();

        // Remove any event listeners or other resources
        const container = document.getElementById('gesture-notifications');
        if (container) {
            container.remove();
        }

        // Clear references
        this.poseDetector = null;
        this.cameraViewer = null;
        this.debugVisualizer = null;
        this.controls = null;
        this.viewer = null;
    }


}
